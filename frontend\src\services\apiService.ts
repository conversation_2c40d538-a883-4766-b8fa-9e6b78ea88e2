import axios, { AxiosRequestConfig, AxiosError } from 'axios';
import { logger } from '../utils/logger';
import { useAuth, User } from '../contexts/AuthContext';
import { API_URL } from '../config/api';
import { authService } from './authService';
import { useCallback } from 'react';
import { isErrorWithResponse } from '../types/common';

// Request cache with TTL
interface CacheEntry<T> {
  promise: Promise<T>;
  timestamp: number;
}

const requestCache: Record<string, CacheEntry<unknown>> = {};

// Define the structure for admin store objects
interface AdminStore {
  id: string; // Or number - verify if possible
  name: string;
}

interface ErrorResponse {
  message?: string;
  error?: string | object;
  detail?: string;
}

/**
 * Custom error class for API errors with better messages
 */
export class ApiError extends Error {
  status: number;
  statusText: string;
  data: unknown;
  response?: { data: unknown; status: number; };

  constructor(message: string, status: number, statusText: string, data?: unknown) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.statusText = statusText;
    this.data = data;
  }

  static fromAxiosError(error: AxiosError): ApiError {
    let message = 'An error occurred while communicating with the server';
    let status = error.response?.status || 0;
    let statusText = error.response?.statusText || '';
    const data = error.response?.data;

    // Handle specific HTTP status codes
    if (error.response) {
      switch (status) {
        case 400:
          message = 'Invalid request. Please check your inputs and try again.';
          break;
        case 401:
          message = 'Authentication error. Please log in again.';
          break;
        case 403:
          message = 'You do not have permission to access this resource.';
          break;
        case 404:
          message = 'The requested resource could not be found.';
          break;
        case 422:
          message = 'Validation error. Please check your inputs.';
          break;
        case 429:
          message = 'Too many requests. Please try again later.';
          break;
        case 402:
          // Insufficient credits custom handling
          if (data && typeof data === 'object' && (data as ErrorResponse).detail === 'insufficient_credits') {
            message = 'errors.insufficientCredits';
          } else {
            message = 'errors.insufficientCredits';
          }
          break;
        case 500:
          message = 'Server error. Please try again later.';
          break;
        case 502:
        case 503:
        case 504:
          message = 'Service temporarily unavailable. Please try again later.';
          break;
        default:
          if (status >= 400 && status < 500) {
            message = 'Client error. Please try again.';
          } else if (status >= 500) {
            message = 'Server error. Please try again later.';
          }
      }

      // If there's a more specific error message in the response, use it
      if (data && typeof data === 'object') {
        const errorData = data as ErrorResponse;
        if (errorData.message) {
          message = errorData.message;
        } else if (errorData.error) {
          message = typeof errorData.error === 'string' ? errorData.error : JSON.stringify(errorData.error);
        } else if (errorData.detail) {
          message = errorData.detail;
        }
      }
    } else if (error.request) {
      // The request was made but no response was received
      message = 'No response from server. Please check your internet connection.';
      status = 0;
      statusText = 'Network Error';
    } else {
      // Something happened in setting up the request
      message = error.message || 'Error setting up the request';
    }

    return new ApiError(message, status, statusText, data);
  }
}

/**
 * Enhanced fetch with proper Authorization handling and CSRF error recovery
 * Includes automatic token refresh and retry logic for CSRF failures
 */
export const fetchWithDeduplication = async <T>(
  url: string,
  options: RequestInit = {},
  retryCount: number = 0
): Promise<T> => {
  const MAX_CSRF_RETRIES = 2;
  const RETRY_DELAYS = [500, 1000]; // Exponential backoff delays in ms
  
  // Add base URL if not already present
  const fullUrl = url.startsWith('http') ? url : `${API_URL}${url}`;
  
  // Add auth header consistently
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers as Record<string, string>
  };

  // Use authService for token management
  const token = authService.getToken();
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Add CSRF token for non-GET requests (excluding auth endpoints)
  const method = options.method?.toLowerCase() || 'get';
  if (method !== 'get') {
    const isAuthEndpoint = url.includes('/auth/') || 
                          url.includes('/login') || 
                          url.includes('/register') ||
                          url.includes('/csrf-token');
    
    if (!isAuthEndpoint) {
      try {
        const { csrfService } = await import('./csrfService');
        const csrfToken = await csrfService.getToken();
        if (csrfToken) {
          headers['X-CSRF-Token'] = csrfToken;
        }
      } catch (csrfError) {
        console.warn('Failed to fetch CSRF token for fetchWithDeduplication:', csrfError);
        // Continue without CSRF token - the server should handle this gracefully
      }
    }
  }

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Check for CSRF-specific errors
      const isCSRFError = (
        (response.status === 401 || response.status === 403) &&
        (
          (typeof errorData.detail === 'string' && 
           (errorData.detail.toLowerCase().includes('csrf') || 
            errorData.detail.toLowerCase().includes('token') ||
            errorData.detail.toLowerCase().includes('expired'))) ||
          (typeof errorData.message === 'string' && 
           (errorData.message.toLowerCase().includes('csrf') ||
            errorData.message.toLowerCase().includes('token') ||
            errorData.message.toLowerCase().includes('expired')))
        )
      );

      // Check for authentication errors that should not be retried
      const isAuthError = (
        response.status === 401 &&
        (
          (typeof errorData.detail === 'string' && 
           (errorData.detail.toLowerCase().includes('authentication') ||
            errorData.detail.toLowerCase().includes('unauthorized') ||
            errorData.detail.toLowerCase().includes('invalid token'))) ||
          (typeof errorData.message === 'string' && 
           (errorData.message.toLowerCase().includes('authentication') ||
            errorData.message.toLowerCase().includes('unauthorized') ||
            errorData.message.toLowerCase().includes('invalid token')))
        )
      );

      // Retry logic for CSRF errors only (not authentication errors)
      if (isCSRFError && !isAuthError && retryCount < MAX_CSRF_RETRIES && method !== 'get') {
        console.warn(`CSRF error detected (attempt ${retryCount + 1}/${MAX_CSRF_RETRIES + 1}). Refreshing token and retrying...`);
        
        try {
          // Clear and refresh CSRF token
          const { csrfService } = await import('./csrfService');
          csrfService.clearToken();
          
          // Wait before retry with exponential backoff
          if (RETRY_DELAYS[retryCount]) {
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAYS[retryCount]));
          }
          
          // Retry the request with fresh token
          return await fetchWithDeduplication<T>(url, options, retryCount + 1);
        } catch (csrfRefreshError) {
          console.error('Failed to refresh CSRF token for retry:', csrfRefreshError);
          // Fall through to throw original error
        }
      }
      
      // Friendly insufficient credits message
      if (response.status === 402) {
        throw new Error('Please charge more credits, contact the administrator or do it in settings tab');
      }

      const error = new Error(errorData.detail || errorData.message || `HTTP ${response.status}`) as ApiError;
      error.response = {
        data: errorData,
        status: response.status
      };
      throw error;
    }

    return response.json();
  } catch (fetchError) {
    // If it's not a response error, check if it might be network-related
    if (!isErrorWithResponse(fetchError) && retryCount < MAX_CSRF_RETRIES) {
      console.warn(`Network error detected (attempt ${retryCount + 1}/${MAX_CSRF_RETRIES + 1}). Retrying...`);
      
      // Wait before retry
      if (RETRY_DELAYS[retryCount]) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAYS[retryCount]));
      }
      
      return await fetchWithDeduplication<T>(url, options, retryCount + 1);
    }
    
    throw fetchError;
  }
};

// Function to manually invalidate cache entries that match a URL pattern
export const invalidateCache = (urlPattern: string): void => {
  // Find all cache keys that match the URL pattern
  const keysToInvalidate = Object.keys(requestCache).filter(key => 
    key.includes(urlPattern)
  );
  
  // Remove matching cache entries
  keysToInvalidate.forEach(key => {
    logger.debug(`[CACHE INVALIDATED] Manually removing cache entry for ${key}`);
    delete requestCache[key];
  });
};

// Interface for the feedback data structure
interface ChatFeedbackPayload {
  feedback_text: string;
  likes: number;
  dislikes: number;
  source: 'chat' | 'dashboard'; // Type for source
}

// Hook version for use in components/hooks
export const useSubmitChatFeedback = () => {
  const { user, viewingStoreID } = useAuth();
  return async (feedbackData: ChatFeedbackPayload): Promise<{ message: string }> => {
    const token = authService.getToken();
    if (!token) {
      throw new ApiError('Authentication token is required.', 401, 'Unauthorized');
    }
    const storeId = getEffectiveStoreID(user, viewingStoreID);
    const url = `/api/store/${storeId}/feedback`;
    const config: AxiosRequestConfig = {
      url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: feedbackData
    };
    logger.info(`Submitting feedback to ${url}`);
    try {
      const response = await axios(config);
      logger.info('Feedback submission successful:', response.data);
      return response.data;
    } catch (error: unknown) {
      logger.error(`Error submitting feedback to ${url}:`, error);
      if (axios.isAxiosError(error)) {
        throw ApiError.fromAxiosError(error);
      } else if (error instanceof Error) {
        throw new ApiError(error.message, 0, 'Client Error');
      }
      throw new ApiError('An unknown error occurred during feedback submission.', 0, 'Unknown Error');
    }
  };
};

export interface ShippingMethodDistributionItem {
  name: string;
  count: number;
}

export interface MostFrequentShippingMethod {
  name: string;
  count: number;
}

export interface ShippingAnalysisResponse {
  most_frequent_shipping_method: MostFrequentShippingMethod | null;
  shipping_method_distribution: ShippingMethodDistributionItem[];
  shipping_analysis?: {
    analysis_text?: string;
    recommendations?: string[];
  };
}

// Hook version for use in components/hooks
export const useStoreShippingAnalysis = () => {
  const { user, viewingStoreID } = useAuth();
  const fetchWithDeduplicationCallback = useCallback(async (customStoreId?: string): Promise<ShippingAnalysisResponse> => {
    const storeId = customStoreId || getEffectiveStoreID(user, viewingStoreID);
    const endpoint = `/api/store/${storeId}/shipping-analysis`;
    try {
      const data = await fetchWithDeduplication<ShippingAnalysisResponse>(endpoint, { method: 'get' });
      return data;
    } catch (error) {
      throw ApiError.fromAxiosError(error as AxiosError);
    }
  }, [user, viewingStoreID]);

  return fetchWithDeduplicationCallback;
};

class ApiService {
    private instance = axios.create({
        baseURL: API_URL,
        timeout: 30000,
        headers: {
            'Content-Type': 'application/json',
        },
    }); // Initialize Axios instance with base URL

    constructor() {
        // Add request interceptor to include auth token
        this.instance.interceptors.request.use(
            (config) => {
                const token = authService.getToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => Promise.reject(error)
        );
    }

    // Basic error handler matching observed usage
    private handleApiError(error: unknown, context: string): void {
        logger.error(`Error during ${context}:`, error);
        // Log details if it's a known error type
        if (error instanceof ApiError) {
             logger.error(`API Error Details: Status ${error.status} - ${error.statusText}`, error.data);
        } else if (axios.isAxiosError(error)) {
             // Use ApiError static method just for consistent message generation if needed
             const apiError = ApiError.fromAxiosError(error);
             logger.error(`Axios Error: ${apiError.message}`, apiError.data);
        } else if (error instanceof Error) {
             logger.error(`Generic Error: ${error.message}`);
        }
    }

    // Method to fetch all stores for admin
    async getAdminStores(token: string | null, skip: number = 0, limit: number = 100, search?: string): Promise<{ stores: AdminStore[], total_count: number }> {
        if (!token) {
            // Use ApiError for consistency
            throw new ApiError('Authentication token is required.', 401, 'Unauthorized');
        }
        try {
            const params = new URLSearchParams({
                skip: skip.toString(),
                limit: limit.toString(),
            });
            // Add search parameter if provided
            if (search) {
                params.set('search', search);
            }
            const response = await this.instance.get(`/api/admin/stores?${params.toString()}`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            // Ensure response matches the expected structure, provide defaults
            return {
                stores: response.data?.stores || [],
                total_count: response.data?.total_count || 0
            };
        } catch (error) {
            logger.error('Error fetching admin stores:', error);
            this.handleApiError(error, 'fetch admin stores');
            // Return default structure on error
            return { stores: [], total_count: 0 }; 
        }
    }

    /**
     * Add credits to a store (admin-only)
     */
    async addStoreCredits(storeId: string, amount: number): Promise<number> {
      if (amount <= 0) {
        throw new ApiError('Amount must be positive', 422, 'Invalid amount');
      }

      const token = authService.getToken();
      if (!token) {
        throw new ApiError('Authentication token required', 401, 'Unauthorized');
      }

      try {
        const response = await this.instance.post<{ credits: number }>(
          `/api/admin/store/${storeId}/add-credits`,
          { amount },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          },
        );
        return response.data.credits;
      } catch (error) {
        this.handleApiError(error, 'addStoreCredits');
        throw error; // handleApiError rethrows a formatted ApiError
      }
    }

    // Instagram API Methods (New Implementation)

    async getInstagramInsights(timeRange: string, storeId?: string) {
        try {
            // Use new Instagram API endpoint
            const store = storeId || this.getCurrentStoreId();
            if (!store) {
                throw new Error('Store ID required for Instagram insights');
            }
            
            const response = await this.instance.get(`/api/instagram/${store}/insights`, {
                params: { 
                    time_range: timeRange,
                    page_id: 'auto' // Let backend determine the Instagram page
                }
            });
            return response;
        } catch (error) {
            // Enhanced error handling with graceful degradation
            if (isErrorWithResponse(error)) {
                const status = error.response?.status;
                
                // For permission errors (403), try to provide partial data
                if (status === 403) {
                    logger.warn('Instagram insights not available due to permissions, trying profile data');
                    try {
                        const profileData = await this.getInstagramProfile(storeId);
                        return {
                            data: {
                                insights: {
                                    impressions: 0,
                                    reach: 0,
                                    profile_views: 0,
                                    // Use follower count from profile if available
                                    followers_count: profileData?.data?.followers_count || 0
                                },
                                error: 'Insights not available due to missing permissions',
                                hasPartialData: true,
                                source: 'profile_fallback',
                                message: 'Showing basic profile data. Reconnect Instagram for detailed insights.'
                            }
                        };
                    } catch (profileError) {
                        logger.warn('Profile data also unavailable:', profileError);
                        // Return minimal structure to prevent UI crashes
                        return {
                            data: {
                                insights: {
                                    impressions: 0,
                                    reach: 0,
                                    profile_views: 0,
                                    followers_count: 0
                                },
                                error: 'Instagram data not available',
                                hasPartialData: false,
                                source: 'error_fallback',
                                message: 'Please reconnect your Instagram account for insights.'
                            }
                        };
                    }
                }
                
                // For auth errors (401), provide helpful message
                if (status === 401) {
                    return {
                        data: {
                            insights: {
                                impressions: 0,
                                reach: 0,
                                profile_views: 0,
                                followers_count: 0
                            },
                            error: 'Instagram access token expired',
                            hasPartialData: false,
                            source: 'auth_error',
                            message: 'Instagram access token has expired. Please reconnect your account.'
                        }
                    };
                }
            }
            
            this.handleApiError(error, 'fetch Instagram insights');
            throw error;
        }
    }

    // Helper method to get current store ID
    private getCurrentStoreId(): string | null {
        try {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            return user.id_store || null;
        } catch {
            return null;
        }
    }

    async getInstagramMedia(params: { timeRange: string, limit: number, mediaType?: string, storeId?: string }) {
        try {
            // Use new Instagram API endpoint
            const store = params.storeId || this.getCurrentStoreId();
            if (!store) {
                throw new Error('Store ID required for Instagram media');
            }
            
            const response = await this.instance.get(`/api/instagram/${store}/media`, {
                params: { 
                    time_range: params.timeRange, 
                    limit: params.limit,
                    page_id: 'auto' // Let backend determine the Instagram page
                }
            });
            return response;
        } catch (error) {
            this.handleApiError(error, 'fetch Instagram media');
            throw error;
        }
    }

    async getInstagramProfile(storeId?: string) {
        try {
            // Use new Instagram API endpoint
            const store = storeId || this.getCurrentStoreId();
            if (!store) {
                throw new Error('Store ID required for Instagram profile');
            }
            
            const response = await this.instance.get(`/api/instagram/${store}/profile`, {
                params: { 
                    page_id: 'auto' // Let backend determine the Instagram page
                }
            });
            return response;
        } catch (error) {
            // Enhanced error handling for profile data
            if (isErrorWithResponse(error)) {
                const status = error.response?.status;
                
                // For permission or auth errors, provide helpful structure
                if (status === 403 || status === 401) {
                    logger.warn(`Instagram profile not available (${status}), providing fallback structure`);
                    return {
                        data: {
                            id: 'unknown',
                            username: 'Instagram Account',
                            name: 'Instagram Business Account',
                            followers_count: 0,
                            follows_count: 0,
                            media_count: 0,
                            biography: '',
                            profile_picture_url: '',
                            website: '',
                            error: status === 401 ? 'Access token expired' : 'Permissions required',
                            hasPartialData: false,
                            source: 'error_fallback',
                            message: status === 401 
                                ? 'Instagram access token has expired. Please reconnect.'
                                : 'Missing Instagram permissions. Please reconnect for profile access.'
                        }
                    };
                }
            }
            
            this.handleApiError(error, 'fetch Instagram profile');
            throw error;
        }
    }

    async getAdMetrics(storeId: string, pageId: string, timeRange?: string, includeOrganic: boolean = true, mongodbOnly: boolean = false) {
        try {
            const params: Record<string, string> = {};
            if (timeRange) params.time_range = timeRange;
            if (includeOrganic !== undefined) params.include_organic = includeOrganic.toString();
            if (mongodbOnly !== undefined) params.mongodb_only = mongodbOnly.toString();
            
            const response = await this.instance.get(`/api/meta/${storeId}/ad-metrics/${pageId}`, {
                params
            });
            return response;
        } catch (error) {
            this.handleApiError(error, 'fetch ad metrics');
            throw error;
        }
    }

    // ... other methods ...
}

// Export instance
export const apiService = new ApiService(); 

// Utility to get the effective store ID based on user role and context
export function getEffectiveStoreID(user: User | null, viewingStoreID: string | null): string {
  if (user?.role === 'admin' && viewingStoreID) {
    return viewingStoreID;
  }
  return user?.id_store?.toString() || '';
} 