#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up deprecated Meta permissions from the database.

This script removes deprecated permissions that are no longer valid in Meta API v23.0
and updates the permission records to only include currently valid permissions.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone
from typing import Dict, List

# Add the backend directory to the Python path
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, backend_dir)

from config.database import db_analysis
from models.meta import PermissionChange

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# List of deprecated permissions that should be removed
DEPRECATED_PERMISSIONS = [
    "threads_business_basic",
    "manage_app_solution", 
    "manage_app_solution_settings",
    "pages_utility_messaging",
    "pages_utility_messages"  # Alternative name
]

# List of currently valid permissions for Meta API v23.0
VALID_PERMISSIONS = [
    # Core Facebook permissions
    "pages_show_list",
    "pages_read_engagement", 
    "pages_read_user_content",
    "pages_manage_metadata",
    "pages_messaging",
    "public_profile",
    "email",
    
    # Instagram permissions
    "instagram_basic",
    "instagram_manage_insights",
    "instagram_manage_comments",
    "instagram_content_publish",
    "instagram_branded_content_brand",
    
    # Business and Ads permissions
    "business_management",
    "ads_management",
    "ads_read",
    
    # WhatsApp Business permissions (if applicable)
    "whatsapp_business_messaging",
    "whatsapp_business_manage_events"
]

async def get_all_permission_records() -> List[Dict]:
    """Get all permission records from the database."""
    try:
        cursor = db_analysis["meta_permissions"].find({})
        records = await cursor.to_list(length=None)
        logger.info(f"Found {len(records)} permission records in database")
        return records
    except Exception as e:
        logger.error(f"Error fetching permission records: {str(e)}")
        return []

async def cleanup_deprecated_permissions_for_store(store_id: str, permissions: Dict[str, str]) -> Dict[str, str]:
    """Clean up deprecated permissions for a single store."""
    cleaned_permissions = {}
    removed_permissions = []
    
    for permission, status in permissions.items():
        if permission in DEPRECATED_PERMISSIONS:
            removed_permissions.append(permission)
            logger.info(f"Removing deprecated permission '{permission}' for store {store_id}")
        elif permission in VALID_PERMISSIONS:
            cleaned_permissions[permission] = status
        else:
            # Log unknown permissions but keep them for manual review
            logger.warning(f"Unknown permission '{permission}' found for store {store_id}. Keeping for manual review.")
            cleaned_permissions[permission] = status
    
    if removed_permissions:
        logger.info(f"Removed {len(removed_permissions)} deprecated permissions for store {store_id}: {removed_permissions}")
    
    return cleaned_permissions

async def update_permission_record(store_id: str, cleaned_permissions: Dict[str, str], original_record: Dict) -> bool:
    """Update a permission record with cleaned permissions."""
    try:
        # Create a change record for the cleanup
        cleanup_change = PermissionChange(
            permission="system_cleanup",
            status="deprecated_permissions_removed",
            timestamp=datetime.now(timezone.utc)
        )
        
        # Update the record
        update_result = await db_analysis["meta_permissions"].update_one(
            {"store_id": store_id},
            {
                "$set": {
                    "permissions": cleaned_permissions,
                    "last_updated": datetime.now(timezone.utc)
                },
                "$push": {
                    "revocation_history": cleanup_change.dict()
                }
            }
        )
        
        if update_result.modified_count > 0:
            logger.info(f"Successfully updated permissions for store {store_id}")
            return True
        else:
            logger.warning(f"No changes made to permissions for store {store_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error updating permissions for store {store_id}: {str(e)}")
        return False

async def cleanup_all_deprecated_permissions():
    """Main function to clean up all deprecated permissions."""
    logger.info("Starting cleanup of deprecated Meta permissions...")
    
    # Get all permission records
    records = await get_all_permission_records()
    
    if not records:
        logger.info("No permission records found. Nothing to clean up.")
        return
    
    total_records = len(records)
    updated_records = 0
    
    for record in records:
        store_id = record.get("store_id")
        permissions = record.get("permissions", {})
        
        if not store_id:
            logger.warning("Found record without store_id, skipping...")
            continue
        
        # Check if this record has any deprecated permissions
        has_deprecated = any(perm in DEPRECATED_PERMISSIONS for perm in permissions.keys())
        
        if has_deprecated:
            logger.info(f"Cleaning up deprecated permissions for store {store_id}")
            
            # Clean up the permissions
            cleaned_permissions = await cleanup_deprecated_permissions_for_store(store_id, permissions)
            
            # Update the record
            if await update_permission_record(store_id, cleaned_permissions, record):
                updated_records += 1
        else:
            logger.info(f"No deprecated permissions found for store {store_id}")
    
    logger.info(f"Cleanup completed. Updated {updated_records} out of {total_records} records.")

async def validate_cleanup():
    """Validate that the cleanup was successful."""
    logger.info("Validating cleanup results...")
    
    records = await get_all_permission_records()
    remaining_deprecated = 0
    
    for record in records:
        store_id = record.get("store_id")
        permissions = record.get("permissions", {})
        
        deprecated_found = [perm for perm in permissions.keys() if perm in DEPRECATED_PERMISSIONS]
        if deprecated_found:
            remaining_deprecated += 1
            logger.warning(f"Store {store_id} still has deprecated permissions: {deprecated_found}")
    
    if remaining_deprecated == 0:
        logger.info("✅ Validation successful: No deprecated permissions found in database")
    else:
        logger.error(f"❌ Validation failed: {remaining_deprecated} records still have deprecated permissions")
    
    return remaining_deprecated == 0

async def main():
    """Main entry point."""
    try:
        logger.info("Meta API v23.0 Deprecated Permissions Cleanup")
        logger.info("=" * 50)
        
        # Perform cleanup
        await cleanup_all_deprecated_permissions()
        
        # Validate results
        if await validate_cleanup():
            logger.info("✅ Deprecated permissions cleanup completed successfully!")
        else:
            logger.error("❌ Cleanup validation failed. Manual intervention may be required.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Cleanup script failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
