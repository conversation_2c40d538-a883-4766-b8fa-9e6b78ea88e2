"""
Instagram data transformation utilities to ensure frontend/backend compatibility
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class InstagramDataTransformer:
    """Transforms Instagram data between backend and frontend formats"""
    
    @staticmethod
    def transform_metrics_to_frontend(backend_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform backend Instagram metrics to frontend-expected format
        
        Backend format (from our API):
        {
            "impressions": 1000,
            "reach": 800,
            "profile_visits": 50,
            "likes": 100,
            "comments": 20,
            "saves": 15,
            "media_count": 5,
            "engagement_rate": 16.875
        }
        
        Frontend expected format:
        {
            "organic_reach": 800,
            "organic_impressions": 1000,
            "engagement": {
                "likes": 100,
                "comments": 20,
                "shares": 0,
                "saves": 15
            },
            "profile_activity": {
                "visits": 50,
                "messages": 0,
                "link_clicks": 0
            }
        }
        """
        return {
            "organic_reach": backend_metrics.get("reach", 0),
            "organic_impressions": backend_metrics.get("impressions", 0),
            "engagement": {
                "likes": backend_metrics.get("likes", 0),
                "comments": backend_metrics.get("comments", 0),
                "shares": backend_metrics.get("shares", 0),  # Instagram doesn't provide shares
                "saves": backend_metrics.get("saves", 0)
            },
            "profile_activity": {
                "visits": backend_metrics.get("profile_visits", 0),
                "messages": backend_metrics.get("messages", 0),
                "link_clicks": backend_metrics.get("link_clicks", 0)
            },
            "media_count": backend_metrics.get("media_count", 0),
            "engagement_rate": backend_metrics.get("engagement_rate", 0),
            "last_updated": backend_metrics.get("last_updated")
        }
    
    @staticmethod
    def transform_media_to_post(media_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform Instagram media object to MetaPost format expected by frontend
        
        Backend format (Instagram API):
        {
            "id": "*********",
            "caption": "Great post!",
            "media_type": "IMAGE",
            "media_url": "https://...",
            "thumbnail_url": "https://...",
            "permalink": "https://...",
            "timestamp": "2023-01-01T12:00:00+0000",
            "like_count": 50,
            "comments_count": 10
        }
        
        Frontend expected format (MetaPost/InstagramPost):
        {
            "id": "*********",
            "message": "Great post!",
            "created_time": "2023-01-01T12:00:00+0000",
            "permalink_url": "https://...",
            "type": "image",
            "platform": "instagram",
            "instagram_metrics": {
                "like_count": 50,
                "comments_count": 10,
                "saved_count": 0,
                "impressions": 0,
                "reach": 0
            }
        }
        """
        # Map Instagram media_type to frontend type
        media_type_mapping = {
            "IMAGE": "image",
            "VIDEO": "video", 
            "CAROUSEL_ALBUM": "carousel_album"
        }
        
        return {
            "id": media_data.get("id"),
            "message": media_data.get("caption", ""),
            "created_time": media_data.get("timestamp"),
            "permalink_url": media_data.get("permalink"),
            "type": media_type_mapping.get(media_data.get("media_type") or "", "image"),
            "platform": "instagram",
            "instagram_metrics": {
                "like_count": media_data.get("like_count", 0),
                "comments_count": media_data.get("comments_count", 0),
                "saved_count": media_data.get("saved_count", 0),
                "impressions": media_data.get("impressions", 0),
                "reach": media_data.get("reach", 0)
            },
            "media_url": media_data.get("media_url"),
            "thumbnail_url": media_data.get("thumbnail_url")
        }
    
    @staticmethod
    def transform_insights_to_frontend(insights_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Transform Instagram insights data to frontend format
        
        Backend format (Instagram API insights):
        [
            {
                "name": "impressions",
                "values": [{"value": 1000, "end_time": "2023-01-01T12:00:00+0000"}]
            },
            {
                "name": "reach", 
                "values": [{"value": 800, "end_time": "2023-01-01T12:00:00+0000"}]
            }
        ]
        
        Frontend expected format:
        {
            "impressions": 1000,
            "reach": 800
        }
        """
        transformed = {}
        
        for insight in insights_data:
            metric_name = insight.get("name")
            values = insight.get("values", [])
            
            if values and len(values) > 0:
                # Take the most recent value
                latest_value = values[-1] if values else {}
                metric_value = latest_value.get("value", 0)
                
                # Handle nested values (for breakdown metrics)
                if isinstance(metric_value, dict):
                    # Sum up breakdown values
                    if isinstance(metric_value, dict):
                        metric_value = sum(metric_value.values()) if metric_value else 0
                
                transformed[metric_name] = metric_value
        
        return transformed
    
    @staticmethod
    def transform_ad_metrics_to_frontend(ad_data: Dict[str, Any], organic_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform combined ad and organic data to frontend InstagramAdMetrics format
        
        Frontend expected format:
        {
            "ad_metrics": {
                "spend": 100,
                "impressions": 5000,
                "reach": 3000,
                "clicks": 200,
                "conversions": 10,
                "ctr": 4.0,
                "cpc": 0.5,
                "cost_per_conversion": 10.0,
                "roi": 150.0
            },
            "organic_metrics": {...},
            "combined": {
                "total_impressions": 6000,
                "total_reach": 3800
            },
            "source": "combined"
        }
        """
        # Extract ad metrics
        ad_metrics = {
            "spend": ad_data.get("spend", 0),
            "impressions": ad_data.get("impressions", 0),
            "reach": ad_data.get("reach", 0),
            "clicks": ad_data.get("clicks", 0),
            "conversions": ad_data.get("conversions", 0),
            "ctr": ad_data.get("ctr", 0),
            "cpc": ad_data.get("cpc", 0),
            "cost_per_conversion": ad_data.get("cost_per_conversion", 0),
            "roi": ad_data.get("roi", 0),
            "account_currency": ad_data.get("account_currency", "USD")
        }
        
        # Transform organic metrics
        organic_metrics = InstagramDataTransformer.transform_metrics_to_frontend(organic_data)
        
        # Calculate combined metrics
        combined_impressions = ad_metrics["impressions"] + organic_metrics.get("organic_impressions", 0)
        combined_reach = ad_metrics["reach"] + organic_metrics.get("organic_reach", 0)
        
        # Determine source
        has_ad_data = ad_metrics["impressions"] > 0 or ad_metrics["spend"] > 0
        has_organic_data = organic_metrics.get("organic_impressions", 0) > 0
        
        if has_ad_data and has_organic_data:
            source = "combined"
        elif has_ad_data:
            source = "ads_only"
        else:
            source = "organic_only"
        
        return {
            "ad_metrics": ad_metrics,
            "organic_metrics": organic_metrics,
            "combined": {
                "total_impressions": combined_impressions,
                "total_reach": combined_reach
            },
            "source": source,
            "daily_metrics": ad_data.get("daily_metrics", []),
            "campaigns": ad_data.get("campaigns", [])
        }
    
    @staticmethod
    def transform_page_to_frontend(page_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform Instagram business account data to frontend MetaPage format
        
        Backend format (Instagram API):
        {
            "id": "*********",
            "username": "myaccount",
            "profile_picture_url": "https://...",
            "followers_count": 1000,
            "media_count": 50,
            "access_token": "token"
        }
        
        Frontend expected format (MetaPage):
        {
            "id": "*********", 
            "name": "myaccount",
            "access_token": "token",
            "category": "Business",
            "platform": "instagram",
            "followers_count": 1000,
            "media_count": 50,
            "profile_picture_url": "https://..."
        }
        """
        return {
            "id": page_data.get("id"),
            "name": page_data.get("username", page_data.get("name", "")),
            "access_token": page_data.get("access_token"),
            "category": "Business",  # Instagram Business accounts are always business
            "category_list": [{"id": "business", "name": "Business"}],
            "tasks": ["ANALYZE", "ADVERTISE"],  # Standard Instagram business tasks
            "platform": "instagram",
            "followers_count": page_data.get("followers_count", 0),
            "media_count": page_data.get("media_count", 0),
            "profile_picture_url": page_data.get("profile_picture_url")
        }
    
    @staticmethod
    def transform_profile_to_frontend(profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform Instagram profile data to frontend format
        
        Backend format (Instagram API):
        {
            "id": "*********",
            "username": "myaccount",
            "name": "My Account",
            "biography": "This is my bio",
            "followers_count": 1000,
            "follows_count": 500,
            "media_count": 50,
            "profile_picture_url": "https://...",
            "website": "https://example.com"
        }
        
        Frontend expected format:
        {
            "id": "*********",
            "username": "myaccount",
            "name": "My Account",
            "biography": "This is my bio",
            "followers_count": 1000,
            "follows_count": 500,
            "media_count": 50,
            "profile_picture_url": "https://...",
            "website": "https://example.com",
            "platform": "instagram"
        }
        """
        return {
            "id": profile_data.get("id"),
            "username": profile_data.get("username"),
            "name": profile_data.get("name"),
            "biography": profile_data.get("biography", ""),
            "followers_count": profile_data.get("followers_count", 0),
            "follows_count": profile_data.get("follows_count", 0),
            "media_count": profile_data.get("media_count", 0),
            "profile_picture_url": profile_data.get("profile_picture_url"),
            "website": profile_data.get("website"),
            "platform": "instagram"
        }

    @staticmethod
    def transform_error_to_frontend(error: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform backend Instagram error to frontend format
        
        Backend format:
        {
            "error": "Token expired",
            "error_code": 190,
            "error_action": "refresh_token",
            "instagram_specific": True
        }
        
        Frontend expected format:
        {
            "type": "AUTH_FAILED",
            "message": "Token expired", 
            "originalError": {
                "code": 190,
                "action": "refresh_token",
                "instagram_specific": True
            }
        }
        """
        # Map Instagram error codes to frontend error types
        error_type_mapping = {
            190: "AUTH_FAILED",  # Token expired
            10: "PERMISSION_DENIED",  # Permission denied
            100: "API_ERROR",  # Invalid parameter
            4: "RATE_LIMIT",  # Rate limit
            17: "RATE_LIMIT",  # Rate limit
            2500: "API_ERROR"  # Business account required
        }
        
        error_code = error.get("error_code", 0)
        error_type = error_type_mapping.get(error_code, "API_ERROR")
        
        return {
            "type": error_type,
            "message": error.get("error", "An error occurred"),
            "originalError": {
                "code": error_code,
                "action": error.get("error_action"),
                "instagram_specific": error.get("instagram_specific", True)
            }
        }
    
    @staticmethod
    def validate_frontend_compatibility(data: Dict[str, Any], expected_type: str) -> bool:
        """
        Validate that transformed data matches frontend expectations
        
        Args:
            data: Transformed data
            expected_type: Expected data type ('metrics', 'post', 'page', 'ad_metrics', 'error')
        
        Returns:
            True if data is compatible, False otherwise
        """
        try:
            if expected_type == "metrics":
                required_fields = ["organic_reach", "organic_impressions", "engagement", "profile_activity"]
                return all(field in data for field in required_fields)
            
            elif expected_type == "post":
                required_fields = ["id", "platform", "created_time"]
                return all(field in data for field in required_fields) and data.get("platform") == "instagram"
            
            elif expected_type == "page":
                required_fields = ["id", "name", "platform"]
                return all(field in data for field in required_fields) and data.get("platform") == "instagram"
            
            elif expected_type == "profile":
                required_fields = ["id", "platform"]
                return all(field in data for field in required_fields) and data.get("platform") == "instagram"
            
            elif expected_type == "ad_metrics":
                required_fields = ["ad_metrics", "organic_metrics", "combined", "source"]
                return all(field in data for field in required_fields)
            
            elif expected_type == "error":
                required_fields = ["type", "message"]
                return all(field in data for field in required_fields)
            
            return False
            
        except Exception as e:
            logger.error(f"Error validating frontend compatibility: {str(e)}")
            return False