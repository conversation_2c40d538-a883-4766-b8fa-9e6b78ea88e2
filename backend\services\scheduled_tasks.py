import logging
import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Set, List, Any, Optional, Tuple
from config.database import db_analysis, db_main
from services.meta import sync_meta_data_for_chat, get_sync_metrics

# Configure logging
logger = logging.getLogger(__name__)

class MetaSyncTask:
    def __init__(self, interval_minutes: int = 30):
        self.interval_minutes = interval_minutes
        self.is_running = False
        self.last_run: Dict[str, datetime] = {}
        self.failed_stores: Set[str] = set()
        self.start_time: Optional[datetime] = None
        self.successful_runs: int = 0
        self.failed_runs: int = 0
        self.total_sync_duration_ms: float = 0
        self.performance_history: List[Dict[str, Any]] = []  # Track recent performance
        
    async def start(self):
        """Start the Meta sync task"""
        if self.is_running:
            logger.warning("Meta sync task is already running")
            return
            
        self.is_running = True
        self.start_time = datetime.now(timezone.utc)
        logger.info(f"Meta sync task started at {self.start_time.isoformat()}")
        
        while self.is_running:
            try:
                sync_start = time.time()
                stores_processed, stores_succeeded = await self._sync_all_stores()
                sync_duration = (time.time() - sync_start) * 1000  # ms
                
                # Log performance metrics
                logger.info(
                    f"Meta sync completed in {sync_duration:.2f}ms. "
                    f"Processed: {stores_processed}, Succeeded: {stores_succeeded}, Failed: {stores_processed - stores_succeeded}"
                )
                
                # Track performance history (keep last 10 runs)
                self.performance_history.append({
                    "timestamp": datetime.now(timezone.utc),
                    "duration_ms": sync_duration,
                    "stores_processed": stores_processed,
                    "stores_succeeded": stores_succeeded,
                    "stores_failed": stores_processed - stores_succeeded
                })
                
                if len(self.performance_history) > 10:
                    self.performance_history.pop(0)
                
                # Update aggregate metrics
                self.total_sync_duration_ms += sync_duration
                self.successful_runs += 1
                
                # Get overall sync metrics for logging
                metrics = get_sync_metrics()
                logger.info(
                    f"Sync task metrics - Total: {metrics.get('total_syncs', 0)}, "
                    f"Success: {metrics.get('successful_syncs', 0)}, "
                    f"Failed: {metrics.get('failed_syncs', 0)}, "
                    f"Avg Duration: {metrics.get('average_sync_duration_ms', 0):.2f}ms"
                )
                
            except Exception as e:
                logger.error(f"Error in Meta sync task: {str(e)}")
                self.failed_runs += 1
            finally:
                # Log upcoming wait period
                next_run = datetime.now(timezone.utc) + timedelta(minutes=self.interval_minutes)
                logger.info(f"Meta sync task sleeping. Next run scheduled at {next_run.isoformat()}")
                
                # Wait for next interval
                await asyncio.sleep(self.interval_minutes * 60)
                
    async def stop(self):
        """Stop the Meta sync task"""
        if not self.is_running:
            logger.warning("Meta sync task is not running")
            return
            
        self.is_running = False
        duration = datetime.now(timezone.utc) - self.start_time if self.start_time else timedelta(0)
        logger.info(f"Meta sync task stopped after running for {duration}")
        
    async def _sync_all_stores(self) -> Tuple[int, int]:
        """Sync Meta data for all stores"""
        stores_processed = 0
        stores_succeeded = 0
        
        try:
            # Get all stores with Meta integration
            stores = db_analysis["global_analysis"].find(
                {"meta_integration.connected": True},
                {"_id": 1}
            )
            
            all_stores = await stores.to_list(length=None)
            logger.info(f"Found {len(all_stores)} stores with Meta integration")
            
            for store in all_stores:
                store_id = str(store["_id"])
                stores_processed += 1
                
                try:
                    # Log start of store processing with ID for traceability
                    logger.info(f"Processing store {store_id} ({stores_processed}/{len(all_stores)})")
                    
                    # Skip if synced recently
                    last_sync = self.last_run.get(store_id)
                    if last_sync:
                        age = datetime.now(timezone.utc) - last_sync
                        if age.total_seconds() < (self.interval_minutes * 60):
                            logger.info(f"Skipping store {store_id}, synced recently ({age.total_seconds():.1f}s ago)")
                            continue
                    
                    # Track store-specific sync timing
                    store_start = time.time()
                    
                    # Sync store data
                    success = await sync_meta_data_for_chat(store_id)
                    
                    # Log store processing time
                    store_duration = (time.time() - store_start) * 1000  # ms
                    logger.info(f"Store {store_id} processed in {store_duration:.2f}ms. Success: {success}")
                    
                    if success:
                        self.last_run[store_id] = datetime.now(timezone.utc)
                        if store_id in self.failed_stores:
                            logger.info(f"Store {store_id} recovered from previous failures")
                            self.failed_stores.remove(store_id)
                        stores_succeeded += 1
                    else:
                        self.failed_stores.add(store_id)
                        logger.warning(f"Failed to sync Meta data for store {store_id}")
                        
                except Exception as e:
                    logger.error(f"Error syncing Meta data for store {store_id}: {str(e)}")
                    self.failed_stores.add(store_id)
                    
            # Log summary
            logger.info(f"Meta sync completed. Processed {stores_processed} stores, "
                        f"succeeded: {stores_succeeded}, failed: {len(self.failed_stores)}")
            
            return stores_processed, stores_succeeded
            
        except Exception as e:
            logger.error(f"Error getting stores for Meta sync: {str(e)}")
            return stores_processed, stores_succeeded
            
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the sync task"""
        now = datetime.now(timezone.utc)
        uptime = now - self.start_time if self.start_time else timedelta(0)
        
        # Calculate average sync duration
        avg_duration = 0
        if self.successful_runs > 0:
            avg_duration = self.total_sync_duration_ms / self.successful_runs
        
        # Calculate average success rate
        total_runs = self.successful_runs + self.failed_runs
        success_rate = (self.successful_runs / total_runs * 100) if total_runs > 0 else 0
        
        return {
            "is_running": self.is_running,
            "interval_minutes": self.interval_minutes,
            "uptime_seconds": int(uptime.total_seconds()),
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "last_run": {k: v.isoformat() for k, v in self.last_run.items()},
            "failed_stores": list(self.failed_stores),
            "metrics": {
                "successful_runs": self.successful_runs,
                "failed_runs": self.failed_runs,
                "success_rate": f"{success_rate:.1f}%",
                "avg_sync_duration_ms": f"{avg_duration:.2f}",
                "total_sync_duration_ms": f"{self.total_sync_duration_ms:.2f}"
            },
            "recent_performance": self.performance_history
        }

# Create singleton instance
meta_sync_task = MetaSyncTask()

async def check_and_refresh_meta_tokens():
    """
    Scheduled task to check for Meta tokens that are about to expire
    and refresh them proactively.
    """
    try:
        logger.info("Running scheduled Meta token refresh check")
        
        # Get Meta integrations with tokens that are more than 45 days old
        # (before the typical 60-day expiration)
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=45)
        
        # Find tokens that might expire soon
        expiring_tokens = db_analysis["meta_integrations"].find({
            "token_created_at": {"$lt": cutoff_date},
            "active": True
        })
        
        refresh_count = 0
        async for integration in expiring_tokens:
            token = integration.get("access_token")
            if not token:
                continue
                
            logger.info(f"Proactively refreshing token for integration ID: {integration.get('_id')}")
            
            try:
                # Import here to avoid circular imports
                from services.auth import refresh_meta_token
                
                # Try to refresh the token
                new_token = refresh_meta_token(token)
                if new_token and new_token != token:
                    # Update the token in the database
                    await db_analysis["meta_integrations"].update_one(
                        {"_id": integration.get("_id")},
                        {
                            "$set": {
                                "access_token": new_token,
                                "token_created_at": datetime.now(timezone.utc)
                            }
                        }
                    )
                    
                    # Also update any associated pages
                    await db_analysis["meta_pages"].update_many(
                        {"access_token": token},
                        {
                            "$set": {
                                "access_token": new_token,
                                "token_updated_at": datetime.now(timezone.utc)
                            }
                        }
                    )
                    
                    refresh_count += 1
                    logger.info(f"Successfully refreshed token for integration ID: {integration.get('_id')}")
                else:
                    logger.warning(f"Token refresh failed or unnecessary for integration ID: {integration.get('_id')}")
            except Exception as e:
                logger.error(f"Error refreshing token for integration ID {integration.get('_id')}: {str(e)}")
        
        logger.info(f"Completed Meta token refresh check. Refreshed {refresh_count} tokens.")
    except Exception as e:
        logger.error(f"Error in Meta token refresh scheduled task: {str(e)}") 