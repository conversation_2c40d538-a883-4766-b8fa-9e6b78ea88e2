import { MetaAuthService } from './auth';
import { isFacebookSDKReady } from './init';
import { MetaErrorType } from './types';
import { logger } from '../utils/logger';

// Event constants
export const META_AUTH_STATE_CHANGE = 'meta-auth-state-change';

// Callback queue for pending operations
type AuthCallback = () => void;
const authCallbackQueue: AuthCallback[] = [];
let isProcessingQueue = false;

/**
 * Validates if the Meta authentication is ready for API calls
 * @returns true if authenticated and ready, false otherwise
 */
export const isMetaAuthReady = (): boolean => {
  // Check if Facebook SDK is loaded and ready
  if (!isFacebookSDKReady()) {
    logger.warn('Meta authentication check failed: Facebook SDK not ready');
    return false;
  }

  // Check if we have a valid token
  if (!MetaAuthService.hasValidToken()) {
    logger.warn('Meta authentication check failed: No valid token');
    return false;
  }

  return true;
};

/**
 * Safely executes a function only if Meta authentication is ready
 * @param callback Function to execute when authenticated
 * @param options Configuration options
 * @returns Promise that resolves with callback result or rejects with auth error
 */
export const withMetaAuth = async <T>(
  callback: () => Promise<T> | T,
  options: { queue?: boolean; throwOnFailure?: boolean } = {}
): Promise<T> => {
  const { queue = true, throwOnFailure = true } = options;

  // Check if authentication is ready
  if (isMetaAuthReady()) {
    // Execute immediately if authenticated
    return await callback();
  }

  // Either queue the callback or throw/return based on options
  if (queue) {
    return new Promise<T>((resolve, reject) => {
      // Add to queue to be executed when auth is ready
      authCallbackQueue.push(async () => {
        try {
          const result = await callback();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      
      logger.debug(`Added callback to Meta auth queue. Queue size: ${authCallbackQueue.length}`);
    });
  }

  // If we don't queue and throwOnFailure is true, throw an auth error
  if (throwOnFailure) {
    throw {
      type: MetaErrorType.AUTH_FAILED,
      message: 'Meta authentication required to perform this operation'
    };
  }

  // Return undefined if not throwing (TypeScript will complain but this is intentional)
  return undefined as unknown as T;
};

/**
 * Process the queue of callbacks waiting for authentication
 */
export const processAuthCallbackQueue = (): void => {
  // Prevent concurrent processing
  if (isProcessingQueue || authCallbackQueue.length === 0) {
    return;
  }

  // Don't process if auth isn't ready
  if (!isMetaAuthReady()) {
    logger.warn('Cannot process Meta auth queue: Auth not ready');
    return;
  }

  isProcessingQueue = true;
  logger.debug(`Processing Meta auth queue. ${authCallbackQueue.length} callbacks pending.`);

  // Take a snapshot of the current queue and clear it
  const currentQueue = [...authCallbackQueue];
  authCallbackQueue.length = 0;

  // Execute all callbacks
  Promise.allSettled(currentQueue.map(callback => {
    try {
      return callback();
    } catch (error) {
      logger.error('Error executing queued Meta auth callback:', error);
      return Promise.reject(error);
    }
  })).finally(() => {
    isProcessingQueue = false;
    
    // If new callbacks were added during processing, process them too
    if (authCallbackQueue.length > 0) {
      setTimeout(processAuthCallbackQueue, 0);
    }
  });
};

// Set up event listener for auth state changes to process queue
document.addEventListener(META_AUTH_STATE_CHANGE, () => {
  logger.debug('Meta auth state changed, processing callback queue');
  setTimeout(processAuthCallbackQueue, 100); // Short delay to ensure auth state is fully updated
}); 