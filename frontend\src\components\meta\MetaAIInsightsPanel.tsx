import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Grid, 
  Alert,
  Button,
  Paper,
  Tabs,
  Tab
} from '@mui/material';
import { MetaPage, MetaInsight } from '../../services/types';
import { InsightCard } from './InsightCard';
import { MetaAdvancedAnalyticsPanel } from './MetaAdvancedAnalyticsPanel';
import { authService } from '../../services/authService';
import { fetchWithDeduplication } from '../../services/apiService';
import { logger } from '../../utils/logger';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`insight-tabpanel-${index}`}
      aria-labelledby={`insight-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

interface MetaAIInsightsPanelProps {
  page: MetaPage;
  useMockData?: boolean;
}

/**
 * Component for displaying AI-generated insights for Meta data
 */
export const MetaAIInsightsPanel: React.FC<MetaAIInsightsPanelProps> = ({ 
  page,
  useMockData = false
}) => {
  const [insights, setInsights] = useState<MetaInsight[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  // Fetch insights from our new backend service
  const fetchInsights = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (useMockData) {
        // Generate mock insights when using mock data
        const mockInsights: MetaInsight[] = [
          {
            id: `mock-insight-1-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "content",
            title: "Content Performance Insight",
            text: "Posts with images receive 2.3x more engagement than text-only posts. Consider including high-quality visuals in your content strategy.",
            confidence: 0.92,
            created_at: new Date().toISOString(),
            recommendations: [
              "Include high-quality images in at least 80% of your posts",
              "Test carousel posts for even higher engagement",
              "Use brand-consistent visuals to maintain recognition"
            ],
            is_mock: true,
            insight_type: "content_analysis",
            insight_text: "Posts with images receive 2.3x more engagement than text-only posts. Consider including high-quality visuals in your content strategy.",
            timestamp: new Date().toISOString(),
            source_data_type: "posts",
            source_data_id: page.id
          },
          {
            id: `mock-insight-2-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "audience",
            title: "Audience Engagement Insight",
            text: "Your audience is most active between 6-8pm on weekdays. Scheduling posts during this time could increase visibility by up to 35%.",
            confidence: 0.87,
            created_at: new Date().toISOString(),
            recommendations: [
              "Schedule important posts between 6-8pm on weekdays",
              "Test posting on Tuesdays and Thursdays for highest engagement",
              "Consider using Meta's scheduling tools to automate posting at optimal times"
            ],
            is_mock: true,
            insight_type: "audience",
            insight_text: "Your audience is most active between 6-8pm on weekdays. Scheduling posts during this time could increase visibility by up to 35%.",
            timestamp: new Date().toISOString(),
            source_data_type: "audience",
            source_data_id: page.id
          },
          {
            id: `mock-insight-3-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "correlation",
            title: "Sales Correlation Insight",
            text: "Posts about product features correlate with a 28% increase in website visits within 48 hours. Focus on highlighting product benefits in your content.",
            confidence: 0.85,
            created_at: new Date().toISOString(),
            recommendations: [
              "Create more posts highlighting specific product features",
              "Include direct links to product pages in your posts",
              "Follow up feature posts with limited-time offers to convert interest to sales"
            ],
            is_mock: true,
            insight_type: "correlation",
            insight_text: "Posts about product features correlate with a 28% increase in website visits within 48 hours. Focus on highlighting product benefits in your content.",
            timestamp: new Date().toISOString(),
            source_data_type: "correlation",
            source_data_id: page.id
          }
        ];
        
        setInsights(mockInsights);
      } else {
        // Use the new direct endpoint
        const token = authService.getToken(); // Correct key: 'token'
        if (!token) {
          throw new Error('Authentication token not found');
        }
        
        const apiUrl = `/api/insights/direct/page/${page.id}`;
        logger.debug(`[Insights Fetch] Calling: ${apiUrl}`); // Log the URL being called
        
        const response = await fetch(apiUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        logger.debug(`[Insights Fetch] Response Status: ${response.status}`); // Log status
        if (!response.ok) {
          const errorText = await response.text(); // Get error text for logging
          logger.error(`[Insights Fetch] Failed. Status: ${response.status}, Body: ${errorText}`);
          // Try to parse error if JSON, otherwise use text
          let detail = `HTTP error! status: ${response.status}`; 
          try {
              const errorJson = JSON.parse(errorText);
              detail = errorJson.detail || detail;
          } catch { /* Ignore if not JSON */ }
          throw new Error(`Failed to fetch insights: ${detail}`);
        }
        
        const data = await response.json();
        logger.debug(`[Insights Fetch] Success. Received ${data.length} insights.`);
        
        // Basic validation: Check if data is an array
        if (!Array.isArray(data)) {
          logger.error('[Insights Fetch] Invalid data format received. Expected array.', data);
          throw new Error('Invalid data format received from server.');
        }
        
        // If data is empty, show a specific message instead of the generic error
        if (data.length === 0) {
          logger.debug('[Insights Fetch] No insights found for this page.');
          // Setting insights to empty array and clearing error is enough,
          // the component already handles rendering the "No insights available" message.
          setInsights([]);
        } else {
          setInsights(data);
        }
      }
    } catch (err) {
      // Ensure err is an Error object
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('[Insights Fetch] Error in fetchInsights block:', error.message);
      // Set a user-friendly error message, including details if available
      setError(`Failed to fetch insights: ${error.message}. Please try again later.`);
      setInsights([]); // Clear insights on error
    } finally {
      setLoading(false);
    }
  }, [page, useMockData]);

  // Generate new insights
  const generateInsights = async () => {
    setIsGenerating(true);
    setError(null);
    
    try {
      if (useMockData) {
        // Generate new mock insights
        const newMockInsights: MetaInsight[] = [
          {
            id: `mock-insight-new-1-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "content",
            title: "New Content Strategy Insight",
            text: "Question-based posts are generating 45% more comments than statement posts. Consider incorporating more questions in your content to boost engagement.",
            confidence: 0.89,
            created_at: new Date().toISOString(),
            recommendations: [
              "Include at least one question in each post to encourage comments",
              "Use polls and interactive content to increase engagement",
              "Respond to comments promptly to encourage further discussion"
            ],
            is_mock: true,
            insight_type: "content_analysis",
            insight_text: "Question-based posts are generating 45% more comments than statement posts. Consider incorporating more questions in your content to boost engagement.",
            timestamp: new Date().toISOString(),
            source_data_type: "posts",
            source_data_id: page.id
          },
          {
            id: `mock-insight-new-2-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "audience",
            title: "New Audience Growth Insight",
            text: "Your follower growth rate has increased by 12% in the past month. Content about product tutorials is attracting the most new followers.",
            confidence: 0.91,
            created_at: new Date().toISOString(),
            recommendations: [
              "Create more tutorial content to continue attracting new followers",
              "Consider a tutorial series to encourage repeat visits",
              "Highlight customer success stories using your products"
            ],
            is_mock: true,
            insight_type: "audience",
            insight_text: "Your follower growth rate has increased by 12% in the past month. Content about product tutorials is attracting the most new followers.",
            timestamp: new Date().toISOString(),
            source_data_type: "audience",
            source_data_id: page.id
          }
        ];
        
        // Add new mock insights to existing ones
        setInsights(prevInsights => [...newMockInsights, ...prevInsights]);
      } else {
        // Generate real insights through our backend
        await fetchWithDeduplication(`/api/insights/generate/${page.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authService.getToken()}`
          }
        });
        
        // Fetch the updated insights
        await fetchInsights();
      }
    } catch (err) {
      logger.error('Error generating insights:', err);
      setError('Failed to generate insights. Please try again later.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Filter insights based on selected tab
  const filteredInsights = insights.filter(insight => {
    if (tabValue === 0) {
      return true; // All insights
    } else if (tabValue === 1) {
      return insight.insight_type === 'content_analysis';
    } else if (tabValue === 2) {
      return insight.insight_type === 'comment_analysis';
    } else if (tabValue === 3) {
      return insight.insight_type === 'engagement';
    } else if (tabValue === 4) {
      return insight.insight_type === 'audience';
    } else if (tabValue === 5) {
      return insight.insight_type === 'correlation';
    } else if (tabValue === 6) {
      return false; // Advanced Analytics tab is handled separately
    }
    return true;
  });

  // Fetch insights when component mounts
  useEffect(() => {
    fetchInsights();
  }, [fetchInsights]);

  return (
    <Box>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">AI-Generated Insights</Typography>
        <Box>
          <Button 
            variant="outlined" 
            size="small" 
            onClick={fetchInsights}
            disabled={loading || isGenerating}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="contained" 
            size="small" 
            onClick={generateInsights}
            disabled={loading || isGenerating}
            color="primary"
          >
            {isGenerating ? 'Generating...' : 'Generate New Insights'}
          </Button>
        </Box>
      </Box>
      
      {loading || isGenerating ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {insights.length === 0 ? (
            <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                No insights available yet. Click "Generate New Insights" to analyze your Meta data.
              </Typography>
            </Paper>
          ) : (
            <>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Tabs 
                  value={tabValue} 
                  onChange={handleTabChange} 
                  aria-label="insight type tabs"
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab label="All Insights" />
                  <Tab label="Content" />
                  <Tab label="Comments" />
                  <Tab label="Engagement" />
                  <Tab label="Audience" />
                  <Tab label="Correlation" />
                  <Tab label="Advanced Analytics" />
                </Tabs>
              </Box>
              
              <TabPanel value={tabValue} index={0}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={1}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={2}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={3}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={4}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={5}>
                <Grid container spacing={2}>
                  {filteredInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))}
                  
                  {filteredInsights.length === 0 && (
                    <Grid item xs={12}>
                      <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No insights found for this category. Try generating new insights or selecting a different category.
                        </Typography>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </TabPanel>
              
              <TabPanel value={tabValue} index={6}>
                <MetaAdvancedAnalyticsPanel page={page} useMockData={useMockData} />
              </TabPanel>
            </>
          )}
        </>
      )}
    </Box>
  );
}; 