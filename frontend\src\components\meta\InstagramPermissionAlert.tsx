import React from 'react';
import { Box, Al<PERSON>, Typography, <PERSON><PERSON>, Divider } from '@mui/material';
import InstagramIcon from '@mui/icons-material/Instagram';
import { PERMISSION_DESCRIPTIONS } from '../../services/types';
import { useInstagramPermissions } from '../../util/permissionHelpers';
import { useTranslation } from 'react-i18next';

interface InstagramPermissionAlertProps {
  message: string;
  onManagePermissions: () => void;
}

export const InstagramPermissionAlert: React.FC<InstagramPermissionAlertProps> = ({
  message,
  onManagePermissions
}) => {
  const { t } = useTranslation();
  const {
    canAccessBasic,
    canAccessInsights,
    instagramBasicRevoked,
    instagramInsightsRevoked,
    hasRevokedInstagramPermissions,
    revokedInstagramPermissions
  } = useInstagramPermissions();

  // If Instagram permissions are not revoked, don't show anything
  if (!hasRevokedInstagramPermissions) {
    return null;
  }

  return (
    <Box sx={{ mb: 3, mt: 2 }}>
      <Alert 
        severity="warning" 
        icon={<InstagramIcon sx={{ color: '#C13584' }} />}
        sx={{ 
          mb: 2,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Instagram Data Unavailable
        </Typography>
        
        {message && (
          <Typography 
            variant="body2" 
            sx={{ 
              mb: 2, 
              p: 1.5, 
              bgcolor: 'rgba(193, 53, 132, 0.05)', 
              borderRadius: 1,
              border: '1px solid rgba(193, 53, 132, 0.1)'
            }}
          >
            Your query: "{message}"
          </Typography>
        )}

        <Typography variant="body2" paragraph>
          {!canAccessBasic && !canAccessInsights
            ? "You currently don't have any Instagram permissions enabled."
            : "You've revoked some Instagram permissions needed to access this information:"}
        </Typography>
        
        <Box sx={{ mb: 2, pl: 2 }}>
          {revokedInstagramPermissions.map(permission => (
            <Typography 
              key={permission} 
              variant="body2" 
              sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}
            >
              • <strong>{t(PERMISSION_DESCRIPTIONS[permission]?.nameKey)}</strong>
              {PERMISSION_DESCRIPTIONS[permission]?.impactKey && (
                <Typography 
                  component="span" 
                  variant="body2" 
                  color="text.secondary" 
                  sx={{ ml: 1 }}
                >
                  - {t(PERMISSION_DESCRIPTIONS[permission]?.impactKey)}
                </Typography>
              )}
            </Typography>
          ))}
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ bgcolor: 'rgba(193, 53, 132, 0.05)', p: 2, borderRadius: 1, mb: 2 }}>
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            {canAccessBasic || canAccessInsights 
              ? "Currently unavailable data:"
              : "Examples of unavailable data:"}
          </Typography>
          
          <Box component="ul" sx={{ pl: 2, mb: 0 }}>
            {instagramBasicRevoked && (
              <>
                <Typography component="li" variant="body2">Instagram profile information</Typography>
                <Typography component="li" variant="body2">Follower counts</Typography>
                <Typography component="li" variant="body2">Post counts and basic metrics</Typography>
              </>
            )}
            
            {instagramInsightsRevoked && (
              <>
                <Typography component="li" variant="body2">Engagement rates and metrics</Typography>
                <Typography component="li" variant="body2">Audience demographics and insights</Typography>
                <Typography component="li" variant="body2">Detailed post performance statistics</Typography>
                <Typography component="li" variant="body2">Reach and impression data</Typography>
              </>
            )}
          </Box>
        </Box>
        
        <Typography variant="body2" paragraph>
          {canAccessBasic || canAccessInsights
            ? "To access all Instagram data, you'll need to reconnect and grant the missing permissions."
            : "To get Instagram data, you'll need to connect to Meta and grant these permissions."}
        </Typography>
        
        <Button 
          variant="contained" 
          size="small" 
          onClick={onManagePermissions}
          sx={{ bgcolor: '#C13584', '&:hover': { bgcolor: '#A1286A' } }}
        >
          Manage Meta Permissions
        </Button>
      </Alert>
    </Box>
  );
}; 