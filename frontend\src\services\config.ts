/**
 * Meta API Configuration and Validation
 * This file handles validation of required Meta environment variables
 * and configuration settings.
 */
import { logger } from '../utils/logger';

const REQUIRED_ENV_VARS = [
  'VITE_FACEBOOK_APP_ID',
  'VITE_FACEBOOK_APP_VERSION'
];

interface ValidationResult {
  isValid: boolean;
  missing: string[];
  warnings: string[];
}

/**
 * Validate Meta configuration settings
 * @returns Validation result
 */
export function validateMetaConfig(): ValidationResult {
  logger.info('Validating Meta configuration...');
  
  const missing: string[] = [];
  const warnings: string[] = [];
  
  // Check required environment variables
  for (const envVar of REQUIRED_ENV_VARS) {
    if (!import.meta.env[envVar]) {
      missing.push(envVar);
      logger.error(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // Check for HTTPS when not in localhost
  if (
    window.location.protocol !== 'https:' &&
    !window.location.hostname.includes('localhost')
  ) {
    warnings.push('Meta integration requires HTTPS in production environments');
    logger.warn('Meta integration requires HTTPS in production environments');
  }
  
  // Check Facebook App ID format
  const appId = import.meta.env.VITE_FACEBOOK_APP_ID;
  if (appId && !/^\d+$/.test(appId)) {
    warnings.push('VITE_FACEBOOK_APP_ID should contain only digits');
    logger.warn('VITE_FACEBOOK_APP_ID should contain only digits');
  }
  
  // Check Facebook API version format
  const apiVersion = import.meta.env.VITE_FACEBOOK_APP_VERSION;
  if (apiVersion && !apiVersion.startsWith('v')) {
    warnings.push('VITE_FACEBOOK_APP_VERSION should start with "v" (e.g., "v22.0")');
    logger.warn('VITE_FACEBOOK_APP_VERSION should start with "v" (e.g., "v22.0")');
  }
  
  // Log validation results
  if (missing.length === 0 && warnings.length === 0) {
    logger.info('✅ Meta configuration validation passed');
  } else {
    logger.info(`⚠️ Meta configuration validation completed with ${missing.length} errors and ${warnings.length} warnings`);
    
    // Show warning in UI if there are critical issues
    if (missing.length > 0) {
      showConfigurationWarning(missing, warnings);
    }
  }
  
  return {
    isValid: missing.length === 0,
    missing,
    warnings
  };
}

/**
 * Display configuration warning in UI
 * @param missing Missing environment variables
 * @param warnings Configuration warnings
 */
function showConfigurationWarning(missing: string[], warnings: string[]): void {
  // Only show in development environment
  if (!import.meta.env.DEV) return;
  
  // Create warning element
  const warningDiv = document.createElement('div');
  warningDiv.style.position = 'fixed';
  warningDiv.style.bottom = '0';
  warningDiv.style.left = '0';
  warningDiv.style.right = '0';
  warningDiv.style.backgroundColor = '#f44336';
  warningDiv.style.color = 'white';
  warningDiv.style.padding = '10px 20px';
  warningDiv.style.zIndex = '9999';
  warningDiv.style.fontFamily = 'sans-serif';
  warningDiv.style.fontSize = '14px';
  warningDiv.style.textAlign = 'center';
  
  // Create content
  let content = '<strong>Meta Integration Configuration Issues:</strong><ul style="margin: 5px 0; padding-left: 20px;">';
  
  missing.forEach(item => {
    content += `<li>Missing: ${item}</li>`;
  });
  
  warnings.forEach(item => {
    content += `<li>Warning: ${item}</li>`;
  });
  
  content += '</ul><p style="margin: 5px 0;">Meta integration will not function correctly until these issues are resolved.</p>';
  
  // Add close button
  content += '<button style="background: white; border: none; color: #f44336; padding: 5px 10px; margin-left: 10px; cursor: pointer; border-radius: 3px;">Dismiss</button>';
  
  warningDiv.innerHTML = content;
  
  // Add to DOM
  document.body.appendChild(warningDiv);
  
  // Add event listener to close button
  const closeButton = warningDiv.querySelector('button');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      document.body.removeChild(warningDiv);
    });
  }
}

/**
 * Get a configuration value with validation
 * @param key Configuration key
 * @param defaultValue Default value if not found
 * @returns Configuration value
 */
export function getMetaConfig<T>(key: string, defaultValue: T): T {
  const value = import.meta.env[key];
  if (value === undefined) {
    logger.warn(`Meta configuration key not found: ${key}, using default value`);
    return defaultValue;
  }
  return value as unknown as T;
}

// Export default configuration
export const META_CONFIG = {
  appId: getMetaConfig('VITE_FACEBOOK_APP_ID', ''),
  appVersion: getMetaConfig('VITE_FACEBOOK_APP_VERSION', 'v22.0'),
  isProduction: import.meta.env.PROD === true
}; 