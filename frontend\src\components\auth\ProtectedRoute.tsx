import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { logger } from '../../utils/logger';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  logger.debug('ProtectedRoute: Authentication state', { isAuthenticated, isLoading });

  if (isLoading) {
    logger.debug('ProtectedRoute: Still loading, showing spinner');
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#0D6EFD]"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    logger.debug('ProtectedRoute: Not authenticated, redirecting to login');
    return <Navigate to="/login" />;
  }

  logger.debug('ProtectedRoute: Authentication successful, rendering children');
  return <>{children}</>;
};

export default ProtectedRoute; 