import logging
import json
from datetime import datetime, timezone
from typing import List, Dict, Any
from openai import AsyncOpenAI # Import AsyncOpenAI

from config.database import db_analysis # Use the correct exported client name
from models.insights import SEORecommendation, SEOInsightsResponse # Import from insights model

logger = logging.getLogger(__name__)
AI_MODEL = "gpt-4.1-nano" # Use the model specified in READMEMain.md

# Initialize the async client
# Ensure your OPENAI_API_KEY is set in your environment variables
# or configure it directly: client = AsyncOpenAI(api_key="YOUR_API_KEY")
client = AsyncOpenAI()

def get_default_seo_recommendations(lang: str = "en") -> List[SEORecommendation]:
    """
    Provides 3 high-quality default SEO recommendations as fallback.
    
    Args:
        lang: Language code ("en" or "es")
        
    Returns:
        List of 3 SEORecommendation objects
    """
    if lang == "es":
        return [
            SEORecommendation(
                title="Optimizar Títulos de Producto",
                description="Mejorar los títulos de productos incluyendo palabras clave relevantes y descripciones atractivas que mejoren el posicionamiento en buscadores.",
                priority="Alta",
                category="Palabras Clave"
            ),
            SEORecommendation(
                title="Mejorar Descripciones de Producto",
                description="Crear descripciones de producto únicas y detalladas que incluyan palabras clave long-tail y beneficios específicos para los clientes.",
                priority="Media",
                category="Contenido"
            ),
            SEORecommendation(
                title="Optimizar Velocidad del Sitio",
                description="Mejorar los tiempos de carga del sitio web optimizando imágenes, reduciendo el tamaño de archivos y mejorando la experiencia del usuario.",
                priority="Alta",
                category="Técnico"
            )
        ]
    else:
        return [
            SEORecommendation(
                title="Optimize Product Titles",
                description="Improve product titles by including relevant keywords and compelling descriptions that enhance search engine visibility.",
                priority="High",
                category="Keywords"
            ),
            SEORecommendation(
                title="Enhance Product Descriptions",
                description="Create unique and detailed product descriptions that include long-tail keywords and specific customer benefits.",
                priority="Medium",
                category="Content"
            ),
            SEORecommendation(
                title="Improve Site Speed",
                description="Optimize website loading times by compressing images, reducing file sizes, and improving overall user experience.",
                priority="High",
                category="Technical"
            )
        ]

async def generate_seo_recommendations(store_id: str, lang: str = "en") -> SEOInsightsResponse:
    logger.info(f"Generating SEO recommendations for store_id: {store_id}, lang: {lang}")
    try:
        # 1. Fetch store context data
        store_analysis_doc = await db_analysis["global_analysis"].find_one({"_id": store_id})
        # Use the CORRECT database client for product_details_cache
        product_details_doc = await db_analysis["product_details_cache"].find_one({"_id": store_id})

        if not store_analysis_doc:
            logger.warning(f"No global analysis found for store {store_id}")
            # Return empty recommendations if no analysis data is available
            return SEOInsightsResponse(
                recommendations=[],
                generated_at=datetime.now(timezone.utc),
                model_used=AI_MODEL
            )

        store_name = store_analysis_doc.get("store", {}).get("name", "Unknown Store")
        business_type = store_analysis_doc.get("store", {}).get("business_type", "Unknown Business")
        
        products_context = "No product data available."
        if product_details_doc and "products" in product_details_doc:
            # Extract relevant product info (names) - limit to 20 for context size
            product_names = [p.get("name", "") for p in product_details_doc["products"][:20]]
            # Filter out any potential empty strings
            product_names = [name for name in product_names if name]
            if product_names:
                 products_context = f"Sample products: {', '.join(product_names)}."

        language_instruction = ""
        if lang == "es":
            language_instruction = " All text in the JSON output, including titles, descriptions, priorities, and categories, must be in Spanish. For example, a priority value should be 'Alta', 'Media', or 'Baja', and a category like 'Palabras Clave'."

        # 2. Construct Prompt for OpenAI
        prompt = f"""
        You are an SEO expert analyzing an e-commerce store. You MUST provide exactly 3 actionable SEO recommendations.

        Store Context:
        - Store Name: {store_name}
        - Business Type: {business_type}
        - {products_context}

        CRITICAL REQUIREMENTS:
        1. Generate EXACTLY 3 recommendations - no more, no less
        2. Each recommendation must be unique and actionable
        3. Focus on content optimization, keyword strategy, and technical SEO
        4. Make recommendations suitable for e-commerce platforms like Shopify/LaNube{language_instruction}

        OUTPUT FORMAT - Return a JSON object with a "recommendations" key containing an array of exactly 3 objects:
        {{
          "recommendations": [
            {{"title": "First Recommendation Title", "description": "Detailed actionable description", "priority": "High", "category": "Keywords"}},
            {{"title": "Second Recommendation Title", "description": "Detailed actionable description", "priority": "Medium", "category": "Content"}},
            {{"title": "Third Recommendation Title", "description": "Detailed actionable description", "priority": "Low", "category": "Technical"}}
          ]
        }}

        Priority must be one of: "High", "Medium", "Low"
        Category must be one of: "Content", "Keywords", "Technical"
        
        ENSURE you provide exactly 3 distinct recommendations in the JSON format above.
        """
        logger.debug(f"Prompt for SEO Recommendations for store {store_id}: {prompt}")

        # 3. Call OpenAI API using the async client instance
        response = await client.chat.completions.create( # Use the client instance
            model=AI_MODEL,
            messages=[
                {"role": "system", "content": "You are an SEO expert providing recommendations."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )
        ai_response_content = response.choices[0].message.content

        # Enhanced null content validation
        if not ai_response_content or ai_response_content.strip() == "":
            logger.error(f"OpenAI API returned empty content for store {store_id}")
            # Return default recommendations instead of empty
            logger.info(f"Using default recommendations due to empty AI response for store {store_id} (lang: {lang})")
            return SEOInsightsResponse(
                recommendations=get_default_seo_recommendations(lang),
                generated_at=datetime.now(timezone.utc),
                model_used=AI_MODEL
            )

        # 4. Parse AI Response with enhanced error handling
        recommendations_list = []
        try:
            # Attempt to parse the JSON string
            response_data = json.loads(ai_response_content)
            
            # Validate response structure
            if not isinstance(response_data, dict):
                logger.error(f"AI response for store {store_id} (lang: {lang}) was not a JSON object. Type: {type(response_data)}. Raw: {ai_response_content}")
                return SEOInsightsResponse(
                    recommendations=[],
                    generated_at=datetime.now(timezone.utc),
                    model_used=AI_MODEL
                )
            
            # PATCH: Accept both {"recommendations": [...]} and direct array format
            if "recommendations" in response_data:
                raw_list = response_data["recommendations"]
                if not isinstance(raw_list, list):
                    logger.error(f"The 'recommendations' key in AI response for store {store_id} (lang: {lang}) was not a JSON list. Found type: {type(raw_list)}. Raw: {ai_response_content}")
                    raw_list = []
            elif isinstance(response_data, list):
                # Handle direct array response
                logger.info(f"AI response for store {store_id} (lang: {lang}) is a direct array format")
                raw_list = response_data
            else:
                # Check if the response looks like a single recommendation object
                if all(k in response_data for k in ("title", "description")):
                    logger.warning(f"AI response for store {store_id} (lang: {lang}) appears to be a single recommendation object. Wrapping as list.")
                    raw_list = [response_data]
                else:
                    logger.error(f"AI response for store {store_id} (lang: {lang}) missing 'recommendations' key and not a valid single recommendation. Raw: {ai_response_content}")
                    raw_list = []
            
            # Validate each item against the Pydantic model
            valid_items_count = 0
            for item_idx, item_data in enumerate(raw_list):
                if not isinstance(item_data, dict):
                    logger.warning(f"Recommendation item #{item_idx} for store {store_id} (lang: {lang}) is not a dict. Item: {item_data}")
                    continue
                try:
                    recommendations_list.append(SEORecommendation(**item_data))
                    valid_items_count += 1
                except Exception as pydantic_err: # Catch potential Pydantic validation errors more specifically
                    logger.warning(f"Pydantic validation failed for recommendation item #{item_idx} for store {store_id} (lang: {lang}). Error: {pydantic_err}. Item: {item_data}")
            
            logger.info(f"Successfully parsed {valid_items_count} valid recommendation items for store {store_id} (lang: {lang}).")

        except json.JSONDecodeError as json_err:
            logger.error(f"Failed to decode AI JSON response for store {store_id} (lang: {lang}): {json_err}. Raw content length: {len(ai_response_content) if ai_response_content else 0}. Raw: {ai_response_content}")
            # Fallback: try to extract any readable content for debugging
            if ai_response_content:
                logger.error(f"First 500 chars of problematic response: {ai_response_content[:500]}")
        except Exception as parse_err:
            logger.error(f"Unexpected error parsing AI response for store {store_id} (lang: {lang}): {parse_err}. Raw: {ai_response_content}")

        # Enhanced validation: Ensure exactly 3 recommendations
        if len(recommendations_list) < 3:
            logger.warning(f"AI provided only {len(recommendations_list)} recommendations for store {store_id} (lang: {lang}). Expected 3. Using fallback to ensure 3 recommendations.")
            # Use default recommendations to fill the gap
            default_recommendations = get_default_seo_recommendations(lang)
            
            # If we have some valid recommendations, keep them and fill with defaults
            if recommendations_list:
                # Take the defaults we need to reach exactly 3
                needed_count = 3 - len(recommendations_list)
                recommendations_list.extend(default_recommendations[:needed_count])
                logger.info(f"Combined {len(recommendations_list) - needed_count} AI recommendations with {needed_count} default recommendations for store {store_id}")
            else:
                # No valid AI recommendations, use all defaults
                recommendations_list = default_recommendations
                logger.info(f"Using 3 default recommendations for store {store_id} (lang: {lang}) due to AI parsing failure")
        elif len(recommendations_list) > 3:
            # Trim to exactly 3 if AI provided more than expected
            logger.info(f"AI provided {len(recommendations_list)} recommendations for store {store_id} (lang: {lang}). Trimming to exactly 3.")
            recommendations_list = recommendations_list[:3]

        # Final validation and fallback
        if not recommendations_list:
            logger.warning(f"No valid recommendations were parsed from AI response for store {store_id} (lang: {lang}). Returning default recommendations.")
            # Optional: Return default recommendations as fallback
            recommendations_list = get_default_seo_recommendations(lang)

        # Final safety check to ensure exactly 3 recommendations
        if len(recommendations_list) != 3:
            logger.error(f"Critical error: Final recommendations list has {len(recommendations_list)} items instead of 3 for store {store_id} (lang: {lang}). Forcing default recommendations.")
            recommendations_list = get_default_seo_recommendations(lang)
        
        logger.info(f"Successfully generated {len(recommendations_list)} SEO recommendations for store {store_id} (lang: {lang})")
        for i, rec in enumerate(recommendations_list, 1):
            logger.debug(f"Recommendation {i}: {rec.title} | Priority: {rec.priority} | Category: {rec.category}")

        return SEOInsightsResponse(
            recommendations=recommendations_list,
            generated_at=datetime.now(timezone.utc),
            model_used=AI_MODEL
        )

    except Exception as e:
        logger.error(f"Error generating SEO recommendations for store {store_id}: {e}", exc_info=True)
        # Re-raise the exception to be handled by the route
        raise 