import asyncio
from datetime import datetime, timedelta, timezone
from typing import Generator

import mongomock
import pytest
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

import sys
import pathlib

# Asegurar que el directorio backend esté en sys.path para las importaciones absolutas
backend_dir = pathlib.Path(__file__).resolve().parents[2]
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Import utilidades async wrappers
from .utils import AsyncMockDatabase

# -------------------------------------------------
# Event loop (pytest-asyncio)
# -------------------------------------------------

@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:  # type: ignore
    """Asegura un loop por sesión para tests async."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

# -------------------------------------------------
# Base de datos mockeada y parches
# -------------------------------------------------

@pytest.fixture(scope="session")
def _mock_dbs():
    """Crea instancias de mongomock y sus envoltorios async."""
    client = mongomock.MongoClient()
    db_main_sync = client["dunit_main"]
    db_analysis_sync = client["dunit_analysis"]
    return AsyncMockDatabase(db_main_sync), AsyncMockDatabase(db_analysis_sync)


@pytest.fixture(scope="function")
def patch_environment(monkeypatch, _mock_dbs):  # noqa: D401
    """Parchea conexiones DB y email antes de cada test."""
    async_db_main, async_db_analysis = _mock_dbs

    # Crear stub para config.database antes de cualquier importación
    import types
    import sys as _sys

    mock_database_module = types.ModuleType("config.database")
    mock_database_module.db_main = async_db_main  # type: ignore[attr-defined]
    mock_database_module.db_analysis = async_db_analysis  # type: ignore[attr-defined]

    async def _noop(*args, **kwargs):
        return None

    for _fn in (
        "verify_connections",
        "verify_database_setup",
        "collection_exists",
        "ensure_indexes",
        "get_mongodb_connection",
    ):
        setattr(mock_database_module, _fn, _noop)

    # Registrar en sys.modules para rutas absolutas y con prefijo backend
    _sys.modules["config.database"] = mock_database_module
    _sys.modules["backend.config.database"] = mock_database_module

    # ---------------- Parchar databases en módulos que ya han sido importados ---------
    import routes.auth as routes_auth
    import services.auth as services_auth

    # Asegurarnos de que usen los async_dbs
    monkeypatch.setattr(routes_auth, "db_main", async_db_main, raising=True)
    monkeypatch.setattr(routes_auth, "db_analysis", async_db_analysis, raising=True)
    monkeypatch.setattr(services_auth, "db_main", async_db_main, raising=True)
    monkeypatch.setattr(services_auth, "db_analysis", async_db_analysis, raising=True)

    # ----------------- Parcheo de email ----------------------------------------
    from services import email as email_service

    sent_codes = {}

    def fake_send_verification_email(dest: str, code: str) -> bool:  # noqa: D401
        sent_codes[dest] = code
        return True

    # Patch send_verification_email in the original email service module
    monkeypatch.setattr(email_service, "send_verification_email", fake_send_verification_email, raising=True)

    # Patch the reference imported inside routes.auth as well, so the route uses the fake function
    monkeypatch.setattr(routes_auth, "send_verification_email", fake_send_verification_email, raising=True)

    # Ensure send_email_in_background is a no-op that returns True immediately
    monkeypatch.setattr(routes_auth, "send_email_in_background", lambda *a, **k: True, raising=True)

    # smtp_pool no se usa en tests, pero por seguridad
    monkeypatch.setattr(email_service, "smtp_pool", None, raising=False)

    yield {
        "db_main": async_db_main,
        "db_analysis": async_db_analysis,
        "sent_codes": sent_codes,
    }

# -------------------------------------------------
# Seed de usuarios de prueba
# -------------------------------------------------

@pytest.fixture(scope="function")
def seed_users(patch_environment):
    from services.auth import get_password_hash

    db_main = patch_environment["db_main"]
    store_users = db_main["store_users"]  # AsyncMockCollection

    # Contraseña fuerte para todos los usuarios base
    hashed = get_password_hash("StrongPass1")

    future_lock = (datetime.now(timezone.utc) + timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

    users = [
        {
            "email": "<EMAIL>",
            "id_store": 1,
            "pass_dunit": hashed,
            "active": 1,
        },
        {
            "email": "<EMAIL>",
            "id_store": 2,
            "pass_dunit": hashed,
            "two_factor_enabled": True,
            "active": 1,
        },
        {
            "email": "<EMAIL>",
            "id_store": 3,
            "pass_dunit": hashed,
            "account_locked_until": future_lock,
            "failed_login_count": 10,
        },
        {
            "email": "<EMAIL>",
            "id_store": 4,
            "pass_dunit": hashed,
        },
        {
            "email": "<EMAIL>",
            "id_store": 5,
            # Sin pass_dunit para simular usuario que aún no se registró en D-Unit
        },
    ]

    # Inserción síncrona mediante el objeto sync subyacente
    for u in users:
        # store_users es AsyncMockCollection -> _collection contiene la sync
        store_users._collection.insert_one(u)  # type: ignore[attr-defined]

    return users

# -------------------------------------------------
# TestClient con app mínima
# -------------------------------------------------

@pytest.fixture(scope="function")
def client(patch_environment, seed_users):
    from routes.auth import router as auth_router

    app = FastAPI()
    app.include_router(auth_router)

    with TestClient(app) as test_client:
        yield test_client, patch_environment  # Devolvemos env para acceder a códigos 