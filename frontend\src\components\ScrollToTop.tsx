import React from 'react';
import { Fab } from '@mui/material';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

export const ScrollToTop: React.FC = () => {
    const [showButton, setShowButton] = React.useState(false);

    React.useEffect(() => {
        const handleScroll = () => {
            // Show button when page is scrolled more than 300px
            setShowButton(window.scrollY > 300);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    return showButton ? (
        <Fab 
            color="primary" 
            size="small" 
            onClick={scrollToTop}
            sx={{ 
                position: 'fixed',
                bottom: 40,
                right: 40,
                zIndex: 9999,
                bgcolor: '#00A3FF',
                '&:hover': {
                    bgcolor: '#0082CC'
                },
                transition: 'all 0.3s ease',
                opacity: 1,
                boxShadow: '0 6px 16px rgba(0, 163, 255, 0.6)',
                '&:active': {
                    transform: 'scale(0.95)'
                },
                width: 42,
                height: 42,
                minHeight: 42,
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                    '0%': {
                        boxShadow: '0 0 0 0 rgba(0, 163, 255, 0.7)'
                    },
                    '70%': {
                        boxShadow: '0 0 0 12px rgba(0, 163, 255, 0)'
                    },
                    '100%': {
                        boxShadow: '0 0 0 0 rgba(0, 163, 255, 0)'
                    }
                }
            }}
        >
            <KeyboardArrowUpIcon sx={{ color: 'white', fontSize: 24 }} />
        </Fab>
    ) : null;
}; 