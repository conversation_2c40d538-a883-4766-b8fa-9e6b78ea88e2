import logging
from datetime import datetime, timezone, timedelta
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, Body
import traceback
import re
from typing import List, Dict, Any, Optional
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import time

from config.database import db_main, db_analysis
from models.user import User, CompanyProfileUpdate
from services.auth import get_current_active_user, verify_user_can_access_store
from services.store import ensure_store_analysis, save_chat_feedback
from models.feedback import ChatFeedbackInput
from pydantic import BaseModel, EmailStr
from services.seo_service import generate_seo_recommendations
from models.insights import SEOInsightsResponse, CategorySummary
from services import store as store_service_module
from services import auth as auth_service_module
from services import meta as meta_service_module
from services import seo_service as seo_service_module

# Configure logging
logger = logging.getLogger(__name__)

# Helper function to parse metrics from string
def parse_metric_from_string(metric_name: str, text: str) -> float:
    # Regex to find the metric name, capture the number (allowing commas and decimals)
    # Handle variations like missing (AOV) and optional currency prefix (UYU)
    base_metric_name = metric_name.split('(')[0].strip() # Get name before parenthesis
    # New improved pattern:
    pattern = rf"""
        -\s+\*\*                  # Start with '- **' (allowing variable space after '-')
        {re.escape(base_metric_name)} # Match the base metric name (e.g., "Total Revenue")
        (?:\s*\([A-Z]+\))?       # Optionally match ' (AOV)' or similar bracketed text
        \s*                       # Match ':**' with surrounding space
        (?:[A-Z]+\s+)?            # Optionally match currency code and space (e.g., 'UYU ')
        ([\d,]+(?:\.\d+)?)        # Capture the number (integer or decimal with commas)
    """
    # Use re.VERBOSE flag to allow comments and ignore whitespace within the pattern itself for readability
    match = re.search(pattern, text, re.IGNORECASE | re.VERBOSE)

    if match:
        try:
            # Remove commas and convert to float
            value_str = match.group(1).replace(',', '')
            return float(value_str)
        except ValueError:
            logger.error(f"Could not convert value '{match.group(1)}' to float for metric '{metric_name}'")
            return 0.0
    logger.warning(f"Could not find metric '{metric_name}' in text: {text[:100]}...") # Restore original warning if needed
    return 0.0

# Create router
router = APIRouter(prefix="/api/store", tags=["store"])

@router.get("/{store_id}/orders")
async def get_store_orders(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get store orders - now returns empty result as data source is removed"""
    logger.info(f"User {current_user.email} getting orders for store_id: {store_id}")
    return {
        "total_orders": 0,
        "orders": []
    }

@router.get("/{store_id}/customers")
async def get_store_customers(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get store customers - now returns empty result as data source is removed"""
    logger.info(f"User {current_user.email} getting customers for store_id: {store_id}")
    return {
        "total_customers": 0,
        "customers": []
    }

@router.get("/{store_id}/product-list", response_model=Dict[str, Any])
async def get_store_product_list(
    store_id: str,
    background_tasks: BackgroundTasks,
    page: int = 1,
    page_size: int = 100,
    since: str = Query(None, description="Start date (YYYY-MM-DD)"),
    until: str = Query(None, description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} accessed product list for store_id: {store_id}, page: {page}, page_size: {page_size}, since: {since}, until: {until}")
        
        # Fetch data from the cache collection in db_analysis
        start_time = datetime.now(timezone.utc)
        product_cache_data = await db_analysis["product_details_cache"].find_one({
            "_id": store_id
        })
        fetch_time = datetime.now(timezone.utc)
        logger.info(f"Fetched product cache data in {(fetch_time - start_time).total_seconds():.2f} seconds")

        if not product_cache_data or "products" not in product_cache_data:
            logger.warning(f"Product details cache not found or empty for store ID: {store_id}")
            return {
                "total_products": 0,
                "products": [],
                "page": page,
                "page_size": page_size,
                "total_pages": 0
            }

        all_products = product_cache_data.get("products", [])

        # Sort by revenue (descending) to show top performers first
        all_products.sort(key=lambda x: x.get("revenue", 0), reverse=True)
            
        total_products = len(all_products)
        
        # Apply pagination
        skip = (page - 1) * page_size
        paginated_products = all_products[skip : skip + page_size] if skip < total_products else []
        total_pages = (total_products + page_size - 1) // page_size

        logger.info(f"Returning {len(paginated_products)} products from cache for page {page} of {total_pages}")
        
        return {
            "total_products": total_products,
            "products": paginated_products,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "sales_by_date": product_cache_data.get("sales_by_date", []),
            "category_summary": product_cache_data.get("category_summary", {}),
            "store_aggregations": product_cache_data.get("store_aggregations", {})
        }
        
    except Exception as e:
        logger.error(f"Error getting store product list from cache: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve store product list from cache: {str(e)}"
        )

@router.get("/{store_id}/product/{product_id}/sales-history", response_model=List[Dict[str, Any]])
async def get_product_sales_history(
    store_id: str,
    product_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get sales history for a specific product from store_customers_cache"""
    logger.info(f"User {current_user.email} getting sales history for product_id: {product_id} in store_id: {store_id}")
    
    try:
        pipeline = [
            {"$match": {"_id": store_id}}, # Match the specific store
            {"$unwind": "$customers"},
            {"$unwind": "$customers.orders"},
            {"$unwind": "$customers.orders.products"},
            {"$match": {"customers.orders.products.product_id": int(product_id)}}, # Match the specific product
            {"$project": {
                "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$customers.orders.order_date"}},
                "units": "$customers.orders.products.quantity",
                "revenue_per_unit": "$customers.orders.products.price",
                "_id": 0
            }},
            {"$group": {
                "_id": "$date",
                "units_sold": {"$sum": "$units"},
                "revenue": {"$sum": {"$multiply": ["$units", "$revenue_per_unit"]}}
            }},
            {"$project": {
                "date": "$_id",
                "units_sold": 1,
                "revenue": 1,
                "_id": 0
            }},
            {"$sort": {"date": 1}}
        ]
        
        sales_history = await db_analysis["store_customers_cache"].aggregate(pipeline).to_list(length=None)
        logger.info(f"Retrieved {len(sales_history)} sales records for product {product_id} in store {store_id}")
        return sales_history
        
    except Exception as e:
        logger.error(f"Error fetching sales history for product {product_id} in store {store_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching product sales history: {e}")

@router.get("/{store_id}/analysis", response_model=Dict[str, Any])
async def get_store_analysis(
    store_id: str,
    # Remove since/until as they are not used by the service function anymore for the main analysis blob
    # since: str = "", 
    # until: str = "",
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        # Log the request with detailed information
        logger.info(f"User {current_user.email} getting store analysis for store_id: {store_id}")
        
        # Capture start time for performance logging
        start_time = time.time()
        
        # Call the store service to get consolidated analysis data
        analysis_data = await store_service_module.get_store_analysis(store_id)
        
        # Capture fetch time
        fetch_time = time.time()
        
        if not analysis_data:
            logger.warning(f"Store analysis not found for store ID: {store_id}. Returning default structure.")
            # Return a default structure that matches frontend expectations for missing data
            return {
                "metrics": {
                    "total_revenue": 0,
                    "order_count": 0,
                    "avg_order_value": 0,
                    "customer_count": 0
                },
                "analysis": {
                    "summary": "No analysis available for this store yet.",
                    "customer_analysis": "No customer analysis available",
                    "market_position": "No market position analysis available",
                    "competitor_analysis": "No competitor analysis available",
                    "shipping_analysis": {
                        "analysis_text": "No shipping analysis available yet.",
                        "recommendations": []
                    },
                    "product_analysis": "No product analysis available",
                    "product_performance": {},
                    "demographics": {
                        "countries": []
                    }
                },
                "customers": {
                    "total": 0,
                    "with_orders": 0,
                    "repeat": 0,
                    "abandoned_cart_count": 0,
                    "abandoned_cart_total_value": 0,
                    "pending_cart_count": 0,
                    "pending_cart_total_value": 0,
                    "average_spend": 0,
                    "status_distribution": {}
                },
                "store": {
                    "name": "Store Analysis Pending",
                    "business_type": "Unknown",
                    "shipping_methods": []
                },
                "currency": {
                    "native": { "symbol": "$" } # Provide default currency symbol
                },
                "social_media": {},
                "feedback": []
            }

        logger.info(f"Successfully fetched combined analysis for store {store_id} in {(fetch_time - start_time):.2f} seconds")
        
        # The service function already combines data. Return it directly.
        # Ensure the structure matches the frontend's StoreAnalysis interface
        # We might need slight adjustments here based on the exact structure from the service
        # and frontend expectations.
        
        # Basic structure expected by frontend based on StoreAnalysis interface:
        response = {
            "metrics": analysis_data.get("metrics", {}),
            "analysis": analysis_data.get("analysis", {}),
            "customers": analysis_data.get("customers", {}),
            "store": analysis_data.get("store", {}),
            "currency": analysis_data.get("currency", {}).get("native", {}), # Extract native currency object
            "social_media": analysis_data.get("social_media", {}),
            "feedback": analysis_data.get("feedback", []),
            "keywords": analysis_data.get("keywords", None),
            "analysis_metrics_text": analysis_data.get("analysis", {}).get("metrics"),
            "social_media_strategy": analysis_data.get("analysis", {}).get("social_media_strategy")
        }
        
        # Ensure nested structures exist to prevent frontend errors
        if "demographics" not in response["analysis"]:
            response["analysis"]["demographics"] = { "countries": [] }
        elif "countries" not in response["analysis"]["demographics"]:
             response["analysis"]["demographics"]["countries"] = []
             
        if not response.get("customers"):
             response["customers"] = {
                 "total": 0,
                 "with_orders": 0,
                 "repeat": 0,
                 "abandoned_cart_count": 0,
                 "abandoned_cart_total_value": 0,
                 "pending_cart_count": 0,
                 "pending_cart_total_value": 0,
                 "average_spend": 0,
                 "status_distribution": {}
             }

        # **MODIFICATION START:** Explicitly add distributions if they exist in analysis_data[\"customers\"]
        # This ensures they are present even if the default block above was triggered initially
        # because analysis_data[\"customers\"] was missing entirely.
        if analysis_data.get("customers"):
            response["customers"].update({
                k: analysis_data["customers"].get(k, response["customers"].get(k)) # Update safely
                for k in [
                    'coupon_code_distribution', 
                    'payment_method_distribution', 
                    'shipping_method_distribution',
                    'country_distribution', # Keep country distribution too
                    'status_distribution', # And status
                    # Also ensure the most frequent are passed if available
                    'most_frequent_coupon_code',
                    'most_frequent_payment_method',
                    'most_frequent_shipping_method',
                    # Pass other aggregated metrics that might be missing from the default dict
                    'total_customers',
                    'total_store_orders',
                    'average_spend_per_customer',
                    'abandoned_cart_count',
                    'abandoned_cart_customer_count',
                    'abandoned_cart_total_value',
                    'pending_cart_count',
                    'pending_cart_total_value',
                    'customers' # Ensure the actual customer list is passed
                ]
            })
        # **MODIFICATION END**

        return response
        
    except Exception as e:
        logger.error(f"Error getting store analysis: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve store analysis: {str(e)}"
        )

@router.get("/{store_id}/summary")
async def get_store_summary(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} getting store summary for store_id: {store_id}")
        # Get store analysis
        store_analysis = await db_analysis["global_analysis"].find_one({
            "_id": store_id
        })
        
        if not store_analysis:
            return {
                "summary": "No analysis available yet.",
                "last_updated": None
            }
        
        return {
            "summary": store_analysis.get('analysis', {}).get('summary', ''),
            "last_updated": store_analysis.get('metadata', {}).get('last_updated'),
            "metrics": {
                "total_revenue": store_analysis.get('order_statistics', {}).get('total_sales', 0),
                "total_revenue_usd": store_analysis.get('order_statistics', {}).get('total_sales_usd', 0),
                "total_orders": store_analysis.get('order_statistics', {}).get('total_orders', 0),
                "avg_order_value": store_analysis.get('order_statistics', {}).get('average_order_value', 0),
                "avg_order_value_usd": store_analysis.get('order_statistics', {}).get('average_order_value_usd', 0),
                "total_customers": store_analysis.get('metrics', {}).get('customer_count', 0),
                "active_customers": store_analysis.get('relationships', {}).get('customers_with_orders', 0),
                "repeat_customers": store_analysis.get('relationships', {}).get('repeat_customers', 0)
            },
            "currency": store_analysis.get('currency', {}).get('native', {}),
            "visits": {
                "total": store_analysis.get('visits', {}).get('total_visits', 0),
                "unique": store_analysis.get('visits', {}).get('unique_visitors', 0)
            }
        }
    except Exception as e:
        logger.error(f"Error getting store summary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{store_id}/performance")
async def get_store_performance(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} getting store performance for store_id: {store_id}")
        # Get store analysis
        store_analysis = await db_analysis["global_analysis"].find_one({
            "_id": store_id
        })
        
        # Get additional data from other collections
        store_customers_cache = await db_analysis["store_customers_cache"].find_one({
            "_id": store_id
        })
        
        product_details_cache = await db_analysis["product_details_cache"].find_one({
            "_id": store_id
        })
        
        store_activity_metrics = await db_analysis["store_activity_metrics"].find_one({
            "_id": store_id
        })
        
        # Get Meta data from meta_chat_context
        meta_context = await db_analysis["meta_chat_context"].find_one({
            "store_id": store_id
        })
        
        if not store_analysis:
            return {
                "performance": "No performance data available.",
                "last_updated": None
            }
        
        # Calculate percentage changes for metrics using activity metrics
        activity_metrics = store_activity_metrics.get('activity_metrics', {}) if store_activity_metrics else {}
        
        # Calculate revenue change percentage
        revenue_30d = activity_metrics.get('revenue_30d', 0)
        revenue_previous_30d = 0
        # Check if we have data for previous period (30-60 days ago)
        revenue_90d = activity_metrics.get('revenue_90d', 0)
        if revenue_90d > revenue_30d:
            revenue_previous_30d = revenue_90d - revenue_30d
        
        revenue_change_pct = 0
        if revenue_previous_30d > 0:
            revenue_change_pct = ((revenue_30d - revenue_previous_30d) / revenue_previous_30d) * 100
        
        # Calculate orders change percentage
        orders_30d = activity_metrics.get('order_count_30d', 0)
        orders_previous_30d = 0
        orders_90d = activity_metrics.get('order_count_90d', 0)
        if orders_90d > orders_30d:
            orders_previous_30d = orders_90d - orders_30d
        
        orders_change_pct = 0
        if orders_previous_30d > 0:
            orders_change_pct = ((orders_30d - orders_previous_30d) / orders_previous_30d) * 100
        
        # Calculate customers change
        # This is more complex as we don't have a direct count of new customers in last 30d
        # Using total customers and approximating
        customers_change_pct = 1  # Default 1% growth if we can't calculate
        
        # Get avg order value change
        # NOTE: avg_order_value now sourced from active_stores_cache via store_analysis, not store_activity_metrics
        # avg_order_value = activity_metrics.get('avg_order_value', 0)
        avg_order_value_change_pct = 2  # Default 2% growth if we can't calculate precisely
        
        # Construct enhanced performance data
        return {
            "sales": {
                "total": store_analysis.get('order_statistics', {}).get('total_sales', 0),
                "total_usd": store_analysis.get('order_statistics', {}).get('total_sales_usd', 0),
                "by_status": store_analysis.get('order_statistics', {}).get('status_distribution', {})
            },
            "products": {
                "total": product_details_cache.get('product_count', 0) if product_details_cache else 0,
                "performance": store_analysis.get('product_performance', {}),
                "top_selling": product_details_cache.get('products', [])[:5] if product_details_cache else [],
            },
            "customers": {
                "total": store_customers_cache.get('total_customers', 0) if store_customers_cache else 0,
                "with_orders": store_analysis.get('relationships', {}).get('customers_with_orders', 0),
                "repeat": store_analysis.get('relationships', {}).get('repeat_customers', 0),
                "abandoned_cart_count": store_customers_cache.get('abandoned_cart_count', 0) if store_customers_cache else 0,
                "abandoned_cart_value": store_customers_cache.get('abandoned_cart_total_value', 0) if store_customers_cache else 0,
            },
            "activity": {
                "visit_count_30d": activity_metrics.get('visit_count_30d', 0),
                "visit_count_90d": activity_metrics.get('visit_count_90d', 0),
                "revenue_30d": activity_metrics.get('revenue_30d', 0),
                "revenue_90d": activity_metrics.get('revenue_90d', 0),
                "order_count_30d": activity_metrics.get('order_count_30d', 0),
                "order_count_90d": activity_metrics.get('order_count_90d', 0),
            },
            "meta": {
                "total_followers": meta_context.get('audience_metrics', {}).get('total_followers', 0) if meta_context else 0,
                "total_engagement": meta_context.get('engagement_metrics', {}).get('total_engagement', 0) if meta_context else 0,
                "pages": meta_context.get('pages', []) if meta_context else [],
            },
            "changes": {
                "revenue_change_pct": round(revenue_change_pct, 2),
                "orders_change_pct": round(orders_change_pct, 2),
                "customers_change_pct": customers_change_pct,
                "avg_order_value_change_pct": avg_order_value_change_pct,
            },
            "payment_methods": store_analysis.get('payment_methods', []),
            "shipping_methods": store_analysis.get('shipping_methods', []),
            "currency": store_analysis.get('currency', {}).get('native', {}),
            "last_updated": store_analysis.get('metadata', {}).get('last_updated'),
            "insights": {
                "performance_metrics_summary": store_analysis.get('analysis', {}).get('performance_metrics_summary', ''),
                "product_insights": store_analysis.get('analysis', {}).get('product_insights', ''),
                "customer_insights": store_analysis.get('analysis', {}).get('customer_insights', ''),
                "social_media_insights": store_analysis.get('analysis', {}).get('social_media_insights', ''),
            }
        }
    except Exception as e:
        logger.error(f"Error getting store performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{store_id}/company-profile")
async def get_company_profile(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} getting company profile for store_id: {store_id}")
        # Get store data from global_analysis
        store_analysis = await db_analysis["global_analysis"].find_one({
            "_id": str(store_id)  # Ensure store_id is a string
        })
        
        if not store_analysis:
            logger.warning(f"Store not found in global_analysis: {store_id}")
            raise HTTPException(status_code=404, detail="Store not found")
        
        # Also get user data from store_users to get alternate_emails and selected_meta_email
        store_user = await db_main["store_users"].find_one({
            "id_store": int(store_id)
        })
        
        store_data = store_analysis.get('store', {})
        logger.info(f"Retrieved store data: {store_data}")
        
        profile_data = {
            "company_name": store_data.get('name', ''),
            "business_type": store_data.get('business_type', ''),
            "email": store_data.get('email', ''),
            "phone": store_data.get('telephone', ''),
            "address": store_data.get('address', ''),
            "contact_name": store_data.get('contact_name', ''),
            "tax_id": store_data.get('tax_id', ''),
            "dni": store_data.get('dni', ''),
            "website": store_data.get('website', '')
        }
        
        # Add alternate_emails and selected_meta_email if they exist in store_user
        if store_user:
            profile_data["alternate_emails"] = store_user.get('alternate_emails', [])
            profile_data["selected_meta_email"] = store_user.get('selected_meta_email', None)
        
        logger.info(f"Returning profile data: {profile_data}")
        return profile_data
        
    except Exception as e:
        logger.error(f"Error getting company profile: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{store_id}/company-profile")
async def update_company_profile(
    store_id: str,
    profile_data: CompanyProfileUpdate,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} updating company profile for store_id: {store_id}")
        logger.info(f"Update data: {profile_data}")
        
        # Create update dictionary for global_analysis
        global_analysis_update = {}
        if profile_data.company_name is not None:
            global_analysis_update["store.name"] = profile_data.company_name
        if profile_data.business_type is not None:
            global_analysis_update["store.business_type"] = profile_data.business_type
        if profile_data.contact_name is not None:
            global_analysis_update["store.contact_name"] = profile_data.contact_name
        if profile_data.tax_id is not None:
            global_analysis_update["store.tax_id"] = profile_data.tax_id
        if profile_data.dni is not None:
            global_analysis_update["store.dni"] = profile_data.dni
        if profile_data.phone is not None:
            global_analysis_update["store.telephone"] = profile_data.phone
        if profile_data.address is not None:
            global_analysis_update["store.address"] = profile_data.address
        if profile_data.website is not None:
            global_analysis_update["store.website"] = profile_data.website

        # Create update dictionary for store_users
        store_users_update = {}
        if profile_data.company_name is not None:
            store_users_update["name"] = profile_data.company_name
        if profile_data.dni is not None:
            store_users_update["dni"] = profile_data.dni
        if profile_data.phone is not None:
            store_users_update["telephone"] = profile_data.phone
        if profile_data.address is not None:
            store_users_update["address"] = profile_data.address
        if profile_data.website is not None:
            store_users_update["website"] = profile_data.website
        
        # Add updated_at timestamp to both updates
        current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
        if global_analysis_update:
            global_analysis_update["store.updated_at"] = current_time
        if store_users_update:
            store_users_update["updated_at"] = current_time
            store_users_update["updated_by"] = current_user.email

        logger.info(f"Global analysis update fields: {global_analysis_update}")
        logger.info(f"Store users update fields: {store_users_update}")
        
        # Perform both updates
        update_results = {
            "global_analysis": False,
            "store_users": False
        }

        # Update global_analysis collection
        if global_analysis_update:
            result = await db_analysis["global_analysis"].update_one(
                {"_id": str(store_id)},
                {"$set": global_analysis_update}
            )
            update_results["global_analysis"] = result.modified_count > 0
            logger.info(f"Global analysis update result: {result.modified_count} document(s) modified")

        # Update store_users collection
        if store_users_update:
            result = await db_main["store_users"].update_one(
                {"id_store": int(store_id)},
                {"$set": store_users_update}
            )
            update_results["store_users"] = result.modified_count > 0
            logger.info(f"Store users update result: {result.modified_count} document(s) modified")

        # Check if either update failed
        if not any(update_results.values()):
            raise HTTPException(
                status_code=404,
                detail="No documents were updated. Store not found or no changes made."
            )

        # Return detailed update status
        return {
            "message": "Company profile updated successfully",
            "updates": update_results
        }
        
    except ValueError as ve:
        logger.error(f"Validation error: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error updating company profile: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users/{id_user}")
async def get_user_by_id(id_user: int):
    try:
        logger.info(f"Searching for user with id_user: {id_user}")
        user = await db_main["store_users"].find_one({"id_user": id_user})
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail=f"User with id {id_user} not found"
            )
        
        # Convert ObjectId to string for JSON serialization
        user["_id"] = str(user["_id"])
        
        return user
        
    except Exception as e:
        logger.error(f"Error getting user by ID: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving user: {str(e)}"
        )

@router.put("/users/{id_user}")
async def update_user(id_user: str, user_data: dict, current_user: User = Depends(verify_user_can_access_store)):
    try:
        # Convert string ID to int if it's numeric
        user_id = int(id_user) if id_user.isdigit() else id_user
        
        # Get the user from the database first
        existing_user = await db_main["store_users"].find_one({"id_user": user_id})
        if not existing_user:
            raise HTTPException(
                status_code=404,
                detail=f"User with id {id_user} not found"
            )
        
        # Verify the user is updating their own profile
        if str(existing_user["id_store"]) != str(current_user.id_store):
            raise HTTPException(
                status_code=403,
                detail="You can only update your own profile"
            )
        
        # Remove fields that shouldn't be updated
        protected_fields = ['id_user', 'id_store', 'password', 'active', 'created_at', 'deleted_at', 'created_by', 'deleted_by', 'cod', 'cod_confirm', 'orderId_domain']
        update_data = {k: v for k, v in user_data.items() if k not in protected_fields}
        
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
        update_data['updated_by'] = current_user.id_store
        
        # Update the user in the database
        result = await db_main["store_users"].update_one(
            {"id_user": user_id},
            {"$set": update_data}
        )
        
        if result.modified_count == 0:
            raise HTTPException(
                status_code=404,
                detail=f"User with id {id_user} not found or no changes made"
            )
        
        # Get and return the updated user data
        updated_user = await db_main["store_users"].find_one({"id_user": user_id})
        if updated_user:
            updated_user["_id"] = str(updated_user["_id"])
            return updated_user
        
        raise HTTPException(
            status_code=404,
            detail=f"User with id {id_user} not found after update"
        )
        
    except ValueError as e:
        logger.error(f"Invalid user ID format: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid user ID format: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error updating user: {str(e)}"
        )

@router.post("/{store_id}/feedback")
async def submit_feedback(
    store_id: str,
    feedback_data: ChatFeedbackInput,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        # Ensure analysis document exists (optional here if service handles upsert well)
        await ensure_store_analysis(store_id, current_user.email) 
        
        # Call the service function to save the feedback, now passing user details
        # Use getattr to safely access meta_user_id, similar to get_feedback endpoint
        raw_meta_user_id = getattr(current_user, 'meta_user_id', None)
        user_meta_id_str = str(raw_meta_user_id) if raw_meta_user_id is not None else None
        
        await save_chat_feedback(
            store_id,
            feedback_data,
            user_email=current_user.email,
            meta_user_id=user_meta_id_str
        )

        return {"message": "Feedback submitted successfully"}

    except Exception as e:
        # Log handled by the service function, route handler just returns error
        logger.error(f"Feedback submission failed for store {store_id}: {str(e)}", exc_info=False) # Keep route log minimal
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit feedback: An internal error occurred."
        )

@router.get("/{store_id}/sales", response_model=Dict[str, Any])
async def get_store_sales(
    store_id: str,
    since: str = Query(None, description="Start date (YYYY-MM-DD)"),
    until: str = Query(None, description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Get store sales data from product_details_cache within a date range.
    """
    logger.info(f"User {current_user.email} requesting sales data for store {store_id}")
    try:
        # Fetch data from product_details_cache
        product_cache_collection = db_analysis["product_details_cache"]
        store_data = await product_cache_collection.find_one({"_id": store_id})

        if not store_data or "sales_by_date" not in store_data:
            logger.warning(f"No sales data found in product_details_cache for store_id: {store_id}")
            # Return empty structure matching frontend expectation
            return {"daily_data": [], "total_sales": 0}

        sales_by_date = store_data.get("sales_by_date", [])
        
        # Filter by date range if provided
        filtered_sales = []
        if since or until:
            for daily_entry in sales_by_date:
                entry_date_str = daily_entry.get("date")
                if not entry_date_str:
                    continue # Skip entries without a date

                include = True
                if since and entry_date_str < since:
                    include = False
                if until and entry_date_str > until:
                    include = False
                
                if include:
                    filtered_sales.append(daily_entry)
        else:
            # If no date range, return all data
            filtered_sales = sales_by_date

        # Map fields and calculate total sales
        daily_data_mapped = []
        total_sales = 0.0
        for entry in filtered_sales:
            mapped_entry = {
                "date": entry.get("date"),
                "sales": entry.get("total_revenue", 0), # Map total_revenue to sales
                "orders": entry.get("order_count", 0) # Map order_count to orders
                # Add other fields if needed by frontend type StoreSalesData
            }
            daily_data_mapped.append(mapped_entry)
            total_sales += mapped_entry["sales"]
            
        logger.info(f"Successfully fetched and processed {len(daily_data_mapped)} daily sales records for store {store_id}. Total Sales: {total_sales}")
        
        return {
            "daily_data": daily_data_mapped,
            "total_sales": total_sales
        }

    except HTTPException as http_exc:
        # Re-raise HTTPException to preserve status code and detail
        raise http_exc
    except Exception as e:
        logger.error(f"Error getting store sales for store_id {store_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve store sales data: {str(e)}"
        )

@router.get("/{store_id}/competitor-analysis")
async def get_store_competitor_analysis(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} requesting competitor analysis for store {store_id}")
        # Get store analysis
        store_analysis = await db_analysis["global_analysis"].find_one({
            "_id": store_id
        })
        
        if not store_analysis:
            return {
                "competitor_analysis": "No competitor analysis available"
            }
        
        # Extract competitor analysis from store_analysis
        competitor_analysis = store_analysis.get('analysis', {}).get('competitor_analysis', 'No competitor analysis available')
        
        return {
            "competitor_analysis": competitor_analysis
        }
    except Exception as e:
        logger.error(f"Error getting store competitor analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Define SelectMetaEmailRequest model
class SelectMetaEmailRequest(BaseModel):
    selected_email: EmailStr

@router.put("/{store_id}/select-meta-email")
async def select_meta_email(
    store_id: str,
    request: SelectMetaEmailRequest,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"User {current_user.email} selecting meta email for store {store_id}")
        # Get user data - CHANGED: Use email instead of id_store for consistency with other endpoints
        user_data = await db_main["store_users"].find_one({"email": current_user.email})
        if not user_data:
            logger.error(f"User not found with email: {current_user.email}")
            raise HTTPException(
                status_code=404,
                detail="Store user not found"
            )
        
        # Verify the selected email is either the primary email or one of the alternate emails
        primary_email = user_data.get("email", "").lower()
        alternate_emails = [email.lower() for email in user_data.get("alternate_emails", [])]
        selected_email_lower = request.selected_email.lower()
        
        if selected_email_lower != primary_email and selected_email_lower not in alternate_emails:
            logger.warning(f"Invalid email selection attempt: {request.selected_email}. Valid options: primary={primary_email}, alternates={alternate_emails}")
            raise HTTPException(
                status_code=400,
                detail="Selected email must be either the primary email or one of the alternate emails"
            )
        
        # Update selected_meta_email - CHANGED: Use email instead of id_store for consistency
        update_result = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {"$set": {
                "selected_meta_email": request.selected_email,
                "updated_at": datetime.now(timezone.utc)
            }}
        )
        
        if update_result.modified_count == 0:
            logger.error(f"Failed to update selected Meta email. MongoDB update result: {update_result.raw_result}")
            raise HTTPException(
                status_code=500,
                detail="Failed to update selected Meta email"
            )
        
        logger.info(f"Successfully updated selected Meta email to: {request.selected_email}")
        return {
            "message": "Meta login email updated successfully",
            "selected_email": request.selected_email
        }
        
    except ValueError as ve:
        logger.error(f"Validation error: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error selecting Meta email: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

class AlternateEmailRequest(BaseModel):
    email: EmailStr

@router.post("/{store_id}/alternate-email")
async def add_alternate_email(
    store_id: str,
    request: AlternateEmailRequest,
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Add an alternate email for the current user.
    This email can be used for Meta authentication.
    """
    try:
        logger.info(f"User {current_user.email} adding alternate email for store {store_id}")
        # Get the user from database
        user_data = await db_main["store_users"].find_one({"email": current_user.email})
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
            
        # Check if email already exists in alternate_emails
        alternate_emails = user_data.get("alternate_emails", [])
        if request.email in alternate_emails:
            return {
                "message": "Email already exists in alternate emails",
                "selected_email": request.email
            }
            
        # Check if email is the primary email
        if request.email == user_data["email"]:
            return {
                "message": "Email is already your primary email",
                "selected_email": request.email
            }
            
        # Add email to alternate_emails
        alternate_emails.append(request.email)
        
        # Update user in database
        result = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {"$set": {
                "alternate_emails": alternate_emails,
                "selected_meta_email": request.email  # Automatically set as Meta email
            }}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update alternate emails")
            
        return {
            "message": "Alternate email added successfully",
            "selected_email": request.email
        }
        
    except Exception as e:
        logger.error(f"Error adding alternate email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add alternate email: {str(e)}")

@router.delete("/{store_id}/alternate-email")
async def remove_alternate_email(
    store_id: str,
    request: AlternateEmailRequest,
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Remove an alternate email from the current user.
    """
    try:
        logger.info(f"User {current_user.email} removing alternate email for store {store_id}")
        # Get the user from database
        user_data = await db_main["store_users"].find_one({"email": current_user.email})
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
            
        # Check if email exists in alternate_emails
        alternate_emails = user_data.get("alternate_emails", [])
        if request.email not in alternate_emails:
            raise HTTPException(status_code=404, detail="Email not found in alternate emails")
            
        # Remove email from alternate_emails
        alternate_emails.remove(request.email)
        
        # Update selected_meta_email if it's being removed
        selected_meta_email = user_data.get("selected_meta_email")
        if selected_meta_email == request.email:
            # Fallback to primary email
            selected_meta_email = user_data["email"]
            
        # Update user in database
        result = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {"$set": {
                "alternate_emails": alternate_emails,
                "selected_meta_email": selected_meta_email
            }}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update alternate emails")
            
        return {
            "message": "Alternate email removed successfully",
            "selected_email": selected_meta_email
        }
        
    except Exception as e:
        logger.error(f"Error removing alternate email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to remove alternate email: {str(e)}")

@router.get(
    "/{store_id}/seo-recommendations",
    response_model=SEOInsightsResponse,
    summary="Get AI-generated SEO recommendations for the store",
    tags=["store", "seo", "ai"]
)
async def get_seo_recommendations_route(
    store_id: str = Path(..., description="The ID of the store"),
    lang: Optional[str] = Query("en", description="Language for the recommendations (e.g., 'en', 'es')"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Retrieves AI-generated SEO recommendations based on the store's context.
    Always returns exactly 3 recommendations with fallback mechanisms.
    """
    logger.info(f"User {current_user.email} requesting SEO recommendations for store_id: {store_id}, lang: {lang}")
    try:
        # Ensure lang is a string, default to "en" if None
        effective_lang = lang if lang is not None else "en"
        recommendations = await generate_seo_recommendations(store_id, lang=effective_lang)
        
        # Validate that we always return exactly 3 recommendations
        if len(recommendations.recommendations) != 3:
            logger.error(f"SEO service returned {len(recommendations.recommendations)} recommendations instead of 3 for store {store_id}")
        else:
            logger.info(f"Successfully returned 3 SEO recommendations for store {store_id} to user {current_user.email}")
            
        return recommendations
    except Exception as e:
        logger.error(f"Failed to get SEO recommendations for store {store_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to generate SEO recommendations."
        )

@router.get("/{store_id}/feedback", response_model=Dict[str, Any], dependencies=[Depends(verify_user_can_access_store)])
async def get_feedback(
    store_id: str,
    current_user: User = Depends(get_current_active_user)  # Verification handled by dependency
):
    """Endpoint to fetch feedback for a given store, filtered by the current user."""
    logger.debug(f"Endpoint hit: GET /api/store/{store_id}/feedback for user {current_user.email}")
    try:
        logger.info(f"Fetching feedback for store_id: {store_id}")
        feedback_document = await db_analysis["global_analysis"].find_one({"_id": store_id})
        
        user_feedback_list = []
        if feedback_document:
            all_feedback = feedback_document.get("feedback", []) # Changed from feedback_history
            for entry in all_feedback:
                # Check if meta_user_id exists in current_user and entry, otherwise fallback to email
                # Assuming current_user might have meta_user_id, if not, it would be None
                user_meta_id = getattr(current_user, 'meta_user_id', None)
                entry_meta_id = entry.get("meta_user_id")
                entry_user_email = entry.get("user_email")

                matches = False
                if user_meta_id and entry_meta_id and str(user_meta_id) == str(entry_meta_id):
                    matches = True
                elif entry_user_email and current_user.email == entry_user_email:
                    matches = True
                
                if matches:
                    # Ensure timestamp is a string if it's a datetime object
                    if isinstance(entry.get("timestamp"), datetime):
                        entry["timestamp"] = entry["timestamp"].isoformat()
                    elif isinstance(entry.get("created_at"), datetime): # Handle alternative timestamp field
                         entry["timestamp"] = entry["created_at"].isoformat()
                         del entry["created_at"] # Avoid duplicate timestamp fields if possible
                    user_feedback_list.append(entry)
        
        logger.info(f"Returning {len(user_feedback_list)} feedback entries for user {current_user.email} from store {store_id}")
        return {"feedback": user_feedback_list}
    except Exception as e:
        logger.error(f"Error getting feedback for store {store_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve feedback")

@router.get("/{store_id}/shipping-analysis")
async def get_store_shipping_analysis(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Returns shipping method distribution and most frequent shipping method for the store.
    """
    doc = await db_analysis["store_customers_cache"].find_one(
        {"_id": store_id},
        {"most_frequent_shipping_method": 1, "shipping_method_distribution": 1}
    )
    if not doc:
        return {"most_frequent_shipping_method": None, "shipping_method_distribution": []}
    # Format distribution
    dist = doc.get("shipping_method_distribution", {})
    distribution = [{"name": k, "count": v} for k, v in dist.items()]
    distribution.sort(key=lambda x: x["count"], reverse=True)
    # Format most frequent
    mf = doc.get("most_frequent_shipping_method")
    if isinstance(mf, dict) and "name" in mf and "count" in mf:
        most_frequent = {"name": mf["name"], "count": mf["count"]}
    else:
        most_frequent = None
    return {"most_frequent_shipping_method": most_frequent, "shipping_method_distribution": distribution}

# === Credits Endpoint START ===
@router.get("/{store_id}/credits")
async def get_store_credits_route(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Return current credits balance for the store."""
    logger.info(f"Credits request: user={current_user.email}, store_id={store_id}, user_store={current_user.id_store}")

    doc = await db_analysis["active_stores_cache"].find_one({"_id": store_id}, {"credits": 1})
    if not doc:
        logger.error(f"Store not found in active_stores_cache: {store_id}")
        raise HTTPException(status_code=404, detail="Store not found")

    credits = int(doc.get("credits", 0))
    logger.info(f"Credits found for store {store_id}: {credits}")
    return {"credits": credits}
# === Credits Endpoint END ===



