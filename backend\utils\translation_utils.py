import asyncio
import logging
from openai import AsyncOpenAI
import openai # Import base library for specific error types
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
# Import specific types for OpenAI messages
from openai.types.chat import ChatCompletionMessageParam, ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam
from config.settings import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define specific retryable OpenAI errors
RETRYABLE_ERRORS = (
    openai.APIConnectionError,
    openai.RateLimitError,
    openai.APITimeoutError,
    openai.InternalServerError,
    # Add other transient errors if needed
)

settings = get_settings()

@retry(
    wait=wait_exponential(multiplier=1, min=4, max=10),
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(RETRYABLE_ERRORS),
    before_sleep=lambda retry_state: logger.warning(
        f"Retrying OpenAI call due to {retry_state.outcome.exception() if retry_state.outcome else 'unknown error'} "
        f"(Attempt {retry_state.attempt_number})"
    )
)
async def translate_text_async(
    text: str,
    target_language: str,
    openai_client: AsyncOpenAI,
    model: str
) -> str | None:
    """
    Translates the given text to the target language using OpenAI's ChatCompletion API asynchronously.

    Args:
        text: The text to translate.
        target_language: The language to translate the text into (e.g., "Spanish").
        openai_client: An initialized instance of AsyncOpenAI.
        model: The OpenAI model to use for translation (e.g., "gpt-4o-mini").

    Returns:
        The translated text as a string, or None if translation fails or input is invalid.
    """
    if not text or not isinstance(text, str):
        logger.warning(f"Invalid input text provided for translation: {text}")
        return None

    try:
        # Cast messages to the required types
        messages: list[ChatCompletionMessageParam] = [
            ChatCompletionSystemMessageParam(role="system", content="You are a helpful assistant that translates text accurately."),
            ChatCompletionUserMessageParam(role="user", content=f"Translate the following English text to {target_language}. Only return the translated text, without any introductory phrases, explanations, or quotation marks around the translation itself:\n\n{text}")
        ]

        logger.debug(f"Requesting translation to {target_language} for text: '{text[:50]}...'")
        response = await openai_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.2, # Lower temperature for more deterministic translation
            max_tokens=int(len(text) * 1.5) + 50 # Estimate max tokens needed
        )

        if response.choices and response.choices[0].message and response.choices[0].message.content:
            translated_text = response.choices[0].message.content.strip()
            # Optional: Add more cleaning if the model sometimes adds quotes
            if translated_text.startswith('"') and translated_text.endswith('"'):
                 translated_text = translated_text[1:-1]
            logger.debug(f"Successfully translated text: '{translated_text[:50]}...'")
            return translated_text
        else:
            logger.error(f"OpenAI response did not contain expected content for text: '{text[:50]}...'")
            return None

    except openai.APIError as e:
        logger.error(f"OpenAI API Error during translation: {e}", exc_info=True)
        # Non-retryable API errors (e.g., invalid request, auth) fall here after tenacity retries fail
        return None
    except Exception as e:
        # Catch any other unexpected errors during the process
        logger.error(f"Unexpected error during translation: {e}", exc_info=True)
        return None

# Example usage (for testing purposes)
async def _test_translation():
    from dotenv import load_dotenv
    import os
    load_dotenv(dotenv_path='../../.env') # Adjust path as needed
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("OPENAI_API_KEY not found in environment variables.")
        return

    client = AsyncOpenAI(api_key=api_key)
    test_text = "Hello, how are you today?"
    translation = await translate_text_async(test_text, "Spanish", client, settings.OPENAI_DEFAULT_MODEL)
    if translation:
        print(f"Original: {test_text}")
        print(f"Translated: {translation}")
    else:
        print("Translation failed.")

if __name__ == '__main__':
    # To test this file directly:
    # 1. Make sure you have a .env file two levels up with your OPENAI_API_KEY
    # 2. Run `python translation_utils.py` from within the `backend/utils` directory
    asyncio.run(_test_translation()) 