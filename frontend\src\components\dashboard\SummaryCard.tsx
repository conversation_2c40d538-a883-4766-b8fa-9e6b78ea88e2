import React from 'react';
import { Card, CardContent, Typography, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useTranslation } from 'react-i18next';
import { getLocalizedDbText } from '../../utils/localizationUtils';
import type { Analysis } from '../../services/storeService';

interface SummaryCardProps {
  title: string;
  analysisData: Analysis | null | undefined;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, analysisData }) => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  const summaryContent = getLocalizedDbText(analysisData, 'summary', currentLang);

  // Define the helper function here
  const processAndStyleLine = (line: string): { text: string; isBold: boolean } => {
    let displayLine = line.trim();
    let isBold = false;

    // Remove list markers
    if (displayLine.startsWith('- ')) {
      displayLine = displayLine.substring(2).trim();
    } else if (displayLine.startsWith('* ')) {
      displayLine = displayLine.substring(2).trim();
    }

    // Basic bolding condition (can be adjusted)
    if (displayLine.endsWith(':')) {
      isBold = true;
    }

    return { text: displayLine, isBold };
  };

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ color: '#42a5f5' }}>
          {title}
        </Typography>
        {(() => {
          if (!summaryContent) {
            return (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', mt: 2 }}>
                {t('dashboard.summary.none', 'No summary available')}
              </Typography>
            );
          }

          const lines = summaryContent.split('\\n').filter(line => line.trim() !== '');

          if (lines.length > 1) {
            // Render list for multiple lines
            return (
              <List disablePadding sx={{ p: 0, mt: 2 }}>
                {lines.map((line, index) => {
                  const { text, isBold } = processAndStyleLine(line);
                  return (
                    <ListItem key={index} sx={{ py: 1.5, px: 0, alignItems: 'flex-start' }}>
                      <ListItemIcon sx={{ minWidth: 'auto', mr: 1, mt: '0.4em', color: 'text.secondary' }}>
                        <FiberManualRecordIcon sx={{ fontSize: '0.6em' }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={
                          <Typography 
                            variant="body2" 
                            color="text.primary"
                            sx={{ fontSize: '1rem', fontWeight: isBold ? 'bold' : 'regular' }}
                          >
                            {text}
                          </Typography>
                        } 
                        sx={{ my: 0 }}
                      />
                    </ListItem>
                  );
                })}
              </List>
            );
          } else if (lines.length === 1) {
            // Render single Typography for single line
            const { text, isBold } = processAndStyleLine(lines[0]);
            return (
              <Typography 
                variant="body2" 
                color="text.primary"
                sx={{ fontSize: '1rem', mt: 2, whiteSpace: 'pre-wrap', fontWeight: isBold ? 'bold' : 'regular' }}
              >
                {text}
              </Typography>
            );
          } else {
            // Fallback if content exists but has no valid lines after split/filter
            return (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', mt: 2 }}>
                {t('dashboard.summary.none', 'No summary available')}
              </Typography>
            );
          }
        })()}
      </CardContent>
    </Card>
  );
};

export default SummaryCard; 
