import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Set, Tuple, List, Any
import asyncio

from config.redis_client import get_redis  # NEW: shared Redis client
from config.settings import get_settings  # NEW: access TTL & URL
from utils.security_utils import generate_csrf_token

logger = logging.getLogger(__name__)

class CSRFTokenService:
    """
    Service to manage CSRF token lifecycle with in-memory storage and TTL.
    In production, this should be replaced with Redis for scalability.
    """
    
    def __init__(self, token_ttl_minutes: int | None = None):
        """Initialise token service.

        If ``REDIS_URL`` is configured, the service stores tokens in Redis so
        they are shared across all workers.  Otherwise it falls back to the
        legacy in-process dictionaries (useful for local development).
        """

        settings = get_settings()

        # TTL precedence: explicit arg > ENV/Settings > 30 default
        self.token_ttl_minutes = token_ttl_minutes or settings.CSRF_TOKEN_TTL_MINUTES or 30

        # Attempt Redis connection (may return None)
        self.redis = get_redis()

        # Legacy in-memory stores retained for fallback when Redis absent
        self.tokens: Dict[str, Dict[str, Any]] = {}
        self.user_tokens: Dict[str, Set[str]] = {}

        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
        
        # Token rotation configuration
        self.sensitive_operations = {
            "/api/auth/logout",
            "/api/auth/change-password", 
            "/api/auth/reset-password",
            "/api/user/delete",
            "/api/user/update-profile",
            "/api/store/settings",
            "/api/admin",
            "/api/security"
        }
    
    async def generate_token(self, user_id: str) -> str:
        """Generate a new CSRF token for a user"""
        token = generate_csrf_token()

        # Prefer shared Redis storage when available
        if self.redis is not None:
            ttl_seconds = self.token_ttl_minutes * 60
            try:
                await self.redis.setex(f"csrf:{token}", ttl_seconds, user_id)
                await self.redis.sadd(f"csrf_user:{user_id}", token)
                await self.redis.expire(f"csrf_user:{user_id}", ttl_seconds)
                logger.debug(f"Generated CSRF token (redis) for user {user_id}")
                return token
            except Exception as redis_exc:
                logger.error("Redis unavailable for CSRF token generation – falling back to in-memory: %s", redis_exc)
                # Fall through to in-memory path

        now = datetime.now(timezone.utc)
        expires_at = now + timedelta(minutes=self.token_ttl_minutes)

        # Store token info (in-memory)
        self.tokens[token] = {
            "user_id": user_id,
            "created_at": now,
            "expires_at": expires_at,
            "rotated_from": None,
        }

        # Track user tokens
        self.user_tokens.setdefault(user_id, set()).add(token)

        # Cleanup expired tokens periodically
        await self._cleanup_expired_tokens()

        logger.debug(f"Generated CSRF token (memory) for user {user_id}")
        return token
    
    async def validate_token(self, token: str, user_id: str) -> bool:
        """Validate a CSRF token for a specific user"""
        if not token or not user_id:
            return False

        # First try Redis if configured
        if self.redis is not None:
            try:
                owner = await self.redis.get(f"csrf:{token}")
                if owner is None:
                    logger.debug("CSRF token not found in redis: %s", token[:8])
                    return False
                if owner != user_id:
                    logger.warning("CSRF token user mismatch (redis). Expected %s, got %s", user_id, owner)
                    return False
                return True
            except Exception as redis_exc:
                logger.error("Redis unavailable during CSRF validate – falling back to memory: %s", redis_exc)
                # Fall through to memory validation

        # In-memory validation path
        token_info = self.tokens.get(token)
        if not token_info:
            logger.debug(f"CSRF token not found: {token[:8]}...")
            return False

        if token_info["user_id"] != user_id:
            logger.warning(
                f"CSRF token user mismatch. Expected: {user_id}, Got: {token_info['user_id']}"
            )
            return False

        # Check expiry
        if datetime.now(timezone.utc) > token_info["expires_at"]:
            logger.debug(f"CSRF token expired for user {user_id}")
            await self._remove_token(token)
            return False

        return True
    
    async def invalidate_user_tokens(self, user_id: str):
        """Invalidate all tokens for a specific user (e.g., on logout)"""
        # Redis path first
        if self.redis is not None:
            try:
                key = f"csrf_user:{user_id}"
                tokens_to_remove = await self.redis.smembers(key)
                if tokens_to_remove:
                    pipe = self.redis.pipeline()
                    for t in tokens_to_remove:
                        pipe.delete(f"csrf:{t}")
                    pipe.delete(key)
                    await pipe.execute()
                    logger.debug(
                        "Invalidated %d CSRF tokens for user %s (redis)", len(tokens_to_remove), user_id
                    )
                return
            except Exception as redis_exc:
                logger.error("Redis error during invalidate_user_tokens – falling back: %s", redis_exc)

        # In-memory fallback
        if user_id in self.user_tokens:
            tokens_to_remove = list(self.user_tokens[user_id])
            for token in tokens_to_remove:
                await self._remove_token(token)
            logger.debug(f"Invalidated {len(tokens_to_remove)} CSRF tokens for user {user_id}")
    
    async def rotate_token_on_sensitive_operation(self, user_id: str, old_token: str) -> str:
        """Rotate CSRF token after a sensitive operation"""
        # If Redis is available, attempt rotation there first
        if self.redis is not None:
            ttl_seconds = self.token_ttl_minutes * 60
            try:
                owner = await self.redis.get(f"csrf:{old_token}")
                if owner != user_id:
                    logger.warning("Attempted to rotate token that does not belong to user (redis)")
                    return await self.generate_token(user_id)

                # Use pipeline for atomic delete + set
                new_token = generate_csrf_token()
                pipe = self.redis.pipeline()
                pipe.delete(f"csrf:{old_token}")
                pipe.setex(f"csrf:{new_token}", ttl_seconds, user_id)
                pipe.srem(f"csrf_user:{user_id}", old_token)
                pipe.sadd(f"csrf_user:{user_id}", new_token)
                pipe.expire(f"csrf_user:{user_id}", ttl_seconds)
                await pipe.execute()
                logger.debug("Rotated CSRF token for user %s (redis)", user_id)
                return new_token
            except Exception as redis_exc:
                logger.error("Redis error during token rotation – falling back: %s", redis_exc)

        # ===== In-memory fallback =====
        if not old_token or old_token not in self.tokens:
            logger.warning(f"Attempted to rotate invalid token for user {user_id}")
            # Generate a new token if old one is invalid
            return await self.generate_token(user_id)
        
        # Invalidate the old token
        await self._remove_token(old_token)
        
        # Generate new token
        new_token = generate_csrf_token()
        now = datetime.now(timezone.utc)
        expires_at = now + timedelta(minutes=self.token_ttl_minutes)
        
        # Store new token info with rotation tracking
        self.tokens[new_token] = {
            "user_id": user_id,
            "created_at": now,
            "expires_at": expires_at,
            "rotated_from": old_token[:8] + "..."  # Store partial old token for tracking
        }
        
        # Track user tokens
        if user_id not in self.user_tokens:
            self.user_tokens[user_id] = set()
        self.user_tokens[user_id].add(new_token)
        
        logger.debug(f"Rotated CSRF token for user {user_id} after sensitive operation")
        return new_token

    def is_sensitive_operation(self, endpoint: str, method: str) -> bool:
        """Check if an endpoint is considered sensitive and requires token rotation"""
        # Check exact matches
        if endpoint in self.sensitive_operations:
            return True
        
        # Check prefix matches for admin and security endpoints
        sensitive_prefixes = ["/api/admin", "/api/security"]
        if any(endpoint.startswith(prefix) for prefix in sensitive_prefixes):
            return True
        
        # Consider all DELETE operations as sensitive
        if method.upper() == "DELETE":
            return True
            
        return False

    async def get_token_with_rotation(self, user_id: str, endpoint: str, method: str) -> Tuple[str, bool]:
        """Get a token and determine if rotation is needed for this operation"""
        # If Redis available – use it to fetch/rotate
        if self.redis is not None:
            try:
                key = f"csrf_user:{user_id}"
                existing_tokens = await self.redis.smembers(key)
            except Exception as redis_exc:
                logger.error("Redis error during get_token_with_rotation – falling back: %s", redis_exc)
                existing_tokens = set()
        else:
            # In-memory fallback
            existing_tokens = self.user_tokens.get(user_id, set())
        
        # If no existing token, generate one
        if not existing_tokens:
            token = await self.generate_token(user_id)
            return token, False
        
        # Get the most recent valid token
        valid_token = None
        for token in existing_tokens:
            if await self.validate_token(token, user_id):
                valid_token = token
                break
        
        if not valid_token:
            # No valid tokens, generate new one
            token = await self.generate_token(user_id)
            return token, False
        
        # Check if this operation requires rotation
        needs_rotation = self.is_sensitive_operation(endpoint, method)
        
        return valid_token, needs_rotation

    async def invalidate_token(self, token: str) -> bool:
        """Explicitly invalidate a specific token"""
        # Redis first
        if self.redis is not None:
            try:
                user_id = await self.redis.get(f"csrf:{token}")
                if user_id is not None:
                    pipe = self.redis.pipeline()
                    pipe.delete(f"csrf:{token}")
                    pipe.srem(f"csrf_user:{user_id}", token)
                    await pipe.execute()
                    logger.debug("Manually invalidated CSRF token (redis): %s", token[:8])
                    return True
            except Exception as redis_exc:
                logger.error("Redis error during invalidate_token – falling back: %s", redis_exc)

        # In-memory fallback
        if token in self.tokens:
            await self._remove_token(token)
            logger.debug(f"Manually invalidated CSRF token: {token[:8]}...")
            return True
        return False

    async def generate_cookie_token(self, user_id: str) -> str:
        """Generate a CSRF token specifically for cookie usage"""
        # Use the same generation logic as regular tokens but mark for cookie usage
        token = generate_csrf_token()
        now = datetime.now(timezone.utc)
        expires_at = now + timedelta(minutes=self.token_ttl_minutes)
        
        # Store token info with cookie flag
        self.tokens[token] = {
            "user_id": user_id,
            "created_at": now,
            "expires_at": expires_at,
            "rotated_from": None,
            "cookie_token": True  # Mark as cookie token
        }
        
        # Track user tokens
        if user_id not in self.user_tokens:
            self.user_tokens[user_id] = set()
        self.user_tokens[user_id].add(token)
        
        logger.debug(f"Generated CSRF cookie token for user {user_id}")
        return token

    async def validate_double_submit(self, header_token: str, cookie_token: str, user_id: str) -> bool:
        """Validate both header and cookie tokens for double submit pattern"""
        if not header_token or not cookie_token or not user_id:
            logger.debug("Double submit validation failed: missing tokens or user_id")
            return False
        
        # Both tokens must be valid for the same user
        header_valid = await self.validate_token(header_token, user_id)
        cookie_valid = await self.validate_token(cookie_token, user_id)
        
        if not header_valid or not cookie_valid:
            logger.debug(f"Double submit validation failed: header_valid={header_valid}, cookie_valid={cookie_valid}")
            return False
        
        # For enhanced security, tokens should be different (not required but recommended)
        if header_token == cookie_token:
            logger.warning(f"Double submit using same token for header and cookie (user: {user_id})")
        
        logger.debug(f"Double submit validation successful for user {user_id}")
        return True

    def supports_double_submit(self) -> bool:
        """Check if double submit cookie pattern is enabled"""
        # This can be configured via environment variables or config
        import os
        return os.getenv("CSRF_DOUBLE_SUBMIT_ENABLED", "true").lower() == "true"
    
    async def _remove_token(self, token: str):
        """Remove a specific token from storage"""
        if token in self.tokens:
            token_info = self.tokens[token]
            user_id = token_info["user_id"]
            
            # Remove from tokens dict
            del self.tokens[token]
            
            # Remove from user tokens set
            if user_id in self.user_tokens:
                self.user_tokens[user_id].discard(token)
                if not self.user_tokens[user_id]:
                    del self.user_tokens[user_id]
    
    async def _cleanup_expired_tokens(self):
        """Clean up expired tokens periodically"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        now = datetime.now(timezone.utc)
        expired_tokens = []
        
        for token, token_info in self.tokens.items():
            if now > token_info["expires_at"]:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            await self._remove_token(token)
        
        self._last_cleanup = current_time
        
        if expired_tokens:
            logger.debug(f"Cleaned up {len(expired_tokens)} expired CSRF tokens")
    
    def get_stats(self) -> Dict[str, int]:
        """Get service statistics"""
        return {
            "active_tokens": len(self.tokens),
            "active_users": len(self.user_tokens),
            "last_cleanup": int(self._last_cleanup)
        }

# Global CSRF token service instance
csrf_service = CSRFTokenService() 