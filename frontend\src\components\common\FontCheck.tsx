import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

export const FontCheck: React.FC = () => {
  return (
    <Paper sx={{ 
      p: 4, 
      maxWidth: 800, 
      mx: 'auto', 
      mt: 4,
      borderRadius: 2,
      boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
    }}>
      <Typography variant="h3" sx={{ mb: 2, letterSpacing: '-0.02em' }}>
        Inter Font Test
      </Typography>
      
      <Typography variant="h5" sx={{ mb: 4, letterSpacing: '-0.01em', color: 'rgba(0,0,0,0.8)' }}>
        This component checks if the Inter font is being applied correctly
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
          Heading Test
        </Typography>
        <Typography variant="body1" sx={{ mb: 3 }}>
          This text should be using the Inter font with regular weight (400). 
          The Inter font is characterized by its clean, modern appearance with optimized legibility.
        </Typography>
      </Box>
      
      <Box sx={{ mb: 4 }}>
        <Typography sx={{ fontWeight: 300, mb: 1, fontSize: '1.1rem' }}>
          Light Weight (300)
        </Typography>
        <Typography sx={{ fontWeight: 400, mb: 1, fontSize: '1.1rem' }}>
          Regular Weight (400)
        </Typography>
        <Typography sx={{ fontWeight: 500, mb: 1, fontSize: '1.1rem' }}>
          Medium Weight (500)
        </Typography>
        <Typography sx={{ fontWeight: 600, mb: 1, fontSize: '1.1rem' }}>
          Semibold Weight (600)
        </Typography>
        <Typography sx={{ fontWeight: 700, mb: 1, fontSize: '1.1rem' }}>
          Bold Weight (700)
        </Typography>
      </Box>
      
      <Box>
        <Typography variant="body2" sx={{ color: 'rgba(0,0,0,0.6)', fontSize: '0.85rem', letterSpacing: '-0.01em' }}>
          Inter features a tall x-height to aid in readability of mixed-case and lower-case text.
          Inter also features special attention to spacing and kerning.
        </Typography>
      </Box>
    </Paper>
  );
};

export default FontCheck; 