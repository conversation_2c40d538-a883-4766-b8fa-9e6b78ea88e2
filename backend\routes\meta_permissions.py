import logging
from fastapi import <PERSON>Router, Depends, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional

from config.database import db_analysis
from models.user import User
from services.auth import get_current_active_user, verify_user_can_access_store, verify_user_can_access_store_query
from services.meta_permissions import (
    get_store_permissions, 
    can_access_feature, 
    get_missing_permissions,
    FEATURE_PERMISSION_MAP
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/meta/permissions", tags=["meta_permissions"])

# Define response models
class PermissionsResponse(BaseModel):
    permissions: Dict[str, str]
    lastUpdated: str

class FeatureAccess(BaseModel):
    hasAccess: bool
    missingPermissions: List[str]

class MetaPermission(BaseModel):
    id: str
    name: str
    status: str
    required: bool
    description: str

# Permission metadata for Meta API v23.0 - only includes currently valid permissions
PERMISSION_METADATA = {
    # Core Facebook permissions
    "pages_show_list": {
        "name": "Meta Pages List",
        "required": True,
        "description": "View list of connected Facebook Pages and Instagram accounts"
    },
    "pages_read_engagement": {
        "name": "Page Engagement",
        "required": True,
        "description": "Access engagement metrics for posts and pages"
    },
    "pages_read_user_content": {
        "name": "Page User Content",
        "required": False,
        "description": "Access user comments and interactions on your content"
    },
    "pages_manage_metadata": {
        "name": "Page Metadata",
        "required": False,
        "description": "Manage page metadata and settings"
    },
    "pages_messaging": {
        "name": "Page Messaging",
        "required": False,
        "description": "Send and receive messages on behalf of pages"
    },
    "public_profile": {
        "name": "Public Profile",
        "required": True,
        "description": "Access basic profile information"
    },
    "email": {
        "name": "Email",
        "required": True,
        "description": "Access email address"
    },

    # Instagram permissions
    "instagram_basic": {
        "name": "Instagram Basic",
        "required": True,
        "description": "Access basic Instagram account and post information"
    },
    "instagram_manage_insights": {
        "name": "Instagram Insights",
        "required": False,
        "description": "Access detailed Instagram engagement and audience metrics"
    },
    "instagram_manage_comments": {
        "name": "Instagram Comments",
        "required": False,
        "description": "Manage comments on Instagram posts"
    },
    "instagram_content_publish": {
        "name": "Instagram Publishing",
        "required": False,
        "description": "Publish content to Instagram"
    },
    "instagram_branded_content_brand": {
        "name": "Instagram Branded Content",
        "required": False,
        "description": "Access branded content features"
    },

    # Business and Ads permissions
    "business_management": {
        "name": "Business Management",
        "required": False,
        "description": "Access business account information and settings"
    },
    "ads_read": {
        "name": "Ads Read",
        "required": False,
        "description": "View advertising data and campaign metrics"
    },
    "ads_management": {
        "name": "Ads Management",
        "required": False,
        "description": "Access detailed performance data for advertising campaigns"
    },

    # WhatsApp Business permissions (if applicable)
    "whatsapp_business_messaging": {
        "name": "WhatsApp Business Messaging",
        "required": False,
        "description": "Send and receive WhatsApp Business messages"
    },
    "whatsapp_business_manage_events": {
        "name": "WhatsApp Business Events",
        "required": False,
        "description": "Manage WhatsApp Business events"
    }
}

@router.get("/{store_id}", response_model=PermissionsResponse)
async def get_permissions(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get current Meta permissions for a store"""
    try:
        # Get permissions from database
        permissions = await get_store_permissions(store_id)
        
        # Get last updated timestamp
        permissions_record = await db_analysis["meta_permissions"].find_one({"store_id": store_id})
        last_updated = "Unknown"
        if permissions_record and permissions_record.get("last_updated"):
            last_updated = permissions_record.get("last_updated").isoformat()
        
        return {
            "permissions": permissions,
            "lastUpdated": last_updated
        }
    except Exception as e:
        logger.error(f"Error getting Meta permissions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving permissions: {str(e)}")

@router.post("/{store_id}/refresh", response_model=PermissionsResponse)
async def refresh_permissions(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Refresh Meta permissions from Meta API"""
    try:
        # In a real implementation, this would query the Meta API
        # For now, just return current permissions
        permissions = await get_store_permissions(store_id)
        
        # Get last updated timestamp
        permissions_record = await db_analysis["meta_permissions"].find_one({"store_id": store_id})
        last_updated = "Unknown"
        if permissions_record and permissions_record.get("last_updated"):
            last_updated = permissions_record.get("last_updated").isoformat()
        
        return {
            "permissions": permissions,
            "lastUpdated": last_updated
        }
    except Exception as e:
        logger.error(f"Error refreshing Meta permissions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error refreshing permissions: {str(e)}")

@router.get("/{store_id}/feature/{feature_key}", response_model=FeatureAccess)
async def check_feature_access(
    store_id: str,
    feature_key: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Check if a store has permissions to access a feature"""
    try:
        has_access = await can_access_feature(store_id, feature_key)
        missing_permissions = await get_missing_permissions(store_id, feature_key)
        
        return {
            "hasAccess": has_access,
            "missingPermissions": missing_permissions
        }
    except Exception as e:
        logger.error(f"Error checking feature access: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking feature access: {str(e)}")

@router.get("/{store_id}/details", response_model=List[MetaPermission])
async def get_permission_details(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get detailed information about Meta permissions"""
    try:
        # Get store permissions
        permissions = await get_store_permissions(store_id)
        
        # Build detailed permission list
        detailed_permissions = []
        for perm_id, metadata in PERMISSION_METADATA.items():
            status = permissions.get(perm_id, "not_requested")
            
            detailed_permissions.append({
                "id": perm_id,
                "name": metadata["name"],
                "status": status,
                "required": metadata["required"],
                "description": metadata["description"]
            })
        
        return detailed_permissions
    except Exception as e:
        logger.error(f"Error getting permission details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving permission details: {str(e)}")

@router.get("/feature-map", response_model=Dict[str, List[str]])
async def get_feature_permission_map(
    user: User = Depends(get_current_active_user)
):
    """Get mapping of features to required permissions"""
    try:
        return FEATURE_PERMISSION_MAP
    except Exception as e:
        logger.error(f"Error getting feature permission map: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving feature permission map: {str(e)}") 