import logging
import re # Import re for regex
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from datetime import datetime, timedelta
from bson import ObjectId

from config.database import db_analysis # Asynchronous client for Analysis DB
from models.user import User
from services.auth import get_current_user # Reuse the base user getter
from config.database import db_main
from pydantic import EmailStr
from pydantic import BaseModel, Field
from pymongo import ReturnDocument

# Model for manual credits addition
class AddCreditsRequest(BaseModel):
    amount: int = Field(..., gt=0, description="Credits amount to add (>0)")

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/admin",
    tags=["Admin"],
    responses={404: {"description": "Not found"}},
)

# Dependency for verifying admin user
async def get_current_active_admin_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")
    if current_user.role != "admin":
        logger.warning(f"Non-admin user '{current_user.email}' attempted to access admin route.")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Operation not permitted")
    return current_user

# ---------------------------------------------------------------------------
# Manual Credits Addition Endpoint
# ---------------------------------------------------------------------------


@router.post("/store/{store_id}/add-credits", response_model=Dict[str, Any])
async def add_store_credits(
    store_id: str,
    payload: AddCreditsRequest,
    admin_user: User = Depends(get_current_active_admin_user),
):
    """Add credits to a store. Admin-only action.

    Args:
        store_id: ID of the store to credit.
        payload: Body with positive `amount` field.
    Returns: Current credits after increment.
    """
    try:
        coll = db_analysis["active_stores_cache"]
        result_doc = await coll.find_one_and_update(
            {"_id": store_id},
            {"$inc": {"credits": payload.amount}},
            projection={"credits": 1},
            return_document=ReturnDocument.AFTER,
        )

        if not result_doc:
            raise HTTPException(status_code=404, detail="Store not found")

        new_total = int(result_doc.get("credits", 0))
        logger.info(
            "Admin %s added %s credits to store %s (new total: %s)",
            admin_user.email,
            payload.amount,
            store_id,
            new_total,
        )

        return {"credits": new_total}

    except HTTPException:
        raise  # Re-raise explicit HTTP errors
    except Exception as exc:
        logger.error("Error adding credits to store %s: %s", store_id, exc, exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to add credits")

@router.get("/stores", response_model=Dict[str, Any])
async def get_all_stores(
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    search: Optional[str] = Query(None, description="Search term for store ID or name"),
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Retrieves a list of all stores with their IDs and names, with optional search.
    Accessible only by admin users.
    Supports pagination.
    """
    try:
        logger.info(f"Admin user '{admin_user.email}' fetching store list with skip={skip}, limit={limit}, search='{search or ''}'")
        
        # Build filter query
        filter_query = {}
        if search:
            try:
                # Check if the search term consists only of digits
                if search.isdigit(): 
                    # Query using the original string form for _id
                    filter_query = {"_id": search}
                    logger.info(f"Applying search filter by string ID: {filter_query}")
                else:
                    # If it contains non-digits, treat as name search
                    filter_query = {"name": {"$regex": re.escape(search), "$options": "i"}}
                    logger.info(f"Applying search filter by name (non-digit input): {filter_query}")
            except Exception as e:
                # Fallback or log error if needed, but primary logic is above
                logger.error(f"Error processing search term '{search}': {e}")
                filter_query = {"name": {"$regex": re.escape(search), "$options": "i"}}
                logger.info(f"Fallback: Applying search filter by name: {filter_query}")
        else:
             logger.info("No search term provided, fetching all stores.")

        # Get total count based on the filter
        total_count = await db_analysis["active_stores_cache"].count_documents(filter_query)
        logger.info(f"Total matching stores found: {total_count}")

        # Query the active_stores_cache collection with filter and pagination
        cursor = db_analysis["active_stores_cache"].find(
            filter_query,
            # Project only needed fields - USE 'name' FIELD
            {"_id": 1, "name": 1}
        ).skip(skip).limit(limit)

        stores = []
        # Asynchronously iterate over the cursor
        async for store_doc in cursor:
            stores.append({
                "id": str(store_doc.get("_id")), # Ensure ID is string
                "name": store_doc.get("name", "Unnamed Store")
            })

        logger.info(f"Retrieved {len(stores)} store records for admin '{admin_user.email}' for the current page.")
        # Update return structure
        return {"stores": stores, "total_count": total_count}

    except Exception as e:
        logger.error(f"Error fetching store list for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the store list."
        )

@router.get("/security-events", response_model=Dict[str, Any])
async def get_security_events(
    time_range: str = Query("week", description="Time range: day, week, month"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    threat_level: Optional[str] = Query(None, description="Filter by threat level"),
    resolved: Optional[bool] = Query(None, description="Filter by resolution status"),
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Get security events with filtering and pagination
    Admin-only endpoint for security monitoring
    """
    try:
        logger.info(f"Admin '{admin_user.email}' accessing security events with time_range={time_range}")
        
        # Calculate time range
        now = datetime.utcnow()
        time_deltas = {
            "day": timedelta(days=1),
            "week": timedelta(days=7),
            "month": timedelta(days=30)
        }
        
        start_time = now - time_deltas.get(time_range, timedelta(days=7))
        
        # Build filter query
        filter_query: Dict[str, Any] = {
            "timestamp": {"$gte": start_time.isoformat()}
        }
        
        if threat_level:
            filter_query["threat_level"] = threat_level
            
        if resolved is not None:
            filter_query["resolved"] = resolved
        
        # Get security events from MongoDB
        cursor = db_analysis.security_events.find(filter_query).sort("timestamp", -1).skip(skip).limit(limit)
        security_events = await cursor.to_list(None)
        
        # Convert ObjectIds to strings for JSON serialization
        for event in security_events:
            if "_id" in event and isinstance(event["_id"], ObjectId):
                event["_id"] = str(event["_id"])
        
        # Get total count for pagination
        total_count = await db_analysis.security_events.count_documents(filter_query)
        
        logger.info(f"Admin {admin_user.email} accessed security events: {len(security_events)} events")
        
        return {
            "data": security_events,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total_count,
                "has_more": skip + limit < total_count
            },
            "filters": {
                "time_range": time_range,
                "threat_level": threat_level,
                "resolved": resolved
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching security events for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch security events")

@router.get("/cost-tracking", response_model=Dict[str, Any])
async def get_cost_tracking(
    time_range: str = Query("week", description="Time range: day, week, month"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    service: Optional[str] = Query(None, description="Filter by service name"),
    store_id: Optional[str] = Query(None, description="Filter by store ID"),
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Get cost tracking data with filtering and pagination
    Admin-only endpoint for cost monitoring
    """
    try:
        logger.info(f"Admin '{admin_user.email}' accessing cost tracking with time_range={time_range}")
        
        # Calculate time range
        now = datetime.utcnow()
        time_deltas = {
            "day": timedelta(days=1),
            "week": timedelta(days=7),
            "month": timedelta(days=30)
        }
        
        start_time = now - time_deltas.get(time_range, timedelta(days=7))
        
        # Build filter query
        filter_query: Dict[str, Any] = {
            "date": {"$gte": start_time.isoformat()}
        }
        
        if service:
            filter_query["service"] = service
            
        if store_id:
            filter_query["store_id"] = store_id
        
        # Get cost tracking data from MongoDB
        cursor = db_analysis.cost_tracking.find(filter_query).sort("date", -1).skip(skip).limit(limit)
        cost_records = await cursor.to_list(None)
        
        # Convert ObjectIds to strings
        for record in cost_records:
            if "_id" in record and isinstance(record["_id"], ObjectId):
                record["_id"] = str(record["_id"])
        
        # Calculate aggregated metrics
        total_cost = sum(record.get("total_cost", 0) for record in cost_records)
        total_requests = sum(record.get("request_count", 0) for record in cost_records)
        
        # Get service breakdown
        service_breakdown = {}
        for record in cost_records:
            svc = record.get("service", "unknown")
            if svc not in service_breakdown:
                service_breakdown[svc] = {"cost": 0, "requests": 0}
            service_breakdown[svc]["cost"] += record.get("total_cost", 0)
            service_breakdown[svc]["requests"] += record.get("request_count", 0)
        
        # Get total count for pagination
        total_count = await db_analysis.cost_tracking.count_documents(filter_query)
        
        logger.info(f"Admin {admin_user.email} accessed cost tracking: {len(cost_records)} records")
        
        return {
            "data": cost_records,
            "summary": {
                "total_cost": total_cost,
                "total_requests": total_requests,
                "avg_cost_per_request": total_cost / total_requests if total_requests > 0 else 0,
                "service_breakdown": service_breakdown
            },
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total_count,
                "has_more": skip + limit < total_count
            },
            "filters": {
                "time_range": time_range,
                "service": service,
                "store_id": store_id
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching cost tracking for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch cost tracking data")

@router.get("/budget-alerts", response_model=Dict[str, Any])
async def get_budget_alerts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    alert_type: Optional[str] = Query(None, description="Filter by alert type"),
    escalated: Optional[bool] = Query(None, description="Filter by escalation status"),
    store_id: Optional[str] = Query(None, description="Filter by store ID"),
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Get budget alerts with filtering and pagination
    Admin-only endpoint for budget monitoring
    """
    try:
        logger.info(f"Admin '{admin_user.email}' accessing budget alerts")
        
        # Build filter query
        filter_query: Dict[str, Any] = {}
        
        if alert_type:
            filter_query["alert_type"] = alert_type
            
        if escalated is not None:
            filter_query["escalated"] = escalated
            
        if store_id:
            filter_query["store_id"] = store_id
        
        # Get budget alerts from MongoDB
        cursor = db_analysis.budget_alerts.find(filter_query).sort("timestamp", -1).skip(skip).limit(limit)
        budget_alerts = await cursor.to_list(None)
        
        # Convert ObjectIds to strings
        for alert in budget_alerts:
            if "_id" in alert and isinstance(alert["_id"], ObjectId):
                alert["_id"] = str(alert["_id"])
        
        # Calculate summary metrics
        total_alerts = len(budget_alerts)
        active_alerts = len([a for a in budget_alerts if not a.get("escalated", False)])
        escalated_alerts = len([a for a in budget_alerts if a.get("escalated", False)])
        total_budget = sum(alert.get("threshold", 0) for alert in budget_alerts)
        total_spent = sum(alert.get("current_amount", 0) for alert in budget_alerts)
        
        # Get total count for pagination
        total_count = await db_analysis.budget_alerts.count_documents(filter_query)
        
        logger.info(f"Admin {admin_user.email} accessed budget alerts: {len(budget_alerts)} alerts")
        
        return {
            "data": budget_alerts,
            "summary": {
                "total_alerts": total_alerts,
                "active_alerts": active_alerts,
                "escalated_alerts": escalated_alerts,
                "total_budget": total_budget,
                "total_spent": total_spent,
                "budget_utilization": (total_spent / total_budget * 100) if total_budget > 0 else 0
            },
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total_count,
                "has_more": skip + limit < total_count
            },
            "filters": {
                "alert_type": alert_type,
                "escalated": escalated,
                "store_id": store_id
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching budget alerts for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch budget alerts")

@router.post("/security-events/{event_id}/resolve", response_model=Dict[str, Any])
async def resolve_security_event(
    event_id: str,
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Resolve a security event
    Admin-only action to mark security events as resolved
    """
    try:
        logger.info(f"Admin '{admin_user.email}' resolving security event {event_id}")
        
        # Update the security event
        result = await db_analysis.security_events.update_one(
            {"_id": ObjectId(event_id)},
            {
                "$set": {
                    "resolved": True,
                    "resolved_by": admin_user.email,
                    "resolved_at": datetime.utcnow().isoformat()
                }
            }
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Security event not found")
        
        logger.info(f"Admin {admin_user.email} resolved security event {event_id}")
        
        return {"message": "Security event resolved successfully", "event_id": event_id}
        
    except Exception as e:
        logger.error(f"Error resolving security event {event_id} by admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to resolve security event")

@router.post("/budget-alerts/{alert_id}/escalate", response_model=Dict[str, Any])
async def escalate_budget_alert(
    alert_id: str,
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Escalate a budget alert
    Admin-only action to escalate budget alerts
    """
    try:
        logger.info(f"Admin '{admin_user.email}' escalating budget alert {alert_id}")
        
        # Update the budget alert
        result = await db_analysis.budget_alerts.update_one(
            {"_id": ObjectId(alert_id)},
            {
                "$set": {
                    "escalated": True,
                    "escalated_by": admin_user.email,
                    "escalated_at": datetime.utcnow().isoformat()
                }
            }
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Budget alert not found")
        
        logger.info(f"Admin {admin_user.email} escalated budget alert {alert_id}")
        
        return {"message": "Budget alert escalated successfully", "alert_id": alert_id}
        
    except Exception as e:
        logger.error(f"Error escalating budget alert {alert_id} by admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to escalate budget alert")

@router.get("/export", response_model=Dict[str, Any])
async def export_admin_report(
    time_range: str = Query("week", description="Time range for export"),
    format: str = Query("json", description="Export format: json, csv"),
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Export admin dashboard data
    Admin-only endpoint for generating reports
    """
    try:
        logger.info(f"Admin '{admin_user.email}' exporting dashboard report for {time_range}")
        
        # Get all data for the time range
        security_events_response = await get_security_events(time_range=time_range, admin_user=admin_user)
        cost_tracking_response = await get_cost_tracking(time_range=time_range, admin_user=admin_user)
        budget_alerts_response = await get_budget_alerts(admin_user=admin_user)
        stores_response = await get_all_stores(admin_user=admin_user)
        
        export_data = {
            "generated_at": datetime.utcnow().isoformat(),
            "generated_by": admin_user.email,
            "time_range": time_range,
            "security_events": {
                "count": len(security_events_response["data"]),
                "data": security_events_response["data"]
            },
            "cost_tracking": {
                "count": len(cost_tracking_response["data"]),
                "summary": cost_tracking_response["summary"],
                "data": cost_tracking_response["data"]
            },
            "budget_alerts": {
                "count": len(budget_alerts_response["data"]),
                "summary": budget_alerts_response["summary"],
                "data": budget_alerts_response["data"]
            },
            "stores": {
                "count": len(stores_response["stores"]),
                "data": stores_response["stores"]
            }
        }
        
        logger.info(f"Admin {admin_user.email} exported dashboard report for {time_range}")
        
        return {"data": export_data, "format": format}
        
    except Exception as e:
        logger.error(f"Error exporting admin report for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to export admin report")

@router.get("/dashboard-summary", response_model=Dict[str, Any])
async def get_admin_dashboard_summary(
    admin_user: User = Depends(get_current_active_admin_user)
):
    """
    Get summary statistics for admin dashboard
    Admin-only endpoint for overview metrics
    """
    try:
        logger.info(f"Admin '{admin_user.email}' accessing dashboard summary")
        
        # Get counts from last 24 hours
        yesterday = datetime.utcnow() - timedelta(days=1)
        
        # Security events summary
        total_security_events = await db_analysis.security_events.count_documents({})
        recent_security_events = await db_analysis.security_events.count_documents({
            "timestamp": {"$gte": yesterday.isoformat()}
        })
        high_threat_events = await db_analysis.security_events.count_documents({
            "threat_level": "high",
            "resolved": False
        })
        
        # Cost tracking summary
        total_cost_records = await db_analysis.cost_tracking.count_documents({})
        recent_costs_cursor = db_analysis.cost_tracking.aggregate([
            {"$match": {"date": {"$gte": yesterday.isoformat()}}},
            {"$group": {"_id": None, "total_cost": {"$sum": "$total_cost"}}}
        ])
        recent_costs = await recent_costs_cursor.to_list(None)
        daily_cost = recent_costs[0]["total_cost"] if recent_costs else 0
        
        # Budget alerts summary
        total_budget_alerts = await db_analysis.budget_alerts.count_documents({})
        active_alerts = await db_analysis.budget_alerts.count_documents({"escalated": False})
        escalated_alerts = await db_analysis.budget_alerts.count_documents({"escalated": True})
        
        # Store summary using existing active_stores_cache
        total_stores = await db_analysis.active_stores_cache.count_documents({})
        
        summary = {
            "security": {
                "total_events": total_security_events,
                "recent_events": recent_security_events,
                "high_threat_events": high_threat_events
            },
            "costs": {
                "total_records": total_cost_records,
                "daily_cost": daily_cost
            },
            "budgets": {
                "total_alerts": total_budget_alerts,
                "active_alerts": active_alerts,
                "escalated_alerts": escalated_alerts
            },
            "stores": {
                "total_stores": total_stores
            },
            "last_updated": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Admin {admin_user.email} accessed dashboard summary")
        
        return summary
        
    except Exception as e:
        logger.error(f"Error fetching admin dashboard summary for admin '{admin_user.email}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch dashboard summary") 

@router.post("/security/unlock-user")
async def unlock_user(
    email: EmailStr,
    admin_user: User = Depends(get_current_active_admin_user)
):
    """Reset failed login counters and lockout for a user (admin-only)."""
    result = await db_main["store_users"].update_one(  # type: ignore[arg-type]
        {"email": email},
        {"$set": {"failed_login_count": 0, "account_locked_until": None}}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    logger.info(f"Admin {admin_user.email} unlocked user {email}")
    return {"status": "unlocked", "email": email}


@router.post("/security/2fa-toggle")
async def toggle_2fa(
    email: EmailStr,
    enable: bool,
    admin_user: User = Depends(get_current_active_admin_user)
):
    """Enable or disable 2FA for a user (admin-only)."""
    result = await db_main["store_users"].update_one(  # type: ignore[arg-type]
        {"email": email},
        {"$set": {"two_factor_enabled": enable}}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    logger.info(f"Admin {admin_user.email} set 2FA={enable} for {email}")
    return {"email": email, "two_factor_enabled": enable} 