import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
// import InventoryIcon from '@mui/icons-material/Inventory'; // Removed unused
// import InfoIcon from '@mui/icons-material/Info'; // Removed unused
import Rating from '@mui/material/Rating';
import { useTranslation } from 'react-i18next';
import { getLocalizedDbText } from '../../utils/localizationUtils'; // Import helper
// import TrendingUpIcon from '@mui/icons-material/TrendingUp'; // Removed unused
// import TrendingDownIcon from '@mui/icons-material/TrendingDown'; // Removed unused
// import TrendingFlatIcon from '@mui/icons-material/TrendingFlat'; // Removed unused
import { ExtendedAnalysis } from '../Dashboard';

interface ProductAnalysisTextProps {
  totalOnlineProducts: number;
  // analysisText: string; // Old prop
  analysisData: ExtendedAnalysis | null | undefined; // New prop with strict type
  storeRatingsCount?: number | null;
  storeAverageRating?: number | null;
}

const ProductAnalysisText: React.FC<ProductAnalysisTextProps> = ({
  totalOnlineProducts,
  // analysisText, // Old prop
  analysisData, // New prop
  storeRatingsCount,
  storeAverageRating
}) => {
  const { t, i18n } = useTranslation(); // Get i18n
  const currentLang = i18n.language; // Get current language

  // Get localized text
  const localizedAnalysisText = getLocalizedDbText(analysisData, 'product_performance', currentLang);

  return (
    <Paper elevation={0} variant="outlined" sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
        <Box>
          <Typography variant="h6" gutterBottom>
            {t('productAnalysis.title', 'Product Overview')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('productAnalysis.totalOnlineProducts', 'Total Online Products:')} {totalOnlineProducts ?? 'N/A'}
          </Typography>
        </Box>
        {storeAverageRating !== null && storeAverageRating !== undefined && (
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="body2" color="text.secondary">
              {t('productAnalysis.storeRating', 'Store Rating')}
            </Typography>
            <Rating value={storeAverageRating} precision={0.1} readOnly size="small" />
            <Typography variant="caption" display="block" color="text.secondary">
              ({storeRatingsCount ?? 0} {t('productAnalysis.ratings', 'ratings')})
            </Typography>
          </Box>
        )}
      </Box>
      {localizedAnalysisText ? (
        localizedAnalysisText.split('- ').filter(segment => segment.trim() !== '').map((segment, index) => (
          <Box
            key={index}
            sx={theme => theme.palette.mode === 'dark' ? {
              py: 0.5,
              px: 1.5,
              mb: 1,
              backgroundColor: theme.palette.background.paper,
              color: '#fff',
              borderRadius: 2,
              transition: 'background 0.2s',
              '&:hover': {
                backgroundColor: '#222',
                color: '#fff',
              },
            } : {
              py: 0.5,
              px: 1.5,
              mb: 1
            }}
          >
            <Typography variant="body1" paragraph sx={theme => theme.palette.mode === 'dark' ? { color: '#fff', mb: 0 } : { mb: 0 }}>
              {`- ${segment.trim()}`}
            </Typography>
          </Box>
        ))
      ) : (
        <Typography variant="body1">
          {t('productAnalysis.noAnalysis', 'No analysis text available.')}
        </Typography>
      )}
    </Paper>
  );
};

export default ProductAnalysisText; 