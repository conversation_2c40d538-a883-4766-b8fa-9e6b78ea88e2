# Cost Control Configuration for D-Unit API
# This file defines cost limits and optimization settings

global_cost_limits:
  # System-wide daily limits (USD)
  system_daily_limit: 200.0
  system_monthly_limit: 5000.0
  
  # Emergency stop thresholds
  emergency_stop_daily: 0.95    # Stop at 95% of daily limit
  emergency_stop_monthly: 0.95  # Stop at 95% of monthly limit
  
  # Alert thresholds
  warning_threshold: 0.70       # Warning at 70%
  critical_threshold: 0.85      # Critical alert at 85%
  
  # Cost calculation currency
  currency: "USD"
  
  # Cost tracking precision
  precision_decimal_places: 4

# Service-specific cost limits
service_limits:
  # OpenAI API costs
  openai:
    daily_limit: 100.0
    monthly_limit: 2500.0
    models:
      "gpt-4":
        cost_per_1k_tokens_input: 0.03
        cost_per_1k_tokens_output: 0.06
        max_tokens_per_request: 4096
        daily_token_limit: 1000000
      "gpt-3.5-turbo":
        cost_per_1k_tokens_input: 0.0015
        cost_per_1k_tokens_output: 0.002
        max_tokens_per_request: 4096
        daily_token_limit: 5000000
      "text-embedding-ada-002":
        cost_per_1k_tokens: 0.0001
        max_tokens_per_request: 8191
        daily_token_limit: 10000000
    rate_limits:
      requests_per_minute: 100
      tokens_per_minute: 100000

  # Meta Graph API costs
  meta_api:
    daily_limit: 50.0
    monthly_limit: 1000.0
    cost_per_request: 0.001
    cost_per_1k_data_points: 0.01
    max_requests_per_hour: 200
    max_data_points_per_day: 1000000

  # AWS services costs
  aws_services:
    daily_limit: 30.0
    monthly_limit: 500.0
    services:
      s3:
        cost_per_gb_storage: 0.023
        cost_per_1k_requests: 0.0004
      lambda:
        cost_per_1m_requests: 0.20
        cost_per_gb_second: 0.0000166667
      cloudwatch:
        cost_per_1k_custom_metrics: 0.30
        cost_per_gb_logs: 0.50

# Store-tier based cost limits
store_tier_limits:
  # Free tier
  free:
    daily_limit: 5.0
    monthly_limit: 50.0
    openai_daily_limit: 2.0
    meta_api_daily_limit: 1.0
    max_chat_requests: 50
    max_analytics_requests: 100
    
  # Basic tier ($29/month)
  basic:
    daily_limit: 15.0
    monthly_limit: 200.0
    openai_daily_limit: 8.0
    meta_api_daily_limit: 3.0
    max_chat_requests: 200
    max_analytics_requests: 500
    
  # Premium tier ($99/month)
  premium:
    daily_limit: 30.0
    monthly_limit: 500.0
    openai_daily_limit: 20.0
    meta_api_daily_limit: 5.0
    max_chat_requests: 500
    max_analytics_requests: 1000
    
  # Enterprise tier (custom pricing)
  enterprise:
    daily_limit: 100.0
    monthly_limit: 2000.0
    openai_daily_limit: 60.0
    meta_api_daily_limit: 20.0
    max_chat_requests: 2000
    max_analytics_requests: 5000

# Endpoint-specific cost estimates
endpoint_costs:
  # Chat endpoints (OpenAI costs)
  "/api/chat/**":
    base_cost: 0.001
    variable_cost_per_token: 0.00003
    average_tokens_per_request: 500
    estimated_cost_per_request: 0.015
    cost_multiplier: 1.0
    
  # Meta analytics endpoints
  "/api/meta/**":
    base_cost: 0.0005
    cost_per_data_point: 0.00001
    average_data_points: 100
    estimated_cost_per_request: 0.0015
    cost_multiplier: 1.0
    
  # Insights endpoints (complex queries)
  "/api/insights/**":
    base_cost: 0.002
    database_query_cost: 0.001
    ai_processing_cost: 0.005
    estimated_cost_per_request: 0.008
    cost_multiplier: 1.5
    
  # Store operations (minimal cost)
  "/api/store/**":
    base_cost: 0.0001
    database_operation_cost: 0.0002
    estimated_cost_per_request: 0.0003
    cost_multiplier: 1.0

# Real-time cost tracking
cost_tracking:
  # Enable real-time cost calculation
  enable_realtime: true
  
  # Cost calculation intervals
  calculation_interval_seconds: 60
  aggregation_interval_minutes: 5
  
  # Cost estimation accuracy
  estimation_buffer_percent: 20  # Add 20% buffer to estimates
  
  # Historical cost data retention
  retain_hourly_data_days: 30
  retain_daily_data_days: 365
  retain_monthly_data_years: 5

# Cost optimization settings
optimization:
  # Caching to reduce API calls
  enable_intelligent_caching: true
  cache_duration_minutes: 15
  cache_hit_cost_reduction: 0.95  # 95% cost reduction for cache hits
  
  # Request batching
  enable_request_batching: true
  batch_size_optimal: 10
  batch_timeout_seconds: 2
  
  # Model selection optimization
  auto_model_selection: true
  models_by_complexity:
    low: "gpt-3.5-turbo"
    medium: "gpt-3.5-turbo"
    high: "gpt-4"
  
  # Token optimization
  enable_token_optimization: true
  max_context_tokens: 3000
  response_token_limit: 1000
  
  # Geographic cost optimization
  prefer_cached_regional_data: true
  regional_cost_multipliers:
    us_east: 1.0
    us_west: 1.1
    eu_west: 1.2
    asia_pacific: 1.3

# Budget alerts and notifications
alerts:
  # Alert channels
  channels:
    email: true
    webhook: true
    dashboard: true
    
  # Alert frequencies
  warning_alert_frequency: "daily"
  critical_alert_frequency: "immediate"
  
  # Alert recipients by store tier
  recipients:
    free:
      - "user_email"
    basic:
      - "user_email"
      - "<EMAIL>"
    premium:
      - "user_email"
      - "<EMAIL>"
      - "<EMAIL>"
    enterprise:
      - "user_email"
      - "<EMAIL>"
      - "<EMAIL>"

# Cost prediction and forecasting
forecasting:
  # Enable cost prediction
  enable_predictions: true
  
  # Prediction models
  prediction_algorithm: "linear_regression"
  historical_data_points: 30  # Use last 30 days
  
  # Prediction accuracy
  confidence_interval: 0.80
  prediction_buffer_percent: 15
  
  # Forecasting periods
  daily_forecast: true
  weekly_forecast: true
  monthly_forecast: true

# Emergency cost controls
emergency_controls:
  # Automatic service degradation
  enable_auto_degradation: true
  degradation_triggers:
    daily_80_percent: "reduce_ai_model_complexity"
    daily_90_percent: "enable_aggressive_caching"
    daily_95_percent: "block_non_essential_requests"
  
  # Circuit breaker patterns
  circuit_breaker:
    failure_threshold: 5
    timeout_seconds: 60
    recovery_timeout_seconds: 300
  
  # Rate limiting on cost overruns
  cost_based_rate_limiting:
    enable: true
    limit_multiplier_on_overage: 0.5
    
# Cost reporting
reporting:
  # Automated reports
  daily_reports: true
  weekly_reports: true
  monthly_reports: true
  
  # Report formats
  formats:
    - "json"
    - "csv"
    - "pdf"
  
  # Report distribution
  email_reports: true
  api_endpoint_reports: true
  dashboard_reports: true
  
  # Cost breakdown categories
  breakdown_by:
    - "service"
    - "endpoint"
    - "store_id"
    - "user_id"
    - "time_period"
    - "geographic_region"

# Integration settings
integrations:
  # AWS Cost Explorer integration
  aws_cost_explorer:
    enabled: false
    api_region: "us-east-1"
    cost_allocation_tags:
      - "Service"
      - "Environment"
      - "Store"
  
  # Third-party cost monitoring
  external_monitoring:
    enabled: false
    webhook_url: ""
    api_key: "" 