import logging
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any, List, Union

from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import IndexModel

from models.security import (
    SecurityEvent, SecurityReport, BudgetAlert, ThreatAlert, 
    CostSummary, OptimizationSuggestion, SecurityEventType, ThreatLevel
)
from models.csrf_monitoring import (
    CSRFViolationEvent, CSRFAttackPattern, CSRFThreatAssessment,
    ViolationType, SeverityLevel, AttackType, ThreatLevel as CSRFThreatLevel
)
from utils.security_utils import generate_trace_id

logger = logging.getLogger(__name__)

class SecurityService:
    """
    Service for managing security events, analytics, and reporting
    """
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """Initialize SecurityService with database connection"""
        self.database = database
        self.security_events_collection = database.security_events
        self.rate_limit_records_collection = database.rate_limit_records
        self.budget_alerts_collection = database.budget_alerts
        self.cost_tracking_collection = database.cost_tracking
        self.threat_alerts_collection = database.threat_alerts
        
        # CSRF monitoring collections
        self.csrf_violations_collection = database.csrf_violations
        self.csrf_attack_patterns_collection = database.csrf_attack_patterns
        self.csrf_threat_assessments_collection = database.csrf_threat_assessments
        
        # Collections will be initialized explicitly via initialize() method
        # Removed async task creation to fix event loop error
    
    async def initialize(self):
        """Initialize database collections and indexes - call this after event loop is running"""
        try:
            await self._initialize_collections()
            logger.info("SecurityService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize SecurityService: {e}")
            raise
    
    async def _initialize_collections(self):
        """Initialize database collections and indexes"""
        try:
            # Create indexes for security_events collection
            security_event_indexes = [
                IndexModel([("timestamp", -1)]),
                IndexModel([("event_type", 1)]),
                IndexModel([("threat_level", 1)]),
                IndexModel([("ip_address", 1)]),
                IndexModel([("store_id", 1)]),
                IndexModel([("user_id", 1)]),
                IndexModel([("endpoint", 1)]),
                IndexModel([("timestamp", -1), ("threat_level", 1)]),  # Compound index
            ]
            await self.security_events_collection.create_indexes(security_event_indexes)
            
            # Create indexes for cost_tracking collection
            cost_tracking_indexes = [
                IndexModel([("date", -1)]),
                IndexModel([("store_id", 1)]),
                IndexModel([("service", 1)]),
                IndexModel([("store_id", 1), ("date", -1)]),  # Compound index
            ]
            await self.cost_tracking_collection.create_indexes(cost_tracking_indexes)
            
                        # Create indexes for budget_alerts collection            budget_alert_indexes = [                IndexModel([("timestamp", -1)]),                IndexModel([("store_id", 1)]),                IndexModel([("alert_type", 1)]),                IndexModel([("escalated", 1)]),            ]            await self.budget_alerts_collection.create_indexes(budget_alert_indexes)                        # Create indexes for CSRF monitoring collections            csrf_violations_indexes = [                IndexModel([("timestamp", -1)]),                IndexModel([("ip_address", 1)]),                IndexModel([("user_id", 1)]),                IndexModel([("violation_type", 1)]),                IndexModel([("severity", 1)]),                IndexModel([("endpoint", 1)]),                IndexModel([("timestamp", -1), ("ip_address", 1)]),  # Compound index for pattern detection                IndexModel([("timestamp", -1), ("user_id", 1)]),     # Compound index for user tracking            ]            await self.csrf_violations_collection.create_indexes(csrf_violations_indexes)                        csrf_patterns_indexes = [                IndexModel([("start_time", -1)]),                IndexModel([("end_time", -1)]),                IndexModel([("attack_type", 1)]),                IndexModel([("severity", 1)]),                IndexModel([("confidence_score", -1)]),                IndexModel([("primary_ip_addresses", 1)]),            ]            await self.csrf_attack_patterns_collection.create_indexes(csrf_patterns_indexes)                        csrf_assessments_indexes = [                IndexModel([("assessment_time", -1)]),                IndexModel([("threat_level", 1)]),                IndexModel([("ip_address", 1)]),            ]            await self.csrf_threat_assessments_collection.create_indexes(csrf_assessments_indexes)
            
            logger.info("Security service database collections initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize security collections: {e}")
    
    async def log_security_event(self, event: SecurityEvent) -> str:
        """Log a security event to the database"""
        try:
            event_dict = event.model_dump()
            result = await self.security_events_collection.insert_one(event_dict)
            
            # Check if this is a high-priority event that needs immediate attention
            if event.threat_level in ["high", "critical"]:
                await self._handle_high_priority_event(event)
            
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
            raise
    
    async def _handle_high_priority_event(self, event: SecurityEvent):
        """Handle high-priority security events"""
        try:
            # Create threat alert
            threat_alert = ThreatAlert(
                alert_id=generate_trace_id(),
                threat_type=event.event_type,
                severity=event.threat_level,
                source_ip=event.ip_address,
                user_id=event.user_id,
                description=f"High-priority security event: {event.event_type}",
                recommended_action=self._get_recommended_action(event),
                auto_resolved=False
            )
            
            await self.threat_alerts_collection.insert_one(threat_alert.model_dump())
            logger.warning(f"High-priority threat alert created: {threat_alert.alert_id}")
            
        except Exception as e:
            logger.error(f"Failed to handle high-priority event: {e}")
    
    def _get_recommended_action(self, event: SecurityEvent) -> str:
        """Get recommended action for a security event"""
        actions = {
            "rate_limit_violation": "Review rate limits and consider temporary IP blocking",
            "validation_failure": "Investigate request patterns and enhance validation rules",
            "cost_limit_exceeded": "Review usage patterns and consider plan upgrade",
            "suspicious_activity": "Monitor IP address and consider blocking if pattern continues",
            "malicious_request": "Block IP address and review security rules",
            "authentication_failure": "Monitor for brute force attacks and consider account lockout"
        }
        return actions.get(event.event_type, "Monitor and investigate further")
    
    async def get_security_events(
        self,
        limit: int = 100,
        event_type: Optional[str] = None,
        threat_level: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        ip_address: Optional[str] = None,
        store_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get security events with filters"""
        try:
            # Build filter query
            filter_query: Dict[str, Any] = {}
            
            if event_type:
                filter_query["event_type"] = event_type
            if threat_level:
                filter_query["threat_level"] = threat_level
            if ip_address:
                filter_query["ip_address"] = ip_address
            if store_id:
                filter_query["store_id"] = store_id
            
            # Time range filter
            if start_time or end_time:
                time_filter: Dict[str, Any] = {}
                if start_time:
                    time_filter["$gte"] = start_time
                if end_time:
                    time_filter["$lte"] = end_time
                filter_query["timestamp"] = time_filter
            
            # Query database
            cursor = self.security_events_collection.find(filter_query).sort("timestamp", -1).limit(limit)
            events = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string for JSON serialization
            for event in events:
                if "_id" in event:
                    event["_id"] = str(event["_id"])
            
            return events
            
        except Exception as e:
            logger.error(f"Failed to get security events: {e}")
            raise
    
    async def get_security_metrics(
        self,
        hours: int = 24,
        store_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get security metrics for the specified time period"""
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=hours)
            
            # Build base filter
            base_filter: Dict[str, Any] = {"timestamp": {"$gte": start_time, "$lte": end_time}}
            if store_id:
                base_filter["store_id"] = store_id  # type: ignore
            
            # Aggregate metrics
            pipeline = [
                {"$match": base_filter},
                {
                    "$group": {
                        "_id": "$event_type",
                        "count": {"$sum": 1},
                        "threat_levels": {"$push": "$threat_level"}
                    }
                }
            ]
            
            results = await self.security_events_collection.aggregate(pipeline).to_list(None)
            
            # Process results
            metrics = {
                "total_events": 0,
                "events_by_type": {},
                "events_by_threat_level": {"low": 0, "medium": 0, "high": 0, "critical": 0},
                "period_start": start_time,
                "period_end": end_time
            }
            
            for result in results:
                event_type = result["_id"]
                count = result["count"]
                threat_levels = result["threat_levels"]
                
                metrics["total_events"] += count
                metrics["events_by_type"][event_type] = count
                
                # Count threat levels
                for level in threat_levels:
                    if level in metrics["events_by_threat_level"]:
                        metrics["events_by_threat_level"][level] += 1
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get security metrics: {e}")
            raise
    
    async def log_budget_alert(self, alert: BudgetAlert) -> str:
        """Log a budget alert"""
        try:
            alert_dict = alert.model_dump()
            result = await self.budget_alerts_collection.insert_one(alert_dict)
            logger.info(f"Budget alert logged: {alert.alert_type} for store {alert.store_id}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Failed to log budget alert: {e}")
            raise
    
    async def track_cost(
        self,
        store_id: str,
        service: str,
        cost: float,
        request_count: int = 1,
        date: Optional[datetime] = None
    ):
        """Track cost for a store and service"""
        try:
            if date is None:
                date = datetime.now(timezone.utc)
            
            # Use date without time for daily aggregation
            date_key = date.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Update or insert cost tracking record
            filter_query = {
                "store_id": store_id,
                "service": service,
                "date": date_key
            }
            
            update_query = {
                "$inc": {
                    "total_cost": cost,
                    "request_count": request_count
                },
                "$setOnInsert": {
                    "store_id": store_id,
                    "service": service,
                    "date": date_key,
                    "created_at": datetime.now(timezone.utc)
                }
            }
            
            await self.cost_tracking_collection.update_one(
                filter_query,
                update_query,
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Failed to track cost: {e}")
            raise
    
    async def get_cost_summary(
        self,
        store_id: str,
        days: int = 30
    ) -> CostSummary:
        """Get cost summary for a store"""
        try:
            end_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = end_date - timedelta(days=days)
            
            # Aggregate costs
            pipeline = [
                {
                    "$match": {
                        "store_id": store_id,
                        "date": {"$gte": start_date, "$lt": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$service",
                        "total_cost": {"$sum": "$total_cost"},
                        "total_requests": {"$sum": "$request_count"}
                    }
                }
            ]
            
            results = await self.cost_tracking_collection.aggregate(pipeline).to_list(None)
            
            # Process results
            total_cost = 0
            openai_cost = 0
            meta_api_cost = 0
            other_costs = 0
            total_requests = 0
            
            for result in results:
                service = result["_id"]
                cost = result["total_cost"]
                requests = result["total_requests"]
                
                total_cost += cost
                total_requests += requests
                
                if service == "openai":
                    openai_cost = cost
                elif service == "meta_api":
                    meta_api_cost = cost
                else:
                    other_costs += cost
            
            cost_per_request = total_cost / total_requests if total_requests > 0 else 0
            
            return CostSummary(
                store_id=store_id,
                period_start=start_date,
                period_end=end_date,
                total_cost_usd=total_cost,
                openai_cost_usd=openai_cost,
                meta_api_cost_usd=meta_api_cost,
                other_costs_usd=other_costs,
                request_count=total_requests,
                cost_per_request=cost_per_request
            )
            
        except Exception as e:
            logger.error(f"Failed to get cost summary: {e}")
            raise
    
    async def generate_optimization_suggestions(
        self,
        store_id: str
    ) -> List[OptimizationSuggestion]:
        """Generate cost optimization suggestions for a store"""
        try:
            suggestions = []
            
            # Get recent cost data
            cost_summary = await self.get_cost_summary(store_id, days=30)
            
            # Analyze OpenAI costs
            if cost_summary.openai_cost_usd > 100:
                suggestions.append(OptimizationSuggestion(
                    suggestion_type="model_optimization",
                    title="Optimize AI Model Usage",
                    description="Consider using GPT-3.5-turbo for simpler queries to reduce costs",
                    potential_savings_usd=cost_summary.openai_cost_usd * 0.3,
                    implementation_effort="medium",
                    priority="high"
                ))
            
            # Analyze request patterns
            if cost_summary.cost_per_request > 0.01:
                suggestions.append(OptimizationSuggestion(
                    suggestion_type="caching",
                    title="Implement Response Caching",
                    description="Cache frequent responses to reduce API calls",
                    potential_savings_usd=cost_summary.total_cost_usd * 0.2,
                    implementation_effort="low",
                    priority="high"
                ))
            
            # Analyze Meta API costs
            if cost_summary.meta_api_cost_usd > 50:
                suggestions.append(OptimizationSuggestion(
                    suggestion_type="api_optimization",
                    title="Optimize Meta API Calls",
                    description="Batch requests and reduce polling frequency",
                    potential_savings_usd=cost_summary.meta_api_cost_usd * 0.25,
                    implementation_effort="medium",
                    priority="medium"
                ))
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to generate optimization suggestions: {e}")
            return []
    
    async def cleanup_old_events(self, days_to_keep: int = 90):
        """Clean up old security events"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            # Delete old security events
            security_result = await self.security_events_collection.delete_many({
                "timestamp": {"$lt": cutoff_date}
            })
            
            # Delete old cost tracking data (keep longer - 1 year)
            cost_cutoff = datetime.now(timezone.utc) - timedelta(days=365)
            cost_result = await self.cost_tracking_collection.delete_many({
                "date": {"$lt": cost_cutoff}
            })
            
            # Delete old budget alerts
            alert_result = await self.budget_alerts_collection.delete_many({
                "timestamp": {"$lt": cutoff_date}
            })
            
            logger.info(
                f"Cleanup completed: {security_result.deleted_count} security events, "
                f"{cost_result.deleted_count} cost records, "
                f"{alert_result.deleted_count} alerts deleted"
            )
            
            return {
                "security_events_deleted": security_result.deleted_count,
                "cost_records_deleted": cost_result.deleted_count,
                "alerts_deleted": alert_result.deleted_count
            }
            
        except Exception as e:
            logger.error(f"Failed to cleanup old events: {e}")
            raise
    
    async def generate_security_report(
        self,
        days: int = 7,
        store_id: Optional[str] = None
    ) -> SecurityReport:
        """Generate a comprehensive security report"""
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)
            
            # Get security metrics
            metrics = await self.get_security_metrics(hours=days*24, store_id=store_id)
            
            # Get recent events
            events = await self.get_security_events(
                limit=100,
                start_time=start_time,
                end_time=end_time,
                store_id=store_id
            )
            
            # Convert to SecurityEvent objects
            security_events = []
            for event_dict in events[:10]:  # Top 10 events
                try:
                    # Remove MongoDB _id field
                    if "_id" in event_dict:
                        del event_dict["_id"]
                    security_events.append(SecurityEvent(**event_dict))
                except Exception as e:
                    logger.warning(f"Failed to convert event: {e}")
            
            # Generate recommendations
            recommendations = [
                "Regularly review and update rate limiting rules",
                "Monitor for repeated violations from specific IP addresses",
                "Consider implementing additional authentication factors for high-value endpoints"
            ]
            
            # Add specific recommendations based on metrics
            if metrics["events_by_threat_level"]["high"] > 10:
                recommendations.append("High number of high-threat events detected - review security policies")
            
            if "rate_limit_violation" in metrics["events_by_type"] and metrics["events_by_type"]["rate_limit_violation"] > 50:
                recommendations.append("Consider adjusting rate limits or implementing progressive penalties")
            
            # Get cost summary if store_id provided
            cost_summary = None
            if store_id:
                cost_summary = await self.get_cost_summary(store_id, days)
            
            return SecurityReport(
                report_id=generate_trace_id(),
                period_start=start_time,
                period_end=end_time,
                total_requests=metrics["total_events"],
                blocked_requests=metrics["events_by_type"].get("malicious_request", 0),
                security_events=security_events,
                top_threats=[],  # You'd implement this based on events
                cost_summary=cost_summary,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Failed to generate security report: {e}")
            raise
    
    # ==================== CSRF MONITORING METHODS ====================
    
    async def log_csrf_violation(self, event: CSRFViolationEvent) -> str:
        """Log a CSRF violation event to the database"""
        try:
            event_dict = event.model_dump()
            result = await self.csrf_violations_collection.insert_one(event_dict)
            
            # Also log as general security event for comprehensive monitoring
            security_event = SecurityEvent(
                event_type=SecurityEventType.MALICIOUS_REQUEST,
                threat_level=ThreatLevel(event.severity.value),
                ip_address=event.ip_address,
                user_id=event.user_id,
                endpoint=event.endpoint,
                method=event.method,
                user_agent=event.user_agent,
                trace_id=generate_trace_id(),
                details={
                    "violation_type": event.violation_type.value,
                    "referer": event.referer,
                    "csrf_violation": True,
                    "description": f"CSRF violation: {event.violation_type.value}"
                }
            )
            
            await self.log_security_event(security_event)
            
            logger.info(f"CSRF violation logged: {event.violation_type.value} from {event.ip_address}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Failed to log CSRF violation: {e}")
            raise
    
    async def analyze_csrf_patterns(self, hours: int = 24) -> List[CSRFAttackPattern]:
        """Analyze CSRF violations and detect attack patterns"""
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=hours)
            
            # Get recent CSRF violations
            violations_cursor = self.csrf_violations_collection.find({
                "timestamp": {"$gte": start_time, "$lte": end_time}
            })
            violations = await violations_cursor.to_list(length=1000)
            
            if not violations:
                return []
            
            patterns = []
            
            # Detect IP-based patterns (brute force)
            ip_groups = {}
            for violation in violations:
                ip = violation.get("ip_address", "unknown")
                if ip not in ip_groups:
                    ip_groups[ip] = []
                ip_groups[ip].append(violation)
            
            # Analyze IP groups for patterns
            for ip, ip_violations in ip_groups.items():
                if len(ip_violations) >= 5:  # Threshold for pattern detection
                    pattern_id = f"ip_pattern_{ip}_{int(datetime.now().timestamp())}"
                    pattern = CSRFAttackPattern(
                        _id=pattern_id,
                        pattern_id=pattern_id,
                        attack_type=AttackType.BRUTE_FORCE,
                        severity=SeverityLevel.HIGH if len(ip_violations) >= 10 else SeverityLevel.MEDIUM,
                        threat_level=CSRFThreatLevel.HIGH if len(ip_violations) >= 15 else CSRFThreatLevel.MODERATE,
                        start_time=min(v["timestamp"] for v in ip_violations),
                        end_time=max(v["timestamp"] for v in ip_violations),
                        duration_minutes=(max(v["timestamp"] for v in ip_violations) - 
                                        min(v["timestamp"] for v in ip_violations)).total_seconds() / 60,
                        total_violations=len(ip_violations),
                        unique_ips=1,
                        unique_users=len(set(v.get("user_id", "") for v in ip_violations)),
                        affected_endpoints=list(set(v.get("endpoint", "") for v in ip_violations)),
                        confidence_score=min(1.0, len(ip_violations) / 20),
                        primary_ip_addresses=[ip],
                        primary_user_agents=list(set(v.get("user_agent", "") for v in ip_violations))[:5]
                    )
                    patterns.append(pattern)
            
            # Store detected patterns
            if patterns:
                pattern_dicts = [p.model_dump() for p in patterns]
                await self.csrf_attack_patterns_collection.insert_many(pattern_dicts)
            
            logger.info(f"CSRF pattern analysis completed: {len(patterns)} patterns detected")
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to analyze CSRF patterns: {e}")
            raise
    
    async def generate_csrf_threat_alerts(self) -> List[ThreatAlert]:
        """Generate threat alerts based on CSRF monitoring data"""
        try:
            alerts = []
            
            # Analyze recent patterns for threats
            recent_patterns = await self.analyze_csrf_patterns(hours=1)
            
            for pattern in recent_patterns:
                if pattern.severity in [SeverityLevel.HIGH, SeverityLevel.CRITICAL]:
                    # Map CSRF SeverityLevel to security ThreatLevel
                    severity_mapping = {
                        SeverityLevel.LOW: ThreatLevel.LOW,
                        SeverityLevel.MEDIUM: ThreatLevel.MEDIUM,
                        SeverityLevel.HIGH: ThreatLevel.HIGH,
                        SeverityLevel.CRITICAL: ThreatLevel.CRITICAL
                    }
                    
                    alert = ThreatAlert(
                        alert_id=generate_trace_id(),
                        threat_type=f"csrf_{pattern.attack_type.value}",
                        severity=severity_mapping.get(pattern.severity, ThreatLevel.MEDIUM),
                        source_ip=pattern.primary_ip_addresses[0] if pattern.primary_ip_addresses else "multiple",
                        description=f"CSRF threat detected: {pattern.attack_type.value} with {pattern.confidence_score:.1%} confidence",
                        recommended_action=self._get_csrf_recommended_action(pattern),
                        auto_resolved=False
                    )
                    alerts.append(alert)
            
            # Store alerts in database
            if alerts:
                alert_dicts = [alert.model_dump() for alert in alerts]
                await self.threat_alerts_collection.insert_many(alert_dicts)
                logger.info(f"Generated {len(alerts)} CSRF threat alerts")
            
            return alerts
            
        except Exception as e:
            logger.error(f"Failed to generate CSRF threat alerts: {e}")
            raise
    
    def _get_csrf_recommended_action(self, pattern: CSRFAttackPattern) -> str:
        """Get recommended action for a CSRF attack pattern"""
        actions = {
            AttackType.BRUTE_FORCE: "Consider IP blocking and rate limiting for repeated offenders",
            AttackType.DISTRIBUTED: "Implement geographic filtering and enhance monitoring",
            AttackType.SYSTEMATIC: "Review endpoint security and implement additional validation",
            AttackType.REPLAY: "Strengthen token generation and implement stricter validation",
            AttackType.COORDINATED: "Escalate to security team and consider network-level blocking"
        }
        
        base_action = actions.get(pattern.attack_type, "Monitor and investigate further")
        
        if pattern.confidence_score > 0.8:
            base_action += " High confidence - immediate action recommended."
        elif pattern.unique_ips > 10:
            base_action += " Large-scale attack - consider escalation."
        
        return base_action 