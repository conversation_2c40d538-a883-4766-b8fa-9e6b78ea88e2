import logging
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorCollection

from config.database import db_analysis
from models.user import User
from models.meta import AdPerformanceRequest
from services.auth import get_current_active_user, verify_store_access, verify_user_can_access_store_query

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["meta_direct"])

@router.get("/direct-metrics")
async def get_direct_metrics(
    pageId: str,
    storeId: str,
    since: Optional[str] = None,
    until: Optional[str] = None,
    source: Optional[str] = None,
    current_user: User = Depends(verify_user_can_access_store_query)
):
    """
    Get metrics directly from MongoDB for a specific page and store.
    This endpoint is used as a fallback when the regular API authentication fails.
    
    Parameters:
    - pageId: Meta page ID
    - storeId: Store ID
    - since: Optional start date for filtering metrics (YYYY-MM-DD)
    - until: Optional end date for filtering metrics (YYYY-MM-DD)
    - source: Optional parameter to control which collection to query
      - 'campaigns_only': Only query meta_ad_campaigns collection
      - None (default): Try both consolidated metrics and individual campaigns
    """
    try:
        # Authorization is now handled by the dependency
        logger.info(f"User {current_user.email} accessing direct metrics for store {storeId} (role: {current_user.role})")
        
        logger.info(f"Direct metrics request for pageId={pageId}, storeId={storeId}, date range={since} to {until}, source={source}")
        
        # Validate parameters
        if not pageId or not storeId:
            logger.error(f"Missing required parameters: pageId={pageId}, storeId={storeId}")
            return JSONResponse(
                status_code=400, 
                content={"error": "Missing required parameters", "details": f"pageId or storeId is missing or empty"}
            )
        
        # If source is 'campaigns_only', skip consolidated metrics and go straight to campaigns
        if source == "campaigns_only":
            logger.info("Using campaigns_only mode - querying meta_ad_campaigns directly")
            # Nested try...except to handle errors within this specific path
            try:
                campaigns_data = await get_campaigns_data(pageId, storeId, since, until)
                # Check if campaigns_data indicates an error
                if campaigns_data.get("status") == "error":
                    return JSONResponse(
                        status_code=500, 
                        content=campaigns_data
                    )
                return campaigns_data
            except Exception as camp_err:
                logger.exception(f"Error fetching campaigns data (campaigns_only mode): {str(camp_err)}")
                return JSONResponse(
                    status_code=500, 
                    content={"error": "Failed to fetch campaigns data", "details": str(camp_err)}
                )
            
        # First, try to find consolidated metrics document
        try:
            consolidated_doc = await db_analysis["meta_ad_metrics"].find_one({
                "store_id": storeId,
                "page_id": pageId
            })
            
            logger.info(f"Consolidated metrics query result: {consolidated_doc is not None}")
            
            if consolidated_doc:
                logger.info(f"Found consolidated metrics document for store {storeId}")
                if "_id" in consolidated_doc:
                    consolidated_doc["_id"] = str(consolidated_doc["_id"])
                    
                metrics = consolidated_doc.get("metrics", {})
                campaigns = metrics.get("campaigns", [])
                
                if not campaigns:
                    logger.info(f"No campaigns in consolidated document, fetching from meta_ad_campaigns")
                    try:
                        campaigns_data = await get_campaigns_data(pageId, storeId, since, until)
                        # Check if campaigns_data indicates an error
                        if campaigns_data.get("status") == "error":
                            logger.warning(f"Error fetching supplemental campaigns: {campaigns_data.get('error')}")
                            # Continue without campaigns data
                        else:
                            campaigns = campaigns_data.get("campaigns", [])
                            if campaigns:
                                logger.info(f"Found {len(campaigns)} campaigns in meta_ad_campaigns, adding to response")
                                metrics["campaigns"] = campaigns
                    except Exception as camp_err:
                         logger.exception(f"Error fetching supplemental campaigns data: {str(camp_err)}")
                         # Don't fail the whole request, just log the error and return consolidated data without campaigns
                
                return {
                    "status": "success",
                    "source": "mongodb_consolidated",
                    "metrics": metrics,
                    # Ensure campaigns key exists even if fetching failed or was empty
                    "campaigns": metrics.get("campaigns", []) 
                }
        except Exception as cons_err:
            logger.exception(f"Error querying or processing consolidated metrics: {str(cons_err)}")
            # Don't raise here, proceed to campaign fallback
        
        # Fallback: Try to find individual campaign metrics if consolidated failed or didn't exist
        logger.info("Consolidated metrics not found or failed, falling back to individual campaigns.")
        try:
            campaigns_data = await get_campaigns_data(pageId, storeId, since, until)
            # Check if campaigns_data indicates an error
            if campaigns_data.get("status") == "error":
                return JSONResponse(
                    status_code=500, 
                    content=campaigns_data
                )
            return campaigns_data
        except Exception as fallback_camp_err:
            logger.exception(f"Error fetching campaigns data during fallback: {str(fallback_camp_err)}")
            # If fallback also fails, return JSON error response
            return JSONResponse(
                status_code=500, 
                content={"error": "Failed to fetch campaigns data during fallback", "details": str(fallback_camp_err)}
            )
            
    except HTTPException as http_exc:
        # Convert HTTPException to a proper JSON response
        error_detail = http_exc.detail
        # Ensure the detail is JSON-serializable
        if not isinstance(error_detail, dict):
            error_detail = {"error": str(error_detail)}
        # Add additional context
        error_detail["source"] = "meta_direct"
        error_detail["endpoint"] = "get_direct_metrics"
        return JSONResponse(
            status_code=http_exc.status_code,
            content=error_detail
        )
    except Exception as e:
        # Catch any other unexpected errors and return a standard JSON 500 response
        logger.exception(f"Unhandled error in direct metrics endpoint: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={
                "error": "Internal server error processing direct metrics", 
                "details": str(e),
                "source": "meta_direct",
                "endpoint": "get_direct_metrics"
            }
        )

@router.get("/ad-metrics")
async def get_ad_metrics_direct(
    page_id: str = Query(..., description="Meta page ID"),
    store_id: str = Query(..., description="Store ID"),
    since: Optional[str] = Query(None, description="Optional start date (YYYY-MM-DD)"),
    until: Optional[str] = Query(None, description="Optional end date (YYYY-MM-DD)"),
    current_user: User = Depends(verify_user_can_access_store_query)
):
    logger.debug(f"Enter /ad-metrics for page_id={page_id}, store_id={store_id}, since={since}, until={until}")

    default_empty_overview = {
        "total_spend": 0, "total_impressions": 0, "total_reach": 0,
        "total_clicks": 0, "total_conversions": 0, "average_cpc": 0,
        "average_ctr": 0, "average_cost_per_conversion": 0,
        "roas": 0, "account_currency": None
    }
    empty_response_body = {
        "overview": default_empty_overview,
        "daily_metrics": [],
        "campaigns": [],
        "warnings": []
    }

    try:
        logger.info(f"Direct ad metrics request for pageId={page_id}, storeId={store_id}, date range={since} to {until}")
        
        if not page_id or not store_id:
            logger.error(f"Missing required parameters: page_id={page_id}, store_id={store_id}")
            return JSONResponse(
                status_code=400, 
                content={"error": "Missing required parameters", "details": "page_id or store_id is missing or empty"}
            )
        
        query_filter: Dict[str, Any] = {
            "page_id": page_id,
            "store_id": store_id
        }
        logger.debug(f"Initial query_filter: {query_filter}")

        date_filter_conditions = []
        if since:
            try:
                since_date_obj = datetime.strptime(since, "%Y-%m-%d")
                # Create a timezone-aware datetime object for the start of the 'since' day
                since_dt_utc = datetime(since_date_obj.year, since_date_obj.month, since_date_obj.day, 0, 0, 0, tzinfo=timezone.utc)
                date_filter_conditions.append({"date": {"$gte": since_dt_utc}})
            except ValueError:
                logger.error(f"Invalid 'since' date format: {since}. Expected YYYY-MM-DD.")
                return JSONResponse(status_code=400, content={"error": "Invalid 'since' date format. Expected YYYY-MM-DD."})

        if until:
            try:
                until_date_obj = datetime.strptime(until, "%Y-%m-%d")
                # Create a timezone-aware datetime object for the end of the 'until' day
                until_dt_utc = datetime(until_date_obj.year, until_date_obj.month, until_date_obj.day, 23, 59, 59, 999999, tzinfo=timezone.utc)
                date_filter_conditions.append({"date": {"$lte": until_dt_utc}})
            except ValueError:
                logger.error(f"Invalid 'until' date format: {until}. Expected YYYY-MM-DD.")
                return JSONResponse(status_code=400, content={"error": "Invalid 'until' date format. Expected YYYY-MM-DD."})

        if date_filter_conditions:
            query_filter["$and"] = date_filter_conditions
            logger.debug(f"Applied date filter, final query_filter: {query_filter}")

        ad_metrics_cursor = db_analysis["meta_ad_metrics"].find(query_filter)
        all_metrics_docs_from_db = await ad_metrics_cursor.to_list(length=None)
        logger.debug(f"Fetched {len(all_metrics_docs_from_db)} documents from meta_ad_metrics based on query_filter: {query_filter}.")

        if not all_metrics_docs_from_db:
            logger.warning(f"No ad metrics found in MongoDB for query: {query_filter}")
            
            # If no data found with date filtering, try without date filters to see if data exists
            if date_filter_conditions:
                logger.info("No data found with date filters, trying without date constraints...")
                fallback_query = {"page_id": page_id, "store_id": store_id}
                fallback_cursor = db_analysis["meta_ad_metrics"].find(fallback_query)
                fallback_docs = await fallback_cursor.to_list(length=None)
                
                if fallback_docs:
                    logger.info(f"Found {len(fallback_docs)} documents without date filters. Returning available data with warning.")
                    # Use the fallback data but continue with the normal processing
                    all_metrics_docs_from_db = fallback_docs
                    is_using_fallback_data = True
                else:
                    logger.warning("No data found even without date filters")
                    return empty_response_body
            else:
                return empty_response_body

        is_date_range_query = bool(since or until)
        is_using_fallback_data = False
        found_valid_consolidated_doc = None

        if not is_date_range_query:
            potential_consolidated_docs = [doc for doc in all_metrics_docs_from_db if "metrics" in doc]
            if potential_consolidated_docs:
                doc_to_check = potential_consolidated_docs[0]
                metrics_field = doc_to_check.get("metrics")
                if metrics_field and metrics_field.get("ad_metrics"):
                    found_valid_consolidated_doc = doc_to_check
                    logger.debug(f"Valid consolidated doc {found_valid_consolidated_doc.get('_id')} found for lifetime query.")
                else:
                    logger.warning(f"Consolidated doc {doc_to_check.get('_id')} found but 'metrics' or 'metrics.ad_metrics' is empty/problematic. Will aggregate instead.")
        
        if found_valid_consolidated_doc:
            doc_id = found_valid_consolidated_doc.get("_id", "N/A")
            logger.debug(f"Using valid consolidated_doc with _id: {doc_id} for lifetime view.")
            metrics = found_valid_consolidated_doc.get("metrics")
            
            ad_metrics_data = metrics.get("ad_metrics", {})
            campaigns_data = metrics.get("campaigns", [])
            daily_metrics_data = metrics.get("daily_metrics", [])

            response_data = {
                "overview": ad_metrics_data if ad_metrics_data else default_empty_overview,
                "campaigns": campaigns_data if campaigns_data else [],
                "daily_metrics": daily_metrics_data if daily_metrics_data else [],
                "warnings": []
            }
            
            # Add warning if using fallback data
            if is_using_fallback_data:
                response_data["warnings"].append(
                    f"No data found for requested date range ({since} to {until}). Showing all available data instead."
                )
            logger.debug(f"Returning mapped consolidated data for lifetime view: {response_data}")
            return response_data
        else:
            if is_date_range_query:
                logger.debug(f"Date range query (since: {since}, until: {until}). Aggregating from date-filtered documents.")
            else:
                logger.debug("Lifetime query, but no valid consolidated doc found/used. Aggregating from all available flat documents.")

            flat_metrics_docs_for_aggregation = [doc for doc in all_metrics_docs_from_db if "metrics" not in doc]
            logger.debug(f"Aggregating {len(flat_metrics_docs_for_aggregation)} flat documents.")

            if not flat_metrics_docs_for_aggregation:
                logger.warning(f"No flat documents available for aggregation. Query: {query_filter}")
                return empty_response_body

            # Aggregation logic starts here, using 'flat_metrics_docs_for_aggregation'
            total_spend = 0.0
            total_impressions = 0
            total_reach = 0
            total_clicks = 0
            total_conversions = 0
            account_currency = None
            aggregated_daily_metrics = {} 

            for doc in flat_metrics_docs_for_aggregation:
                doc_id_str = str(doc.get("_id", "N/A"))
                logger.debug(f"Aggregating flat doc {doc_id_str}")
                spend = float(doc.get("spend", 0))
                impressions = int(doc.get("impressions", 0))
                reach = int(doc.get("reach", 0))
                clicks = int(doc.get("clicks", 0))
                conversions = int(doc.get("conversions", 0))
                currency = doc.get("account_currency") or doc.get("currency")
                doc_date_str = doc.get("date")
                
                if isinstance(doc_date_str, datetime):
                    doc_date_str = doc_date_str.strftime("%Y-%m-%d")
                elif not isinstance(doc_date_str, str) or not doc_date_str: 
                    logger.warning(f"Document {doc_id_str} has missing or invalid date for daily aggregation. Skipping daily for this doc.")
                    doc_date_str = None 

                total_spend += spend
                total_impressions += impressions
                total_reach += reach
                total_clicks += clicks
                total_conversions += conversions
                if not account_currency and currency:
                    account_currency = currency

                if doc_date_str: 
                    if doc_date_str not in aggregated_daily_metrics:
                        aggregated_daily_metrics[doc_date_str] = {
                            "date": doc_date_str, "spend": 0.0, "impressions": 0,
                            "clicks": 0, "conversions": 0
                        }
                    aggregated_daily_metrics[doc_date_str]["spend"] += spend
                    aggregated_daily_metrics[doc_date_str]["impressions"] += impressions
                    aggregated_daily_metrics[doc_date_str]["clicks"] += clicks
                    aggregated_daily_metrics[doc_date_str]["conversions"] += conversions
            
            logger.debug(f"Aggregation totals: spend={total_spend}, impressions={total_impressions}, clicks={total_clicks}")
            
            average_ctr = (total_clicks / total_impressions) * 100 if total_impressions > 0 else 0.0
            average_cpc = total_spend / total_clicks if total_clicks > 0 else 0.0
            # Calculate ROAS based on actual conversion value if available, otherwise use industry standard
            avg_conversion_value = 25.0  # Conservative industry average for e-commerce
            roas = (float(total_conversions) * avg_conversion_value) / total_spend if total_spend > 0 else 0.0 
            average_cost_per_conversion = total_spend / total_conversions if total_conversions > 0 else 0.0

            final_daily_metrics_list = sorted(list(aggregated_daily_metrics.values()), key=lambda x: x["date"])

            # Get campaigns directly from meta_ad_campaigns collection since they have real data
            try:
                campaigns_cursor = db_analysis["meta_ad_campaigns"].find({
                    "page_id": page_id,
                    "store_id": store_id
                })
                campaigns_list = await campaigns_cursor.to_list(length=None)
                logger.debug(f"Found {len(campaigns_list)} campaigns in meta_ad_campaigns collection")
                
                final_campaigns_list = []
                for camp in campaigns_list:
                    # Convert MongoDB ObjectId to string if present
                    if "_id" in camp:
                        camp["_id"] = str(camp["_id"])
                    
                    # Extract actual campaign data from MongoDB
                    campaign_spend = float(camp.get("spend", 0))
                    campaign_results = int(camp.get("results", 0))
                    campaign_budget = float(camp.get("budget", 0))
                    campaign_cost_per_result = float(camp.get("cost_per_result", 0))
                    campaign_conversion_rate = float(camp.get("conversion_rate", 0))
                    
                    # Calculate derived metrics
                    campaign_ctr = campaign_conversion_rate * 100 if campaign_conversion_rate > 0 else 0.0
                    campaign_roas = 0.0
                    if campaign_spend > 0:
                        campaign_roas = (campaign_results * avg_conversion_value) / campaign_spend
                    
                    final_campaigns_list.append({
                        "id": camp.get("id"),
                        "name": camp.get("name"),
                        "status": camp.get("status"),
                        "objective": camp.get("objective"),
                        "spend": campaign_spend,
                        "impressions": campaign_results * 100,  # Estimate impressions based on results
                        "clicks": campaign_results,  # Use results as proxy for clicks/actions
                        "conversions": campaign_results,
                        "ctr": campaign_ctr,
                        "roas": campaign_roas,
                        "cost_per_result": campaign_cost_per_result,
                        "budget": campaign_budget,
                        "currency": camp.get("currency", "UYU"),
                        "start_time": camp.get("start_time"),
                        "end_time": camp.get("end_time")
                    })
                    
                logger.debug(f"Processed {len(final_campaigns_list)} campaigns with real data from MongoDB")
                
            except Exception as campaign_error:
                logger.warning(f"Error fetching campaigns from meta_ad_campaigns: {campaign_error}")
                final_campaigns_list = []
            
            aggregated_overview_data = {
                "total_spend": total_spend, "total_impressions": total_impressions, "total_reach": total_reach,
                "total_clicks": total_clicks, "total_conversions": total_conversions, "average_cpc": average_cpc,
                "average_ctr": average_ctr, "average_cost_per_conversion": average_cost_per_conversion,
                "roas": roas, "account_currency": account_currency
            }

            aggregated_response = {
                "overview": {**default_empty_overview, **aggregated_overview_data},
                "daily_metrics": final_daily_metrics_list if final_daily_metrics_list else [],
                "campaigns": final_campaigns_list if final_campaigns_list else [],
                "warnings": []
            }
            
            # Add warning if using fallback data
            if is_using_fallback_data:
                aggregated_response["warnings"].append(
                    f"No data found for requested date range ({since} to {until}). Showing all available data instead."
                )
            logger.debug(f"Returning aggregated response: {aggregated_response}")
            return aggregated_response

    except HTTPException as http_exc:
        logger.error(f"HTTPException in /ad-metrics: {http_exc.detail}")
        raise http_exc 
    except Exception as e:
        logger.exception(f"Unhandled error in /ad-metrics for page_id={page_id}, store_id={store_id}: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"error": "Internal server error processing ad metrics", "details": str(e)}
        )

async def get_campaigns_data(pageId: str, storeId: str, since: Optional[str] = None, until: Optional[str] = None):
    logger.info(f"get_campaigns_data called for pageId={pageId}, storeId={storeId}, since={since}, until={until}")
    
    query_filter = {"page_id": pageId, "store_id": storeId}
    # Date filtering for campaigns could be added here if needed, similar to /ad-metrics
    # For now, fetching all campaigns associated with the page/store

    campaigns_cursor = db_analysis["meta_ad_campaigns"].find(query_filter)
    campaigns_list = await campaigns_cursor.to_list(length=None)

    if not campaigns_list:
        logger.warning(f"No campaigns found in meta_ad_campaigns for pageId={pageId}, storeId={storeId}")
        return {
            "status": "success",
            "source": "mongodb_campaigns",
            "campaigns": [],
            "overview": {} 
        }

    enriched_campaigns = []
    total_campaign_spend = 0.0
    total_campaign_results = 0
    total_campaign_budget = 0.0
    
    for camp in campaigns_list:
        # Convert MongoDB ObjectId to string if present
        if "_id" in camp:
            camp["_id"] = str(camp["_id"])
        
        # Extract actual campaign data from MongoDB
        campaign_spend = float(camp.get("spend", 0))
        campaign_results = int(camp.get("results", 0))
        campaign_budget = float(camp.get("budget", 0))
        campaign_cost_per_result = float(camp.get("cost_per_result", 0))
        campaign_conversion_rate = float(camp.get("conversion_rate", 0))
        
        # Calculate derived metrics
        campaign_ctr = campaign_conversion_rate * 100 if campaign_conversion_rate > 0 else 0.0
        campaign_roas = 0.0
        if campaign_spend > 0:
            # Use industry standard average order value for ROAS calculation
            avg_order_value = 25.0
            campaign_roas = (campaign_results * avg_order_value) / campaign_spend
        
        enriched_campaigns.append({
            "id": camp.get("id"),
            "name": camp.get("name"),
            "status": camp.get("status"),
            "objective": camp.get("objective"),
            "start_time": camp.get("start_time"),
            "end_time": camp.get("end_time"),
            "budget": campaign_budget,
            "spend": campaign_spend,
            "impressions": campaign_results * 100,  # Estimate impressions based on results
            "clicks": campaign_results,  # Use results as proxy for clicks/actions
            "conversions": campaign_results,
            "ctr": campaign_ctr,
            "roas": campaign_roas,
            "cost_per_result": campaign_cost_per_result,
            "conversion_rate": campaign_conversion_rate,
            "currency": camp.get("currency", "UYU"),
            "platform": camp.get("platform", "instagram"),
            "correlation_metric": camp.get("correlation_metric", 0),
            "correlation_data": camp.get("correlation_data", [])
        })
        
        # Aggregate totals for overview
        total_campaign_spend += campaign_spend
        total_campaign_results += campaign_results
        total_campaign_budget += campaign_budget

    # Calculate aggregated overview metrics
    total_campaign_ctr = sum(camp["ctr"] for camp in enriched_campaigns) / len(enriched_campaigns) if enriched_campaigns else 0.0
    total_campaign_roas = sum(camp["roas"] for camp in enriched_campaigns) / len(enriched_campaigns) if enriched_campaigns else 0.0
    avg_cost_per_result = total_campaign_spend / total_campaign_results if total_campaign_results > 0 else 0.0
    
    overview_data = {
        "total_campaigns": len(enriched_campaigns),
        "total_spend": total_campaign_spend,
        "total_budget": total_campaign_budget,
        "total_results": total_campaign_results,
        "total_conversions": total_campaign_results,  # Using results as conversions
        "average_ctr": total_campaign_ctr,
        "average_roas": total_campaign_roas,
        "average_cost_per_result": avg_cost_per_result,
        "budget_utilization": (total_campaign_spend / total_campaign_budget * 100) if total_campaign_budget > 0 else 0.0
    }
    
    logger.info(f"Returning {len(enriched_campaigns)} campaigns with overview: {overview_data}")
    
    return {
        "status": "success", 
        "source": "mongodb_campaigns_with_data", 
        "campaigns": enriched_campaigns,
        "overview": overview_data
    } 

@router.post("/campaigns/{campaign_id}/performance")
async def save_campaign_performance(
    campaign_id: str,
    performance_data: AdPerformanceRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Save ad performance metrics for a specific campaign.
    This endpoint is used by the frontend to store performance data in MongoDB.
    
    Parameters:
    - campaign_id: Meta campaign ID
    - performance_data: List of performance metrics
    """
    try:
        logger.info(f"Saving performance data for campaign {campaign_id}")
        
        # Convert performance data to MongoDB documents
        documents = []
        for perf in performance_data.performance:
            doc = {
                "campaign_id": campaign_id,
                "date": perf.date,
                "metrics": {
                    "ad_metrics": {
                        "impressions": perf.impressions,
                        "reach": perf.reach,
                        "clicks": perf.clicks,
                        "ctr": perf.ctr,
                        "cpc": perf.cpc,
                        "spend": perf.spend,
                        "conversions": perf.conversions,
                        "cost_per_conversion": perf.cost_per_conversion,
                        "roi": perf.roi
                    }
                },
                "platform": perf.publisher_platform or "instagram",  # Default to instagram as this is mainly for Instagram
                "last_updated": datetime.now(timezone.utc)
            }
            if perf.views is not None:
                doc["metrics"]["ad_metrics"]["views"] = perf.views
            documents.append(doc)
            
        # Use bulk write to efficiently update/insert documents
        operations = []
        for doc in documents:
            operations.append({
                "update_one": {
                    "filter": {
                        "campaign_id": doc["campaign_id"],
                        "date": doc["date"]
                    },
                    "update": {"$set": doc},
                    "upsert": True
                }
            })
        
        if operations:
            result = await db_analysis["meta_ad_metrics"].bulk_write(operations)
            logger.info(f"Bulk write result: {result.bulk_api_result}")
            
            return JSONResponse(
                status_code=200,
                content={
                "status": "success",
                    "message": f"Successfully saved {len(documents)} performance records",
                    "modified_count": result.modified_count,
                    "upserted_count": result.upserted_count
            }
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "message": "No performance data provided"
        }
            )
            
    except Exception as e:
        logger.exception(f"Error saving campaign performance data: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
            "status": "error",
                "message": "Failed to save performance data",
                "details": str(e)
            }
        ) 