import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import Dashboard from '../components/Dashboard';import { storeService } from '../services/storeService';import { User } from '../contexts/AuthContext';import { logger } from '../utils/logger';

/**
 * StoreView component that renders the Dashboard for a specific store ID from URL params.
 * Used primarily by admin users to view different stores.
 */
const StoreView: React.FC = () => {
  const { t } = useTranslation();
  const { storeId } = useParams<{ storeId: string }>();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [validStore, setValidStore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Verify the store exists and the admin can access it
    const validateStore = async () => {
      if (!storeId || !isAuthenticated) {
        setLoading(false);
        return;
      }

      try {
        // This will throw an error if the store doesn't exist or user can't access it
        await storeService.getStoreAnalysis(storeId);
        setValidStore(true);
      } catch (err) {
        logger.error('Error validating store access:', err);
        setError(t('storeView.errorNoPermission', 'You do not have permission to view this store or it does not exist.'));
      } finally {
        setLoading(false);
      }
    };

    validateStore();
  }, [storeId, isAuthenticated, t]);

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if the user is not an admin
  if (user && user.role !== 'admin') {
    return <Navigate to="/dashboard" replace />;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!validStore) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', p: 3 }}>
        <Alert severity="warning">{t('storeView.invalidStoreId', 'Invalid store ID or insufficient permissions.')}</Alert>
      </Box>
    );
  }

  // Add this check: Ensure user and user.email are defined before proceeding
  if (!user || typeof user.email !== 'string') {
    // This case should ideally not be reached due to earlier checks,
    // but this satisfies TypeScript and handles edge cases.
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', p: 3 }}>
        <Alert severity="error">{t('storeView.incompleteUserData', 'User data is incomplete. Cannot display dashboard.')}</Alert>
      </Box>
    );
  }

  // Override the user's store ID with the one from the URL for dashboard context
  // At this point, user and user.email are guaranteed to be defined
  // Explicitly type storeUser and ensure required fields are present
  const storeUser: User = {
    email: user.email, // Guaranteed non-null by earlier check
    id_store: storeId || '', // Provide default or ensure storeId is defined
    name: user.name || null, // Use user's name, fallback to null
    role: user.role, // Copy optional fields if they exist
    active: user.active,
    created_at: user.created_at,
    updated_at: user.updated_at,
  };

  // Return the Dashboard component with the overridden user context
  return (
    <Box sx={{ position: 'relative' }}>
      <Typography 
        variant="caption" 
        sx={{ 
          position: 'absolute', 
          top: 8, 
          left: 16, 
          zIndex: 1100, 
          backgroundColor: 'primary.light', 
          color: 'primary.contrastText',
          padding: '4px 8px',
          borderRadius: 1
        }}
      >
        {t('storeView.adminViewLabel', 'Admin View: Store #{{storeId}}', { storeId })}
      </Typography>
      {/* We need to pass a custom user prop to Dashboard with the selected store ID */}
      <Dashboard storeUser={storeUser} />
    </Box>
  );
};

export default StoreView; 