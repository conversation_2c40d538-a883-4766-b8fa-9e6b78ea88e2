import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Menu, MenuItem, Tooltip } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';

const LanguageSelector: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    handleClose();
  };

  let currentLanguageName = 'English';
  if (i18n.language) {
    if (i18n.language.startsWith('es')) {
      currentLanguageName = t('spanish') || 'Spanish';
    } else if (i18n.language.startsWith('en')) {
      currentLanguageName = t('english') || 'English';
    }
  }

  return (
    <div>
      <Tooltip title={t('language') || 'Language'}>
        <Button
          id="language-button"
          aria-controls={open ? 'language-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          startIcon={<LanguageIcon sx={{ color: '#FFFFFF' }} />}
          sx={{
            textTransform: 'none',
            color: '#FFFFFF',
            backgroundColor: '#00A3FF',
            '&:hover': {
              backgroundColor: '#0082CC',
              color: '#FFFFFF',
            },
          }}
        >
          {currentLanguageName}
        </Button>
      </Tooltip>
      <Menu
        id="language-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'language-button',
        }}
      >
        <MenuItem
          onClick={() => changeLanguage('en')}
          selected={i18n.language?.startsWith('en')}
        >
          {t('english') || 'English'}
        </MenuItem>
        <MenuItem
          onClick={() => changeLanguage('es')}
          selected={i18n.language?.startsWith('es')}
        >
          {t('spanish') || 'Spanish'}
        </MenuItem>
      </Menu>
    </div>
  );
};

export default LanguageSelector; 
