from datetime import datetime, timezone
from typing import List, Optional
from pydantic import BaseModel

class ChatMessage(BaseModel):
    role: str
    content: str
    created_at: datetime = datetime.now(timezone.utc)

class ChatSession(BaseModel):
    store_id: str
    conversation_id: str
    messages: List[ChatMessage]
    title: str
    user_id: str
    full_title: str
    created_at: datetime = datetime.now(timezone.utc)
    updated_at: datetime = datetime.now(timezone.utc)
  

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    mode: Optional[str] = None  # Can be 'think', 'deepsearch', 'deepersearch', or None (default)
    # Note: image is handled through multipart/form-data and not included in this model

class FeedbackRequest(BaseModel):
    feedback: str

