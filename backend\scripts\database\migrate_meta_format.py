#!/usr/bin/env python3
"""
Migration script to convert old Meta connection format to new format.

This script migrates stores from the old format:
- meta_access_token (string)
- meta_user_id (string)

To the new format:
- meta_integration.connected (boolean)
- meta_integration.pages (array)
- meta_integration.last_sync (datetime)
"""

import asyncio
import logging
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorClient
from config.settings import get_settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def migrate_meta_format():
    """Migrate stores from old Meta format to new format."""
    
    settings = get_settings()
    client = AsyncIOMotorClient(settings.MONGODB_CONNECTION)
    db = client[settings.MONGODB_ANALYSIS_DB]
    
    # Find all stores with old Meta format
    query = {
        "meta_access_token": {"$exists": True, "$ne": ""},
        "meta_integration": {"$exists": False}
    }
    
    stores_cursor = db.global_analysis.find(query)
    stores_to_migrate = await stores_cursor.to_list(length=None)
    
    logger.info(f"Found {len(stores_to_migrate)} stores to migrate")
    
    migrated_count = 0
    
    for store in stores_to_migrate:
        store_id = store["_id"]
        meta_access_token = store.get("meta_access_token")
        meta_user_id = store.get("meta_user_id")
        
        if not meta_access_token:
            logger.warning(f"Store {store_id} has no valid access token, skipping")
            continue
        
        # Create new meta_integration structure
        meta_integration = {
            "connected": True,
            "last_sync": datetime.now(timezone.utc),
            "pages": []
        }
        
        # Create a basic page entry from the old data
        page_entry = {
            "id": meta_user_id if meta_user_id else "unknown",
            "name": f"Migrated Page for Store {store_id}",
            "platform": "facebook",  # Assume Facebook for old format
            "access_token": meta_access_token,
            "category": "Business"
        }
        
        meta_integration["pages"] = [page_entry]
        
        # Update the store document
        try:
            result = await db.global_analysis.update_one(
                {"_id": store_id},
                {
                    "$set": {"meta_integration": meta_integration},
                    "$unset": {
                        "meta_access_token": "",
                        "meta_user_id": ""
                    }
                }
            )
            
            if result.modified_count > 0:
                migrated_count += 1
                logger.info(f"Successfully migrated store {store_id}")
            else:
                logger.warning(f"No changes made to store {store_id}")
                
        except Exception as e:
            logger.error(f"Error migrating store {store_id}: {str(e)}")
    
    logger.info(f"Migration complete. Successfully migrated {migrated_count} stores")
    client.close()

if __name__ == "__main__":
    asyncio.run(migrate_meta_format())