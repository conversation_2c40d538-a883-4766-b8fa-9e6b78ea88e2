import { useState, useCallback } from 'react';
import { useCsrfErrorRecovery } from './useCsrfErrorRecovery';
import { fetchWithDeduplication } from '../services/apiService';
import { logger } from '../utils/logger';

export interface FormSubmissionOptions<TData = Record<string, unknown>, TResponse = unknown> {
  url: string;
  method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: TData;
  onSuccess?: (response: TResponse) => void;
  onError?: (error: unknown) => void;
  successMessage?: string;
  errorMessage?: string;
}

export interface FormValidation {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface UseSecureFormResult {
  isSubmitting: boolean;
  isRecovering: boolean;
  lastError: string | null;
  submitSecureForm: <TData = Record<string, unknown>, TResponse = unknown>(
    options: FormSubmissionOptions<TData, TResponse>
  ) => Promise<void>;
  validateAndSubmit: <TData = Record<string, unknown>, TResponse = unknown>(
    validation: FormValidation,
    options: FormSubmissionOptions<TData, TResponse>
  ) => Promise<void>;
  clearError: () => void;
  resetForm: () => void;
}

/**
 * Hook for secure form handling with built-in CSRF management and validation
 * Provides automatic error recovery, loading states, and consistent patterns
 */
export const useSecureForm = (): UseSecureFormResult => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { retryRequest, isRecovering, lastError, clearError } = useCsrfErrorRecovery();

  const submitSecureForm = useCallback(async <TData = Record<string, unknown>, TResponse = unknown>(
    options: FormSubmissionOptions<TData, TResponse>
  ) => {
    const {
      url,
      method = 'POST',
      data,
      onSuccess,
      onError,
      successMessage,
      errorMessage
    } = options;

    setIsSubmitting(true);
    clearError();

    try {
      logger.debug(`Secure form submission to ${url} with method ${method}`);
      
      const response = await retryRequest(async () => {
        return fetchWithDeduplication(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: data ? JSON.stringify(data) : undefined,
        });
      }) as TResponse;

      logger.info(`Form submission successful to ${url}`);
      
      if (successMessage) {
        logger.info(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(response);
      }
    } catch (error) {
      logger.error(`Form submission failed to ${url}:`, error);
      
      const finalError = lastError || errorMessage || 'Form submission failed';
      
      if (onError) {
        onError(error);
      } else {
        throw new Error(finalError);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [retryRequest, lastError, clearError]);

  const validateAndSubmit = useCallback(async <TData = Record<string, unknown>, TResponse = unknown>(
    validation: FormValidation,
    options: FormSubmissionOptions<TData, TResponse>
  ) => {
    if (!validation.isValid) {
      const errorMessages = Object.values(validation.errors).filter(Boolean);
      const validationError = errorMessages.length > 0 
        ? errorMessages.join(', ') 
        : 'Please fix validation errors before submitting';
      
      logger.warn('Form validation failed:', validation.errors);
      
      if (options.onError) {
        options.onError(validationError);
      } else {
        throw new Error(validationError);
      }
      return;
    }

    await submitSecureForm(options);
  }, [submitSecureForm]);

  const resetForm = useCallback(() => {
    setIsSubmitting(false);
    clearError();
  }, [clearError]);

  return {
    isSubmitting,
    isRecovering,
    lastError,
    submitSecureForm,
    validateAndSubmit,
    clearError,
    resetForm
  };
}; 