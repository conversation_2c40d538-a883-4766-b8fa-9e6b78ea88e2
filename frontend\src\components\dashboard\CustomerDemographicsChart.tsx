import React, { useMemo } from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend } from 'recharts';
import { normalizeLocationName } from '../../utils/locationUtils';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';

// Define interfaces for expected data props
interface CountryData {
  [countryName: string]: number; // Represents count, not percentage directly
}

interface CustomerDemographicsChartProps {
  countryData?: CountryData | null;
}

// Define colors for the chart slices
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#FF4560'];

const CustomerDemographicsChart: React.FC<CustomerDemographicsChartProps> = React.memo(({ countryData }) => {
  const { t } = useTranslation();

  // Memoize chart data calculation for performance
  const { countryChartData, hasCountryData } = useMemo(() => {
    const total = countryData ? Object.values(countryData).reduce((sum, count) => sum + count, 0) : 0;
    
    const chartData = countryData
      ? Object.entries(countryData).map(([name, value]) => ({
          name: normalizeLocationName(name),
          value: value, // The actual count
          percentage: total > 0 ? Math.round((value / total) * 1000) / 10 : 0, // Calculate percentage
        }))
      : [];

    return {
      countryChartData: chartData,
      hasCountryData: chartData.length > 0
    };
  }, [countryData]);

  return (
    <Card elevation={2} sx={{ 
      minHeight: '400px' // Ensure minimum height instead of aspectRatio
    }}>
      <CardContent sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <Typography variant="h6" gutterBottom>
          {t('customerDemographics.title', 'Customer Demographics')}
        </Typography>
        {!hasCountryData ? (
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Typography variant="body2" color="text.secondary" fontStyle="italic">
              {t('customerDemographics.notAvailable', 'No customer demographic data available.')}
            </Typography>
          </Box>
        ) : (
          <div style={{ minHeight: '300px', width: '100%', flex: 1 }}>
            <ChartContainer width="100%" height="100%" minHeight={300}>
              <PieChart>
                <Pie
                  data={countryChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={(entry) => `${entry.name}: ${entry.percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {countryChartData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number, name: string) => [`${value} ${t('customerDemographics.customers', 'customers')}`, name]}
                  labelFormatter={(label: string) => `${label}: ${countryChartData?.find((item: {name: string, value: number, percentage: number}) => item.name === label)?.percentage || 0}%`}
                />
                <Legend />
              </PieChart>
            </ChartContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

export default CustomerDemographicsChart; 