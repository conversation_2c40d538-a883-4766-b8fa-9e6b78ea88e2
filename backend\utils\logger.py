import logging
from config.database import db_analysis

# Configure logging
logger = logging.getLogger(__name__)

def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

async def ensure_indexes_util(db_name: str, collection_name: str, indexes: list):
    # Placeholder until function is implemented or removed
    pass

# Ensure indexes for optimal query performance
async def ensure_indexes():
    """Ensure all necessary indexes exist for efficient querying"""
    try:
        # Get existing indexes using async list comprehension
        cursor = db_analysis.global_analysis.list_indexes()
        existing_indexes = [idx async for idx in cursor]
        existing_index_names = [idx['name'] for idx in existing_indexes if 'name' in idx]

        # Drop existing text indexes to avoid conflicts
        if any('text' in idx_name for idx_name in existing_index_names):
            logger.info("Dropping existing text indexes before recreation")
            for idx_name in existing_index_names:
                if 'text' in idx_name:
                    await db_analysis.global_analysis.drop_index(idx_name)

        # Create text indexes for analysis sections
        logger.info("Creating text indexes")
        await db_analysis.global_analysis.create_index([
            ("analysis.summary", "text"),
            ("analysis.metrics", "text"),
            ("analysis.customer_analysis", "text"),
            ("analysis.product_performance", "text"),
            ("analysis.market_position", "text"),
            ("analysis.recommendations", "text"),
            ("store.name", "text"),
            ("store.business_type", "text")
        ], name="global_analysis_text_index")
        
        # Create regular indexes if they don't exist
        regular_indexes = [
            ("metadata.last_updated", "metadata_last_updated"),
            ("store.id_store", "store_id_store"),
            ("metrics.customer_count", "metrics_customer_count"),
            ("metrics.order_count", "metrics_order_count"),
            ("metrics.total_revenue", "metrics_total_revenue"),
            ("metrics.avg_order_value", "metrics_avg_order_value"),
            ("relationships.customers_with_orders", "relationships_customers_with_orders"),
            ("relationships.repeat_customers", "relationships_repeat_customers"),
            ("order_statistics.total_orders", "order_statistics_total_orders"),
            ("order_statistics.total_sales", "order_statistics_total_sales"),
            ("order_statistics.total_sales_usd", "order_statistics_total_sales_usd"),
            ("visits.total_visits", "visits_total_visits"),
            ("visits.unique_visitors", "visits_unique_visitors"),
            ("coupons.total_active", "coupons_total_active"),
            ("meta_integration.connected", "meta_integration_connected"),
            ("meta_integration.business_id", "meta_integration_business_id"),
            ("meta_integration.last_sync", "meta_integration_last_sync"),
            ("meta_integration.insights.page_views", "meta_insights_page_views"),
            ("meta_integration.insights.page_likes", "meta_insights_page_likes"),
            ("meta_integration.insights.post_engagement", "meta_insights_post_engagement"),
            ("meta_integration.insights.reach", "meta_insights_reach")
        ]

        logger.info("Creating regular indexes")
        for field, name in regular_indexes:
            if name not in existing_index_names:
                await db_analysis.global_analysis.create_index(field, name=name)
        
        logger.info("All indexes have been created successfully")
    except Exception as e:
        logger.error(f"Error creating indexes: {str(e)}")
        # Continue execution even if index creation fails
        pass

# Call setup_logging at the module level to ensure it's configured when imported
setup_logging()

