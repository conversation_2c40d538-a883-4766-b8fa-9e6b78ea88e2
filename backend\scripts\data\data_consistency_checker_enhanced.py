#!/usr/bin/env python3
"""
data_consistency_checker_enhanced.py - Comprehensive data consistency validation

This enhanced version validates:
1. Data integrity across all MongoDB collections
2. Consistency with app goals and business logic
3. Script interdependencies and execution order
4. Revenue calculation consistency
5. Store filtering logic adherence
6. Meta integration data alignment
7. AI analysis quality
8. Pipeline execution health

Run this AFTER all update scripts to ensure complete data integrity
"""

import os
import sys
import logging
import mysql.connector
from pymongo import MongoClient
from pymongo.errors import BulkWriteError
from datetime import datetime, timezone, timedelta
import traceback
import json
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import hashlib
import statistics

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

try:
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    print(f"FATAL: Error importing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_consistency_check_enhanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants from app configuration
MONGO_CONNECTION_STRING = settings.MONGODB_CONNECTION
MONGO_DB_NAME = settings.MONGODB_ANALYSIS_DB
MYSQL_CONFIG = {
    'host': settings.MYSQL_HOST,
    'port': int(settings.MYSQL_PORT),
    'user': settings.MYSQL_USER,
    'password': settings.MYSQL_PASSWORD,
    'database': settings.MYSQL_DB_LANUBE
}

# Business logic constants (from update_active_stores.py)
ACTIVITY_WINDOW_YEARS = 3
NEW_STORE_CUTOFF_YEAR = 2025
BATCH_SIZE = 100
INACTIVE_STORE_EXCEPTIONS = {
    10, 4, 349, 503, 1018, 573, 757, 947, 470, 627, 854, 195, 263, 1094, 687, 927, 591,
    904, 1107, 1105, 889, 1092, 749, 57, 381, 1067, 769, 793, 745, 725, 1044, 1038, 841,
    1011, 1003, 1004, 1131, 998, 1023, 995, 991, 1112, 1074, 463, 983, 981, 978
}

# Valid order statuses for revenue calculation
VALID_ORDER_STATUSES = [2, 5, 7]

# Data quality thresholds
MAX_AGE_HOURS = 24
CRITICAL_DISCREPANCY_THRESHOLD = 0.05  # 5%
WARNING_DISCREPANCY_THRESHOLD = 0.01   # 1%
MIN_EMBEDDING_DIMENSION = 3072  # OpenAI text-embedding-3-large
MAX_EMPTY_AI_CONTENT_RATIO = 0.1  # Max 10% of stores can have empty AI content

# Pipeline execution order
PIPELINE_SEQUENCE = [
    'meta_db_init.py',
    'update_store_users.py',
    'update_active_stores.py',
    'update_product_details.py',
    'fix_large_store_documents.py',
    'update_product_variations.py',
    'optimize_large_store_products.py',
    'consolidate_optimized_store.py',
    'update_product_categories.py',
    'update_customers_relationships.py',
    'update_store_activity_metrics.py',
    'update_meta_sales_correlation.py',
    'data_consistency_checker.py',
    'market_analyzer_batch.py',
    'update_competitor_analysis.py',
    'embeddings_generator_analysis.py',
    'translate_mongo_collections_async.py'
]

# Email settings (optional)
SMTP_HOST = os.getenv('SMTP_HOST', '')
SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
SMTP_USER = os.getenv('SMTP_USER', '')
SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
ALERT_EMAIL_TO = os.getenv('ALERT_EMAIL_TO', '')


class EnhancedDataConsistencyChecker:
    def __init__(self):
        """Initialize the enhanced consistency checker"""
        self.mongo_client = None
        self.mongo_db = None
        self.mysql_connection = None
        self.mysql_cursor = None
        
        self.issues = {
            'critical': [],
            'warning': [],
            'info': []
        }
        
        self.stats = {
            'stores_checked': 0,
            'stores_with_issues': 0,
            'critical_issues': 0,
            'warnings': 0,
            'info_issues': 0,
            'data_age_issues': 0,
            'revenue_consistency_issues': 0,
            'store_filtering_issues': 0,
            'pipeline_integrity_issues': 0,
            'ai_quality_issues': 0,
            'meta_alignment_issues': 0
        }
        
        self.collection_info = {}
        self.pipeline_status = {}
        self.mysql_store_data = {}
        
    def connect(self) -> bool:
        """Establish MongoDB and MySQL connections"""
        try:
            # MongoDB connection
            self.mongo_client = MongoClient(MONGO_CONNECTION_STRING)
            self.mongo_db = self.mongo_client[MONGO_DB_NAME]
            logger.info(f"Connected to MongoDB database: {MONGO_DB_NAME}")
            
            # MySQL connection
            self.mysql_connection = mysql.connector.connect(**MYSQL_CONFIG)
            self.mysql_cursor = self.mysql_connection.cursor(dictionary=True)
            logger.info("Connected to MySQL database")
            
            return True
        except Exception as e:
            logger.error(f"Failed to connect to databases: {str(e)}")
            return False
    
    def check_pipeline_execution_order(self):
        """Verify scripts were executed in correct order based on timestamps"""
        logger.info("Checking pipeline execution order...")
        
        try:
            # Get pipeline progress if available
            pipeline_progress_path = os.path.join(project_root, 'scripts', 'pipeline_progress.json')
            if os.path.exists(pipeline_progress_path):
                with open(pipeline_progress_path, 'r') as f:
                    self.pipeline_status = json.load(f)
                    
                # Check execution order
                last_timestamp = None
                for script in PIPELINE_SEQUENCE:
                    if script in self.pipeline_status:
                        script_info = self.pipeline_status[script]
                        if script_info.get('status') == 'completed':
                            timestamp = datetime.fromisoformat(script_info['last_run'])
                            if last_timestamp and timestamp < last_timestamp:
                                self.issues['critical'].append({
                                    'type': 'pipeline_order_violation',
                                    'message': f"Script {script} executed out of order",
                                    'expected_after': last_timestamp.isoformat(),
                                    'actual_time': timestamp.isoformat()
                                })
                                self.stats['pipeline_integrity_issues'] += 1
                                self.stats['critical_issues'] += 1
                            last_timestamp = timestamp
                        elif script_info.get('status') == 'failed':
                            self.issues['critical'].append({
                                'type': 'pipeline_script_failed',
                                'script': script,
                                'error': script_info.get('error', 'Unknown error')
                            })
                            self.stats['pipeline_integrity_issues'] += 1
                            self.stats['critical_issues'] += 1
            else:
                self.issues['warning'].append({
                    'type': 'pipeline_progress_missing',
                    'message': 'Pipeline progress file not found'
                })
                self.stats['warnings'] += 1
                
        except Exception as e:
            logger.error(f"Error checking pipeline execution order: {str(e)}")
            self.issues['warning'].append({
                'type': 'pipeline_check_failed',
                'error': str(e)
            })
    
    def validate_store_filtering_logic(self):
        """Ensure store filtering logic is consistent with business rules"""
        logger.info("Validating store filtering logic...")
        
        try:
            # Get active stores from cache
            active_stores_cache = set()
            for doc in self.mongo_db['active_stores_cache'].find({}, {'_id': 1}):
                active_stores_cache.add(doc['_id'])
            
            # Query MySQL for store validation
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=ACTIVITY_WINDOW_YEARS * 365)
            
            # Get stores that should be active according to business rules
            query = """
            SELECT DISTINCT s.id, s.name, s.active, s.created_at,
                   COUNT(DISTINCT o.id) as order_count,
                   MAX(o.created_at) as last_order_date
            FROM stores s
            LEFT JOIN orders o ON o.store_id = s.id 
                AND o.status IN (%s, %s, %s)
                AND o.created_at >= %s
            WHERE (
                (s.active = 1)  -- Active stores
                OR s.id IN ({})  -- Exception stores
                OR YEAR(s.created_at) >= %s  -- New stores
            )
            AND (s.name NOT LIKE '%%test%%' AND s.name NOT LIKE '%%demo%%')
            GROUP BY s.id
            """.format(','.join(map(str, INACTIVE_STORE_EXCEPTIONS)))
            
            self.mysql_cursor.execute(query, (
                VALID_ORDER_STATUSES[0], VALID_ORDER_STATUSES[1], VALID_ORDER_STATUSES[2],
                cutoff_date, NEW_STORE_CUTOFF_YEAR
            ))
            
            expected_active_stores = set()
            for row in self.mysql_cursor.fetchall():
                store_id = str(row['id'])
                expected_active_stores.add(store_id)
                self.mysql_store_data[store_id] = row
            
            # Check for missing stores
            missing_stores = expected_active_stores - active_stores_cache
            if missing_stores:
                self.issues['critical'].append({
                    'type': 'missing_active_stores',
                    'count': len(missing_stores),
                    'sample_stores': list(missing_stores)[:10],
                    'message': 'Stores meeting criteria but not in active_stores_cache'
                })
                self.stats['store_filtering_issues'] += len(missing_stores)
                self.stats['critical_issues'] += 1
            
            # Check for incorrectly included stores
            extra_stores = active_stores_cache - expected_active_stores
            if extra_stores:
                # Verify these aren't valid exceptions
                for store_id in list(extra_stores)[:20]:  # Check sample
                    self.mysql_cursor.execute(
                        "SELECT id, name, active, created_at FROM stores WHERE id = %s",
                        (int(store_id),)
                    )
                    store_data = self.mysql_cursor.fetchone()
                    if store_data:
                        if ('test' in store_data['name'].lower() or 
                            'demo' in store_data['name'].lower()):
                            self.issues['critical'].append({
                                'type': 'test_store_included',
                                'store_id': store_id,
                                'store_name': store_data['name'],
                                'message': 'Test/demo store included in active stores'
                            })
                            self.stats['store_filtering_issues'] += 1
                            self.stats['critical_issues'] += 1
                            
        except Exception as e:
            logger.error(f"Error validating store filtering: {str(e)}")
            self.issues['warning'].append({
                'type': 'store_filtering_check_failed',
                'error': str(e)
            })
            self.stats['warnings'] += 1
    
    def validate_revenue_calculations(self, store_id: str) -> Dict[str, Any]:
        """Validate revenue calculations across all collections and MySQL"""
        revenue_data = {}
        
        try:
            # Get revenue from active_stores_cache
            active_store = self.mongo_db['active_stores_cache'].find_one({'_id': store_id})
            if active_store:
                revenue_data['active_cache'] = active_store.get('metrics', {}).get('total_revenue', 0)
            
            # Get revenue from product_details_cache
            product_cache = self.mongo_db['product_details_cache'].find_one({'_id': store_id})
            if product_cache:
                revenue_data['product_revenue'] = product_cache.get('store_aggregations', {}).get('total_revenue', 0)
                revenue_data['shipping_revenue'] = product_cache.get('shipping_info', {}).get('shipping_revenue', 0)
                revenue_data['total_with_shipping'] = product_cache.get('shipping_info', {}).get('total_revenue_with_shipping', 0)
            
            # Calculate revenue from MySQL using exact formula
            query = """
            SELECT 
                SUM(oi.price * oi.quantity) as item_total,
                SUM(o.shipping_cost_customer) as shipping_total,
                SUM(o.discount) as discount_total,
                COUNT(DISTINCT o.id) as order_count
            FROM orders o
            JOIN order_items oi ON oi.order_id = o.id
            WHERE o.store_id = %s
                AND o.status IN (%s, %s, %s)
            """
            
            self.mysql_cursor.execute(query, (
                int(store_id),
                VALID_ORDER_STATUSES[0], VALID_ORDER_STATUSES[1], VALID_ORDER_STATUSES[2]
            ))
            
            mysql_result = self.mysql_cursor.fetchone()
            if mysql_result and mysql_result['item_total']:
                # Apply exact revenue formula: sum(item_prices) + shipping_cost - order_discount
                revenue_data['mysql_calculated'] = (
                    float(mysql_result['item_total'] or 0) +
                    float(mysql_result['shipping_total'] or 0) -
                    float(mysql_result['discount_total'] or 0)
                )
                revenue_data['mysql_order_count'] = mysql_result['order_count']
            
            # Check for discrepancies
            if 'mysql_calculated' in revenue_data and 'active_cache' in revenue_data:
                if revenue_data['mysql_calculated'] > 0:
                    discrepancy = abs(revenue_data['active_cache'] - revenue_data['mysql_calculated'])
                    discrepancy_pct = (discrepancy / revenue_data['mysql_calculated']) * 100
                    
                    if discrepancy_pct > CRITICAL_DISCREPANCY_THRESHOLD * 100:
                        self.issues['critical'].append({
                            'type': 'revenue_calculation_mismatch',
                            'store_id': store_id,
                            'mongo_revenue': revenue_data['active_cache'],
                            'mysql_revenue': revenue_data['mysql_calculated'],
                            'discrepancy_percentage': round(discrepancy_pct, 2),
                            'message': 'Revenue calculation differs significantly from source data'
                        })
                        self.stats['revenue_consistency_issues'] += 1
                        self.stats['critical_issues'] += 1
                        
        except Exception as e:
            logger.error(f"Error validating revenue for store {store_id}: {str(e)}")
            
        return revenue_data
    
    def check_meta_integration_alignment(self, store_id: str):
        """Validate Meta (Facebook/Instagram) integration data consistency"""
        try:
            # Check meta_sales_correlation collection
            meta_correlation = self.mongo_db['meta_sales_correlation'].find_one({'_id': store_id})
            if not meta_correlation:
                return
            
            # Check for logical consistency in Meta data
            campaigns = meta_correlation.get('campaigns', [])
            total_spend = sum(c.get('spend', 0) for c in campaigns)
            total_impressions = sum(c.get('impressions', 0) for c in campaigns)
            total_clicks = sum(c.get('clicks', 0) for c in campaigns)
            
            # Validate Meta metrics logic
            if total_impressions > 0 and total_clicks > total_impressions:
                self.issues['critical'].append({
                    'type': 'meta_metrics_invalid',
                    'store_id': store_id,
                    'message': 'Meta clicks exceed impressions',
                    'clicks': total_clicks,
                    'impressions': total_impressions
                })
                self.stats['meta_alignment_issues'] += 1
                self.stats['critical_issues'] += 1
            
            # Check for spend without impressions
            if total_spend > 0 and total_impressions == 0:
                self.issues['warning'].append({
                    'type': 'meta_spend_no_impressions',
                    'store_id': store_id,
                    'spend': total_spend,
                    'message': 'Meta campaigns show spend but no impressions'
                })
                self.stats['meta_alignment_issues'] += 1
                self.stats['warnings'] += 1
            
            # Check ROI calculations
            attributed_revenue = meta_correlation.get('attribution_summary', {}).get('attributed_revenue', 0)
            if total_spend > 0 and attributed_revenue > 0:
                roi = (attributed_revenue - total_spend) / total_spend
                if roi > 100:  # ROI > 10,000% is suspicious
                    self.issues['warning'].append({
                        'type': 'meta_roi_suspicious',
                        'store_id': store_id,
                        'roi_percentage': round(roi * 100, 2),
                        'message': 'Unusually high ROI detected'
                    })
                    self.stats['meta_alignment_issues'] += 1
                    self.stats['warnings'] += 1
                    
        except Exception as e:
            logger.error(f"Error checking Meta integration for store {store_id}: {str(e)}")
    
    def check_ai_analysis_quality(self):
        """Validate AI-generated content quality across collections"""
        logger.info("Checking AI analysis quality...")
        
        try:
            # Check global_analysis collection
            total_stores = 0
            empty_analysis_count = 0
            placeholder_count = 0
            
            for doc in self.mongo_db['global_analysis'].find({}, {
                '_id': 1, 
                'analysis': 1,
                'business_recommendations': 1,
                'competitive_advantages': 1
            }):
                total_stores += 1
                
                # Check for empty or minimal AI content
                analysis = doc.get('analysis', '')
                recommendations = doc.get('business_recommendations', {})
                advantages = doc.get('competitive_advantages', [])
                
                if not analysis or len(analysis) < 100:
                    empty_analysis_count += 1
                
                # Check for placeholder content
                if analysis and ('placeholder' in analysis.lower() or 
                               'lorem ipsum' in analysis.lower() or
                               'test content' in analysis.lower()):
                    placeholder_count += 1
                    self.issues['warning'].append({
                        'type': 'ai_placeholder_content',
                        'store_id': doc['_id'],
                        'message': 'AI analysis contains placeholder text'
                    })
                    self.stats['ai_quality_issues'] += 1
                    self.stats['warnings'] += 1
            
            # Check if too many stores have empty analysis
            if total_stores > 0:
                empty_ratio = empty_analysis_count / total_stores
                if empty_ratio > MAX_EMPTY_AI_CONTENT_RATIO:
                    self.issues['critical'].append({
                        'type': 'ai_analysis_missing',
                        'empty_count': empty_analysis_count,
                        'total_stores': total_stores,
                        'empty_percentage': round(empty_ratio * 100, 2),
                        'message': 'Too many stores missing AI analysis'
                    })
                    self.stats['ai_quality_issues'] += empty_analysis_count
                    self.stats['critical_issues'] += 1
            
            # Check embeddings quality
            embeddings_count = 0
            invalid_embeddings = 0
            
            for doc in self.mongo_db['analysis_embeddings'].find({}, {
                '_id': 1,
                'embedding': 1,
                'metadata.dimension': 1
            }).limit(1000):  # Sample check
                embeddings_count += 1
                
                embedding = doc.get('embedding', [])
                expected_dimension = doc.get('metadata', {}).get('dimension', MIN_EMBEDDING_DIMENSION)
                
                if not embedding or len(embedding) != expected_dimension:
                    invalid_embeddings += 1
                    self.issues['warning'].append({
                        'type': 'invalid_embedding_dimension',
                        'document_id': str(doc['_id']),
                        'expected': expected_dimension,
                        'actual': len(embedding) if embedding else 0
                    })
                    self.stats['ai_quality_issues'] += 1
                    self.stats['warnings'] += 1
            
            if embeddings_count > 0 and invalid_embeddings > embeddings_count * 0.05:
                self.issues['critical'].append({
                    'type': 'embedding_quality_issue',
                    'invalid_count': invalid_embeddings,
                    'checked_count': embeddings_count,
                    'message': 'Too many invalid embeddings detected'
                })
                self.stats['critical_issues'] += 1
                
        except Exception as e:
            logger.error(f"Error checking AI analysis quality: {str(e)}")
            self.issues['warning'].append({
                'type': 'ai_quality_check_failed',
                'error': str(e)
            })
    
    def check_store_comprehensive(self, store_id: str) -> Dict[str, Any]:
        """Comprehensive consistency check for a single store"""
        store_issues = []
        
        try:
            # Basic collection presence check
            collections_present = {}
            for collection in ['active_stores_cache', 'product_details_cache', 
                             'store_customers_cache', 'global_analysis']:
                collections_present[collection] = bool(
                    self.mongo_db[collection].find_one({'_id': store_id})
                )
            
            if not all(collections_present.values()):
                store_issues.append({
                    'severity': 'warning',
                    'type': 'missing_collections',
                    'collections': collections_present
                })
            
            # Revenue validation
            revenue_data = self.validate_revenue_calculations(store_id)
            
            # Meta integration check
            self.check_meta_integration_alignment(store_id)
            
            # Customer data consistency
            active_store = self.mongo_db['active_stores_cache'].find_one({'_id': store_id})
            customer_cache = self.mongo_db['store_customers_cache'].find_one({'_id': store_id})
            
            if active_store and customer_cache:
                active_customers = active_store.get('metrics', {}).get('total_customers', 0)
                cache_customers = customer_cache.get('total_customers', 0)
                
                if abs(active_customers - cache_customers) > 5:
                    store_issues.append({
                        'severity': 'warning',
                        'type': 'customer_count_mismatch',
                        'active_stores': active_customers,
                        'customer_cache': cache_customers
                    })
            
            # Check update timestamps
            for collection_name, present in collections_present.items():
                if present:
                    doc = self.mongo_db[collection_name].find_one({'_id': store_id})
                    timestamp_fields = ['last_updated_script', 'overall_last_updated', 'last_updated']
                    
                    timestamp = None
                    for field in timestamp_fields:
                        if field in doc:
                            timestamp = doc[field]
                            break
                    
                    if timestamp:
                        if isinstance(timestamp, str):
                            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        elif isinstance(timestamp, datetime) and timestamp.tzinfo is None:
                            timestamp = timestamp.replace(tzinfo=timezone.utc)
                        
                        age_hours = (datetime.now(timezone.utc) - timestamp).total_seconds() / 3600
                        
                        if age_hours > MAX_AGE_HOURS * 2:  # Very stale
                            store_issues.append({
                                'severity': 'critical',
                                'type': 'very_stale_data',
                                'collection': collection_name,
                                'age_hours': round(age_hours, 2)
                            })
            
            return {
                'store_id': store_id,
                'issues': store_issues,
                'revenue_data': revenue_data,
                'collections_present': collections_present
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive check for store {store_id}: {str(e)}")
            return {
                'store_id': store_id,
                'error': str(e),
                'issues': [{
                    'severity': 'error',
                    'type': 'check_failed',
                    'message': str(e)
                }]
            }
    
    def run_enhanced_check(self, store_ids: Optional[List[str]] = None):
        """Run comprehensive consistency check"""
        logger.info("Starting enhanced data consistency check...")
        
        # Check pipeline execution order
        self.check_pipeline_execution_order()
        
        # Validate store filtering logic
        self.validate_store_filtering_logic()
        
        # Check AI analysis quality
        self.check_ai_analysis_quality()
        
        # Get stores to check
        if store_ids:
            stores_to_check = store_ids
        else:
            stores_to_check = [doc['_id'] for doc in 
                             self.mongo_db['active_stores_cache'].find({}, {'_id': 1})]
        
        logger.info(f"Checking {len(stores_to_check)} stores...")
        
        # Check each store
        stores_with_issues = []
        for i, store_id in enumerate(stores_to_check):
            if i % 100 == 0 and i > 0:
                logger.info(f"Progress: {i}/{len(stores_to_check)} stores checked")
            
            self.stats['stores_checked'] += 1
            result = self.check_store_comprehensive(store_id)
            
            if result.get('issues'):
                stores_with_issues.append(result)
                self.stats['stores_with_issues'] += 1
                
                # Categorize issues
                for issue in result['issues']:
                    severity = issue.get('severity', 'info')
                    issue['store_id'] = store_id
                    
                    if severity == 'critical':
                        self.issues['critical'].append(issue)
                    elif severity == 'warning':
                        self.issues['warning'].append(issue)
                    else:
                        self.issues['info'].append(issue)
        
        # Generate comprehensive report
        self.generate_enhanced_report(stores_with_issues)
        
        # Send alerts if critical issues found
        if self.issues['critical']:
            self.send_alerts()
        
        return self.stats, self.issues
    
    def generate_enhanced_report(self, stores_with_issues: List[Dict]):
        """Generate comprehensive consistency report"""
        report_path = f"consistency_report_enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Analyze issue patterns
        issue_patterns = defaultdict(int)
        for issue_list in [self.issues['critical'], self.issues['warning'], self.issues['info']]:
            for issue in issue_list:
                issue_patterns[issue['type']] += 1
        
        # Generate actionable recommendations
        recommendations = self.generate_actionable_recommendations(issue_patterns)
        
        # Calculate health score
        total_checks = self.stats['stores_checked'] * 10  # Approximate checks per store
        total_issues = sum(len(issues) for issues in self.issues.values())
        health_score = max(0, 100 - (total_issues / total_checks * 100)) if total_checks > 0 else 0
        
        report = {
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'health_score': round(health_score, 2),
            'summary': self.stats,
            'pipeline_status': self.pipeline_status,
            'issue_patterns': dict(issue_patterns),
            'critical_issues': self.issues['critical'][:100],
            'warnings': self.issues['warning'][:100],
            'recommendations': recommendations,
            'stores_with_issues': len(stores_with_issues),
            'sample_issues': stores_with_issues[:20]
        }
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Enhanced consistency report saved to: {report_path}")
        
        # Print comprehensive summary
        print("\n" + "="*70)
        print("ENHANCED DATA CONSISTENCY CHECK SUMMARY")
        print("="*70)
        print(f"Overall Health Score: {health_score:.1f}%")
        print(f"\nStores Checked: {self.stats['stores_checked']}")
        print(f"Stores with Issues: {self.stats['stores_with_issues']} ({self.stats['stores_with_issues']/max(1, self.stats['stores_checked'])*100:.1f}%)")
        print(f"\nIssue Breakdown:")
        print(f"  Critical Issues: {self.stats['critical_issues']}")
        print(f"  Warnings: {self.stats['warnings']}")
        print(f"  Info Issues: {self.stats['info_issues']}")
        print(f"\nSpecific Issues:")
        print(f"  Revenue Consistency: {self.stats['revenue_consistency_issues']}")
        print(f"  Store Filtering: {self.stats['store_filtering_issues']}")
        print(f"  Pipeline Integrity: {self.stats['pipeline_integrity_issues']}")
        print(f"  AI Quality: {self.stats['ai_quality_issues']}")
        print(f"  Meta Alignment: {self.stats['meta_alignment_issues']}")
        print(f"  Stale Data: {self.stats['data_age_issues']}")
        print("\nTop Recommendations:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"  {i}. {rec}")
        print("="*70)
    
    def generate_actionable_recommendations(self, issue_patterns: Dict[str, int]) -> List[str]:
        """Generate specific, actionable recommendations based on issues found"""
        recommendations = []
        
        # Critical recommendations based on issue patterns
        if issue_patterns.get('pipeline_order_violation', 0) > 0:
            recommendations.append(
                "CRITICAL: Re-run entire pipeline in correct sequence using pipeline_automation.py"
            )
        
        if issue_patterns.get('missing_active_stores', 0) > 0:
            recommendations.append(
                "URGENT: Re-run update_active_stores.py - some stores meeting criteria are missing"
            )
        
        if issue_patterns.get('revenue_calculation_mismatch', 0) > 5:
            recommendations.append(
                "Implement database transaction support to ensure atomic revenue updates across collections"
            )
        
        if issue_patterns.get('test_store_included', 0) > 0:
            recommendations.append(
                "Update store filtering logic to properly exclude test/demo stores"
            )
        
        if self.stats['revenue_consistency_issues'] > self.stats['stores_checked'] * 0.01:
            recommendations.append(
                "Revenue calculation logic needs review - over 1% of stores have discrepancies"
            )
        
        if issue_patterns.get('meta_metrics_invalid', 0) > 0:
            recommendations.append(
                "Validate Meta API responses before storing - invalid metrics detected"
            )
        
        if issue_patterns.get('ai_analysis_missing', 0) > 0:
            recommendations.append(
                f"Re-run market_analyzer_batch.py for {issue_patterns['ai_analysis_missing']} stores"
            )
        
        if issue_patterns.get('very_stale_data', 0) > 0:
            recommendations.append(
                "Schedule pipeline to run more frequently - data staleness exceeds thresholds"
            )
        
        if self.stats['pipeline_integrity_issues'] > 0:
            recommendations.append(
                "Review pipeline_automation.py error handling - some scripts failed or ran out of order"
            )
        
        # Performance recommendations
        if self.stats['stores_checked'] > 1000 and self.stats['stores_with_issues'] > self.stats['stores_checked'] * 0.1:
            recommendations.append(
                "Consider implementing incremental updates instead of full refreshes for better consistency"
            )
        
        return recommendations
    
    def send_alerts(self):
        """Send email alerts for critical issues"""
        if not all([SMTP_HOST, SMTP_USER, SMTP_PASSWORD, ALERT_EMAIL_TO]):
            logger.info("Email configuration not complete, skipping alerts")
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = SMTP_USER
            msg['To'] = ALERT_EMAIL_TO
            msg['Subject'] = f"[D-Unit] Critical Data Consistency Issues - {self.stats['critical_issues']} found"
            
            # Calculate health score
            total_checks = self.stats['stores_checked'] * 10
            total_issues = sum(len(issues) for issues in self.issues.values())
            health_score = max(0, 100 - (total_issues / total_checks * 100)) if total_checks > 0 else 0
            
            body = f"""
            Critical data consistency issues detected in D-Unit platform:
            
            Overall Health Score: {health_score:.1f}%
            
            Summary:
            - Stores Checked: {self.stats['stores_checked']}
            - Critical Issues: {self.stats['critical_issues']}
            - Warnings: {self.stats['warnings']}
            - Revenue Issues: {self.stats['revenue_consistency_issues']}
            - Pipeline Issues: {self.stats['pipeline_integrity_issues']}
            
            Top Critical Issues:
            """
            
            # Group critical issues by type
            critical_by_type = defaultdict(list)
            for issue in self.issues['critical'][:20]:
                critical_by_type[issue['type']].append(issue)
            
            for issue_type, issues in critical_by_type.items():
                body += f"\n\n{issue_type.upper().replace('_', ' ')} ({len(issues)} occurrences):"
                for issue in issues[:3]:  # Top 3 of each type
                    body += f"\n- Store {issue.get('store_id', 'N/A')}: {issue.get('message', 'No message')}"
            
            body += "\n\nPlease check the full consistency report for complete details."
            body += "\n\nThis is an automated alert from the D-Unit Data Consistency Checker."
            
            msg.attach(MIMEText(body, 'plain'))
            
            with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
                server.starttls()
                server.login(SMTP_USER, SMTP_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Alert email sent to {ALERT_EMAIL_TO}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {str(e)}")
    
    def close(self):
        """Close all database connections"""
        if self.mongo_client:
            self.mongo_client.close()
        if self.mysql_cursor:
            self.mysql_cursor.close()
        if self.mysql_connection:
            self.mysql_connection.close()


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Enhanced data consistency checker for D-Unit platform'
    )
    parser.add_argument('--stores', nargs='+', help='Specific store IDs to check')
    parser.add_argument('--email-alerts', action='store_true', 
                       help='Send email alerts for critical issues')
    parser.add_argument('--quick', action='store_true',
                       help='Quick check - sample 10% of stores')
    args = parser.parse_args()
    
    checker = EnhancedDataConsistencyChecker()
    
    try:
        if not checker.connect():
            logger.error("Failed to connect to databases")
            sys.exit(1)
        
        # Run the enhanced consistency check
        store_ids = args.stores
        if args.quick and not store_ids:
            # Quick check - sample 10% of stores
            all_stores = [doc['_id'] for doc in 
                         checker.mongo_db['active_stores_cache'].find({}, {'_id': 1})]
            import random
            store_ids = random.sample(all_stores, max(1, len(all_stores) // 10))
            logger.info(f"Quick check mode: sampling {len(store_ids)} stores")
        
        stats, issues = checker.run_enhanced_check(store_ids=store_ids)
        
        # Determine exit code based on severity
        if stats['critical_issues'] > 0:
            sys.exit(2)  # Critical issues found
        elif stats['warnings'] > 0:
            sys.exit(1)  # Warnings found
        else:
            sys.exit(0)  # All checks passed
            
    finally:
        checker.close()


if __name__ == "__main__":
    main()