import authService from './authService';
import { API_URL } from '../config/api';
import { DISABLE_CSRF } from '../config/api';

interface CSRFTokenResponse {
    success: boolean;
    csrf_token: string;
    expires_in: number;
    rotated?: boolean;
    message?: string;
    double_submit_enabled?: boolean;
    cookie_token_set?: boolean;
}

class CSRFService {
    private token: string | null = null;
    private tokenExpiry: number | null = null;
    private readonly TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry
    private readonly MAX_RETRY_ATTEMPTS = 3;
    private readonly RETRY_DELAY_BASE = 1000; // 1 second base delay
    private rotationEnabled: boolean = true;
    private doubleSubmitEnabled: boolean = false;

    /**
     * Get a valid CSRF token, refreshing if necessary
     */
    async getToken(): Promise<string> {
        if (DISABLE_CSRF) {
            // CSRF protection disabled – return empty string placeholder
            return '';
        }
        // Check if we have a valid token
        if (this.token && this.tokenExpiry && Date.now() < this.tokenExpiry - this.TOKEN_REFRESH_THRESHOLD) {
            return this.token;
        }

        // Fetch a new token
        return this.fetchNewToken();
    }

    /**
     * Fetch a new CSRF token from the server with retry mechanism
     */
    private async fetchNewToken(): Promise<string> {
        if (DISABLE_CSRF) {
            return '';
        }
        let lastError: Error | null = null;
        
        for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                console.debug(`CSRF token fetch attempt ${attempt}/${this.MAX_RETRY_ATTEMPTS}`);
                
                const authToken = authService.getToken();
                if (!authToken) {
                    throw new Error('Authentication token required to fetch CSRF token');
                }

                const response = await fetch(`${API_URL}/api/auth/csrf-token`, {
                    method: 'GET',
                    credentials: 'include', // Enable sending cookies
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    let errorMessage = '';
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.detail || errorData.message || response.statusText;
                    } catch {
                        errorMessage = response.statusText;
                    }
                    
                    // Handle specific error cases
                    if (response.status === 401) {
                        // Authentication failure - don't retry to prevent infinite loops
                        throw new Error(`Authentication token expired or invalid: ${errorMessage}`);
                    } else if (response.status === 403) {
                        // Access denied - don't retry
                        throw new Error(`Access denied for CSRF token generation: ${errorMessage}`);
                    } else if (response.status === 500) {
                        throw new Error(`Server error generating CSRF token: ${errorMessage}`);
                    } else {
                        throw new Error(`Failed to fetch CSRF token: ${response.status} ${errorMessage}`);
                    }
                }

                const data: CSRFTokenResponse = await response.json();
                
                if (!data.success || !data.csrf_token) {
                    throw new Error('Invalid CSRF token response from server');
                }

                // Store the token and calculate expiry
                this.token = data.csrf_token;
                this.tokenExpiry = Date.now() + (data.expires_in * 1000);
                
                // Update double submit configuration
                this.doubleSubmitEnabled = data.double_submit_enabled || false;

                console.debug(`CSRF token fetched successfully on attempt ${attempt} (double submit: ${this.doubleSubmitEnabled})`);
                return this.token;
                
            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                console.warn(`CSRF token fetch attempt ${attempt} failed:`, lastError.message);
                
                // Don't retry on authentication errors or access denied to prevent infinite loops
                if (lastError.message.includes('Authentication token') || 
                    lastError.message.includes('Access denied') ||
                    lastError.message.includes('expired or invalid')) {
                    console.debug('Authentication error detected, stopping retry attempts');
                    break;
                }
                
                // If this isn't the last attempt, wait before retrying
                if (attempt < this.MAX_RETRY_ATTEMPTS) {
                    const delay = this.RETRY_DELAY_BASE * Math.pow(2, attempt - 1); // Exponential backoff
                    console.debug(`Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        // Clear any stale token on fetch failure
        this.clearToken();
        
        const finalError = lastError || new Error('Unknown error occurred during CSRF token fetch');
        // Only log as debug since initializeAfterLogin will handle the error appropriately
        console.debug('All CSRF token fetch attempts failed:', finalError.message);
        throw finalError;
    }

    /**
     * Get headers object with CSRF token
     */
    async getHeaders(): Promise<{ 'X-CSRF-Token': string }> {
        if (DISABLE_CSRF) {
            return {} as { 'X-CSRF-Token': string };
        }
        try {
            const token = await this.getToken();
            return { 'X-CSRF-Token': token };
        } catch (error) {
            console.error('Failed to get CSRF headers:', error);
            throw error;
        }
    }

    /**
     * Clear the stored CSRF token
     */
    clearToken(): void {
        if (DISABLE_CSRF) {
            this.token = null;
            this.tokenExpiry = null;
            return;
        }
        this.token = null;
        this.tokenExpiry = null;
        this.clearCsrfCookie(); // Also clear cookie
        console.debug('CSRF token and cookie cleared');
    }

    /**
     * Check if we have a valid token
     */
    hasValidToken(): boolean {
        return this.token !== null && 
               this.tokenExpiry !== null && 
               Date.now() < this.tokenExpiry - this.TOKEN_REFRESH_THRESHOLD;
    }

    /**
     * Initialize CSRF token after login with retry mechanism
     * Production-ready with exponential backoff and health checking
     */
    async initializeAfterLogin(): Promise<void> {
        if (DISABLE_CSRF) {
            return;
        }
        const maxRetries = 3;
        const baseDelay = 200; // Start with 200ms
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // First verify the backend is reachable with health check
                const healthCheck = await this.healthCheck();
                if (!healthCheck.healthy) {
                    throw new Error(`CSRF service not healthy: ${healthCheck.message}`);
                }
                
                // Now fetch the actual token
                await this.getToken();
                console.debug(`CSRF token initialized successfully on attempt ${attempt}`);
                return;
            } catch (error) {
                const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
                console.warn(`CSRF initialization attempt ${attempt}/${maxRetries} failed:`, error);
                
                // Check for authentication errors that should stop retry attempts
                const errorMessage = error instanceof Error ? error.message : String(error);
                if (errorMessage.includes('Authentication token') || 
                    errorMessage.includes('unauthorized') ||
                    errorMessage.includes('invalid token') ||
                    errorMessage.includes('expired or invalid')) {
                    console.debug('Authentication error during CSRF initialization, stopping retries');
                    break;
                }
                
                if (attempt < maxRetries) {
                    console.debug(`Retrying CSRF initialization in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    console.warn('All CSRF initialization attempts failed - continuing without CSRF token');
                }
            }
        }
    }

    /**
     * Health check method to test CSRF service
     */
    async healthCheck(): Promise<{ healthy: boolean; message: string }> {
        try {
            const response = await fetch(`${API_URL}/api/auth/csrf-token/health`, {
                method: 'GET',
                credentials: 'include', // Enable sending cookies
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return {
                healthy: data.success === true,
                message: data.message || 'Health check completed'
            };
        } catch (error) {
            return {
                healthy: false,
                message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    /**
     * Manually rotate the CSRF token
     */
    async rotateToken(): Promise<string> {
        if (DISABLE_CSRF) {
            return '';
        }
        try {
            const authToken = authService.getToken();
            if (!authToken) {
                throw new Error('Authentication token required to rotate CSRF token');
            }

            const response = await fetch(`${API_URL}/api/auth/csrf-token/rotate`, {
                method: 'POST',
                credentials: 'include', // Enable sending cookies
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                let errorMessage = '';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.detail || errorData.message || response.statusText;
                } catch {
                    errorMessage = response.statusText;
                }
                throw new Error(`Failed to rotate CSRF token: ${response.status} ${errorMessage}`);
            }

            const data: CSRFTokenResponse = await response.json();
            
            if (!data.success || !data.csrf_token) {
                throw new Error('Invalid CSRF token rotation response');
            }

            // Store the new token and calculate expiry
            this.token = data.csrf_token;
            this.tokenExpiry = Date.now() + (data.expires_in * 1000);

            console.debug('CSRF token manually rotated successfully');
            return this.token;
            
        } catch (error) {
            console.error('Error rotating CSRF token:', error);
            throw error;
        }
    }

    /**
     * Handle token rotation from response headers
     */
    handleTokenRotation(response: Response): void {
        if (!this.rotationEnabled) {
            return;
        }

        const rotationFlag = response.headers.get('X-CSRF-Token-Rotation');
        const newToken = response.headers.get('X-CSRF-Token-Rotated');

        if (rotationFlag === 'true' && newToken) {
            this.updateTokenFromResponse(newToken);
            console.debug('CSRF token automatically rotated from response');
        }
    }

    /**
     * Update token from response header
     */
    private updateTokenFromResponse(newToken: string): void {
        if (newToken && newToken.length >= 16) {
            this.token = newToken;
            // Assume same TTL as original token
            this.tokenExpiry = Date.now() + (30 * 60 * 1000); // 30 minutes
            console.debug('CSRF token updated from response header');
        }
    }

    /**
     * Configure response interceptor for automatic token rotation
     */
    configureRotationInterceptor(): void {
        // This method provides the logic for setting up response interceptors
        // It will be called from axios configuration
        console.debug('CSRF rotation interceptor configured');
    }

    /**
     * Check if token rotation is enabled
     */
    isRotationEnabled(): boolean {
        return this.rotationEnabled;
    }

    /**
     * Enable or disable token rotation
     */
    setRotationEnabled(enabled: boolean): void {
        this.rotationEnabled = enabled;
        console.debug(`CSRF token rotation ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get CSRF token from cookie
     */
    getCsrfCookie(): string | null {
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf_token') {
                return decodeURIComponent(value);
            }
        }
        return null;
    }

    /**
     * Clear CSRF cookie
     */
    clearCsrfCookie(): void {
        document.cookie = 'csrf_token=; Max-Age=0; path=/; secure; samesite=strict';
        console.debug('CSRF cookie cleared');
    }

    /**
     * Check if double submit pattern is enabled
     */
    isDoubleSubmitEnabled(): boolean {
        return this.doubleSubmitEnabled;
    }

    /**
     * Get headers for double submit pattern (includes both header and ensures cookie is present)
     */
    async getDoubleSubmitHeaders(): Promise<{ 'X-CSRF-Token': string }> {
        try {
            const headerToken = await this.getToken();
            const cookieToken = this.getCsrfCookie();
            
            if (this.doubleSubmitEnabled && !cookieToken) {
                console.warn('Double submit enabled but no CSRF cookie found. Requesting new tokens...');
                // Re-fetch tokens to ensure cookie is set
                await this.fetchNewToken();
            }
            
            return { 'X-CSRF-Token': headerToken };
        } catch (error) {
            console.error('Failed to get double submit headers:', error);
            throw error;
        }
    }

}

// Export a singleton instance
export const csrfService = new CSRFService(); 