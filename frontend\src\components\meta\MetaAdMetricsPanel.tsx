import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Alert,
  Button,
  Tooltip,
  IconButton,
  Chip,
} from '@mui/material';
import { 
  MetaPage,
  TimeRange,
  TimeRangePreset,
  InstagramAdMetrics,
  MetaCampaignBasic
} from '../../services/types';
import { MetaStoreService, AdMetricsResponse } from '../../services/metaStoreService';
import { TimeRangeFilter } from '../common/TimeRangeFilter';
import { getMetaTimeRangePresets } from '../../services/metaTimeRanges';
import { ErrorDisplay } from '../common/ErrorDisplay';
import { 
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend
} from 'recharts';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import CommentIcon from '@mui/icons-material/Comment';
import ShareIcon from '@mui/icons-material/Share';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import PersonIcon from '@mui/icons-material/Person';
import MessageIcon from '@mui/icons-material/Message';
import LinkIcon from '@mui/icons-material/Link';
import RefreshIcon from '@mui/icons-material/Refresh';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import { isMetaAuthReady } from '../../services/authChecker';
import { MetaAuthRequired } from "./MetaAuthRequired";
import { AdAccountAccessError } from "../../services/dataService";
import { useAuth } from "../../contexts/AuthContext";
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';
import { logger } from '../../utils/logger';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ad-metrics-tabpanel-${index}`}
      aria-labelledby={`ad-metrics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

export interface MetaAdMetricsPanelProps {
  page?: MetaPage;
}

interface MetricsOverview {
  total_spend: number;
  total_impressions: number;
  total_reach: number;
  total_clicks: number;
  total_conversions: number;
  average_cpc: number;
  average_ctr: number;
  average_cost_per_conversion: number;
  average_roi?: number; // Keep optional for potential fallback if needed
  roas: number;
  account_currency?: string; // Checklist Item: Add account_currency to state type
}

interface MetricsState {
  overview: MetricsOverview; // Use the specific type
  daily_metrics: Array<{
    date: string;
    spend: number;
    impressions: number;
    reach?: number;
    clicks: number;
    conversions: number;
    campaign_id?: string;
  }>;
  campaigns: Array<MetaCampaignBasic & { status?: string }>;
  organic?: {
    engagement?: {
      likes?: number;
      comments?: number;
      shares?: number;
      saves?: number;
    };
    profile_activity?: {
      profile_visits?: number;
      messages?: number;
      link_clicks?: number;
    };
  };
}

/**
 * Component for displaying Meta ad metrics
 */
export const MetaAdMetricsPanel: React.FC<MetaAdMetricsPanelProps> = ({
  page
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [adAccountError, setAdAccountError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [includeOrganic, setIncludeOrganic] = useState<boolean>(true);
  const [dataLastUpdated, setDataLastUpdated] = useState<string | null>(null);
  const [isDataFromCache, setIsDataFromCache] = useState<boolean>(false);
  const [isFiltered, setIsFiltered] = useState<boolean>(false);
  const [metrics, setMetrics] = useState<MetricsState>({
    overview: {
      total_spend: 0,
      total_impressions: 0,
      total_reach: 0,
      total_clicks: 0,
      total_conversions: 0,
      average_cpc: 0,
      average_ctr: 0,
      average_cost_per_conversion: 0,
      roas: 0
    },
    daily_metrics: [],
    campaigns: [],
    organic: {
      engagement: {},
      profile_activity: {}
    }
  });
  
  const [timeRange, setTimeRange] = useState<TimeRange | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const authContext = useAuth();
  const user = authContext?.user;
  const storeIdFromAuth = user ? user.id_store : null;
  const { t } = useTranslation();

  // Helper function for currency formatting
  const formatCurrency = (value: number, currencyCode?: string): string => {
    if (currencyCode) {
      try {
        return new Intl.NumberFormat(undefined, { style: 'currency', currency: currencyCode }).format(value);
      } catch (_e) {
        // Fallback if currency code is invalid
        logger.warn(`Invalid currency code: ${currencyCode}, falling back to default.`);
        return `$${value.toFixed(2)}`; 
      }
    } else {
      // Default fallback if no currency code is provided
      return `$${value.toFixed(2)}`;
    }
  };

  // Get available period options based on platform
  const getAdPeriodOptions = (): TimeRangePreset[] => {
    return getMetaTimeRangePresets(page?.platform || 'facebook', 'advertising');
  };

  // Toggle between combined and ad-only metrics for Instagram
  const handleIncludeOrganicToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIncludeOrganic(event.target.checked);
  };

  const fetchAdMetrics = useCallback(async (forceRefresh: boolean = false) => {
    setLoading(true);
    setError(null);
    setAdAccountError(null);
    
    if (!page) {
      setError("Page information is missing.");
      setLoading(false);
      return;
    }
    
    try {
      logger.debug('Fetching metrics for page:', page.id, 'platform:', page.platform, 'forceRefresh:', forceRefresh, 'timeRange:', timeRange);
      
      // Add retries for API call
      let retryCount = 0;
      const maxRetries = 2;
      let adMetricsData: InstagramAdMetrics | AdMetricsResponse | null = null;
      let lastError = null;
      
      while (retryCount <= maxRetries && !adMetricsData) {
        try {
          // If we're retrying, add a delay
          if (retryCount > 0) {
            logger.debug(`Retry attempt ${retryCount}/${maxRetries} for metrics fetch`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay between retries
          }
          
          adMetricsData = await MetaStoreService.getPageAdMetricsWithPlatformBreakdown(
            page.id, 
            String(storeIdFromAuth || ''),
            timeRange === null ? undefined : timeRange,
            page.platform,
            includeOrganic,
            forceRefresh
          );
          
          // If we get here, we've successfully retrieved data
          break;
        } catch (error) {
                      logger.warn(`Attempt ${retryCount + 1} failed:`, error);
          lastError = error;
          retryCount++;
          
          // On last retry, throw the error to be caught by outer catch block
          if (retryCount > maxRetries) {
            throw error;
          }
        }
      }
      
      // If we reach here with adMetricsData null, we've exhausted retries
      if (!adMetricsData) {
        throw lastError || new Error('Failed to fetch metrics after multiple attempts');
      }
      
              logger.debug('Received metrics:', adMetricsData);
      
      // Add detailed debug logging for metrics structure
      if (page.platform === 'instagram' && 'ad_metrics' in adMetricsData) {
        const igMetrics = adMetricsData as InstagramAdMetrics;
        logger.debug('DETAILED METRICS DEBUG - Instagram:', {
          ad_metrics_spend: igMetrics.ad_metrics?.spend,
          ad_metrics_impressions: igMetrics.ad_metrics?.impressions,
          combined_impressions: igMetrics.combined?.total_impressions,
          clicks: igMetrics.ad_metrics?.clicks,
          conversions: igMetrics.ad_metrics?.conversions,
          cpc: igMetrics.ad_metrics?.cpc,
          ctr: igMetrics.ad_metrics?.ctr,
          cost_per_conversion: igMetrics.ad_metrics?.cost_per_conversion,
          roas: igMetrics.ad_metrics?.roi,
          daily_metrics_count: igMetrics.daily_metrics?.length || 0
        });
      } else {
        const fbMetrics = adMetricsData as AdMetricsResponse;
        logger.debug('DETAILED METRICS DEBUG - Facebook:', {
          overview_spend: fbMetrics.overview?.total_spend,
          overview_impressions: fbMetrics.overview?.total_impressions,
          overview_clicks: fbMetrics.overview?.total_clicks,
          overview_conversions: fbMetrics.overview?.total_conversions,
          overview_cpc: fbMetrics.overview?.average_cpc,
          overview_ctr: fbMetrics.overview?.average_ctr,
          overview_cost_per_conversion: fbMetrics.overview?.average_cost_per_conversion,
          overview_roas: fbMetrics.overview?.roas,
          daily_metrics_count: fbMetrics.daily_metrics?.length || 0
        });
      }
      
      // Check if data came from cache (this would be included in response metadata in a real implementation)
      // Since we don't have actual metadata, let's set a placeholder that assumes data is from cache if not forcing refresh
      setIsDataFromCache(!forceRefresh);
      setDataLastUpdated(new Date().toISOString()); // In a real implementation, this would come from the API response
      
      // Check if we're using a custom date range by comparing with default 30-day range
      const defaultTimeRange = {
        since: (() => {
          const date = new Date();
          date.setDate(date.getDate() - 30);
          return date.toISOString().split('T')[0];
        })(),
        until: new Date().toISOString().split('T')[0]
      };
      
      // Determine if we're using a filtered range
      const isUsingCustomRange = 
        timeRange?.since !== defaultTimeRange.since || 
        timeRange?.until !== defaultTimeRange.until;
      
      setIsFiltered(isUsingCustomRange);
      logger.debug('Data is filtered:', isUsingCustomRange, 'Custom timeRange:', timeRange, 'Default:', defaultTimeRange);
            
      // Type guard to check the structure of the response
      if (page.platform === 'instagram' && 'ad_metrics' in adMetricsData) {
        const igMetrics = adMetricsData as InstagramAdMetrics; 
        logger.debug('Processing Instagram metrics:', igMetrics);
        logger.debug('Instagram metrics - daily_metrics count:', igMetrics.daily_metrics?.length || 0);
        logger.debug('Instagram metrics - campaigns count:', igMetrics.campaigns?.length || 0);
        
        const adSpend = igMetrics.ad_metrics?.spend || 0;
        const adImpressions = igMetrics.ad_metrics?.impressions || 0;
        const adReach = igMetrics.ad_metrics?.reach || 0;
        const adClicks = igMetrics.ad_metrics?.clicks || 0;
        const adConversions = igMetrics.ad_metrics?.conversions || 0;

        // Check if organic metrics are substantially present
        const hasMeaningfulOrganicData = 
          (igMetrics.organic_metrics?.organic_impressions || 0) > 0 ||
          (igMetrics.organic_metrics?.organic_reach || 0) > 0 ||
          (igMetrics.organic_metrics?.engagement?.likes || 0) > 0;

        setMetrics({
          overview: {
            total_spend: adSpend,
            total_impressions: hasMeaningfulOrganicData ? (igMetrics.combined?.total_impressions || adImpressions) : adImpressions, 
            total_reach: hasMeaningfulOrganicData ? (igMetrics.combined?.total_reach || adReach) : adReach, 
            total_clicks: adClicks,
            total_conversions: adConversions,
            average_cpc: igMetrics.ad_metrics?.cpc || 0,
            average_ctr: igMetrics.ad_metrics?.ctr || 0,
            average_cost_per_conversion: igMetrics.ad_metrics?.cost_per_conversion || 0,
            roas: igMetrics.ad_metrics?.roi || 0, 
            account_currency: igMetrics.ad_metrics?.account_currency
          },
          daily_metrics: igMetrics.daily_metrics || [],
          campaigns: igMetrics.campaigns || [],
          organic: hasMeaningfulOrganicData ? (igMetrics.organic_metrics ? {
             engagement: igMetrics.organic_metrics.engagement || {},
             profile_activity: igMetrics.organic_metrics.profile_activity || {}
          } : undefined) : undefined // Set organic to undefined if no meaningful data
        });
      } else {
        const metricsData = adMetricsData as AdMetricsResponse;
        logger.debug('Processing Facebook metrics:', metricsData);
        logger.debug('Facebook metrics - daily_metrics count:', metricsData.daily_metrics?.length || 0);
        logger.debug('Facebook metrics - campaigns count:', metricsData.campaigns?.length || 0);
        logger.debug('Facebook metrics - campaigns data:', metricsData.campaigns);
        
        setMetrics({
          overview: {
            total_spend: metricsData.overview?.total_spend || 0,
            total_impressions: metricsData.overview?.total_impressions || 0,
            total_reach: metricsData.overview?.total_reach || 0,
            total_clicks: metricsData.overview?.total_clicks || 0,
            total_conversions: metricsData.overview?.total_conversions || 0,
            average_cpc: metricsData.overview?.average_cpc ?? 0,
            average_ctr: metricsData.overview?.average_ctr ?? 0,
            average_cost_per_conversion: metricsData.overview?.average_cost_per_conversion ?? 0,
            roas: metricsData.overview?.roas || 0,
            account_currency: metricsData.overview?.account_currency
          },
          daily_metrics: metricsData.daily_metrics || [],
          campaigns: metricsData.campaigns || [],
          organic: metricsData.organic || { engagement: {}, profile_activity: {} } 
        });
      }
    } catch (error) {
      logger.error('Error fetching ad metrics:', error);
      if (error instanceof AdAccountAccessError) {
        if (error.message.includes('Could not find a linked Facebook Page')) {
          setError('Could not find linked Facebook Page for this Instagram Account. Please check Meta Business Suite settings.');
        } else if (error.message.includes('Permission denied')) {
          setError('Permission denied fetching accounts from Meta. Please reconnect the account and grant necessary permissions.');
        } else if (error.message.includes('Facebook API structure changed')) {
          setError('Failed to fetch data due to Meta API changes. Please contact support.');
        } else {
           // Use the message from AdAccountAccessError directly if it's specific
           setError(`Failed to load Ad Metrics: ${error.message}`); 
        }
      } else if (error instanceof Error) {
        setError(`Failed to load Ad Metrics: ${error.message}`);
      } else {
        setError('An unknown error occurred while fetching Ad Metrics.');
      }
    } finally {
      setLoading(false);
    }
  }, [page, timeRange, includeOrganic, storeIdFromAuth]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    const checkAuthAndFetch = async () => {
      setLoading(true);
      setError(null); // Clear previous errors
      try {
        const authReady = await isMetaAuthReady();
        setIsAuthenticated(authReady);
        if (authReady && page) {
          logger.debug('Auth is ready, fetching metrics with timeRange:', timeRange);
          await fetchAdMetrics();
        } else if (!authReady) {
          logger.debug('Meta auth is not ready');
          setError('Meta authentication is not ready. Please connect.');
          setLoading(false);
        }
      } catch (err) {
        logger.error('Error checking auth status:', err);
        setError('Failed to authenticate with Meta API.');
        setLoading(false);
      }
    };

    checkAuthAndFetch();
    // Original dependencies: fetchAdMetrics, page, timeRange
    // Corrected dependencies: Add fetchAdMetrics back as per lint rule
  }, [page, timeRange, fetchAdMetrics]);

  // Handle time range changes
  const handleTimeRangeChange = (newTimeRange: TimeRange | null) => {
    logger.debug('Time range changed:', newTimeRange);
    setTimeRange(newTimeRange);
    setIsFiltered(!!newTimeRange); // Set to true if newTimeRange is not null
    // Fetch metrics with the new time range
    fetchAdMetrics(false);
  };

  // Update the handleTabChange to log metrics data for debugging
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    logger.debug(`Tab changed to ${newValue}`);
    // Log relevant data based on the tab selected
    if (newValue === 0) {
      logger.debug('Campaigns tab data:', metrics.campaigns);
    } else if (newValue === 1) {
              logger.debug('Performance tab data:', metrics.daily_metrics);
    } else if (newValue === 2) {
              logger.debug('ROI Analysis tab data:', {
        campaigns: metrics.campaigns,
        hasCampaigns: metrics.campaigns && metrics.campaigns.length > 0
      });
    }
    setTabValue(newValue);
  };

  // Add a helper to check if we're using a custom date range
  useEffect(() => {
    // Check if we're using a custom date range by comparing with default 30-day range
    const defaultTimeRange = {
      since: (() => {
        const date = new Date();
        date.setDate(date.getDate() - 30);
        return date.toISOString().split('T')[0];
      })(),
      until: new Date().toISOString().split('T')[0]
    };
    
    // Determine if we're using a filtered range
    const isUsingCustomRange = 
      timeRange?.since !== defaultTimeRange.since || 
      timeRange?.until !== defaultTimeRange.until;
    
    setIsFiltered(isUsingCustomRange);
    logger.debug('Data filtering status updated:', {
      isFiltered: isUsingCustomRange,
      timeRange,
      defaultTimeRange
    });
  }, [timeRange]);

  // Render the account summary section
  const renderAccountSummary = () => {
    // Use the main `metrics.overview` state which is populated correctly based on platform
    const overviewData = metrics.overview;
    
    // Add detailed debug logging for Account Summary values
    logger.debug('ACCOUNT SUMMARY DEBUG - Values being displayed:', {
      spend: overviewData.total_spend,
      impressions: overviewData.total_impressions,
      clicks: overviewData.total_clicks,
      conversions: overviewData.total_conversions,
      ctr: overviewData.average_ctr,
      cpc: overviewData.average_cpc,
      cost_per_conversion: overviewData.average_cost_per_conversion,
      roas: overviewData.roas,
      isFiltered: isFiltered,
      timeRange
    });
    
    // Format date range for display
    const formatDateRange = () => {
      if (!timeRange || !timeRange.since || !timeRange.until) return "";
      
      // Format dates in a more readable format
      const sinceDate = new Date(timeRange.since);
      const untilDate = new Date(timeRange.until);
      
      const options: Intl.DateTimeFormatOptions = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      };
      
      return `${sinceDate.toLocaleDateString(undefined, options)} - ${untilDate.toLocaleDateString(undefined, options)}`;
    };
      
    return (
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6" gutterBottom>
            {t('metaDashboard.adMetrics.accountSummary')}
          </Typography>
          {isFiltered && (
            <Chip 
              icon={<FilterAltIcon />} 
              label={`${t('metaDashboard.adMetrics.dateRange')}: ${formatDateRange()}`} 
              size="small" 
              color="primary" 
              variant="outlined"
              sx={{ ml: 2 }}
            />
          )}
        </Box>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.totalSpend')}</Typography>
              <Typography variant="h5">{formatCurrency(overviewData.total_spend, overviewData.account_currency)}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.impressions')}</Typography>
              <Typography variant="h5">{(overviewData.total_impressions).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.clicks')}</Typography>
              <Typography variant="h5">{(overviewData.total_clicks).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.conversions')}</Typography>
              <Typography variant="h5">{(overviewData.total_conversions).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.ctr')}</Typography>
              <Typography variant="h5">{(overviewData.average_ctr).toFixed(2)}%</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.cpc')}</Typography>
              <Typography variant="h5">{formatCurrency(overviewData.average_cpc, overviewData.account_currency)}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.costPerConversion')}</Typography>
              <Typography variant="h5">{formatCurrency(overviewData.average_cost_per_conversion, overviewData.account_currency)}</Typography>
            </Paper>
          </Grid>
           <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
              <Typography variant="subtitle2" color="text.secondary">{t('metaDashboard.adMetrics.roas')}</Typography> 
              <Typography variant="h5">{(overviewData.roas).toFixed(2)}x</Typography>
            </Paper>
          </Grid>
        </Grid>
        {isFiltered && (
          <Box mt={2} mb={1} display="flex" alignItems="center">
            <Chip
              label={`${t('metaDashboard.adMetrics.dateRange')}: ${formatDateRange()}`}
              color="primary"
              variant="outlined"
              size="small"
              sx={{ mr: 1 }}
            />
            <Typography variant="caption" color="text.secondary">
              * {t('metaDashboard.adMetrics.showingMetrics')}
            </Typography>
          </Box>
        )}
      </Box>
    );
  };

  // Render the engagement section (only when includeOrganic is true)
  const renderEngagement = () => {
    if (!includeOrganic) return null;
    
    // Use the main metrics state
    const organicData = metrics.organic?.engagement;
          
    if (!organicData || Object.keys(organicData).length === 0) return null; // Check if object is empty
    
    return (
      <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Engagement
          </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <ThumbUpIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Likes</Typography>
              <Typography variant="h5">{(organicData.likes ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <CommentIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Comments</Typography>
              <Typography variant="h5">{(organicData.comments ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <ShareIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Shares</Typography>
              <Typography variant="h5">{(organicData.shares ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <BookmarkIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Saves</Typography>
              <Typography variant="h5">{(organicData.saves ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render the profile activity section (only when includeOrganic is true)
  const renderProfileActivity = () => {
    if (!includeOrganic) return null;
    if (page?.platform !== 'facebook') return null; // Only show for Facebook pages
    
    // Use the main metrics state
    const profileData = metrics.organic?.profile_activity;
          
    if (!profileData || Object.keys(profileData).length === 0) return null; // Check if object is empty
    
    return (
      <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Profile Activity
          </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <PersonIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Profile Visits</Typography>
              <Typography variant="h5">{(profileData.profile_visits ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <MessageIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Messages</Typography>
              <Typography variant="h5">{(profileData.messages ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}> {/* Adjusted grid sizing */}
            <Paper sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <LinkIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="subtitle2" color="text.secondary">Link Clicks</Typography>
              <Typography variant="h5">{(profileData.link_clicks ?? 0).toLocaleString()}</Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  if (!isAuthenticated) {
    // Pass props directly if MetaAuthRequiredProps is not exported
    return <MetaAuthRequired onLoginSuccess={() => fetchAdMetrics()} />;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    // Pass props directly if ErrorDisplayProps is not exported
    return <ErrorDisplay error={error} /* retry={fetchAdMetrics} */ />; 
  }

  // Place this before the return statement, after metrics is defined and available
  if (metrics && metrics.campaigns) {
    logger.debug('MetaAdMetricsPanel - metrics.campaigns:', JSON.stringify(metrics.campaigns));
  }

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5">{t('metaDashboard.adMetrics.panelTitle')}</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TimeRangeFilter 
            value={timeRange} 
            onChange={handleTimeRangeChange}
            presets={getAdPeriodOptions()}
            platformType={page?.platform} 
          />
          <FormControlLabel
            control={
              <Switch 
                checked={includeOrganic}
                onChange={handleIncludeOrganicToggle}
                color="primary"
              />
            }
            label={t('metaDashboard.adMetrics.includeOrganicData')}
            sx={{ ml: 2 }}
          />
          <Tooltip title="Refresh Data">
            <span>
              <IconButton
                onClick={() => fetchAdMetrics(true)}
                disabled={loading}
                sx={{ ml: 1 }}
                color="primary"
              >
                <RefreshIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>
      </Box>

      {adAccountError && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {adAccountError}
        </Alert>
      )}

      {isDataFromCache && dataLastUpdated && (
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Data from cache: {new Date(dataLastUpdated).toLocaleString()}
          </Typography>
          <Button size="small" onClick={() => fetchAdMetrics(true)}>{t('metaDashboard.adMetrics.refreshNow')}</Button>
        </Box>
      )}

      {renderAccountSummary()}

      {includeOrganic && renderEngagement()}
      
      {includeOrganic && page?.platform === 'facebook' && renderProfileActivity()}

      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="ad metrics tabs">
            <Tab label={t('metaDashboard.adMetrics.campaignsTab')} id="ad-metrics-tab-0" aria-controls="ad-metrics-tabpanel-0" />
            <Tab label={t('metaDashboard.adMetrics.performanceTab')} id="ad-metrics-tab-1" aria-controls="ad-metrics-tabpanel-1" />
            <Tab label={t('metaDashboard.adMetrics.roiAnalysisTab')} id="ad-metrics-tab-2" aria-controls="ad-metrics-tabpanel-2" />
          </Tabs>
        </Box>
        
        {/* Campaigns Tab */}
        <TabPanel value={tabValue} index={0}>
          <TableContainer component={Paper}>
            <Table aria-label="campaign table">
              <TableHead>
                <TableRow>
                  <TableCell>{t('metaDashboard.adMetrics.campaign')}</TableCell>
                  <TableCell>{t('metaDashboard.adMetrics.status')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.spend')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.impressions')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.clicks')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.ctr')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.conversions')}</TableCell>
                  <TableCell align="right">{t('metaDashboard.adMetrics.roas')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {metrics.campaigns.map((campaign) => {
                  // Calculate metrics dynamically when not available directly
                  const clicks = campaign.clicks || 0;
                  const impressions = campaign.impressions || 0;
                  const conversions = campaign.conversions || 0;
                  const spend = campaign.spend || 0;
                  const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
                  // Assuming $50 conversion value for ROAS calculation
                  const campaignRoas = spend > 0 ? (conversions * 50) / spend : 0;
                  
                  return (
                    <TableRow key={campaign.id}>
                      <TableCell component="th" scope="row">
                        {campaign.name}
                      </TableCell>
                      <TableCell>
                        {campaign.status || 'UNKNOWN'}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(spend, metrics.overview.account_currency)}
                      </TableCell>
                      <TableCell align="right">{impressions.toLocaleString()}</TableCell>
                      <TableCell align="right">{clicks.toLocaleString()}</TableCell>
                      <TableCell align="right">{ctr.toFixed(2)}%</TableCell>
                      <TableCell align="right">{conversions.toLocaleString()}</TableCell>
                      <TableCell align="right">{campaignRoas.toFixed(2)}x</TableCell>
                    </TableRow>
                  );
                })}
                {metrics.campaigns.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      {isFiltered 
                        ? t('metaDashboard.adMetrics.noCampaignData')
                        : t('metaDashboard.adMetrics.noCampaignData')
                      }
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
        
        {/* Performance Tab */}
        <TabPanel value={tabValue} index={1}>
          {metrics.daily_metrics && metrics.daily_metrics.length > 0 ? (
            <Box sx={{ height: 400, width: '100%' }}>
              <ChartContainer width="100%" height="100%" minHeight={300}>
                <LineChart
                  data={metrics.daily_metrics}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis 
                    yAxisId="left" 
                    tickFormatter={(value) => `$${value}`} 
                    label={{ value: 'Spend ($)', angle: -90, position: 'insideLeft' }}
                  />
                  <YAxis 
                    yAxisId="right" 
                    orientation="right" 
                    label={{ value: 'Count', angle: 90, position: 'insideRight' }}
                  />
                  <RechartsTooltip formatter={(value, name) => {
                    if (name === 'spend') {
                      return [`$${Number(value).toFixed(2)}`, 'Spend'];
                    } else if (name === 'impressions') {
                      return [Number(value).toLocaleString(), 'Impressions'];
                    } else if (name === 'clicks') {
                      return [Number(value).toLocaleString(), 'Clicks'];
                    }
                    return [value, typeof name === 'string' ? name.charAt(0).toUpperCase() + name.slice(1) : name];
                  }} />
                  <Legend />
                  <Line 
                    yAxisId="left" 
                    type="monotone" 
                    dataKey="spend" 
                    stroke="#8884d8" 
                    activeDot={{ r: 8 }} 
                    name="Spend"
                  />
                  <Line 
                    yAxisId="right" 
                    type="monotone" 
                    dataKey="impressions" 
                    stroke="#82ca9d" 
                    name="Impressions"
                  />
                  <Line 
                    yAxisId="right" 
                    type="monotone" 
                    dataKey="clicks" 
                    stroke="#ffc658" 
                    name="Clicks"
                  />
                </LineChart>
              </ChartContainer>
            </Box>
          ) : (
            <Alert severity="info">
              {isFiltered 
                ? t('metaDashboard.adMetrics.noPerformanceData')
                : t('metaDashboard.adMetrics.noPerformanceData')
              }
            </Alert>
          )}
        </TabPanel>
        
        {/* ROI Analysis Tab */}
        <TabPanel value={tabValue} index={2}>
          {metrics.campaigns && metrics.campaigns.length > 0 ? (
            <Box sx={{ height: 400, width: '100%' }}>
              <ChartContainer width="100%" height="100%" minHeight={300}>
                <BarChart
                  data={metrics.campaigns}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  {/* Left Y-axis for Spend values (in dollars) */}
                  <YAxis 
                    yAxisId="left" 
                    orientation="left" 
                    tickFormatter={(value) => `$${value}`} 
                    label={{ value: 'Spend ($)', angle: -90, position: 'insideLeft' }}
                  />
                  {/* Right Y-axis specifically for ROAS (ratio) */}
                  <YAxis 
                    yAxisId="right" 
                    orientation="right" 
                    domain={[0, 'dataMax + 1']} 
                    tickFormatter={(value) => `${value.toFixed(1)}x`}
                    label={{ value: 'ROAS', angle: 90, position: 'insideRight' }} 
                  />
                  <RechartsTooltip formatter={(value, name, entry) => {
                    if (name === 'spend') {
                      return [`$${Number(value).toFixed(2)}`, 'Spend'];
                    } else if (name === 'ROAS' || name === 'roas') {
                      // Extract campaign data from entry.payload
                      const campaign = entry.payload;
                      const conversions = campaign.conversions || 0;
                      const spend = campaign.spend || 0;
                      
                      // Create enhanced tooltip with conversion and spend details
                      return [
                        `${Number(value).toFixed(2)}x (${conversions} conv. on $${spend.toFixed(2)})`, 
                        'ROAS'
                      ];
                    }
                    return [value, typeof name === 'string' ? name.charAt(0).toUpperCase() + name.slice(1) : name];
                  }} />
                  <Legend />
                  <Bar 
                    yAxisId="left"
                    dataKey="spend" 
                    fill="#8884d8" 
                    name="Spend"
                  />
                  <Bar 
                    yAxisId="right"
                    dataKey={(campaign) => {
                      const conversions = campaign.conversions || 0;
                      const spend = campaign.spend || 0;
                      return spend > 0 ? (conversions * 50) / spend : 0;
                    }} 
                    fill="#82ca9d" 
                    name="ROAS"
                  />
                </BarChart>
              </ChartContainer>
            </Box>
          ) : (
            <Alert severity="info">
              {isFiltered 
                ? t('metaDashboard.adMetrics.noRoiData')
                : t('metaDashboard.adMetrics.noRoiData')
              }
            </Alert>
          )}
        </TabPanel>
      </Box>
    </Box>
  );
}; 