/**
 * Type guard utilities for Meta API data types
 * These functions help safely extract numeric values from complex union types
 */

/**
 * Type guard to check if a value is a number
 */
export function isNumericValue(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Safely extracts a numeric value from a MetricInsight.value union type
 * @param value The complex union type value from MetricInsight
 * @returns A number or 0 if extraction fails
 */
export function extractNumericValue(
  value: number | Record<string, number> | Array<number | Record<string, number>> | Record<string, Array<number | Record<string, number>>>
): number {
  // If it's already a number, return it
  if (isNumericValue(value)) {
    return value;
  }

  // If it's an array, try to get the first numeric value
  if (Array.isArray(value)) {
    for (const item of value) {
      if (isNumericValue(item)) {
        return item;
      }
      // If array item is an object, try to extract from it
      if (typeof item === 'object' && item !== null) {
        const extracted = extractFromObject(item);
        if (extracted !== 0) return extracted;
      }
    }
    return 0;
  }

  // If it's an object, try to extract a numeric value
  if (typeof value === 'object' && value !== null) {
    return extractFromObject(value);
  }

  // Fallback to 0
  return 0;
}

/**
 * Helper function to extract numeric values from object types
 */
function extractFromObject(obj: Record<string, unknown>): number {
  // Try common keys that might contain the main value
  const commonKeys = ['value', 'total', 'count', 'sum'];
  
  for (const key of commonKeys) {
    if (key in obj && isNumericValue(obj[key])) {
      return obj[key] as number;
    }
  }

  // Try to get the first numeric value from any key
  for (const value of Object.values(obj)) {
    if (isNumericValue(value)) {
      return value;
    }
    // If the value is an array, recursively extract
    if (Array.isArray(value)) {
      for (const item of value) {
        if (isNumericValue(item)) {
          return item;
        }
      }
    }
  }

  return 0;
}

/**
 * Safely sums up numeric values from an array of MetricInsight values
 * @param values Array of complex union type values
 * @returns Sum of all extractable numeric values
 */
export function safeSumValues(
  values: Array<number | Record<string, number> | Array<number | Record<string, number>> | Record<string, Array<number | Record<string, number>>>>
): number {
  return values.reduce((sum: number, value: number | Record<string, number> | Array<number | Record<string, number>> | Record<string, Array<number | Record<string, number>>>) => {
    return sum + extractNumericValue(value);
  }, 0);
}

/**
 * Safely checks if a MetricInsight value is greater than a threshold
 * @param value The complex union type value
 * @param threshold The number to compare against
 * @returns Boolean indicating if the extracted numeric value is greater than threshold
 */
export function isValueGreaterThan(
  value: number | Record<string, number> | Array<number | Record<string, number>> | Record<string, Array<number | Record<string, number>>>,
  threshold: number
): boolean {
  return extractNumericValue(value) > threshold;
} 