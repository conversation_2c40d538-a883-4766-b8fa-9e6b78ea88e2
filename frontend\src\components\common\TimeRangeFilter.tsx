import * as React from 'react';
import { useState, useEffect } from 'react';
import { 
  Box, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField, 
  Button, 
  Stack,
  SelectChangeEvent,
  Typography,
  Alert,
  Tooltip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { TimeRange, TimeRangePreset } from '../../services/types';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';

interface TimeRangeFilterProps {
  value: TimeRange | null;
  onChange: (range: TimeRange | null) => void;
  platformType?: 'facebook' | 'instagram';
  presets?: Array<TimeRangePreset | 'lifetime'>;
  allowCustom?: boolean;
}

/**
 * A reusable time range filter component
 */
export const TimeRangeFilter: React.FC<TimeRangeFilterProps> = ({
  value,
  onChange,
  platformType,
  presets = platformType === 'instagram' 
    ? ['lifetime', 'week', 'month', 'custom'] 
    : ['7d', '30d', '90d', '180d', '1y', 'lifetime', 'custom'],
  allowCustom = true,
}) => {
  const { t } = useTranslation();
  
  // Helper function to handle missing translations by providing a default
  const tWithDefault = (key: string, defaultValue: string): string => {
    const translation = t(key, defaultValue);
    // Check if the translation is the same as the key (missing translation)
    // But only if the key contains a dot (namespace)
    if (translation === key && key.includes('.')) {
      return defaultValue;
    }
    return translation;
  };

  // Initialize selectedPreset based on the current value
  const [selectedPreset, setSelectedPreset] = useState<TimeRangePreset | 'lifetime'>(() => {
    // Handle null value for lifetime
    if (value === null) {
      return 'lifetime';
    }
    // If the value already has a preset, use it
    if (value.preset) {
      return value.preset;
    }

    // Otherwise, determine the preset based on the date range
    const now = new Date();
    const valueDate = new Date(value.since);
    const diffDays = Math.floor((now.getTime() - valueDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Check if we're using Instagram-style presets
    if (presets.includes('week') || presets.includes('month')) {
      if (diffDays <= 7) return 'week';
      if (diffDays <= 30) return 'month';
      return 'month'; // Default to month for Instagram
    } else {
      if (diffDays <= 7) return '7d';
      if (diffDays <= 30) return '30d';
      if (diffDays <= 90) return '90d';
      if (diffDays <= 180) return '180d';
      if (diffDays <= 365) return '1y';
      // Check if it matches a known preset or default to custom
      // Note: We don't have a way to map back to 'lifetime' based on dates alone
      return 'custom'; 
    }
  });

  const [customRange, setCustomRange] = useState<TimeRange>(value !== null ? {
    since: value.since,
    until: value.until
  } : {
    // Default custom range if initial value is null
    since: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    until: new Date().toISOString().split('T')[0]
  });
  const [showCustom, setShowCustom] = useState(selectedPreset === 'custom');
  const [dateRangeError, setDateRangeError] = useState<string | null>(null);

  // Check if the date range exceeds 30 days
  const validateDateRange = (since: string, until: string): boolean => {
    const sinceDate = new Date(since);
    const untilDate = new Date(until);
    const diffTime = Math.abs(untilDate.getTime() - sinceDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 30) {
      setDateRangeError(`Date range exceeds 30 days (${diffDays} days selected). Instagram API limits queries to 30 days.`);
      return false;
    }
    
    setDateRangeError(null);
    return true;
  };

  // Calculate the current range days
  useEffect(() => {
    if (showCustom) {
      validateDateRange(customRange.since, customRange.until);
    }
  }, [customRange.since, customRange.until, showCustom]);

  // Function to calculate date range based on preset
  // Ensure preset is TimeRangePreset before calling
  const getDateForPreset = (preset: TimeRangePreset): TimeRange => {
    const now = new Date();
    let since = '';
    const until = now.toISOString().split('T')[0];

    // Special handling for Instagram: Limit range if necessary
    // This part might need adjustment if presets like '90d' are used for Instagram elsewhere,
    // but currently, the default presets for Instagram are limited.
    if (platformType === 'instagram') {
      const maxDays = 30; // Instagram API limit
      let daysToSubtract = 0;
      
      switch(preset) {
        case 'week': daysToSubtract = 7; break; // Assuming 'week' means last 7 days
        case 'month': daysToSubtract = 30; break; // Assuming 'month' means last 30 days
        // Add cases for '7d', '28d', '30d' if they can be passed for Instagram
        case '7d': daysToSubtract = 7; break;
        case '28d': daysToSubtract = 28; break;
        case '30d': daysToSubtract = 30; break;
        // Presets like '90d', '180d', '1y' are likely filtered out for Instagram,
        // but if they *could* be passed, they should be capped at 30 days.
        // Default case handles this implicitly by calculating based on 30 days below.
      }

      if (daysToSubtract > 0 && daysToSubtract <= maxDays) {
          const date = new Date(now);
          date.setDate(date.getDate() - daysToSubtract);
          since = date.toISOString().split('T')[0];
      } else {
          // Default to max 30 days for Instagram if preset implies longer or is unhandled
          const date = new Date(now);
          date.setDate(date.getDate() - maxDays);
          since = date.toISOString().split('T')[0];
      }
    } else {
      // Logic for non-Instagram platforms (or default if platformType is undefined)
      switch (preset) {
        case '7d': {
          const last7Days = new Date(now);
          last7Days.setDate(last7Days.getDate() - 7);
          since = last7Days.toISOString().split('T')[0];
          break;
        }
        case '28d': {
          const last28Days = new Date(now);
          last28Days.setDate(last28Days.getDate() - 28);
          since = last28Days.toISOString().split('T')[0];
          break;
        }
        case '30d': {
          const last30Days = new Date(now);
          last30Days.setDate(last30Days.getDate() - 30);
          since = last30Days.toISOString().split('T')[0];
          break;
        }
        case '90d': {
          const last90Days = new Date(now);
          last90Days.setDate(last90Days.getDate() - 90);
          since = last90Days.toISOString().split('T')[0];
          break;
        }
        case '180d': {
          const last180Days = new Date(now);
          last180Days.setDate(last180Days.getDate() - 180);
          since = last180Days.toISOString().split('T')[0];
          break;
        }
        case '1y': {
          const last1Year = new Date(now);
          last1Year.setFullYear(last1Year.getFullYear() - 1);
          since = last1Year.toISOString().split('T')[0];
          break;
        }
        // Removed 'custom' and 'lifetime' cases as per plan
        default: {
          // Check for exhaustive preset match (optional, add if needed for strictness)
          // const _exhaustiveCheck: never = preset; 
          // Default to last 30 days if somehow an invalid preset is passed
          logger.warn(`Invalid preset "${preset}" provided to getDateForPreset.`);
          const defaultRange = new Date(now);
          defaultRange.setDate(defaultRange.getDate() - 30);
          since = defaultRange.toISOString().split('T')[0];
        }
      }
    } // End platformType check

    // Always return a TimeRange object as per the updated signature
    return { since, until, preset };
  };

  // Handle preset change
  const handlePresetChange = (event: SelectChangeEvent<string>) => {
    const preset = event.target.value as TimeRangePreset | 'lifetime';
    
    // Basic validation: Use TimeRangePreset | 'lifetime' instead of any
    if (![...presets, 'lifetime', 'custom'].includes(preset)) { 
      logger.warn(`Invalid preset value selected: ${preset}`);
      return;
    }

    setSelectedPreset(preset);
    
    if (preset === 'custom') {
      setShowCustom(true);
      // Don't call onChange yet, wait for Apply button
    } else if (preset === 'lifetime') {
      setShowCustom(false);
      onChange(null); // Pass null for lifetime
    } else {
      setShowCustom(false);
      const newRange = getDateForPreset(preset as TimeRangePreset);
      if (newRange) {
         onChange(newRange);
      }
    }
  };

  // Handle custom range change
  const handleCustomRangeChange = (field: 'since' | 'until', value: string) => {
    setCustomRange((prev: TimeRange) => ({
      ...prev,
      [field]: value,
      preset: 'custom'
    }));
  };

  // Apply custom range
  const applyCustomRange = () => {
    if (validateDateRange(customRange.since, customRange.until)) {
      onChange({...customRange, preset: 'custom'});
    }
  };

  // Adjust custom range to fit within 30 days
  const adjustCustomRange = () => {
    const untilDate = new Date(customRange.until);
    
    // Calculate adjusted since date (30 days before until)
    const adjustedSince = new Date(untilDate);
    adjustedSince.setDate(adjustedSince.getDate() - 30);
    
    const newSince = adjustedSince.toISOString().split('T')[0];
    setCustomRange((prev: TimeRange) => ({
      ...prev,
      since: newSince,
      preset: 'custom'
    }));
    
    // Apply changes
    onChange({
      since: newSince,
      until: customRange.until,
      preset: 'custom'
    });
    
    setDateRangeError(null);
  };

  // Filter available presets
  const availablePresets = presets.filter(preset => 
    preset === 'custom' ? allowCustom : true
  );

  return (
    <Box sx={{ minWidth: 200 }}>
      <Stack direction="row" spacing={1} alignItems="center">
        <FormControl fullWidth size="small">
          <InputLabel id="time-range-select-label">{tWithDefault('dashboard.common.timeRange', 'Time Range')}</InputLabel>
          <Select
            labelId="time-range-select-label"
            id="time-range-select"
            value={selectedPreset}
            label={tWithDefault('dashboard.common.timeRange', 'Time Range')}
            onChange={handlePresetChange}
            MenuProps={{
              PaperProps: {
                style: {
                  marginTop: 0
                }
              }
            }}
          >
            {availablePresets
              .filter(preset => preset === 'custom' ? allowCustom : true)
              .map(preset => {
                let label = '';
                switch (preset) {
                  case 'week':
                    label = tWithDefault('dashboard.common.lastWeek', 'Last week');
                    break;
                  case 'month':
                    label = tWithDefault('dashboard.common.lastMonth', 'Last month');
                    break;
                  case 'custom':
                    label = tWithDefault('dashboard.common.customRange', 'Custom Range');
                    break;
                  case '7d':
                    label = tWithDefault('dashboard.common.last7days', 'Last 7 days');
                    break;
                  case '28d':
                    label = tWithDefault('dashboard.common.last28days', 'Last 28 days');
                    break;
                  case '30d':
                    label = tWithDefault('dashboard.common.last30days', 'Last 30 days');
                    break;
                  case '90d':
                    label = tWithDefault('dashboard.common.last90days', 'Last 90 days');
                    break;
                  case '180d':
                    label = tWithDefault('dashboard.common.last180days', 'Last 180 days');
                    break;
                  case '1y':
                    label = tWithDefault('dashboard.common.lastYear', 'Last year');
                    break;
                  case 'lifetime':
                    label = tWithDefault('dashboard.common.lifetime', 'Lifetime');
                    break;
                  default:
                    label = String(preset);
                }
                
                return (
                  <MenuItem 
                    key={preset} 
                    value={preset}
                    sx={{ py: 1 }}
                  >
                    {label}
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
        
        {platformType === 'instagram' && (
          <Tooltip title={tWithDefault('dashboard.common.instagramAPILimit', 'Instagram API limits queries to 30 days maximum')}>
            <InfoIcon color="info" fontSize="small" />
          </Tooltip>
        )}
      </Stack>

      {showCustom && (
        <>
          <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
            <TextField
              label={tWithDefault('dashboard.common.from', 'From')}
              type="date"
              size="small"
              value={customRange.since}
              onChange={(e) => handleCustomRangeChange('since', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              label={tWithDefault('dashboard.common.to', 'To')}
              type="date"
              size="small"
              value={customRange.until}
              onChange={(e) => handleCustomRangeChange('until', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
            <Button 
              variant="contained" 
              size="small" 
              onClick={applyCustomRange}
              disabled={!!dateRangeError && platformType === 'instagram'}
            >
              {tWithDefault('dashboard.common.apply', 'Apply')}
            </Button>
            
            {dateRangeError && platformType === 'instagram' && (
              <Button 
                variant="outlined" 
                color="warning" 
                size="small" 
                onClick={adjustCustomRange}
              >
                {tWithDefault('dashboard.common.adjustTo30days', 'Adjust to 30 days')}
              </Button>
            )}
          </Stack>
          
          {dateRangeError && platformType === 'instagram' && (
            <Alert severity="warning" sx={{ mt: 1 }}>
              {dateRangeError}
            </Alert>
          )}
          
          {platformType === 'instagram' && !dateRangeError && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              {tWithDefault('dashboard.common.instagramAPIRequirement', '* Instagram API requires date ranges to be 30 days or less')}
            </Typography>
          )}
        </>
      )}
    </Box>
  );
}; 