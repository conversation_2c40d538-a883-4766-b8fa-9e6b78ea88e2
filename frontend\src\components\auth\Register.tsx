import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Link,
  Collapse,
} from '@mui/material';
import { CheckCircle, Cancel } from '@mui/icons-material';
import AuthLayout from './AuthLayout';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';
import { formService, FormSubmissionResult } from '../../services/formService';
import { useSecureForm } from '../../hooks/useSecureForm';

// Define error interface for better type safety
interface ApiError {
  response?: {
    data?: {
      detail?: string;
      message?: string;
    };
    status?: number;
  };
  message?: string;
}

const Register = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isSubmitting, isRecovering, lastError, clearError } = useSecureForm();
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    store_name: '',
    password: '',
    confirm_password: ''
  });
  const [verificationCode, setVerificationCode] = useState('');
  const [showVerification, setShowVerification] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  
  // Password validation states
  const [lengthValid, setLengthValid] = useState(false);
  const [uppercaseValid, setUppercaseValid] = useState(false);
  const [numberValid, setNumberValid] = useState(false);
  const [symbolValid, setSymbolValid] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(false);

  const validatePassword = (password: string) => {
    const errors: string[] = [];
    if (password.length < 8) {
      errors.push(t('errorPasswordLength'));
    }
    if (!password.match(/[A-Z]/)) {
      errors.push(t('errorPasswordUppercase'));
    }
    if (!password.match(/[0-9]/)) {
      errors.push(t('errorPasswordNumber'));
    }
    if (!password.match(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/)) {
      errors.push(t('errorPasswordSymbol'));
    }
    return errors;
  };
  
  // Update validation states as user types
  const validatePasswordRequirements = (password: string) => {
    setLengthValid(password.length >= 8);
    setUppercaseValid(/[A-Z]/.test(password));
    setNumberValid(/[0-9]/.test(password));
    setSymbolValid(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password));
  };
  
  // Validate password as user types
  React.useEffect(() => {
    validatePasswordRequirements(formData.password);
  }, [formData.password]);
  
  // Check if passwords match
  React.useEffect(() => {
    setPasswordsMatch(formData.password === formData.confirm_password && formData.password !== '');
  }, [formData.password, formData.confirm_password]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    clearError();

    try {
      // Validate password requirements
      const passwordErrors = validatePassword(formData.password);
      if (passwordErrors.length > 0) {
        throw new Error(passwordErrors[0]);
      }

      if (formData.password !== formData.confirm_password) {
        throw new Error(t('errorPasswordsDontMatch'));
      }

      const result: FormSubmissionResult = await formService.submitForm({
        url: '/api/auth/dunit/register',
        method: 'POST',
        data: {
          email: formData.email,
          name: formData.name,
          store_name: formData.store_name,
          password_dunit: formData.password
        }
      });

      if (result.success) {
        setSuccess(result.message || 'Registration successful');
        setShowVerification(true);
      } else {
        throw new Error(result.error || 'Registration failed');
      }
    } catch (err: unknown) {
      logger.error('Registration error:', err);
      const error = err as ApiError;
      if (error.response?.status === 400 && error.response?.data?.detail?.includes('not found in La Nube')) {
        setError(t('errorRegisterEmailNotFoundLaNube'));
      } else if (error.response?.status === 400 && error.response?.data?.detail?.includes('already registered')) {
        setError(t('errorRegisterAlreadyRegistered'));
      } else {
        setError(lastError || error.response?.data?.detail || error.message || t('errorRegistrationFailed'));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    clearError();

    try {
      const result: FormSubmissionResult = await formService.submitForm({
        url: '/api/auth/dunit/verify-registration',
        method: 'POST',
        data: {
          email: formData.email,
          verification_code: verificationCode
        }
      });

      if (result.success) {
        const token = result.access_token;
        if (token) {
          localStorage.setItem('token', token);
          setSuccess(t('successRegistration'));
        } else {
          throw new Error('No access token received');
        }
      } else {
        throw new Error(result.error || 'Verification failed');
      }
      
      setTimeout(() => {
        navigate('/');
      }, 2000);
    } catch (err: unknown) {
      logger.error('Verification error:', err);
      const error = err as ApiError;
      setError(lastError || error.response?.data?.detail || error.message || t('errorVerificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Paper
        elevation={3}
        sx={{
          p: { xs: 2, sm: 4 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          backgroundColor: (theme) => theme.palette.mode === 'dark' 
            ? 'rgba(30, 30, 30, 0.9)' 
            : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: 2,
          width: { xs: '92%', sm: '100%' },
          maxWidth: { xs: 'none', sm: '100%' },
          mx: 'auto',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          position: 'relative',
          '& .MuiTextField-root': {
            mb: 1.5,
          }
        }}
      >
        <Box
          component="img"
          src="/logo.png"
          alt="D-Unit Logo"
          sx={{
            width: { xs: '120px', sm: '150px' },
            height: 'auto',
            objectFit: 'contain',
            mb: 2,
            filter: 'drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.1))'
          }}
        />

        <Typography variant="h5" sx={{ mb: 0.5 }}>
          {t('registerTitle')}
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          {t('registerSubtitle')}
        </Typography>

        {error && (
          <Alert 
            severity={error.includes('Please login instead') ? 'info' : 'error'} 
            sx={{ mb: 1.5, width: '100%' }}
          >
            {error}
            {error.includes('Please login instead') && (
              <Box mt={0.5}>
                <Link 
                  href="/login" 
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/login');
                  }}
                >
                  {t('goToLoginLink')}
                </Link>
              </Box>
            )}
          </Alert>
        )}
        {success && <Alert severity="success" sx={{ mb: 1.5, width: '100%' }}>{success}</Alert>}

        <form onSubmit={!showVerification ? handleSubmit : handleVerification} noValidate style={{ width: '100%' }}>
          <TextField
            fullWidth
            label={t('registerLabelLaNubeEmail')}
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            margin="dense"
            required
            disabled={showVerification}
            autoComplete="email"
            inputProps={{
              'aria-label': t('registerLabelLaNubeEmail')
            }}
            size="small"
          />
          
          <Collapse in={!showVerification}>
            <TextField
              fullWidth
              label={t('registerLabelName')}
              name="name"
              value={formData.name}
              onChange={handleChange}
              margin="dense"
              required
              autoComplete="name"
              inputProps={{
                'aria-label': t('registerLabelName')
              }}
              size="small"
            />
            <TextField
              fullWidth
              label={t('registerLabelStoreName')}
              name="store_name"
              value={formData.store_name}
              onChange={handleChange}
              margin="dense"
              required
              autoComplete="organization"
              inputProps={{
                'aria-label': t('registerLabelStoreName')
              }}
              size="small"
            />
            
            <Box sx={{ mt: 1, mb: 0.5 }}>
              <Typography variant="caption" display="block" sx={{ mb: 1, mt: 1, color: 'text.secondary' }}>
                {t('registerPasswordRequirements')}
              </Typography>
              <Box sx={{ mb: 1.5, pl: 1 }}>
                <Typography variant="caption" display="flex" alignItems="center" sx={{ color: lengthValid ? 'success.main' : 'error.main' }}>
                  {lengthValid ? <CheckCircle sx={{ fontSize: '1rem', mr: 0.5 }} /> : <Cancel sx={{ fontSize: '1rem', mr: 0.5 }} />}
                  {t('registerReqLength')}
                </Typography>
                <Typography variant="caption" display="flex" alignItems="center" sx={{ color: uppercaseValid ? 'success.main' : 'error.main' }}>
                  {uppercaseValid ? <CheckCircle sx={{ fontSize: '1rem', mr: 0.5 }} /> : <Cancel sx={{ fontSize: '1rem', mr: 0.5 }} />}
                  {t('registerReqUppercase')}
                </Typography>
                <Typography variant="caption" display="flex" alignItems="center" sx={{ color: numberValid ? 'success.main' : 'error.main' }}>
                  {numberValid ? <CheckCircle sx={{ fontSize: '1rem', mr: 0.5 }} /> : <Cancel sx={{ fontSize: '1rem', mr: 0.5 }} />}
                  {t('registerReqNumber')}
                </Typography>
                <Typography variant="caption" display="flex" alignItems="center" sx={{ color: symbolValid ? 'success.main' : 'error.main' }}>
                  {symbolValid ? <CheckCircle sx={{ fontSize: '1rem', mr: 0.5 }} /> : <Cancel sx={{ fontSize: '1rem', mr: 0.5 }} />}
                  {t('registerReqSymbol')}
                </Typography>
              </Box>
            </Box>

            <TextField
              fullWidth
              label={t('registerLabelPassword')}
              name="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              margin="dense"
              required
              disabled={showVerification}
              autoComplete="new-password"
              inputProps={{
                'aria-label': t('registerLabelPassword')
              }}
              size="small"
            />
            <TextField
              fullWidth
              label={t('registerLabelConfirmPassword')}
              name="confirm_password"
              type="password"
              value={formData.confirm_password}
              onChange={handleChange}
              margin="dense"
              required
              disabled={showVerification}
              autoComplete="new-password"
              inputProps={{
                'aria-label': t('registerLabelConfirmPassword')
              }}
              size="small"
              error={formData.confirm_password.length > 0 && !passwordsMatch}
              helperText={formData.confirm_password.length > 0 && !passwordsMatch ? t('errorPasswordsDontMatch') : ''}
            />
          </Collapse>

          {!showVerification && (
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading || isSubmitting || isRecovering || !lengthValid || !uppercaseValid || !numberValid || !symbolValid || !passwordsMatch}
              sx={{ mt: 2, mb: 1, backgroundColor: '#2196f3', '&:hover': { backgroundColor: '#1976d2' } }}
            >
              {(loading || isSubmitting || isRecovering) ? <CircularProgress size={24} /> : t('registerButton')}
            </Button>
          )}

          <Collapse in={!showVerification}>
            <Box sx={{ mt: 1, textAlign: 'center' }}>
              <Typography variant="body2" color="textSecondary">
                {t('registerAlreadyHaveAccount')} <Link href="/login" sx={{ color: '#2196f3' }}>{t('signInLink')}</Link>
              </Typography>
            </Box>
          </Collapse>

          {/* Verification Code Section */}
          <Collapse in={showVerification} sx={{ width: '100%' }}>
            <Typography variant="h6" align="center" sx={{ mt: 3, mb: 1 }}>
              {t('registerVerifyTitle')}
            </Typography>
            <Typography variant="body2" color="textSecondary" align="center" sx={{ mb: 2 }}>
              {t('registerVerifySubtitle')}
            </Typography>
            <TextField
              fullWidth
              label={t('registerLabelVerificationCode')}
              name="verification_code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              margin="dense"
              required
              autoFocus
              inputProps={{
                'aria-label': t('registerLabelVerificationCode')
              }}
              size="small"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading || isSubmitting || isRecovering}
              sx={{ mt: 2, mb: 1, backgroundColor: '#4caf50', '&:hover': { backgroundColor: '#388e3c' } }}
            >
              {(loading || isSubmitting || isRecovering) ? <CircularProgress size={24} /> : t('registerVerifyButton')}
            </Button>
          </Collapse>
        </form>
      </Paper>
    </AuthLayout>
  );
}

export default Register; 