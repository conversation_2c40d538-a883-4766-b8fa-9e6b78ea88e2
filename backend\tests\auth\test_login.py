import pytest

LOGIN_ENDPOINT = "/api/auth/token"
VERIFY_2FA_ENDPOINT = "/api/auth/2fa/verify-login"

# ---------------------------------------------------------------------------
# E<PERSON><PERSON><PERSON><PERSON> de Login
# ---------------------------------------------------------------------------

def _login(client, username: str, password: str):
    return client.post(
        LOGIN_ENDPOINT,
        data={"username": username, "password": password},
        headers={"Content-Type": "application/x-www-form-urlencoded"},
    )


def test_login_success(client):
    test_client, _ = client
    resp = _login(test_client, "<EMAIL>", "StrongPass1")
    assert resp.status_code == 200
    data = resp.json()
    assert "access_token" in data and data["token_type"] == "bearer"


def test_login_wrong_password(client):
    test_client, _ = client
    resp = _login(test_client, "<EMAIL>", "WrongPass1")
    assert resp.status_code == 401


def test_login_account_locked(client):
    test_client, _ = client
    resp = _login(test_client, "<EMAIL>", "StrongPass1")
    # Ruta devuelve 423 si la cuenta está bloqueada
    assert resp.status_code == 423


def test_login_requires_2fa(client):
    test_client, env = client
    resp = _login(test_client, "<EMAIL>", "StrongPass1")
    assert resp.status_code == 200
    data = resp.json()
    assert data.get("requires_2fa") is True
    # El código fue capturado por fixture
    code_sent = env["sent_codes"].get("<EMAIL>")
    assert code_sent is not None

    # Verificación correcta
    verify_resp = test_client.post(
        VERIFY_2FA_ENDPOINT,
        json={"email": "<EMAIL>", "code": code_sent},
    )
    assert verify_resp.status_code == 200
    verify_data = verify_resp.json()
    assert "access_token" in verify_data

    # Verificación con código incorrecto
    bad_resp = test_client.post(
        VERIFY_2FA_ENDPOINT,
        json={"email": "<EMAIL>", "code": "000000"},
    )
    assert bad_resp.status_code == 400 