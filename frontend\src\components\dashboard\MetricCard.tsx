import React from 'react';
import { 
  Box, 
  Card, 
  Typography, 
  Tooltip,
  IconButton
} from '@mui/material';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import RemoveIcon from '@mui/icons-material/Remove';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { 
  LineChart, 
  Line
} from 'recharts';
import ChartContainer from '../common/ChartContainer';

interface MetricCardProps {
  title: string;
  value: string | number;
  trend?: 'up' | 'down' | 'neutral';
  trendPercentage?: number;
  trendLabel?: string;
  chartData?: Array<{ value: number }>;
  tooltip?: string;
}

/**
 * A reusable metric card component with trend indicator and mini chart
 */
export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  trend,
  trendPercentage,
  trendLabel,
  chartData,
  tooltip
}) => {
  // Default chart data if none provided
  const defaultChartData = [
    { value: 10 },
    { value: 15 },
    { value: 13 },
    { value: 17 },
    { value: 20 },
    { value: 18 },
    { value: 22 }
  ];

  const data = chartData || defaultChartData;
  
  return (
    <Card 
      elevation={0} 
      className="bg-gray-800 rounded-lg shadow-md"
      sx={{ 
        p: 2, 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column', 
        position: 'relative',
        boxShadow: 'none',
        opacity: 1,
        transition: 'opacity 0.2s ease, box-shadow 0.2s ease',
        // Remove any border properties
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          {title}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {tooltip && (
            <Tooltip title={tooltip} arrow placement="top">
              <IconButton size="small" sx={{ p: 0, ml: 0.5 }}>
                <InfoOutlinedIcon fontSize="small" color="action" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>
      
      <Typography variant="h5" sx={{ my: 1, fontWeight: 'medium' }}>
        {value}
      </Typography>
      
      {trend && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          {trend === 'up' ? (
            <ArrowUpwardIcon color="success" fontSize="small" />
          ) : trend === 'down' ? (
            <ArrowDownwardIcon color="error" fontSize="small" />
          ) : (
            <RemoveIcon color="action" fontSize="small" />
          )}
          
          <Typography 
            variant="body2" 
            color={
              trend === 'up' 
                ? 'success.main' 
                : trend === 'down' 
                  ? 'error.main' 
                  : 'text.secondary'
            }
            sx={{ ml: 0.5 }}
          >
            {trendPercentage !== undefined ? `${trendPercentage}%` : ''} {trendLabel || ''}
          </Typography>
        </Box>
      )}
      
      {chartData && (
        <Box sx={{ height: 40, mt: 'auto' }}>
          <Tooltip title="Trend over time">
            <Box sx={{ width: '100%', height: '100%' }}>
              <ChartContainer width="100%" height="100%">
                <LineChart data={data}>
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke={
                      trend === 'up' 
                        ? '#4caf50' 
                        : trend === 'down' 
                          ? '#f44336' 
                          : '#2196f3'
                    } 
                    strokeWidth={2} 
                    dot={false} 
                  />
                </LineChart>
              </ChartContainer>
            </Box>
          </Tooltip>
        </Box>
      )}
    </Card>
  );
}; 
