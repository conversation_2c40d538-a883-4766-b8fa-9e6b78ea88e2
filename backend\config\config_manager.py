import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio
from threading import RLock

from models.security import SecurityConfig, RateLimit, ValidationRules, CostLimits

logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Dynamic configuration manager for security settings
    """
    
    def __init__(self, config_dir: str = "backend/config"):
        self.config_dir = Path(config_dir)
        self._lock = RLock()
        
        # Configuration cache
        self._rate_limits_config: Optional[Dict[str, Any]] = None
        self._validation_rules_config: Optional[Dict[str, Any]] = None
        self._cost_limits_config: Optional[Dict[str, Any]] = None
        
        # File modification timestamps for hot reloading
        self._file_timestamps: Dict[str, float] = {}
        
        # Initialize configurations
        self._load_all_configs()
    
    def _load_yaml_file(self, filename: str) -> Dict[str, Any]:
        """Load and parse a YAML configuration file"""
        file_path = self.config_dir / filename
        
        try:
            if not file_path.exists():
                logger.warning(f"Configuration file not found: {file_path}")
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            # Update file timestamp for hot reloading
            self._file_timestamps[filename] = file_path.stat().st_mtime
            
            logger.info(f"Loaded configuration from {filename}")
            return config or {}
            
        except Exception as e:
            logger.error(f"Failed to load configuration from {filename}: {e}")
            return {}
    
    def _save_yaml_file(self, filename: str, config: Dict[str, Any]) -> bool:
        """Save configuration to YAML file"""
        file_path = self.config_dir / filename
        
        try:
            # Create backup
            if file_path.exists():
                backup_path = file_path.with_suffix(f".{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak")
                file_path.rename(backup_path)
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            # Update timestamp
            self._file_timestamps[filename] = file_path.stat().st_mtime
            
            logger.info(f"Saved configuration to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration to {filename}: {e}")
            return False
    
    def _load_all_configs(self):
        """Load all configuration files"""
        with self._lock:
            self._rate_limits_config = self._load_yaml_file("rate_limits.yaml")
            self._validation_rules_config = self._load_yaml_file("validation_rules.yaml")
            self._cost_limits_config = self._load_yaml_file("cost_limits.yaml")
    
    def _check_file_updates(self):
        """Check if configuration files have been updated"""
        files_to_check = [
            ("rate_limits.yaml", "_rate_limits_config"),
            ("validation_rules.yaml", "_validation_rules_config"),
            ("cost_limits.yaml", "_cost_limits_config")
        ]
        
        updated_files = []
        
        for filename, attr_name in files_to_check:
            file_path = self.config_dir / filename
            
            if not file_path.exists():
                continue
            
            current_mtime = file_path.stat().st_mtime
            stored_mtime = self._file_timestamps.get(filename, 0)
            
            if current_mtime > stored_mtime:
                updated_files.append((filename, attr_name))
        
        # Reload updated files
        if updated_files:
            with self._lock:
                for filename, attr_name in updated_files:
                    new_config = self._load_yaml_file(filename)
                    setattr(self, attr_name, new_config)
                    logger.info(f"Hot-reloaded configuration: {filename}")
        
        return updated_files
    
    def get_rate_limits_config(self, hot_reload: bool = True) -> Dict[str, Any]:
        """Get rate limits configuration"""
        if hot_reload:
            self._check_file_updates()
        
        with self._lock:
            return self._rate_limits_config or {}
    
    def get_validation_rules_config(self, hot_reload: bool = True) -> Dict[str, Any]:
        """Get validation rules configuration"""
        if hot_reload:
            self._check_file_updates()
        
        with self._lock:
            return self._validation_rules_config or {}
    
    def get_cost_limits_config(self, hot_reload: bool = True) -> Dict[str, Any]:
        """Get cost limits configuration"""
        if hot_reload:
            self._check_file_updates()
        
        with self._lock:
            return self._cost_limits_config or {}
    
    def update_rate_limits_config(self, updates: Dict[str, Any]) -> bool:
        """Update rate limits configuration"""
        try:
            with self._lock:
                current_config = self.get_rate_limits_config(hot_reload=False)
                
                # Deep merge the updates
                updated_config = self._deep_merge(current_config, updates)
                
                # Validate the configuration
                if self._validate_rate_limits_config(updated_config):
                    self._rate_limits_config = updated_config
                    return self._save_yaml_file("rate_limits.yaml", updated_config)
                else:
                    logger.error("Rate limits configuration validation failed")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to update rate limits configuration: {e}")
            return False
    
    def update_validation_rules_config(self, updates: Dict[str, Any]) -> bool:
        """Update validation rules configuration"""
        try:
            with self._lock:
                current_config = self.get_validation_rules_config(hot_reload=False)
                
                # Deep merge the updates
                updated_config = self._deep_merge(current_config, updates)
                
                # Validate the configuration
                if self._validate_validation_config(updated_config):
                    self._validation_rules_config = updated_config
                    return self._save_yaml_file("validation_rules.yaml", updated_config)
                else:
                    logger.error("Validation rules configuration validation failed")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to update validation rules configuration: {e}")
            return False
    
    def update_cost_limits_config(self, updates: Dict[str, Any]) -> bool:
        """Update cost limits configuration"""
        try:
            with self._lock:
                current_config = self.get_cost_limits_config(hot_reload=False)
                
                # Deep merge the updates
                updated_config = self._deep_merge(current_config, updates)
                
                # Validate the configuration
                if self._validate_cost_limits_config(updated_config):
                    self._cost_limits_config = updated_config
                    return self._save_yaml_file("cost_limits.yaml", updated_config)
                else:
                    logger.error("Cost limits configuration validation failed")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to update cost limits configuration: {e}")
            return False
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _validate_rate_limits_config(self, config: Dict[str, Any]) -> bool:
        """Validate rate limits configuration"""
        try:
            # Check required sections
            required_sections = ["global_defaults"]
            for section in required_sections:
                if section not in config:
                    logger.error(f"Missing required section in rate limits config: {section}")
                    return False
            
            # Validate global defaults
            global_defaults = config["global_defaults"]
            required_user_types = ["anonymous_users", "authenticated_users"]
            
            for user_type in required_user_types:
                if user_type not in global_defaults:
                    logger.error(f"Missing user type in global defaults: {user_type}")
                    return False
                
                user_config = global_defaults[user_type]
                required_fields = ["requests_per_minute", "requests_per_hour", "requests_per_day"]
                
                for field in required_fields:
                    if field not in user_config or not isinstance(user_config[field], int):
                        logger.error(f"Invalid or missing field in {user_type}: {field}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Rate limits config validation error: {e}")
            return False
    
    def _validate_validation_config(self, config: Dict[str, Any]) -> bool:
        """Validate validation rules configuration"""
        try:
            # Check required sections
            required_sections = ["global_rules"]
            for section in required_sections:
                if section not in config:
                    logger.error(f"Missing required section in validation config: {section}")
                    return False
            
            # Validate global rules
            global_rules = config["global_rules"]
            required_fields = ["max_request_size", "allowed_content_types"]
            
            for field in required_fields:
                if field not in global_rules:
                    logger.error(f"Missing required field in global rules: {field}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Validation config validation error: {e}")
            return False
    
    def _validate_cost_limits_config(self, config: Dict[str, Any]) -> bool:
        """Validate cost limits configuration"""
        try:
            # Check required sections
            required_sections = ["global_cost_limits"]
            for section in required_sections:
                if section not in config:
                    logger.error(f"Missing required section in cost limits config: {section}")
                    return False
            
            # Validate global cost limits
            global_limits = config["global_cost_limits"]
            required_fields = ["system_daily_limit", "system_monthly_limit"]
            
            for field in required_fields:
                if field not in global_limits or not isinstance(global_limits[field], (int, float)):
                    logger.error(f"Invalid or missing field in global cost limits: {field}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Cost limits config validation error: {e}")
            return False
    
    def get_security_config(self) -> SecurityConfig:
        """Get comprehensive security configuration"""
        try:
            # Create SecurityConfig from individual configs
            rate_config = self.get_rate_limits_config()
            validation_config = self.get_validation_rules_config()
            cost_config = self.get_cost_limits_config()
            
            # Extract global defaults
            global_defaults = rate_config.get("global_defaults", {})
            
            # Create RateLimit objects
            anonymous_rate_limit = RateLimit()
            authenticated_rate_limit = RateLimit()
            admin_rate_limit = RateLimit()
            
            if "anonymous_users" in global_defaults:
                anon_config = global_defaults["anonymous_users"]
                anonymous_rate_limit = RateLimit(
                    requests_per_minute=anon_config.get("requests_per_minute", 10),
                    requests_per_hour=anon_config.get("requests_per_hour", 100),
                    requests_per_day=anon_config.get("requests_per_day", 500),
                    burst_allowance=anon_config.get("burst_allowance", 5),
                    window_size=anon_config.get("window_size", 60)
                )
            
            if "authenticated_users" in global_defaults:
                auth_config = global_defaults["authenticated_users"]
                authenticated_rate_limit = RateLimit(
                    requests_per_minute=auth_config.get("requests_per_minute", 60),
                    requests_per_hour=auth_config.get("requests_per_hour", 1000),
                    requests_per_day=auth_config.get("requests_per_day", 10000),
                    burst_allowance=auth_config.get("burst_allowance", 20),
                    window_size=auth_config.get("window_size", 60)
                )
            
            if "admin_users" in global_defaults:
                admin_config = global_defaults["admin_users"]
                admin_rate_limit = RateLimit(
                    requests_per_minute=admin_config.get("requests_per_minute", 200),
                    requests_per_hour=admin_config.get("requests_per_hour", 5000),
                    requests_per_day=admin_config.get("requests_per_day", 50000),
                    burst_allowance=admin_config.get("burst_allowance", 50),
                    window_size=admin_config.get("window_size", 60)
                )
            
            # Create ValidationRules
            global_validation_rules = validation_config.get("global_rules", {})
            validation_rules = ValidationRules(
                max_request_size=global_validation_rules.get("max_request_size", "10MB"),
                allowed_content_types=global_validation_rules.get("allowed_content_types", [
                    "application/json", "multipart/form-data"
                ]),
                required_headers=global_validation_rules.get("required_headers", ["User-Agent"]),
                profanity_filter=global_validation_rules.get("profanity_filter", False),
                rate_limit_on_failure=global_validation_rules.get("rate_limit_on_failure", True),
                suspicious_pattern_detection=global_validation_rules.get("suspicious_pattern_detection", True)
            )
            
            # Create CostLimits
            global_cost_limits = cost_config.get("global_cost_limits", {})
            cost_limits = CostLimits(
                daily_limit_usd=global_cost_limits.get("system_daily_limit", 200.0),
                monthly_limit_usd=global_cost_limits.get("system_monthly_limit", 5000.0),
                alert_threshold=global_cost_limits.get("warning_threshold", 0.8),
                openai_daily_limit=global_cost_limits.get("openai_daily_limit", 100.0),
                meta_api_daily_limit=global_cost_limits.get("meta_api_daily_limit", 50.0),
                emergency_stop_threshold=global_cost_limits.get("emergency_stop_daily", 0.95)
            )
            
            return SecurityConfig(
                enabled=True,
                rate_limiting_enabled=True,
                validation_enabled=True,
                cost_control_enabled=True,
                security_headers_enabled=True,
                monitoring_enabled=True,
                threat_detection_enabled=True,
                anonymous_rate_limit=anonymous_rate_limit,
                authenticated_rate_limit=authenticated_rate_limit,
                admin_rate_limit=admin_rate_limit,
                global_validation=validation_rules,
                global_cost_limits=cost_limits
            )
            
        except Exception as e:
            logger.error(f"Failed to get security config: {e}")
            # Return default config on error
            return SecurityConfig()
    
    def export_config(self, format: str = "yaml") -> str:
        """Export all configurations in specified format"""
        try:
            config_data = {
                "rate_limits": self.get_rate_limits_config(),
                "validation_rules": self.get_validation_rules_config(),
                "cost_limits": self.get_cost_limits_config(),
                "exported_at": datetime.now().isoformat()
            }
            
            if format.lower() == "yaml":
                return yaml.dump(config_data, default_flow_style=False, indent=2)
            elif format.lower() == "json":
                return json.dumps(config_data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
            raise
    
    def import_config(self, config_data: str, format: str = "yaml") -> bool:
        """Import configurations from string data"""
        try:
            if format.lower() == "yaml":
                data = yaml.safe_load(config_data)
            elif format.lower() == "json":
                data = json.loads(config_data)
            else:
                raise ValueError(f"Unsupported import format: {format}")
            
            success = True
            
            # Import each configuration type
            if "rate_limits" in data:
                success &= self.update_rate_limits_config(data["rate_limits"])
            
            if "validation_rules" in data:
                success &= self.update_validation_rules_config(data["validation_rules"])
            
            if "cost_limits" in data:
                success &= self.update_cost_limits_config(data["cost_limits"])
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to import config: {e}")
            return False

# Global configuration manager instance
config_manager = ConfigManager() 