#!/usr/bin/env python3
"""
CSRF Production Test Script
Tests CSRF token flow to identify production issues
"""

import requests
import json
import sys
from urllib.parse import urljoin


def test_csrf_flow(base_url, test_credentials=None):
    """Test the complete CSRF flow"""
    session = requests.Session()
    
    print(f"Testing CSRF flow on: {base_url}")
    print("=" * 50)
    
    # Test 1: Check CSRF health endpoint
    print("1. Testing CSRF health endpoint...")
    try:
        health_url = urljoin(base_url, "/api/auth/csrf-token/health")
        response = session.get(health_url)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   Health: {health_data.get('success', False)}")
            print(f"   Message: {health_data.get('message', 'N/A')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 2: Login (if credentials provided)
    auth_token = None
    if test_credentials:
        print("\n2. Testing login...")
        try:
            login_url = urljoin(base_url, "/api/auth/login")
            login_data = {
                "email": test_credentials["email"],
                "password": test_credentials["password"]
            }
            response = session.post(login_url, json=login_data)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                login_result = response.json()
                auth_token = login_result.get("access_token")
                print("   Login successful")
            else:
                print(f"   Login failed: {response.text}")
        except Exception as e:
            print(f"   Exception: {e}")
    
    # Test 3: Get CSRF token
    if auth_token:
        print("\n3. Testing CSRF token fetch...")
        try:
            csrf_url = urljoin(base_url, "/api/auth/csrf-token")
            headers = {"Authorization": f"Bearer {auth_token}"}
            response = session.get(csrf_url, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                csrf_data = response.json()
                csrf_token = csrf_data.get("csrf_token")
                print(f"   CSRF token received: {csrf_token[:20]}..." if csrf_token else "   No token in response")
                print(f"   Double submit enabled: {csrf_data.get('double_submit_enabled', False)}")
                print(f"   Cookie set: {csrf_data.get('cookie_token_set', False)}")
                
                # Check response headers for cookie
                print("   Response headers:")
                for header, value in response.headers.items():
                    if 'cookie' in header.lower() or 'csrf' in header.lower():
                        print(f"     {header}: {value}")
                        
                return csrf_token
            else:
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"   Exception: {e}")
    
    # Test 4: Make protected request with CSRF token
    if auth_token and csrf_token:
        print("\n4. Testing protected request with CSRF...")
        try:
            # Try a simple POST endpoint that requires CSRF
            test_url = urljoin(base_url, "/api/user/profile")  # Adjust endpoint as needed
            headers = {
                "Authorization": f"Bearer {auth_token}",
                "X-CSRF-Token": csrf_token,
                "Content-Type": "application/json"
            }
            response = session.post(test_url, headers=headers, json={})
            print(f"   Status: {response.status_code}")
            if response.status_code == 404:
                print("   Endpoint not found - trying different endpoint")
            elif response.status_code in [200, 201]:
                print("   CSRF protection working correctly")
            else:
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   Exception: {e}")
    
    print("\n" + "=" * 50)
    print("CSRF Test Complete")


def main():
    """Main test function"""
    # Configuration
    BASE_URL = "https://your-backend-url.com"  # Replace with actual backend URL
    
    # Optional test credentials (remove or comment out for public testing)
    TEST_CREDENTIALS = {
        "email": "<EMAIL>",  # Replace with test account
        "password": "test_password"   # Replace with test password
    }
    
    # Get URL from command line if provided
    if len(sys.argv) > 1:
        BASE_URL = sys.argv[1]
    
    print("CSRF Production Test Script")
    print(f"Base URL: {BASE_URL}")
    
    # Run the test
    test_csrf_flow(BASE_URL, TEST_CREDENTIALS if 'TEST_CREDENTIALS' in locals() else None)


if __name__ == "__main__":
    main()