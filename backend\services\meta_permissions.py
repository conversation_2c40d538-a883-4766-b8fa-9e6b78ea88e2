import logging
import re
import json
from typing import Dict, List, Optional, Set, Any, Tuple, cast
from config.database import db_analysis
from models.meta import MetaChatContext
from services.meta_utils import get_cached_meta_context_wrapper

# Configure logging
logger = logging.getLogger(__name__)

# List of deprecated permissions that should be filtered out in Meta API v23.0
DEPRECATED_PERMISSIONS = {
    "threads_business_basic",
    "manage_app_solution",
    "manage_app_solution_settings",
    "pages_utility_messaging",
    "pages_utility_messages"
}

# Utility function to safely access meta_context attributes
def safe_get(obj, key, default=None):
    """Safely access an attribute/key from either a Pydantic model or dictionary."""
    if obj is None:
        return default
    
    # If it's a Pydantic model
    if isinstance(obj, MetaChatContext):
        return getattr(obj, key, default)
    # If it's a dictionary
    elif isinstance(obj, dict):
        return obj.get(key, default)
    # If it's something else, try attribute access first, then dict access
    try:
        return getattr(obj, key, default)
    except (AttributeError, TypeError):
        try:
            return obj.get(key, default)
        except (AttributeError, TypeError):
            return default

# Meta query detection patterns
META_QUERY_PATTERNS = [
    r'(?i)facebook',
    r'(?i)instagram',
    r'(?i)meta',
    r'(?i)social\s+media',
    r'(?i)post',
    r'(?i)engagement',
    r'(?i)follower',
    r'(?i)audience',
    r'(?i)comment',
    r'(?i)like',
    r'(?i)share'
]

def is_meta_related_query(query: str) -> bool:
    """Detect if a query is related to Meta data"""
    for pattern in META_QUERY_PATTERNS:
        if re.search(pattern, query):
            return True
    return False

# Granular mapping between specific data points and required permissions
META_DATA_POINT_PERMISSIONS = {
    # Basic profile data
    "followers": ["instagram_basic"],
    "profile": ["instagram_basic"],
    
    # Insights and metrics
    "reach": ["instagram_basic", "instagram_manage_insights"],
    "impressions": ["instagram_basic", "instagram_manage_insights"],
    "engagement": ["instagram_basic", "instagram_manage_insights"],
    
    # Content interactions
    "likes": ["instagram_basic"],
    "comments": ["instagram_basic", "instagram_manage_comments"],
    "saves": ["instagram_basic"],
    
    # Business features
    "branded_content": ["instagram_basic", "instagram_branded_content_brand"],
    
    # Default fallback for unknown data points
    "default": ["instagram_basic"]
}

# User-friendly descriptions for each data point
META_DATA_POINT_DESCRIPTIONS = {
    "reach": "how many unique accounts see your content",
    "impressions": "total number of times your content has been viewed",
    "followers": "your account's follower information",
    "followers_count": "the number of followers your account has",
    "follower_demographics": "demographic information about your followers",
    "likes": "likes on your posts",
    "comments": "comments on your posts",
    "shares": "shares and saves of your content",
    "engagement_rate": "how engaged your audience is with your content",
    "post_performance": "how well your individual posts are performing",
    "branded_content": "branded content and sponsorship information",
    "ad_performance": "how your ads are performing",
    "ad_campaigns": "your advertising campaign metrics",
    "page_views": "visits to your page",
    "profile_visits": "visits to your profile"
}

# Map features to required permissions
FEATURE_PERMISSION_MAP = {
    "overview": ["pages_read_engagement", "pages_show_list", "instagram_basic"],
    "page_impressions": ["pages_read_engagement", "instagram_basic"],
    "page_fans": ["pages_read_engagement", "instagram_basic"],
    "page_views": ["pages_read_engagement", "instagram_basic"],
    "posts_analytics": ["pages_read_engagement", "instagram_basic", "instagram_manage_insights"],
    "audience_demographics": ["pages_read_engagement", "pages_read_user_content", "instagram_basic", "instagram_manage_insights"],
    "ad_metrics": ["ads_read", "ads_management"],
    "campaigns_comparison": ["ads_read", "ads_management"],
    "ai_insights": ["pages_read_engagement", "instagram_basic"]
}

# Chat feature to permission mapping
CHAT_FEATURE_PERMISSION_MAP = {
    "engagement": ["pages_read_engagement"],
    "posts": ["pages_read_engagement", "instagram_basic"],
    "followers": ["pages_read_engagement", "instagram_basic"],
    "audience": ["pages_read_engagement", "pages_read_user_content", "instagram_basic"],
    "ads": ["ads_read"],
    "campaigns": ["ads_read", "ads_management"]
}

def filter_deprecated_permissions(permissions: Dict[str, str]) -> Dict[str, str]:
    """Filter out deprecated permissions from a permissions dictionary."""
    filtered = {}
    deprecated_found = []

    for permission, status in permissions.items():
        if permission in DEPRECATED_PERMISSIONS:
            deprecated_found.append(permission)
            logger.debug(f"Filtering out deprecated permission: {permission}")
        else:
            filtered[permission] = status

    if deprecated_found:
        logger.info(f"Filtered out {len(deprecated_found)} deprecated permissions: {deprecated_found}")

    return filtered

async def get_store_permissions(store_id: str) -> Dict[str, str]:
    """Get Meta permissions for a store, filtering out deprecated permissions"""
    if not store_id:
        return {}

    try:
        # Get Meta permissions for the store
        permissions_record = await db_analysis["meta_permissions"].find_one({"store_id": store_id})

        if not permissions_record:
            return {}

        raw_permissions = permissions_record.get("permissions", {})

        # Filter out deprecated permissions
        filtered_permissions = filter_deprecated_permissions(raw_permissions)

        return filtered_permissions
    except Exception as e:
        logger.error(f"Error getting Meta permissions: {str(e)}")
        return {}

async def can_access_feature(store_id: str, feature_key: str) -> bool:
    """Check if a store has permissions to access a feature"""
    if not store_id or not feature_key:
        return False
    
    # Get required permissions for the feature
    required_permissions = FEATURE_PERMISSION_MAP.get(feature_key, [])
    if not required_permissions:
        # If no specific permissions are required, allow access
        return True
    
    # Get store permissions
    store_permissions = await get_store_permissions(store_id)
    if not store_permissions:
        return False
    
    # Check if all required permissions are granted
    for permission in required_permissions:
        status = store_permissions.get(permission, "")
        if status != "granted":
            return False
    
    return True

async def get_missing_permissions(store_id: str, feature_key: str) -> List[str]:
    """Get missing permissions for a feature"""
    if not store_id or not feature_key:
        return []
    
    # Get required permissions for the feature
    required_permissions = FEATURE_PERMISSION_MAP.get(feature_key, [])
    if not required_permissions:
        return []
    
    # Get store permissions
    store_permissions = await get_store_permissions(store_id)
    if not store_permissions:
        return required_permissions
    
    # Find missing permissions
    missing = []
    for permission in required_permissions:
        status = store_permissions.get(permission, "")
        if status != "granted":
            missing.append(permission)
    
    return missing

async def get_missing_permissions_for_chat_query(store_id: str, query: str) -> Dict[str, List[str]]:
    """Get missing permissions for a chat query about Meta data"""
    if not store_id or not query:
        return {}
    
    result = {}
    query_lower = query.lower()
    
    # Check each chat feature
    for feature, terms in [
        ("engagement", ["engagement", "likes", "comments", "shares"]),
        ("posts", ["post", "content", "publish"]),
        ("followers", ["follower", "fan", "following"]),
        ("audience", ["audience", "demographic", "age", "gender", "location"]),
        ("ads", ["ad", "advertisement", "promotion"]),
        ("campaigns", ["campaign", "marketing"])
    ]:
        if any(term in query_lower for term in terms):
            required_permissions = CHAT_FEATURE_PERMISSION_MAP.get(feature, [])
            if required_permissions:
                # Get store permissions
                store_permissions = await get_store_permissions(store_id)
                
                # Find missing permissions
                missing = []
                for permission in required_permissions:
                    status = store_permissions.get(permission, "")
                    if status != "granted":
                        missing.append(permission)
                
                if missing:
                    result[feature] = missing
    
    return result

def extract_meta_data_points(query: str) -> List[str]:
    """Extract specific Meta data points being requested in a query."""
    query_lower = query.lower()
    
    requested_data_points = []
    
    # Map of keywords to data points
    keyword_mapping = {
        "reach": ["reach", "saw my", "seen by", "audience size"],
        "impressions": ["impression", "views", "how many times"],
        "followers": ["follower", "following me", "audience size"],
        "followers_count": ["how many follower", "follower count", "number of follower"],
        "follower_demographics": ["demographic", "audience age", "audience gender", "audience location"],
        "likes": ["like", "liked", "heart"],
        "comments": ["comment", "commented", "said on my post"],
        "shares": ["share", "shared", "saved"],
        "engagement_rate": ["engagement", "engaging", "interact"],
        "post_performance": ["post performance", "content performance", "how posts are doing"],
        "branded_content": ["branded", "sponsored", "brand collab", "partnership"],
        "ad_performance": ["ad performance", "advertisement", "promoted"],
        "ad_campaigns": ["campaign", "ad campaign", "marketing campaign"],
        "page_views": ["page view", "page visit"],
        "profile_visits": ["profile visit", "profile view", "visited my profile"]
    }
    
    # Check for each data point keyword set
    for data_point, keywords in keyword_mapping.items():
        if any(keyword in query_lower for keyword in keywords):
            requested_data_points.append(data_point)
    
    return requested_data_points

async def get_available_meta_data_points(store_id: str, requested_points: List[str]) -> Dict[str, bool]:
    """Determine which requested data points are available based on permissions."""
    store_permissions = await get_store_permissions(store_id)
    
    available_points = {}
    
    for point in requested_points:
        required_permissions = META_DATA_POINT_PERMISSIONS.get(point, [])
        
        # If no permissions required, mark as available
        if not required_permissions:
            available_points[point] = True
            continue
            
        # Check if all required permissions are granted
        has_all_permissions = True
        for permission in required_permissions:
            status = store_permissions.get(permission, "")
            if status != "granted":
                has_all_permissions = False
                break
                
        available_points[point] = has_all_permissions
        
    return available_points

async def fetch_meta_data_for_chat(store_id: str, requested_points: List[str]) -> Dict[str, Any]:
    """Fetch only the available Meta data points for a chat response."""
    # Check which data points we can access
    available_points = await get_available_meta_data_points(store_id, requested_points)
    
    # Get cached Meta context using the wrapper function
    meta_context = await get_cached_meta_context_wrapper(store_id)
    
    # Handle case when meta_context is None or not a valid type
    if meta_context is None:
        logger.warning(f"No Meta context found for store {store_id}")
        return {"available": {}, "unavailable": list(requested_points)}
    
    # Log the type of meta_context for debugging
    logger.debug(f"Meta context type: {type(meta_context)}")
    
    # Additional validation to ensure meta_context is a valid object
    if isinstance(meta_context, str):
        logger.error(f"Invalid Meta context type for store {store_id}: got string instead of expected object")
        return {"available": {}, "unavailable": list(requested_points)}
    
    # Extract only the available data
    result = {"available": {}, "unavailable": []}
    
    for point, is_available in available_points.items():
        if is_available:
            # Extract the specific data based on the data point
            if point == "reach":
                data = _extract_reach_data(meta_context)
                result["available"][point] = data
            elif point == "impressions":
                data = _extract_impressions_data(meta_context)
                result["available"][point] = data
            elif point == "followers" or point == "followers_count":
                data = _extract_followers_data(meta_context)
                result["available"][point] = data
            elif point == "follower_demographics":
                data = _extract_follower_demographics(meta_context)
                result["available"][point] = data
            elif point == "likes":
                data = _extract_likes_data(meta_context)
                result["available"][point] = data
            elif point == "comments":
                data = _extract_comments_data(meta_context)
                result["available"][point] = data
            elif point == "engagement_rate":
                data = _extract_engagement_rate_data(meta_context)
                result["available"][point] = data
            elif point == "post_performance":
                data = _extract_post_performance_data(meta_context)
                result["available"][point] = data
            elif point == "branded_content":
                data = _extract_branded_content_data(meta_context)
                result["available"][point] = data
            elif point == "ad_performance":
                data = _extract_ad_performance_data(meta_context)
                result["available"][point] = data
            elif point == "ad_campaigns":
                data = _extract_ad_campaigns_data(meta_context)
                result["available"][point] = data
            # Add more data point extractors as needed
        else:
            result["unavailable"].append(point)
    
    return result

def _extract_reach_data(meta_context):
    """Extract reach data from meta context."""
    # Example implementation
    reach_data = {
        "total_reach": 0,
        "reach_by_page": []
    }
    
    # Return default data if meta_context is None or not a valid object
    if meta_context is None or not hasattr(meta_context, 'pages') and not isinstance(meta_context, dict):
        logger.warning(f"Invalid meta_context in _extract_reach_data: {type(meta_context)}")
        return reach_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return reach_data
    
    # Aggregate reach across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        audience = page.get("audience", {})
        if not isinstance(audience, dict):
            continue
            
        if "reach" in audience:
            page_reach = audience.get("reach", 0)
            reach_data["total_reach"] += page_reach
            reach_data["reach_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "reach": page_reach
            })
    
    return reach_data

def _extract_impressions_data(meta_context):
    """Extract impressions data from meta context."""
    impressions_data = {
        "total_impressions": 0,
        "impressions_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_impressions_data")
        return impressions_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return impressions_data
    
    # Aggregate impressions across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        audience = page.get("audience", {})
        if not isinstance(audience, dict):
            continue
            
        if "impressions" in audience:
            page_impressions = audience.get("impressions", 0)
            impressions_data["total_impressions"] += page_impressions
            impressions_data["impressions_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "impressions": page_impressions
            })
    
    return impressions_data

def _extract_followers_data(meta_context):
    """Extract followers data from meta context."""
    followers_data = {
        "total_followers": 0,
        "followers_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_followers_data")
        return followers_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return followers_data
    
    # Aggregate followers across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        audience = page.get("audience", {})
        if not isinstance(audience, dict):
            continue
            
        if "total_followers" in audience:
            page_followers = audience.get("total_followers", 0)
            followers_data["total_followers"] += page_followers
            followers_data["followers_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "followers": page_followers
            })
    
    return followers_data

def _extract_follower_demographics(meta_context):
    """Extract follower demographics data from meta context."""
    demographics_data = {
        "age_gender": [],
        "locations": [],
        "by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_follower_demographics")
        return demographics_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return demographics_data
    
    # Aggregate demographics across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        audience = page.get("audience", {})
        if not isinstance(audience, dict):
            continue
            
        demographics = audience.get("demographics", {})
        if not isinstance(demographics, dict):
            continue
            
        # Process age/gender data
        if "age_gender" in demographics:
            demographics_data["age_gender"].extend(demographics.get("age_gender", []))
                
        # Process location data
        if "locations" in demographics:
            demographics_data["locations"].extend(demographics.get("locations", []))
                
        # Store page-specific demographics summary
        demographics_data["by_page"].append({
            "page_name": page.get("name", "Unknown"),
            "demographics": demographics
        })
    
    return demographics_data

def _extract_likes_data(meta_context):
    """Extract likes data from meta context."""
    likes_data = {
        "total_likes": 0,
        "likes_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_likes_data")
        return likes_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return likes_data
    
    # Aggregate likes across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        engagement = page.get("engagement", {})
        if not isinstance(engagement, dict):
            continue
            
        if "total_likes" in engagement:
            page_likes = engagement.get("total_likes", 0)
            likes_data["total_likes"] += page_likes
            likes_data["likes_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "likes": page_likes
            })
    
    return likes_data

def _extract_comments_data(meta_context):
    """Extract comments data from meta context."""
    comments_data = {
        "total_comments": 0,
        "comments_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_comments_data")
        return comments_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return comments_data
    
    # Aggregate comments across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        engagement = page.get("engagement", {})
        if not isinstance(engagement, dict):
            continue
            
        if "total_comments" in engagement:
            page_comments = engagement.get("total_comments", 0)
            comments_data["total_comments"] += page_comments
            comments_data["comments_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "comments": page_comments
            })
    
    return comments_data

def _extract_engagement_rate_data(meta_context):
    """Extract engagement rate data from meta context."""
    engagement_data = {
        "average_engagement_rate": 0,
        "engagement_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_engagement_rate_data")
        return engagement_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return engagement_data
    
    total_pages = 0
    
    # Aggregate engagement across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        engagement = page.get("engagement", {})
        if not isinstance(engagement, dict):
            continue
            
        if "average_engagement_rate" in engagement:
            page_rate = engagement.get("average_engagement_rate", 0)
            engagement_data["engagement_by_page"].append({
                "page_name": page.get("name", "Unknown"),
                "engagement_rate": page_rate
            })
            
            # Track for average calculation
            engagement_data["average_engagement_rate"] += page_rate
            total_pages += 1
    
    # Calculate average engagement rate
    if total_pages > 0:
        engagement_data["average_engagement_rate"] = engagement_data["average_engagement_rate"] / total_pages
    
    return engagement_data

def _extract_post_performance_data(meta_context):
    """Extract post performance data from meta context."""
    post_data = {
        "total_posts": 0,
        "top_performing_posts": [],
        "average_performance": {}
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_post_performance_data")
        return post_data
    
    # This would typically be more complex in a real implementation
    # For now, return a placeholder with proper type checking
    return post_data

def _extract_branded_content_data(meta_context):
    """Extract branded content data from meta context."""
    branded_content_data = {
        "total_branded_posts": 0,
        "branded_content_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_branded_content_data")
        return branded_content_data
    
    # This would typically be more complex in a real implementation
    # For now, return a placeholder with proper type checking
    return branded_content_data

def _extract_ad_performance_data(meta_context):
    """Extract ad performance data from meta context."""
    ad_data = {
        "total_spend": 0,
        "total_impressions": 0,
        "total_clicks": 0,
        "average_cpc": 0,
        "average_ctr": 0,
        "by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_ad_performance_data")
        return ad_data
    
    # Get pages using safe_get
    pages = safe_get(meta_context, 'pages', [])
    if not pages:
        return ad_data
    
    # Aggregate ad metrics across all pages
    for page in pages:
        # Ensure page is a dictionary and has the required fields
        if not isinstance(page, dict):
            continue
            
        ad_metrics = page.get("ad_metrics", {})
        if not isinstance(ad_metrics, dict):
            continue
            
        # Accumulate totals
        ad_data["total_spend"] += ad_metrics.get("total_spend", 0)
        ad_data["total_impressions"] += ad_metrics.get("total_impressions", 0)
        ad_data["total_clicks"] += ad_metrics.get("total_clicks", 0)
        
        # Add page-specific data
        ad_data["by_page"].append({
            "page_name": page.get("name", "Unknown"),
            "ad_metrics": ad_metrics
        })
    
    # Calculate averages
    if ad_data["total_impressions"] > 0:
        ad_data["average_ctr"] = (ad_data["total_clicks"] / ad_data["total_impressions"]) * 100
        
    if ad_data["total_clicks"] > 0:
        ad_data["average_cpc"] = ad_data["total_spend"] / ad_data["total_clicks"]
    
    return ad_data

def _extract_ad_campaigns_data(meta_context):
    """Extract ad campaigns data from meta context."""
    campaigns_data = {
        "total_campaigns": 0,
        "active_campaigns": 0,
        "campaigns_by_page": []
    }
    
    # Return default data if meta_context is None
    if meta_context is None:
        logger.warning("meta_context is None in _extract_ad_campaigns_data")
        return campaigns_data
    
    # This would typically be more complex in a real implementation
    # For now, return a placeholder with proper type checking
    return campaigns_data

def generate_permission_message(query: str, data: Dict[str, Any]) -> str:
    """
    Generate a user-friendly message about Meta permission status.
    
    Args:
        query: The user's original query
        data: Dictionary with available and unavailable data
            {
                "available": {data_point: data, ...},
                "unavailable": [data_point1, data_point2, ...]
            }
    
    Returns:
        A message explaining what data is available and what is restricted by permissions
    """
    if not data.get("unavailable"):
        return ""  # All data is available, no permission message needed
    
    # Get unavailable data points with their descriptions
    unavailable_points = data.get("unavailable", [])
    unavailable_descriptions = cast(list[str], [
        META_DATA_POINT_DESCRIPTIONS.get(point, point) or str(point)
        for point in unavailable_points
    ])
    
    # Get available data points
    available_points = list(data.get("available", {}).keys())
    available_descriptions = cast(list[str], [
        META_DATA_POINT_DESCRIPTIONS.get(point, point) or str(point)
        for point in available_points
    ])
    
    message = ""
    
    # If we have some available data
    if available_points:
        message += f"I can provide information about {', '.join(available_descriptions)}.\n\n"
    
    # Message about unavailable data
    message += f"I can't access {', '.join(unavailable_descriptions)} because the necessary permissions have been revoked.\n\n"
    
    # Compile the unique required permissions for all unavailable data points
    required_permissions = set()
    for point in unavailable_points:
        permissions = META_DATA_POINT_PERMISSIONS.get(point, [])
        required_permissions.update(permissions)
    
    # Add information about required permissions
    if required_permissions:
        message += f"To access this data, you need to grant these permissions: {', '.join(sorted(required_permissions))}"
    
    return message

async def get_data_point_permission_status(store_id: str, data_point: str) -> Dict[str, Any]:
    """
    Get detailed permission status for a specific data point.
    
    Args:
        store_id: The ID of the store to check permissions for
        data_point: The data point to check (e.g., 'followers', 'likes', etc.)
        
    Returns:
        A dictionary with permission status details:
        {
            "available": bool,  # Whether the data point is available
            "required_permissions": List[str],  # List of required permissions
            "missing_permissions": List[str],  # List of missing permissions
            "permission_status": Dict[str, str]  # Status of each required permission
        }
    """
    # Get required permissions for this data point
    required_permissions = META_DATA_POINT_PERMISSIONS.get(data_point, [])
    
    if not required_permissions:
        # If no permissions required, the data point is available
        return {
            "available": True,
            "required_permissions": [],
            "missing_permissions": [],
            "permission_status": {}
        }
    
    # Get store permissions
    store_permissions = await get_store_permissions(store_id)
    
    # Check each required permission
    missing_permissions = []
    permission_status = {}
    
    for permission in required_permissions:
        status = store_permissions.get(permission, "")
        permission_status[permission] = status
        
        if status != "granted":
            missing_permissions.append(permission)
    
    # Determine if the data point is available based on permissions
    is_available = len(missing_permissions) == 0
    
    return {
        "available": is_available,
        "required_permissions": required_permissions,
        "missing_permissions": missing_permissions,
        "permission_status": permission_status
    }

async def track_permission_change(store_id: str, permission: str, status: str) -> None:
    """
    Track permission changes in the revocation history.
    
    Args:
        store_id: The ID of the store
        permission: The permission that changed
        status: The new status ('granted' or 'revoked')
    """
    from models.meta import PermissionChange
    from datetime import datetime, timezone
    
    try:
        # Create permission change record
        change = PermissionChange(
            permission=permission,
            status=status,
            timestamp=datetime.now(timezone.utc)
        )
        
        # Add to the revocation history
        await db_analysis["meta_permissions"].update_one(
            {"store_id": store_id},
            {
                "$push": {"revocation_history": change.dict()},
                "$set": {
                    "last_updated": datetime.now(timezone.utc),
                    f"permissions.{permission}": status
                }
            },
            upsert=True
        )
        
        logger.info(f"Tracked permission change for store {store_id}: {permission} -> {status}")
    except Exception as e:
        logger.error(f"Error tracking permission change: {str(e)}")

async def get_permission_history(store_id: str, permission: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get permission change history for a store.
    
    Args:
        store_id: The ID of the store
        permission: Optional specific permission to filter by
        
    Returns:
        List of permission change records
    """
    try:
        # Get the permissions document
        permissions_doc = await db_analysis["meta_permissions"].find_one({"store_id": store_id})
        
        if not permissions_doc or "revocation_history" not in permissions_doc:
            return []
        
        history = permissions_doc["revocation_history"]
        
        # Filter by specific permission if requested
        if permission:
            history = [record for record in history if record["permission"] == permission]
        
        # Sort by timestamp, most recent first
        history.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return history
    except Exception as e:
        logger.error(f"Error getting permission history: {str(e)}")
        return []

def _log_debug(message: str):
    """Helper to log debug messages, checking environment."""
    # Only log if not in production (assuming central logging handles level)
    # if os.getenv("APP_ENV", "development") != "production":
    # Use logger.debug which is controlled by central config
    logger.debug(message)

def get_required_permissions(data_point: str) -> List[str]:
    """Gets the required Meta permissions for a specific data point."""
    permissions = META_DATA_POINT_PERMISSIONS.get(data_point, META_DATA_POINT_PERMISSIONS["default"])
    # _log_debug(f"[DEBUG] Looking up permissions for data point: {data_point}")
    logger.debug(f"Required permissions for {data_point}: {permissions}")
    return permissions 