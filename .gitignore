# Environment files - CRITICAL SECURITY
.env
.env.production
.env.development
.env.staging
.env.local
.env.test
backend/.env
frontend/.env
backend/.env.production
frontend/.env.production
backend/.env.development
frontend/.env.development
backend/.env.staging
frontend/.env.staging

# Environment patterns
.env.*
!.env.example
!.env.template
*/.env
*/.env.*
!*/.env.example
!*/.env.template

# IDE and cursor files
.contexfiles
.cursorignore
.cursorindexignore.bak
.cursorindexignore
.python-version
.repomixignore

# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Logs - POTENTIAL SENSITIVE DATA
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs with potential sensitive data
**/debug.log
**/error.log
**/combined.log
**/app.log
logs/

# Specific log files found in codebase
backend/logs/
backend/*.log
market_analysis.log
competitor_analysis.log
backend/market_analysis.log
backend/competitor_analysis.log
**/market_analysis.log
**/competitor_analysis.log

# Virtual environment
venv/
env/
.venv/
ENV/

# IDE specific files
.vscode/
.idea/
*.swp
*.swo
.vs/

# Sensitive data and secrets
*.key
*.pem
*.crt
*.csr
*.env
secrets/
**/secrets/*
secrets.yaml
secrets.yml
secrets.json
.secrets/
.cursor/
aws.json
gcp.json
azure.json
.npmrc
.pypirc

# API Keys and Tokens
**/apikey*
**/api_key*
**/*apikey*
**/*api_key*
**/token*
**/*.token
**/*.apikey
**/*.api_key
**/*.secret
**/*.credentials
credentials.json
client_secrets.json
service-account.json

# Certificate files - CRITICAL SECURITY
*.pfx
*.p12
*.cer
*.der
*.csr
*.key
keystore
.keystore
*.jks
*.p7b
*.p7c

# Specific certificate locations found in codebase
backend/certs/
frontend/certs/
**/certs/*.key
**/certs/*.pem
**/certs/*.crt
**/certs/ca.*
**/certs/cert.*
certs/
ssl/

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
Thumbs.db
.directory
desktop.ini

# Node modules
node_modules/

# Build directories
dist/
build/
out/

# Coverage directory
coverage/

# Temporary files
*.tmp
*.temp
.cache/

# Local development files
*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml
.ebextensions/*_secrets.config

# AWS specific
.aws/
aws-credentials
aws.config
.aws-credentials
.aws-config
aws_config
aws_credentials

# MongoDB
mongod*.conf
*.mongodb
mongodb*.conf

# OpenAI
openai.json
.openai
**/openai_key*
**/openai_api*

# Meta (Facebook)
meta_credentials.json
facebook_credentials.json
**/meta_token*
**/facebook_token*

# Google
google_credentials.json
google-credentials.json
**/google_token*
**/oauth*
**/client_secret*

# JWT
jwt_secret*
jwt.key
jwt_private*
jwt_public*

# Email credentials
email_config*
smtp_config*
mail_credentials*

# Keep template files
!template.env
!example.env
!sample.env

# Ignore .cursor folder and all its contents
.cursor/
.cursor/**

# Ignore all dotfiles except .gitignore, .env.example, .env.template
.*
!.gitignore
!.env.example
!.env.template

# Kubernetes deployment files with secrets
backend/d-unit-backend-service.yaml
backend/d-unit-backend-secrets.yaml
backend/d-unit-backend-configmap.yaml
backend/d-unit-backend-deployment.yaml

# Kubernetes files (general patterns)
**/k8s-secrets.yaml
**/k8s-config.yaml
**/*-secrets.yaml
**/*-configmap.yaml
**/*-deployment.yaml
**/kubernetes/*.yaml
**/k8s/*.yaml
k8s/
kubernetes/

# Kubernetes template files (keep these)
!**/*.template.yaml
!**/*.example.yaml
!**/*-template.yaml
!**/*-example.yaml

# Docker and container secrets
docker-compose.override.yml
docker-compose.prod.yml
**/Dockerfile.prod
**/Dockerfile.production

# Infrastructure as Code with secrets
terraform.tfstate*
terraform.tfvars
*.tfvars
terraform/
.terraform/
*.terraform.lock.hcl

# Helm charts with secrets
**/helm/values.yaml
**/helm/values-prod.yaml
**/helm/values-production.yaml
**/charts/*/values.yaml
# Keep template values
!**/helm/values.template.yaml
!**/helm/values.example.yaml

# Cloud provider specific
# AWS
.aws/
aws-credentials
aws.config
.aws-credentials
.aws-config
aws_config
aws_credentials

# GCP
gcp-service-account.json
service-account-key.json
.gcp/
google-cloud-keyfile.json

# Azure
.azure/
azure-credentials.json

# Debug and development files - SECURITY RISK
debug-*.js
debug-*.ts
**/debug-*
frontend/debug-csrf.js
frontend/debug-csrf-detailed.js
**/debug-csrf*.js

# Test result files with potential real data
test-results/
**/test-results/
*.test-results
backend/test-results/

# Additional sensitive patterns found in audit
*.mongodb-backup
*.db-dump
*-backup.*
*-dump.*

# Specific files to ignore
codebase.xml
CLAUDE.md
D-Unit.code-workspace

# Ignore refactor directory
D-Unit-Refactor/
