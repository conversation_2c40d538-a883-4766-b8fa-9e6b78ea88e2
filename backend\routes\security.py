from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
import logging

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from models.security import (
    SecurityEvent, SecurityReport, BudgetAlert, CostSummary,
    OptimizationSuggestion, SecurityConfig, ThreatAlert
)
from utils.security_utils import extract_client_ip, generate_trace_id
from config.settings import get_settings

logger = logging.getLogger(__name__)

router = APIRouter()

settings = get_settings()

# Response models
class SecurityStatusResponse(BaseModel):
    status: str
    enabled_features: Dict[str, bool]
    system_health: Dict[str, Any]
    recent_events_count: int
    threat_level: str

class SecurityMetricsResponse(BaseModel):
    total_requests: int
    blocked_requests: int
    rate_limited_requests: int
    validation_failures: int
    cost_violations: int
    top_threat_types: List[Dict[str, Any]]
    period_start: datetime
    period_end: datetime

class CostAnalysisResponse(BaseModel):
    current_daily_cost: float
    current_monthly_cost: float
    projected_monthly_cost: float
    cost_breakdown: Dict[str, float]
    optimization_suggestions: List[OptimizationSuggestion]
    alerts: List[BudgetAlert]

# Dependency to check admin access
async def require_admin_access(request: Request):
    """Require admin access for security endpoints"""
    # In production, you'd verify JWT token and check admin role
    # For now, we'll allow access but log the request
    client_ip = extract_client_ip(request)
    logger.info(f"Security endpoint accessed from {client_ip}")
    return True

@router.get("/status", response_model=SecurityStatusResponse)
async def get_security_status(
    request: Request,
    admin_access: bool = Depends(require_admin_access)
):
    """Get current security system status"""
    try:
        # Get security gateway instance from app state
        security_gateway = getattr(request.app.state, 'security_gateway', None)
        
        if not security_gateway:
            # Return basic status if gateway not available
            return SecurityStatusResponse(
                status="unknown",
                enabled_features={
                    "rate_limiting": False,
                    "validation": False,
                    "cost_control": False,
                    "threat_detection": False
                },
                system_health={"status": "unknown"},
                recent_events_count=0,
                threat_level="unknown"
            )
        
        # Get security summary
        summary = security_gateway.get_security_summary()
        
        return SecurityStatusResponse(
            status="active",
            enabled_features=summary["status"],
            system_health={
                "memory_usage": "normal",  # You'd implement actual monitoring
                "cpu_usage": "normal",
                "response_time": "normal"
            },
            recent_events_count=summary["recent_events"],
            threat_level="low"  # Based on recent events
        )
        
    except Exception as e:
        logger.error(f"Error getting security status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security status")

@router.get("/metrics", response_model=SecurityMetricsResponse)
async def get_security_metrics(
    request: Request,
    hours: int = Query(24, ge=1, le=168, description="Hours to look back"),
    admin_access: bool = Depends(require_admin_access)
):
    """Get security metrics for the specified time period"""
    try:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        # In production, you'd query from database
        # For now, return mock data
        return SecurityMetricsResponse(
            total_requests=1000,
            blocked_requests=5,
            rate_limited_requests=15,
            validation_failures=8,
            cost_violations=2,
            top_threat_types=[
                {"type": "rate_limit_violation", "count": 15},
                {"type": "validation_failure", "count": 8},
                {"type": "suspicious_activity", "count": 3}
            ],
            period_start=start_time,
            period_end=end_time
        )
        
    except Exception as e:
        logger.error(f"Error getting security metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security metrics")

@router.get("/events")
async def get_security_events(
    request: Request,
    limit: int = Query(100, ge=1, le=1000),
    event_type: Optional[str] = Query(None),
    threat_level: Optional[str] = Query(None),
    admin_access: bool = Depends(require_admin_access)
):
    """Get recent security events"""
    try:
        # In production, you'd query from database with filters
        # For now, return mock events
        mock_events = [
            {
                "id": generate_trace_id(),
                "event_type": "rate_limit_violation",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "ip_address": "*************",
                "endpoint": "/api/chat",
                "threat_level": "medium",
                "details": {"limit_exceeded": "requests_per_minute"}
            },
            {
                "id": generate_trace_id(),
                "event_type": "validation_failure",
                "timestamp": (datetime.now(timezone.utc) - timedelta(minutes=5)).isoformat(),
                "ip_address": "********",
                "endpoint": "/api/auth/token",
                "threat_level": "high",
                "details": {"pattern": "sql_injection"}
            }
        ]
        
        # Apply filters if specified
        filtered_events = mock_events
        if event_type:
            filtered_events = [e for e in filtered_events if e["event_type"] == event_type]
        if threat_level:
            filtered_events = [e for e in filtered_events if e["threat_level"] == threat_level]
        
        return {"events": filtered_events[:limit]}
        
    except Exception as e:
        logger.error(f"Error getting security events: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security events")

@router.get("/cost-analysis", response_model=CostAnalysisResponse)
async def get_cost_analysis(
    request: Request,
    store_id: Optional[str] = Query(None),
    admin_access: bool = Depends(require_admin_access)
):
    """Get cost analysis and optimization suggestions"""
    try:
        # In production, you'd query actual cost data
        # For now, return mock analysis
        return CostAnalysisResponse(
            current_daily_cost=15.75,
            current_monthly_cost=245.30,
            projected_monthly_cost=385.50,
            cost_breakdown={
                "openai": 180.20,
                "meta_api": 45.10,
                "infrastructure": 20.00
            },
            optimization_suggestions=[
                OptimizationSuggestion(
                    suggestion_type="caching",
                    title="Enable Intelligent Caching",
                    description="Cache frequent API responses to reduce external API calls",
                    potential_savings_usd=85.30,
                    implementation_effort="low",
                    priority="high"
                ),
                OptimizationSuggestion(
                    suggestion_type="model_optimization",
                    title="Optimize AI Model Selection",
                    description="Use settings.OPENAI_DEFAULT_MODEL for simple queries, reserve GPT-4 for complex ones",
                    potential_savings_usd=120.40,
                    implementation_effort="medium",
                    priority="medium"
                )
            ],
            alerts=[]
        )
        
    except Exception as e:
        logger.error(f"Error getting cost analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cost analysis")

@router.get("/config")
async def get_security_config(
    request: Request,
    admin_access: bool = Depends(require_admin_access)
):
    """Get current security configuration"""
    try:
        # In production, you'd return actual config from database/files
        return {
            "rate_limiting": {
                "enabled": True,
                "default_limits": {
                    "requests_per_minute": 60,
                    "requests_per_hour": 1000,
                    "requests_per_day": 10000
                }
            },
            "validation": {
                "enabled": True,
                "sql_injection_protection": True,
                "xss_protection": True,
                "max_request_size": "10MB"
            },
            "cost_control": {
                "enabled": True,
                "daily_limit": 50.0,
                "monthly_limit": 1000.0,
                "alert_threshold": 0.8
            },
            "monitoring": {
                "enabled": True,
                "log_level": "INFO",
                "retention_days": 30
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting security config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get security config")

@router.post("/config")
async def update_security_config(
    request: Request,
    config_update: Dict[str, Any],
    admin_access: bool = Depends(require_admin_access)
):
    """Update security configuration"""
    try:
        # In production, you'd validate and update actual config
        logger.info(f"Security config update requested: {config_update}")
        
        # Validate the configuration update
        allowed_sections = ["rate_limiting", "validation", "cost_control", "monitoring"]
        
        for section in config_update.keys():
            if section not in allowed_sections:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid config section: {section}"
                )
        
        # Here you would apply the configuration changes
        # For now, just return success
        return {
            "status": "success",
            "message": "Configuration updated successfully",
            "updated_sections": list(config_update.keys()),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating security config: {e}")
        raise HTTPException(status_code=500, detail="Failed to update security config")

@router.post("/clear-events")
async def clear_security_events(
    request: Request,
    older_than_hours: int = Query(24, ge=1, le=8760),
    admin_access: bool = Depends(require_admin_access)
):
    """Clear old security events"""
    try:
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=older_than_hours)
        
        # In production, you'd delete from database
        logger.info(f"Clearing security events older than {cutoff_time}")
        
        return {
            "status": "success",
            "message": f"Cleared events older than {older_than_hours} hours",
            "cutoff_time": cutoff_time.isoformat(),
            "events_cleared": 42  # Mock number
        }
        
    except Exception as e:
        logger.error(f"Error clearing security events: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear security events")

@router.get("/health")
async def security_health_check():
    """Health check endpoint for security system"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "components": {
            "rate_limiter": "operational",
            "validator": "operational", 
            "cost_controller": "operational",
            "threat_detector": "operational"
        },
        "version": "2.0.0"
    }

@router.get("/report")
async def generate_security_report(
    request: Request,
    days: int = Query(7, ge=1, le=90),
    format: str = Query("json", regex="^(json|csv)$"),
    admin_access: bool = Depends(require_admin_access)
):
    """Generate comprehensive security report"""
    try:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days)
        
        # In production, you'd generate actual report from data
        report = {
            "report_id": generate_trace_id(),
            "period_start": start_time.isoformat(),
            "period_end": end_time.isoformat(),
            "summary": {
                "total_requests": 15000,
                "blocked_requests": 125,
                "block_rate": 0.0083,
                "top_blocked_ips": ["*************", "********"],
                "cost_savings": 245.30
            },
            "recommendations": [
                "Consider implementing IP-based blocking for repeat offenders",
                "Review rate limits for authentication endpoints",
                "Enable additional caching for cost optimization"
            ]
        }
        
        if format == "json":
            return report
        else:
            # For CSV format, you'd convert to CSV
            return JSONResponse(
                content={"error": "CSV format not yet implemented"},
                status_code=501
            )
        
    except Exception as e:
        logger.error(f"Error generating security report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate security report") 