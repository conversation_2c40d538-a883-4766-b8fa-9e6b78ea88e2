import React, { useState, useCallback, ReactNode, useEffect } from 'react';
import { FeedbackContext } from './feedback.context'; // Import the context object

interface FeedbackProviderProps {
  children: ReactNode;
}

export const FeedbackProvider: React.FC<FeedbackProviderProps> = ({ children }) => {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  // State to hold the resolve function of the promise for the logout flow
  const [logoutPromiseResolver, setLogoutPromiseResolver] = useState<{ resolve: () => void } | null>(null);
  const [isLogoutIntent, setIsLogoutIntent] = useState(false);
  
  // Log when FeedbackProvider renders
  useEffect(() => {
    // logger.info('[FeedbackContext] FeedbackProvider rendering. Current state: isFeedbackModalOpen=' + isFeedbackModalOpen);
  }, [isFeedbackModalOpen]);

  // Opens the modal and returns a promise that resolves when the modal is closed.
  // Used specifically for the logout flow to pause execution.
  const openFeedbackModalForLogout = useCallback(() => {
    return new Promise<void>((resolve) => {
      // logger.info(`[FeedbackContext] openFeedbackModalForLogout called. Current state: isFeedbackModalOpen=${isFeedbackModalOpen}, isLogoutIntent=${isLogoutIntent}`); // Add log
      setIsLogoutIntent(true);
      setIsFeedbackModalOpen(true);
      setLogoutPromiseResolver({ resolve }); // Store the resolver
      // logger.info(`[FeedbackContext] openFeedbackModalForLogout state updated. Prev: open=${prevStateOpen}, intent=${prevStateIntent}. New: open=true, intent=true`); // Add log
    });
  }, []); // Removed unnecessary dependencies for logging current state accurately

  // Closes the modal and resolves the logout promise if it exists.
  const closeFeedbackModal = useCallback(() => {
    // logger.info(`[FeedbackContext] closeFeedbackModal called. Current state: isFeedbackModalOpen=${isFeedbackModalOpen}, isLogoutIntent=${isLogoutIntent}, resolverExists=${!!logoutPromiseResolver}`); // Add log
    setIsFeedbackModalOpen(false);
    setIsLogoutIntent(false);
    // logger.info(`[FeedbackContext] closeFeedbackModal state updated. Prev: open=${prevStateOpen}, intent=${prevStateIntent}. New: open=false, intent=false`); // Add log
    if (logoutPromiseResolver) {
      // logger.info('[FeedbackContext] Resolving logout promise.'); // Add log
      logoutPromiseResolver.resolve(); // Resolve the promise
      setLogoutPromiseResolver(null); // Clear the resolver
    } else {
      // logger.info('[FeedbackContext] No logout promise resolver to resolve.'); // Add log
    }
  }, [logoutPromiseResolver]); // Removed unnecessary dependencies for logging current state accurately

  // Opens the modal normally (not for logout).
  // Ensures any lingering logout promise is resolved/cleared.
  const openFeedbackModal = useCallback(() => {
      setIsLogoutIntent(false);
      setIsFeedbackModalOpen(true);
      // Ensure resolver is cleared if opened not for logout
      if (logoutPromiseResolver) {
          logoutPromiseResolver.resolve(); // Resolve previous just in case
          setLogoutPromiseResolver(null);
      }
  }, [logoutPromiseResolver]);


  const value = {
    isFeedbackModalOpen,
    openFeedbackModal,
    closeFeedbackModal,
    openFeedbackModalForLogout,
    isLogoutIntent,
  };

  return <FeedbackContext.Provider value={value}>{children}</FeedbackContext.Provider>;
}; 