fastapi>=0.100.0
uvicorn>=0.15.0
python-jose==3.3.0
passlib==1.7.4
python-multipart==0.0.6
pymongo==4.6.1
python-dotenv>=1.0.0
py3langid>=0.3.0
openai>=1.0.0
websockets==11.0.3
bcrypt==4.0.1
google-auth==2.27.0
google-api-python-client>=2.0.0
requests==2.31.0
email-validator==2.1.0.post1
boto3>=1.26.0
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0
gunicorn>=20.1.0
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
statsmodels>=0.14.0
apscheduler>=3.10.0
aiohttp>=3.8.0
motor>=2.0.0
flake8>=7.0.0

# Added packages that were in root requirements.txt but missing in backend:
beautifulsoup4>=4.12.2
nltk>=3.9.1
tenacity>=9.0.0
mysql-connector-python

# Security Gateway Dependencies
slowapi>=0.1.9
redis>=5.0.0
pyyaml>=6.0
cryptography>=40.0.0
ratelimit>=2.2.1

# Meta/Facebook SDK Dependencies
facebook-business>=19.0.0

