import React, { useState, ReactNode } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  Paper,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AuthLayout from './AuthLayout';
import { authService } from '../../services/authService';
import { useTranslation, Trans } from 'react-i18next';
import { ValidationUtils } from '../../utils/validation';

// Define error interface for better type safety
interface ApiError {
  response?: {
    data?: {
      detail?: string | Array<{ msg: string }> | Record<string, unknown> | null;
      message?: string;
    };
    status?: number;
  };
  message?: string;
}

const ForgotPassword = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [step, setStep] = useState<'request' | 'verify'>('request');
  const [error, setError] = useState<{ message: string | ReactNode; type: 'error' | 'info' }>({ message: '', type: 'error' });
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const validateEmail = (email: string): boolean => {
    const result = ValidationUtils.validateEmail(email);
    if (!result.isValid) {
      setError({
        message: result.error || t('errorInvalidEmail'),
        type: 'error'
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError({ message: '', type: 'error' });
    setSuccess('');
    
    // Client-side validation
    if (!validateEmail(email)) {
      return;
    }
    
    setLoading(true);

    try {
      if (step === 'request') {
        const response = await authService.requestPasswordReset(email);
        if (response.message) {
          setSuccess(t('successPasswordCodeSent'));
          setStep('verify');
        }
      } else {
        const resp = await authService.verifyPasswordReset(email, code, newPassword, confirmPassword);
        if (resp.access_token) {
          setSuccess(t('successPasswordReset'));
          // Automatically log in
          authService.setToken(resp.access_token);
          navigate('/dashboard');
        }
      }
    } catch (err: unknown) {
      const error = err as ApiError; // Type assertion with interface instead of any
      if (error.response?.status === 404) {
        setError({
          message: t('errorEmailNotFoundLaNube'),
          type: 'error'
        });
      } else if (error.response?.status === 403) {
        setError({
          message: (
            <Trans i18nKey="errorRegisterFirst">
              <Link
                component="button"
                onClick={() => navigate('/register')}
                sx={{
                  color: '#00A3FF',
                  cursor: 'pointer',
                  textDecoration: 'underline'
                }}
              />
            </Trans>
          ),
          type: 'info'
        });
      } else if (error.response?.status === 422) {
        // Handle validation errors from the backend
        const validationError = error.response?.data?.detail;
        if (Array.isArray(validationError)) {
          // If it's an array of validation errors, take the first one
          setError({
            message: validationError[0]?.msg || t('errorInvalidEmailFormat'),
            type: 'error'
          });
        } else if (typeof validationError === 'object' && validationError !== null) {
          // If it's an object with validation details
          setError({
            message: t('errorInvalidEmailFormatCheck'),
            type: 'error'
          });
        } else {
          setError({
            message: t('errorInvalidEmail'),
            type: 'error'
          });
        }
      } else {
        // Handle other errors
        let errorMessage: string | ReactNode = t('errorForgotPasswordGeneral');
        const backendMsgRaw = typeof error.response?.data?.detail === 'string' ? error.response.data.detail : '';
        const backendMsg = backendMsgRaw.toLowerCase();

        if (backendMsg.includes('uppercase')) {
          errorMessage = t('errorPasswordUppercase');
        } else if (backendMsg.includes('number')) {
          errorMessage = t('errorPasswordNumber');
        } else if (backendMsg.includes('symbol')) {
          errorMessage = t('errorPasswordSymbol');
        } else if (backendMsg.includes('at least 8')) {
          errorMessage = t('errorPasswordLength');
        } else if (backendMsg.includes('Passwords do not match')) {
          errorMessage = t('errorPasswordsDontMatch');
        } else if (backendMsgRaw) {
          errorMessage = backendMsgRaw; // fallback untranslated
        }
        
        setError({ message: errorMessage, type: 'error' });
      }
    } finally {
      setLoading(false);
    }
  };

  const renderRequestStep = () => (
    <>
      <TextField fullWidth required id="email" label={t('emailLabel')} value={email} onChange={(e)=>setEmail(e.target.value)} disabled={loading}/>
    </>
  );

  const renderVerifyStep = () => (
    <>
      <TextField fullWidth required id="code" label={t('verificationCodeLabel')} value={code} onChange={(e)=>setCode(e.target.value)} disabled={loading}/>
      <TextField fullWidth required type="password" id="newPassword" label={t('newPasswordLabel')} value={newPassword} onChange={(e)=>setNewPassword(e.target.value)} disabled={loading}/>
      <TextField fullWidth required type="password" id="confirmPassword" label={t('confirmPasswordLabel')} value={confirmPassword} onChange={(e)=>setConfirmPassword(e.target.value)} disabled={loading}/>
    </>
  );

  return (
    <AuthLayout>
      <Paper
        elevation={3}
        sx={{
          p: { xs: 2, sm: 4 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          backgroundColor: (theme) => theme.palette.mode === 'dark' 
            ? 'rgba(30, 30, 30, 0.9)' 
            : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: 2,
          width: { xs: '92%', sm: '100%' },
          position: 'relative'
        }}
      >
        <Box
          component="img"
          src="/logo.png"
          alt="D-Unit Logo"
          sx={{
            width: { xs: '120px', sm: '140px' },
            height: 'auto',
            objectFit: 'contain',
            mb: 4,
            filter: 'drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.1))'
          }}
        />

        <Typography component="h1" variant="h5" sx={{ mb: 2 }}>
          {t('forgotPasswordTitle')}
        </Typography>

        <Typography variant="body2" color="textSecondary" align="center" sx={{ mb: 3 }}>
          {t('forgotPasswordSubtitle')}
        </Typography>

        {error.message && (
          <Alert 
            severity={error.type}
            sx={{ width: '100%', mb: 2 }}
          >
            {error.message}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
            {success}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }} noValidate>
          {step === 'request' ? renderRequestStep() : renderVerifyStep()}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            disabled={loading}
            sx={{
              mt: 2,
              mb: 2,
              backgroundColor: '#2196f3',
              '&:hover': {
                backgroundColor: '#1976d2',
              },
              '&:disabled': {
                backgroundColor: 'rgba(33, 150, 243, 0.5)',
              }
            }}
          >
            {loading ? <CircularProgress size={24} /> : step === 'request' ? t('continueButton') : t('resetPasswordButton')}
          </Button>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Link 
              component="button"
              variant="body2" 
              onClick={() => navigate('/login')}
              sx={{ 
                color: '#00A3FF',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'none',
                  color: '#0082CC'
                }
              }}
            >
              {t('backToLoginLink')}
            </Link>
          </Box>
        </Box>
      </Paper>
    </AuthLayout>
  );
}

export default ForgotPassword; 