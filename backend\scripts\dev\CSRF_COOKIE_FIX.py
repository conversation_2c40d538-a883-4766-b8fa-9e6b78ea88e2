# CSRF Cookie Fix for Cross-Origin Requests
# This fix should be applied to backend/routes/auth.py

# BEFORE (lines in get_csrf_token function):
"""
        # Set secure cookie if double submit is enabled
        if double_submit_enabled:
            cookie_options = {
                "httponly": True,
                "secure": True,  # Ensure HTTPS in production
                "samesite": "strict",  # <-- THIS BLOCKS CROSS-ORIGIN
                "max_age": expires_in
            }
"""

# AFTER (replace the above with):
"""
        # Set secure cookie if double submit is enabled
        if double_submit_enabled:
            # Check if this is a cross-origin request
            origin = request.headers.get("Origin", "")
            frontend_urls = [
                "https://d37am3rrp4a9iv.cloudfront.net",
                "https://d-unit.com",
                "https://www.d-unit.com"
            ]
            
            is_cross_origin = origin and origin not in frontend_urls
            
            cookie_options = {
                "httponly": True,
                "secure": True,  # Ensure HTTPS in production
                "samesite": "none" if is_cross_origin else "strict",  # Allow cross-origin
                "max_age": expires_in
            }
            
            # For cross-origin, we may also need to set the domain
            if is_cross_origin:
                # Don't set domain for cross-origin to let browser handle it
                pass
"""

# EXPLANATION:
# 1. SameSite=strict blocks cross-site cookies completely
# 2. SameSite=none allows cross-site cookies (requires Secure=true)
# 3. Cross-origin requests between CloudFront domains need SameSite=none

# ALTERNATIVE SIMPLER FIX:
# Just change "samesite": "strict" to "samesite": "lax"
# Lax allows cross-site GET requests and same-site POST requests