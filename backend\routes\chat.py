import logging
import time
import json
import os
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Form, File, UploadFile, Request
from typing import List, Optional
from bson import ObjectId
from pymongo.results import UpdateResult, DeleteResult, InsertOneResult

from config.database import db_analysis
from config.settings import get_settings
from models.user import User
from models.chat import ChatMessage, ChatRequest, FeedbackRequest, ChatSession
from services.auth import get_current_active_user, verify_user_can_access_store
from services.chat import (
    detect_language, title_case, get_chat_response, 
    store_context_cache, datetime_handler, generate_chat_summary,
    track_openai_cost_per_model, check_budget_before_request,
    get_openai_cost_for_tokens, check_store_tier_limits
)
from services.store import build_complete_store_context, get_store_credits, deduct_store_credits  # Credits helpers
from utils.serialization import serialize_mongo_doc
from openai.types.chat import ChatCompletionMessageParam, ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam, ChatCompletionAssistantMessageParam
from openai import AsyncOpenAI
from services.meta_permissions import (
    get_data_point_permission_status,
    track_permission_change,
    get_permission_history
)
from models.meta import MetaPage

# Configure logging
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Chat collection reference
chat_collection = db_analysis["store_chats"]

# --- NEW: Define Personas and Intent Classification ---
PERSONA_PROMPTS = {
    "Strategy": """You are a Chief Strategy Officer with 30 years of experience in e-commerce. Analyze the user's query and the provided context from a high-level strategic perspective. Focus on long-term growth, competitive positioning, market trends, and profitability. Provide actionable, data-driven strategic recommendations.""",
    "Marketing": """You are a Marketing CEO specializing in e-commerce brands. Analyze the user's query and the provided context through a marketing lens. Focus on customer acquisition, branding, campaign effectiveness, social media engagement, SEO, and customer retention. Provide creative and data-backed marketing tactics.""",
    "Operations": """You are an E-commerce Operations Expert. Analyze the user's query and the provided context focusing on operational efficiency. Address topics like inventory management, shipping logistics, customer service, website performance, and payment processing. Provide practical advice to streamline operations.""",
    "Data Analysis": """You are a Senior Data Analyst specializing in e-commerce data. Analyze the user's query and the provided context with a focus on data interpretation. Explain metrics, identify trends, highlight statistical significance, and suggest further data exploration based *only* on the provided data.""",
    "General": """You are a helpful and knowledgeable D-Unit AI assistant. Answer the user's query clearly and concisely based on the provided context. If the query requires specific expertise, frame your answer helpfully but avoid speculation beyond the data."""
}

DEFAULT_PERSONA = "General"

async def _get_question_intent(user_message: str) -> str:
    """
    Uses a quick LLM call to classify the user's message intent.
    """
    try:
        # Initialize Async Client
        settings = get_settings()
        openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
        classification_model = settings.OPENAI_DEFAULT_MODEL  # Use configured model
        categories = ["Strategy", "Marketing", "Operations", "Data Analysis", "General"]
        
        system_prompt = f"Classify the user's primary question intent into one of the following categories: {', '.join(categories)}. Respond with ONLY the category name."
        
        response = await openai_client.chat.completions.create(
             model=classification_model,
             messages=[
                 {"role": "system", "content": system_prompt},
                 {"role": "user", "content": user_message}
             ],
             max_tokens=10,
             temperature=0.1 
        )
        
        # Safely handle the response content
        if response.choices and response.choices[0].message and response.choices[0].message.content:
            intent = response.choices[0].message.content.strip()
            # Validate the response is one of the expected categories
            if intent in categories:
                logger.debug(f"Classified intent for '{user_message[:50]}...' as: {intent}")
                return intent
        
        logger.warning(f"Invalid or empty response from classification. Defaulting to {DEFAULT_PERSONA}.")
        return DEFAULT_PERSONA
        
    except Exception as e:
        logger.error(f"Error during question intent classification: {e}", exc_info=True)
        return DEFAULT_PERSONA # Default on error

# --- END NEW ---

# Create router
router = APIRouter(prefix="/api/chat", tags=["chat"])

def convert_to_chat_messages(messages: List[dict]) -> List[ChatCompletionMessageParam]:
    converted_messages = []
    for msg in messages:
        role = msg["role"]
        content = msg["content"]
        if role == "system":
            converted_messages.append(ChatCompletionSystemMessageParam(role=role, content=content))
        elif role == "user":
            converted_messages.append(ChatCompletionUserMessageParam(role=role, content=content))
        elif role == "assistant":
            converted_messages.append(ChatCompletionAssistantMessageParam(role=role, content=content))
    return converted_messages

@router.post("/{store_id}")
async def chat(
    background_tasks: BackgroundTasks,
    store_id: str,
    request: Request,
    message: Optional[str] = Form(None),
    conversation_id: Optional[str] = Form(None),
    mode: Optional[str] = Form(None),
    image: Optional[UploadFile] = File(None),
    chat_request: Optional[ChatRequest] = None,
    force_refresh: bool = False,
    current_user: User = Depends(verify_user_can_access_store)
):
    # Add the assertion here
    assert store_context_cache is not None, "Store context cache not initialized"

    try:
        # Enhanced security validation for chat requests
        await _validate_chat_security(request, message, chat_request, store_id)
        
        # Handle both form data and JSON requests by combining parameters
        # If form data is provided, use those values, otherwise use chat_request object
        message_content = message if message is not None else (chat_request.message if chat_request else None)
        chat_conversation_id = conversation_id if conversation_id is not None else (chat_request.conversation_id if chat_request else None)
        chat_mode = mode if mode is not None else (chat_request.mode if chat_request else None)
        
        # Ensure we have a message
        if not message_content:
            raise HTTPException(
                status_code=400,
                detail="Message is required"
            )

        # === Credits pre-check START ===
        deduction_amount = 10_000
        if chat_mode:
            mode_l = chat_mode.lower()
            if mode_l in ("think",):
                deduction_amount = 20_000
            elif mode_l in ("deep search", "deeper search", "deepsearch", "deepersearch"):
                deduction_amount = 40_000

        current_credits = await get_store_credits(store_id)
        if current_credits < deduction_amount:
            raise HTTPException(status_code=402, detail="Insufficient credits")
        # === Credits pre-check END ===

        # Cost enforcement before AI operations
        estimated_cost = await _estimate_chat_cost(message_content, chat_mode)
        if not await check_budget_before_request(store_id, estimated_cost):
            raise HTTPException(
                status_code=429,
                detail="Daily budget limit exceeded for AI operations"
            )
        
        # Check store tier limits
        if not await check_store_tier_limits(store_id, estimated_cost):
            raise HTTPException(
                status_code=403,
                detail="Operation exceeds your current plan limits"
            )

        # Get store context from cache or build new one
        async with store_context_cache.get_lock(store_id):
            logger.debug(f"Attempting to get store context from cache for store {store_id} (force_refresh={force_refresh})")
            store_context = await store_context_cache.get(store_id, force_refresh=force_refresh)
            logger.debug(f"Result of cache get for store {store_id}: {repr(store_context)[:300]}")
            if not store_context:
                logger.info(f"Building new context for store {store_id} (forced: {force_refresh})")
                try:
                    store_context = await build_complete_store_context(store_id)
                    logger.debug(f"Result of build_complete_store_context for store {store_id}: {repr(store_context)[:300]}")
                    await store_context_cache.set(store_id, store_context)
                    logger.debug(f"Set new store context in cache for store {store_id}")
                except Exception as build_exc:
                    logger.error(f"Exception during build/set of store context for store {store_id}: {build_exc}", exc_info=True)
                    store_context = None
            # Start background sync
            background_tasks.add_task(store_context_cache.start_background_sync, store_id)
        logger.debug(f"Final store_context before 404 check for store {store_id}: {repr(store_context)[:300]}")

        if not store_context:
            logger.warning(f"Proceeding with empty store context for store {store_id} due to missing or invalid context.")
            store_context = {}
            
        # IMPORTANT: Serialize any MongoDB ObjectId fields to strings
        try:
            store_context = serialize_mongo_doc(store_context)
        except Exception as serialization_error:
            logger.warning(f"Error serializing store context: {serialization_error}")
            # Continue with original context, but we may encounter issues later
            
        # Process image if provided
        image_data = None
        if image:
            # Read the uploaded image data
            image_data = await image.read()
            logger.debug(f"Received image with content type {image.content_type} and size {len(image_data)} bytes")

        # Find or create conversation
        existing_conversation = None
        if chat_conversation_id:
            # Simplify query: Always look up using the 'conversation_id' field if an ID is provided.
            # The frontend sends the ID it received previously, which corresponds to this field.
            query = {"conversation_id": chat_conversation_id, "store_id": store_id} 
            logger.debug(f"Attempting to find existing conversation with query: {query}")
            existing_conversation = await chat_collection.find_one(query)
            
            if existing_conversation:
                 logger.info(f"Found existing conversation with conversation_id: {chat_conversation_id} (_id: {existing_conversation['_id']})")
            else:
                 # If ID provided but not found, log warning and treat as a new conversation
                 logger.warning(f"Conversation ID {chat_conversation_id} provided but not found for store {store_id}. Creating new conversation.")
                 chat_conversation_id = None # Force creation of a new conversation

        # Initialize variables for the response
        response_data = {}
        now_utc = datetime.now(timezone.utc)

        if not existing_conversation:
            # === Logic for NEW Conversation ===
            logger.info("Creating a new conversation.")
            conversation_id_to_use = str(ObjectId())
            
            # Generate title using OpenAI summarization
            generated_title = None
            try:
                # Call the summarization function
                generated_title = await generate_chat_summary(message_content, logger)
            except Exception as e:
                # Log error if generate_chat_summary raises an unexpected exception
                logger.error(f"Error calling generate_chat_summary: {e}", exc_info=True)

            # Fallback logic: Use first 5 words if summary generation fails
            if not generated_title:
                logger.warning("Failed to generate summary via OpenAI. Falling back to first 5 words.")
                words = message_content.split()
                generated_title = " ".join(words[:5])
                if len(words) > 5:
                    generated_title += "..."
                if not generated_title:  # Handle empty initial prompt
                    generated_title = "New Chat"

            # Prepare initial messages
            user_message = {
                "role": "user",
                "content": message_content,
                "created_at": now_utc
            }
            
            # Get AI response based *only* on the first user message
            ai_response = await get_chat_response(
                messages=[user_message], # Pass only the first user message
                store_analysis=store_context,
                mode=chat_mode,
                image_data=image_data
            )
            
            # Handle new response format (dictionary with message and context_documents)
            context_documents = [] # Initialize context_documents for the response
            if isinstance(ai_response, dict) and "message" in ai_response:
                # Extract context documents if available
                context_documents = ai_response.get("context_documents", [])
                # Use the message part as the actual ai_response for content
                ai_response = ai_response["message"]
            elif not isinstance(ai_response, str):
                # If it's not a dict with 'message' and not a string, log an error and default
                logger.error(f"Unexpected ai_response format: {type(ai_response)}. Content: {ai_response}")
                ai_response = "Sorry, I encountered an issue processing the response."
            
            ai_message = {
                "role": "assistant",
                "content": ai_response, # Now ai_response is guaranteed to be a string
                "created_at": datetime.now(timezone.utc) # Use current time for AI message
            }

            # Create the complete new conversation document data
            new_conversation_data = {
                "_id": ObjectId(conversation_id_to_use),
                "conversation_id": conversation_id_to_use,
                "title": generated_title,
                "full_title": message_content, # Use full message initially
                "user_id": current_user.email,
                "store_id": store_id,
                "language": detect_language(message_content),
                "messages": [user_message, ai_message], # Include both messages
                "created_at": now_utc, # Use the timestamp from user message creation
                "updated_at": ai_message["created_at"], # Use the timestamp from AI message creation
                "full_user_prompt": message_content
            }
            
            # Validate and insert the complete document
            try:
                new_conversation = ChatSession(**new_conversation_data)
                insert_result: InsertOneResult = await chat_collection.insert_one(
                    new_conversation.dict(by_alias=True)
                )
                if not insert_result.inserted_id:
                     raise HTTPException(status_code=500, detail="Failed to create new conversation in database")
                logger.info(f"Successfully inserted new conversation with _id: {insert_result.inserted_id}")

                # Prepare response data from the newly created conversation
                response_data = {
                    "message": ai_response, # Ensure this uses the extracted string
                    "conversation_id": conversation_id_to_use,
                    "title": generated_title,
                    "full_user_prompt": message_content,
                    "meta_permission_error": False, # Assuming no error for new chat flow here
                    "missing_permissions": [],       # Assuming no error for new chat flow here
                    "context_documents": context_documents # Add the extracted documents
                }
                
                # === Credits deduction for NEW conversation ===
                if not await deduct_store_credits(store_id, deduction_amount):
                    # Retry once in case of race condition
                    if not await deduct_store_credits(store_id, deduction_amount):
                        logger.error("Credit deduction failed for store %s after chat", store_id)
                        raise HTTPException(status_code=409, detail="Credit deduction failed, please retry")

            except Exception as e:
                logger.error(f"Error inserting new conversation or validation failed: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail="Failed to process new conversation.")

        else:
            # === Logic for EXISTING Conversation ===
            logger.info(f"Updating existing conversation: {existing_conversation['_id']}")
            # Use the conversation_id field from the found document for the response
            conversation_id_to_use = existing_conversation["conversation_id"]

            # Prepare user message
            user_message = {
                "role": "user",
                "content": message_content,
                "created_at": now_utc
            }

            # Prepare message list for AI (existing + new user message)
            messages_for_api = [msg for msg in existing_conversation.get("messages", [])]
            messages_for_api.append(user_message)

            # Get AI response
            ai_response = await get_chat_response(
                messages=messages_for_api, 
                store_analysis=store_context,
                mode=chat_mode,
                image_data=image_data
            )

            # Handle possible meta permissions error (special case)
            meta_permission_error = False
            missing_permissions = []
            
            # Handle new response format (dictionary with message and context_documents)
            context_documents = [] # Initialize context_documents for the response
            if isinstance(ai_response, dict) and "message" in ai_response:
                # Extract context documents if available
                context_documents = ai_response.get("context_documents", [])
                # Use the message part as the actual ai_response for content
                ai_response = ai_response["message"]
            elif not isinstance(ai_response, str):
                # If it's not a dict with 'message' and not a string, log an error and default
                logger.error(f"Unexpected ai_response format: {type(ai_response)}. Content: {ai_response}")
                ai_response = "Sorry, I encountered an issue processing the response."
            
            # Continue with the existing Meta error detection
            if isinstance(ai_response, str) and "Meta data" in ai_response and "permissions" in ai_response:
                meta_permission_error = True
                missing_permissions = ["instagram_basic", "instagram_content_publish"] # Example

            # Prepare AI message
            ai_message = {
                "role": "assistant",
                "content": ai_response, # Now ai_response is guaranteed to be a string
                "created_at": datetime.now(timezone.utc) # Use current time for AI message
            }

            # Update the conversation in the database using $push
            full_title = None # Check if title needs update (optional logic)
            # ... (keep existing full_title update logic if needed, adapt it here)

            try:
                target_id = existing_conversation["_id"] # Use the ObjectId directly
                
                # Prepare the update operation using $push for messages and $set for others
                update_operation = {
                     "$push": {"messages": {"$each": [user_message, ai_message]}},
                     "$set": {"updated_at": ai_message["created_at"]} # Update timestamp
                }
                
                # Add full_title to $set if it needs updating
                # if full_title:
                #      update_operation["$set"]["full_title"] = full_title
                     
                logger.debug(f"Update operation for existing _id {target_id}: {update_operation}")

                update_result: UpdateResult = await chat_collection.update_one(
                    {"_id": target_id},
                    update_operation
                )

                logger.info(f"Update result for existing _id {target_id}: Matched={update_result.matched_count}, Modified={update_result.modified_count}")
                if update_result.matched_count == 0:
                     logger.warning(f"Update failed to match existing document with _id {target_id}")
                elif update_result.modified_count == 0:
                     logger.warning(f"Update matched document with _id {target_id} but did not modify it.")

                # Prepare response data for existing conversation
                response_data = {
                    "message": ai_response, # Ensure this uses the extracted string
                    "conversation_id": conversation_id_to_use, # Use the correct existing ID
                    "title": existing_conversation.get("title"), # Keep existing title
                    "full_user_prompt": existing_conversation.get("full_user_prompt"), # Keep existing prompt
                    "meta_permission_error": meta_permission_error,
                    "missing_permissions": missing_permissions,
                    "context_documents": context_documents # Add the extracted documents
                }

                # === Credits deduction for EXISTING conversation ===
                if not await deduct_store_credits(store_id, deduction_amount):
                    if not await deduct_store_credits(store_id, deduction_amount):
                        logger.error("Credit deduction failed for store %s after chat", store_id)
                        raise HTTPException(status_code=409, detail="Credit deduction failed, please retry")

            except Exception as e:
                # Corrected logging: use conversation_id_to_use which is available in this scope
                logger.error(f"Error updating existing conversation with conversation_id {conversation_id_to_use}: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail="Failed to update conversation.")

        # Return the response
        return response_data

    except HTTPException as http_exc:
        logger.error(f"HTTP Exception in chat endpoint: {http_exc.detail}", exc_info=True)
        raise http_exc # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error during chat processing for store {store_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An internal error occurred while processing your chat message."
        )

@router.get("/history/{store_id}")
async def get_chat_history(
    store_id: str, 
    current_user: User = Depends(verify_user_can_access_store)
) -> List[ChatSession]: # <-- Use correct model name ChatSession
    try:
        # Checklist Step 3: Combine primary and alternate emails
        all_associated_emails = [current_user.email]
        if current_user.alternate_emails: # Safely check if alternate_emails exists and is not empty
            all_associated_emails.extend(current_user.alternate_emails)
        # Remove potential duplicates 
        all_associated_emails = list(set(all_associated_emails)) 
        logger.debug(f"Fetching chat history for user emails: {all_associated_emails} in store {store_id}") # Added logging

        # If admin, fetch all conversations for the store regardless of user_id
        if current_user.role == "admin":
            logger.info(f"Admin {current_user.email} fetching ALL chat history for store {store_id}")
            conversations_cursor = chat_collection.find({
                "store_id": store_id
            }).sort("updated_at", -1)
        else:
            # Non-admin users restricted to their emails (incl. alternates)
            conversations_cursor = chat_collection.find({
                "store_id": store_id,
                "user_id": {"$in": all_associated_emails}
            }).sort("updated_at", -1)
        
        # Convert cursor to list and validate with Pydantic model
        # Ensure full_user_prompt is included
        conversations_list = []
        async for conv in conversations_cursor:
            # Ensure necessary fields exist, provide defaults if needed
            conv_data = {
                **conv,
                'id': str(conv.get('_id')), # Add string id for Pydantic
                'title': conv.get('title', 'Untitled Chat'),
                'full_user_prompt': conv.get('full_user_prompt', None), # Ensure field is present
                'messages': conv.get('messages', []) # Ensure messages field is present
            }
            try:
                # Use correct model name
                validated_conv = ChatSession(**conv_data)
                conversations_list.append(validated_conv)
            except Exception as validation_error:
                logger.warning(f"Validation error loading chat history item {conv.get('_id')}: {validation_error}")
                # Optionally skip this item or use default values

        return conversations_list

    except Exception as e:
        logger.error(f"Error fetching chat history for store {store_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An error occurred while fetching chat history."
        )

@router.put("/{conversation_id}/rename")
async def rename_chat(
    conversation_id: str,
    request: dict,
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Get the chat to verify ownership
        # First try with the conversation_id as a string
        chat = await chat_collection.find_one({"conversation_id": conversation_id})
        
        # If not found, check if the conversation_id might be stored as the MongoDB _id
        if not chat:
            # Try to convert the string to ObjectId (will raise an exception if invalid)
            try:
                obj_id = ObjectId(conversation_id)
                chat = await chat_collection.find_one({"_id": obj_id})
                # If found by _id, use _id for updates later
                using_object_id = True
                
                # If still not found, raise 404
                if not chat:
                    raise HTTPException(status_code=404, detail="Chat not found")
            except Exception:
                # Not a valid ObjectId
                using_object_id = False
                # Chat truly not found
                raise HTTPException(status_code=404, detail="Chat not found")
        else:
            using_object_id = False
        
        # Chat exists at this point, verify user owns this chat
        if chat.get("user_id") != current_user.email:
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to modify this chat"
            )
        
        # Update the chat title with the appropriate query
        if using_object_id:
            result = await chat_collection.update_one(
                {"_id": ObjectId(conversation_id)},
                {
                    "$set": {
                        "title": request.get("title"),
                        "full_title": request.get("title"),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        else:
            result = await chat_collection.update_one(
                {"conversation_id": conversation_id},
                {
                    "$set": {
                        "title": request.get("title"),
                        "full_title": request.get("title"),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=404, detail="Chat not found or not modified")
        
        return {"message": "Chat renamed successfully"}
        
    except Exception as e:
        logger.error(f"Error renaming chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{conversation_id}")
async def delete_chat(
    conversation_id: str,
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Get the chat to verify ownership
        # First try with the conversation_id as a string
        chat = await chat_collection.find_one({"conversation_id": conversation_id})
        
        # If not found, check if the conversation_id might be stored as the MongoDB _id
        if not chat:
            # Try to convert the string to ObjectId (will raise an exception if invalid)
            try:
                obj_id = ObjectId(conversation_id)
                chat = await chat_collection.find_one({"_id": obj_id})
                # If found by _id, use _id for deletion later
                using_object_id = True
                
                # If still not found, raise 404
                if not chat:
                    raise HTTPException(status_code=404, detail="Chat not found")
            except Exception:
                # Not a valid ObjectId
                using_object_id = False
                # Chat truly not found
                raise HTTPException(status_code=404, detail="Chat not found")
        else:
            using_object_id = False
        
        # Chat exists at this point, verify user owns this chat
        if chat.get("user_id") != current_user.email:
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to delete this chat"
            )
        
        # Delete the chat with the appropriate query
        if using_object_id:
            result = await chat_collection.delete_one({"_id": ObjectId(conversation_id)})
        else:
            result = await chat_collection.delete_one({"conversation_id": conversation_id})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Chat not found or not deleted")
        
        return {"message": "Chat deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{store_id}/feedback")
async def submit_feedback(
    store_id: str,
    feedback_request: FeedbackRequest,
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        logger.info(f"Received feedback for store {store_id} from user {current_user.email}")

        # Get current timestamp
        timestamp = datetime.now(timezone.utc)
        
        # Update the store document in global_analysis
        result = await db_analysis["global_analysis"].update_one(
            {"_id": store_id},
            {
                "$push": {
                    "feedback": {
                        "text": feedback_request.feedback,
                        "created_at": timestamp,
                        "user_email": current_user.email
                    }
                }
            }
        )
        
        if result.modified_count == 0:
            raise HTTPException(
                status_code=404,
                detail="Store not found or feedback not saved"
            )
            
        logger.info(f"Feedback submitted for store ID: {store_id}")
        return {"message": "Feedback submitted successfully"}
        
    except Exception as e:
        logger.error(f"Error submitting feedback: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit feedback: {str(e)}"
        )

@router.post("/{store_id}/refresh_context")
async def refresh_store_context(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Force refresh of the store context for the given store ID"""
    try:
        logger.info(f"User {current_user.email} requested context refresh for store {store_id}")

        # Invalidate the cache for this store
        await store_context_cache.invalidate(store_id)
        
        # Force a rebuild of the context
        async with store_context_cache.get_lock(store_id):
            store_context = await build_complete_store_context(store_id)
            await store_context_cache.set(store_id, store_context)
            
        return {"status": "success", "message": f"Context refreshed for store {store_id}"}
    except Exception as e:
        logger.error(f"Error refreshing context for store {store_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error refreshing context: {str(e)}"
        )

async def _validate_chat_security(request: Request, message: Optional[str], chat_request: Optional[ChatRequest], store_id: str):
    """Validate chat request for security threats"""
    try:
        from utils.security_utils import extract_client_ip, detect_attack_patterns_string
        
        client_ip = extract_client_ip(request)
        
        # Get message content to check
        message_content = message
        if chat_request and hasattr(chat_request, 'message'):
            message_content = chat_request.message
        
        # Check for malicious patterns in chat content
        if message_content:
            threats = detect_attack_patterns_string(message_content)
            if threats:
                logger.warning(f"Malicious chat content detected from {client_ip}: {threats}")
                raise HTTPException(
                    status_code=400,
                    detail="Message contains potentially malicious content"
                )
        
        # Check rate limiting (basic implementation)
        # In production, this would use the rate limiter middleware
        
        return True
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat security validation error: {e}")
        return True  # Allow on error to avoid blocking legitimate requests

async def _estimate_chat_cost(message_content: str, mode: Optional[str] = None) -> float:
    """Estimate cost for chat operation based on content and mode"""
    try:
        # Base token estimation (rough approximation: 1 token ≈ 4 characters)
        base_tokens = len(message_content) / 4 if message_content else 100
        
        # Mode-specific multipliers for complexity
        mode_multipliers = {
            "simple": 0.5,
            "detailed": 1.5,
            "analysis": 2.0,
            "report": 3.0
        }
        
        # Handle None mode parameter
        multiplier = mode_multipliers.get(mode or "simple", 1.0)
        estimated_tokens = int(base_tokens * multiplier)
        
        # Use default model cost (GPT-4 for chat) - Note: this function IS async
        estimated_cost = await get_openai_cost_for_tokens(settings.OPENAI_DEFAULT_MODEL, estimated_tokens, estimated_tokens // 2)
        
        return estimated_cost
        
    except Exception as e:
        logger.error(f"Failed to estimate chat cost: {e}")
        return 0.01  # Default fallback cost





