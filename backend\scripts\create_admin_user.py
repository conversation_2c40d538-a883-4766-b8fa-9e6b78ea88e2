#!/usr/bin/env python3
"""
Create Admin User Script for D-Unit
Creates an admin user in the database with proper role permissions
"""

import asyncio
import bcrypt
import sys
import os
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import db_main

async def create_admin_user():
    """
    Create an admin user in the database
    """
    print("🔧 D-Unit Admin User Creation Script")
    print("=" * 50)
    
    # Get admin details
    admin_email = input("Enter admin email: ").strip()
    if not admin_email:
        print("❌ Email is required")
        return
    
    admin_password = input("Enter admin password: ").strip()
    if not admin_password:
        print("❌ Password is required")
        return
    
    admin_name = input("Enter admin name (optional): ").strip() or "Admin User"
    
    try:
        # Check if user already exists
        existing_user = await db_main["store_users"].find_one({"email": admin_email})
        if existing_user:
            print(f"⚠️  User with email {admin_email} already exists")
            update = input("Update existing user to admin? (y/N): ").strip().lower()
            if update == 'y':
                # Update existing user to admin
                result = await db_main["store_users"].update_one(
                    {"email": admin_email},
                    {
                        "$set": {
                            "role": "admin",
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                if result.modified_count > 0:
                    print(f"✅ Successfully updated {admin_email} to admin role")
                else:
                    print("❌ Failed to update user")
                return
            else:
                print("❌ Admin user creation cancelled")
                return
        
        # Hash the password
        print("🔐 Hashing password...")
        password_bytes = admin_password.encode('utf-8')
        salt = bcrypt.gensalt()
        hashed_password = bcrypt.hashpw(password_bytes, salt).decode('utf-8')
        
        # Create admin user document
        admin_user = {
            "email": admin_email,
            "pass_dunit": hashed_password,
            "name": admin_name,
            "role": "admin",  # Single role field
            "roles": ["admin"],  # Array roles field for compatibility
            "id_store": "admin",
            "active": 1,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "permissions": [
                "admin:read",
                "admin:write", 
                "admin:delete",
                "users:manage",
                "stores:manage",
                "security:manage",
                "analytics:admin"
            ]
        }
        
        # Insert admin user
        print("💾 Creating admin user...")
        result = await db_main["store_users"].insert_one(admin_user)
        
        if result.inserted_id:
            print("✅ Admin user created successfully!")
            print(f"📧 Email: {admin_email}")
            print(f"👤 Name: {admin_name}")
            print(f"🔑 Role: admin")
            print(f"🆔 User ID: {result.inserted_id}")
            print("\n🚀 You can now login and access the admin dashboard at /admin")
        else:
            print("❌ Failed to create admin user")
            
    except Exception as e:
        print(f"❌ Error creating admin user: {str(e)}")
        import traceback
        traceback.print_exc()

async def list_admin_users():
    """
    List all admin users in the database
    """
    print("📋 Current Admin Users:")
    print("=" * 30)
    
    try:
        # Find all admin users
        admin_users = await db_main["store_users"].find({
            "$or": [
                {"role": "admin"},
                {"roles": {"$in": ["admin"]}}
            ]
        }).to_list(None)
        
        if not admin_users:
            print("❌ No admin users found")
        else:
            for i, user in enumerate(admin_users, 1):
                print(f"{i}. {user.get('name', 'Unknown')} ({user.get('email')})")
                print(f"   Role: {user.get('role', 'Not set')}")
                print(f"   Created: {user.get('created_at', 'Unknown')}")
                print(f"   Active: {'Yes' if user.get('active') else 'No'}")
                print()
                
    except Exception as e:
        print(f"❌ Error listing admin users: {str(e)}")

async def main():
    """
    Main function
    """
    print("D-Unit Admin Management")
    print("=" * 30)
    print("1. Create new admin user")
    print("2. List existing admin users")
    print("3. Exit")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        await create_admin_user()
    elif choice == "2":
        await list_admin_users()
    elif choice == "3":
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    asyncio.run(main())