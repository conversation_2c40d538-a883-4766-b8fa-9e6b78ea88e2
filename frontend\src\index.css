@tailwind base;
@tailwind components;
@tailwind utilities;

/* Inter font optimization */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZs.woff) format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZs.woff) format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fMZs.woff) format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYMZs.woff) format('woff');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYMZs.woff) format('woff');
}

/* Apply Inter font to all elements */
html, body, #root {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Make sure headings use the correct font weight */
h1, h2, h3, h4, h5, h6 {
  font-weight: 500;
  letter-spacing: -0.02em;
}

/* Adjust paragraph spacing and readability */
p {
  line-height: 1.6;
  letter-spacing: -0.01em;
}

/* Apply sleek styles to buttons and interactive elements */
button, 
a, 
input, 
select, 
textarea {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-weight: 500;
}

/* Basic styles for ReactMarkdown content */
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
  font-size: 0.95rem; /* Match input field size */
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.2em;
  margin-bottom: 0.8em;
  font-weight: 600;
}

.markdown-content p {
  margin-bottom: 0.8em; /* Slightly reduce paragraph spacing */
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.2em; /* Adjust list indent */
  margin-bottom: 0.8em;
  padding-left: 1.2em;
}

.markdown-content li {
  margin-bottom: 0.2em; /* Reduce list item spacing */
}

.markdown-content code {
  background-color: rgba(0, 0, 0, 0.06); /* Slightly darker code background */
  padding: 0.2em 0.4em;
  border-radius: 4px; /* Match other rounding */
  font-family: monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  background-color: rgba(0, 0, 0, 0.06);
  padding: 0.8em 1em; /* Adjust padding */
  border-radius: 8px; /* Match button rounding */
  overflow-x: auto;
  margin-bottom: 1em;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
  margin-bottom: 1em;
}

.markdown-content blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

/* Styles for tables (complementing MUI overrides if used) */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
  display: block; /* Makes table scrollable horizontally if needed */
  overflow-x: auto; 
  border-radius: 8px; /* Round table corners */
  border-style: hidden; /* Hide outer border to rely on cell borders + radius */
  box-shadow: 0 0 0 1px #ddd; /* Use box-shadow for outer border with radius */
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #ddd; /* Light grey border */
  padding: 10px 14px; /* Increase padding slightly */
  text-align: left;
}

.markdown-content th {
  background-color: #f0f0f0; /* Lighter header */
  font-weight: 600; /* Match heading weight */
}

.markdown-content tr:nth-child(even) {
  background-color: transparent; /* Remove zebra striping if using alternating row styles elsewhere */
}

/* Round top-left/right corners of header cells */
.markdown-content th:first-child {
  border-top-left-radius: 7px; /* Slightly less than parent radius */
}
.markdown-content th:last-child {
  border-top-right-radius: 7px;
}

/* Round bottom-left/right corners of last row cells */
.markdown-content tr:last-child td:first-child {
  border-bottom-left-radius: 7px;
}
.markdown-content tr:last-child td:last-child {
  border-bottom-right-radius: 7px;
}

/* Custom Scrollbar for Message List */
:root {
  --scrollbar-track: var(--background-default, #1a1a1a);
  --scrollbar-thumb: var(--background-paper, #333);
  --scrollbar-thumb-hover: var(--background-paper-hover, #444);
  --chat-input-border-color: #D1D5DB; /* Example: Tailwind gray-300 */
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.chat-input-wrapper {
  background-color: var(--background-paper);
}

.sidebar-container {
  background-color: var(--background-default);
}

.nav-button {
  background-color: var(--background-paper);
}

.start-chat-button {
  background-color: var(--primary-main);
  color: var(--primary-contrast-text);
}

.dark {
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #1a1a1a; /* Slightly different dark background */

  /* Custom D-Unit Variables for Dark Mode */
  --primary-color-dark: #00A3FF; /* Keep primary blue */
  --secondary-color-dark: #6caeff; /* Adjust secondary blue for dark */
  --tertiary-color-dark: #a6d4ff; /* Adjust tertiary blue for dark */
  --error-color-dark: #ef5350; /* Slightly lighter red */
  --warning-color-dark: #ffa726; /* Slightly lighter orange */
  --info-color-dark: #42a5f5; /* Slightly lighter blue */
  --success-color-dark: #66bb6a; /* Slightly lighter green */

  /* Update text and background colors for dark mode if needed */
  --text-primary-dark: rgba(255, 255, 255, 0.87);
  --text-secondary-dark: rgba(255, 255, 255, 0.6);
  --background-default-dark: #121212;
  --background-paper-dark: #1e1e1e;

  /* Border color for chat input in dark mode */
  --chat-input-border-color: #4B5563; /* Example: Tailwind gray-600 */
}

/* Chat Input Container Styling */
.chat-input-container {
  border-left: 1px solid var(--chat-input-border-color);
  border-right: 1px solid var(--chat-input-border-color);
  border-radius: 12px; /* Adjust as needed for desired roundness */
  margin-top: 8px;    /* Adjust as needed for spacing from message list */
  /* Add padding if needed to prevent content touching the new border */
  /* padding-left: 8px; */
  /* padding-right: 8px; */
  /* Transition for smooth color changes */
  transition: border-color 0.3s ease;
}

/* General Body Styles */
body {
  margin: 0;
  min-width: 320px;
  overflow-x: hidden; /* Prevent horizontal scroll on all screens */
}

/* -------------------------------------------- */
/* Mobile specific adjustments */
@media (max-width: 600px) {
  body {
    overflow-x: hidden;
  }
}

