"""
Instagram cache cleanup and maintenance tasks
"""
import logging
import asyncio
from datetime import datetime, timedelta
from services.instagram_cache import InstagramCacheService
from config.database import db_analysis

logger = logging.getLogger(__name__)

async def cleanup_expired_instagram_cache():
    """
    Clean up expired Instagram cache entries
    This should be run periodically (e.g., every hour)
    """
    try:
        logger.info("Starting Instagram cache cleanup...")
        
        cleaned_count = await InstagramCacheService.cleanup_expired_cache()
        
        if cleaned_count > 0:
            logger.info(f"Instagram cache cleanup completed: {cleaned_count} entries removed")
        else:
            logger.debug("Instagram cache cleanup completed: no expired entries found")
        
        return cleaned_count
        
    except Exception as e:
        logger.error(f"Error during Instagram cache cleanup: {str(e)}")
        return 0

async def optimize_instagram_cache_collections():
    """
    Optimize Instagram cache collections by ensuring proper indexes
    """
    try:
        logger.info("Optimizing Instagram cache collections...")
        
        # Define index configurations for each cache type
        cache_types = [
            "profile_insights", "media_insights", "story_insights", 
            "account_insights", "business_discovery", "media_list",
            "hashtag_insights", "user_insights", "advertising_insights"
        ]
        
        indexes_created = 0
        
        for cache_type in cache_types:
            collection_name = f"instagram_{cache_type}_cache"
            
            try:
                # Create compound index for efficient querying
                await db_analysis[collection_name].create_index([
                    ("cache_key", 1),
                    ("cached_at", 1)
                ], background=True)
                
                # Create TTL index for automatic cleanup
                await db_analysis[collection_name].create_index(
                    "cached_at",
                    expireAfterSeconds=7200,  # 2 hours max for any cache
                    background=True
                )
                
                # Create indexes for metadata fields
                await db_analysis[collection_name].create_index("page_id", background=True)
                await db_analysis[collection_name].create_index("store_id", background=True)
                
                indexes_created += 1
                
            except Exception as e:
                logger.warning(f"Could not create indexes for {collection_name}: {str(e)}")
        
        logger.info(f"Instagram cache optimization completed: {indexes_created} collections optimized")
        return indexes_created
        
    except Exception as e:
        logger.error(f"Error during Instagram cache optimization: {str(e)}")
        return 0

async def get_instagram_cache_health_report():
    """
    Generate a health report for Instagram cache
    """
    try:
        stats = await InstagramCacheService.get_cache_stats()
        
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_entries": stats.get("total_entries", 0),
            "total_size_mb": stats.get("total_size_mb", 0),
            "oldest_entry": stats.get("oldest_entry"),
            "newest_entry": stats.get("newest_entry"),
            "collections": stats.get("collections", {}),
            "health_score": "good"  # Will be calculated below
        }
        
        # Calculate health score based on various factors
        health_issues = []
        
        # Check for oversized cache
        if report["total_size_mb"] > 500:  # 500MB threshold
            health_issues.append("cache_too_large")
        
        # Check for stale data
        if report["oldest_entry"]:
            oldest_age_hours = (datetime.utcnow() - report["oldest_entry"]).total_seconds() / 3600
            if oldest_age_hours > 24:
                health_issues.append("stale_data_detected")
        
        # Check for empty collections
        empty_collections = [
            name for name, info in report["collections"].items() 
            if info["count"] == 0
        ]
        if len(empty_collections) > 5:
            health_issues.append("many_empty_collections")
        
        # Determine overall health
        if len(health_issues) == 0:
            report["health_score"] = "excellent"
        elif len(health_issues) <= 2:
            report["health_score"] = "good"
        elif len(health_issues) <= 4:
            report["health_score"] = "fair"
        else:
            report["health_score"] = "poor"
        
        report["health_issues"] = health_issues
        
        logger.info(f"Instagram cache health report generated: {report['health_score']} health score")
        return report
        
    except Exception as e:
        logger.error(f"Error generating Instagram cache health report: {str(e)}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
            "health_score": "unknown"
        }

async def invalidate_instagram_cache_by_age(max_age_hours: int = 24):
    """
    Invalidate Instagram cache entries older than specified age
    
    Args:
        max_age_hours: Maximum age in hours before cache is invalidated
    """
    try:
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        
        total_invalidated = 0
        cache_types = [
            "profile_insights", "media_insights", "story_insights", 
            "account_insights", "business_discovery", "media_list",
            "hashtag_insights", "user_insights", "advertising_insights"
        ]
        
        for cache_type in cache_types:
            collection_name = f"instagram_{cache_type}_cache"
            
            result = await db_analysis[collection_name].delete_many({
                "cached_at": {"$lt": cutoff_time}
            })
            
            total_invalidated += result.deleted_count
            
            if result.deleted_count > 0:
                logger.debug(f"Invalidated {result.deleted_count} old {cache_type} cache entries")
        
        logger.info(f"Invalidated {total_invalidated} Instagram cache entries older than {max_age_hours} hours")
        return total_invalidated
        
    except Exception as e:
        logger.error(f"Error invalidating old Instagram cache: {str(e)}")
        return 0

# Convenience function to run all maintenance tasks
async def run_instagram_cache_maintenance():
    """
    Run all Instagram cache maintenance tasks
    """
    try:
        logger.info("Starting comprehensive Instagram cache maintenance...")
        
        # Run cleanup
        cleaned = await cleanup_expired_instagram_cache()
        
        # Optimize collections
        optimized = await optimize_instagram_cache_collections()
        
        # Generate health report
        health_report = await get_instagram_cache_health_report()
        
        # Invalidate very old entries (older than 24 hours)
        invalidated = await invalidate_instagram_cache_by_age(24)
        
        maintenance_summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "cleaned_entries": cleaned,
            "optimized_collections": optimized,
            "invalidated_old_entries": invalidated,
            "health_score": health_report.get("health_score", "unknown"),
            "total_cache_size_mb": health_report.get("total_size_mb", 0)
        }
        
        logger.info(f"Instagram cache maintenance completed: {maintenance_summary}")
        return maintenance_summary
        
    except Exception as e:
        logger.error(f"Error during Instagram cache maintenance: {str(e)}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
            "status": "failed"
        }