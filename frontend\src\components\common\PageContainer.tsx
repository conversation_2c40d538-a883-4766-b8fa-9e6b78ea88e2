import React, { useState, useEffect, ReactNode } from 'react';
import { Box, SxProps, Theme } from '@mui/material';
import { useLocation } from 'react-router-dom';

interface PageContainerProps {
  children: ReactNode;
  sx?: SxProps<Theme>;
  noPadding?: boolean;
}

/**
 * A container component that handles proper padding and margin
 * when the vertical navigation sidebar is active.
 * This ensures content doesn't get covered by the sidebar.
 */
const PageContainer: React.FC<PageContainerProps> = ({ children, sx, noPadding = false }) => {
  const [isVerticalNav, setIsVerticalNav] = useState(false);
  const location = useLocation();
  
  // Determine if on chat page
  const isChatPage = location.pathname === '/chat' || location.pathname === '/' || location.pathname.includes('/chat');

  // Listen for navigation mode changes
  useEffect(() => {
    const handleNavModeChange = (event: CustomEvent) => {
      setIsVerticalNav(event.detail.isVerticalNav);
    };

    // Initial check
    setIsVerticalNav(window.isVerticalNav || false);

    // Add event listener for navigation mode changes
    window.addEventListener('navigationModeChanged', handleNavModeChange as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('navigationModeChanged', handleNavModeChange as EventListener);
    };
  }, []);

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        // Apply right padding when vertical navigation is active
        // Different widths for different pages
        pr: isVerticalNav ? (isChatPage ? 0 : '48px') : 0,
        // Apply transition for smooth resizing
        transition: 'padding 0.3s ease-in-out, max-width 0.3s ease-in-out',
        boxSizing: 'border-box',
        ...sx
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: isChatPage ? '100%' : '1600px', // Changed from 1800px to 100% for chat page
          margin: '0 auto',
          p: noPadding ? 0 : 3,
          boxSizing: 'border-box',
          bgcolor: 'inherit',
          borderRight: 'none',
          // Remove any margin that might create gaps
          ...(isChatPage 
            ? { 
                ml: 0,
                mr: 0,
                pr: 0 
              } 
            : isVerticalNav 
              ? { mr: '48px', ml: 'auto' }
              : {}),
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default PageContainer; 
