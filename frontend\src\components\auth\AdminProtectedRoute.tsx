import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Box, CircularProgress } from '@mui/material';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * Component that protects routes, allowing access only to authenticated users with admin role.
 * If user is not authenticated, redirects to login.
 * If user is authenticated but not an admin, redirects to dashboard.
 * Shows a loading indicator while auth state is resolving.
 */
const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  // Destructure isLoading along with isAuthenticated and user
  const { isAuthenticated, user, isLoading } = useAuth();

  // Check if authentication state is still loading
  if (isLoading) {
    // Display a loading indicator while checking authentication
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // ---- Checks below only run after isLoading is false ----

  // Check if authenticated
  if (!isAuthenticated) {
    // Not authenticated, redirect to login
    return <Navigate to="/login" replace />;
  }

  // Check if user exists and has admin role
  // This check now safely assumes user state is resolved because isLoading is false
  if (!user?.role || user.role !== 'admin') {
    // Authenticated but not an admin, redirect to the standard dashboard
    return <Navigate to="/dashboard" replace />;
  }

  // User is authenticated and has admin role, render children
  return <>{children}</>;
};

export default AdminProtectedRoute; 