import logging
import random
import string
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Response, Request
from fastapi.security import OAuth2PasswordRequestForm
from passlib.context import CryptContext
from jose import JWTError, jwt
import os
import time
from bson.objectid import ObjectId
from pymongo.errors import DuplicateKeyError
from pymongo.results import UpdateResult, DeleteResult
from typing import List, Optional, Dict, Any
import re
import json
import traceback

# Configure logging early so it is available for any code executed during module import
logger = logging.getLogger("d-unit")

from config.database import db_main, db_analysis
from config.settings import get_settings
from models.user import (
    User, Token, TwoFactorResponse, Verify2FALoginRequest, 
    ForgotPasswordRequest, DUnitRegistration, VerifyDUnitRegistration,
    GoogleAuthRequest, PasswordChangeRequest, LoginResponse, Verify2FARequest,
    MetaAuthRequest, PasswordResetVerifyRequest
)
from services.auth import (
    authenticate_user, get_current_active_user, create_access_token,
    verify_google_token, verify_meta_token, create_user_object,
    get_password_hash, verify_password, invalidate_user_cache,
    get_cache_stats
)
from services.email import (
    generate_verification_code, generate_random_password,
    send_verification_email, send_password_reset_email,
    send_email_in_background,
    smtp_pool
)
from services.store import ensure_store_analysis
from services.csrf_service import csrf_service

# Import customer data trigger function
try:
    from scripts.data.update_customers_relationships import trigger_for_newly_registered_store
except ImportError:
    logger.warning("Could not import customer data trigger function")
    trigger_for_newly_registered_store = None

async def exchange_for_long_lived_token(short_lived_token: str) -> Optional[Dict[str, Any]]:
    """
    Exchange a short-lived token for a long-lived token (60 days)
    https://developers.facebook.com/docs/facebook-login/guides/access-tokens/get-long-lived
    """
    try:
        settings = get_settings()
        
        import aiohttp
        async with aiohttp.ClientSession() as session:
            url = "https://graph.facebook.com/v23.0/oauth/access_token"
            params = {
                "grant_type": "fb_exchange_token",
                "client_id": settings.META_APP_ID,
                "client_secret": settings.META_APP_SECRET,
                "fb_exchange_token": short_lived_token
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("Successfully exchanged for long-lived token")
                    return data
                else:
                    error_data = await response.json()
                    logger.error(f"Failed to exchange for long-lived token: {error_data}")
                    return None
    except Exception as e:
        logger.error(f"Error exchanging for long-lived token: {str(e)}")
        return None

async def refresh_meta_token_if_needed(store_id: str) -> Optional[str]:
    """
    Check if Meta token is expired and refresh if needed
    """
    try:
        # Get current token info from database
        store_data = await db_analysis["global_analysis"].find_one({"_id": store_id})
        if not store_data:
            logger.warning(f"No store data found for {store_id}")
            return None
            
        current_token = store_data.get("meta_access_token")
        expires_at = store_data.get("meta_token_expires_at")
        
        if not current_token:
            logger.warning(f"No Meta token found for store {store_id}")
            return None
        
        # Check if token is still valid (with 1 hour buffer)
        if expires_at:
            buffer_time = datetime.now(timezone.utc) + timedelta(hours=1)
            # Ensure expires_at is timezone-aware
            if isinstance(expires_at, datetime):
                if expires_at.tzinfo is None:
                    expires_at = expires_at.replace(tzinfo=timezone.utc)
                if expires_at > buffer_time:
                    logger.debug(f"Meta token for store {store_id} is still valid")
                    return current_token
        
        # Token is expired or about to expire, validate with Facebook
        settings = get_settings()
        import aiohttp
        async with aiohttp.ClientSession() as session:
            debug_url = f"https://graph.facebook.com/v23.0/debug_token"
            params = {
                "input_token": current_token,
                "access_token": f"{settings.META_APP_ID}|{settings.META_APP_SECRET}"
            }
            
            async with session.get(debug_url, params=params) as response:
                if response.status == 200:
                    debug_data = await response.json()
                    token_data = debug_data.get("data", {})
                    
                    if token_data.get("is_valid"):
                        # Token is still valid, update expiration if provided
                        expires_at_timestamp = token_data.get("expires_at")
                        if expires_at_timestamp:
                            new_expires_at = datetime.fromtimestamp(expires_at_timestamp, tz=timezone.utc)
                            await db_analysis["global_analysis"].update_one(
                                {"_id": store_id},
                                {"$set": {"meta_token_expires_at": new_expires_at}}
                            )
                        logger.info(f"Meta token for store {store_id} is valid, updated expiration")
                        return current_token
                    else:
                        logger.warning(f"Meta token for store {store_id} is invalid: {token_data}")
                        return None
                else:
                    logger.warning(f"Failed to debug Meta token for store {store_id}")
                    return current_token  # Return current token as fallback
        
    except Exception as e:
        logger.error(f"Error refreshing Meta token for store {store_id}: {str(e)}")
        return None

# Get settings
settings = get_settings()

# Create router
router = APIRouter(prefix="/api/auth", tags=["authentication"])

# ---------------------------------------------------------------------------
# NEW PASSWORD RESET FLOW
# ---------------------------------------------------------------------------

# Step 1: request verification code

@router.post("/password-reset/request")
async def password_reset_request(request_data: ForgotPasswordRequest):
    """Send a verification code to user's email for password reset."""
    try:
        # Find user (primary or alternate email, case-insensitive)
        email_pattern = {"$regex": f"^{re.escape(request_data.email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern}
            ]
        }
        user = await db_main["store_users"].find_one(query)
        if not user:
            raise HTTPException(status_code=404, detail="Email not found. Please register first.")

        # Ensure user already registered for D-Unit
        if not user.get("pass_dunit"):
            raise HTTPException(status_code=403, detail="You need to register for D-Unit first.")

        code = generate_verification_code()
        expiry = datetime.now(timezone.utc) + timedelta(minutes=10)

        await db_main["store_users"].update_one(
            {"_id": user["_id"]},
            {"$set": {"password_reset_pending": {"code": code, "expiry": expiry}}}
        )

        if not send_verification_email(request_data.email, code):
            raise HTTPException(status_code=500, detail="Failed to send verification email")

        return {"message": "Verification code sent to your email"}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        raise HTTPException(status_code=500, detail="Failed to process password reset request")


# Step 2: verify code and set new password

@router.post("/password-reset/verify")
async def password_reset_verify(data: PasswordResetVerifyRequest):
    try:
        # Basic password match check
        if data.new_password != data.confirm_password:
            raise HTTPException(status_code=400, detail="Passwords do not match")

        # Password strength validation (similar to change-password)
        if len(data.new_password) < 8:
            raise HTTPException(status_code=400, detail="Password must be at least 8 characters long")
        if not any(c.isupper() for c in data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one uppercase letter")
        if not any(c.isdigit() for c in data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one number")
        if not any(c in "!@#$%^&*()_+-=[]{}|;:'\",.<>/?" for c in data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one symbol")

        # Find user
        email_pattern = {"$regex": f"^{re.escape(data.email)}$", "$options": "i"}
        query = {"$or": [{"email": email_pattern}, {"alternate_emails": email_pattern}]}
        user = await db_main["store_users"].find_one(query)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        pending = user.get("password_reset_pending")
        if not pending:
            raise HTTPException(status_code=400, detail="No password reset requested")

        if data.verification_code != pending.get("code"):
            raise HTTPException(status_code=400, detail="Invalid verification code")

        expiry = pending.get("expiry")
        if expiry and datetime.now(timezone.utc) > expiry.replace(tzinfo=timezone.utc):
            raise HTTPException(status_code=400, detail="Verification code has expired")

        # Update password
        new_hash = get_password_hash(data.new_password)
        await db_main["store_users"].update_one(
            {"_id": user["_id"]},
            {"$set": {"pass_dunit": new_hash, "failed_login_count": 0, "account_locked_until": None}, "$unset": {"password_reset_pending": ""}}
        )

        # Invalidate cache
        invalidate_user_cache(user["email"])

        # Issue new access token
        store_id = str(user.get("id_store"))
        access_token = create_access_token({"sub": user["email"], "store_id": store_id})

        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Password reset verify error: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset password")
# ---------------------------------------------------------------------------

# db_main is ONLY to be used for user authentication (login, registration, password management) purposes. All other data access must use db_analysis or allowed collections.

@router.post("/token", response_model=LoginResponse)
async def login_for_access_token(
    background_tasks: BackgroundTasks,
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends()
):
    try:
        # Get user from database
        email_pattern = {"$regex": f"^{re.escape(form_data.username)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern}
            ]
        }
        user = await db_main["store_users"].find_one(query)
        if not user:
            logger.warning(f"Failed login attempt for user: {form_data.username}")
            raise HTTPException(
                status_code=401,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if account is locked
        user_obj = User.from_db(user)  # type: ignore[arg-type]
        if user_obj.is_account_locked():
            logger.warning(f"Login attempt for locked account: {form_data.username}")
            raise HTTPException(
                status_code=423,  # Locked
                detail="Account is temporarily locked due to too many failed login attempts",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify password
        if not await authenticate_user(form_data.username, form_data.password):
            # Track failed login attempt
            await _track_failed_login(user["email"], request)
            
            logger.warning(f"Failed login attempt (invalid password) for user: {form_data.username}")
            raise HTTPException(
                status_code=401,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Update security metadata on successful login
        await _update_successful_login(user["email"], request)

        # Ensure store analysis exists
        store_id = str(user["id_store"])
        await ensure_store_analysis(store_id, user["email"], user.get("name"))

        # Check if 2FA is enabled
        if user.get('two_factor_enabled', False):
            # Generate and send verification code
            code = generate_verification_code()
            expiry = datetime.now(timezone.utc) + timedelta(minutes=10)

            # Store the code and expiry
            await db_main["store_users"].update_one(
                {"email": user["email"]},  # Use the primary email from the found user document
                {
                    "$set": {
                        "login_verification_code": code,
                        "login_verification_code_expiry": expiry
                    }
                }
            )

            # Send verification code via email
            if not send_verification_email(form_data.username, code):
                raise HTTPException(
                    status_code=500,
                    detail="Failed to send verification code"
                )

            return TwoFactorResponse(
                requires_2fa=True,
                message="Please check your email for the verification code"
            )

        # If 2FA is not enabled, proceed with normal login
        access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["email"], "store_id": store_id},
            expires_delta=access_token_expires
        )
        
        return {"access_token": access_token, "token_type": "bearer"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def _track_failed_login(email: str, request: Request):
    """Track failed login attempts and implement account lockout"""
    try:
        from utils.security_utils import extract_client_ip
        
        client_ip = extract_client_ip(request)  # type: ignore[arg-type]
        current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
        
        # Get current user data
        user = await db_main["store_users"].find_one({"email": email})
        if not user:
            return
            
        failed_count = user.get("failed_login_count", 0) + 1
        
        # Security settings from config
        max_attempts = settings.BRUTE_FORCE_THRESHOLD
        lockout_minutes = settings.BRUTE_FORCE_WINDOW_MINUTES
        
        update_data = {
            "failed_login_count": failed_count,
            "last_failed_login": current_time,
            "last_login_ip": client_ip
        }
        
        # Check if account should be locked
        if failed_count >= max_attempts:
            lockout_until = datetime.now(timezone.utc) + timedelta(minutes=lockout_minutes)
            update_data["account_locked_until"] = lockout_until.strftime("%Y-%m-%d %H:%M:%S")
            
            logger.warning(f"Account locked for {email} due to {failed_count} failed attempts")
            
            # Add security event
            security_event_id = f"failed_login_{email}_{int(time.time())}"
            update_data.setdefault("security_events", []).append(security_event_id)
        
        await db_main["store_users"].update_one(
            {"email": email},
            {"$set": update_data}
        )
        
    except Exception as e:
        logger.error(f"Failed to track failed login for {email}: {e}")

async def _update_successful_login(email: str, request: Request):
    """Update user security metadata on successful login"""
    try:
        from utils.security_utils import extract_client_ip
        
        client_ip = extract_client_ip(request)  # type: ignore[arg-type]
        current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
        
        update_data = {
            "last_login_at": current_time,
            "last_login_ip": client_ip,
            "failed_login_count": 0,  # Reset on successful login
            "account_locked_until": None  # Clear any lockout
        }
        
        # Add successful login to security events
        security_event_id = f"successful_login_{email}_{int(time.time())}"
        
        await db_main["store_users"].update_one(
            {"email": email},
            {
                "$set": update_data,
                "$addToSet": {"security_events": security_event_id}
            }
        )
        
        logger.info(f"Updated security metadata for successful login: {email}")
        
    except Exception as e:
        logger.error(f"Failed to update successful login for {email}: {e}")

@router.post("/2fa/verify-login")
async def verify_login_2fa(request: Verify2FALoginRequest):
    try:
        # Get user from database
        email_pattern = {"$regex": f"^{re.escape(request.email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern}
            ]
        }
        user = await db_main["store_users"].find_one(query)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if there's a pending verification
        stored_code = user.get("login_verification_code")
        expiry = user.get("login_verification_code_expiry")

        if not stored_code or not expiry:
            raise HTTPException(status_code=400, detail="No verification code found")

        # Ensure both times are timezone-aware for comparison
        current_time = datetime.now(timezone.utc)
        expiry_time = expiry.replace(tzinfo=timezone.utc) if expiry.tzinfo is None else expiry

        if current_time > expiry_time:
            raise HTTPException(status_code=400, detail="Verification code has expired")

        if request.code != stored_code:
            raise HTTPException(status_code=400, detail="Invalid verification code")

        # Clear the verification code
        await db_main["store_users"].update_one(
            {"email": user["email"]},  # Use the primary email from the found user document
            {
                "$unset": {
                    "login_verification_code": "",
                    "login_verification_code_expiry": ""
                }
            }
        )

        # Generate access token
        access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": request.email, "store_id": str(user["id_store"])},
            expires_delta=access_token_expires
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying login 2FA: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/google")
async def google_auth(request: GoogleAuthRequest):
    try:
        logger.info("Starting Google authentication process")
        # Verify the Google token
        idinfo = verify_google_token(request.credential)
        logger.info(f"Token verified for email: {idinfo.get('email')}")

        # Get user email from token
        email = idinfo['email']
        name = idinfo.get('name', '')

        # Create case-insensitive regex pattern for the email
        email_pattern = {"$regex": f"^{re.escape(email)}$", "$options": "i"}

        # First, check if user exists in La Nube (store_users) using email or alternate_emails (case-insensitive)
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern}
            ]
        }
        # Use the same logger for consistency, noting the query now includes the pattern
        logger.info(f"Attempting to find user with query: {query}")
        existing_user = await db_main["store_users"].find_one(query)
        
        if not existing_user:
            # If still not found, log the detailed query attempt (already done in previous step)
            logger.warning(f"User lookup failed for email '{email}' using case-insensitive query: {query}")
            logger.warning(f"Email not found in La Nube (primary or alternate, case-insensitive): {email}")
            raise HTTPException(
                status_code=400,
                detail="Email not found in La Nube. You must be a La Nube user to access D-Unit."
            )
        
        # Log the found user's ID for verification if needed
        logger.info(f"Found user with ID: {existing_user['_id']} for email: {email}")

        # User exists in La Nube, now check if they have D-Unit access
        # Use the found user's primary email for subsequent operations if needed
        primary_email = existing_user['email']
        logger.info(f"Proceeding with primary email: {primary_email} for user ID: {existing_user['_id']}")

        if not existing_user.get('pass_dunit'):
            logger.info(f"User {primary_email} exists in La Nube but needs D-Unit registration")
            # Generate a secure random password for D-Unit
            dunit_password = generate_random_password()
            
            # Update user with D-Unit specific fields using their _id
            update_result: UpdateResult = await db_main["store_users"].update_one(
                {"_id": existing_user["_id"]}, # Update using unique _id
                {
                    "$set": {
                        "pass_dunit": get_password_hash(dunit_password),
                        # Ensure 'name' is updated correctly if Google provides a different one
                        "name": name or existing_user.get('name', ''), 
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S"),
                        "updated_by": "google_auth"
                    }
                }
            )
            
            if update_result.modified_count == 0:
                logger.error(f"Failed to update user {primary_email} with D-Unit access using _id: {existing_user['_id']}")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to update user with D-Unit access"
                )
            
            logger.info(f"Successfully added D-Unit access for user {primary_email}")
            
            # Trigger customer data update with PII for newly registered store
            store_id = str(existing_user["id_store"])
            if trigger_for_newly_registered_store:
                try:
                    # Run in background to not block the authentication response
                    import asyncio
                    asyncio.create_task(asyncio.to_thread(trigger_for_newly_registered_store, store_id))
                    logger.info(f"Triggered customer data update for newly registered store {store_id} (Google auth)")
                except Exception as e:
                    logger.error(f"Failed to trigger customer data update for store {store_id} (Google auth): {e}")
                    # Don't fail the authentication if customer data update fails
        
        # Ensure store analysis exists using the primary email and found store_id
        store_id = str(existing_user["id_store"])
        # Pass primary email and name from the found user document
        await ensure_store_analysis(store_id, primary_email, existing_user.get("name")) 
        
        # Create access token with proper store_id from existing user
        access_token = create_access_token(
            data={
                "sub": primary_email,  # Use primary email for the token subject
                "store_id": store_id
            },
            expires_delta=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        logger.info(f"Access token created successfully for {primary_email}")

        # Format user data using the data from the found user document
        user_data = {
            "email": existing_user["email"], # Return primary email
            "id_store": existing_user["id_store"],
            "name": existing_user.get("name", ""),
            "active": existing_user.get("active", 1), # Ensure active status is included
            "created_at": existing_user.get("created_at"),
            "updated_at": existing_user.get("updated_at")
        }

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_data
        }
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Google auth error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Authentication failed: {str(e)}"
        )

@router.post("/dunit/register")
async def register_dunit_user(registration: DUnitRegistration):
    try:
        logger.info(f"Starting D-Unit registration for email: {registration.email}")
        
        # Validate password requirements
        if len(registration.password_dunit) < 8:
            raise HTTPException(
                status_code=400, 
                detail="Password must be at least 8 characters long"
            )
        if not any(c.isupper() for c in registration.password_dunit):
            raise HTTPException(
                status_code=400, 
                detail="Password must contain at least one uppercase letter"
            )
        if not any(c.isdigit() for c in registration.password_dunit):
            raise HTTPException(
                status_code=400, 
                detail="Password must contain at least one number"
            )
        
        # Check if user exists in store_users (La Nube)
        user = await db_main["store_users"].find_one({"email": registration.email})
        if not user:
            # Try checking alternate_emails if primary email not found
            email_pattern = {"$regex": f"^{re.escape(registration.email)}$", "$options": "i"}
            user = await db_main["store_users"].find_one({"alternate_emails": email_pattern})
            
            if not user:
                logger.warning(f"Email not found in La Nube: {registration.email}")
                raise HTTPException(
                    status_code=400,
                    detail="Email not found in La Nube. You must be a La Nube user to register for D-Unit."
                )
        
        # Check if user already has a D-Unit password
        if user.get("pass_dunit"):
            logger.warning(f"User already has D-Unit registration: {registration.email}")
            raise HTTPException(
                status_code=400,
                detail="You are already registered for D-Unit. Please login instead."
            )
        
        # Generate verification code
        verification_code = generate_verification_code()
        expiry = datetime.now(timezone.utc) + timedelta(minutes=10)
        
        logger.info(f"Generated verification code for {registration.email}")
        
        # Store registration data and verification code temporarily
        update_result: UpdateResult = await db_main["store_users"].update_one(
            {"_id": user["_id"]},  # Use the _id to ensure we update the right document
            {
                "$set": {
                    "dunit_registration_pending": {
                        "name": registration.name,
                        "store_name": registration.store_name,
                        "password_dunit": get_password_hash(registration.password_dunit),
                        "verification_code": verification_code,
                        "verification_code_expiry": expiry,
                        "registration_email": registration.email  # Store which email was used for registration
                    }
                }
            }
        )
        
        if update_result.modified_count == 0:
            logger.error(f"Failed to update user document for {registration.email}")
            raise HTTPException(
                status_code=500,
                detail="Failed to process registration"
            )
        
        # Send verification email
        if not send_verification_email(registration.email, verification_code):
            logger.error(f"Failed to send verification email to {registration.email}")
            # Rollback the registration data
            await db_main["store_users"].update_one(
                {"_id": user["_id"]},  # Use _id for consistency with the update
                {"$unset": {"dunit_registration_pending": ""}}
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to send verification email"
            )
        
        logger.info(f"Successfully processed registration for {registration.email}")
        return {"message": "Verification code sent to your email"}
        
    except HTTPException as he:
        logger.error(f"HTTP Exception in D-Unit registration: {str(he)}")
        raise he
    except Exception as e:
        logger.error(f"Error in D-Unit registration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/dunit/verify-registration")
async def verify_dunit_registration(verification: VerifyDUnitRegistration):
    try:
        logger.info(f"Starting verification for email: {verification.email} with code: {verification.verification_code}")
        
        # Get user and pending registration data
        # Use $or to check both primary email and alternate emails
        email_pattern = {"$regex": f"^{re.escape(verification.email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern},
                {"selected_meta_email": email_pattern}
            ]
        }
        logger.info(f"Executing verification query: {query}")
        user = await db_main["store_users"].find_one(query)
        logger.info(f"User found result: {user is not None}")
        
        if not user:
            logger.warning(f"User not found for verification: {verification.email}")
            raise HTTPException(status_code=404, detail="User not found")
        
        pending_reg = user.get("dunit_registration_pending")
        logger.info(f"Pending registration data found: {pending_reg}")
        
        if not pending_reg:
            logger.warning(f"No pending registration found for: {verification.email} (User document found but no pending data)")
            raise HTTPException(status_code=400, detail="No pending registration found")
        
        # Check verification code
        if verification.verification_code != pending_reg.get("verification_code"):
            logger.warning(f"Invalid verification code for: {verification.email}")
            raise HTTPException(status_code=400, detail="Invalid verification code")
        
        # Check if code has expired
        expiry = pending_reg.get("verification_code_expiry")
        if expiry and datetime.now(timezone.utc) > expiry.replace(tzinfo=timezone.utc):
            logger.warning(f"Verification code expired for: {verification.email}")
            raise HTTPException(status_code=400, detail="Verification code has expired")
        
        # Get the email used for registration (could be primary or alternate)
        registration_email = pending_reg.get("registration_email", verification.email)
        
        # Update user with D-Unit specific fields
        update_result: UpdateResult = await db_main["store_users"].update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "name": pending_reg["name"],
                    "store_name": pending_reg["store_name"],
                    "pass_dunit": pending_reg["password_dunit"]
                },
                "$unset": {
                    "dunit_registration_pending": ""
                }
            }
        )
        
        if update_result.modified_count == 0:
            logger.error(f"Failed to update user document for {verification.email}")
            raise HTTPException(
                status_code=500,
                detail="Failed to complete registration"
            )
        
        # Invalidate user cache since data changed
        invalidate_user_cache(verification.email)
        
        # Trigger customer data update with PII for newly registered store
        store_id = str(user["id_store"])
        if trigger_for_newly_registered_store:
            try:
                # Run in background to not block the registration response
                import asyncio
                asyncio.create_task(asyncio.to_thread(trigger_for_newly_registered_store, store_id))
                logger.info(f"Triggered customer data update for newly registered store {store_id}")
            except Exception as e:
                logger.error(f"Failed to trigger customer data update for store {store_id}: {e}")
                # Don't fail the registration if customer data update fails
        
        # Create access token - use the email from the registration data or primary email
        access_token = create_access_token(
            data={"sub": registration_email, "store_id": str(user["id_store"])}
        )
        
        logger.info(f"Successfully completed registration for {verification.email}")
        return {
            "message": "D-Unit registration successful",
            "access_token": access_token,
            "token_type": "bearer"
        }
        
    except HTTPException as he:
        logger.error(f"HTTP Exception in D-Unit verification: {str(he)}")
        raise he
    except Exception as e:
        logger.error(f"Error in D-Unit verification: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Deprecated endpoint - guides clients to new flow
# This keeps backward compatibility without functionality

@router.post("/forgot-password")
async def deprecated_forgot_password():
    """Deprecated. Use /api/auth/password-reset/request instead."""
    raise HTTPException(
        status_code=400,
        detail="Endpoint deprecated. Use /api/auth/password-reset/request instead."
    )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Get user from database
        user = await db_main["store_users"].find_one({"email": current_user.email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Verify old password using the secure verification function
        stored_password = user.get('pass_dunit')
        if not stored_password or not verify_password(password_data.old_password, stored_password):
            raise HTTPException(status_code=400, detail="Current password is incorrect")

        # Verify new password matches confirmation
        if password_data.new_password != password_data.confirm_password:
            raise HTTPException(status_code=400, detail="New passwords do not match")

        # Validate new password requirements
        if len(password_data.new_password) < 8:
            raise HTTPException(status_code=400, detail="Password must be at least 8 characters long")
        if not any(c.isupper() for c in password_data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one uppercase letter")
        if not any(c.isdigit() for c in password_data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one number")
        
        # Add validation for symbols
        if not any(c in "!@#$%^&*()_+-=[]{}|;:'\",.<>/?" for c in password_data.new_password):
            raise HTTPException(status_code=400, detail="Password must contain at least one symbol")

        # Update the user's password
        result: UpdateResult = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {"$set": {"pass_dunit": get_password_hash(password_data.new_password)}}
        )
        
        if result.modified_count == 0:
            logger.warning(f"Password change failed for user: {current_user.email}")
            raise HTTPException(
                status_code=500,
                detail="Failed to update password"
            )
        
        # Invalidate user cache since password changed
        invalidate_user_cache(current_user.email)
        
        logger.info(f"Password successfully changed for user: {current_user.email}")
        return {"message": "Password changed successfully"}

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to change password")

@router.post("/2fa/enable")
async def enable_2fa(current_user: User = Depends(get_current_active_user)):
    try:
        # Find user and check if they exist
        user = await db_main["store_users"].find_one({"email": current_user.email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if 2FA is already enabled
        if user.get("two_factor_enabled", False):
            raise HTTPException(status_code=400, detail="Two-factor authentication is already enabled")
        
        # Generate verification code
        code = generate_verification_code()
        expiry = datetime.now(timezone.utc) + timedelta(minutes=10)
        
        # Store the code and expiry in the database WITHOUT changing 2FA status
        await db_main["store_users"].update_one(
            {"email": current_user.email},
            {
                "$set": {
                    "verification_code": code,
                    "verification_code_expiry": expiry,
                    "verification_action": "enable"  # Mark this as an enable action
                }
            }
        )
        
        # Send verification code via email in background thread to avoid blocking
        success = send_email_in_background(current_user.email, code)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to send verification email"
            )
        
        return {"message": "Verification code sent to enable 2FA"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in 2FA enable process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/2fa/verify")
async def verify_2fa(request: Verify2FARequest, current_user: User = Depends(get_current_active_user)):
    try:
        # Find user and check if they exist
        user = await db_main["store_users"].find_one({"email": current_user.email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if code exists and hasn't expired
        stored_code = user.get("verification_code")
        expiry = user.get("verification_code_expiry")
        
        if not stored_code or not expiry:
            raise HTTPException(status_code=400, detail="No verification code found")
        
        # Ensure both times are timezone-aware for comparison
        current_time = datetime.now(timezone.utc)
        expiry_time = expiry.replace(tzinfo=timezone.utc) if expiry.tzinfo is None else expiry
        
        if current_time > expiry_time:
            raise HTTPException(status_code=400, detail="Verification code has expired")
        
        if request.code != stored_code:
            raise HTTPException(status_code=400, detail="Invalid verification code")
        
        # Check if this is a disable action from the stored verification_action
        # This takes precedence over the action parameter in the request
        action = user.get("verification_action", "enable")
        if not action:
            # If verification_action is not set, use the action from the request
            action = getattr(request, 'action', 'enable')
        
        # Determine the new status
        new_status = action != 'disable'
        
        # Update 2FA status in database
        result: UpdateResult = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {
                "$set": {"two_factor_enabled": new_status},
                "$unset": {
                    "verification_code": "",
                    "verification_code_expiry": "",
                    "verification_action": ""
                }
            }
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update 2FA settings")
        
        # Invalidate user cache since 2FA settings changed
        invalidate_user_cache(current_user.email)
        
        status_text = "enabled" if new_status else "disabled"
        logger.info(f"2FA {status_text} for user: {current_user.email}")
        return {"message": f"Two-factor authentication has been {status_text}"}
        
    except HTTPException as he:
        logger.error(f"HTTP Exception in 2FA verification: {str(he)}")
        raise he
    except Exception as e:
        logger.error(f"Error in 2FA verification: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process verification"
        )

@router.post("/2fa/disable")
async def disable_2fa(current_user: User = Depends(get_current_active_user)):
    try:
        # Find user and check if they exist
        user = await db_main["store_users"].find_one({"email": current_user.email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if 2FA is already disabled
        if not user.get("two_factor_enabled", False):
            raise HTTPException(status_code=400, detail="Two-factor authentication is already disabled")
        
        # Generate verification code
        code = generate_verification_code()
        expiry = datetime.now(timezone.utc) + timedelta(minutes=10)
        
        # Store the code and expiry in the database WITHOUT changing 2FA status
        await db_main["store_users"].update_one(
            {"email": current_user.email},
            {
                "$set": {
                    "verification_code": code,
                    "verification_code_expiry": expiry,
                    "verification_action": "disable"  # Mark this as a disable action
                }
            }
        )
        
        # Send verification code via email in background thread
        success = send_email_in_background(current_user.email, code)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to send verification email"
            )
        
        # Disable 2FA
        update_result: UpdateResult = await db_main["store_users"].update_one(
            {"email": current_user.email},
            {"$set": {"two_factor_enabled": False}}
        )

        # Log the disable action
        logger.info(f"2FA disabled for user: {current_user.email}")

        return {"message": "2FA disabled successfully"}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error in 2FA disable process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/2fa/status/{email}")
async def get_2fa_status(email: str, current_user: User = Depends(get_current_active_user)):
    try:
        # Verify the user is checking their own status
        if current_user.email != email:
            raise HTTPException(
                status_code=403,
                detail="You can only check your own 2FA status"
            )
        
        # Find user and check if they exist
        user = await db_main["store_users"].find_one({"email": email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return {
            "enabled": user.get("two_factor_enabled", False)
        }
    except Exception as e:
        logger.error(f"Error checking 2FA status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user/me", response_model=User)
async def get_current_user_profile(current_user: User = Depends(get_current_active_user)):
    """
    Get the profile of the currently authenticated user.
    This endpoint requires a valid JWT token.
    """
    try:
        # The current_user object already contains the necessary data fetched by get_current_active_user
        # We might want to refetch to ensure freshness or add more fields not in the token payload
        # Checklist item 25 (Re-fetch user - optional but good practice)
        user_db = await db_main["store_users"].find_one({"email": current_user.email})
        if not user_db:
            # This should technically not happen if the token is valid and user wasn't deleted
            raise HTTPException(status_code=404, detail="User not found")

        # Use create_user_object instead of User.from_db directly to ensure proper handling of alternate_emails
        return create_user_object(user_db)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/meta")
async def meta_auth(request: MetaAuthRequest):
    try:
        logger.info("Starting Meta authentication process")

        # Extract the access token (credential) from the request
        # Checklist item 2 (Fix attribute name - step 1)
        access_token = request.credential
        if not access_token:
            raise HTTPException(
                status_code=400,
                detail="Meta credential (access token) not provided."
            )

        # Verify the Meta token using Facebook Graph API
        try:
            user_info = await verify_meta_token(access_token)
            logger.info(f"Meta token verified for user ID: {user_info.get('id')}")
        except HTTPException as he:
            # Re-raise HTTPExceptions from verify_meta_token
            raise he
        except Exception as e:
            # Catch other potential errors during token verification
            logger.error(f"Meta token verification failed: {str(e)}")
            raise HTTPException(status_code=401, detail="Invalid Meta token")

        email = user_info.get('email')
        name = user_info.get('name', '')

        if not email:
            raise HTTPException(status_code=400, detail="Email not found in Meta token.")

        # Check if user exists in La Nube (store_users)
        email_pattern = {"$regex": f"^{re.escape(email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern},
                {"selected_meta_email": email_pattern}
            ]
        }
        existing_user = await db_main["store_users"].find_one(query)
        if not existing_user:
            logger.warning(f"Email not found in La Nube: {email} during Meta auth")
            raise HTTPException(
                status_code=400,
                detail="Email not found in La Nube. You must be a La Nube user to access D-Unit."
            )

        # User exists in La Nube, check D-Unit access
        if not existing_user.get('pass_dunit'):
            logger.info(f"User {email} (Meta auth) exists in La Nube but needs D-Unit registration")
            dunit_password = generate_random_password()
            await db_main["store_users"].update_one(
                {"_id": existing_user["_id"]},  # Use the _id of the found user document
                {
                    "$set": {
                        "pass_dunit": get_password_hash(dunit_password),
                        "name": name or existing_user.get('name', ''), # Use Meta name if available, else existing
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S"),
                        "updated_by": "meta_auth"
                    }
                }
            )
            logger.info(f"Successfully added D-Unit access for user {existing_user['email']} (Meta email: {email}) via Meta auth")
            
            # Trigger customer data update with PII for newly registered store
            store_id = str(existing_user["id_store"])
            if trigger_for_newly_registered_store:
                try:
                    # Run in background to not block the authentication response
                    import asyncio
                    asyncio.create_task(asyncio.to_thread(trigger_for_newly_registered_store, store_id))
                    logger.info(f"Triggered customer data update for newly registered store {store_id} (Meta auth)")
                except Exception as e:
                    logger.error(f"Failed to trigger customer data update for store {store_id} (Meta auth): {e}")
                    # Don't fail the authentication if customer data update fails

        # Store Meta access token (credential) in the database with expiration tracking
        store_id = str(existing_user["id_store"])  # type: ignore[index]
        
        # Try to exchange for a long-lived token (valid for 60 days)
        try:
            long_lived_token = await exchange_for_long_lived_token(request.credential)
            if long_lived_token:
                logger.info(f"Successfully exchanged for long-lived token for store {store_id}")
                token_to_store = long_lived_token["access_token"]
                expires_in = long_lived_token.get("expires_in", 5184000)  # Default 60 days
                token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)
            else:
                logger.warning(f"Failed to exchange for long-lived token, using short-lived token for store {store_id}")
                token_to_store = request.credential
                expires_in = 3600  # Default 1 hour for short-lived tokens
                token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)
        except Exception as e:
            logger.warning(f"Error exchanging for long-lived token: {str(e)}, using original token")
            token_to_store = request.credential
            expires_in = 3600  # Default 1 hour
            token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)
        
        await db_analysis["global_analysis"].update_one(
            {"_id": store_id},
            {
                "$set": {
                    "meta_access_token": token_to_store,
                    "meta_user_id": user_info.get('id'),
                    "meta_token_expires_at": token_expires_at,
                    "meta_token_type": "long_lived" if long_lived_token else "short_lived",
                    "meta_token_updated_at": datetime.now(timezone.utc)
                }
            },
            upsert=True
        )

        # Ensure store analysis exists
        await ensure_store_analysis(store_id, email, existing_user.get("name", ""))
        
        # Create access token with proper store_id from existing user
        jwt_token = create_access_token(
            data={
                "sub": existing_user["email"],  # Use primary email from user document
                "store_id": store_id
            },
            expires_delta=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        logger.info("Access token created successfully")
        
        # Format user data to match normal login response
        user_data = {
            "email": existing_user["email"],
            "id_store": existing_user["id_store"],
            "name": existing_user.get("name", ""),
            "active": existing_user.get("active", 1),
            "created_at": existing_user.get("created_at"),
            "updated_at": existing_user.get("updated_at")
        }
        
        # Return the response in the format expected by the frontend
        return {
            "access_token": jwt_token,
            "token_type": "bearer",
            "user": user_data
        }
    
    except HTTPException as he:  # pyright: ignore[reportUnusedExcept]
        raise he
    except Exception as e:  # pyright: ignore[reportUnusedExcept]
        logger.error(f"Meta auth error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Authentication failed: {str(e)}"
        )

@router.post("/refresh-meta-token")
async def refresh_meta_token_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """
    Endpoint to refresh Meta token for the current user's store
    """
    try:
        store_id = str(current_user.store_id)
        refreshed_token = await refresh_meta_token_if_needed(store_id)
        
        if refreshed_token:
            return {
                "success": True,
                "message": "Meta token refreshed successfully"
            }
        else:
            return {
                "success": False,
                "message": "Failed to refresh Meta token. Please reconnect your Meta account."
            }
    except Exception as e:
        logger.error(f"Error refreshing Meta token: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to refresh Meta token"
        )
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Meta auth error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Authentication failed: {str(e)}"
        )

@router.get("/email-service/health", include_in_schema=False)
async def check_email_service_health():
    """Health check endpoint for email service"""
    if not smtp_pool:
        raise HTTPException(status_code=503, detail="Email service pool not initialized")
    try:
        # Example: Send a NOOP command to check connection
        # This depends on the capabilities of the smtp_pool implementation
        # If smtp_pool manages connections automatically, this might not be necessary
        # Or, check a flag or status provided by the pool
        # Placeholder for actual health check logic
        is_healthy = True # Replace with actual check
        if is_healthy:
            return {"status": "Email service is healthy"}
        else:
            raise HTTPException(status_code=503, detail="Email service connection failed")
    except Exception as e:
        logger.error(f"Email service health check error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Email service health check failed: {str(e)}")

@router.get("/cache/stats")
async def get_authentication_cache_stats(current_user: User = Depends(get_current_active_user)):
    """Get authentication cache statistics for monitoring"""
    try:
        stats = get_cache_stats()
        return {
            "cache_stats": stats,
            "message": "Authentication cache statistics"
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")

@router.get("/csrf-token")
async def get_csrf_token(request: Request, response: Response, current_user: User = Depends(get_current_active_user)):
    """
    Generate and return a CSRF token for the authenticated user.
    This endpoint should be called to get a fresh CSRF token for form submissions.
    """
    try:
        # Ensure user is properly authenticated
        if not current_user or not current_user.email:
            logger.warning("CSRF token request from unauthenticated user")
            raise HTTPException(
                status_code=401,
                detail="Authentication required to generate CSRF token"
            )

        # Extract client information for security logging
        from utils.security_utils import extract_client_ip
        
        client_ip = extract_client_ip(request)  # type: ignore[arg-type]
        user_agent = request.headers.get("User-Agent", "")
        
        # Generate CSRF token
        token_data = await csrf_service.generate_token(current_user.email)
        
        # Handle case where token generation fails
        if not token_data:
            logger.error(f"Failed to generate CSRF token for user {current_user.email}")
            raise HTTPException(
                status_code=500,
                detail="Failed to generate CSRF token. Please try again."
            )
        
        # Handle string token response (backward compatibility)
        if isinstance(token_data, str):
            csrf_token = token_data
            expires_in = 1800  # 30 minutes default
            double_submit_enabled = False
        else:
            # Handle dict response
            csrf_token = token_data.get("csrf_token") if hasattr(token_data, 'get') else token_data
            expires_in = token_data.get("expires_in", 1800) if hasattr(token_data, 'get') else 1800
            double_submit_enabled = token_data.get("double_submit_enabled", False) if hasattr(token_data, 'get') else False
        
        if not csrf_token:
            logger.error(f"Invalid CSRF token generated for user {current_user.email}")
            raise HTTPException(
                status_code=500,
                detail="Failed to generate valid CSRF token. Please try again."
            )
        
        # Set secure cookie if double submit is enabled
        if double_submit_enabled:
            cookie_options = {
                "httponly": True,
                "secure": True,  # Ensure HTTPS in production
                "samesite": "none",  # Allow cross-origin requests (required for CloudFront setup)
                "max_age": expires_in
            }
            
            # Set the CSRF cookie
            response.set_cookie(
                "csrf_token", 
                csrf_token, 
                **cookie_options
            )
            
            logger.debug(f"CSRF token and cookie set for user {current_user.email}")
        
        # Log successful token generation (for security monitoring)
        logger.debug(f"CSRF token generated for user {current_user.email} from IP {client_ip}")
        
        return {
            "success": True,
            "csrf_token": csrf_token,
            "expires_in": expires_in,
            "double_submit_enabled": double_submit_enabled,
            "cookie_token_set": double_submit_enabled,
            "message": "CSRF token generated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating CSRF token for user {getattr(current_user, 'email', 'unknown')}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while generating CSRF token"
        )

@router.get("/csrf-token/health")
async def get_csrf_health():
    """Health check endpoint for CSRF service (no authentication required)"""
    try:
        # Test CSRF service without user dependency
        test_user_id = "health_check_user"
        
        # Test token generation
        token = await csrf_service.generate_token(test_user_id)
        logger.debug(f"Health check: CSRF token generated successfully")
        
        # Test token validation
        is_valid = await csrf_service.validate_token(token, test_user_id)
        logger.debug(f"Health check: CSRF token validation result: {is_valid}")
        
        # Test token cleanup
        await csrf_service.invalidate_user_tokens(test_user_id)
        logger.debug(f"Health check: CSRF token cleanup completed")
        
        # Get service stats
        stats = csrf_service.get_stats()
        
        return {
            "success": True,
            "message": "CSRF service is healthy",
            "token_generated": bool(token),
            "token_valid": is_valid,
            "service_stats": stats,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"CSRF health check failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Health check traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500, 
            detail=f"CSRF service health check failed: {str(e)}"
        )

@router.post("/csrf-token/rotate")
async def rotate_csrf_token(current_user: User = Depends(get_current_active_user)):
    """Manually rotate CSRF token for the authenticated user"""
    try:
        # Get current user's active tokens
        existing_tokens = csrf_service.user_tokens.get(current_user.email, set())
        
        if not existing_tokens:
            # No existing token, generate a new one
            new_token = await csrf_service.generate_token(current_user.email)
            logger.debug(f"Generated new CSRF token for user {current_user.email} (no existing token)")
            
            return {
                "success": True,
                "csrf_token": new_token,
                "expires_in": 30 * 60,  # 30 minutes in seconds
                "rotated": False,
                "message": "New CSRF token generated"
            }
        
        # Find a valid token to rotate
        old_token = None
        for token in existing_tokens:
            if await csrf_service.validate_token(token, current_user.email):
                old_token = token
                break
        
        if not old_token:
            # No valid tokens, generate a new one
            new_token = await csrf_service.generate_token(current_user.email)
            logger.debug(f"Generated new CSRF token for user {current_user.email} (no valid token)")
            
            return {
                "success": True,
                "csrf_token": new_token,
                "expires_in": 30 * 60,
                "rotated": False,
                "message": "New CSRF token generated (no valid existing token)"
            }
        
        # Rotate the existing token
        new_token = await csrf_service.rotate_token_on_sensitive_operation(
            current_user.email, old_token
        )
        
        logger.info(f"CSRF token manually rotated for user {current_user.email}")
        
        return {
            "success": True,
            "csrf_token": new_token,
            "expires_in": 30 * 60,  # 30 minutes in seconds
            "rotated": True,
            "message": "CSRF token successfully rotated"
        }
        
    except Exception as e:
        logger.error(f"Error rotating CSRF token for user {current_user.email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to rotate CSRF token")

@router.post("/logout")
async def logout(current_user: User = Depends(get_current_active_user)):
    """Logout and invalidate CSRF tokens"""
    try:
        # Invalidate all CSRF tokens for the user
        await csrf_service.invalidate_user_tokens(current_user.email)
        
        return {
            "success": True,
            "message": "Successfully logged out"
        }
        
    except Exception as e:
        logger.error(f"Error during logout for user {current_user.email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to logout")

