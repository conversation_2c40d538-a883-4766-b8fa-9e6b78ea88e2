import React from 'react';
import { Box, Paper, Typography, Button, Tooltip, Alert } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import BlockIcon from '@mui/icons-material/Block';
import InstagramIcon from '@mui/icons-material/Instagram';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';
import { PERMISSION_DESCRIPTIONS } from '../../services/types';
import { useTranslation, Trans } from 'react-i18next';

interface RestrictedWidgetProps {
  featureKey: string;
  title: string;
  children: React.ReactNode;
  minHeight?: number | string;
  platform?: 'facebook' | 'instagram';
}

export const RestrictedWidget: React.FC<RestrictedWidgetProps> = ({
  featureKey,
  title,
  children,
  minHeight = 200,
  platform = 'facebook'
}) => {
  const { canAccessFeature, getPermissionStatus, refreshPermissions } = useMetaPermissions();
  const { canAccess, missingPermissions, revokedPermissions } = canAccessFeature(featureKey, platform);
  const { t } = useTranslation();

  // Add more detailed debugging to help identify permission issues
  if (process.env.NODE_ENV === 'development') {
    // Removed console logs
  }

  // If we have all permissions, render children normally
  if (canAccess) {
    return <>{children}</>;
  }

  const hasRevokedPermissions = revokedPermissions.length > 0;
  
  // Check if this is Instagram-specific
  const isInstagramPlatform = platform === 'instagram';
  
  // Filter Instagram-specific permissions for revoked permissions check
  const revokedInstagramPermissions = revokedPermissions.filter(p => 
    p.toLowerCase().includes('instagram') || p === 'instagram_basic' || p === 'instagram_manage_insights'
  );
  
  const hasRevokedInstagramPermissions = revokedInstagramPermissions.length > 0;
  const hasRevokedInstagramInsights = revokedInstagramPermissions.includes('instagram_manage_insights');
  const hasRevokedInstagramBasic = revokedInstagramPermissions.includes('instagram_basic');
  
  // Widget is restricted due to missing permissions
  return (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 3, 
        minHeight, 
        display: 'flex', 
        flexDirection: 'column', 
        justifyContent: 'center', 
        alignItems: 'center',
        opacity: 0.7,
        backgroundColor: hasRevokedPermissions 
          ? (isInstagramPlatform || hasRevokedInstagramPermissions) 
            ? 'rgba(221, 42, 123, 0.05)' // Instagram pink for Instagram permissions
            : 'rgba(244, 67, 54, 0.03)' // Regular error red
          : 'rgba(0, 0, 0, 0.03)',
        filter: 'grayscale(1)',
        borderColor: hasRevokedPermissions 
          ? (isInstagramPlatform || hasRevokedInstagramPermissions) 
            ? '#C13584' // Instagram brand color
            : 'error.light' 
          : 'inherit'
      }}
    >
      {isInstagramPlatform || hasRevokedInstagramPermissions ? (
        <InstagramIcon sx={{ fontSize: 40, color: '#C13584', mb: 2 }} />
      ) : hasRevokedPermissions ? (
        <BlockIcon sx={{ fontSize: 40, color: 'error.main', mb: 2 }} />
      ) : (
        <LockIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 2 }} />
      )}
      
      <Typography variant="h6" gutterBottom align="center">
        {t('meta.restricted.unavailableTitle', { title: t(title, { defaultValue: title }), defaultValue: `${title} Unavailable` })}
      </Typography>
      
      {/* Instagram-specific alerts */}
      {(isInstagramPlatform || hasRevokedInstagramPermissions) && (
        <Alert severity="error" sx={{ mb: 2, width: '100%', borderColor: '#C13584' }}>
          {hasRevokedInstagramInsights && !hasRevokedInstagramBasic ? (
            <>
              <Typography fontWeight="bold" gutterBottom>{t('meta.restricted.igInsightsRevokedTitle', 'Instagram Insights Permission Revoked')}</Typography>
              <Typography variant="body2">
                <Trans i18nKey="meta.restricted.igInsightsRevokedBody" components={{ 0: <strong />, 1: <strong /> }} />
              </Typography>
              <Typography variant="body2" gutterBottom sx={{ mt: 1 }}>
                <Trans i18nKey="meta.restricted.igInsightsCurrentStatus" components={{ 0: <strong />, 1: <strong /> }} />
              </Typography>
            </>
          ) : hasRevokedInstagramBasic && !hasRevokedInstagramInsights ? (
            <>
              <Typography fontWeight="bold" gutterBottom>{t('meta.restricted.igBasicRevokedTitle', 'Instagram Basic Permission Revoked')}</Typography>
              <Typography variant="body2">
                 <Trans i18nKey="meta.restricted.igBasicRevokedBody" components={{ 0: <strong /> }} />
              </Typography>
              <Typography variant="body2" gutterBottom sx={{ mt: 1 }}>
                <Trans i18nKey="meta.restricted.igBasicCurrentStatus" components={{ 0: <strong /> }} />
              </Typography>
            </>
          ) : hasRevokedInstagramBasic && hasRevokedInstagramInsights ? (
            <>
              <Typography fontWeight="bold" gutterBottom>{t('meta.restricted.igMultipleRevokedTitle', 'Multiple Instagram Permissions Revoked')}</Typography>
              <Typography variant="body2">
                <Trans i18nKey="meta.restricted.igMultipleRevokedBody" components={{ 0: <strong />, 1: <strong /> }} />
              </Typography>
              <Typography variant="body2" gutterBottom sx={{ mt: 1 }}>
                <Trans i18nKey="meta.restricted.igMultipleCurrentStatus" components={{ 0: <strong />, 1: <strong /> }} />
              </Typography>
            </>
          ) : (
            <>
              <Typography fontWeight="bold" gutterBottom>{t('meta.restricted.igGenericRevokedTitle', 'Instagram Permissions Revoked')}</Typography>
              <Typography variant="body2">{t('meta.restricted.igGenericRevokedBody', 'You\'ve revoked required Instagram permissions for this feature.')}</Typography>
              <Typography variant="body2" gutterBottom sx={{ mt: 1 }}>
                {t('meta.restricted.requiredPermissionsList', { permissions: missingPermissions.join(', '), defaultValue: `Required permissions: ${missingPermissions.join(', ')}` })}
              </Typography>
            </>
          )}
        </Alert>
      )}
      
      {/* Non-Instagram permissions alert */}
      {hasRevokedPermissions && !hasRevokedInstagramPermissions && (
        <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
          {revokedPermissions.length === 1 ? (
            <Trans 
              i18nKey="meta.restricted.singlePermissionRevoked" 
              values={{ permissionName: PERMISSION_DESCRIPTIONS[revokedPermissions[0]] ? t(PERMISSION_DESCRIPTIONS[revokedPermissions[0]]!.nameKey) : revokedPermissions[0] }}
              components={{ 0: <strong /> }}
             />
          ) : (
            <>{t('meta.restricted.multiplePermissionsRevoked', 'You\'ve revoked required permissions for this feature.')}</>
          )}
        </Alert>
      )}
      
      {/* If no permissions are revoked, show required permissions */}
      {!hasRevokedPermissions && (
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
          {t('meta.restricted.requiresFollowingPermissions', { count: missingPermissions.length, defaultValue: `This widget requires the following permission${missingPermissions.length > 1 ? 's' : ''}:` })}
        </Typography>
      )}
      
      <Box sx={{ mb: 2 }}>
        {missingPermissions.map(permission => {
          const isRevoked = getPermissionStatus(permission) === 'declined';
          const isInstagramPermission = permission.toLowerCase().includes('instagram');
          
          return (
            <Typography 
              key={permission}
              variant="body2" 
              fontWeight="bold" 
              align="center"
              color={isRevoked ? (isInstagramPermission ? '#C13584' : 'error.main') : 'text.primary'}
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 0.5 }}
            >
              {isRevoked && isInstagramPermission ? (
                <InstagramIcon fontSize="small" sx={{ mr: 0.5 }} />
              ) : isRevoked ? (
                <BlockIcon fontSize="small" sx={{ mr: 0.5 }} />
              ) : null}
              {t('meta.restricted.permissionStatus', { 
                permissionName: PERMISSION_DESCRIPTIONS[permission] ? t(PERMISSION_DESCRIPTIONS[permission]!.nameKey, { defaultValue: permission }) : permission, 
                status: isRevoked ? t('meta.restricted.revokedStatus', ' (Revoked)') : '' 
              })}
            </Typography>
          );
        })}
      </Box>
      
      <Tooltip title={hasRevokedPermissions ? 
        t('meta.restricted.tooltipReconnect', "You'll need to reconnect to Meta to grant these permissions") : 
        t('meta.restricted.tooltipManage', "Visit the Permissions Management tab to learn more")
      }>
        <Button 
          variant="outlined" 
          size="small"
          color={hasRevokedInstagramPermissions ? "secondary" : (hasRevokedPermissions ? "error" : "primary")}
          onClick={() => {
            // Force permissions refresh first to check for any updates
            refreshPermissions(true);
            
            // Navigate to permissions tab - Permissions tab is at index 5 (6th tab)
            const permissionsTabButton = document.querySelector('button[role="tab"]:nth-child(6)') as HTMLButtonElement;
            if (permissionsTabButton) {
              permissionsTabButton.click();
            }
          }}
        >
          {hasRevokedPermissions ? t('meta.restricted.reconnectButton', "Reconnect to Meta") : t('meta.restricted.manageButton', "Manage Permissions")}
        </Button>
      </Tooltip>
    </Paper>
  );
}; 