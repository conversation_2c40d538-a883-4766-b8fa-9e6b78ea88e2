import os
import sys
import logging
import mysql.connector
import pymongo
from pymongo.errors import ConnectionFailure, PyMongoError
from dotenv import load_dotenv
from datetime import datetime
import traceback

# --- Load environment variables ---
load_dotenv()

# --- Logging setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Configuration from environment variables ---
MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
MYSQL_USER = os.getenv('MYSQL_USER', 'root')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'lanube')

MONGO_URI = os.getenv('MONGODB_CONNECTION')
MONGO_DB_NAME = os.getenv('MONGO_DB_NAME', 'D-Unit-AnalysisGPT')
MONGO_COLLECTION_NAME = 'platform_reference_data'

# --- Validate required environment variables ---
missing_vars = []
for var_name in ['MONGODB_CONNECTION']:
    if os.getenv(var_name) is None:
        missing_vars.append(var_name)
if missing_vars:
    logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    logger.error("Please check your .env file or set these environment variables")
    sys.exit(1)

def sync_reference_data():
    mysql_conn = None
    mongo_client = None
    shipping_methods = []
    faqs_active = []
    upsert_result = None
    try:
        # --- Connect to MySQL ---
        logger.info(f"Connecting to MySQL: {MYSQL_HOST}:{MYSQL_PORT} DB: {MYSQL_DATABASE}")
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            connect_timeout=10
        )
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        logger.info("Connected to MySQL.")

        # --- Connect to MongoDB ---
        logger.info(f"Connecting to MongoDB: {MONGO_URI} DB: {MONGO_DB_NAME}")
        try:
            mongo_client = pymongo.MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
            mongo_client.admin.command('ismaster')
            mongo_db = mongo_client[MONGO_DB_NAME]
            mongo_collection = mongo_db[MONGO_COLLECTION_NAME]
            logger.info(f"Connected to MongoDB database '{MONGO_DB_NAME}', collection '{MONGO_COLLECTION_NAME}'.")
        except ConnectionFailure as e:
            logger.error(f"MongoDB Connection Error: {e}")
            if mysql_conn and mysql_conn.is_connected():
                mysql_conn.close()
            sys.exit(1)

        # --- Fetch data from MySQL ---
        mysql_cursor.execute('SELECT * FROM shipping_methods')
        shipping_methods = mysql_cursor.fetchall()
        logger.info(f"Fetched {len(shipping_methods)} shipping methods from MySQL.")
        mysql_cursor.execute('SELECT * FROM faqs WHERE active=1')
        faqs_active = mysql_cursor.fetchall()
        logger.info(f"Fetched {len(faqs_active)} active FAQs from MySQL.")

        # --- Upsert reference data in MongoDB ---
        upsert_result = mongo_collection.update_one(
            {'_id': 'reference'},
            {'$set': {
                'shipping_methods': shipping_methods,
                'faqs_active': faqs_active,
                'last_updated': datetime.utcnow()
            }},
            upsert=True
        )
        if upsert_result.upserted_id:
            logger.info(f"Inserted new reference document with _id: {upsert_result.upserted_id}")
        elif upsert_result.modified_count > 0:
            logger.info("Updated existing reference document.")
        else:
            logger.info("Reference document already up-to-date (no changes made).")

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error: {err}")
        traceback.print_exc()
    except PyMongoError as e:
        logger.error(f"MongoDB Error: {e}")
        traceback.print_exc()
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        traceback.print_exc()
    finally:
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
            logger.info("MySQL cursor closed.")
        if mysql_conn and mysql_conn.is_connected():
            mysql_conn.close()
            logger.info("MySQL connection closed.")
        if mongo_client:
            mongo_client.close()
            logger.info("MongoDB connection closed.")
        # --- Final Summary ---
        logger.info("\n--- Sync Summary ---")
        logger.info(f"Shipping methods fetched: {len(shipping_methods)}")
        logger.info(f"Active FAQs fetched: {len(faqs_active)}")
        if upsert_result:
            logger.info(f"Upsert result: matched={upsert_result.matched_count}, modified={upsert_result.modified_count}, upserted_id={upsert_result.upserted_id}")
        logger.info("--- End of Script ---")

if __name__ == "__main__":
    logger.info("Starting sync of shipping methods and active FAQs from MySQL to MongoDB...")
    sync_reference_data()
    logger.info("Sync script finished.") 