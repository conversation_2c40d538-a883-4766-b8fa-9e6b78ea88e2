#!/usr/bin/env python3
"""
Quick test script to verify environment loading
"""
import os
import sys
from pathlib import Path

# Add parent directory to path for config imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from dotenv import load_dotenv
    env_file = Path(__file__).parent.parent / ".env.development"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✓ Loaded environment from: {env_file}")
    else:
        print("✗ No .env.development file found")
except ImportError:
    print("✗ python-dotenv not installed")

# Test MongoDB connection
mongodb_conn = os.getenv('MONGODB_CONNECTION')
if mongodb_conn:
    print(f"✓ MongoDB connection found: {mongodb_conn[:50]}...")
else:
    print("✗ MONGODB_CONNECTION not found")

# Test config import
try:
    from config.settings import get_settings
    settings = get_settings()
    print("✓ Config import successful")
    print(f"✓ MongoDB Analysis DB: {settings.MONGODB_ANALYSIS_DB}")
except ImportError as e:
    print(f"✗ Config import failed: {e}")
except Exception as e:
    print(f"✗ Settings error: {e}")