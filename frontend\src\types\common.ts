/**
 * Common utility types for type-safe development
 * Used to replace 'any' types throughout the application
 */

// Form data value types
export type FormDataValue = string | number | boolean | File | null | undefined;

// Form data object type
export type FormData = Record<string, FormDataValue>;

// Generic form data that can contain nested objects
export type FormDataObject = Record<string, unknown>;

// API Error response structure
export interface ApiErrorResponse {
  message?: string;
  detail?: string;
  error?: string | object;
  errors?: string[];
}

// Type guards for error handling
export function isErrorWithMessage(error: unknown): error is { message: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as { message: unknown }).message === 'string'
  );
}

export function isErrorWithResponse(error: unknown): error is { 
  response: { status?: number; data?: unknown } 
} {
  return (
    typeof error === 'object' &&
    error !== null &&
    'response' in error &&
    typeof (error as { response: unknown }).response === 'object'
  );
}

export function isErrorWithStatus(error: unknown): error is {
  response: { status: number; data?: unknown }
} {
  return (
    isErrorWithResponse(error) &&
    typeof error.response === 'object' &&
    error.response !== null &&
    'status' in error.response &&
    typeof (error.response as { status: unknown }).status === 'number'
  );
}

export function hasProperty<T extends string>(
  obj: unknown,
  prop: T
): obj is Record<T, unknown> {
  return typeof obj === 'object' && obj !== null && prop in obj;
} 