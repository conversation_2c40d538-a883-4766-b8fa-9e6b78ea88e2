import { authService } from '../services/authService';
import { logger } from './logger';

export interface ApiError extends Error {
  status: number;
  statusText: string;
  data: unknown;
  response?: { data: unknown; status: number; };
}

export interface ErrorResponse {
  message?: string;
  error?: string | object;
  detail?: string;
}

/**
 * Unified authentication utility for consistent auth header management
 */
export class AuthUtils {
  /**
   * Get authorization headers with token
   */
  static getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    const token = authService.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Get authorization headers for API requests (includes additional headers)
   */
  static getApiHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    return {
      ...this.getAuthHeaders(),
      ...customHeaders
    };
  }

  /**
   * Validate token availability
   */
  static validateToken(): string {
    const token = authService.getToken();
    if (!token) {
      throw new Error('Authentication token is required. Please log in again.');
    }
    return token;
  }

  /**
   * Create standardized API error from response
   */
  static createApiError(message: string, status: number, statusText: string = '', data?: unknown): ApiError {
    const error = new Error(message) as ApiError;
    error.name = 'ApiError';
    error.status = status;
    error.statusText = statusText;
    error.data = data;
    return error;
  }

  /**
   * Parse error response and return user-friendly message
   */
  static parseErrorResponse(error: unknown): string {
    let message = 'An unexpected error occurred';

    try {
      if (error && typeof error === 'object') {
        const apiError = error as ApiError;
        
        // Handle specific HTTP status codes
        if (apiError.status) {
          switch (apiError.status) {
            case 400:
              message = 'Invalid request. Please check your inputs and try again.';
              break;
            case 401:
              message = 'Authentication error. Please log in again.';
              break;
            case 403:
              message = 'You do not have permission to access this resource.';
              break;
            case 404:
              message = 'The requested resource could not be found.';
              break;
            case 422:
              message = 'Validation error. Please check your inputs.';
              break;
            case 429:
              message = 'Too many requests. Please try again later.';
              break;
            case 500:
              message = 'Server error. Please try again later.';
              break;
            case 502:
            case 503:
            case 504:
              message = 'Service temporarily unavailable. Please try again later.';
              break;
            default:
              if (apiError.status >= 400 && apiError.status < 500) {
                message = 'Client error. Please try again.';
              } else if (apiError.status >= 500) {
                message = 'Server error. Please try again later.';
              }
          }

          // Try to get more specific error message from response
          if (apiError.data && typeof apiError.data === 'object') {
            const errorData = apiError.data as ErrorResponse;
            if (errorData.message) {
              message = errorData.message;
            } else if (errorData.error) {
              message = typeof errorData.error === 'string' ? errorData.error : JSON.stringify(errorData.error);
            } else if (errorData.detail) {
              message = errorData.detail;
            }
          }
        }

        // If it's just a plain error with message
        if ('message' in apiError && typeof apiError.message === 'string') {
          message = apiError.message;
        }
      }

      // If error is a string
      if (typeof error === 'string') {
        message = error;
      }
    } catch (parseError) {
      logger.error('Error parsing error response:', parseError);
      message = 'An unexpected error occurred while processing the response';
    }

    return message;
  }

  /**
   * Handle authentication errors consistently
   */
  static handleAuthError(error: unknown): void {
    const message = this.parseErrorResponse(error);
    
    // Log the error
    logger.error('Authentication error:', error);

        // If it's a 401 error, handle token expiration    if (error && typeof error === 'object' && 'status' in error && (error as ApiError).status === 401) {      authService.clearTokens();      // Optionally redirect to login page or show login modal      logger.warn('Token expired, user logged out');    }

    throw new Error(message);
  }

  /**
   * Standard error handling for API calls
   */
  static handleApiError(error: unknown, context: string = 'API call'): never {
    const message = this.parseErrorResponse(error);
    logger.error(`${context} failed:`, error);

    // Handle authentication errors
    if (error && typeof error === 'object' && 'status' in error && (error as ApiError).status === 401) {
      this.handleAuthError(error);
    }

    throw new Error(message);
  }
} 