import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import ChartContainer from '../common/ChartContainer';

interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  cost: number;
  margin: number;
  stock: number;
  sales: number;
  rating: number;
  reviews: number;
}

interface CompetitorProduct {
  name: string;
  price: number;
  features: string[];
  advantages: string[];
  disadvantages: string[];
  marketShare: number;
}

interface ProductRecommendation {
  name: string;
  description: string;
  category: string;
  potentialMargin: number;
  demandLevel: 'high' | 'medium' | 'low';
  competitionLevel: 'high' | 'medium' | 'low';
  implementationComplexity: 'high' | 'medium' | 'low';
  estimatedCost: number;
}

interface ProductData {
  currentProducts: Product[];
  competitorProducts: CompetitorProduct[];
  recommendedProducts: ProductRecommendation[];
  marketTrends: Array<{
    category: string;
    growth: number;
    volume: number;
  }>;
}

interface ProductAnalysisProps {
  data: ProductData;
}

const ProductAnalysis: React.FC<ProductAnalysisProps> = ({ data }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No product data available</p>
      </div>
    );
  }

  const categories = ['all', ...new Set(data.currentProducts.map(p => p.category))];

  const filteredProducts = selectedCategory === 'all'
    ? data.currentProducts
    : data.currentProducts.filter(p => p.category === selectedCategory);

  return (
    <div className="space-y-8">
      {/* Category Filter */}
      <div className="flex space-x-2">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedCategory === category
                ? 'bg-[#0D6EFD] text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>

      {/* Performance Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Performance Overview</h3>
        <div className="h-80">
          <ChartContainer width="100%" height="100%">
            <BarChart data={filteredProducts}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" orientation="left" stroke="#0D6EFD" />
              <YAxis yAxisId="right" orientation="right" stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="sales" name="Sales" fill="#0D6EFD" />
              <Bar yAxisId="right" dataKey="margin" name="Margin %" fill="#6C757D" />
            </BarChart>
          </ChartContainer>
        </div>
      </div>

      {/* Current Products */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Current Products</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Margin
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{product.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{product.category}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">${product.price}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.margin}%</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm ${
                      product.stock < 10 ? 'text-red-600' : 'text-gray-900'
                    }`}>
                      {product.stock}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900">{product.rating}</span>
                      <span className="text-sm text-gray-500 ml-2">({product.reviews})</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Competitor Products */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Competitor Products</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data.competitorProducts.map((product, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-4">
                <h4 className="font-medium text-gray-800">{product.name}</h4>
                <span className="text-sm font-medium text-gray-600">${product.price}</span>
              </div>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Key Features</p>
                  <ul className="space-y-1">
                    {product.features.map((feature, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-center">
                        <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Advantages</p>
                    <ul className="space-y-1">
                      {product.advantages.map((advantage, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                          {advantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Disadvantages</p>
                    <ul className="space-y-1">
                      {product.disadvantages.map((disadvantage, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                          <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-2"></span>
                          {disadvantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Market Share</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#0D6EFD] h-2 rounded-full"
                      style={{ width: `${product.marketShare}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{product.marketShare}%</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Product Recommendations */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Recommended Products</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {data.recommendedProducts.map((product, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h4 className="font-medium text-gray-800">{product.name}</h4>
                  <p className="text-sm text-gray-500 mt-1">{product.category}</p>
                </div>
                <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                  {product.demandLevel.toUpperCase()} Demand
                </span>
              </div>

              <p className="text-sm text-gray-600 mb-4">{product.description}</p>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Potential Margin</p>
                  <p className="text-lg font-medium text-gray-900">{product.potentialMargin}%</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Estimated Cost</p>
                  <p className="text-lg font-medium text-gray-900">${product.estimatedCost}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div>
                  <p className="text-xs font-medium text-gray-500 mb-1">Competition</p>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    product.competitionLevel === 'high' ? 'bg-red-100 text-red-800' :
                    product.competitionLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {product.competitionLevel.toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-500 mb-1">Complexity</p>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    product.implementationComplexity === 'high' ? 'bg-red-100 text-red-800' :
                    product.implementationComplexity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {product.implementationComplexity.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Trends */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Market Trends</h3>
        <div className="h-80">
          <ChartContainer width="100%" height="100%">
            <BarChart data={data.marketTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis yAxisId="left" orientation="left" stroke="#0D6EFD" />
              <YAxis yAxisId="right" orientation="right" stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="growth" name="Growth %" fill="#0D6EFD" />
              <Bar yAxisId="right" dataKey="volume" name="Volume" fill="#6C757D" />
            </BarChart>
          </ChartContainer>
        </div>
      </div>
    </div>
  );
};

export default ProductAnalysis; 