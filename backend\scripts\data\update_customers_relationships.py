import os
import sys
import logging
import mysql.connector
from datetime import datetime, timezone
import argparse
from pymongo import MongoClient
from pymongo.errors import NetworkTimeout, ConnectionFailure, OperationFailure
import time
from typing import List, Dict, Any, Optional, Set
from collections import Counter, defaultdict

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    # Changed collection name to match requirements
    STORE_CUSTOMERS_CACHE_COLLECTION = 'store_customers_cache'
    # ENRICHED_STORES_COLLECTION = 'stores'  # REMOVED: Collection for enriched store data
    
    # Privacy protection constants
    MONGO_DB_MAIN_NAME = settings.MONGODB_MAIN_DB  # Main DB for store_users
    STORE_USERS_COLLECTION = 'store_users'  # Collection to check for registered users
    
    # Privacy constants
    ANONYMOUS_NAME = "Anonymous"
    ANONYMOUS_EMAIL = "<EMAIL>"
    ANONYMOUS_DNI = "XXXXXXXX"
    
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    
    # Add constants for store filtering to match update_active_stores.py
    INACTIVE_STORE_EXCEPTIONS = getattr(settings, 'INACTIVE_STORE_EXCEPTIONS', [])
    NEW_STORE_CUTOFF_YEAR = getattr(settings, 'NEW_STORE_CUTOFF_YEAR', 2025)
    
    # Batch size for processing large datasets
    BATCH_SIZE = 5000
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# --- Helper Functions ---

def get_registered_stores(mongo_db_main) -> Set[str]:
    """
    Fetches all store IDs that have registered users in D-Unit (have pass_dunit field).
    
    Parameters:
    - mongo_db_main: MongoDB main database connection
    
    Returns:
    - Set of store IDs as strings
    """
    registered_stores = set()
    
    if mongo_db_main is None:
        logger.error("MongoDB main database connection is not available.")
        return registered_stores
    
    try:
        store_users_collection = mongo_db_main[STORE_USERS_COLLECTION]
        
        # Find all documents that have pass_dunit field
        registered_users = store_users_collection.find(
            {"pass_dunit": {"$exists": True}},
            {"id_store": 1}  # Only fetch id_store field
        )
        
        for user in registered_users:
            if user.get('id_store'):
                registered_stores.add(str(user['id_store']))
        
        logger.info(f"Found {len(registered_stores)} registered stores with D-Unit users")
        
    except Exception as e:
        logger.error(f"Error fetching registered stores: {e}", exc_info=True)
    
    return registered_stores

def anonymize_customer_data(customer_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Anonymizes sensitive customer information.
    
    Parameters:
    - customer_data: Customer data dictionary
    
    Returns:
    - Anonymized customer data dictionary
    """
    # Create a copy to avoid modifying the original
    anonymized = customer_data.copy()
    
    # Anonymize sensitive fields
    anonymized['customer_name'] = ANONYMOUS_NAME
    anonymized['customer_email'] = ANONYMOUS_EMAIL
    
    # If DNI field exists in the data, anonymize it
    if 'customer_dni' in anonymized:
        anonymized['customer_dni'] = ANONYMOUS_DNI
    
    # Mark as anonymized for tracking
    anonymized['is_anonymized'] = True
    
    return anonymized

def get_stores_to_process(mysql_conn, specific_store_id: Optional[str] = None, registered_stores_only: bool = False, registered_stores: Optional[Set[str]] = None) -> List[Dict[str, Any]]:
    """
    Fetches active stores from the MySQL database, using the same criteria as
    update_active_stores.py to ensure consistency.
    
    Parameters:
    - mysql_conn: MySQL connection
    - specific_store_id: Optional specific store ID to process
    - registered_stores_only: If True, only return stores that are registered
    - registered_stores: Set of registered store IDs
    
    Returns:
    - List of store dictionaries
    """
    stores: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available for get_stores_to_process.")
        return stores
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            exception_clause = f"OR s.id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
            
            # Specific store filter
            specific_store_clause = ""
            if specific_store_id:
                specific_store_clause = f"AND s.id_store = {int(specific_store_id)}"
            
            query = f"""
            SELECT 
                s.id_store, s.symbol, s.name
            FROM stores s
            WHERE 
                (s.active = 1
                {exception_clause}
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
                {specific_store_clause}
            ORDER BY s.id_store
            """
            cursor.execute(query)
            all_stores = cursor.fetchall()
            
            # Filter by registered stores if requested
            if registered_stores_only and registered_stores is not None:
                stores = [s for s in all_stores if str(s['id_store']) in registered_stores]
                logger.info(f"Filtered to {len(stores)} registered stores out of {len(all_stores)} total stores")
            else:
                stores = all_stores
                
            logger.info(f"Found {len(stores)} stores to process using consistent criteria.")
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching stores: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching stores from MySQL: {e}", exc_info=True)
    return stores

def query_customer_data_mysql(store_id: int, mysql_conn, batch_size: int = BATCH_SIZE, offset: int = 0, last_updated: Optional[datetime] = None) -> List[Dict[str, Any]]:
    """
    Queries MySQL for customer transaction data using the comprehensive query that includes
    order details, payment methods, shipping information, and product details.
    
    Parameters:
    - store_id: The store ID to query
    - mysql_conn: MySQL connection
    - batch_size: Number of records to fetch per batch
    - offset: Offset for pagination
    - last_updated: Optional timestamp to only fetch orders since the last update
    
    Returns:
    - List of transaction records
    """
    transaction_data: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for query_customer_data_mysql (store: {store_id}).")
        return transaction_data
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Build query with optional timestamp filter
            if last_updated:
                time_filter = "AND so.created_at > %s "
                logger.info(f"Filtering orders updated after {last_updated.isoformat()}")
                query = f"""
                SELECT 
                    s.id_store, 
                    s.name AS store_name, 
                    sc.id_store_customer, 
                    sc.name AS customer_name, 
                    sc.email AS customer_email, 
                    sc.provincia AS customer_state, 
                    sc.ciudad AS customer_city,
                    su_dni.dni AS customer_dni,
                    so.postal_code AS order_postal_code, 
                    so.id_store_order, 
                    so.created_at AS transaction_date, 
                    os.name AS order_status, 
                    so.discount AS order_discount, 
                    pm.name AS payment_method, 
                    sm.name AS shipping_method, 
                    scp.price AS shipping_cost, 
                    scup.code AS coupon_code, 
                    scup.amount AS coupon_amount, 
                    p.id_product, 
                    p.name AS product_name, 
                    sop.qty AS quantity, 
                    sop.price AS unit_price, 
                    (COALESCE(oit.total_item_price, 0) + COALESCE(scp.price, 0) - COALESCE(so.discount, 0)) AS order_calculated_total
                FROM store_orders so
                JOIN stores s ON so.id_store = s.id_store 
                JOIN store_customers sc ON so.id_store_customer = sc.id_store_customer 
                JOIN store_ordered_products sop ON so.id_store_order = sop.id_store_order
                JOIN products p ON sop.id_product = p.id_product 
                LEFT JOIN store_users su_dni ON sc.email = su_dni.email AND su_dni.id_store = so.id_store
                LEFT JOIN (
                    SELECT 
                        id_store_order, 
                        SUM(COALESCE(qty, 0) * COALESCE(price, 0)) as total_item_price
                    FROM store_ordered_products
                    GROUP BY id_store_order
                ) oit ON so.id_store_order = oit.id_store_order
                LEFT JOIN orders_statuses os ON so.id_order_status = os.id_order_status 
                LEFT JOIN payment_methods pm ON so.id_payment_method = pm.id_payment_method 
                LEFT JOIN shipping_methods sm ON so.id_shipping = sm.id_shipping 
                LEFT JOIN store_cupons scup ON so.id_cupon = scup.id_cupon 
                LEFT JOIN shipping_code_prices scp ON so.id_store = scp.store_id AND so.id_shipping = scp.shipping_id AND so.postal_code = scp.postal_code 
                WHERE so.id_order_status IN (2, 5, 7)
                AND so.id_store = %s
                {time_filter}
                ORDER BY s.id_store, sc.id_store_customer, so.created_at DESC, p.id_product
                LIMIT %s OFFSET %s
                """
                cursor.execute(query, (store_id, last_updated, batch_size, offset))
            else:
                # Query without timestamp filter
                query = """
                SELECT 
                    s.id_store, 
                    s.name AS store_name, 
                    sc.id_store_customer, 
                    sc.name AS customer_name, 
                    sc.email AS customer_email, 
                    sc.provincia AS customer_state, 
                    sc.ciudad AS customer_city,
                    su_dni.dni AS customer_dni,
                    so.postal_code AS order_postal_code, 
                    so.id_store_order, 
                    so.created_at AS transaction_date, 
                    os.name AS order_status, 
                    so.discount AS order_discount, 
                    pm.name AS payment_method, 
                    sm.name AS shipping_method, 
                    scp.price AS shipping_cost, 
                    scup.code AS coupon_code, 
                    scup.amount AS coupon_amount, 
                    p.id_product, 
                    p.name AS product_name, 
                    sop.qty AS quantity, 
                    sop.price AS unit_price,
                    (COALESCE(oit.total_item_price, 0) + COALESCE(scp.price, 0) - COALESCE(so.discount, 0)) AS order_calculated_total
                FROM store_orders so
                JOIN stores s ON so.id_store = s.id_store 
                JOIN store_customers sc ON so.id_store_customer = sc.id_store_customer 
                JOIN store_ordered_products sop ON so.id_store_order = sop.id_store_order
                JOIN products p ON sop.id_product = p.id_product
                LEFT JOIN store_users su_dni ON sc.email = su_dni.email AND su_dni.id_store = so.id_store
                LEFT JOIN (
                    SELECT 
                        id_store_order, 
                        SUM(COALESCE(qty, 0) * COALESCE(price, 0)) as total_item_price
                    FROM store_ordered_products
                    GROUP BY id_store_order
                ) oit ON so.id_store_order = oit.id_store_order
                LEFT JOIN orders_statuses os ON so.id_order_status = os.id_order_status 
                LEFT JOIN payment_methods pm ON so.id_payment_method = pm.id_payment_method 
                LEFT JOIN shipping_methods sm ON so.id_shipping = sm.id_shipping 
                LEFT JOIN store_cupons scup ON so.id_cupon = scup.id_cupon 
                LEFT JOIN shipping_code_prices scp ON so.id_store = scp.store_id AND so.id_shipping = scp.shipping_id AND so.postal_code = scp.postal_code 
                WHERE so.id_order_status IN (2, 5, 7)
                AND so.id_store = %s
                ORDER BY s.id_store, sc.id_store_customer, so.created_at DESC, p.id_product
                LIMIT %s OFFSET %s
                """
                cursor.execute(query, (store_id, batch_size, offset))
                
            transaction_data = cursor.fetchall()
            
            # Convert decimal.Decimal to float for JSON serialization
            for record in transaction_data:
                for key, value in record.items():
                    if hasattr(value, 'quantize'):  # Check if it's a Decimal
                        record[key] = float(value)
            
            logger.info(f"Fetched {len(transaction_data)} transaction records for store {store_id} (batch: {offset//batch_size + 1})")
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error querying customer data for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error querying customer data for store {store_id}: {e}", exc_info=True)
    
    return transaction_data

def query_abandoned_cart_data_mysql(store_id: int, mysql_conn) -> List[Dict[str, Any]]:
    """
    Queries MySQL for abandoned cart data for customers who may or may not have paid orders.
    
    Parameters:
    - store_id: The store ID to query
    - mysql_conn: MySQL connection
    
    Returns:
    - List of abandoned cart records
    """
    abandoned_cart_data: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for query_abandoned_cart_data_mysql (store: {store_id}).")
        return abandoned_cart_data
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query = """
            SELECT 
                s.id_store,
                s.name AS store_name,
                sc.id_customer AS id_store_customer,
                cust.name AS customer_name,
                cust.email AS customer_email,
                cust.provincia AS customer_state,
                cust.ciudad AS customer_city,
                cust.created_at AS customer_created_at,
                sc.id_carshop,
                sc.created_at AS cart_created_at,
                sc.active AS cart_active,
                -- Check if customer has any paid orders
                CASE 
                    WHEN EXISTS (
                        SELECT 1 
                        FROM store_orders so
                        WHERE so.id_store_customer = sc.id_customer
                        AND so.id_store = sc.id_store
                        AND so.id_order_status IN (2, 5, 7)
                    ) THEN 0
                    ELSE 1
                END AS abandoned_cart_only,
                -- Cart details
                GROUP_CONCAT(
                    JSON_OBJECT(
                        'product_id', p.id_product,
                        'product_name', p.name,
                        'quantity', scd.qty,
                        'price', scd.price
                    )
                ) AS cart_products,
                COUNT(DISTINCT scd.id_product) AS unique_products_in_cart,
                SUM(scd.qty) AS total_items_in_cart,
                SUM(COALESCE(scd.qty, 0) * COALESCE(scd.price, 0)) AS cart_total_value
            FROM store_carshop sc
            JOIN stores s ON sc.id_store = s.id_store
            LEFT JOIN store_customers cust ON sc.id_customer = cust.id_store_customer
            LEFT JOIN store_carshop_detail scd ON sc.id_carshop = scd.id_carshop
            LEFT JOIN products p ON scd.id_product = p.id_product
            WHERE sc.active = 0  -- Abandoned carts only
            AND sc.id_store = %s
            GROUP BY 
                s.id_store,
                s.name,
                sc.id_customer,
                cust.name,
                cust.email,
                cust.provincia,
                cust.ciudad,
                cust.created_at,
                sc.id_carshop,
                sc.created_at,
                sc.active
            """
            cursor.execute(query, (store_id,))
            abandoned_cart_data = cursor.fetchall()
            
            # Convert decimal.Decimal to float and parse JSON strings
            for record in abandoned_cart_data:
                for key, value in record.items():
                    if hasattr(value, 'quantize'):  # Check if it's a Decimal
                        record[key] = float(value)
                    elif key == 'cart_products' and value:
                        # Parse the concatenated JSON strings
                        import json
                        products_str = f"[{value}]"
                        try:
                            record[key] = json.loads(products_str)
                        except:
                            record[key] = []
            
            logger.info(f"Fetched {len(abandoned_cart_data)} abandoned cart records for store {store_id}")
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error querying abandoned cart data for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error querying abandoned cart data for store {store_id}: {e}", exc_info=True)
    
    return abandoned_cart_data

def get_transaction_count(store_id: int, mysql_conn, last_updated: Optional[datetime] = None) -> int:
    """
    Get the total number of transaction records for a store to enable batched processing.
    FIXED: Now uses the same order status filter as the main query (2, 5, 7).
    
    Parameters:
    - store_id: The store ID to count transactions for
    - mysql_conn: MySQL connection
    - last_updated: Optional timestamp to only count transactions since the last update
    
    Returns:
    - int: Count of transaction records
    """
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_transaction_count (store: {store_id}).")
        return 0
    
    try:
        with mysql_conn.cursor() as cursor:
            # Build query with optional timestamp filter
            if last_updated:
                query = """
                SELECT COUNT(*) AS count
                FROM store_orders so
                JOIN store_ordered_products sop ON so.id_store_order = sop.id_store_order
                WHERE so.id_order_status IN (2, 5, 7)  -- FIXED: Match main query filter
                AND so.id_store = %s
                AND so.created_at > %s
                """
                cursor.execute(query, (store_id, last_updated))
            else:
                query = """
                SELECT COUNT(*) AS count
                FROM store_orders so
                JOIN store_ordered_products sop ON so.id_store_order = sop.id_store_order
                WHERE so.id_order_status IN (2, 5, 7)  -- FIXED: Match main query filter
                AND so.id_store = %s
                """
                cursor.execute(query, (store_id,))
                
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            if last_updated:
                logger.info(f"Store {store_id} has {count} transaction records since {last_updated.isoformat()}")
            else:
                logger.info(f"Store {store_id} has {count} total transaction records")
                
            return count
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error getting transaction count for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error getting transaction count for store {store_id}: {e}", exc_info=True)
    
    return 0

def detect_country(address='', telephone='', dni='', city='', province=''):
    """
    Enhanced country detection based on multiple data points
    Returns standardized country name
    FIXED: Added better null/empty handling for telephone parameter
    """
    country = None
    
    # 1. Province/State/City Detection
    if province or city:
        location_lower = (str(province) or str(city)).lower().strip() # Ensure string conversion
        
        # Uruguay locations (including Montevideo neighborhoods)
        uruguay_locations = [
            'montevideo', 'maldonado', 'canelones', 'colonia', 'salto',
            'paysandú', 'rivera', 'artigas', 'tacuarembó',
            # Montevideo neighborhoods
            'malvin', 'pocitos', 'punta carretas', 'ciudad vieja', 'centro',
            'cordón', 'parque rodó', 'buceo', 'carrasco', 'prado'
        ]
        
        # Argentina
        if any(state in location_lower for state in [
            'buenos aires', 'córdoba', 'santa fe', 'mendoza', 'tucumán', 
            'entre ríos', 'salta', 'chaco', 'corrientes', 'misiones', 'formosa'
        ]):
            country = 'Argentina'
        
        # Uruguay - check first as it's more specific
        elif any(loc in location_lower for loc in uruguay_locations):
            country = 'Uruguay'
        
        # Peru
        elif any(state in location_lower for state in [
            'lima', 'arequipa', 'cusco', 'trujillo', 'piura', 'chiclayo',
            'ica', 'huancayo', 'tacna', 'cajamarca', 'ilo'
        ]):
            country = 'Peru'
        
        # Rest of the countries remain the same...
        elif any(state in location_lower for state in [
            'madrid', 'barcelona', 'valencia', 'sevilla', 'zaragoza',
            'málaga', 'murcia', 'palma', 'bilbao', 'alicante', 'córdoba',
            'valladolid', 'vigo', 'gijón'
        ]):
            country = 'Spain'
        
        elif any(state in location_lower for state in [
            'santiago', 'valparaíso', 'biobío', 'antofagasta', 'maule',
            'o\'higgins', 'los lagos', 'tarapacá', 'atacama'
        ]):
            country = 'Chile'
        
        elif any(state in location_lower for state in [
            'bogotá', 'medellín', 'cali', 'barranquilla', 'cartagena',
            'cúcuta', 'bucaramanga', 'pereira', 'santa marta'
        ]):
            country = 'Colombia'
        
        elif any(state in location_lower for state in [
            'ciudad de méxico', 'jalisco', 'nuevo león', 'puebla', 'guanajuato',
            'chihuahua', 'baja california', 'sonora', 'tamaulipas'
        ]):
            country = 'Mexico'
    
    # 2. Phone Number Format Detection
    if not country and telephone:
        # FIXED: Better null/empty handling
        if telephone is None or str(telephone).strip() == '':
            pass  # Skip phone detection if empty
        else:
            phone_clean = str(telephone).replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            
            # Uruguay specific patterns first
            if (phone_clean.startswith('09') and len(phone_clean) == 9) or \
               (phone_clean.startswith('2') and len(phone_clean) == 8) or \
               phone_clean.startswith('+598') or phone_clean.startswith('598'):
                country = 'Uruguay'
            
            # Argentina patterns
            elif (phone_clean.startswith('11') and len(phone_clean) == 10) or \
                 (phone_clean.startswith('15') and len(phone_clean) == 10) or \
                 phone_clean.startswith('+54') or phone_clean.startswith('54'):
                country = 'Argentina'
            
            # Peru patterns
            elif (phone_clean.startswith('1') and len(phone_clean) == 7) or \
                 (phone_clean.startswith('9') and len(phone_clean) == 9) or \
                 phone_clean.startswith('+51') or phone_clean.startswith('51'):
                country = 'Peru'
            
            # Spain patterns
            elif (phone_clean.startswith('6') and len(phone_clean) == 9) or \
                 (phone_clean.startswith('7') and len(phone_clean) == 9) or \
                 (phone_clean.startswith('8') and len(phone_clean) == 9) or \
                  (phone_clean.startswith('9') and len(phone_clean) == 9) or \
                  phone_clean.startswith('+34') or phone_clean.startswith('34'):
                country = 'Spain'
            
            # Chile patterns
            elif (phone_clean.startswith('2') and len(phone_clean) == 9) or \
                 (phone_clean.startswith('9') and len(phone_clean) == 9) or \
                 phone_clean.startswith('+56') or phone_clean.startswith('56'):
                country = 'Chile'
            
            # Colombia patterns
            elif (phone_clean.startswith('1') and len(phone_clean) == 10) or \
                 (phone_clean.startswith('3') and len(phone_clean) == 10) or \
                 (phone_clean.startswith('60') and len(phone_clean) == 10) or \
                 phone_clean.startswith('+57') or phone_clean.startswith('57'):
                country = 'Colombia'
            
            # Mexico patterns
            elif (phone_clean.startswith('55') and len(phone_clean) == 10) or \
                 (phone_clean.startswith('81') and len(phone_clean) == 10) or \
                 (phone_clean.startswith('33') and len(phone_clean) == 10) or \
                 phone_clean.startswith('+52') or phone_clean.startswith('52'):
                country = 'Mexico'
    
    # 3. DNI/Document Format Detection
    if not country and dni:
        dni_clean = str(dni).replace('.', '').replace('-', '').strip() # Ensure string conversion
        
        # Uruguay CI - 8 digits with specific validation
        def is_valid_uy_ci(ci):
            if not (len(ci) == 8 and ci.isdigit()):
                return False
            # Uruguay CI validation algorithm
            validation = [2, 9, 8, 7, 6, 3, 4]
            total = 0
            for i in range(7):
                total += int(ci[i]) * validation[i]
            check_digit = (10 - (total % 10)) % 10
            return int(ci[7]) == check_digit
        
        if is_valid_uy_ci(dni_clean):
            country = 'Uruguay'
        # Argentina - 8 digits
        elif len(dni_clean) == 8 and dni_clean.isdigit():
            country = 'Argentina'
        # Peru - 8 digits
        elif len(dni_clean) == 8 and dni_clean.isdigit():
            country = 'Peru'
        # Spain - 8 chars (numbers + letter)
        elif len(dni_clean) == 9 and dni_clean[:-1].isdigit() and dni_clean[-1].isalpha():
            country = 'Spain'
        # Chile - RUT format
        elif len(dni_clean) in [9, 10] and dni_clean[:-1].isdigit() and (dni_clean[-1].isdigit() or dni_clean[-1].lower() == 'k'): # handle lowercase k
            country = 'Chile'
        # Colombia - Cédula
        elif len(dni_clean) == 10 and dni_clean.isdigit():
            country = 'Colombia'
        # Mexico - CURP
        elif len(dni_clean) == 18 and dni_clean[:4].isalpha():
            country = 'Mexico'
    
    # 4. Address Keywords Detection
    if not country and address:
        address_lower = str(address).lower().strip() # Ensure string conversion
        
        # Uruguay specific streets and areas first
        uruguay_streets = [
            'estrazulas', '18 de julio', 'avenida italia', 'rivera',
            'agraciada', 'boulevard artigas', 'rambla', 'malvin',
            'pocitos', 'punta carretas'
        ]
        
        if any(street in address_lower for street in uruguay_streets):
            country = 'Uruguay'
        # Then check other countries
        elif any(term in address_lower for term in [
            'argentina', 'caba', 'buenos aires', 'rosario', 'córdoba', 'mendoza'
        ]):
            country = 'Argentina'
        elif any(term in address_lower for term in [ # This elif might be redundant if covered by uruguay_streets and province/city check
            'uruguay', 'montevideo', 'maldonado', 'punta del este', 'colonia'
        ]) and not country: # Ensure not to overwrite if already found by more specific means
             country = 'Uruguay'
        elif any(term in address_lower for term in [
            'perú', 'peru', 'lima', 'arequipa', 'cusco', 'trujillo', 'ilo'
        ]):
            country = 'Peru'
        elif any(term in address_lower for term in [
            'españa', 'spain', 'madrid', 'barcelona', 'valencia', 'sevilla'
        ]):
            country = 'Spain'
        elif any(term in address_lower for term in [
            'chile', 'santiago', 'valparaíso', 'concepción', 'antofagasta'
        ]):
            country = 'Chile'
        elif any(term in address_lower for term in [
            'colombia', 'bogotá', 'medellín', 'cali', 'barranquilla'
        ]):
            country = 'Colombia'
        elif any(term in address_lower for term in [
            'méxico', 'mexico', 'cdmx', 'guadalajara', 'monterrey'
        ]):
            country = 'Mexico'
    
    return country

def process_customer_cache_data(transactions, store_id, abandoned_carts=None, should_anonymize=True):
    """
    Process transaction data and abandoned cart data and prepare for cache storage
    - Group by customer email instead of ID to merge different IDs with same email
    - Compute customer metrics and order details
    - Process abandoned cart data for customers with no paid orders
    - Detect country based on address and other fields
    - Anonymize sensitive data if should_anonymize is True
    - Returns data formatted for MongoDB store_customers_cache
    """
    if not transactions and not abandoned_carts:
        logger.warning(f"No transactions or abandoned carts passed to process_customer_cache_data for store {store_id}")
        return {"customers": []}
    
    # Add debug log to show field names
    if transactions:
        sample_tx = transactions[0]
        logger.debug(f"Sample transaction fields for store {store_id}: {list(sample_tx.keys())}")
    if abandoned_carts:
        sample_cart = abandoned_carts[0]
        logger.debug(f"Sample abandoned cart fields for store {store_id}: {list(sample_cart.keys())}")
    
    # Group by customer email for registered stores, by customer_id for anonymous stores
    customer_transactions = {}
    # Track customer_ids for each grouping key
    customer_ids_by_key = {}
    
    # Process transactions if available
    if transactions:
        for tx in transactions:
            # Skip transactions without customer data or email
            if not tx.get('id_store_customer') or not tx.get('customer_email'):
                logger.debug(f"Skipping transaction - missing id_store_customer or customer_email: {tx.get('id_store_order')}")
                continue
            
            # Choose grouping key based on anonymization
            if should_anonymize:
                # For anonymous stores, group by customer_id to maintain individual customers
                grouping_key = str(tx.get('id_store_customer'))
            else:
                # For registered stores, group by email to merge same emails with different IDs
                grouping_key = tx.get('customer_email').lower().strip()
            
            customer_id = tx.get('id_store_customer')
            
            # Initialize data structure for this grouping key if first encounter
            if grouping_key not in customer_transactions:
                customer_transactions[grouping_key] = {'transactions': [], 'abandoned_carts': []}
                customer_ids_by_key[grouping_key] = set()
            
            # Add this customer_id to the set for this grouping key
            customer_ids_by_key[grouping_key].add(customer_id)
            
            # Add transaction to grouping key's transactions
            customer_transactions[grouping_key]['transactions'].append(tx)
    
    # Process abandoned carts if available
    if abandoned_carts:
        for cart in abandoned_carts:
            # Skip carts without customer data or email
            if not cart.get('id_store_customer') or not cart.get('customer_email'):
                logger.debug(f"Skipping abandoned cart - missing id_store_customer or customer_email: {cart.get('id_carshop')}")
                continue
            
            # Choose grouping key based on anonymization (same logic as transactions)
            if should_anonymize:
                # For anonymous stores, group by customer_id to maintain individual customers
                grouping_key = str(cart.get('id_store_customer'))
            else:
                # For registered stores, group by email to merge same emails with different IDs
                grouping_key = cart.get('customer_email').lower().strip()
            
            customer_id = cart.get('id_store_customer')
            
            # Initialize data structure for this grouping key if first encounter
            if grouping_key not in customer_transactions:
                customer_transactions[grouping_key] = {'transactions': [], 'abandoned_carts': []}
                customer_ids_by_key[grouping_key] = set()
            
            # Add this customer_id to the set for this grouping key
            customer_ids_by_key[grouping_key].add(customer_id)
            
            # Add abandoned cart to grouping key's data
            customer_transactions[grouping_key]['abandoned_carts'].append(cart)
    
    # Log the count of unique customers found
    total_transactions = len(transactions) if transactions else 0
    total_abandoned_carts = len(abandoned_carts) if abandoned_carts else 0
    logger.info(f"Found {len(customer_transactions)} unique customers from {total_transactions} transactions and {total_abandoned_carts} abandoned carts for store {store_id}")
    
    # Process customers data
    customers_data = []
    
    for grouping_key, customer_data_dict in customer_transactions.items():
        customer_tx_list = customer_data_dict['transactions']
        customer_cart_list = customer_data_dict['abandoned_carts']
        
        # Get first transaction or first cart for customer details (assuming consistent data)
        first_record = customer_tx_list[0] if customer_tx_list else customer_cart_list[0] if customer_cart_list else None
        if not first_record:
            continue
        
        # Get the original customer email before any potential anonymization
        original_customer_email = first_record.get('customer_email', '').lower().strip()
        
        # Initialize customer data
        customer_data = {
            'store_id': store_id,
            'customer_id': list(customer_ids_by_key[grouping_key])[0],  # Use first ID as canonical
            'customer_email': original_customer_email,  # Use original email (will be anonymized later if needed)
            'customer_ids': list(customer_ids_by_key[grouping_key]),  # All IDs for this grouping key
            'customer_name': first_record.get('customer_name', ''),
            'customer_dni': first_record.get('customer_dni', ''),
            'total_orders': 0,
            'total_spend': 0,
            'first_order_date': None,
            'last_order_date': None,
            'preferred_payment_method': '',
            'preferred_shipping_method': '',
            'orders': [],
            'abandoned_carts': [],  # Add abandoned carts field
            'unique_products': set(),
            'payment_methods': Counter(), # Use Counter for easier updates
            'shipping_methods': Counter(),# Use Counter for easier updates
            'has_paid_orders': len(customer_tx_list) > 0,  # Track if customer has paid orders
        }
        
        # Detect country from address and other fields
        city = first_record.get('customer_city', '')
        province = first_record.get('customer_state', '')
        # postal_code = first_record.get('order_postal_code', '') # Not used in detect_country
        
        customer_data['country'] = detect_country(
            city=city,
            province=province,
            dni=first_record.get('customer_dni', ''),
            # Pass other relevant fields from first_record if available and used by detect_country
            address=first_record.get('customer_address', ''), # Example if address is available
            telephone=first_record.get('customer_phone', '') # Example if phone is available
        )
        
        # Group transactions by order_id
        orders = {}
        for tx in customer_tx_list: # Use renamed variable
            order_id = tx.get('id_store_order')
            if not order_id:
                continue
                
            # Initialize new order
            if order_id not in orders:
                order_date = tx.get('transaction_date')
                
                # Set first order date if not set or earlier than current
                if not customer_data['first_order_date'] or (order_date and order_date < customer_data['first_order_date']):
                    customer_data['first_order_date'] = order_date
                
                # Set last order date if not set or later than current
                if not customer_data['last_order_date'] or (order_date and order_date > customer_data['last_order_date']):
                    customer_data['last_order_date'] = order_date
                
                # Calculate total from the pre-calculated SQL value
                total = float(tx.get('order_calculated_total', 0.0))
                # Fetch item total and shipping cost for discount calculation later
                order_item_total = float(tx.get('total_item_price', 0.0)) 
                shipping_cost = float(tx.get('shipping_cost', 0) or 0) 
                
                # Initialize order data
                orders[order_id] = {
                    'order_id': order_id,
                    'order_date': order_date,
                    'total': total, 
                    'status': tx.get('order_status', ''),
                    'payment_method': tx.get('payment_method', ''),
                    'shipping_method': tx.get('shipping_method', ''),
                    'shipping_cost': shipping_cost,
                    'order_item_total': order_item_total, 
                    'products': [],
                    'coupon_code': None,
                    'coupon_amount': 0.0 
                }
                
                # Add coupon info if present (only once per order)
                coupon_code = tx.get('coupon_code')
                coupon_amount = float(tx.get('coupon_amount', 0) or 0) 
                if coupon_code:
                    orders[order_id]['coupon_code'] = coupon_code
                    orders[order_id]['coupon_amount'] = coupon_amount 
                
                # Update payment method count
                payment_method = tx.get('payment_method', '')
                if payment_method:
                    customer_data['payment_methods'][payment_method] += 1
                
                # Update shipping method count
                shipping_method = tx.get('shipping_method', '')
                if shipping_method:
                    customer_data['shipping_methods'][shipping_method] += 1
                
                # Increment order count
                customer_data['total_orders'] += 1
                customer_data['total_spend'] += total
            
            # Add product to order
            product_id = tx.get('id_product')
            if product_id:
                orders[order_id]['products'].append({
                    'product_id': product_id,
                    'product_name': tx.get('product_name', ''),
                    'quantity': int(tx.get('quantity', 1)),
                    'price': float(tx.get('unit_price', 0))
                })
                customer_data['unique_products'].add(product_id)
        
        # Determine preferred payment method
        if customer_data['payment_methods']:
            preferred_payment = customer_data['payment_methods'].most_common(1)[0][0]
            customer_data['preferred_payment_method'] = preferred_payment
        
        # Determine preferred shipping method
        if customer_data['shipping_methods']:
            preferred_shipping = customer_data['shipping_methods'].most_common(1)[0][0]
            customer_data['preferred_shipping_method'] = preferred_shipping
        
        # Convert orders dict to list
        customer_data['orders'] = list(orders.values())
        
        # Process abandoned carts for this customer
        for cart in customer_cart_list:
            cart_data = {
                'cart_id': cart.get('id_carshop'),
                'cart_created_date': cart.get('cart_created_at'),
                'cart_total_value': float(cart.get('cart_total_value', 0)),
                'unique_products_in_cart': cart.get('unique_products_in_cart', 0),
                'total_items_in_cart': cart.get('total_items_in_cart', 0),
                'products': cart.get('cart_products', []) or []
            }
            customer_data['abandoned_carts'].append(cart_data)
            
            # Add cart products to unique products set
            if cart.get('cart_products'):
                for product in cart.get('cart_products', []):
                    if isinstance(product, dict) and product.get('product_id'):
                        customer_data['unique_products'].add(product.get('product_id'))
        
        # Convert unique_products set to count
        customer_data['unique_products_count'] = len(customer_data['unique_products'])
        customer_data.pop('unique_products')  # Remove the set as it's not needed anymore
        
        # Anonymize sensitive data if requested
        if should_anonymize:
            logger.debug(f"Anonymizing customer data for store {store_id}")
            customer_data = anonymize_customer_data(customer_data)
        
        # Add to customers data
        customers_data.append(customer_data)
    
    return {"customers": customers_data}

def calculate_store_aggregations(customers_data: List[Dict]) -> Dict:
    """
    Calculates store-level aggregations from processed customer data.

    Parameters:
    - customers_data: List of processed customer dictionaries.

    Returns:
    - Dict: Dictionary containing aggregated store metrics.
    """
    total_customers = len(customers_data)
    total_revenue = 0.0
    total_orders_count = 0
    # Renamed variable to reflect monetary value
    total_monetary_coupon_discount = 0.0 
    
    # Abandoned cart metrics
    total_abandoned_carts = 0
    total_abandoned_cart_value = 0.0
    customers_with_abandoned_carts = 0
    customers_with_only_abandoned_carts = 0

    status_counts = Counter()
    payment_counts = Counter()
    shipping_counts = Counter()
    country_counts = Counter()
    coupon_counts = Counter()

    for customer in customers_data:
        # Aggregate country
        country = customer.get('country')
        if country:
            country_counts[country] += 1
        
        # Process abandoned carts
        abandoned_carts = customer.get('abandoned_carts', [])
        if abandoned_carts:
            customers_with_abandoned_carts += 1
            total_abandoned_carts += len(abandoned_carts)
            for cart in abandoned_carts:
                total_abandoned_cart_value += cart.get('cart_total_value', 0.0)
        
        # Check if customer has only abandoned carts (no paid orders)
        if not customer.get('has_paid_orders', True) and abandoned_carts:
            customers_with_only_abandoned_carts += 1

        # Aggregate order-level data
        for order in customer.get('orders', []):
            total_orders_count += 1
            total_revenue += order.get('total', 0.0)

            status = order.get('status')
            if status:
                status_counts[status] += 1

            payment = order.get('payment_method')
            if payment:
                payment_counts[payment] += 1

            shipping = order.get('shipping_method')
            if shipping:
                shipping_counts[shipping] += 1

            # Calculate monetary coupon discount based on item total and percentage
            item_total = order.get('order_item_total', 0.0) 
            coupon_percent = order.get('coupon_amount', 0.0) 

            if coupon_percent > 0 and item_total > 0: 
                monetary_discount = item_total * (coupon_percent / 100.0)
                total_monetary_coupon_discount += monetary_discount

            # Count coupon codes separately for frequency tracking
            coupon_code = order.get('coupon_code')
            if coupon_code:
                coupon_counts[coupon_code] += 1

    # Calculate averages
    # average_order_value_store = total_revenue / total_orders_count if total_orders_count > 0 else 0.0 # Removed per plan
    average_spend_per_customer = total_revenue / total_customers if total_customers > 0 else 0.0

    # Find most frequent items
    most_frequent_payment = payment_counts.most_common(1)
    most_frequent_shipping = shipping_counts.most_common(1)
    most_frequent_coupon = coupon_counts.most_common(1)

    aggregated_data = {
        'total_customers': total_customers,
        'total_store_orders': total_orders_count,
        # 'total_store_revenue': round(total_revenue, 2), # Removed per plan
        'average_spend_per_customer': round(average_spend_per_customer, 2), # Kept average spend per customer
        # 'average_order_value_store': round(average_order_value_store, 2), # Removed per plan
        'status_distribution': dict(status_counts),
        'payment_method_distribution': dict(payment_counts),
        'shipping_method_distribution': dict(shipping_counts),
        'country_distribution': dict(country_counts),
        'coupon_code_distribution': dict(coupon_counts),
        'most_frequent_payment_method': {
            'name': most_frequent_payment[0][0] if most_frequent_payment else None,
            'count': most_frequent_payment[0][1] if most_frequent_payment else 0
        },
        'most_frequent_shipping_method': {
            'name': most_frequent_shipping[0][0] if most_frequent_shipping else None,
            'count': most_frequent_shipping[0][1] if most_frequent_shipping else 0
        },
        'most_frequent_coupon_code': {
            'code': most_frequent_coupon[0][0] if most_frequent_coupon else None,
            'count': most_frequent_coupon[0][1] if most_frequent_coupon else 0
        },
        # Abandoned cart metrics
        'total_abandoned_carts': total_abandoned_carts,
        'total_abandoned_cart_value': round(total_abandoned_cart_value, 2),
        'customers_with_abandoned_carts': customers_with_abandoned_carts,
        'customers_with_only_abandoned_carts': customers_with_only_abandoned_carts,
        'abandoned_cart_rate': round((customers_with_abandoned_carts / total_customers) * 100, 2) if total_customers > 0 else 0.0,
        'last_customer_aggregation_update': datetime.now(timezone.utc)
    }

    return aggregated_data

def retry_mongo_operation(operation, *args, **kwargs):
    """
    Retry helper function for MongoDB operations with escalating timeouts.
    
    Parameters:
    - operation: The MongoDB operation function to execute
    - args, kwargs: Arguments to pass to the operation function
    
    Returns:
    - The result of the operation, or None if all retries failed
    """
    max_retries = 3
    retry_delay = 5  # seconds between retries
    
    # Define timeout escalation sequence (in seconds)
    timeouts = [120, 300, 600]  # 2 min, 5 min, 10 min
    
    for attempt in range(max_retries):
        try:
            # Use current client for first attempt
            if attempt == 0:
                return operation(*args, **kwargs)
            else:
                # For retries, create a temporary client with longer timeout
                logger.info(f"Creating temporary MongoDB client with {timeouts[attempt-1]} second timeout for retry #{attempt+1}") # Corrected timeout index
                
                # Get connection string from settings
                from config.settings import get_settings # Already imported, but keep for clarity
                settings_instance = get_settings()
                
                if hasattr(settings_instance, 'MONGODB_CONNECTION'):
                    # Create temp client with longer timeout
                    temp_client = MongoClient(
                        settings_instance.MONGODB_CONNECTION,
                        socketTimeoutMS=timeouts[attempt-1] * 1000, # Corrected timeout index
                        connectTimeoutMS=timeouts[attempt-1] * 1000, # Corrected timeout index
                        serverSelectionTimeoutMS=timeouts[attempt-1] * 1000 # Corrected timeout index
                    )
                    
                    # Get temp database and collection
                    temp_db = temp_client[MONGO_DB_ANALYSIS_NAME]
                    
                    # Replace collection in the operation with temp_collection
                    if hasattr(operation, '__self__'):
                        # For bound methods (like collection.update_one)
                        collection_name = operation.__self__.name
                        temp_collection = temp_db[collection_name]
                        
                        # Replace the first argument (self) with temp_collection
                        bound_method = getattr(temp_collection, operation.__name__)
                        result = bound_method(*args, **kwargs)
                    else:
                        # For regular functions
                        result = operation(*args, **kwargs) # This branch might not be used with current script structure
                    
                    # Close temp client after operation
                    temp_client.close()
                    return result
                else:
                    # Fall back to original operation
                    logger.warning("MONGODB_CONNECTION setting not found, falling back to original operation without timeout adjustment.")
                    return operation(*args, **kwargs)
                
        except (NetworkTimeout, ConnectionFailure) as err:
            if attempt < max_retries - 1:
                logger.warning(f"MongoDB timeout error, retrying in {retry_delay} seconds with longer timeout (attempt {attempt+1}/{max_retries}): {err}")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff for delay between retries
            else:
                # Last attempt failed
                logger.error(f"MongoDB operation failed after {max_retries} attempts with increasing timeouts: {err}")
                raise
    # This part should ideally not be reached if an exception is raised on the last attempt.
    # However, to satisfy linters or strict typing, returning None or raising an explicit error here.
    logger.error(f"MongoDB operation failed after all retries and did not raise an exception as expected.")
    raise OperationFailure("MongoDB operation failed after all retries.")


def update_store_customers_cache(store_id: str, cache_data: Dict[str, List[Dict[str, Any]]], aggregated_data: Dict, mongo_db, is_registered: bool = False) -> bool:
    """
    Updates the store customers cache data in MongoDB, including aggregated metrics.

    Parameters:
    - store_id: The store ID
    - cache_data: The processed customer cache data (containing {"customers": [...]})
    - aggregated_data: Dictionary containing calculated store-level aggregations
    - mongo_db: MongoDB database connection
    - is_registered: Whether the store is registered (has pass_dunit)

    Returns:
    - bool: True if successful, False otherwise
    """
    if mongo_db is None:
        logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update customer cache.")
        return False
    
    try:
        collection = mongo_db[STORE_CUSTOMERS_CACHE_COLLECTION]
        now_utc = datetime.now(timezone.utc)
        
        # Prepare document for storage
        document = {
            "_id": str(store_id),
            "customers": cache_data["customers"],
            "last_updated": now_utc,
            "only_paid_orders": True,  # Flag to indicate this cache contains only paid orders (status 2, 5, 7)
            "is_registered": is_registered,
            "has_pii": is_registered
        }
        
        # Merge aggregated data into the main document
        if aggregated_data:
             document.update(aggregated_data)
        else:
             logger.warning(f"Aggregated data is missing for store {store_id}, not adding to cache document.")

        # Update or insert document using retry logic
        result = retry_mongo_operation(
            collection.update_one,
            {"_id": str(store_id)},
            {"$set": document},
            upsert=True
        )
        
        if result:
            if result.upserted_id:
                logger.info(f"Inserted new store customers cache data for store {store_id} with {len(cache_data['customers'])} customers")
            elif result.modified_count > 0:
                logger.info(f"Updated store customers cache data for store {store_id} with {len(cache_data['customers'])} customers")
            else:
                logger.info(f"No changes needed for store customers cache data for store {store_id} (data identical)")
            return True
        else:
            logger.warning(f"MongoDB update operation returned no result for store {store_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error updating MongoDB store customers cache for store {store_id}: {e}", exc_info=True)
        return False

def fetch_and_calculate_cart_aggregations(store_id: int, mysql_conn) -> Dict[str, Any]:
    """
    Fetches and calculates cart aggregation metrics for a given store.
    FIXED: Updated queries to exclude unpaid orders (status 1) from pending cart calculations.
    FIXED: Uses separate cursors for each query to prevent "Commands out of sync" error.

    Parameters:
    - store_id: The store ID
    - mysql_conn: MySQL connection object

    Returns:
    - Dict: Containing cart aggregation metrics
    """
    cart_aggregations = {
        'abandoned_cart_count': 0,
        'abandoned_cart_customer_count': 0,
        'abandoned_cart_total_value': 0.0,
        'pending_cart_count': 0,
        'pending_cart_total_value': 0.0
    }

    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for fetch_and_calculate_cart_aggregations (store: {store_id}).")
        return cart_aggregations

    try:
        # Query 1: Abandoned Cart Count
        try:
            with mysql_conn.cursor(dictionary=True) as cursor:
                query1 = """
                SELECT COUNT(id_carshop) AS abandoned_cart_count
                FROM store_carshop
                WHERE id_store = %s AND active = 0;
                """
                cursor.execute(query1, (store_id,))
                result1 = cursor.fetchone()
                if result1 and result1.get('abandoned_cart_count') is not None:
                    cart_aggregations['abandoned_cart_count'] = int(result1['abandoned_cart_count'])
        except mysql.connector.Error as err:
            logger.warning(f"MySQL error fetching abandoned cart count for store {store_id}: {err}")

        # Query 2: Abandoned Cart Customer Count
        try:
            with mysql_conn.cursor(dictionary=True) as cursor:
                query2 = """
                SELECT COUNT(DISTINCT id_customer) AS abandoned_cart_customer_count
                FROM store_carshop
                WHERE id_store = %s AND active = 0;
                """
                cursor.execute(query2, (store_id,))
                result2 = cursor.fetchone()
                if result2 and result2.get('abandoned_cart_customer_count') is not None:
                    cart_aggregations['abandoned_cart_customer_count'] = int(result2['abandoned_cart_customer_count'])
        except mysql.connector.Error as err:
            logger.warning(f"MySQL error fetching abandoned cart customer count for store {store_id}: {err}")

        # Query 3: Abandoned Cart Total Value
        try:
            with mysql_conn.cursor(dictionary=True) as cursor:
                query3 = """
                SELECT SUM(COALESCE(scd.qty, 0) * COALESCE(scd.price, 0)) AS abandoned_cart_total_value
                FROM store_carshop sc
                JOIN store_carshop_detail scd ON sc.id_carshop = scd.id_carshop
                WHERE sc.id_store = %s AND sc.active = 0;
                """
                cursor.execute(query3, (store_id,))
                result3 = cursor.fetchone()
                if result3 and result3.get('abandoned_cart_total_value') is not None:
                    cart_aggregations['abandoned_cart_total_value'] = float(result3['abandoned_cart_total_value'])
        except mysql.connector.Error as err:
            logger.warning(f"MySQL error fetching abandoned cart total value for store {store_id}: {err}")

        # Query 4: Pending/Cancelled Cart Count
        try:
            with mysql_conn.cursor(dictionary=True) as cursor:
                query4 = """
                SELECT COUNT(DISTINCT sc.id_carshop) as pending_cart_count
                FROM store_carshop sc
                LEFT JOIN store_orders so ON sc.id_customer = so.id_store_customer 
                    AND sc.id_store = so.id_store 
                    AND so.created_at >= sc.created_at
                WHERE sc.id_store = %s
                  AND sc.active = 1
                  AND (so.id_store_order IS NULL OR so.id_order_status IN (3, 4, 6));
                """
                cursor.execute(query4, (store_id,))
                result4 = cursor.fetchone()
                if result4 and result4.get('pending_cart_count') is not None:
                    cart_aggregations['pending_cart_count'] = int(result4['pending_cart_count'])
        except mysql.connector.Error as err:
            logger.warning(f"MySQL error fetching pending cart count for store {store_id}: {err}")

        # Query 5: Pending/Cancelled Cart Value
        try:
            with mysql_conn.cursor(dictionary=True) as cursor:
                query5 = """
                SELECT SUM(COALESCE(scd.qty, 0) * COALESCE(scd.price, 0)) AS pending_cart_total_value
                FROM store_carshop sc
                JOIN store_carshop_detail scd ON sc.id_carshop = scd.id_carshop
                LEFT JOIN store_orders so ON sc.id_customer = so.id_store_customer 
                    AND sc.id_store = so.id_store 
                    AND so.created_at >= sc.created_at
                WHERE sc.id_store = %s
                  AND sc.active = 1
                  AND (so.id_store_order IS NULL OR so.id_order_status IN (3, 4, 6));
                """
                cursor.execute(query5, (store_id,))
                result5 = cursor.fetchone()
                if result5 and result5.get('pending_cart_total_value') is not None:
                    cart_aggregations['pending_cart_total_value'] = float(result5['pending_cart_total_value'])
        except mysql.connector.Error as err:
            logger.warning(f"MySQL error fetching pending cart total value for store {store_id}: {err}")

    except Exception as e: # Catching broader exceptions here if mysql_conn itself is problematic
        logger.error(f"Unexpected error in fetch_and_calculate_cart_aggregations for store {store_id}: {e}", exc_info=True)

    logger.info(f"Cart aggregations for store {store_id}: {cart_aggregations}")
    return cart_aggregations

def process_store_in_batches(store_id: int, mysql_conn, mongo_db, is_registered: bool = False) -> bool:
    """
    Process store customer data in batches with privacy protection.
    
    Parameters:
    - store_id: The store ID to process
    - mysql_conn: MySQL connection
    - mongo_db: MongoDB database connection
    - is_registered: Whether the store has a registered D-Unit user
    
    Returns:
    - bool: Success status
    """
    try:
        all_customers_data = {"customers": []}  # Initialize here for all paths

        # Optimization: Check for last updated timestamp in MongoDB
        last_updated = None
        collection = mongo_db[STORE_CUSTOMERS_CACHE_COLLECTION]
        existing_doc = None  # Initialize existing_doc
        try:
            existing_doc = collection.find_one({"_id": str(store_id)})
            if existing_doc and "last_updated" in existing_doc:
                last_updated = existing_doc["last_updated"]
                logger.info(f"Found previous cache document for store {store_id}, last updated at {last_updated}")
        except Exception as e:
            logger.warning(f"Error getting last updated timestamp for store {store_id}: {e}")
            # existing_doc might be None or remain as is if error occurred after find_one partially succeeded (unlikely for find_one)
        
        total_records = get_transaction_count(store_id, mysql_conn, last_updated)
        
        if total_records == 0:
            # This block handles cases with no new transactions (or no transactions at all)
            # all_customers_data is already {"customers": []}
            
            # Check for abandoned carts even if no transactions
            logger.info(f"Fetching abandoned cart data for store {store_id} (no transactions case)...")
            abandoned_cart_data = query_abandoned_cart_data_mysql(store_id, mysql_conn)
            
            customers_dict_for_zero_records = {} # Use a specific dict for this scope
            if last_updated: # Incremental update, no new transactions
                logger.info(f"No new transaction records found for store {store_id} since {last_updated}. Aggregations will be based on existing cache.")
                if existing_doc and "customers" in existing_doc:
                    # Load existing customers into customers_dict_for_zero_records
                    for customer in existing_doc["customers"]:
                        # Ensure customer_id is correctly obtained
                        customer_key = str(customer.get("customer_id", customer.get("id_store_customer")))
                        if customer_key and customer_key != 'None': # Check for valid key
                             customers_dict_for_zero_records[customer_key] = customer
                    logger.info(f"Loaded {len(customers_dict_for_zero_records)} existing customers from cache for store {store_id} for aggregation.")
                all_customers_data["customers"] = list(customers_dict_for_zero_records.values()) # Populate from existing if any
            else: # Full run, but no transactions found for the store
                logger.info(f"No transaction records found at all for store {store_id}.")
                # Process abandoned carts if they exist
                if abandoned_cart_data:
                    logger.info(f"Processing {len(abandoned_cart_data)} abandoned carts for store {store_id}")
                    # Determine if we should anonymize based on registration status
                    should_anonymize = not is_registered
                    abandoned_cart_result = process_customer_cache_data([], store_id, abandoned_cart_data, should_anonymize)
                    all_customers_data["customers"] = abandoned_cart_result.get("customers", [])
                # else all_customers_data["customers"] remains [] as initialized
            
            # Proceed to calculate aggregations even if no new customer data, cart data might have changed
            logger.info(f"Calculating store customer aggregations for store {store_id} (based on available data)...")
            aggregated_data = calculate_store_aggregations(all_customers_data["customers"])

            logger.info(f"Fetching and calculating cart aggregations for store {store_id}...")
            cart_aggregations = fetch_and_calculate_cart_aggregations(store_id, mysql_conn)
            aggregated_data.update(cart_aggregations)

            logger.info(f"Updating store customers cache (including all aggregations) for store {store_id}...")
            success = update_store_customers_cache(str(store_id), all_customers_data, aggregated_data, mongo_db, is_registered)
            return success
        
        # If execution reaches here, total_records > 0.
        # all_customers_data is already {"customers": []} but will be populated by customers_dict later.
        
        batch_size = BATCH_SIZE
        total_batches = (total_records + batch_size - 1) // batch_size
        logger.info(f"Processing store {store_id} in {total_batches} batches (total records: {total_records})")
        
        customers_dict = {} # This will accumulate customer data from batches
        if last_updated:
            try:
                if existing_doc and "customers" in existing_doc:
                    # Initialize with existing customers, using same grouping logic
                    for cust_profile in existing_doc["customers"]:
                        if is_registered:
                            # For registered stores, group by email
                            email = cust_profile.get("customer_email")
                            if email: # Ensure email exists
                               customers_dict[email.lower().strip()] = cust_profile
                        else:
                            # For anonymous stores, group by customer_id
                            customer_id = cust_profile.get("customer_id")
                            if customer_id: # Ensure customer_id exists
                               customers_dict[str(customer_id)] = cust_profile
                    logger.info(f"Starting with {len(customers_dict)} existing customer profiles from cache for incremental update")
            except Exception as e:
                logger.warning(f"Error retrieving existing customers for store {store_id} for merge: {e}")
                customers_dict = {} # Reset if error
        
        # Process each batch
        for batch_num in range(total_batches):
            offset = batch_num * batch_size
            
            logger.info(f"Processing batch {batch_num + 1}/{total_batches} for store {store_id} (offset: {offset})")
            
            batch_data = query_customer_data_mysql(store_id, mysql_conn, batch_size, offset, last_updated)
            
            # Fetch abandoned cart data for this store (only once for the entire store, not per batch)
            abandoned_cart_data = []
            if batch_num == 0:  # Only fetch abandoned carts on first batch to avoid duplication
                logger.info(f"Fetching abandoned cart data for store {store_id}...")
                abandoned_cart_data = query_abandoned_cart_data_mysql(store_id, mysql_conn)
            
            if not batch_data and not abandoned_cart_data:
                logger.warning(f"No transaction or abandoned cart data returned for batch {batch_num + 1}, skipping.")
                continue
            
            # Determine if we should anonymize based on registration status
            should_anonymize = not is_registered
            batch_result = process_customer_cache_data(batch_data, store_id, abandoned_cart_data, should_anonymize)
            
            if not batch_result.get("customers"):
                logger.warning(f"No customers found in processed batch {batch_num + 1} for store {store_id}")
                continue
                
            for customer_profile in batch_result["customers"]:
                # Use same grouping logic as in process_customer_cache_data
                if is_registered:
                    # For registered stores, group by email to merge customers with same email
                    grouping_key = customer_profile["customer_email"].lower().strip()
                else:
                    # For anonymous stores, group by customer_id to maintain individual customers
                    grouping_key = str(customer_profile["customer_id"])

                if grouping_key not in customers_dict:
                    customers_dict[grouping_key] = customer_profile
                else:
                    existing_customer_profile = customers_dict[grouping_key]
                    
                    existing_customer_profile["total_orders"] += customer_profile["total_orders"]
                    existing_customer_profile["total_spend"] += customer_profile["total_spend"]
                    
                    existing_customer_profile["customer_ids"] = list(set(existing_customer_profile.get("customer_ids", []) + customer_profile.get("customer_ids", [])))

                    if customer_profile.get("first_order_date") and \
                       (not existing_customer_profile.get("first_order_date") or 
                        customer_profile["first_order_date"] < existing_customer_profile["first_order_date"]):
                        existing_customer_profile["first_order_date"] = customer_profile["first_order_date"]
                    
                    if customer_profile.get("last_order_date") and \
                       (not existing_customer_profile.get("last_order_date") or 
                        customer_profile["last_order_date"] > existing_customer_profile["last_order_date"]):
                        existing_customer_profile["last_order_date"] = customer_profile["last_order_date"]
                    
                    order_ids_in_existing = {order['order_id'] for order in existing_customer_profile.get("orders", [])}
                    current_orders = existing_customer_profile.get("orders", [])
                    for new_order in customer_profile.get("orders", []):
                        if new_order['order_id'] not in order_ids_in_existing:
                            current_orders.append(new_order)
                            order_ids_in_existing.add(new_order['order_id'])
                    existing_customer_profile["orders"] = current_orders
                    
                    existing_customer_profile["orders"].sort(
                        key=lambda x: x.get('order_date', datetime.min.replace(tzinfo=timezone.utc)) if x.get('order_date') else datetime.min.replace(tzinfo=timezone.utc),
                        reverse=True
                    )
                    
                    # Merge abandoned carts (avoid duplicates by cart_id)
                    cart_ids_in_existing = {cart['cart_id'] for cart in existing_customer_profile.get("abandoned_carts", [])}
                    current_carts = existing_customer_profile.get("abandoned_carts", [])
                    for new_cart in customer_profile.get("abandoned_carts", []):
                        if new_cart['cart_id'] not in cart_ids_in_existing:
                            current_carts.append(new_cart)
                            cart_ids_in_existing.add(new_cart['cart_id'])
                    existing_customer_profile["abandoned_carts"] = current_carts
                    
                    # Update has_paid_orders flag
                    existing_customer_profile["has_paid_orders"] = existing_customer_profile.get("has_paid_orders", False) or customer_profile.get("has_paid_orders", False)

                    temp_payment_methods = Counter(existing_customer_profile.get('payment_methods', {}))
                    temp_shipping_methods = Counter(existing_customer_profile.get('shipping_methods', {}))
                    # Re-count from new orders in this batch for this customer
                    for order_in_batch in customer_profile.get("orders", []): # these are orders from the current batch for this email
                        if order_in_batch.get('payment_method'):
                            temp_payment_methods[order_in_batch['payment_method']] += 1 # This might double count if process_customer_cache_data already gives totals
                        if order_in_batch.get('shipping_method'):
                            temp_shipping_methods[order_in_batch['shipping_method']] +=1
                    # The Counter in process_customer_cache_data is for that batch's transactions for the email.
                    # We should re-aggregate payment/shipping methods from all orders of the customer.
                    
                    final_payment_methods = Counter()
                    final_shipping_methods = Counter()
                    final_unique_products = set()

                    for order in existing_customer_profile["orders"]: # Iterate all merged orders
                        if order.get('payment_method'):
                            final_payment_methods[order['payment_method']] +=1
                        if order.get('shipping_method'):
                            final_shipping_methods[order['shipping_method']] +=1
                        for prod in order.get('products', []):
                            final_unique_products.add(prod['product_id'])
                    
                    # Add products from abandoned carts to unique products count
                    for cart in existing_customer_profile.get("abandoned_carts", []):
                        for product in cart.get('products', []):
                            if isinstance(product, dict) and product.get('product_id'):
                                final_unique_products.add(product.get('product_id'))
                    
                    if final_payment_methods:
                        existing_customer_profile['preferred_payment_method'] = final_payment_methods.most_common(1)[0][0]
                    else:
                        existing_customer_profile['preferred_payment_method'] = '' # Reset if no payment methods
                    existing_customer_profile['payment_methods'] = dict(final_payment_methods)

                    if final_shipping_methods:
                        existing_customer_profile['preferred_shipping_method'] = final_shipping_methods.most_common(1)[0][0]
                    else:
                        existing_customer_profile['preferred_shipping_method'] = '' # Reset
                    existing_customer_profile['shipping_methods'] = dict(final_shipping_methods)
                    
                    existing_customer_profile['unique_products_count'] = len(final_unique_products)
            
            logger.info(f"Processed batch {batch_num + 1}/{total_batches} - merged data for {len(batch_result['customers'])} email profiles from this batch")
        
        all_customers_data["customers"] = list(customers_dict.values()) # This line should now work

        logger.info(f"Calculating store customer aggregations for store {store_id}...")
        aggregated_data = calculate_store_aggregations(all_customers_data["customers"])

        logger.info(f"Fetching and calculating cart aggregations for store {store_id}...")
        cart_aggregations = fetch_and_calculate_cart_aggregations(store_id, mysql_conn)
        aggregated_data.update(cart_aggregations)

        logger.info(f"Updating store customers cache (including all aggregations) for store {store_id}...")
        success = update_store_customers_cache(str(store_id), all_customers_data, aggregated_data, mongo_db, is_registered)

        return success

    except Exception as e:
        logger.error(f"Error processing store {store_id} in batches: {e}", exc_info=True)
        return False

# --- Main Execution ---

def main():
    # Process command-line arguments
    parser = argparse.ArgumentParser(description='Update store customers cache data with privacy protection')
    parser.add_argument('--store_id', type=str, help='Process only a specific store ID')
    parser.add_argument('--force_all', action='store_true', help='Process all stores regardless of registration status')
    parser.add_argument('--registered_only', action='store_true', help='Process only registered stores')
    parser.add_argument('--test_trigger', type=str, help='Test trigger function for specific store ID')
    args = parser.parse_args()
    
    specific_store_id = args.store_id
    force_all = args.force_all
    registered_only = args.registered_only
    test_trigger_store = args.test_trigger
    
    # Handle test trigger mode
    if test_trigger_store:
        logger.info(f"Running trigger test for store {test_trigger_store}")
        success = trigger_for_newly_registered_store(test_trigger_store)
        if success:
            logger.info("✅ Trigger test completed successfully")
        else:
            logger.error("❌ Trigger test failed")
        return
    
    logger.info("Starting store customers cache update script with privacy protection")
    logger.info("NOTE: This script only processes paid orders (status 2, 5, 7)")
    logger.info("NOTE: Customer PII is anonymized for non-registered stores")
    
    if specific_store_id:
        logger.info(f"Processing only store ID: {specific_store_id}")
    elif registered_only:
        logger.info("Processing only registered stores")
    elif force_all:
        logger.info("Processing all stores regardless of registration status")
    else:
        logger.info("Processing all stores (anonymizing non-registered stores)")
        
    mysql_conn = None
    mongo_client = None
    processed_count = 0
    failed_count = 0

    try:
        # 1. Establish Connections
        logger.info(f"Connecting to MySQL host: {MYSQL_HOST}, database: {MYSQL_DB_LANUBE}")
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=20, # Increased timeout slightly
            # autocommit=True # Consider if this helps, though typically not for read-heavy scripts
        )
        logger.info("MySQL connection successful")

        logger.info("Connecting to MongoDB...")
        mongo_client = get_mongodb_connection() # Assuming this handles its own retry/timeout logic
        if mongo_client is None:
            logger.error("Failed to establish MongoDB connection. Exiting.")
            # Allow script to proceed if mongo is optional for some parts, or raise
            raise ConnectionError("MongoDB connection failed.") 

        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        mongo_db_main = mongo_client[MONGO_DB_MAIN_NAME]
        logger.info(f"MongoDB connection successful (Analysis DB: {MONGO_DB_ANALYSIS_NAME}, Main DB: {MONGO_DB_MAIN_NAME})")
        
        # Get registered stores
        registered_stores = get_registered_stores(mongo_db_main)
        logger.info(f"Found {len(registered_stores)} registered stores in D-Unit")

        # 2. Get stores to process
        if specific_store_id:
            # Check if specific store is registered
            is_registered = str(specific_store_id) in registered_stores
            # If specific store_id is provided, just process that one
            store_details = None
            # Use a new cursor for this specific task
            with mysql_conn.cursor(dictionary=True) as cursor:
                cursor.execute("""
                    SELECT s.id_store, s.symbol, s.name  
                    FROM stores s
                    WHERE s.id_store = %s
                """, (specific_store_id,))
                store_details = cursor.fetchone()
            
            if store_details:
                active_stores_to_process = [store_details]
                logger.info(f"Found store with ID: {specific_store_id} (Registered: {is_registered})")
            else:
                logger.error(f"Store with ID {specific_store_id} not found. Exiting.")
                return # Exit if specific store not found
        else:
            # Filter stores based on mode
            if registered_only:
                active_stores_to_process = get_stores_to_process(mysql_conn, registered_stores_only=True, registered_stores=registered_stores)
            else:
                active_stores_to_process = get_stores_to_process(mysql_conn)
            
        if not active_stores_to_process:
            logger.warning("No active stores found to process. Exiting.")
            return

        # 3. Process each store
        logger.info(f"Processing {len(active_stores_to_process)} stores...")
        
        # New counters
        anonymized_count = 0
        registered_count = 0
        
        for store_data in active_stores_to_process: # Renamed loop variable
            current_store_id = None # To track current store ID for logging in case of error
            try:
                # Safely get store ID
                if not isinstance(store_data, dict):
                    logger.warning(f"Store data is not a dictionary: {store_data}. Skipping.")
                    failed_count += 1
                    continue

                # Use correct column name: id_store
                store_id_val = store_data.get('id_store')
                if store_id_val is None:
                    logger.warning(f"Skipping store entry due to missing 'id_store': {store_data}")
                    failed_count += 1
                    continue

                current_store_id = int(str(store_id_val))  # Convert fetched ID to int
                store_name = store_data.get('name', 'Unknown Store')
                
                # Check registration and skip logic
                is_registered = str(current_store_id) in registered_stores
                
                if registered_only and not is_registered:
                    logger.info(f"Skipping unregistered store ID: {current_store_id} ({store_name})")
                    continue
                
                logger.info(f"--- Processing store ID: {current_store_id} ({store_name}) - Registered: {is_registered} ---")
                
                if is_registered:
                    registered_count += 1
                else:
                    anonymized_count += 1

                # Check MySQL connection before processing each store
                if not mysql_conn or not mysql_conn.is_connected():
                    logger.error(f"MySQL connection lost before processing store {current_store_id}. Attempting to reconnect...")
                    try:
                        mysql_conn.close() # Close potentially broken connection
                    except Exception:
                        pass # Ignore errors during close
                    mysql_conn = mysql.connector.connect(
                        host=MYSQL_HOST, user=MYSQL_USER, password=MYSQL_PASSWORD,
                        database=MYSQL_DB_LANUBE, port=MYSQL_PORT, connect_timeout=20
                    )
                    logger.info(f"Successfully reconnected to MySQL for store {current_store_id}.")
                else:
                    # Ping connection to ensure it's alive
                    try:
                        mysql_conn.ping(reconnect=True, attempts=3, delay=5)
                    except mysql.connector.Error as ping_err:
                        logger.error(f"MySQL ping failed for store {current_store_id}: {ping_err}. Attempting to reconnect...")
                        try:
                            mysql_conn.close()
                        except Exception:
                            pass
                        mysql_conn = mysql.connector.connect(
                            host=MYSQL_HOST, user=MYSQL_USER, password=MYSQL_PASSWORD,
                            database=MYSQL_DB_LANUBE, port=MYSQL_PORT, connect_timeout=20
                        )
                        logger.info(f"Successfully reconnected to MySQL after ping fail for store {current_store_id}.")


                # 4. Process store in batches
                success = process_store_in_batches(current_store_id, mysql_conn, mongo_db, is_registered)
                
                if success:
                    processed_count += 1
                    logger.info(f"Successfully processed store customers cache for store {current_store_id} (PII included: {is_registered})")
                else:
                    failed_count += 1
                    logger.error(f"Failed to update store customers cache for store {current_store_id}")

            except mysql.connector.Error as db_err_loop: # Catch MySQL errors per store
                logger.error(f"MySQL Error processing store {current_store_id}: {db_err_loop}", exc_info=True)
                failed_count += 1
                # Attempt to re-establish connection for the next store
                if mysql_conn:
                    try:
                        mysql_conn.close()
                    except Exception:
                        pass # ignore
                try:
                    logger.info(f"Attempting to re-establish MySQL connection after error in store {current_store_id}...")
                    mysql_conn = mysql.connector.connect(
                        host=MYSQL_HOST, user=MYSQL_USER, password=MYSQL_PASSWORD,
                        database=MYSQL_DB_LANUBE, port=MYSQL_PORT, connect_timeout=20
                    )
                    logger.info("MySQL re-connection successful.")
                except mysql.connector.Error as recon_err:
                    logger.fatal(f"Failed to re-establish MySQL connection: {recon_err}. Exiting script.")
                    break # Exit main loop if can't reconnect
            except Exception as e:
                logger.error(f"Unexpected error processing store {current_store_id}: {e}", exc_info=True)
                failed_count += 1
                # Decide if you want to try to reconnect MySQL here as well or let the main error handling catch it.

        logger.info("--- Store processing finished ---")

    except mysql.connector.Error as db_err_main:
        logger.fatal(f"Main Database connection error: {db_err_main}", exc_info=True) # Changed to fatal
    except ConnectionError as conn_err_main: # MongoDB or other connection errors
        logger.fatal(f"Main Connection error: {conn_err_main}", exc_info=True) # Changed to fatal
    except Exception as e_main:
        logger.fatal(f"Unexpected critical error during script execution: {e_main}", exc_info=True) # Changed to fatal
    finally:
        # Close connections
        logger.info("Closing database connections...")
        if mysql_conn and mysql_conn.is_connected():
            try:
                mysql_conn.close()
                logger.info("MySQL connection closed.")
            except Exception as e_close_mysql:
                logger.error(f"Error closing MySQL connection: {e_close_mysql}")
        if mongo_client:
            try:
                mongo_client.close()
                logger.info("MongoDB connection closed.")
            except Exception as e_close_mongo:
                logger.error(f"Error closing MongoDB connection: {e_close_mongo}")

        logger.info("--- Script Summary ---")
        logger.info(f"Successfully processed/updated store customers cache for: {processed_count} stores")
        logger.info(f"Failed to process/update store customers cache for: {failed_count} stores")
        logger.info(f"- Registered stores with PII: {registered_count}")
        logger.info(f"- Anonymized stores: {anonymized_count}")
        logger.info("Script finished.")

# --- Trigger Functions for Newly Registered Stores ---

def trigger_for_newly_registered_store(store_id: str):
    """
    Trigger function to be called when a store registers on D-Unit.
    Processes the specific store with full PII access.
    
    Parameters:
    - store_id: The store ID that just registered
    """
    logger.info(f"Triggering customer data update for newly registered store: {store_id}")
    
    mysql_conn = None
    mongo_client = None
    
    try:
        # Establish connections
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=20
        )
        
        mongo_client = get_mongodb_connection()
        if mongo_client is None:
            raise ConnectionError("MongoDB connection failed.")
        
        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        
        # Force full reprocessing by clearing the last_updated timestamp
        # This ensures that all existing anonymized data gets replaced with real PII
        collection = mongo_db[STORE_CUSTOMERS_CACHE_COLLECTION]
        try:
            result = collection.update_one(
                {"_id": str(store_id)},
                {"$unset": {"last_updated": ""}}
            )
            if result.matched_count > 0:
                logger.info(f"Cleared last_updated timestamp for store {store_id} to force full reprocessing")
            else:
                logger.info(f"No existing cache found for store {store_id}, will do full processing")
        except Exception as e:
            logger.warning(f"Could not clear last_updated for store {store_id}: {e}")
        
        # Process the store with PII access (is_registered=True)
        success = process_store_in_batches(int(store_id), mysql_conn, mongo_db, is_registered=True)
        
        if success:
            logger.info(f"Successfully updated customer data with PII for newly registered store {store_id}")
        else:
            logger.error(f"Failed to update customer data for newly registered store {store_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error processing newly registered store {store_id}: {e}", exc_info=True)
        return False
    finally:
        # Close connections
        if mysql_conn and mysql_conn.is_connected():
            mysql_conn.close()
        if mongo_client:
            mongo_client.close()

async def process_store_async(store_id: str):
    """
    Asynchronous wrapper for processing a store.
    Can be used with FastAPI background tasks.
    
    Parameters:
    - store_id: The store ID to process
    """
    import asyncio
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, trigger_for_newly_registered_store, store_id)

# --- Utility Functions for Privacy Status and Bulk Updates ---

def get_store_customer_privacy_status(store_id: str, mongo_client=None) -> Dict[str, Any]:
    """
    Check the privacy status of a store's customer data.
    
    Parameters:
    - store_id: The store ID to check
    - mongo_client: Optional MongoDB client (will create if not provided)
    
    Returns:
    - Dict containing privacy status information
    """
    close_client = False
    if mongo_client is None:
        mongo_client = get_mongodb_connection()
        close_client = True
    
    if mongo_client is None:
        logger.error("Failed to establish MongoDB connection for privacy status check")
        return {"store_id": store_id, "error": "MongoDB connection failed"}
    
    try:
        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        mongo_db_main = mongo_client[MONGO_DB_MAIN_NAME]
        
        # Check if store is registered
        registered_stores = get_registered_stores(mongo_db_main)
        is_registered = str(store_id) in registered_stores
        
        # Check cache document
        collection = mongo_db[STORE_CUSTOMERS_CACHE_COLLECTION]
        cache_doc = collection.find_one({"_id": str(store_id)})
        
        status = {
            "store_id": store_id,
            "is_registered": is_registered,
            "has_cache_data": cache_doc is not None,
            "cache_has_pii": cache_doc.get("has_pii", False) if cache_doc else False,
            "cache_is_registered": cache_doc.get("is_registered", False) if cache_doc else False,
            "last_updated": cache_doc.get("last_updated") if cache_doc else None,
            "customer_count": len(cache_doc.get("customers", [])) if cache_doc else 0
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Error checking privacy status for store {store_id}: {e}", exc_info=True)
        return {"store_id": store_id, "error": str(e)}
    finally:
        if close_client and mongo_client:
            mongo_client.close()

def bulk_update_registered_stores():
    """
    Utility function to update all registered stores that currently have anonymized data.
    This can be used to retroactively update stores that registered after their initial processing.
    """
    logger.info("Starting bulk update of registered stores with anonymized data")
    
    mysql_conn = None
    mongo_client = None
    
    try:
        # Establish connections
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=20
        )
        
        mongo_client = get_mongodb_connection()
        if mongo_client is None:
            raise ConnectionError("MongoDB connection failed.")
        
        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        mongo_db_main = mongo_client[MONGO_DB_MAIN_NAME]
        
        # Get registered stores
        registered_stores = get_registered_stores(mongo_db_main)
        logger.info(f"Found {len(registered_stores)} registered stores")
        
        # Find stores that are registered but have anonymized data
        collection = mongo_db[STORE_CUSTOMERS_CACHE_COLLECTION]
        stores_to_update = collection.find({
            "_id": {"$in": list(registered_stores)},
            "has_pii": False
        })
        
        stores_to_update_list = list(stores_to_update)
        logger.info(f"Found {len(stores_to_update_list)} registered stores with anonymized data to update")
        
        updated_count = 0
        failed_count = 0
        
        for store_doc in stores_to_update_list:
            store_id = store_doc["_id"]
            try:
                success = process_store_in_batches(int(store_id), mysql_conn, mongo_db, is_registered=True)
                if success:
                    updated_count += 1
                    logger.info(f"Successfully updated store {store_id} with PII")
                else:
                    failed_count += 1
                    logger.error(f"Failed to update store {store_id}")
            except Exception as e:
                failed_count += 1
                logger.error(f"Error updating store {store_id}: {e}", exc_info=True)
        
        logger.info(f"Bulk update completed: {updated_count} updated, {failed_count} failed")
        return {"updated": updated_count, "failed": failed_count}
        
    except Exception as e:
        logger.error(f"Error in bulk update: {e}", exc_info=True)
        return {"error": str(e)}
    finally:
        if mysql_conn and mysql_conn.is_connected():
            mysql_conn.close()
        if mongo_client:
            mongo_client.close()

def test_anonymization():
    """
    Test function to verify anonymization is working correctly.
    Creates sample customer data and tests the anonymization function.
    """
    logger.info("Testing anonymization function")
    
    # Sample customer data
    sample_customer = {
        "store_id": "123",
        "customer_id": "456",
        "customer_email": "<EMAIL>",
        "customer_name": "John Doe",
        "customer_dni": "12345678",
        "total_orders": 5,
        "total_spend": 150.50
    }
    
    logger.info(f"Original data: {sample_customer}")
    
    # Test anonymization
    anonymized = anonymize_customer_data(sample_customer)
    
    logger.info(f"Anonymized data: {anonymized}")
    
    # Verify anonymization
    assert anonymized["customer_name"] == ANONYMOUS_NAME
    assert anonymized["customer_email"] == ANONYMOUS_EMAIL
    assert anonymized["customer_dni"] == ANONYMOUS_DNI
    assert anonymized["is_anonymized"] == True
    assert anonymized["store_id"] == sample_customer["store_id"]  # Should remain unchanged
    assert anonymized["total_orders"] == sample_customer["total_orders"]  # Should remain unchanged
    
    logger.info("Anonymization test passed!")
    return True

def test_trigger_function():
    """
    Test function to manually trigger customer data update for a specific store.
    Run this to test the trigger function manually.
    """
    # CHANGE THIS TO A TEST STORE ID
    test_store_id = "1018"  # Change this to your test store
    
    logger.info(f"Testing trigger function for store {test_store_id}")
    
    try:
        success = trigger_for_newly_registered_store(test_store_id)
        if success:
            logger.info(f"✅ Trigger test successful for store {test_store_id}")
        else:
            logger.error(f"❌ Trigger test failed for store {test_store_id}")
        return success
    except Exception as e:
        logger.error(f"❌ Trigger test error for store {test_store_id}: {e}")
        return False

# --- Execution Guard ---
if __name__ == "__main__":
    main()