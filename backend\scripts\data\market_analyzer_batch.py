#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch version of market analyzer that processes a limited number of stores.
This prevents timeouts by processing stores in smaller batches.
"""

import sys
import os

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import the original market analyzer
from market_analyzer import MarketAnalyzer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Run market analyzer with batching"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run market analyzer in batches')
    parser.add_argument('--batch-size', type=int, default=0, help='Number of stores to process (0 = all stores, default: 0)')
    parser.add_argument('--offset', type=int, default=0, help='Offset to start from (default: 0)')
    
    args = parser.parse_args()
    
    # Initialize the analyzer
    analyzer = MarketAnalyzer()
    
    if args.batch_size == 0:
        # Process all stores
        logger.info("Processing ALL stores (batch_size=0)")
        analyzer.analyze_stores(store_ids=None)  # None means process all stores
        logger.info("Analysis complete for all stores!")
    else:
        # Process specific batch
        logger.info(f"Starting market analyzer batch mode - Processing {args.batch_size} stores starting from offset {args.offset}")
        
        # Get store IDs
        store_ids = [doc['_id'] for doc in analyzer.analysis_db['active_stores_cache'].find({}, {'_id': 1}).skip(args.offset).limit(args.batch_size)]
        
        if not store_ids:
            logger.info("No stores to process at this offset")
            return
        
        logger.info(f"Processing stores: {store_ids}")
        
        # Process only the specified batch
        analyzer.analyze_stores(store_ids=store_ids)
        
        logger.info(f"Batch processing complete. Processed {len(store_ids)} stores.")
        logger.info(f"To process next batch, run with --offset {args.offset + args.batch_size}")

if __name__ == "__main__":
    main()