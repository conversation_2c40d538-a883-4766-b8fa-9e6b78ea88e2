{"greeting": "Hello", "dashboardTitle": "Dashboard", "loginButton": "<PERSON><PERSON>", "language": "Language", "english": "English", "spanish": "Spanish", "visualizadorDeTasas": "Rate Viewer", "welcomeBack": "Welcome Back", "signInPrompt": "Sign in to your D-Unit account", "registerNote": "(Register first with your La Nube Account)", "emailLabel": "Email Address", "passwordLabel": "Password", "forgotPasswordLink": "Forgot password?", "orDivider": "OR", "continueWithGoogle": "Continue with Google", "continueWithMeta": "Continue with Meta", "noAccountPrompt": "Don't have an account?", "signUpLink": "Sign up", "errorGoogleNoCredential": "No credential received from Google", "errorGoogleLoginFailedGeneral": "Failed to login with Google. Please try again.", "errorGoogleLoginFailedTryAgain": "Google login failed. Please try again or use email/password.", "errorEnterEmailPassword": "Please enter both email and password", "errorInvalidEmailPassword": "Invalid email or password", "errorLoginGeneral": "An error occurred during login. Please try again.", "errorFacebookNoToken": "Failed to get access token from Facebook", "errorFacebookLoginFailed": "Failed to login with Facebook. Please try again.", "connectingStatus": "Connecting...", "twoFAInfo": "Please enter the verification code sent to your email.", "twoFACodeLabel": "Verification Code", "twoFAButton": "Verify Code", "error2FANoCode": "Please enter the verification code", "error2FANoToken": "No access token received from 2FA verification", "error2FAFailed": "Failed to verify code. Please try again.", "termsAndConditions": "Terms & Conditions", "privacyPolicy": "Privacy Policy", "poweredBy": "Powered by", "lightMode": "Light Mode", "darkMode": "Dark Mode", "errorEnterEmail": "Please enter your email address", "errorInvalidEmail": "Please enter a valid email address", "successPasswordSent": "A new password has been sent to your email. Please check your inbox.", "errorEmailNotFoundLaNube": "Email not found. Please make sure you are using your La Nube email address.", "errorRegisterFirst": "You need to register for D-Unit first. Please use the <1>Sign Up</1> page or Continue with Google to create your D-Unit account.", "errorInvalidEmailFormat": "Invalid email format", "errorInvalidEmailFormatCheck": "Invalid email format. Please check your email and try again.", "errorForgotPasswordGeneral": "Failed to process request. Please try again later.", "forgotPasswordTitle": "Forgot Your Password?", "forgotPasswordSubtitle": "Enter your La Nube email address and we will send you a new password.", "continueButton": "Continue", "backToLoginLink": "Back to Login", "errorPasswordLength": "Password must be at least 8 characters long", "errorPasswordUppercase": "Password must contain at least one uppercase letter", "errorPasswordNumber": "Password must contain at least one number", "errorPasswordSymbol": "Password must contain at least one symbol", "errorPasswordsDontMatch": "Passwords do not match", "errorRegisterEmailNotFoundLaNube": "Email not found. You must be a La Nube user to register for D-Unit.", "errorRegisterAlreadyRegistered": "You are already registered for D-Unit. Please login instead.", "errorRegistrationFailed": "Registration failed", "errorVerificationFailed": "Verification failed", "successRegistration": "Registration successful! Redirecting...", "registerTitle": "D-Unit Registration", "registerSubtitle": "For existing La Nube users only", "goToLoginLink": "Go to Login", "registerLabelLaNubeEmail": "La Nube Email Address", "registerLabelName": "Your Name", "registerLabelStoreName": "Store Name", "registerPasswordRequirements": "Password requirements:", "registerReqLength": "Minimum 8 characters", "registerReqUppercase": "At least 1 uppercase letter", "registerReqNumber": "At least 1 number", "registerReqSymbol": "At least 1 symbol", "registerLabelPassword": "D-Unit Password", "registerLabelConfirmPassword": "Confirm D-Unit Password", "registerButton": "Register", "registerAlreadyHaveAccount": "Already have an account?", "signInLink": "Sign in", "registerVerifyTitle": "Verify Your Email", "registerVerifySubtitle": "Please enter the verification code sent to your email address.", "registerLabelVerificationCode": "Verification Code", "registerVerifyButton": "Verify Account", "adminLogin": {"title": "Admin Portal Login", "subtitle": "Sign in to access the Admin Portal.", "usernameLabel": "Username", "passwordLabel": "Password", "signInButton": "Sign In", "clearSession": "Having trouble? Clear session and reload", "togglePasswordVisibility": "toggle password visibility", "errorRequired": "Email and password are required.", "errorUnexpected": "An unexpected error occurred during login.", "errorNoAdminPrivileges": "Login successful, but you do not have admin privileges.", "errorInvalidCredentials": "Invalid email or password.", "errorLoginFailedFallback": "<PERSON><PERSON> failed. Please check your credentials."}, "adminPortal": {"title": "Admin Portal", "searchLabel": "Search Stores (ID or Name)", "storeSelectionTitle": "Store Selection", "infoNoStoresFound": "No stores found matching your criteria.", "unnamedStore": "Unnamed Store", "viewDashboardButton": "View Dashboard", "errorNoToken": "Authentication token not found. Please log in again.", "errorUnknownFetch": "An unknown error occurred while fetching stores", "errorAuthRequired": "Authentication required"}, "settings": {"pageTitle": "Settings", "errorNoStoreId": "No store ID available", "errorLoadProfileFailed": "Failed to load profile", "companyProfile": {"title": "Company Profile", "updateSuccess": "Profile updated successfully", "updateError": "Error updating profile. Please try again later.", "validationError": "Please fix validation errors before submitting", "companyNameLabel": "Company Name *", "companyNamePlaceholder": "Store Name Pending", "contactNameLabel": "Contact Name", "businessTypeLabel": "Business Type *", "businessTypePlaceholder": "Business Type Pending", "emailLabel": "Email", "taxIdLabel": "Tax ID", "phoneLabel": "Phone", "dniLabel": "DNI", "websiteLabel": "Website", "addressLabel": "Address", "updateButton": "Update Profile"}, "metaEmail": {"title": "Meta Dashboard Email Settings", "description": "Select which email address to use for Meta Dashboard login. This can be your primary email or one of your alternate emails.", "selectLabel": "<PERSON><PERSON>a <PERSON>", "primaryLabel": "(Primary)", "setButton": "Set as <PERSON><PERSON>", "updateSuccess": "Successfully updated Meta Dashboard login email.", "updateError": "Failed to update Meta Dashboard login email.", "currentSelectionPrefix": "Currently selected email for Meta Dashboard:", "primaryEmailUsage": "No alternate emails available. Your primary email ({email}) will be used for Meta Dashboard."}, "alternateEmail": {"addTitle": "Add Alternate Email Address", "addDescription": "Add additional email addresses that you use for your Meta accounts. These can be selected for Meta Dashboard login.", "newEmailInputLabel": "New Email Address", "addButton": "Add <PERSON>", "yourAlternateEmails": "Your Alternate Emails:", "selectedForMeta": "Selected for Meta", "noneAdded": "No alternate emails added yet.", "errorInvalidFormat": "Please enter a valid email address", "addSuccess": "Email added successfully", "addErrorFallback": "Failed to add email", "addErrorConflict": "This email address is already associated with the account.", "removeSuccess": "Email removed successfully", "removeError": "Failed to remove email"}, "appearance": {"title": "Appearance Settings", "darkModeLabel": "Dark Mode", "darkModeDescription": "Toggle between light and dark themes for the application."}, "language": {"title": "Language Settings", "description": "Choose your preferred language for the application."}, "validation": {"companyNameRequired": "Company name is required", "companyNameTooShort": "Company name must be at least 2 characters", "companyNameTooLong": "Company name must not exceed 100 characters", "companyNameInvalidChars": "Company name contains invalid characters", "contactNameRequired": "Contact name is required", "contactNameTooShort": "Contact name must be at least 2 characters", "contactNameTooLong": "Contact name must not exceed 50 characters", "contactNameInvalidChars": "Contact name can only contain letters, spaces, hyphens, and apostrophes", "businessTypeRequired": "Business type is required", "businessTypeTooShort": "Business type must be at least 2 characters", "businessTypeTooLong": "Business type must not exceed 50 characters", "businessTypeInvalidChars": "Business type can only contain letters, spaces, and hyphens", "emailRequired": "Email is required", "emailTooLong": "Email must not exceed 254 characters", "emailInvalid": "Please enter a valid email address", "taxIdRequired": "Tax ID is required", "taxIdTooShort": "Tax ID must be at least 8 characters", "taxIdTooLong": "Tax ID must not exceed 20 characters", "taxIdInvalidChars": "Tax ID can only contain letters, numbers, and hyphens", "phoneRequired": "Phone number is required", "phoneTooShort": "Phone number must be at least 10 characters", "phoneTooLong": "Phone number must not exceed 18 characters", "phoneInvalid": "Please enter a valid phone number", "dniRequired": "DNI is required", "dniTooShort": "DNI must be at least 7 characters", "dniTooLong": "DNI must not exceed 12 characters", "dniInvalidChars": "DNI can only contain letters and numbers", "websiteTooLong": "Website URL must not exceed 255 characters", "websiteInvalid": "Please enter a valid website URL (including http:// or https://)", "addressRequired": "Address is required", "addressTooShort": "Address must be at least 5 characters", "addressTooLong": "Address must not exceed 200 characters", "addressInvalidChars": "Address contains invalid characters", "codeRequired": "Verification code is required", "codeInvalid": "Verification code must be exactly 6 digits"}, "changePassword": {"title": "Change Your Password", "requirementsTitle": "Password requirements:", "reqLength": "Minimum 8 characters", "reqUppercase": "At least 1 uppercase letter", "reqNumber": "At least 1 number", "reqSymbol": "At least 1 symbol", "reqMatch": "Passwords match", "currentPasswordLabel": "Current Password *", "newPasswordLabel": "New Password *", "confirmPasswordLabel": "Confirm New Password *", "changeButton": "Change Password", "errorMismatch": "Passwords do not match", "errorSameAsOld": "New password cannot be the same as the old password", "errorRequirements": "Password must meet all requirements", "updateSuccess": "Password changed successfully", "errorIncorrectOld": "Incorrect old password", "updateErrorFallback": "Failed to change password. Please try again."}, "twoFactor": {"title": "Two-Factor Authentication", "enableSwitchLabel": "Enable Two-Factor Authentication", "description": "Enhance your account security by enabling two-factor authentication. You'll receive a verification code via email when signing in.", "updateSuccess": "Successfully updated 2FA status.", "enableSuccess": "Successfully enabled 2FA.", "disableSuccess": "Successfully disabled 2FA.", "updateError": "Failed to update 2FA status.", "errorDisableStatus": "Failed to disable 2FA. Status still shows enabled.", "infoCodeSent": "Verification code sent to your email. Please enter it below.", "verificationTitle": "Verify Two-Factor Authentication", "verificationInstructions": "Please enter the 6-digit code sent to your email address to complete setup.", "codeInputLabel": "Verification Code", "verifyButton": "Verify Code", "warnEnterCode": "Please enter the 6-digit code.", "verifySuccess": "Verification successful. 2FA is now enabled.", "verifyErrorInvalidOrExpired": "Verification failed. Invalid code or request expired.", "verifyErrorFallback": "Failed to verify code. Please try again.", "resendButtonIdle": "Resend Code", "resendButtonSending": "Sending...", "resendButtonSent": "Code Sent", "refreshButton": "Refresh Status"}, "adminPasswordStoreSpecificWarning": "Password settings are store-specific and cannot be managed here.", "admin2FAStoreSpecificWarning": "Two-Factor Authentication settings are store-specific and cannot be managed here.", "categoryEnabled": "{{category}} cookies enabled", "categoryDisabled": "{{category}} cookies disabled", "allAccepted": "All cookies accepted", "optionalRejected": "Optional cookies rejected", "legalInfo": "Legal Information", "legalText": "This website complies with GDPR, CCPA, LGPD, PIPEDA, and other applicable privacy regulations. You have the right to:", "right1": "Withdraw consent at any time", "right2": "Request deletion of your data", "right3": "Access your personal data", "right4": "Data portability", "supportedRegulations": "Supported Regulations", "noConsent": "No consent given yet", "details": "Details", "resetConsent": "Reset All <PERSON>", "save": "Save Preferences", "title": "<PERSON><PERSON>", "description": "Manage your cookie preferences and view detailed information about the cookies we use. Changes will take effect immediately.", "consentGiven": "Consent Given", "consentVersion": "Version", "consentId": "Consent ID", "reset": "Cookie consent has been reset", "version": "Configuration Version"}, "login": {"welcomeBack": "Welcome Back", "signInPrompt": "Sign in to your D-Unit account", "registerNote": "(Register first with your La Nube account)", "emailLabel": "Email Address *", "passwordLabel": "Password *", "forgotPasswordLink": "Forgot your password?", "loginButton": "Sign In", "orDivider": "or", "continueWithGoogle": "Continue with Google", "continueWithMeta": "Continue with Meta", "noAccountPrompt": "Don't have an account?", "signUpLink": "Register", "errorEnterEmailPassword": "Please enter both email and password.", "errorInvalidEmailPassword": "Invalid email or password.", "errorLoginGeneral": "An unexpected error occurred during login.", "errorGoogleLoginFailedGeneral": "Google login failed. Please try again later.", "errorGoogleLoginFailedTryAgain": "Google login failed. Please try again.", "errorFacebookLoginFailed": "Facebook login failed. Please try again.", "errorFacebookNoToken": "No access token received from Facebook.", "error2FANoCode": "Please enter the 2FA verification code.", "error2FANoToken": "2FA verification failed: No token received.", "error2FAFailed": "2FA verification failed. Please check the code and try again.", "enterVerificationCodePrompt": "Please enter the verification code sent to your email.", "verificationCodeLabel": "Verification Code", "verifyCodeButton": "Verify Code"}, "metaDashboard": {"title": "Meta Analytics Dashboard", "promptLogin": "Log in with your Meta account to access analytics for your business pages and Instagram accounts.", "errorReloginFailed": "Failed to re-authenticate with <PERSON><PERSON>. Please try refreshing the page and ensure cookies are enabled.", "errorAuthFailedGeneral": "Authentication failed. Please login again.", "errorAuthFailedRetry": "Authentication failed. Please login again.", "loginAgainButton": "Login Again", "disconnectButton": "Disconnect from Meta", "errorDisconnectFailed": "Failed to disconnect from Meta.", "loading": {"initializing": "Initializing data...", "title": "Loading Meta Dashboard", "description": "Checking authentication status and loading your Meta data...", "main": "Loading Meta Dashboard"}, "errorRateLimit": "API rate limit reached. Please try again in a minute.", "errorPartialFetch": "Some accounts could not be fetched, but displaying available data.", "errorNoBusinessAccounts": "No business accounts found. You may need to create a Meta Business account first. Please login again.", "errorNoPagesFound": "No pages found. Please connect a Facebook Page or Instagram Account to continue.", "errorFetchPagesFallback": "Error fetching pages. Please try refreshing.", "errorRefreshPermissions": "Error refreshing permissions.", "selectPageLabel": "Select Page", "noPagesAvailableOption": "No pages available", "refreshPagesButton": "Refresh Pages", "refreshPermissionsButton": "Refresh Permissions", "successPermissionsRefreshed": "Permissions refreshed successfully.", "warningMockData": "Using mock data due to connection issues.", "altEmailInfo": "Want to use a different email for <PERSON><PERSON> login? You can select your preferred email on the Settings page.", "pageHeader": "Page", "categoryHeader": "Category", "idHeader": "ID", "noAccounts": {"title": "No Meta Business Account Connected", "description": "To get started, please connect your Meta Business account. This allows D-Unit to access your pages and ad accounts.", "connectButton": "Connect Business Account", "goToSettingsButton": "Go to Meta Business Settings"}, "connect": {"title": "Connect Meta Business Account", "noBusinessAccounts": "No business accounts found. You may need to create a Meta Business account first. Please login again.", "continueWithMeta": "Continue with Meta", "info": "This will allow D-Unit to access your Facebook Page and Instagram business accounts for insights and analytics. We will ask for specific permissions in the next step.", "createBusinessAccount": "Create Business Account", "refreshAccounts": "Refresh Accounts"}, "tabs": {"overview": "Overview", "posts": "Posts", "audience": "Audience", "adMetricsTab": "Ad Metrics", "campaigns": "Campaigns", "permissions": "Permissions", "aiInsights": "AI Insights"}, "overview": {"pageTitle": "Page Overview", "instagramTitle": "Instagram Overview", "pageSubtitle": "Key metrics for your Facebook Page over the last 28 days", "instagramSubtitle": "Key metrics for your Instagram profile (period-based and current data)", "impressionsTitle": "Page Impressions", "impressionsDescFB": "Total number of times your Page content was seen", "engagementTitle": "Page Engagement", "engagementDescFB": "Total number of actions people took on your Page (clicks, likes, comments, shares)", "pageLikesTitle": "Page Followers", "pageLikesDesc": "Total number of people who like your Page", "pageViewsTitle": "Page Views", "pageViewsDescFB": "Total number of times your Page was viewed", "impressionsTitleIG": "Impressions", "impressionsDesc": "Total number of times your Instagram content was viewed", "reachTitle": "Reach", "reachDesc": "Total unique accounts that saw your Instagram content", "reachDescFB": "Total unique people who saw your Facebook content", "engagementDescIG": "Total interactions with your Instagram content (likes, comments, saves)", "followersTitle": "Followers", "followersDesc": "Total followers of your Instagram profile", "profileViewsTitle": "Profile Views", "profileViewsDesc": "Total number of times your Instagram profile was viewed", "savedTitle": "Saved Posts", "savedDesc": "Total number of times your Instagram posts were saved by users", "mediaCountTitle": "Published Posts", "mediaCountDesc": "Total number of posts published in the selected time period", "lowDataWarning": "Metrics may be zero if the Page has fewer than 100 likes, or if there's insufficient data for the selected period. Please check your Page's audience size and try a different time range if applicable.", "reconnectPrompt": "Please reconnect your Facebook account to view metrics"}, "posts": {"analyticsTitle": "Instagram Posts Analytics", "totalPosts": "Total Posts", "avgEngagement": "Avg. Engagement", "engagementDistribution": "Engagement Distribution", "postTypeDistribution": "Post Type Distribution", "likesLabel": "Likes:", "commentsLabel": "Comments:", "savedLabel": "Saved:", "imageLabel": "Image:", "topPerformingTitle": "Top Performing Posts", "tableHeaderPost": "Post", "tableHeaderDate": "Date", "tableHeaderType": "Type", "tableHeaderLikes": "<PERSON>s", "tableHeaderComments": "Comments", "tableHeaderSaved": "Saved", "tableHeaderTotal": "Total", "tableHeaderLink": "Link", "imageType": "Image", "legendFemale": "Female"}, "audience": {"demographicsTitle": "Audience Demographics", "totalAudience": "Total Audience", "ageGenderTab": "Age & Gender", "countriesTab": "Countries", "citiesTab": "Cities", "interestsTab": "Interests", "ageDistributionTitle": "Age Distribution", "genderDistributionTitle": "Gender Distribution", "legendMale": "Male", "legendFemale": "Female", "errorMetricsUnavailable": "One or more metrics are no longer available. Please contact support.", "period": "Period", "page": "Page", "noAgeData": "No age data available", "noGenderData": "No gender data available", "tabAgeGender": "Age & Gender", "tabCountries": "Countries", "tabCities": "Cities", "tabInterests": "Interests", "noDemographicsData": "No audience demographics data available for this page.", "insufficientFollowers": "Audience data is not available. This typically requires a minimum number of followers (e.g., 100 for Instagram).", "permissionDenied": "Permission denied to access audience demographics.", "authFailed": "Authentication failed. Please re-login to Met<PERSON>.", "rateLimit": "API rate limit reached while fetching audience data.", "failedToLoad": "Failed to load audience demographics data.", "demographicsTabs": "Audience demographics tabs", "followers": "Followers", "instagramDemographicsNote": "Instagram audience demographics require a business account with 100+ followers and specific permissions. This feature may not be available for all accounts.", "basicDataOnly": "Showing basic follower count. Detailed demographics require additional Instagram permissions."}, "caption": {"actualLikesComments": "Actual likes and comments from recent posts", "currentFollowerCount": "Current follower count; historical trend estimated", "derivedFromReachData": "Derived from reach data and follower activity"}, "campaigns": {"title": "Campaigns Comparison", "errorLoading": "Failed to load campaign data: {{errorDetails}}", "errorNotFound": "Failed to load campaign data: Not Found", "labelCampaignType": "Campaign Type", "placeholderCampaignType": "Select Campaign Type", "labelSelectCampaign": "Select Campaign", "placeholderSelectCampaign": "Select a Campaign", "labelStatus": "Status", "labelObjective": "Objective", "labelBudget": "Budget", "labelBudgetPerDay": "{{amount}}/day", "labelConversionSalesCorrelation": "Conversion and Sales Correlation", "labelCorrelationCoefficient": "Correlation Coefficient", "labelImpactOnSales": "Impact on Sales", "labelRelationship": "Relationship", "chartTitleConversionVsSales": "Conversion Rate vs. Sales Over Time", "legendConversionRate": "Conversion Rate", "legendSales": "Sales", "tableTitleComparisonData": "Campaign Comparison Data", "tableHeaderCampaign": "Campaign", "tableHeaderConversionRate": "Conversion Rate", "tableHeaderLaNubeSales": "La Nube Sales", "tableHeaderImpact": "Impact", "statusActive": "ACTIVE", "statusCompleted": "COMPLETED", "statusPaused": "PAUSED", "statusArchived": "ARCHIVED", "statusUnknown": "UNKNOWN", "objectiveConversions": "CONVERSIONS", "objectiveMessages": "MESSAGES", "objectiveLinkClicks": "LINK CLICKS", "objectiveBrandAwareness": "BRAND AWARENESS", "objectiveReach": "REACH", "objectiveLeadGeneration": "LEAD GENERATION", "objectiveUnknown": "UNKNOWN", "relationshipWeakNegative": "Weak Negative", "relationshipStrongNegative": "Strong Negative", "relationshipWeakPositive": "<PERSON>ak <PERSON>", "relationshipStrongPositive": "Strong Positive", "relationshipNeutral": "Neutral", "mockSummerCampaign": "<PERSON><PERSON> Campaign", "mockSpringSale": "Mock Spring Sale", "loadingCampaigns": "Loading campaigns...", "loadingComparisonData": "Loading comparison data...", "noCampaignData": "No campaign data available for the selected criteria.", "noSalesData": "No sales data available for the selected period.", "defaultCampaignName": "Campaign {{id}}", "tooltipConversionRate": "Conversion Rate", "tooltipSales": "Sales"}, "common": {"timeRange": "Time Range", "last7days": "Last 7 days", "last28days": "Last 28 days", "last30days": "Last 30 days", "last90days": "Last 90 days", "last180days": "Last 180 days", "lastYear": "Last year", "lifetime": "Lifetime", "customRange": "Custom Range", "from": "From", "to": "To", "apply": "Apply", "instagramAPILimit": "Instagram API limits queries to a maximum of 30 days", "instagramAPIRequirement": "* Instagram API requires date ranges to be 30 days or less", "adjustTo30days": "Adjust to 30 days", "total": "Total", "average": "Average", "change": "Change", "netChange": "Net Change", "period": "Period", "page": "Page", "overviewChartTitle": "Overview", "pageImpressionsChartTitle": "Page Impressions", "pageImpressionsChartDesc": "Total number of times your page content was viewed", "pageEngagementChartTitle": "Page Engagement", "pageEngagementChartDescFB": "Total number of actions taken on your page (clicks, likes, comments, shares)", "pageFollowersChartTitle": "Page Followers", "pageFollowersChartDesc": "Total number of people who like your page", "pageViewsChartTitle": "Page Views", "pageViewsChartDesc": "Total number of times your page was viewed", "postsAnalyticsChartTitle": "Posts Analytics", "audienceDemographicsChartTitle": "Audience Demographics", "adMetricsChartTitle": "Ad Metrics", "campaignsComparisonChartTitle": "Campaigns Comparison", "failedToFetchInsights": "Failed to fetch insights data", "actualLikesCommentsFootnote": "* Actual likes and comments from recent posts", "currentFollowerFootnote": "* Current follower count; historical trend estimated", "derivedReachFootnote": "* Derived from reach and follower activity data", "instagramReachChartTitle": "Instagram Reach", "pageImpressionsChartDescIG": "Total number of unique accounts that have seen your Instagram content", "instagramEngagementChartTitle": "Instagram Engagement", "instagramEngagementChartDesc": "Total number of interactions with your Instagram content (likes, comments, saves)", "instagramFollowersChartTitle": "Instagram Followers", "instagramFollowersChartDesc": "Total number of accounts following your Instagram profile", "instagramProfileViewsChartTitle": "Instagram Profile Views", "instagramProfileViewsChartDesc": "Total number of times your Instagram profile was viewed"}, "permissions": {"title": "Meta Permissions Check", "alertTitle": "Missing <PERSON><PERSON> Permissions", "alertDescription": "To perform this action, D-Unit requires the following permissions from Meta:", "alertGuidance": "Please grant these permissions in your Meta Business settings or reconnect your account.", "goToSettingsButton": "Go to Meta Settings"}, "adMetrics": {"title": "Ad Metrics", "dataFromCache": "Data from cache: {{date}}", "accountSummary": "Account Summary", "totalSpend": "Total Spend", "impressions": "Impressions", "clicks": "<PERSON>licks", "conversions": "Conversions", "ctr": "CTR", "cpc": "CPC", "costPerConversion": "Cost per Conversion", "roas": "ROAS", "dateRange": "Date Range", "showingMetrics": "Showing metrics for selected date range", "campaignsTab": "Campaigns", "performanceTab": "Performance", "roiAnalysisTab": "ROI Analysis", "campaign": "Campaign", "status": "Status", "spend": "Spend", "noCampaignData": "No campaign data available", "noPerformanceData": "No daily performance data available", "noRoiData": "No ROI analysis data available", "panelTitle": "Ad Metrics", "includeOrganicData": "Include Organic Data", "refreshNow": "Refresh Now", "errorPageMissing": "Page information is missing.", "errorStoreIdMissing": "Store ID is missing.", "errorNoDataAfterRetries": "No metrics data available after retries.", "errorAdAccountAccessGeneric": "Could not access ad account. Please check your setup.", "errorAdAccountAccessNoAccount": "No ad account is linked to this page/user.", "errorAdAccountAccessPermissions": "Ad account permissions are missing or insufficient.", "errorAdAccountAccessOther": "Ad Account Error: {{message}}", "errorFetchFailed": "Failed to fetch ad metrics. Please try again later.", "currencySymbolFallback": "$", "dateRangeLifetime": "Lifetime", "dateRangeToday": "Today", "dateRangeYesterday": "Yesterday", "dateRangeLastXDays": "Last {{count}} Days", "dateRangeLastXWeeks": "Last {{count}} Weeks", "dateRangeLastXMonths": "Last {{count}} Months", "dateRangeLastXYear": "Last Year", "dateRangeCustomPrefix": "Custom Range: ", "tableHeaderCampaign": "Campaign", "tableHeaderStatus": "Status", "tableHeaderSpend": "Spend", "tableHeaderImpressions": "Impressions", "tableHeaderClicks": "<PERSON>licks", "tableHeaderConversions": "Conversions", "tableHeaderCtr": "CTR", "tableHeaderCpc": "CPC", "tableHeaderCostPerConv": "Cost / Conv.", "tableHeaderRoas": "ROAS"}, "error": {"apiRateLimit": "API rate limit reached. Please try again in a minute.", "refreshPermissionsFailed": "Failed to refresh permissions. Please try again.", "tokenExpired": "Your Meta access token has expired. Please log in again to continue.", "genericSdkMessage": "An SDK error occurred: {{message}}", "loginResponse": "Invalid response from Meta SDK login", "checkStatus": "Failed to check Meta login status.", "initializationFailed": "Failed to initialize after login.", "selectValidRange": "Please select a valid time range."}, "connectToMetaTitle": "Connect to Meta", "alert": {"missingTokenTitle": "Missing Page Access Token", "missingTokenBody": "This page doesn't have a valid access token, which is required to fetch metrics data. Try disconnecting and reconnecting with Meta, making sure to grant all requested permissions. If you're using an Instagram account, make sure it's linked to a Facebook page you manage.", "sessionExpiredTitle": "Your Meta session has expired or is invalid", "sessionExpiredBody": "This usually happens when your access token expires (typically after 60 days) or permissions have been revoked. Click the button to log in again and ensure you grant all requested permissions."}, "selectPagePrompt": {"title": "Select a Page", "body": "Please select a Facebook page or Instagram account from the dropdown above to view analytics."}, "info": {"altEmailLogin": "Using a different email for Met<PERSON> login? You can add it in your <0>Settings</0> page."}}, "dashboard": {"title": "Store Dashboard", "welcomeMessage": "Welcome, {{name}}! Here's an overview of your store.", "defaultUserName": "Store Manager", "errorFetchData": "Failed to fetch dashboard data.", "errorSubmitFeedback": "Failed to submit feedback.", "tabs": {"overview": "Overview", "products": "Products", "customers": "Customers", "competition": "Competition", "shipping": "Shipping", "seo": "SEO", "feedback": "<PERSON><PERSON><PERSON>"}, "overview": {"summaryTitle": "AI Summary", "recommendationsTitle": "AI Recommendations", "noSummary": "No summary available.", "noRecommendations": "No recommendations available."}, "products": {"analysisTitle": "Product Performance Analysis", "totalProducts": "Total Products", "noAnalysis": "No product analysis available."}, "customers": {"insightsTitle": "Customer Insights", "totalCustomers": "Total Customers", "noAnalysis": "No customer analysis available."}, "competition": {"marketPositionTitle": "Market Position Analysis", "competitorAnalysisTitle": "Competitor Details", "noAnalysis": "No competition analysis available."}, "shipping": {"title": "Shipping Analysis", "noAnalysis": "No shipping analysis available.", "recommendationsTitle": "Recommendations:", "shippingAnalysis": {"centralLabel": {"methodText": "Shipping Method"}, "tooltipUsage": "Usage", "ordersSuffix": "orders"}}, "seo": {"title": "SEO Recommendations"}, "feedback": {"title": "Your Feedback", "placeholder": "Share your thoughts on the dashboard...", "submit": "Submit <PERSON>", "submitting": "Submitting...", "success": "Thank you for your feedback!", "previousFeedback": "Previous Feedback", "noFeedback": "No previous feedback found.", "errorFetchFeedback": "Failed to fetch feedback."}, "metrics": {"revenue": "Revenue", "orders": "Orders", "visits": "Visits", "avgOrderValue": "Avg. Order Value", "totalRevenue": "Total Revenue", "grossRevenue": "Gross Revenue", "totalNetRevenue": "Total Net Revenue", "totalRevenueUSD": "Total Revenue (USD)", "totalCustomers": "Total Customers", "totalProducts": "Total Products", "totalVisits": "Total Visits", "activeCustomers": "Active Customers", "abandonedCarts": "Abandoned Carts", "metaFollowers": "Meta Followers", "metaAdSpend30d": "<PERSON><PERSON> Spend (30d)"}, "titles": {"keyDates": "Key Dates", "socialMedia": "Social Media Profiles", "keywords": "Keywords", "detailedMetrics": "Detailed Metrics Summary"}, "common": {"timeRange": "Time Range", "last7days": "Last 7 days", "last30days": "Last 30 days", "last90days": "Last 90 days", "last180days": "Last 180 days", "lastYear": "Last year", "customRange": "Custom Range", "from": "From", "to": "To", "apply": "Apply", "instagramAPILimit": "Instagram API limits queries to 30 days maximum", "instagramAPIRequirement": "* Instagram API requires date ranges to be 30 days or less", "adjustTo30days": "Adjust to 30 days", "total": "Total", "average": "Average", "change": "Change", "netChange": "Net Change", "period": "Period", "page": "Page", "pageImpressionsChartTitle": "Page Impressions", "pageImpressionsChartDesc": "Total number of times your page content was viewed", "pageEngagementChartTitle": "Page Engagement", "pageEngagementChartDescFB": "Total number of actions taken on your page (clicks, likes, comments, shares)", "pageFollowersChartTitle": "Page Followers", "pageFollowersChartDesc": "Total number of people who like your page", "pageViewsChartTitle": "Page Views", "pageViewsChartDesc": "Total number of times your page was viewed", "failedToFetchInsights": "Failed to fetch insights data", "actualLikesCommentsFootnote": "* Actual likes and comments from recent posts", "currentFollowerFootnote": "* Current follower count; historical trend estimated", "derivedReachFootnote": "* Derived from reach and follower activity data", "pageImpressionsChartDescIG": "Total number of unique accounts that have seen your content", "instagramReachChartTitle": "Reach", "instagramEngagementChartTitle": "Engagement", "instagramEngagementChartDesc": "Total number of interactions with your content (likes, comments, saves)", "instagramFollowersChartTitle": "Followers", "instagramFollowersChartDesc": "Total number of accounts following your profile", "instagramProfileViewsChartTitle": "Profile Views", "instagramProfileViewsChartDesc": "Total number of times your profile was viewed", "loading": "Loading...", "noDataAvailable": "No data available.", "notAvailableShort": "N/A", "refreshing": "Refreshing...", "numberSuffix": {"million": "M", "thousand": "K"}}}, "chat": {"pageTitle": "Cha<PERSON>", "newChatDefaultTitle": "New Chat", "newChatTooltip": "New Chat", "optionsTooltip": "Chat Options", "openSidebarTooltip": "Open Sidebar", "closeSidebarTooltip": "Close Sidebar", "inputPlaceholder": "Type your message here...", "sendTooltip": "Send message", "feedbackButton": "Provide <PERSON>", "dateGroup": {"invalid": "Invalid Date", "today": "Today", "yesterday": "Yesterday", "previous7Days": "Previous 7 Days", "previous30Days": "Previous 30 Days", "older": "Older"}, "loadingSteps": {"thinking": "Thinking...", "analyzingContext": "Analyzing context...", "generating": "Generating response...", "processingQuery": "Processing query...", "searchingBrave": "Searching web (Brave)...", "analyzingResults": "Analyzing search results...", "searchingGoogle": "Searching web (Google)...", "processing": "Processing..."}, "errorLog": {"fetchHistory": "Error fetching chat history:", "sendMessage": "Error sending message:"}, "error": {"fetchHistory": "Failed to fetch chat history. Please refresh the page.", "fetchMessages": "Failed to fetch messages for this chat.", "sendMessage": "Failed to send message. Please check your connection and try again.", "renameChat": "Failed to rename chat. Please try again.", "deleteChat": "Failed to delete chat. Please try again.", "unexpected": "An unexpected error occurred. Please try again."}, "menu": {"rename": "<PERSON><PERSON>", "delete": "Delete"}, "rename": {"dialogTitle": "<PERSON><PERSON>", "inputLabel": "Enter new title", "confirmButton": "<PERSON><PERSON>", "errorEnterTitle": "Please enter a new title.", "errorCannotRenamePlaceholder": "Cannot rename an empty 'New Chat'. Send a message first."}, "delete": {"dialogTitle": "Delete Chat", "confirmationText": "Are you sure you want to delete this chat?", "confirmButton": "Delete"}, "export": {"exportToPDF": "Export chat to PDF", "exportedOn": "Exported on", "chatCreated": "Chat created on"}, "dialog": {"cancelButton": "Cancel"}, "metaPermissions": {"alertTitle": "Missing <PERSON><PERSON> Permissions", "alertDescription": "To perform this action, D-Unit requires the following permissions from Meta:", "alertGuidance": "Please grant these permissions in your Meta Business settings or reconnect your account.", "goToSettingsButton": "Go to Meta Settings"}, "initialAssistantMessage": "What do you want to know? Ask me anything", "newChatTitle": "New Chat", "startNewChatButton": "Start New Chat", "roleAgent": "Agent", "attachImageTooltip": "Attach Image", "modeButton": {"think": "Think", "deepSearch": "DeepSearch", "deeperSearch": "DeeperSearch"}, "agent": {"greeting": "Hello! How can I help you today with the Bandida store?"}, "input": {"placeholder": "Write your message here..."}, "actions": {"think": "Think", "thinkTooltip": "Get thoughtful and detailed responses for questions that require researching.", "deepSearch": "Deep Search", "deepSearchTooltip": "Explore the web thoroughly for detailed competitor insights, recent market news, or other market insights of the industry you score.", "deeperSearch": "Deeper Search", "deeperSearchTooltip": "Quickly search the web for recent market news or competitor updates relevant to your store."}, "emptyStates": {"storeInfoNotAvailable": "Store information not available", "noChatHistory": "No chat history yet", "startNewChat": "Start a new chat to begin"}, "loadingConsole": {"notReady": "Loading Chat Console..."}, "contextDocuments": {"relatedInformation": "Related Information:", "productInfo": "Product Information", "customerInfo": "Customer Information", "storeInfo": "Store Information", "topProducts": "Top Products", "customerBase": "Customer Base", "noCustomerData": "No detailed customer data available", "locatedIn": "Located in", "unknownLocation": "Unknown location", "unknownType": "Unknown type", "connectedSocial": "Connected social:"}, "processing": "Processing...", "untitledChat": "Untitled Chat", "customerData": {"orders": "orders", "spent": "spent", "avgCustomerSpend": "Avg. Customer Spend:", "inStock": "in stock", "outOfStock": "Out of stock"}, "similarity": "Similarity", "loadingIndicator": {"mode": "Mode:", "time": "Time:", "defaultProcessing": "Processing..."}, "creditsRemaining": "Credits Remaining", "creditsTooltip": "Token Cost per Message:\n• Normal chat: 10,000 tokens\n• Think mode: 20,000 tokens\n• Deepsearch mode: 40,000 tokens\n• DeeperSearch mode: 40,000 tokens", "hideInput": "Hide input", "showInput": "Show input"}, "navigation": {"chat": "Cha<PERSON>", "dashboard": "Dashboard", "meta": "Meta", "socialMedia": "Social Media", "settings": "Settings", "logout": "Logout"}, "common": {"deleteAlt": "delete", "platform": "Platform", "facebook": "Facebook", "instagram": "Instagram", "metaLogo": "Meta <PERSON>", "pages": "pages", "accounts": "accounts", "pageInsights": "page insights, posts, and audience data", "instagramInsights": "access to Instagram and insights", "timeRange": "Time Range", "last7days": "Last 7 days", "last28days": "Last 28 days", "last30days": "Last 30 days", "last90days": "Last 90 days", "last180days": "Last 180 days", "lastYear": "Last year", "lifetime": "Lifetime", "customRange": "Custom Range", "from": "From", "to": "To", "apply": "Apply", "retry": "Retry", "instagramAPILimit": "Instagram API limits queries to 30 days maximum", "instagramAPIRequirement": "* Instagram API requires date ranges to be 30 days or less", "adjustTo30days": "Adjust to 30 days", "total": "Total", "average": "Average", "change": "Change", "netChange": "Net Change", "period": "Period", "page": "Page", "pageImpressionsChartTitle": "Page Impressions", "pageImpressionsChartDesc": "Total number of times your page content was viewed", "pageEngagementChartTitle": "Page Engagement", "pageEngagementChartDescFB": "Total number of actions taken on your page (clicks, likes, comments, shares)", "pageFollowersChartTitle": "Page Followers", "pageFollowersChartDesc": "Total number of people who like your page", "pageViewsChartTitle": "Page Views", "pageViewsChartDesc": "Total number of times your page was viewed", "failedToFetchInsights": "Failed to fetch insights data", "actualLikesCommentsFootnote": "* Actual likes and comments from recent posts", "currentFollowerFootnote": "* Current follower count; historical trend estimated", "derivedReachFootnote": "* Derived from reach and follower activity data", "pageImpressionsChartDescIG": "Total number of unique accounts that have seen your content", "loading": "Loading...", "noDataAvailable": "No data available.", "notAvailableShort": "N/A", "refreshing": "Refreshing...", "numberSuffix": {"million": "M", "thousand": "K"}}, "meta": {"overview": {"title": "Overview", "page_info": "Page: {{name}} ({{platform}})", "using_cached_data": "Using cached data", "cached_age": "Data cached {{minutes}} minutes ago"}, "metrics": {"impressions": "Impressions", "engagement": "Engagement", "followers": "Followers", "views": "Views"}, "errors": {"insufficient_permissions": "Insufficient permissions", "page_undefined": "Page not defined", "metrics_fetch_failed": "Failed to fetch metrics", "token_expired": "Access token expired", "network_error": "Network error", "permission_denied": "Permission denied", "rate_limit_exceeded": "Rate limit exceeded", "invalid_platform_for_insights": "Invalid platform for insights", "insights_fetch_failed": "Failed to fetch insights", "authentication_failed": "Authentication failed", "server_error": "Server error", "no_metrics_available": "No metrics available"}, "insights": {"no_data_available": "No data available", "total_value": "Total", "current_period": "Current Period", "previous_period": "Previous Period", "using_cached_data": "Using cached data", "cached_age": "Data cached {{minutes}} minutes ago"}, "restricted": {"unavailableTitle": "{{title}} Unavailable", "igInsightsRevokedTitle": "Instagram Insights Permission Revoked", "igInsightsRevokedBody": "You've revoked access to Instagram Insights data. This widget requires both <0>instagram_basic</0> and <1>instagram_manage_insights</1> permissions.", "igInsightsCurrentStatus": "Current status: <0>instagram_basic</0> (Granted), <1>instagram_manage_insights</1> (Revoked)", "igBasicRevokedTitle": "Instagram Basic Permission Revoked", "igBasicRevokedBody": "You've revoked basic access to your Instagram account. This widget requires the <0>instagram_basic</0> permission.", "igBasicCurrentStatus": "Current status: <0>instagram_basic</0> (Revoked)", "igMultipleRevokedTitle": "Multiple Instagram Permissions Revoked", "igMultipleRevokedBody": "You've revoked both Instagram Basic and Instagram Insights permissions. This widget requires <0>instagram_basic</0> and <1>instagram_manage_insights</1> permissions.", "igMultipleCurrentStatus": "Current status: <0>instagram_basic</0> (Revoked), <1>instagram_manage_insights</1> (Revoked)", "igGenericRevokedTitle": "Instagram Permissions Revoked", "igGenericRevokedBody": "You've revoked required Instagram permissions for this feature.", "requiredPermissionsList": "Required permissions: {{permissions}}", "singlePermissionRevoked": "You've revoked the <0>{{permissionName}}</0> permission.", "multiplePermissionsRevoked": "You've revoked required permissions for this feature.", "requiresFollowingPermissions_one": "This widget requires the following permission:", "requiresFollowingPermissions_other": "This widget requires the following permissions:", "permissionStatus": "{{permissionName}}{{status}}", "grantedStatus": " (Granted)", "revokedStatus": " (Revoked)", "reconnectButton": "Reconnect to Meta", "manageButton": "Manage Permissions", "tooltipReconnect": "You'll need to reconnect to Meta to grant these permissions", "tooltipManage": "Visit the Permissions Management tab to learn more"}, "posts": {"facebookTitle": "Facebook Posts Analytics", "permissionError": "Permission error: Please refresh your Meta permissions and try again.", "noPostsInRange": "No posts found in the selected date range ({{startDate}} - {{endDate}}). Try adjusting the time period or check that the account has published content during this period.", "errorLoad": "Failed to load {{platform}} posts data. Please try again later.", "rateLimitError": "Facebook API rate limit reached. Please wait and try again later.", "tableHeaderPost": "Post", "tableHeaderDate": "Date", "tableHeaderType": "Type", "tableHeaderLikes": "<PERSON>s", "tableHeaderComments": "Comments", "tableHeaderShares": "Shares", "tableHeaderSaved": "Saved", "tableHeaderTotalEngagement": "Total Engagement", "tableHeaderLink": "Link", "engagementLabelLikes": "<PERSON>s", "engagementLabelComments": "Comments", "engagementLabelShares": "Shares", "engagementLabelSaved": "Saved", "postTypeLabelPhoto": "Photo", "postTypeLabelVideo": "Video", "postTypeLabelStatus": "Status", "postTypeLabelLink": "Link", "postTypeLabelAlbum": "Album", "postTypeLabelEvent": "Event", "postTypeLabelNote": "Note", "postTypeLabelOffer": "Offer", "postTypeLabelGeneric": "Other", "type": {"photo": "Photo", "video": "Video", "status": "Status", "link": "Link", "album": "Album", "event": "Event", "note": "Note", "offer": "Offer", "image": "Image", "unknown": "Unknown"}}}, "metaLogin": {"error": {"httpsRequired": "Meta Login requires HTTPS. Please use HTTPS to connect with Meta.", "sdkFailed": "Facebook SDK failed to load. Please try refreshing the page.", "authFailed": "Authentication failed. Please make sure you are logged in to Meta and try again.", "permissionDenied": "Permission denied. Please grant the required permissions to use this feature.", "securityError": "Meta login requires HTTPS. Please use a secure connection.", "networkError": "Network error occurred. Please check your connection and try again.", "sdkNotLoaded": "Meta SDK failed to load. Please ensure cookies are enabled and try again.", "unexpected": "An unexpected error occurred. Please try again.", "missingPermissions": "Missing critical permissions: {{permissions}}. Dashboard functionality will be limited."}, "warning": {"optionalPermissions": "Some optional permissions were not granted: {{permissions}}. Some features may be limited.", "noPagesFound": "Connected to {{platform}}, but no {{accountType}} were found."}, "success": {"connectedPlatform": "Connected to {{platform}} successfully!"}, "connecting": "Connecting...", "continueWithMeta": "Continue with Meta", "continueWithInstagram": "Continue with Instagram", "info": {"allowAccess": "This will allow D-Unit to access your Facebook Page and Instagram Business accounts to retrieve insights and analytics data. We'll ask for specific permissions in the next step."}, "choosePlatform": "Choose a platform to connect with:", "requiredPermissions": "Required permissions:", "permissionsInfo": "When prompted, please grant all requested permissions for {{platformDetails}}.", "permissionsWarning": "Without these permissions, the dashboard may not function properly."}, "customerAnalysisCard": {"title": "Customer Analysis", "notAvailable": "No customer analysis available."}, "customerDemographics": {"title": "Customer Demographics", "notAvailable": "No customer demographic data available.", "countryDistributionTitle": "Distribution by Country"}, "customerAggregatedMetricsCard": {"title": "Aggregated Customer Metrics", "notAvailable": "No aggregated metrics available.", "totalCustomers": "Total Customers", "totalStoreOrders": "Total Store Orders", "avgSpend": "Avg. Spend/Customer", "abandonedCarts": "Abandoned Carts", "abandonedCartCustomers": "Customers (Abandoned)", "abandonedValue": "Abandoned Value", "pendingCarts": "Pending Carts", "pendingValue": "Pending Value", "mostFrequentPayment": "Most Frequent Payment", "mostFrequentShipping": "Most Frequent Shipping", "mostFrequentCoupon": "Most Frequent Coupon", "countryDistribution": "Country Distribution", "couponDistribution": "Coupon Distribution", "paymentDistribution": "Payment Method Distribution", "shippingDistribution": "Shipping Method Distribution", "statusDistribution": "Status Distribution"}, "customerList": {"title": "Customers", "searchPlaceholder": "Search by name or email...", "header": {"name": "Name", "email": "Email", "totalOrders": "Total Orders", "totalSpend": "Total Spend", "lastOrder": "Last Order Date"}, "noResults": "No customers found"}, "customerDetailView": {"title": "Customer Details", "placeholder": "Select a customer from the list to view details.", "name": "Name", "email": "Email", "totalSpend": "Total Spend", "firstOrderDate": "First Order", "lastOrderDate": "Last Order", "preferredPayment": "Pref. Payment", "preferredShipping": "Pref. Shipping", "country": "Country", "uniqueProducts": "Unique Products", "paymentMethods": "Payment Methods Used", "shippingMethods": "Shipping Methods Used", "ordersTitle": "Orders", "orderId": "Order", "paymentMethod": "Payment", "shippingMethod": "Shipping", "coupon": "Coupon", "productHeader": "Product", "qtyHeader": "Qty", "priceHeader": "Price", "noProducts": "No product details available for this order.", "noOrders": "No orders found for this customer."}, "shippingAnalysis": {"centralLabel": {"methodText": "Shipping Method"}, "tooltipUsage": "Usage", "ordersSuffix": "orders", "recommendationsTitle": "Recommendations:"}, "topProductsCard": {"cardTitle": "Top Products", "noData": "No product data available for the selected period", "productName": "Product Name", "productSales": "Sales", "productUnits": "Units", "revenue": "Revenue", "orders": "Orders"}, "adminDashboard": {"title": "Admin Dashboard", "metrics": {"totalStores": "Total Stores", "activeStores": "Active Stores", "newStores": "New Stores"}}, "metaPermissions": {"title": "Meta Permissions Management", "description": "Manage the permissions you've granted to this application. Revoking permissions may limit functionality.", "refreshButton": "Refresh Permissions", "refreshingButton": "Refreshing...", "searchPlaceholder": "Search permissions...", "tabs": {"all": "All Permissions", "page": "Page", "business": "Business", "content": "Content", "instagram": "Instagram", "advertising": "Advertising", "other": "Other"}, "status": {"optional": "Optional permission", "required": "Required permission", "revoked": "This permission has been revoked", "whyRequiredTooltip": "This permission is required for core functionality and cannot be revoked here. Click for more info.", "whyButton": "Why?"}, "warnings": {"genericImpact": "{{impactText}}"}, "revokeDialog": {"title": "Revoke Permission?", "confirmText": "Are you sure you want to revoke the permission: {{permissionName}}? Revoking this permission may affect application functionality.", "cancelButton": "Cancel", "revokeButton": "Revoke", "impactPrefix": "Impact: {{impactText}}", "unknownImpact": "Unknown"}, "errors": {"refreshFailed": "Failed to refresh permissions. Please try again.", "fetchFailed": "Failed to fetch permissions. Please try again.", "revokeFailed": "Failed to revoke permission: {{permissionName}}. Please try again.", "revokeFailedGeneric": "Failed to revoke permission. Please try again.", "alreadyRevoked": "This permission has already been revoked. To grant it again, you must disconnect and reconnect to Meta."}, "noMatchFilters": "No permissions match your current filters.", "requiredInfoDialogText": "Some permissions cannot be revoked within this interface because they're essential for core functionality. Without these permissions, the Meta dashboard would stop working entirely.", "requiredInfoDialog": {"whyRequired": {"title": "Why are some permissions required?", "accessPagesList": "Access Pages List", "businessManagement": "Business Management", "listItem1": "We can't display which Pages you manage.", "listItem2": "We can't access any page metrics or content.", "listItem3": "Other permissions that depend on these will also fail."}, "howToRevokeAll": {"title": "How to revoke all permissions (workaround)", "text": "If you still want to revoke all permissions including required ones:", "step1": "Click \"Disconnect from Met<PERSON>\" at the top of this dashboard.", "step2": "Visit Facebook Application Settings.", "step3": "Find this application and click \"Remove\".", "note": "Note: You'll need to reconnect and grant permissions again to use the Meta dashboard features."}, "warning": "Revoking required permissions will completely disable Meta integration functionality."}, "common": {"okButton": "OK"}, "details": {"pages_show_list": {"name": "Access Pages List", "description": "Allows your app to access the list of Pages a person manages.", "impact": "Without this permission, you cannot select Facebook pages to manage."}, "pages_read_engagement": {"name": "Page Engagement Data", "description": "Allows your app to read content (posts, photos, videos, events) posted by the Page, read followers data, and read metadata and insights about the Page.", "impact": "Without this permission, engagement metrics will not be available."}, "pages_manage_metadata": {"name": "Manage Page Metadata", "description": "Allows your app to subscribe and receive webhooks about activity on the Page, and to update settings on the Page.", "impact": "Without this permission, you cannot update Page metadata through this app."}, "pages_read_user_content": {"name": "Read Page Content", "description": "Allows your app to read user generated content on the Page, such as posts, comments, and ratings by users or other Pages.", "impact": "Without this permission, content analysis features will be unavailable."}, "public_profile": {"name": "Public Profile", "description": "Allows an app to read the Default Public Profile Fields on the User profile.", "impact": "Without this permission, basic account information cannot be accessed."}, "email": {"name": "Email Address", "description": "Allows the app to access your email address.", "impact": "Without this permission, account notifications may not work properly."}, "business_management": {"name": "Business Management", "description": "Allows the app to access and manage your business assets.", "impact": "Without this permission, business features will be unavailable."}, "ads_management": {"name": "Ads Management", "description": "Allows your app to manage ads associated with the Page.", "impact": "Without this permission, ad management features will be unavailable."}, "ads_read": {"name": "Read Ads Data", "description": "Allows the app to read your advertising campaign data.", "impact": "Without this permission, ad analytics will be unavailable."}, "instagram_basic": {"name": "Instagram Basic", "description": "Allows your app to read a business's Instagram account info and media.", "impact": "Without this permission, Instagram features will be unavailable."}, "instagram_manage_insights": {"name": "Instagram Insights", "description": "Allows your app to read insights for a business's Instagram account.", "impact": "Without this permission, Instagram analytics will be unavailable."}, "instagram_content_publish": {"name": "Instagram Content Publishing", "description": "Allows your app to publish content to Instagram accounts connected to a Page.", "impact": "Without this permission, you cannot publish to Instagram through this app."}, "read_insights": {"name": "Read Insights", "description": "Provides access to Page and domain insights data for Pages and domains that you own or have access to.", "impact": "Without this permission, insights data will not be available."}, "catalog_management": {"name": "Catalog Management", "description": "Allows your app to create, read, update and delete business-owned product catalogs that the user is an admin of.", "impact": "Without this permission, catalog management features will be unavailable."}, "pages_manage_cta": {"name": "Manage Page CTA", "description": "Allows your app to manage call-to-action buttons on a Facebook Page.", "impact": "Without this permission, you cannot manage call-to-action buttons on Pages."}, "pages_manage_instant_articles": {"name": "Manage Instant Articles", "description": "Allows your app to manage Instant Articles on behalf of Facebook Pages administered by people using your app.", "impact": "Without this permission, you cannot manage Instant Articles."}, "read_page_mailboxes": {"name": "Read Page Mailboxes", "description": "Allows your app to read messages in a Page's inbox.", "impact": "Without this permission, you cannot access Page conversation messages."}, "pages_messaging": {"name": "Pages Messaging", "description": "Allows your app to manage and access Page conversations in Messenger.", "impact": "Without this permission, messaging features for Pages will be unavailable."}, "pages_messaging_phone_number": {"name": "Pages Messaging Phone Number", "description": "Allows access to the phone number used in a Page's messaging flow.", "impact": "Without this permission, you cannot access page messaging phone numbers."}, "pages_messaging_subscriptions": {"name": "Pages Messaging Subscriptions", "description": "Allows your app to manage messaging subscriptions for Pages.", "impact": "Without this permission, you cannot manage messaging subscriptions."}, "instagram_manage_comments": {"name": "Instagram Manage Comments", "description": "Allows your app to create and delete comments on behalf of an Instagram Business account.", "impact": "Without this permission, you cannot manage Instagram comments."}, "leads_retrieval": {"name": "Leads Retrieval", "description": "Allows your app to retrieve information collected through a business's lead generation forms.", "impact": "Without this permission, you cannot access lead generation data."}, "whatsapp_business_management": {"name": "WhatsApp Business Management", "description": "Allows your app to manage WhatsApp business accounts.", "impact": "Without this permission, WhatsApp business features will be unavailable."}, "instagram_manage_messages": {"name": "Instagram Manage Messages", "description": "Allows your app to manage messages for an Instagram Business account.", "impact": "Without this permission, you cannot manage Instagram messages."}, "page_events": {"name": "Page Events", "description": "Allows your app permission to log events on behalf of Facebook Pages administered by people using your app.", "impact": "Without this permission, you cannot log Page events for targeting and optimization."}, "pages_manage_ads": {"name": "Pages Manage Ads", "description": "Allows your app to manage ads associated with the Page.", "impact": "Without this permission, you cannot manage ads for Pages."}, "pages_manage_posts": {"name": "Pages Manage Posts", "description": "Allows your app to create, edit and delete your Page posts.", "impact": "Without this permission, you cannot manage Page posts."}, "instagram_shopping_tag_products": {"name": "Instagram Shopping Tag Products", "description": "Allows your app to tag products in Instagram posts and stories.", "impact": "Without this permission, you cannot tag products in Instagram content."}, "instagram_branded_content_creator": {"name": "Instagram Branded Content Creator", "description": "Allows your app to manage branded content permissions as a creator on Instagram.", "impact": "Without this permission, creator content features on Instagram will be limited."}, "instagram_branded_content_brand": {"name": "Instagram Branded Content Brand", "description": "Allows your app to manage branded content permissions as a brand on Instagram.", "impact": "Without this permission, brand content features on Instagram will be limited."}, "instagram_branded_content_ads_brand": {"name": "Instagram Branded Content Ads Brand", "description": "Allows your app to create ads from branded content as a brand on Instagram.", "impact": "Without this permission, you cannot create branded content ads on Instagram."}, "instagram_manage_events": {"name": "Instagram Manage Events", "description": "Allows your app to manage events for an Instagram Business account.", "impact": "Without this permission, you cannot manage Instagram events."}, "instagram_manage_upcoming_events": {"name": "Instagram Manage Upcoming Events", "description": "Allows your app to manage upcoming events for an Instagram Business account.", "impact": "Without this permission, you cannot manage upcoming Instagram events."}, "manage_fundraisers": {"name": "Manage Fundraisers", "description": "Allows your app to manage fundraisers on Facebook.", "impact": "Without this permission, fundraiser management features will be unavailable."}, "publish_video": {"name": "Publish Video", "description": "Allows your app to publish videos to Facebook.", "impact": "Without this permission, you cannot publish videos to Facebook."}, "private_computation_access": {"name": "Private Computation Access", "description": "Allows an app to access the Meta Private Computation products.", "impact": "Without this permission, private computation features will be unavailable."}, "attribution_read": {"name": "Attribution Read", "description": "Allows your app to read attribution data for ad campaigns.", "impact": "Without this permission, ad attribution analysis will be unavailable."}, "commerce_account_read_settings": {"name": "Commerce Account <PERSON> Settings", "description": "Allows your app to read commerce account settings.", "impact": "Without this permission, you cannot read commerce account information."}, "commerce_account_manage_orders": {"name": "Commerce Account Manage Orders", "description": "Allows your app to read and update commerce account orders.", "impact": "Without this permission, you cannot manage commerce account orders."}, "commerce_account_read_orders": {"name": "Commerce Account Read Orders", "description": "Allows your app to read commerce account orders.", "impact": "Without this permission, you cannot read commerce account orders."}, "commerce_account_read_reports": {"name": "Commerce Account Read Reports", "description": "Allows your app to read finance reporting data to build custom tax, cash reconciliation and reimbursement reports for a commerce account.", "impact": "Without this permission, commerce financial reporting will be unavailable."}, "whatsapp_business_messaging": {"name": "WhatsApp Business Messaging", "description": "Allows your app to send and receive messages through WhatsApp Business API.", "impact": "Without this permission, WhatsApp business messaging will be unavailable."}, "whatsapp_business_manage_events": {"name": "WhatsApp Business Manage Events", "description": "Allows your app to manage events for WhatsApp Business accounts.", "impact": "Without this permission, you cannot manage WhatsApp business events."}, "pages_manage_engagement": {"name": "Pages Manage Engagement", "description": "Allows your app to create, edit and delete comments posted on the Page.", "impact": "Without this permission, you cannot manage comments and engagement on Pages."}, "unavailableDescription": "Description not available.", "unavailableImpact": "Impact information not available.", "unknownPermission": {"name": "Unknown Permission ({{permissionId}})", "description": "This permission is not recognized by the application. Its details are unavailable.", "impact": "The impact of revoking this unknown permission cannot be determined."}}, "missingTitle": "Missing Permissions for {{feature}}", "missingDescription": "The following permissions are required to access this feature:", "businessManagementDescription": "Required to access business-level ad accounts", "adsManagementDescription": "Required to manage and read advertising data", "revoked": "Previously declined", "requestPermissions": "Request Permissions", "revokedNote": "Some permissions were previously declined. You may need to reconnect to Meta to grant them again."}, "tabs": {"chat": "Cha<PERSON>", "dashboard": "Dashboard", "iaMeta": "Meta AI", "configuration": "Configuration"}, "header": {"buttons": {"logout": "Log Out"}}, "chat.agent.greeting": "Hello! How can I help you today with the Bandida store?", "chat.input.placeholder": "Write your message here...", "chat.actions.think": "Think", "chat.actions.thinkTooltip": "Get thoughtful and detailed responses for questions that require researching.", "chat.actions.deepSearch": "Deep Search", "chat.actions.deepSearchTooltip": "Explore the web thoroughly for detailed competitor insights, recent market news, or other market insights of the industry you score.", "chat.actions.deeperSearch": "Deeper Search", "chat.actions.deeperSearchTooltip": "Quickly search the web for recent market news or competitor updates relevant to your store.", "chat.modeButton.think": "Think", "chat.modeButton.deepSearch": "Deep Search", "chat.modeButton.deeperSearch": "Deeper Search", "chat.attachImageTooltip": "Attach Image", "timeRangeFilter": {"error": {"exceeds30DaysInstagram": "Date range exceeds 30 days ({{diffDays}} days selected). Instagram API limits queries to 30 days."}}, "feedbackModal": {"title": "Give us your Feedback", "textFieldLabel": "Your Feedback", "errorEmptyText": "Feedback text cannot be empty.", "errorNoStore": "Could not identify store. Please login again.", "errorSubmitFailed": "Failed to submit feedback. Please try again.", "cancelButton": "Cancel", "submitButton": "Submit"}, "socialMediaCard.visitProfile": "Visit Profile", "socialMediaCard.officialPage": "Official Page", "socialMediaCard.notAvailable": "Not Available", "socialMediaCard.strategyTitle": "Social Media Strategy", "socialMediaCard.strategyTooltip": "AI-generated summary of the social media approach.", "cookies": {"firstTime": {"title": "🍪 Welcome to D-Unit!", "message": "We use cookies to enhance your experience. You can manage your preferences anytime.", "accept": "Accept All", "manage": "Manage", "close": "Close"}, "banner": {"title": "<PERSON><PERSON>", "description": "We use cookies to enhance your experience, analyze site usage, and assist with marketing. Essential cookies are required for the site to function properly.", "legalNotice": "This website complies with GDPR, CCPA, LGPD, and other privacy regulations. You can withdraw consent at any time through the cookie settings.", "acceptAll": "Accept All", "rejectAll": "Reject All", "customize": "Customize", "close": "Close", "moreInfo": "Learn more in our", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>"}, "details": {"title": "Cookie Preferences", "description": "Manage your cookie preferences below. You can enable or disable categories of cookies, and view detailed information about each cookie we use.", "close": "Close", "cancel": "Cancel", "rejectAll": "Reject All Optional", "save": "Save Preferences", "version": "Consent Version", "lastUpdated": "Last Updated"}, "settings": {"title": "<PERSON><PERSON>", "description": "Manage your cookie preferences and view detailed information about the cookies we use. Changes will take effect immediately.", "consentGiven": "Consent Given", "consentVersion": "Version", "consentId": "Consent ID", "save": "Save Preferences", "resetConsent": "Reset Consent", "saved": "<PERSON>ie preferences saved successfully", "reset": "Cookie consent has been reset", "legalInfo": "Legal Information", "legalText": "This website complies with GDPR, CCPA, LGPD, PIPEDA, and other applicable privacy regulations. You have the right to:", "right1": "Withdraw consent at any time", "right2": "Request deletion of your data", "right3": "Access your personal data", "right4": "Data portability", "version": "Configuration Version", "supportedRegulations": "Supported Regulations", "details": "Details", "noConsent": "No consent given yet", "allAccepted": "All cookies accepted", "optionalRejected": "Optional cookies rejected", "privacyPolicyLink": "Privacy Policy"}, "category": {"essential": "Essential", "cookies": "cookies", "cookiesUsed": "Cookies in this category:"}, "table": {"name": "<PERSON><PERSON>", "purpose": "Purpose", "duration": "Duration", "type": "Type", "provider": "Provider"}, "types": {"first-party": "First Party", "third-party": "Third Party"}, "durations": {"session": "Session", "persistent": "Persistent", "timeUnits": {"hour": "hour", "hours": "hours", "day": "day", "days": "days", "year": "year", "years": "years", "session": "Session"}}, "purposes": {"authSession": "Authentication and user session management", "sessionRenewal": "Session renewal and security", "csrfProtection": "Security - CSRF protection", "storeIsolation": "Store data isolation and access control", "contentDelivery": "Content delivery and load balancing", "userPreferences": "Store user preferences and customization", "languageLocalization": "Language localization", "themeCustomization": "UI theme customization", "usageAnalytics": "Website usage analytics and improvement", "navigationPatterns": "User navigation patterns and page performance", "performanceMonitoring": "Performance monitoring and optimization", "errorTracking": "Error detection and application stability", "metaIntegration": "Meta/Facebook platform integration", "facebookTools": "Facebook business tools integration"}}, "cookiePolicy": {"title": "<PERSON><PERSON>", "subtitle": "Comprehensive information about cookies used on D-Unit", "lastUpdated": "Last Updated: {{date}}", "effectiveDate": "Effective Date: May 1, 2025", "introduction": {"title": "Introduction", "content": "This Cookie Policy explains how D-Unit uses cookies and similar technologies to recognize you when you visit our website. It explains what these technologies are and why we use them, as well as your rights to control our use of them."}, "whatAreCookies": {"title": "What Are Cookies?", "content": "Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work, or to work more efficiently, as well as to provide reporting information."}, "whyWeUseCookies": {"title": "Why Do We Use Cookies?", "content": "We use cookies for several reasons. Some cookies are required for technical reasons in order for our website to operate, and we refer to these as 'essential' or 'strictly necessary' cookies. Other cookies enable us to track and target the interests of our users to enhance the experience on our website."}, "categories": {"title": "Categories of Cookies We Use", "essential": {"title": "Essential Cookies", "description": "These cookies are strictly necessary to provide you with services available through our website and to use some of its features, such as access to secure areas. Because these cookies are strictly necessary to deliver the website, you cannot refuse them without impacting how our site functions."}, "functional": {"title": "Functional Cookies", "description": "These cookies enable the website to provide enhanced functionality and personalization. They may be set by us or by third-party providers whose services we have added to our pages."}, "analytics": {"title": "Analytics Cookies", "description": "These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site."}, "performance": {"title": "Performance Cookies", "description": "These cookies are used to enhance the performance and functionality of our website but are non-essential to their use. However, without these cookies, certain functionality may become unavailable."}, "marketing": {"title": "Marketing Cookies", "description": "These cookies may be set through our site by our advertising partners. They may be used by those companies to build a profile of your interests and show you relevant adverts on other sites."}}, "cookieInventory": {"title": "Detailed <PERSON><PERSON>", "description": "Below is a comprehensive list of all cookies used on our website:", "tableHeaders": {"name": "<PERSON><PERSON>", "purpose": "Purpose", "category": "Category", "duration": "Duration", "type": "Type", "provider": "Provider"}}, "management": {"title": "How to Manage Your Cookie Preferences", "content": "You have several options for managing cookies:", "options": {"browserSettings": "Browser Settings: Most web browsers allow you to control cookies through their settings preferences.", "ourSettings": "Our Cookie Settings: You can manage your cookie preferences at any time through our cookie settings panel.", "optOut": "Opt-out Links: For certain cookies, we provide direct opt-out mechanisms."}}, "yourRights": {"title": "Your Rights Under Privacy Laws", "content": "Under various privacy regulations (GDPR, CCPA, LGPD, PIPEDA), you have the following rights:", "rights": {"access": "Access: You have the right to know what personal data we collect and how we use it.", "rectification": "Rectification: You have the right to correct inaccurate personal data.", "erasure": "Erasure: You have the right to request deletion of your personal data.", "portability": "Data Portability: You have the right to receive your personal data in a structured format.", "withdraw": "Withdraw Consent: You can withdraw your consent for non-essential cookies at any time.", "object": "Object: You have the right to object to certain types of processing."}}, "legalBasis": {"title": "Legal Basis for Processing", "content": "We process cookies based on the following legal grounds:", "grounds": {"consent": "Consent: For non-essential cookies, we rely on your explicit consent.", "legitimate": "Legitimate Interest: For analytics and performance cookies that help us improve our services.", "necessary": "Contractual Necessity: For essential cookies required to provide our services."}}, "thirdParty": {"title": "Third-Party Cookies", "content": "Some cookies are placed by third-party services that appear on our pages. We do not control these cookies, and you should check the relevant third party's website for more information.", "services": {"meta": "Meta/Facebook: For social media integration and advertising", "google": "Google Analytics: For website usage analytics", "aws": "Amazon Web Services: For content delivery and performance"}}, "updates": {"title": "Updates to This Policy", "content": "We may update this Cookie Policy from time to time to reflect changes in our practices or for legal, operational, or regulatory reasons. We will notify you of any material changes by posting the new policy on this page with a new 'Last Updated' date."}, "contact": {"title": "Contact Information", "content": "If you have any questions about our use of cookies or this Cookie Policy, please contact us:", "email": "Email: <EMAIL>", "address": "Data Protection Officer, D-Unit"}, "compliance": {"title": "Regulatory Compliance", "frameworks": {"gdpr": "GDPR (General Data Protection Regulation) - European Union", "ccpa": "CCPA (California Consumer Privacy Act) - California, USA", "lgpd": "LGPD (Lei Geral de Proteção de Dados) - Brazil", "pipeda": "PIPEDA (Personal Information Protection and Electronic Documents Act) - Canada"}}, "navigation": {"backToTop": "Back to Top", "manageCookies": "Manage Cookie Preferences", "viewPrivacyPolicy": "View Privacy Policy"}}, "storeView": {"errorNoPermission": "You do not have permission to view this store or it does not exist.", "invalidStoreId": "Invalid store ID or insufficient permissions.", "incompleteUserData": "User data is incomplete. Cannot display dashboard.", "adminViewLabel": "Admin View: Store #{{storeId}}"}, "productDetails": {"detailsTitle": "Details", "salesDetailsTitle": "Sales Details", "salesDate": "Date", "salesUnits": "Units", "salesRevenue": "Revenue", "noSalesDetails": "No sales details available.", "loadMoreSales": "Load More Sales", "showLessSales": "Show Less Sales", "noProducts": "No product details available.", "header": {"product": "Product", "salesUnits": "Units Sold", "grossRevenue": "Gross Revenue", "netRevenue": "Net Revenue (After Discounts)", "price": "Price", "stock": "Stock", "favorites": "Favorites", "primaryCategory": "Primary Category"}}, "verificationCodeLabel": "Verification Code", "newPasswordLabel": "New Password", "confirmPasswordLabel": "Confirm Password", "successPasswordCodeSent": "We've emailed you a verification code.", "successPasswordReset": "Password reset successful. Redirecting…", "resetPasswordButton": "Reset Password", "errorUnknownAdd": "Failed to add credits", "errors": {"insufficientCredits": "You ran out of credits for using the chat, please contact the administrator"}}