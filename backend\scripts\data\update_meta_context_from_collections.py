#!/usr/bin/env python
"""
Script to build meta_chat_context using real data from all meta_* collections.
This ensures we're using actual database data instead of mock values.
"""

from datetime import datetime
import pymongo
from bson import ObjectId
import logging
import os
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# MongoDB connection details
MONGO_URI = os.getenv('MONGODB_CONNECTION', "mongodb+srv://operaciones:<EMAIL>/")
DB_NAME = os.getenv('MONGODB_ANALYSIS_DB', "D-Unit-AnalysisGPT")

def connect_to_mongo():
    """Connect to MongoDB and return client and database objects."""
    try:
        client = pymongo.MongoClient(MONGO_URI)
        db = client[DB_NAME]
        logger.info(f"Connected to MongoDB database: {DB_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"Error connecting to MongoDB: {e}")
        raise

def get_meta_collections(db):
    """Get all meta_* collections from the database."""
    try:
        all_collections = db.list_collection_names()
        meta_collections = [coll for coll in all_collections if coll.startswith('meta_')]
        logger.debug(f"Found {len(meta_collections)} meta collections: {meta_collections}")
        return meta_collections
    except Exception as e:
        logger.error(f"Error getting meta collections: {e}")
        return []

def get_store_data(db, store_id):
    """Get store information from global_analysis collection."""
    try:
        store_data = db.global_analysis.find_one({"_id": str(store_id)})
        if not store_data:
            logger.error(f"No data found for store {store_id} in global_analysis")
            return None
        logger.info(f"Found store data for store {store_id}")
        return store_data
    except Exception as e:
        logger.error(f"Error retrieving store data: {e}")
        return None

def get_data_from_meta_collection(db, collection_name, store_id):
    """Get data from a meta collection for the specified store."""
    try:
        # Different collections might use different field names for store_id
        possible_id_fields = ["store_id", "id_store", "_id"]
        
        # Try different id fields
        for id_field in possible_id_fields:
            # Try string and int formats
            for id_value in [str(store_id), int(store_id)]:
                query = {id_field: id_value}
                data = list(db[collection_name].find(query))
                if data:
                    logger.info(f"Found {len(data)} documents in {collection_name} using {id_field}={id_value}")
                    return data
        
        logger.warning(f"No data found in {collection_name} for store {store_id}")
        return []
    except Exception as e:
        logger.error(f"Error retrieving data from {collection_name}: {e}")
        return []

def build_meta_chat_context(db, store_id):
    """Build meta_chat_context using real data from all meta collections."""
    try:
        # Initialize the context structure
        meta_context = {
            "store_id": str(store_id),
            "pages": [],
            "insights": [],
            "engagement_metrics": {
                "total_likes": 0,
                "total_comments": 0,
                "total_shares": 0,
                "total_engagement": 0
            },
            "audience_metrics": {
                "total_followers": 0
            },
            "ad_metrics": {},
            "is_mock_data": False,
            "last_updated": datetime.now(),
            "managed_by_script": True,  # Add flag to prevent automatic updates
            "_metadata": {
                "created_by": "build_meta_context_from_collections.py",
                "created_at": datetime.now(),
                "last_updated_at": datetime.now(),
                "version": "1.0"
            }
        }
        
        # Get store data for reference
        store_data = get_store_data(db, store_id)
        if store_data:
            store_info = store_data.get('store', {})
            logger.info(f"Store name: {store_info.get('name', 'Unknown')}")
        else:
            store_info = {}
        
        # Get all meta collections
        meta_collections = get_meta_collections(db)
        
        # Process meta_ad_* collections for ad metrics
        ad_metrics = {}
        ad_collections = [c for c in meta_collections if c.startswith('meta_ad_')]
        for collection in ad_collections:
            ad_data = get_data_from_meta_collection(db, collection, store_id)
            if ad_data:
                collection_key = collection.replace('meta_ad_', '')
                ad_metrics[collection_key] = ad_data
                logger.info(f"Added {len(ad_data)} ad metrics from {collection}")
        
        meta_context['ad_metrics'] = ad_metrics
        
        # Process meta_insights
        if 'meta_insights' in meta_collections:
            insights_data = get_data_from_meta_collection(db, 'meta_insights', store_id)
            
            # Convert insights to the required format
            insights = []
            for insight in insights_data:
                insights.append({
                    "type": insight.get('type', 'general'),
                    "text": insight.get('text', insight.get('content', '')),
                    "date": insight.get('date', insight.get('created_at', datetime.now()))
                })
            
            meta_context['insights'] = insights
            logger.info(f"Added {len(insights)} insights")
        
        # Process meta_pages
        if 'meta_pages' in meta_collections:
            pages_data = get_data_from_meta_collection(db, 'meta_pages', store_id)
            
            # Convert pages to the required format
            pages = []
            for page in pages_data:
                pages.append({
                    "id": page.get('id', page.get('page_id', '')),
                    "name": page.get('name', ''),
                    "platform": page.get('platform', 'facebook'),
                    "url": page.get('url', ''),
                    "followers": page.get('followers', 0),
                    "engagement_rate": page.get('engagement_rate', 0)
                })
            
            meta_context['pages'] = pages
            logger.info(f"Added {len(pages)} pages")
        
        # Process meta_post_metrics for engagement
        if 'meta_post_metrics' in meta_collections:
            engagement_data = get_data_from_meta_collection(db, 'meta_post_metrics', store_id)
            if engagement_data:
                total_likes = 0
                total_comments = 0
                total_shares = 0
                # Aggregate metrics from all post metric documents for the store
                for post_metric in engagement_data:
                    total_likes += post_metric.get('likes', 0)
                    total_comments += post_metric.get('comments', 0)
                    total_shares += post_metric.get('shares', 0)
                
                total_engagement = total_likes + total_comments + total_shares
                
                meta_context['engagement_metrics'] = {
                    "total_likes": total_likes,
                    "total_comments": total_comments,
                    "total_shares": total_shares,
                    "total_engagement": total_engagement
                }
                logger.info(f"Aggregated engagement metrics from {len(engagement_data)} post metrics documents")
        
        # Process audience metrics (followers and demographics)
        total_followers = 0
        if 'meta_followers' in meta_collections:
            # Fetch the most recent follower document for the store
            follower_doc = db.meta_followers.find_one(
                {'store_id': str(store_id)}, # Assuming store_id is stored as string
                sort=[('date', pymongo.DESCENDING)]
            )
            if not follower_doc:
                # Try int store_id as fallback
                 follower_doc = db.meta_followers.find_one(
                    {'store_id': int(store_id)},
                    sort=[('date', pymongo.DESCENDING)]
                 )
            if follower_doc:
                total_followers = follower_doc.get('total', 0)
                logger.info(f"Retrieved total followers: {total_followers}")

        demographics_detail = {}
        locations_detail = []
        if 'meta_demographics' in meta_collections:
            # Fetch the most recent demographics document
            demo_doc = db.meta_demographics.find_one(
                {'store_id': str(store_id)},
                sort=[('date', pymongo.DESCENDING)]
            )
            if not demo_doc:
                # Try int store_id
                 demo_doc = db.meta_demographics.find_one(
                    {'store_id': int(store_id)},
                    sort=[('date', pymongo.DESCENDING)]
                 )

            if demo_doc:
                # Map the fields from meta_demographics doc to the expected structure
                demographics_detail['age_ranges'] = demo_doc.get('age_ranges', [])
                demographics_detail['gender'] = demo_doc.get('gender', [])
                locations_detail = demo_doc.get('top_locations', [])
                logger.info(f"Retrieved demographics details")

        # Update the context (add or update keys)
        meta_context['audience_metrics'] = {
             "total_followers": total_followers,
             "demographics": demographics_detail,
             "locations": locations_detail
        }
        logger.info(f"Added audience metrics")
        
        # Check if we have Meta integration
        if store_info and 'meta_integration' in store_info:
            if store_info['meta_integration'].get('connected', False):
                meta_id = store_info['meta_integration'].get('user_id', '')
                
                # If no pages were found but Meta is connected, add a default page
                if not meta_context['pages'] and meta_id:
                    store_name = store_info.get('name', 'Store Page')
                    meta_context['pages'].append({
                        "id": meta_id,
                        "name": store_name,
                        "platform": "facebook",
                        "url": f"https://facebook.com/{meta_id}",
                        "followers": 0,
                        "engagement_rate": 0
                    })
                    logger.info(f"Added default page for Meta-connected store")
        
        # If we still have no insights but have store data, generate basic insights
        if not meta_context['insights'] and store_data:
            metrics = store_data.get('metrics', {})
            
            # Add revenue insight
            if 'total_revenue' in metrics:
                currency_symbol = store_info.get('currency', {}).get('native', {}).get('symbol', '$')
                meta_context['insights'].append({
                    "type": "revenue",
                    "text": f"Your store has generated {currency_symbol}{metrics.get('total_revenue', 0)} in total revenue.",
                    "date": datetime.now()
                })
            
            # Add customer insight
            if 'customer_count' in metrics:
                meta_context['insights'].append({
                    "type": "customers",
                    "text": f"You have {metrics.get('customer_count', 0)} customers in your database.",
                    "date": datetime.now()
                })
            
            # Add order insight
            if 'order_count' in metrics:
                meta_context['insights'].append({
                    "type": "orders",
                    "text": f"Your store has received {metrics.get('order_count', 0)} orders in total.",
                    "date": datetime.now()
                })
            
            logger.info(f"Generated {len(meta_context['insights'])} basic insights from store data")
        
        # Determine if this is mock data
        has_real_data = (
            len(meta_context['insights']) > 0 or
            len(meta_context['pages']) > 0 or
            meta_context['engagement_metrics']['total_engagement'] > 0 or
            meta_context['audience_metrics']['total_followers'] > 0 or
            bool(meta_context['ad_metrics'])
        )
        
        meta_context['is_mock_data'] = not has_real_data
        logger.info(f"is_mock_data set to {meta_context['is_mock_data']}")
        
        # Update or create the meta_chat_context document
        collection = db["meta_chat_context"]
        existing = collection.find_one({"store_id": str(store_id)})
        
        if existing:
            logger.info(f"Found existing meta_chat_context for store {store_id}, updating it")
            # Check if this document was previously managed, preserve that info
            if existing.get("managed_by_script") is True:
                logger.info(f"Existing document for store {store_id} was already managed by script")
            
            # Update metadata if it exists
            if "_metadata" in existing:
                meta_context["_metadata"] = existing["_metadata"]
                meta_context["_metadata"]["last_updated_at"] = datetime.now()
                meta_context["_metadata"]["last_updated_by"] = "build_meta_context_from_collections.py"
                meta_context["_metadata"]["update_count"] = existing["_metadata"].get("update_count", 0) + 1
            
            result = collection.replace_one({"store_id": str(store_id)}, meta_context)
            logger.info(f"Updated meta_chat_context for store {store_id}, matched: {result.matched_count}, modified: {result.modified_count}")
        else:
            logger.info(f"No existing meta_chat_context found for store {store_id}, creating new document")
            result = collection.insert_one(meta_context)
            logger.info(f"Inserted meta_chat_context for store {store_id} with _id: {result.inserted_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error building meta_chat_context: {e}")
        logger.exception("Full traceback:")
        return False

def process_store(db, store_id):
    """Process a single store."""
    logger.info(f"Processing store {store_id}")
    success = build_meta_chat_context(db, store_id)
    if success:
        logger.info(f"Successfully processed store {store_id}")
    else:
        logger.error(f"Failed to process store {store_id}")
    return success

def process_all_stores(db):
    """Process all stores in the database."""
    try:
        # Get all store IDs from global_analysis
        store_ids = [doc["_id"] for doc in db.global_analysis.find({}, {"_id": 1})]
        logger.info(f"Found {len(store_ids)} stores to process")
        
        successful = 0
        for store_id in store_ids:
            if process_store(db, store_id):
                successful += 1
        
        logger.info(f"Processed {successful} out of {len(store_ids)} stores successfully")
        return successful
    except Exception as e:
        logger.error(f"Error processing all stores: {e}")
        return 0

def main():
    """Main function to process stores."""
    try:
        client, db = connect_to_mongo()
        
        # Check if mock data testing is enabled
        enable_mock_testing = os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true"
        
        if enable_mock_testing:
            # Only process store 566 for testing when mock data is enabled
            logger.info("====== Processing store 566 (Mock Data Testing) ======")
            success = process_store(db, 566)
        else:
            logger.info("Mock data testing is disabled. Skipping store 566 processing.")
            logger.info("To enable mock data testing, set ENABLE_META_MOCK_DATA=true")
            success = True  # Consider this a success since we're deliberately skipping
        
        if success:
            # Let's check what's in the meta_chat_context for store 566
            meta_context = db.meta_chat_context.find_one({"store_id": "566"})
            if meta_context:
                logger.info("====== Meta Chat Context Summary ======")
                logger.info(f"Pages: {len(meta_context.get('pages', []))}")
                logger.info(f"Insights: {len(meta_context.get('insights', []))}")
                logger.info(f"Ad metrics keys: {meta_context.get('ad_metrics', {}).keys()}")
                logger.info(f"Engagement metrics: {meta_context.get('engagement_metrics', {})}")
                logger.info(f"Audience metrics: {meta_context.get('audience_metrics', {})}")
                logger.info(f"is_mock_data: {meta_context.get('is_mock_data', True)}")
        
        client.close()
        
    except Exception as e:
        logger.error(f"Error in main function: {e}")
        logger.exception("Full traceback:")

if __name__ == "__main__":
    main() 
