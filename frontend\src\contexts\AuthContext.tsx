import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import authService from '../services/authService';
import logger from '../utils/logger';
import cookieService from '../services/cookieService';

export interface User {
  email: string;
  id_store: string;  // Changed from 'number | string' to 'string' to align with backend model
  name: string | null;
  role?: string;
  active?: number;
  created_at?: string;
  updated_at?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  googleLogin: (accessToken: string) => Promise<void>;
  metaLogin: (accessToken: string, platform?: 'facebook' | 'instagram') => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (value: boolean) => void;
  performLogoutActions: () => void;
  viewingStoreID: string | null;
  setViewStoreID: (id: string | null) => void;
}

// Define error interface for API responses
interface APIError {
  response?: {
    data?: {
      detail?: string;
      requires_2fa?: boolean;
      message?: string;
    };
  };
  message?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false); // Add state for logout progress
  const [viewingStoreID, setViewStoreID] = useState<string | null>(null);

  // Helper method to trigger first-time cookie notice - MOVED HERE TO FIX INITIALIZATION ORDER
  const triggerFirstTimeCookieNotice = useCallback(() => {
    try {
      if (cookieService.isFirstTimeLogin() && !cookieService.hasConsent()) {
        // Dispatch custom event to show first-time cookie notice
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('show-first-time-cookie-notice'));
        }, 1000); // Small delay to let the dashboard load
      }
    } catch (error) {
      logger.error('Error checking first-time cookie notice:', error);
      // Continue without showing the popup if there's an error
    }
  }, []);

  // Helper to get stored user with fallbacks: localStorage -> sessionStorage -> cookie
const getStoredUser = (): string | null => {
  try {
    return localStorage.getItem('user');
  } catch {
    try {
      return sessionStorage.getItem('user');
    } catch {
      const cookies = document.cookie.split('; ');
      const userCookie = cookies.find(c => c.startsWith('user='));
      return userCookie ? decodeURIComponent(userCookie.split('=')[1]) : null;
    }
  }
};

// Helper to set user with fallbacks: localStorage -> sessionStorage -> cookie
const setStoredUser = (userData: User) => {
  const data = JSON.stringify(userData);
  try {
    localStorage.setItem('user', data);
  } catch {
    try {
      sessionStorage.setItem('user', data);
    } catch {
      document.cookie = `user=${encodeURIComponent(data)}; path=/; SameSite=Lax; Secure`;
    }
  }
};

// Detect and handle third-party cookie blocking 
const testThirdPartyCookies = async (): Promise<boolean> => { 
  try { 
    document.cookie = "test=1; SameSite=None; Secure"; 
    const hasCookie = document.cookie.includes("test=1"); 
    // Clean up 
    document.cookie = "test=; Max-Age=0; path=/;"; 
    return hasCookie; 
  } catch { 
    return false; 
  } 
}; 

useEffect(() => {
const initializeAuth = async () => { 
  const canUseThirdPartyCookies = await testThirdPartyCookies(); 
  if (!canUseThirdPartyCookies) { 
    logger.warn('Third-party cookies appear to be blocked. Using fallback storage methods.'); 
  }
      const token = authService.getToken();
      const storedUser = getStoredUser();
      
      if (token && storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          setUser({
            email: userData.email,
            id_store: userData.id_store,
            name: userData.name || null,
            active: userData.active,
            created_at: userData.created_at,
            updated_at: userData.updated_at,
            role: userData.role
          });
          setIsAuthenticated(true);
        } catch (error) {
          logger.error('Auth initialization error:', error);
          authService.clearTokens();
          localStorage.removeItem('user');
        }
      }
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (email: string, password: string) => {
    try {
       await performLogoutActions(true);
      logger.info('Sending login request...');
      // Get the token using the auth service
      const tokenData = await authService.login(email, password);

      logger.info('Token response received');
      
      // Check if this is a 2FA response
      if (tokenData.requires_2fa) {
        logger.info('2FA required, throwing response for component to handle');
        throw { response: { data: { requires_2fa: true, message: tokenData.message } } };
      }
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error('No access token received');
      }

      // Clear any existing auth data only after successful token response
      authService.clearTokens();
      localStorage.removeItem('user');

      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }

      // Get user data
      try {
        const userData = await authService.getUserProfile();
        
        // Store user data with fallback
        setStoredUser(userData);
        
        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });
        
        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('Error fetching user data:', userError);
        throw new Error('Failed to fetch user data after login');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Login error:', apiError);
      logger.error('Login error details:', apiError.response?.data);
      
      // Don't clear tokens if this is a 2FA response
      if (!apiError.response?.data?.requires_2fa) {
        authService.clearTokens();
        localStorage.removeItem('user');
      }
      
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice]);

  const googleLogin = useCallback(async (accessToken: string) => {
    try {
      await performLogoutActions(true);
      logger.info('Processing Google access token...');
      
      // Exchange Google access token for our app token
      const tokenData = await authService.googleLogin(accessToken);
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error('No access token received from Google login');
      }
      
      // Clear any existing auth data
      authService.clearTokens();
      localStorage.removeItem('user');
      
      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }
      
      // Get user data
      try {
        const userData = await authService.getUserProfile();
        
        // Store user data with fallback
        setStoredUser(userData);
        
        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });
        
        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('Error fetching user data after Google login:', userError);
        throw new Error('Failed to fetch user data after Google login');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Google login error:', apiError);
      logger.error('Google login error details:', apiError.response?.data);
      
      authService.clearTokens();
      localStorage.removeItem('user');
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice]);

  const metaLogin = useCallback(async (accessToken: string, platform: 'facebook' | 'instagram' = 'facebook') => {
    try {
      await performLogoutActions(true);
      logger.info(`AuthContext: Processing ${platform} access token...`);
      
      // Exchange Meta token for our app token
      const tokenData = await authService.metaLogin(accessToken, platform);
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error(`No access token received from ${platform} login`);
      }
      
      // Clear any existing auth data
      authService.clearTokens();
      localStorage.removeItem('user');
      
      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }
      
      // Get user data
      try {
        const userData = await authService.getUserProfile();
        
        // Store user data with fallback
        setStoredUser(userData);
        
        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });
        
        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('AuthContext: Error fetching user data:', userError);
        throw new Error('Failed to fetch user data after Meta authentication');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Meta login error:', apiError);
      logger.error('Meta login error details:', apiError.response?.data);
      
      authService.clearTokens();
      localStorage.removeItem('user');
      
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice]);

  // Define the core logout actions in a separate function for aggressive clearing
  const performLogoutActions = useCallback(async (skipReload: boolean = false) => {
    logger.info('Performing aggressive session clear' + (skipReload ? ' without reload' : ''));

    try {
      // Clear CSRF tokens
      const { csrfService } = await import('../services/csrfService');
      try {
        csrfService.clearToken();
      } catch (error: unknown) {
        logger.warn('Failed to clear CSRF tokens:', error);
      }
      
      // Clear all tokens and user data
      authService.clearTokens();
      cookieService.clearAllCookies();
      localStorage.clear();
sessionStorage.clear();
document.cookie = 'user=; Max-Age=0; path=/;';
document.cookie = 'test=; Max-Age=0; path=/;';

      // Reset state
      setUser(null);
      setIsAuthenticated(false);
      setViewStoreID(null);
    } catch (error) {
      logger.error('Error during session clear:', error);
    } finally {
      if (!skipReload) {
        window.location.href = '/login';
      }
    }
  }, []);

  // The main logout function, which prevents duplicates and calls the action.
  const logout = useCallback(() => {
    if (isLoggingOut) {
      logger.warn('[AuthContext] Logout already in progress, ignoring duplicate request.');
      return;
    }
    setIsLoggingOut(true);
    logger.info('[AuthContext] Logout requested. Performing aggressive logout...');
    performLogoutActions();
    // No code will execute after this point due to page navigation
  }, [isLoggingOut, performLogoutActions]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        login,
        googleLogin,
        metaLogin,
        logout,
        isLoading,
        setUser,
        setIsAuthenticated,
        performLogoutActions,
        viewingStoreID,
        setViewStoreID
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
