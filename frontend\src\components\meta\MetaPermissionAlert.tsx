import React from 'react';
import { useTranslation } from 'react-i18next';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';
import { MetaAuthService } from '../../services/auth';

interface MetaPermissionAlertProps {
  requiredPermissions: string[];
  featureName: string;
  onRetry?: () => void;
}

export const MetaPermissionAlert: React.FC<MetaPermissionAlertProps> = ({
  requiredPermissions,
  featureName,
  onRetry
}) => {
  const { t } = useTranslation();
  const { getPermissionStatus, refreshPermissions } = useMetaPermissions();

  const missingPermissions = requiredPermissions.filter(
    permission => getPermissionStatus(permission) !== 'granted'
  );

  const revokedPermissions = requiredPermissions.filter(
    permission => getPermissionStatus(permission) === 'declined'
  );

  const handleRequestPermissions = async () => {
    try {
      await MetaAuthService.requestAdditionalPermissions(missingPermissions);
      await refreshPermissions(true);
      if (onRetry) {
        onRetry();
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
    }
  };

  if (missingPermissions.length === 0) {
    return null;
  }

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">
            {t('meta.permissions.missingTitle', { feature: featureName })}
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p className="mb-2">
              {t('meta.permissions.missingDescription')}
            </p>
            <ul className="list-disc list-inside space-y-1">
              {missingPermissions.map(permission => (
                <li key={permission}>
                  <span className="font-medium">{permission}</span>
                  {permission === 'business_management' && (
                    <span className="text-xs ml-2">
                      ({t('meta.permissions.businessManagementDescription')})
                    </span>
                  )}
                  {permission === 'ads_management' && (
                    <span className="text-xs ml-2">
                      ({t('meta.permissions.adsManagementDescription')})
                    </span>
                  )}
                  {revokedPermissions.includes(permission) && (
                    <span className="text-red-600 text-xs ml-2">
                      ({t('meta.permissions.revoked')})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
          <div className="mt-4">
            <div className="flex space-x-3">
              <button
                onClick={handleRequestPermissions}
                className="bg-yellow-100 px-3 py-2 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                {t('meta.permissions.requestPermissions')}
              </button>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="bg-white px-3 py-2 rounded-md text-sm font-medium text-yellow-800 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  {t('common.retry')}
                </button>
              )}
            </div>
          </div>
          {revokedPermissions.length > 0 && (
            <div className="mt-3 text-xs text-yellow-600">
              <p>
                {t('meta.permissions.revokedNote')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};