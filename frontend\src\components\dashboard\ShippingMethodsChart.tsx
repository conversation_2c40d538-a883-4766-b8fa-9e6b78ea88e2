import React, { useEffect, useRef } from 'react';
import { Box, Typography, Card, CardContent, useTheme } from '@mui/material';
import * as d3 from 'd3';
import { useTranslation } from 'react-i18next';

interface ShippingMethodData {
  name: string;
  timesUsed: number;
}

// Define chart-specific data type that includes isEmpty flag
type ChartMethodData = ShippingMethodData & { 
  isEmpty?: boolean;
  displayName?: string;
};

interface ShippingMethodsChartProps {
  methods: ShippingMethodData[];
  title?: string;
  height?: number;
}

/**
 * A component for visualizing shipping methods usage data
 */
export const ShippingMethodsChart: React.FC<ShippingMethodsChartProps> = ({
  methods,
  title,
  height = 300
}) => {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const theme = useTheme(); // Add theme hook

  useEffect(() => {
    if (!chartRef.current) return;

    // Format method names to prevent long labels
    const formattedMethods = methods.map(method => ({
      ...method,
      // Ensure original name is preserved for display
      originalName: method.name,
      name: method.name.length > 15 ? `${method.name.substring(0, 15)}...` : method.name,
      isEmpty: false
    }));

    // Add empty slots if less than 5 methods
    const defaultMethods = Array(5).fill(null).map((_, index) => {
      if (index < formattedMethods.length) {
        return formattedMethods[index];
      }
      return {
        name: `slot${index + 1}`,
        originalName: '',
        displayName: '',
        timesUsed: 0,
        isEmpty: true
      };
    });

    // Clear previous chart
    d3.select(chartRef.current).selectAll('*').remove();

    // Set up dimensions
    const margin = { top: 20, right: 20, bottom: 40, left: 60 };
    const width = chartRef.current.clientWidth - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(chartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add background grid
    svg.append('g')
      .attr('class', 'grid')
      .attr('opacity', 0.05)
      .call(d3.axisLeft(d3.scaleLinear().range([chartHeight, 0]))
        .tickSize(-width)
        .tickFormat(() => '')
      );

    // Set up scales
    const x = d3.scaleBand()
      .domain(defaultMethods.map(d => d.name))
      .range([0, width])
      .padding(0.5);

    const y = d3.scaleLinear()
      .domain([0, d3.max(defaultMethods, d => d.timesUsed) || 0])
      .range([chartHeight, 0])
      .nice();

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('fill', theme.palette.mode === 'dark' ? '#fff' : '#666') // Update text color
      .text(d => {
        const method = defaultMethods.find(m => m.name === d);
        return method?.isEmpty ? '' : method?.name || '';
      });

    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y)
        .ticks(5)
        .tickFormat(d => `${(d as number).toLocaleString()}`)
      )
      .selectAll('text')
      .style('font-size', '12px')
      .style('fill', theme.palette.mode === 'dark' ? '#fff' : '#666'); // Update text color

    // Add bars
    svg.selectAll('.bar')
      .data(defaultMethods)
      .join('rect')
      .attr('class', 'bar')
      .attr('x', d => x(d.name) || 0)
      .attr('y', chartHeight)
      .attr('width', x.bandwidth())
      .attr('height', 0)
      .attr('fill', '#00A3FF')
      .attr('opacity', d => d.isEmpty ? 0.1 : 0.9)
      .transition()
      .duration(800)
      .attr('y', d => y(d.timesUsed))
      .attr('height', d => chartHeight - y(d.timesUsed));

    // Add tooltips
    const tooltip = d3.select(chartRef.current)
      .append('div')
      .attr('class', 'tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background-color', theme.palette.mode === 'dark' ? '#2D2D2D' : 'white')
      .style('color', theme.palette.mode === 'dark' ? '#fff' : '#111')
      .style('border', `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(224, 231, 255, 0.8)'}`)
      .style('padding', '8px 12px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('box-shadow', '0 2px 4px rgba(0,0,0,0.1)');

    const showTooltip = (event: MouseEvent, d: ShippingMethodData & { isEmpty?: boolean }) => {
      if (d.isEmpty) return;
      tooltip
        .style('visibility', 'visible')
        .html(`
          <strong style="color: ${theme.palette.mode === 'dark' ? '#fff' : '#111'}">${d.name}</strong><br/>
          <span style="color: ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#666'}">
            ${t('shippingMethodsChart.timesUsed', 'Times Used')}: ${d.timesUsed.toLocaleString()}
          </span>
        `)
        .style('left', `${event.offsetX + 10}px`)
        .style('top', `${event.offsetY - 10}px`);
    };

    const hideTooltip = () => {
      tooltip.style('visibility', 'hidden');
    };

    // Add tooltip events to bars
    svg.selectAll('.bar')
      .on('mouseover', function(event, d) { 
        const data = d as ChartMethodData;
        if (data.isEmpty) return;
        d3.select(this)
          .attr('opacity', 1)
          .attr('fill', '#82b8ff');
        showTooltip(event, data); 
      })
      .on('mousemove', function(event, d) { showTooltip(event, d as ChartMethodData); })
      .on('mouseout', function(_event, d) { 
        const data = d as ChartMethodData;
        d3.select(this)
          .attr('opacity', data.isEmpty ? 0.1 : 0.9)
          .attr('fill', '#00A3FF');
        hideTooltip(); 
      });

    // Create text group for method names and counts
    svg.selectAll('.method-label')
      .data(defaultMethods)
      .join('g')
      .attr('class', 'method-label')
      .attr('transform', d => `translate(${x(d.name) || 0},${chartHeight + 10})`)
      .each(function(d) {
        const g = d3.select(this);
        if (!d.isEmpty) {
          // Method name
          g.append('text')
            .attr('y', 10)
            .attr('x', x.bandwidth() / 2)
            .attr('text-anchor', 'middle')
            .style('fill', theme.palette.mode === 'dark' ? '#ffffff' : '#666666')
            .style('font-size', '12px')
            .text(d.originalName);

          // Count
          g.append('text')
            .attr('y', 25)
            .attr('x', x.bandwidth() / 2)
            .attr('text-anchor', 'middle')
            .style('fill', theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#666666')
            .style('font-size', '12px')
            .text(`${d.timesUsed} ${t('shippingMethodsChart.orders', 'orders')}`);
        }
      });

    // Remove default x-axis text
    svg.select('.x-axis')
      .selectAll('text')
      .remove();

  }, [methods, height, t, theme.palette.mode]); // Add theme.palette.mode to dependencies

  return (
    <Card elevation={1} sx={{ mt: 2 }}>
      <CardContent>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        {!title && (
          <Typography variant="h6" gutterBottom>
            {t('shippingMethodsChart.title', 'Shipping Methods Usage')}
          </Typography>
        )}
        <Box 
          ref={chartRef} 
          sx={{ 
            height: height, 
            width: '100%',
            position: 'relative',
            '& .grid line': {
              stroke: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
            },
            '& .grid path': {
              stroke: 'none'
            }
          }}
        />
      </CardContent>
    </Card>
  );
}; 

