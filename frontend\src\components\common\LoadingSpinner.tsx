import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface LoadingSpinnerProps {
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ text }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const spinnerColor = theme.palette.mode === 'dark' ? 'white' : '#0284c7'; // Use white in dark mode, blue in light mode
  
  const displayText = text || t('common.loading', 'Loading...');

  return (
    <Box 
      className="flex flex-col items-center justify-center p-8" 
      sx={{ minHeight: 200 }}
    >
      <div 
        className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2" 
        style={{ borderColor: spinnerColor }}
      />
      <Typography 
        className="text-sm" 
        sx={{ 
          mt: 3, // Added more top margin (24px)
          mb: 1,
          color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'text.secondary'
        }}
      >
        {displayText}
      </Typography>
    </Box>
  );
};

export default LoadingSpinner; 

