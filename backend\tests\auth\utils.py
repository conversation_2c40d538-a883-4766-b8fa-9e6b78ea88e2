from __future__ import annotations

"""Utilidades de soporte para las pruebas de autenticación.

Incluye wrappers sencillos que convierten colecciones síncronas de `mongomock` en
interfaces asíncronas compatibles con Motor, suficientes para los métodos que
se utilizan dentro de las rutas y servicios de autenticación (find_one,
update_one, insert_one, count_documents, etc.).
"""

import types
from typing import Any, Callable


class AsyncMockCollection:
    """Wrapper que expone métodos de colección de manera asíncrona."""

    def __init__(self, collection):
        self._collection = collection

    def __getattr__(self, item: str) -> Any:  # pragma: no cover
        attr = getattr(self._collection, item)
        if callable(attr):
            async def _async_wrapper(*args, **kwargs):
                return attr(*args, **kwargs)
            return _async_wrapper
        return attr


class AsyncMockDatabase:
    """Wrapper mínimo para un objeto Database de mongomock con compatibilidad async."""

    def __init__(self, db):
        self._db = db

    def __getitem__(self, name: str) -> AsyncMockCollection:
        return AsyncMockCollection(self._db[name])

    async def command(self, *args, **kwargs):  # pragma: no cover
        """Simula comandos como 'ping'."""
        return {"ok": 1}

    async def list_collection_names(self):  # pragma: no cover
        return self._db.list_collection_names() 