// Log Errors Suppressor - Recharts Library Console Warnings Filter
// This file completely overrides Recharts internal warning system to suppress false positive warnings

interface RechartsLogUtils {
  warn?: (...args: unknown[]) => void;
  error?: (...args: unknown[]) => void;
}

interface RechartsModule {
  logUtils?: RechartsLogUtils;
}

/**
 * Override the logUtils from Recharts to suppress false positive warnings
 * This directly patches the library's warning system
 */
export const overrideRechartsLogUtils = () => {
  // Try to find and override logUtils if it exists in the global scope
  if (typeof window !== 'undefined') {
    // Store original console methods
    const originalWarn = console.warn;
    const originalError = console.error;
    const originalLog = console.log;

    // Create a comprehensive warning suppressor
    const createRechartsFilter = (originalMethod: (...args: unknown[]) => void) => {
      return (...args: unknown[]) => {
        const message = args.join(' ').toLowerCase();
        
        // Comprehensive pattern matching for Recharts warnings
        const isRechartsWarning = 
          // Core pattern
          (message.includes('width') && message.includes('height') && message.includes('chart')) ||
          // LogUtils specific
          message.includes('logutils') ||
          // ResponsiveContainer specific
          message.includes('responsivecontainer') ||
          // Specific error messages
          message.includes('should be greater than 0') ||
          message.includes('please check the style of container') ||
          message.includes('add a minwidth') ||
          message.includes('to control the height and width') ||
          // Pattern variations
          /the width\(\d*\).*height\(\d*\).*chart.*greater/i.test(message) ||
          /width\(\d*%?\).*height\(\d*%?\)/i.test(message) ||
          /minwidth.*minheight.*aspect/i.test(message);

        if (!isRechartsWarning) {
          originalMethod.apply(console, args);
        }
        // If it is a Recharts warning, do nothing (suppress it)
      };
    };

    // Override console methods globally
    console.warn = createRechartsFilter(originalWarn);
    console.error = createRechartsFilter(originalError);
    console.log = createRechartsFilter(originalLog);

    // Try to patch Recharts internal logUtils if accessible
    try {
      // Attempt to find recharts in window or modules
      const rechartsModule = (window as unknown as Record<string, unknown>)["Recharts"] as RechartsModule | undefined;
      if (rechartsModule && rechartsModule.logUtils) {
        const logUtils = rechartsModule.logUtils;
        if (logUtils.warn) logUtils.warn = () => {};
        if (logUtils.error) logUtils.error = () => {};
      }
    } catch (_error) {
      // Silently fail if we can't find recharts module
    }

    // Return cleanup function
    return () => {
      console.warn = originalWarn;
      console.error = originalError; 
      console.log = originalLog;
    };
  }

  return () => {}; // Return empty cleanup for server-side rendering
};

/**
 * Monkey patch approach - Override at import time
 * This attempts to patch the module when it's loaded
 */
export const patchRechartsModule = () => {
  try {
    // Dynamic import approach
    import('recharts').then((recharts) => {
      // Try to access internal logUtils and override it
      const rechartsAny = recharts as Record<string, unknown>;
      const logUtils = (rechartsAny as Record<string, unknown>)["logUtils"] as RechartsLogUtils | undefined;
      if (logUtils) {
        if ("warn" in logUtils) logUtils.warn = () => {};
        if ("error" in logUtils) logUtils.error = () => {};
      }
    }).catch(() => {
      // Silently fail if recharts can't be imported this way
    });
  } catch (_error) {
    // Silently fail
  }
};

// Auto-execute the override when this module is imported
if (typeof window !== 'undefined') {
  // Wait for DOM and imports to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      overrideRechartsLogUtils();
      patchRechartsModule();
    });
  } else {
    overrideRechartsLogUtils();
    patchRechartsModule();
  }
}

export default { overrideRechartsLogUtils, patchRechartsModule }; 