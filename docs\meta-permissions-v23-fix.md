# Meta API v23.0 Deprecated Permissions Fix

## Overview

This document describes the fix for deprecated Meta permissions that were showing as "Permiso Desconocido" (Unknown Permission) in the Meta permissions page. These permissions were deprecated in Meta API v23.0 and needed to be removed from the application.

## Problem

The following deprecated permissions were causing issues:
- `threads_business_basic` - Unknown permission
- `manage_app_solution` - Unknown permission  
- `pages_utility_messaging` - Unknown permission

These permissions were:
1. Stored in the database from previous API versions
2. Referenced in frontend type definitions
3. Included in translation files
4. Causing the Meta permissions page to show "Unknown Permission" errors

## Solution

### 1. Backend Changes

#### Updated Permission Filtering (`backend/services/meta_permissions.py`)
- Added `DEPRECATED_PERMISSIONS` constant listing all deprecated permissions
- Added `filter_deprecated_permissions()` function to filter out deprecated permissions
- Updated `get_store_permissions()` to automatically filter deprecated permissions

#### Updated Permission Metadata (`backend/routes/meta_permissions.py`)
- Expanded `PERMISSION_METADATA` to include all valid Meta API v23.0 permissions
- Added proper categorization (Core Facebook, Instagram, Business/Ads, WhatsApp)
- Removed any references to deprecated permissions

### 2. Frontend Changes

#### Removed Deprecated Permission References (`frontend/src/services/types.ts`)
- Removed `manage_app_solution_settings` from `PERMISSION_DESCRIPTIONS`

#### Updated Translation Files
- Removed deprecated permission translations from `frontend/public/locales/en/translation.json`
- Removed deprecated permission translations from `frontend/public/locales/es/translation.json`

### 3. Database Cleanup

#### Created Cleanup Script (`backend/scripts/admin/cleanup_deprecated_permissions.py`)
- Identifies all permission records with deprecated permissions
- Removes deprecated permissions from database records
- Adds cleanup history to permission change tracking
- Validates cleanup was successful

#### Created Runner Script (`run_permissions_cleanup.py`)
- Simple script to execute the cleanup from project root
- Provides clear feedback on cleanup progress and results

## Valid Permissions for Meta API v23.0

### Core Facebook Permissions
- `pages_show_list` - View list of connected pages
- `pages_read_engagement` - Access engagement metrics
- `pages_read_user_content` - Access user comments and interactions
- `pages_manage_metadata` - Manage page metadata
- `pages_messaging` - Send/receive page messages
- `public_profile` - Access basic profile info
- `email` - Access email address

### Instagram Permissions
- `instagram_basic` - Basic Instagram access
- `instagram_manage_insights` - Instagram analytics
- `instagram_manage_comments` - Manage Instagram comments
- `instagram_content_publish` - Publish Instagram content
- `instagram_branded_content_brand` - Branded content features

### Business and Ads Permissions
- `business_management` - Business account access
- `ads_read` - View advertising data
- `ads_management` - Manage advertising campaigns

### WhatsApp Business Permissions
- `whatsapp_business_messaging` - WhatsApp messaging
- `whatsapp_business_manage_events` - WhatsApp events

## How to Apply the Fix

### 1. Run the Cleanup Script

From the project root directory:

```bash
python run_permissions_cleanup.py
```

This will:
- Remove deprecated permissions from the database
- Add cleanup history to permission tracking
- Validate the cleanup was successful

### 2. Restart the Application

After running the cleanup, restart your application to ensure all changes take effect.

### 3. Verify the Fix

1. Navigate to the Meta permissions page (`https://localhost:5173/meta`)
2. Check that no "Permiso Desconocido" (Unknown Permission) errors appear
3. Verify that all displayed permissions are recognized and properly labeled

## Technical Details

### Automatic Filtering

The `get_store_permissions()` function now automatically filters out deprecated permissions, so even if they exist in the database, they won't be returned to the frontend.

### Graceful Degradation

The system gracefully handles:
- Existing database records with deprecated permissions
- Unknown permissions (logs warning but keeps for manual review)
- Missing permission records

### Change Tracking

All permission changes, including the cleanup process, are tracked in the `revocation_history` field for audit purposes.

## Testing

After applying the fix:

1. **Database Test**: Verify deprecated permissions are removed from `meta_permissions` collection
2. **API Test**: Call `/api/meta/permissions/{store_id}` and verify no deprecated permissions are returned
3. **UI Test**: Check Meta permissions page shows no unknown permissions
4. **Functionality Test**: Verify all Meta features still work correctly

## Rollback Plan

If issues occur, you can:

1. **Database Rollback**: Restore from backup if needed (cleanup script tracks changes)
2. **Code Rollback**: Revert the code changes and redeploy
3. **Manual Fix**: Use the permission change tracking to identify what was changed

## Future Maintenance

- Monitor Meta API changelog for new deprecated permissions
- Update `DEPRECATED_PERMISSIONS` constant when new deprecations are announced
- Run cleanup script after major Meta API version updates
