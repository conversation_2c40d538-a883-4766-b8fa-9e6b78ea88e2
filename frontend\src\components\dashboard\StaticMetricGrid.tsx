import React, { useRef, useState } from 'react';
import { Grid, useTheme, useMediaQuery } from '@mui/material';
import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';
import { MetricCard } from './MetricCard';
import SummaryCard from './SummaryCard';
import RecommendationCard from './RecommendationCard';
import KeyDatesCard from './KeyDatesCard';
import SocialMediaCard from './SocialMediaCard';
import KeywordsCard from './KeywordsCard';
import DetailedMetricsCard from './DetailedMetricsCard';
import { StoreAnalysis } from '../../services/storeService';
import type { Analysis } from '../../services/storeService';

interface KeyDates {
  last_order_date?: string;
  last_visit_date?: string;
  last_customer_registration_date?: string;
  last_product_added_date?: string;
}

// Define the structure of a metric card configuration
export interface MetricCardConfig {
  id: string;
  type: 'metric';
  title: string;
  value: string | number;
  trend?: 'up' | 'down' | 'neutral';
  trendPercentage?: number;
  trendLabel?: string;
  chartData?: Array<{ value: number }>;
  tooltip?: string;
  extra?: { currency?: string };
}

// Step 1.1-1.2: Add SummaryCardConfig and RecommendationCardConfig
type SummaryCardConfig = {
  id: 'summary_card';
  type: 'summary';
  title: string;
  analysisData: Analysis | null | undefined;
};

type RecommendationCardConfig = {
  id: 'ai_recommendations_card';
  type: 'recommendation';
  title: string;
  content: string | null;
};

// Step 1.4: Union type for all dashboard items
export type DashboardItemConfig = MetricCardConfig | SummaryCardConfig | RecommendationCardConfig | KeyDatesCardConfig | SocialMediaCardConfig | KeywordsCardConfig | DetailedMetricsCardConfig;

// --- ADD NEW CONFIG TYPES ---
type KeyDatesCardConfig = {
  id: 'key_dates_card';
  type: 'key_dates';
  title: string;
  dates?: KeyDates;
};

type SocialMediaCardConfig = {
  id: 'social_media_card';
  type: 'social_media';
  title: string;
  socialMedia?: StoreAnalysis['social_media'];
  analysisData: Analysis | null | undefined;
};

type KeywordsCardConfig = {
  id: 'keywords_card';
  type: 'keywords';
  title: string;
  keywords?: string[];
};

// Define DetailedMetricsCardConfig
type DetailedMetricsCardConfig = {
  id: 'detailed_metrics_card';
  type: 'detailed_metrics';
  title: string;
  content: string | null;
};
// --- END ADD NEW CONFIG TYPES ---

interface StaticDashboardGridProps {
  items: DashboardItemConfig[];
}

export const StaticMetricGrid: React.FC<StaticDashboardGridProps> = ({
  items,
}) => {
  const theme = useTheme();
  // Disable dragging on screens md and below (tablet & phone)
  const dragDisabled = useMediaQuery(theme.breakpoints.down('md'));
  
  // Create refs for each draggable item
  const nodeRefs = useRef(items.map(() => React.createRef<HTMLDivElement>()));
  
  // Track the position of each draggable item
  const [positions, setPositions] = useState(items.map(() => ({ x: 0, y: 0 })));
  
  // Handle the stop event to SAVE the new position
  const handleDragStop = (index: number, _e: DraggableEvent, data: DraggableData) => {
    const newPositions = [...positions];
    // Save the final position after dragging
    newPositions[index] = { x: data.x, y: data.y }; 
    setPositions(newPositions);
  };
  
  return (
    <Grid container spacing={3}>
      {items.map((item, index) => {
        // Get the ref for this item
        const nodeRef = nodeRefs.current[index];
        
        return (
          <Grid
            item
            key={item.id}
            xs={12}
            sm={item.type === 'metric' ? 6 : (item.type === 'key_dates' || item.type === 'social_media' || item.type === 'keywords') ? 6 : 12}
            md={item.type === 'metric' ? 3 : item.type === 'detailed_metrics' ? 12 : item.type === 'social_media' ? 6 : (item.type === 'key_dates' || item.type === 'keywords') ? 6 : 12}
            sx={{ '& > * > * ': { cursor: dragDisabled ? 'default' : 'grab' } }}
          >
            <React.Fragment>
              {item.type === 'metric' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <MetricCard
                      title={item.title}
                      value={item.value}
                      trend={item.trend}
                      trendPercentage={item.trendPercentage}
                      trendLabel={item.trendLabel}
                      chartData={item.chartData}
                      tooltip={item.tooltip}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'summary' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <SummaryCard
                      title={item.title}
                      analysisData={item.analysisData}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'recommendation' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <RecommendationCard
                      title={item.title}
                      content={item.content}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'key_dates' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <KeyDatesCard
                      title={item.title}
                      dates={item.dates}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'social_media' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <SocialMediaCard
                      title={item.title}
                      socialMedia={item.socialMedia}
                      analysisData={item.analysisData}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'keywords' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <KeywordsCard
                      title={item.title}
                      keywords={item.keywords}
                    />
                  </div>
                </Draggable>
              )}
              {item.type === 'detailed_metrics' && (
                <Draggable 
                  nodeRef={nodeRef} 
                  position={positions[index]}
                  onStop={(e, data) => handleDragStop(index, e, data)}
                  cancel=".no-drag"
                  disabled={dragDisabled}
                >
                  <div ref={nodeRef} className="draggable-card">
                    <DetailedMetricsCard
                      title={item.title}
                      content={item.content}
                    />
                  </div>
                </Draggable>
              )}
            </React.Fragment>
          </Grid>
        );
      })}
    </Grid>
  );
};

export default StaticMetricGrid; 