#!/usr/bin/env python3
"""
Simple test script to verify Brave Search API is working
"""

import os
import requests
import json
import re

# Try to get Brave Search API key from environment
brave_api_key = os.environ.get('BRAVE_SEARCH_API_KEY')

if not brave_api_key:
    print("ERROR: BRAVE_SEARCH_API_KEY environment variable not found")
    print("Please run: export BRAVE_SEARCH_API_KEY='your_api_key'")
    exit(1)

print(f"Brave Search API Key found: {brave_api_key[:10]}...")

# Test search query for optical stores in Uruguay
query = "optical stores Uruguay Montevideo"
print(f"\nTesting search query: '{query}'")

# Make API request
headers = {
    "Accept": "application/json",
    "X-Subscription-Token": brave_api_key
}

params = {
    "q": query,
    "count": 10,
    "freshness": "py"  # Past year
}

url = "https://api.search.brave.com/res/v1/web/search"

try:
    response = requests.get(url, headers=headers, params=params, timeout=30)
    print(f"\nAPI Response Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"\nNumber of results: {len(data.get('web', {}).get('results', []))}")
        
        print("\n=== SEARCH RESULTS ===")
        real_competitors = set()
        
        if "web" in data and "results" in data["web"]:
            for i, result in enumerate(data["web"]["results"][:10], 1):
                title = result.get("title", "No title")
                url = result.get("url", "No URL")
                description = result.get("description", "No description")
                
                # Extract domain for brand identification
                domain = url.split('/')[2] if '/' in url and len(url.split('/')) > 2 else url
                
                print(f"\nResult {i}:")
                print(f"  Title: {title}")
                print(f"  Domain: {domain}")
                print(f"  URL: {url}")
                print(f"  Description: {description[:200]}..." if len(description) > 200 else f"  Description: {description}")
                
                # Extract competitor names
                # From title
                title_match = re.search(r'^([A-Z][a-zA-Z0-9\s&\'-]+?)\s*(?:-|\||–|\.|,)', title)
                if title_match:
                    name = title_match.group(1).strip()
                    if len(name) > 2 and name.lower() not in ['the', 'best', 'top', 'online', 'optical', 'store', 'shop']:
                        real_competitors.add(name)
                
                # From Uruguayan domains
                if '.com.uy' in url or '.uy' in url:
                    domain_match = re.search(r'https?://(?:www\.)?([a-zA-Z0-9\-]+)\.(?:com\.)?uy', url)
                    if domain_match:
                        domain_name = domain_match.group(1).replace('-', ' ').title()
                        if len(domain_name) > 2:
                            real_competitors.add(domain_name)
                
                # Look for known optical chains
                text_content = f"{title} {description}".lower()
                known_chains = ['grandvision', 'optica americana', 'óptica florida', 'mytho eyewear', 'opticentro', 'vision center']
                for chain in known_chains:
                    if chain in text_content:
                        real_competitors.add(chain.title())
        
        print("\n\n=== REAL COMPETITORS EXTRACTED ===")
        print("These are actual business names found in the search results:")
        for name in sorted(real_competitors):
            print(f"  ✓ {name}")
        
        print(f"\nTotal real competitors found: {len(real_competitors)}")
        
        # Test a different query
        print("\n\n=== TESTING SECOND QUERY ===")
        query2 = "tiendas de lentes Uruguay ópticas"
        print(f"Testing query: '{query2}'")
        
        params2 = {
            "q": query2,
            "count": 5,
            "freshness": "py"
        }
        
        response2 = requests.get(url, headers=headers, params=params2, timeout=30)
        if response2.status_code == 200:
            data2 = response2.json()
            print(f"Results found: {len(data2.get('web', {}).get('results', []))}")
            
            for i, result in enumerate(data2.get('web', {}).get('results', [])[:3], 1):
                print(f"  {i}. {result.get('title', 'No title')}")
                print(f"     {result.get('url', 'No URL')}")
        
    else:
        print(f"\nError response ({response.status_code}): {response.text}")
        
except Exception as e:
    print(f"\nError making request: {str(e)}")
    import traceback
    traceback.print_exc()