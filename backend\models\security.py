from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Union
from pydantic import BaseModel, Field
from enum import Enum

class SecurityEventType(str, Enum):
    RATE_LIMIT_VIOLATION = "rate_limit_violation"
    VALIDATION_FAILURE = "validation_failure"
    COST_LIMIT_EXCEEDED = "cost_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    AUTHENTICATION_FAILURE = "authentication_failure"
    AUTHORIZATION_FAILURE = "authorization_failure"
    MALICIOUS_REQUEST = "malicious_request"

class ThreatLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SecurityEvent(BaseModel):
    event_type: SecurityEventType
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    user_id: Optional[str] = None
    store_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    threat_level: ThreatLevel = ThreatLevel.LOW
    details: Dict[str, Any] = Field(default_factory=dict)
    resolved: bool = False
    trace_id: Optional[str] = None

class RateLimitResult(BaseModel):
    allowed: bool
    current_count: int
    limit: int
    window_seconds: int
    reset_time: datetime
    retry_after: Optional[int] = None
    rate_limit_key: str

class ValidationError(BaseModel):
    field: str
    message: str
    value: Any
    rule: str

class ValidationResult(BaseModel):
    valid: bool
    errors: List[ValidationError] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    sanitized_data: Optional[Dict[str, Any]] = None

class CostCheckResult(BaseModel):
    allowed: bool
    current_daily_cost: float
    current_monthly_cost: float
    daily_limit: float
    monthly_limit: float
    estimated_request_cost: float
    remaining_daily_budget: float
    remaining_monthly_budget: float

class SecurityViolation(BaseModel):
    violation_type: str
    severity: ThreatLevel
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    user_id: Optional[str] = None
    store_id: Optional[str] = None
    ip_address: Optional[str] = None
    endpoint: str
    details: Dict[str, Any] = Field(default_factory=dict)
    action_taken: Optional[str] = None

class ThreatAlert(BaseModel):
    alert_id: str
    threat_type: str
    severity: ThreatLevel
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    source_ip: Optional[str] = None
    user_id: Optional[str] = None
    description: str
    recommended_action: str
    auto_resolved: bool = False

class BudgetAlert(BaseModel):
    alert_id: str
    store_id: str
    alert_type: str  # daily_threshold, monthly_threshold, spike_detected
    current_amount: float
    threshold: float
    percentage_used: float
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    escalated: bool = False

class RateLimit(BaseModel):
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_allowance: int = 10
    window_size: int = 60  # seconds
    cost_weight: float = 1.0
    external_api_calls: bool = False

class ValidationRules(BaseModel):
    max_request_size: str = "10MB"
    allowed_content_types: List[str] = Field(default_factory=lambda: ["application/json", "multipart/form-data"])
    required_headers: List[str] = Field(default_factory=lambda: ["User-Agent"])
    max_message_length: Optional[int] = None
    profanity_filter: bool = False
    rate_limit_on_failure: bool = True
    suspicious_pattern_detection: bool = True

class CostLimits(BaseModel):
    daily_limit_usd: float = 50.0
    monthly_limit_usd: float = 1000.0
    alert_threshold: float = 0.8  # Alert at 80% of limit
    openai_daily_limit: float = 20.0
    meta_api_daily_limit: float = 15.0
    emergency_stop_threshold: float = 0.95  # Emergency stop at 95%

class SecurityConfig(BaseModel):
    enabled: bool = True
    rate_limiting_enabled: bool = True
    validation_enabled: bool = True
    cost_control_enabled: bool = True
    security_headers_enabled: bool = True
    monitoring_enabled: bool = True
    threat_detection_enabled: bool = True
    
    # Global rate limits by user type
    anonymous_rate_limit: RateLimit = Field(default_factory=lambda: RateLimit(
        requests_per_minute=10, requests_per_hour=100, requests_per_day=500
    ))
    authenticated_rate_limit: RateLimit = Field(default_factory=lambda: RateLimit(
        requests_per_minute=60, requests_per_hour=1000, requests_per_day=10000
    ))
    admin_rate_limit: RateLimit = Field(default_factory=lambda: RateLimit(
        requests_per_minute=200, requests_per_hour=5000, requests_per_day=50000
    ))
    
    # Global validation rules
    global_validation: ValidationRules = Field(default_factory=ValidationRules)
    
    # Global cost limits
    global_cost_limits: CostLimits = Field(default_factory=CostLimits)
    
    # Endpoint-specific overrides
    endpoint_rate_limits: Dict[str, RateLimit] = Field(default_factory=dict)
    endpoint_validation_rules: Dict[str, ValidationRules] = Field(default_factory=dict)
    endpoint_cost_limits: Dict[str, CostLimits] = Field(default_factory=dict)

class OptimizationSuggestion(BaseModel):
    suggestion_type: str
    title: str
    description: str
    potential_savings_usd: float
    implementation_effort: str  # low, medium, high
    priority: str  # low, medium, high
    details: Dict[str, Any] = Field(default_factory=dict)

class CostSummary(BaseModel):
    store_id: str
    period_start: datetime
    period_end: datetime
    total_cost_usd: float
    openai_cost_usd: float
    meta_api_cost_usd: float
    other_costs_usd: float
    request_count: int
    cost_per_request: float
    trends: Dict[str, float] = Field(default_factory=dict)

class CostPrediction(BaseModel):
    store_id: str
    predicted_monthly_cost: float
    confidence_level: float
    factors: List[str] = Field(default_factory=list)
    recommendations: List[OptimizationSuggestion] = Field(default_factory=list)

class SecurityReport(BaseModel):
    report_id: str
    period_start: datetime
    period_end: datetime
    total_requests: int
    blocked_requests: int
    security_events: List[SecurityEvent] = Field(default_factory=list)
    top_threats: List[ThreatAlert] = Field(default_factory=list)
    cost_summary: Optional[CostSummary] = None
    recommendations: List[str] = Field(default_factory=list) 