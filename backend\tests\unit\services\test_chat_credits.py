import pytest
from fastapi.testclient import Test<PERSON>lient

from backend.application import app  # assuming FastAPI app is exposed here
from config.database import db_analysis

client = TestClient(app)

STORE_ID = "test_store_credits"

@pytest.fixture(autouse=True, scope="module")
def _setup_test_store(event_loop):
    """Create a temporary store doc with abundant credits for tests."""
    async def _create():
        await db_analysis["active_stores_cache"].insert_one({
            "_id": STORE_ID,
            "name": "Credits Test Store",
            "credits": 50_000
        })
        yield
        await db_analysis["active_stores_cache"].delete_one({"_id": STORE_ID})

    event_loop.run_until_complete(_create())


def test_insufficient_credits_returns_402(monkeypatch):
    # Force deduction amount high by mocking mode to deepsearch
    response = client.post(f"/api/chat/{STORE_ID}", data={"message": "hello", "mode": "deepsearch"})
    assert response.status_code == 402


def test_credits_deducted_on_success(monkeypatch):
    # Top up credits first
    db_analysis["active_stores_cache"].update_one({"_id": STORE_ID}, {"$set": {"credits": 50_000}})

    # Mock OpenAI & cost checks if necessary – here assume route succeeds with simple request
    response = client.post(f"/api/chat/{STORE_ID}", data={"message": "hello"})
    assert response.status_code == 200

    # Check credits reduced by 10_000
    doc = db_analysis["active_stores_cache"].find_one({"_id": STORE_ID})
    assert doc["credits"] == 40_000 