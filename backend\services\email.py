import logging
import random
import string
import smtplib
import asyncio
import threading
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config.settings import get_settings
import logging
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger("d-unit")

# Get settings
settings = get_settings()

# Create a connection pool for SMTP
class SMTPConnectionPool:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SMTPConnectionPool, cls).__new__(cls)
                cls._instance.initialize()
            return cls._instance
    
    def initialize(self):
        """Initialize the connection pool attributes"""
        self.pool = []
        self.max_connections = 3
        self.in_use = set()
    
    def get_connection(self):
        """Get an available SMTP connection from the pool or create a new one"""
        with self._lock:
            # Try to find an existing connection
            for conn in self.pool:
                if conn not in self.in_use:
                    try:
                        # Check if connection is still alive
                        conn.noop()
                        self.in_use.add(conn)
                        return conn
                    except:
                        # Connection is stale, remove it
                        self.pool.remove(conn)
            
            # Create a new connection if pool is not full
            if len(self.pool) < self.max_connections:
                try:
                    conn = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
                    conn.starttls()
                    conn.login(settings.EMAIL_USERNAME, settings.EMAIL_APP_PASSWORD)
                    self.pool.append(conn)
                    self.in_use.add(conn)
                    return conn
                except Exception as e:
                    logger.error(f"Failed to create SMTP connection: {str(e)}")
                    return None
            
            # Pool is full and all connections are in use
            return None
    
    def release_connection(self, conn):
        """Return a connection to the pool"""
        with self._lock:
            if conn in self.in_use:
                self.in_use.remove(conn)
    
    def close_all(self):
        """Close all connections in the pool"""
        with self._lock:
            for conn in self.pool:
                try:
                    conn.quit()
                except:
                    pass
            self.pool = []
            self.in_use = set()

# Initialize the connection pool
smtp_pool = SMTPConnectionPool()

# Log initial pool identity for diagnostics
logger.info(f"SMTP pool initialized id={id(smtp_pool)}")

# Helper to ensure pool remains healthy

def ensure_smtp_pool_health():
    """Recreate or refresh smtp_pool if connections are stale or pool missing."""
    global smtp_pool
    try:
        # If pool was set to None somehow, recreate
        if smtp_pool is None:
            smtp_pool = SMTPConnectionPool()
            logger.warning(f"smtp_pool was None – recreated new pool id={id(smtp_pool)}")
            return

        # Remove stale connections
        stale = []
        for conn in list(smtp_pool.pool):
            try:
                conn.noop()
            except Exception:
                stale.append(conn)

        for conn in stale:
            try:
                conn.quit()
            except Exception:
                pass
            if conn in smtp_pool.pool:
                smtp_pool.pool.remove(conn)

        # If no healthy connections remain, rebuild pool
        if not smtp_pool.pool:
            smtp_pool.close_all()
            smtp_pool = SMTPConnectionPool()
            logger.warning(f"Rebuilt smtp_pool due to all stale connections id={id(smtp_pool)}")
    except Exception as e:
        logger.error(f"Error ensuring smtp pool health: {e}")

def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))

def generate_random_password():
    """Generate a random 12-character password"""
    characters = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(random.choice(characters) for _ in range(12))

# ---------------- Main email send with pool + fallback -------------------

def send_verification_email(email: str, code: str):
    """Send verification code via email - synchronous version with connection pooling"""
    # If pool object vanished, recreate or just fallback
    if smtp_pool is None:
        logger.warning("smtp_pool is None, falling back to direct SMTP connection")
        return send_verification_email_fallback(email, code)

    conn = None
    try:
        # Try to obtain connection from pool
        conn = smtp_pool.get_connection()
        if not conn:
            logger.warning("No SMTP connection available in pool – using fallback")
            return send_verification_email_fallback(email, code)
        
        msg = MIMEMultipart()
        msg['From'] = settings.EMAIL_USERNAME
        msg['To'] = email
        msg['Subject'] = "Your D-Unit Verification Code"
        
        body = f"""
        Your verification code is: {code}
        
        This code will expire in 10 minutes.
        If you didn't request this code, please ignore this email.
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Attempt to send via pooled connection
        try:
            conn.send_message(msg)
            logger.info(f"Verification email sent to {email} using connection pool id={id(conn)} pool_id={id(smtp_pool)}")
            return True
        except Exception as send_err:
            logger.error(f"Error sending verification email via pool (conn_id={id(conn)} pool_id={id(smtp_pool)}): {send_err}")
            # Release the faulty connection so it can be cleaned up
            try:
                smtp_pool.release_connection(conn)
            except Exception:
                pass

            # Fallback to direct SMTP send
            return send_verification_email_fallback(email, code)
    except Exception as e:
        logger.error(f"Unexpected error preparing verification email: {str(e)}")
        return False
    finally:
        # Release the connection back to the pool
        if conn:
            try:
                smtp_pool.release_connection(conn)
            except Exception:
                pass

def send_verification_email_fallback(email: str, code: str):
    """Fallback method for sending verification code via email"""
    try:
        msg = MIMEMultipart()
        msg['From'] = settings.EMAIL_USERNAME
        msg['To'] = email
        msg['Subject'] = "Your D-Unit Verification Code"
        
        body = f"""
        Your verification code is: {code}
        
        This code will expire in 10 minutes.
        If you didn't request this code, please ignore this email.
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        server = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
        server.starttls()
        server.login(settings.EMAIL_USERNAME, settings.EMAIL_APP_PASSWORD)
        server.send_message(msg)
        server.quit()
        
        logger.info(f"Verification email sent to {email} using fallback method")
        return True
    except Exception as e:
        logger.error(f"Error sending verification email (fallback): {str(e)}")
        return False

# Start a background thread to send emails
def send_email_in_background(email: str, code: str):
    """Send email in a background thread to avoid blocking the main thread"""
    thread = threading.Thread(target=send_verification_email, args=(email, code))
    thread.daemon = True
    thread.start()
    return True

# Optional: For async environments
async def send_verification_email_async(email: str, code: str):
    """Send verification code via email asynchronously"""
    loop = asyncio.get_running_loop()
    # Use ThreadPoolExecutor to run the synchronous email sending in a separate thread
    return await loop.run_in_executor(None, send_verification_email, email, code)

def send_password_reset_email(email: str, new_password: str):
    """Send the new password via email"""
    try:
        msg = MIMEMultipart()
        msg['From'] = settings.EMAIL_USERNAME
        msg['To'] = email
        msg['Subject'] = "Your D-Unit Password Reset"
        
        body = f"""
        Your D-Unit password has been reset.
        
        Your new password is: {new_password}
        
        Please login with this password and change it immediately for security purposes.
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        server = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
        server.starttls()
        server.login(settings.EMAIL_USERNAME, settings.EMAIL_APP_PASSWORD)
        server.send_message(msg)
        server.quit()
        
        logger.info(f"Password reset email sent to {email}")
        return True
    except Exception as e:
        logger.error(f"Error sending password reset email: {str(e)}")
        return False

