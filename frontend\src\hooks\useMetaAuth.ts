import { useState, useEffect, useCallback } from 'react';import { isMetaAuthReady, META_AUTH_STATE_CHANGE } from '../services/authChecker';import { MetaAuthService } from '../services/auth';import { logger } from '../utils/logger';

/**
 * Custom hook for Meta authentication status
 * 
 * Provides:
 * - Current authentication status
 * - Method to check authentication
 * - Method to trigger login
 * - Listening for auth state changes
 */
export const useMetaAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Check authentication status
  const checkAuthStatus = useCallback(() => {
    const status = isMetaAuthReady();
    setIsAuthenticated(status);
    return status;
  }, []);

  // Handle Meta login
  const login = useCallback(async () => {
    setIsCheckingAuth(true);
    setError(null);
    
    try {
      const result = await MetaAuthService.login('facebook');
      setIsAuthenticated(result !== null);
      return result;
    } catch (err) {
      logger.error('Meta login error:', err);
      setError(typeof err === 'string' ? err : 'Failed to login with Meta');
      return null;
    } finally {
      setIsCheckingAuth(false);
    }
  }, []);

  // Listen for auth state changes
  useEffect(() => {
    // Initial check
    checkAuthStatus();

    // Set up listener for auth state changes
    const handleAuthChange = () => {
      checkAuthStatus();
    };

    document.addEventListener(META_AUTH_STATE_CHANGE, handleAuthChange);
    
    return () => {
      document.removeEventListener(META_AUTH_STATE_CHANGE, handleAuthChange);
    };
  }, [checkAuthStatus]);

  return {
    isAuthenticated,
    isCheckingAuth,
    error,
    checkAuthStatus,
    login
  };
};

export default useMetaAuth; 