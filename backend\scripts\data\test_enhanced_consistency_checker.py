#!/usr/bin/env python3
"""
Test script for the enhanced data consistency checker
This script runs both the original and enhanced versions to compare results
"""

import os
import sys
import subprocess
import json
from datetime import datetime

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)


def run_consistency_check(script_name: str, quick_mode: bool = True) -> dict:
    """Run a consistency checker script and capture results"""
    print(f"\nRunning {script_name}...")
    
    cmd = ['python', script_name]
    if quick_mode:
        cmd.append('--quick')
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(__file__)
        )
        
        print(f"Exit code: {result.returncode}")
        
        # Find the most recent report file
        report_files = [f for f in os.listdir('.') if f.startswith('consistency_report') and f.endswith('.json')]
        report_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        if report_files:
            with open(report_files[0], 'r') as f:
                report_data = json.load(f)
            return {
                'exit_code': result.returncode,
                'report': report_data,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
        else:
            return {
                'exit_code': result.returncode,
                'report': None,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
    except Exception as e:
        print(f"Error running {script_name}: {e}")
        return {
            'exit_code': -1,
            'error': str(e)
        }


def compare_results(original_result: dict, enhanced_result: dict):
    """Compare results from both consistency checkers"""
    print("\n" + "="*70)
    print("COMPARISON OF CONSISTENCY CHECKERS")
    print("="*70)
    
    # Compare exit codes
    print(f"\nExit Codes:")
    print(f"  Original: {original_result['exit_code']}")
    print(f"  Enhanced: {enhanced_result['exit_code']}")
    
    if original_result['report'] and enhanced_result['report']:
        orig_summary = original_result['report'].get('summary', {})
        enh_summary = enhanced_result['report'].get('summary', {})
        
        print(f"\nStores Checked:")
        print(f"  Original: {orig_summary.get('stores_checked', 0)}")
        print(f"  Enhanced: {enh_summary.get('stores_checked', 0)}")
        
        print(f"\nCritical Issues:")
        print(f"  Original: {orig_summary.get('critical_issues', 0)}")
        print(f"  Enhanced: {enh_summary.get('critical_issues', 0)}")
        
        print(f"\nWarnings:")
        print(f"  Original: {orig_summary.get('warnings', 0)}")
        print(f"  Enhanced: {enh_summary.get('warnings', 0)}")
        
        # Enhanced-only metrics
        print(f"\nEnhanced-Only Metrics:")
        print(f"  Revenue Consistency Issues: {enh_summary.get('revenue_consistency_issues', 0)}")
        print(f"  Store Filtering Issues: {enh_summary.get('store_filtering_issues', 0)}")
        print(f"  Pipeline Integrity Issues: {enh_summary.get('pipeline_integrity_issues', 0)}")
        print(f"  AI Quality Issues: {enh_summary.get('ai_quality_issues', 0)}")
        print(f"  Meta Alignment Issues: {enh_summary.get('meta_alignment_issues', 0)}")
        
        # Health score (enhanced only)
        if 'health_score' in enhanced_result['report']:
            print(f"  Overall Health Score: {enhanced_result['report']['health_score']:.1f}%")
        
        # Compare issue types
        orig_issues = original_result['report'].get('critical_issues', [])
        enh_issues = enhanced_result['report'].get('critical_issues', [])
        
        orig_issue_types = set(issue.get('type') for issue in orig_issues)
        enh_issue_types = set(issue.get('type') for issue in enh_issues)
        
        new_issue_types = enh_issue_types - orig_issue_types
        if new_issue_types:
            print(f"\nNew Issue Types Detected by Enhanced Checker:")
            for issue_type in sorted(new_issue_types):
                print(f"  - {issue_type}")
        
        # Recommendations comparison
        orig_recs = original_result['report'].get('recommendations', [])
        enh_recs = enhanced_result['report'].get('recommendations', [])
        
        print(f"\nRecommendations:")
        print(f"  Original: {len(orig_recs)} recommendations")
        print(f"  Enhanced: {len(enh_recs)} recommendations")
        
        if enh_recs and len(enh_recs) > len(orig_recs):
            print(f"\nAdditional Enhanced Recommendations:")
            for i, rec in enumerate(enh_recs[len(orig_recs):], 1):
                print(f"  {i}. {rec}")


def main():
    print("D-Unit Enhanced Data Consistency Checker Test")
    print("This script compares the original and enhanced consistency checkers")
    
    # Check if both scripts exist
    original_script = 'data_consistency_checker.py'
    enhanced_script = 'data_consistency_checker_enhanced.py'
    
    if not os.path.exists(original_script):
        print(f"Error: {original_script} not found")
        return
        
    if not os.path.exists(enhanced_script):
        print(f"Error: {enhanced_script} not found")
        return
    
    # Run both checkers in quick mode
    print("\nRunning consistency checks in quick mode (10% sample)...")
    
    original_result = run_consistency_check(original_script, quick_mode=True)
    enhanced_result = run_consistency_check(enhanced_script, quick_mode=True)
    
    # Compare results
    compare_results(original_result, enhanced_result)
    
    # Show specific examples of new checks
    if enhanced_result['report']:
        print("\n" + "="*70)
        print("EXAMPLES OF ENHANCED CHECKS")
        print("="*70)
        
        # Pipeline status
        pipeline_status = enhanced_result['report'].get('pipeline_status', {})
        if pipeline_status:
            print("\nPipeline Execution Status:")
            for script, info in list(pipeline_status.items())[:5]:
                status = info.get('status', 'unknown')
                last_run = info.get('last_run', 'never')
                print(f"  {script}: {status} (last run: {last_run})")
        
        # Issue patterns
        issue_patterns = enhanced_result['report'].get('issue_patterns', {})
        if issue_patterns:
            print("\nIssue Pattern Analysis:")
            for issue_type, count in sorted(issue_patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  {issue_type}: {count} occurrences")
    
    print("\n" + "="*70)
    print("Test completed. Check the generated report files for full details.")
    print("="*70)


if __name__ == "__main__":
    main()