#!/usr/bin/env python3
"""
data_consistency_checker.py - Validates data consistency across MongoDB collections

This script ensures data integrity between:
- active_stores_cache
- product_details_cache
- store_customers_cache
- global_analysis

Run this AFTER update scripts but BEFORE market_analyzer.py
"""

import os
import sys
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Tuple, Optional, Any
from pymongo import MongoClient
import json
from collections import defaultdict
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

try:
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    print(f"FATAL: Error importing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_consistency_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MONGO_CONNECTION_STRING = settings.MONGODB_CONNECTION
MONGO_DB_NAME = settings.MONGODB_ANALYSIS_DB
MAX_AGE_HOURS = 24  # Consider data stale if older than this
CRITICAL_DISCREPANCY_THRESHOLD = 0.05  # 5% difference is critical
WARNING_DISCREPANCY_THRESHOLD = 0.01  # 1% difference is warning

# Email settings (optional - set in environment variables)
SMTP_HOST = os.getenv('SMTP_HOST', '')
SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
SMTP_USER = os.getenv('SMTP_USER', '')
SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
ALERT_EMAIL_TO = os.getenv('ALERT_EMAIL_TO', '')


class DataConsistencyChecker:
    def __init__(self):
        """Initialize the consistency checker"""
        self.client = None
        self.db = None
        self.issues = {
            'critical': [],
            'warning': [],
            'info': []
        }
        self.stats = {
            'stores_checked': 0,
            'stores_with_issues': 0,
            'critical_issues': 0,
            'warnings': 0,
            'data_age_issues': 0
        }
        self.version_info = {}
        
    def connect(self):
        """Establish MongoDB connection"""
        try:
            self.client = MongoClient(MONGO_CONNECTION_STRING)
            self.db = self.client[MONGO_DB_NAME]
            logger.info(f"Connected to MongoDB database: {MONGO_DB_NAME}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            return False
    
    def check_collection_timestamps(self) -> Dict[str, Dict]:
        """Check last update timestamps for all collections"""
        collections = [
            'active_stores_cache',
            'product_details_cache',
            'store_customers_cache',
            'global_analysis'
        ]
        
        timestamps = {}
        current_time = datetime.now(timezone.utc)
        
        if self.client is None or self.db is None:
            logger.error("Database connection not available for freshness check")
            return timestamps
        
        for collection_name in collections:
            try:
                collection = self.db[collection_name]
                
                # Get the most recent update timestamp
                latest_doc = collection.find_one(
                    {},
                    sort=[("last_updated_script", -1), ("overall_last_updated", -1), ("last_updated", -1)]
                )
                
                if latest_doc:
                    # Try different timestamp fields
                    timestamp = None
                    for field in ['last_updated_script', 'overall_last_updated', 'last_updated', 'metadata.last_updated']:
                        if '.' in field:
                            parts = field.split('.')
                            value = latest_doc
                            for part in parts:
                                value = value.get(part, {}) if isinstance(value, dict) else None
                                if value is None:
                                    break
                            if value:
                                timestamp = value
                                break
                        elif field in latest_doc:
                            timestamp = latest_doc[field]
                            break
                    
                    if timestamp:
                        if isinstance(timestamp, str):
                            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        elif isinstance(timestamp, datetime) and timestamp.tzinfo is None:
                            timestamp = timestamp.replace(tzinfo=timezone.utc)
                        
                        age_hours = (current_time - timestamp).total_seconds() / 3600
                        
                        timestamps[collection_name] = {
                            'last_updated': timestamp,
                            'age_hours': round(age_hours, 2),
                            'is_stale': age_hours > MAX_AGE_HOURS
                        }
                        
                        if age_hours > MAX_AGE_HOURS:
                            self.stats['data_age_issues'] += 1
                            self.issues['warning'].append({
                                'type': 'stale_data',
                                'collection': collection_name,
                                'age_hours': round(age_hours, 2),
                                'last_updated': timestamp.isoformat()
                            })
                    else:
                        timestamps[collection_name] = {
                            'last_updated': None,
                            'age_hours': None,
                            'is_stale': True
                        }
                        self.issues['warning'].append({
                            'type': 'no_timestamp',
                            'collection': collection_name
                        })
                
                # Count documents
                doc_count = collection.count_documents({})
                timestamps[collection_name]['document_count'] = doc_count
                
            except Exception as e:
                logger.error(f"Error checking timestamps for {collection_name}: {str(e)}")
                timestamps[collection_name] = {'error': str(e)}
        
        self.version_info = timestamps
        return timestamps
    
    def check_store_consistency(self, store_id: str) -> Dict[str, Any]:
        """Check consistency for a single store across all collections"""
        store_issues = []
        
        if self.client is None or self.db is None:
            return {
                'store_id': store_id,
                'issues': [{'severity': 'critical', 'type': 'connection_error', 'message': 'Database connection not available'}]
            }
        
        try:
            # Fetch data from all collections
            active_store = self.db['active_stores_cache'].find_one({'_id': store_id})
            product_cache = self.db['product_details_cache'].find_one({'_id': store_id})
            customer_cache = self.db['store_customers_cache'].find_one({'_id': store_id})
            global_analysis = self.db['global_analysis'].find_one({'_id': store_id})
            
            # Check if all collections have data for this store
            if not all([active_store, product_cache, customer_cache]):
                store_issues.append({
                    'severity': 'warning',
                    'type': 'missing_data',
                    'message': f"Store {store_id} missing in some collections",
                    'details': {
                        'active_store': bool(active_store),
                        'product_cache': bool(product_cache),
                        'customer_cache': bool(customer_cache),
                        'global_analysis': bool(global_analysis)
                    }
                })
                return {'store_id': store_id, 'issues': store_issues}
            
            # Extract key metrics with null safety
            metrics = {
                'active_store': {
                    'total_revenue': active_store.get('metrics', {}).get('total_revenue', 0) if active_store else 0,
                    'total_orders': active_store.get('metrics', {}).get('total_orders', 0) if active_store else 0,
                    'total_customers': active_store.get('metrics', {}).get('total_customers', 0) if active_store else 0,
                    'active_customers': active_store.get('metrics', {}).get('active_customers', 0) if active_store else 0,
                    'total_products': active_store.get('metrics', {}).get('total_products', 0) if active_store else 0
                },
                'product_cache': {
                    'product_revenue': product_cache.get('store_aggregations', {}).get('total_revenue', 0) if product_cache else 0,
                    'shipping_revenue': product_cache.get('shipping_info', {}).get('shipping_revenue', 0) if product_cache else 0,
                    'total_revenue_calc': product_cache.get('shipping_info', {}).get('total_revenue_with_shipping', 0) if product_cache else 0,
                    'product_count': product_cache.get('product_count', 0) if product_cache else 0,
                    'products_with_sales': product_cache.get('store_aggregations', {}).get('products_with_sales', 0) if product_cache else 0
                },
                'customer_cache': {
                    'total_customers': customer_cache.get('total_customers', 0) if customer_cache else 0,
                    'total_orders': customer_cache.get('total_store_orders', 0) if customer_cache else 0,
                    'abandoned_carts': customer_cache.get('abandoned_cart_count', 0) if customer_cache else 0
                }
            }
            
            # Check 1: Revenue Consistency
            total_rev_active = metrics['active_store']['total_revenue']
            product_rev = metrics['product_cache']['product_revenue']
            total_rev_calc = metrics['product_cache']['total_revenue_calc']
            
            # Critical: Product revenue exceeds total revenue
            if product_rev > total_rev_active and total_rev_active > 0:
                discrepancy_pct = ((product_rev - total_rev_active) / total_rev_active) * 100
                store_issues.append({
                    'severity': 'critical',
                    'type': 'revenue_inconsistency',
                    'message': f"Product revenue ({product_rev}) exceeds total revenue ({total_rev_active})",
                    'discrepancy_percentage': round(discrepancy_pct, 2),
                    'details': metrics
                })
                self.stats['critical_issues'] += 1
            
            # Warning: Total revenue mismatch between calculations
            elif total_rev_calc > 0 and abs(total_rev_active - total_rev_calc) > 1:
                discrepancy_pct = abs((total_rev_active - total_rev_calc) / total_rev_active) * 100 if total_rev_active > 0 else 100
                severity = 'critical' if discrepancy_pct > CRITICAL_DISCREPANCY_THRESHOLD * 100 else 'warning'
                store_issues.append({
                    'severity': severity,
                    'type': 'total_revenue_mismatch',
                    'message': f"Total revenue mismatch: active_stores={total_rev_active}, calculated={total_rev_calc}",
                    'discrepancy_percentage': round(discrepancy_pct, 2),
                    'details': metrics
                })
                if severity == 'critical':
                    self.stats['critical_issues'] += 1
                else:
                    self.stats['warnings'] += 1
            
            # Check 2: Order Count Consistency
            orders_active = metrics['active_store']['total_orders']
            orders_customer = metrics['customer_cache']['total_orders']
            
            if orders_active != orders_customer and orders_active > 0:
                discrepancy_pct = abs((orders_active - orders_customer) / orders_active) * 100
                severity = 'warning' if discrepancy_pct < 5 else 'critical'
                store_issues.append({
                    'severity': severity,
                    'type': 'order_count_mismatch',
                    'message': f"Order count mismatch: active_stores={orders_active}, customers={orders_customer}",
                    'discrepancy_percentage': round(discrepancy_pct, 2)
                })
                if severity == 'critical':
                    self.stats['critical_issues'] += 1
                else:
                    self.stats['warnings'] += 1
            
            # Check 3: Customer Count Consistency
            customers_active = metrics['active_store']['total_customers']
            customers_cache = metrics['customer_cache']['total_customers']
            
            if customers_active != customers_cache and customers_active > 0:
                discrepancy_pct = abs((customers_active - customers_cache) / customers_active) * 100
                severity = 'info' if discrepancy_pct < 5 else 'warning'
                store_issues.append({
                    'severity': severity,
                    'type': 'customer_count_mismatch',
                    'message': f"Customer count mismatch: active_stores={customers_active}, cache={customers_cache}",
                    'discrepancy_percentage': round(discrepancy_pct, 2)
                })
                if severity == 'warning':
                    self.stats['warnings'] += 1
            
            # Check 4: Product Count Consistency
            products_active = metrics['active_store']['total_products']
            products_cache = metrics['product_cache']['product_count']
            
            if abs(products_active - products_cache) > 5:  # Allow small differences due to active/inactive
                store_issues.append({
                    'severity': 'info',
                    'type': 'product_count_mismatch',
                    'message': f"Product count differs: active_stores={products_active}, cache={products_cache}",
                    'note': 'This may be due to active/inactive product filtering'
                })
            
            # Check 5: Global Analysis Consistency (if exists)
            if global_analysis:
                ga_total_rev = global_analysis.get('metrics', {}).get('total_revenue', 0)
                ga_total_orders = global_analysis.get('metrics', {}).get('total_orders', 0)
                
                if ga_total_rev != total_rev_active and ga_total_rev > 0:
                    store_issues.append({
                        'severity': 'warning',
                        'type': 'global_analysis_outdated',
                        'message': f"Global analysis has different revenue: {ga_total_rev} vs {total_rev_active}",
                        'recommendation': 'Re-run market_analyzer.py for this store'
                    })
                    self.stats['warnings'] += 1
            
            return {
                'store_id': store_id,
                'store_name': active_store.get('name', 'Unknown') if active_store else 'Unknown',
                'issues': store_issues,
                'metrics': metrics
            }
            
        except Exception as e:
            logger.error(f"Error checking consistency for store {store_id}: {str(e)}")
            return {
                'store_id': store_id,
                'error': str(e),
                'issues': [{
                    'severity': 'error',
                    'type': 'check_failed',
                    'message': str(e)
                }]
            }
    
    def run_full_check(self, store_ids: Optional[List[str]] = None):
        """Run consistency check on all stores or specific stores"""
        logger.info("Starting data consistency check...")
        
        # Check collection timestamps first
        self.check_collection_timestamps()
        
        # Get stores to check
        if store_ids:
            stores_to_check = store_ids
            logger.info(f"Checking {len(stores_to_check)} specific stores")
        else:
            # Get all store IDs from active_stores_cache
            if self.client is None or self.db is None:
                logger.error("Database connection not available")
                return {'critical_issues': 1, 'warnings': 0, 'stores_checked': 0}, []
            stores_to_check = [doc['_id'] for doc in self.db['active_stores_cache'].find({}, {'_id': 1})]
            logger.info(f"Checking all {len(stores_to_check)} stores")
        
        # Check each store
        stores_with_issues = []
        for i, store_id in enumerate(stores_to_check):
            if i % 100 == 0 and i > 0:
                logger.info(f"Progress: {i}/{len(stores_to_check)} stores checked")
            
            self.stats['stores_checked'] += 1
            result = self.check_store_consistency(store_id)
            
            if result.get('issues'):
                stores_with_issues.append(result)
                self.stats['stores_with_issues'] += 1
                
                # Categorize issues
                for issue in result['issues']:
                    severity = issue.get('severity', 'info')
                    if severity == 'critical':
                        self.issues['critical'].append({**issue, 'store_id': store_id})
                    elif severity == 'warning':
                        self.issues['warning'].append({**issue, 'store_id': store_id})
                    else:
                        self.issues['info'].append({**issue, 'store_id': store_id})
        
        # Generate report
        self.generate_report(stores_with_issues)
        
        # Send alerts if critical issues found
        if self.issues['critical']:
            self.send_alerts()
        
        return self.stats, self.issues
    
    def generate_report(self, stores_with_issues: List[Dict]):
        """Generate a detailed consistency report"""
        report_path = f"consistency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'summary': self.stats,
            'collection_status': self.version_info,
            'critical_issues': self.issues['critical'][:50],  # Top 50
            'warnings': self.issues['warning'][:50],  # Top 50
            'recommendations': self.generate_recommendations(),
            'stores_with_issues': len(stores_with_issues),
            'sample_issues': stores_with_issues[:10]  # Sample of stores with issues
        }
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Consistency report saved to: {report_path}")
        
        # Print summary with Windows-safe characters
        print("\n" + "="*60)
        print("DATA CONSISTENCY CHECK SUMMARY")
        print("="*60)
        print(f"Stores Checked: {self.stats['stores_checked']}")
        print(f"Stores with Issues: {self.stats['stores_with_issues']} ({self.stats['stores_with_issues']/self.stats['stores_checked']*100:.1f}%)")
        print(f"Critical Issues: {self.stats['critical_issues']}")
        print(f"Warnings: {self.stats['warnings']}")
        print(f"Stale Data Collections: {self.stats['data_age_issues']}")
        print("\nCollection Status:")
        
        # Fix the Unicode issue by using ASCII-safe characters
        for collection, info in self.version_info.items():
            if 'age_hours' in info:
                # Replace checkmark with ASCII-safe indicator
                status = "STALE" if info['is_stale'] else "OK"
                print(f"  {collection}: [{status}] (age: {info['age_hours']:.1f} hours)")
        print("="*60)
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on issues found"""
        recommendations = []
        
        if self.stats['critical_issues'] > 0:
            recommendations.append("URGENT: Re-run all update scripts in sequence to fix critical revenue inconsistencies")
        
        if self.stats['data_age_issues'] > 0:
            recommendations.append("Schedule update scripts to run more frequently (current data is stale)")
        
        if self.stats['stores_with_issues'] > self.stats['stores_checked'] * 0.1:
            recommendations.append("More than 10% of stores have issues - consider full pipeline audit")
        
        # Specific recommendations based on issue types
        issue_types = defaultdict(int)
        for issue in self.issues['critical'] + self.issues['warning']:
            issue_types[issue['type']] += 1
        
        if issue_types.get('revenue_inconsistency', 0) > 0:
            recommendations.append("Implement transaction-like updates to prevent revenue mismatches")
        
        if issue_types.get('order_count_mismatch', 0) > 0:
            recommendations.append("Verify order status filtering is consistent across all scripts")
        
        if issue_types.get('global_analysis_outdated', 0) > 0:
            recommendations.append("Run market_analyzer.py after consistency fixes")
        
        return recommendations
    
    def send_alerts(self):
        """Send email alerts for critical issues"""
        if not all([SMTP_HOST, SMTP_USER, SMTP_PASSWORD, ALERT_EMAIL_TO]):
            logger.info("Email configuration not complete, skipping alerts")
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = SMTP_USER
            msg['To'] = ALERT_EMAIL_TO
            msg['Subject'] = f"Critical Data Consistency Issues - {self.stats['critical_issues']} found"
            
            body = f"""
            Critical data consistency issues detected:
            
            Summary:
            - Stores Checked: {self.stats['stores_checked']}
            - Critical Issues: {self.stats['critical_issues']}
            - Warnings: {self.stats['warnings']}
            
            Top Critical Issues:
            """
            
            for issue in self.issues['critical'][:10]:
                body += f"\n- Store {issue['store_id']}: {issue['message']}"
            
            body += "\n\nPlease check the consistency report for full details."
            
            msg.attach(MIMEText(body, 'plain'))
            
            with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
                server.starttls()
                server.login(SMTP_USER, SMTP_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Alert email sent to {ALERT_EMAIL_TO}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {str(e)}")
    
    def close(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Check data consistency across MongoDB collections')
    parser.add_argument('--stores', nargs='+', help='Specific store IDs to check')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix issues (not implemented yet)')
    args = parser.parse_args()
    
    checker = DataConsistencyChecker()
    
    try:
        if not checker.connect():
            logger.error("Failed to connect to database")
            sys.exit(1)
        
        # Run the consistency check
        stats, issues = checker.run_full_check(store_ids=args.stores)
        
        # Exit with error code if critical issues found
        if stats['critical_issues'] > 0:
            sys.exit(2)  # Exit code 2 for critical issues
        elif stats['warnings'] > 0:
            sys.exit(1)  # Exit code 1 for warnings
        else:
            sys.exit(0)  # Exit code 0 for success
            
    finally:
        checker.close()


if __name__ == "__main__":
    main()