import React from 'react';
import { 
  Paper, 
  Typography, 
  Box, 
  Chip, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText 
} from '@mui/material';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import { MetaInsight } from '../../services/types';

interface InsightCardProps {
  insight: MetaInsight;
}

export const InsightCard: React.FC<InsightCardProps> = ({ insight }) => {
  return (
    <Paper elevation={0} variant="outlined" sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Chip 
          label={insight.insight_type} 
          size="small" 
          color="primary" 
          sx={{ mr: 1 }} 
        />
        <Typography variant="caption" color="text.secondary">
          {new Date(insight.timestamp).toLocaleDateString()}
        </Typography>
      </Box>
      
      <Typography variant="subtitle1" gutterBottom>
        {insight.insight_text}
      </Typography>
      
      {insight.recommendations && (
        <Box mt={2}>
          <Typography variant="subtitle2">Recommendations:</Typography>
          <List dense>
            {insight.recommendations.map((rec, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <LightbulbIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText primary={rec} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}
    </Paper>
  );
}; 