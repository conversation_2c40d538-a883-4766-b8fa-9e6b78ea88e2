import React from 'react';
import {
  Card, 
  CardContent, 
  Typography, 
  Box,
  useTheme
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';
import { ProductDetail } from '../../services/storeService';
import { format, parseISO } from 'date-fns';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';

type SalesDetail = NonNullable<ProductDetail['sales_details']>[number];

interface ProductSalesHistoryChartProps {
  salesDetails: SalesDetail[] | null | undefined;
  productName?: string;
}

const ProductSalesHistoryChart: React.FC<ProductSalesHistoryChartProps> = ({ 
  salesDetails, 
  productName 
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!salesDetails || salesDetails.length === 0) {
    return (
      <Card elevation={0} variant="outlined">
        <CardContent>
          <Typography color="text.secondary">
            {productName 
              ? t('salesHistory.noProductData', 'No sales details available for this product.')
              : t('salesHistory.noStoreData', 'No sales history available for this store.')
            }
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Sort data by date just in case
  const sortedData = [...salesDetails].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Format data for the chart
  const chartData = sortedData.map(item => ({
    date: format(parseISO(item.date), 'MMM dd'), // Format date for display
    units_sold: item.units_sold,
    revenue: item.revenue
  }));

  return (
    <Card elevation={0} variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('salesHistory.cardTitle', 'Sales History')}{productName ? `: ${productName}` : ''}
        </Typography>
        <Box sx={{ height: 300, width: '100%' }}>
          <ChartContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
              <XAxis dataKey="date" stroke={theme.palette.text.secondary} tick={{ fontSize: 12 }} />
              <YAxis yAxisId="left" stroke={theme.palette.primary.main} tick={{ fontSize: 12 }} label={{ value: t('salesHistory.yAxis.units', 'Units Sold'), angle: -90, position: 'insideLeft', fill: theme.palette.primary.main }} />
              <YAxis yAxisId="right" orientation="right" stroke={theme.palette.secondary.main} tick={{ fontSize: 12 }} label={{ value: t('salesHistory.yAxis.revenue', 'Revenue'), angle: 90, position: 'insideRight', fill: theme.palette.secondary.main }} />
              <Tooltip 
                contentStyle={{ 
                    backgroundColor: theme.palette.background.paper, 
                    borderColor: theme.palette.divider 
                }}
                labelStyle={{ color: theme.palette.text.primary }}
                itemStyle={{ color: theme.palette.text.secondary }}
              />
              <Legend verticalAlign="top" height={36} />
              <Line yAxisId="left" type="monotone" dataKey="units_sold" name={t('salesHistory.legend.units', 'Units Sold')} stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
              <Line yAxisId="right" type="monotone" dataKey="revenue" name={t('salesHistory.legend.revenue', 'Revenue')} stroke={theme.palette.secondary.main} />
            </LineChart>
          </ChartContainer>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProductSalesHistoryChart; 