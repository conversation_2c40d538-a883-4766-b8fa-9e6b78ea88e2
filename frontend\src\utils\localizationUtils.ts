/**
 * Selects a localized text field from a data object based on the current language.
 * Defaults to the base field name if the Spanish version doesn't exist or language is not Spanish.
 *
 * @param dataObject The object containing the base field and potentially the _es field.
 * @param baseFieldName The name of the field without the _es suffix (e.g., 'summary').
 * @param language The current language code (e.g., 'en', 'es').
 * @returns The localized string or the base string, or null if neither exists.
 */
export const getLocalizedDbText = (
  dataObject: Record<string, unknown> | null | undefined,
  baseFieldName: string,
  language: string
): string | null => {
  if (!dataObject) {
    return null;
  }

  const spanishFieldName = `${baseFieldName}_es`;

  if (
    language.startsWith('es') &&
    spanishFieldName in dataObject &&
    typeof dataObject[spanishFieldName] === 'string' &&
    dataObject[spanishFieldName] !== null &&
    dataObject[spanishFieldName] !== undefined
  ) {
    return dataObject[spanishFieldName] as string;
  } else if (
    baseFieldName in dataObject &&
    typeof dataObject[baseFieldName] === 'string' &&
    dataObject[baseFieldName] !== null &&
    dataObject[baseFieldName] !== undefined
  ) {
    return dataObject[baseFieldName] as string;
  }

  return null;
}; 