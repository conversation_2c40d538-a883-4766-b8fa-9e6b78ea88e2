import cookieService from './cookieService';
import axios, { AxiosError } from 'axios';
import { httpClient } from './httpClient';
import { logger } from '../utils/logger';
// import { AUTH_BASE_URL } from '../config/api';

interface RegisterUserData {
  email: string;
  name: string;
  store_name: string;
  password_dunit: string;
}

interface UserProfileData {
  email: string;
  id_store: string;
  name: string | null;
  active?: number;
  created_at?: string;
  updated_at?: string;
  role?: string;
}

// Authentication token management using cookie service
const AUTH_TOKEN_KEY = 'dunit_auth_token';
const REFRESH_TOKEN_KEY = 'dunit_refresh_token';

export const authService = {
  login: async (email: string, password: string) => {
    logger.info(`Sending login request for ${email}`);
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/token`, 
      const response = await axios.post(`/api/auth/token`, 
        new URLSearchParams({
          username: email,
          password: password,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      logger.info('Login API response:', response.data);
      
      // Check if this is a 2FA response
      if (response.data.requires_2fa) {
        logger.info('Received 2FA response from API');
      }
      
      // Store tokens using cookie service if login successful
      if (response.data.access_token) {
        cookieService.setCookie(AUTH_TOKEN_KEY, response.data.access_token, 'essential', 1); // 1 day
        if (response.data.refresh_token) {
          cookieService.setCookie(REFRESH_TOKEN_KEY, response.data.refresh_token, 'essential', 7); // 7 days
        }
      }
      
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Login API error:', axiosError);
      logger.error('Login error response:', axiosError.response?.data);
      throw error;
    }
  },
  
  forgotPassword: async (email: string) => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/forgot-password`, { email });
      const response = await axios.post(`/api/auth/forgot-password`, { email });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Forgot password API error:', axiosError);
      logger.error('Forgot password error response:', axiosError.response?.data);
      throw error;
    }
  },
  
  resetPassword: async (token: string, newPassword: string) => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/reset-password`, {
      const response = await axios.post(`/api/auth/reset-password`, {
        token,
        new_password: newPassword,
      });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Reset password API error:', axiosError);
      logger.error('Reset password error response:', axiosError.response?.data);
      throw error;
    }
  },
  
  googleLogin: async (accessToken: string) => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/google`, { credential: accessToken });
      const response = await axios.post(`/api/auth/google`, { credential: accessToken });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Google login API error:', axiosError);
      logger.error('Google login error response:', axiosError.response?.data);
      throw error;
    }
  },
  
  metaLogin: async (accessToken: string, platform: 'facebook' | 'instagram' = 'facebook') => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/meta`, { 
      const response = await axios.post(`/api/auth/meta`, { 
        credential: accessToken,
        platform: platform
      });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Meta login API error:', axiosError);
      logger.error('Meta login error response:', axiosError.response?.data);
      throw error;
    }
  },

  requestPasswordReset: async (email: string) => {
    try {
      const response = await axios.post(`/api/auth/password-reset/request`, { email });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Password reset request API error:', axiosError);
      throw error;
    }
  },

  verifyPasswordReset: async (email: string, code: string, newPassword: string, confirmPassword: string) => {
    try {
      const response = await axios.post(`/api/auth/password-reset/verify`, {
        email,
        verification_code: code,
        new_password: newPassword,
        confirm_password: confirmPassword
      });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Password reset verify API error:', axiosError);
      throw error;
    }
  },

  verify2FA: async (email: string, code: string) => {
    logger.info(`Sending 2FA verification request for ${email} with code ${code}`);
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/2fa/verify-login`, { email, code });
      const response = await axios.post(`/api/auth/2fa/verify-login`, { email, code });
      logger.info('2FA verification API response:', response.data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('2FA verification API error:', axiosError);
      logger.error('2FA verification error response:', axiosError.response?.data);
      throw error;
    }
  },

  getUserProfile: async (): Promise<UserProfileData> => {
    try {
      // Use httpClient which automatically adds auth headers
      const response = await httpClient.get<UserProfileData>(`/api/auth/user/me`);
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Get user profile API error:', axiosError);
      logger.error('Get user profile error response:', axiosError.response?.data);
      throw error;
    }
  },

  register: async (userData: RegisterUserData) => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/register`, userData);
      const response = await axios.post(`/api/auth/register`, userData);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Register API error:', axiosError);
      logger.error('Register error response:', axiosError.response?.data);
      throw error;
    }
  },

  changePassword: async (oldPassword: string, newPassword: string, confirmPassword: string) => {
    try {
      // const response = await axios.post(`${AUTH_BASE_URL}/change-password`, {
      const response = await axios.post(`/api/auth/change-password`, {
        old_password: oldPassword,
        new_password: newPassword,
        confirm_password: confirmPassword
      });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error('Change password API error:', axiosError);
      logger.error('Change password error response:', axiosError.response?.data);
      throw error;
    }
  },

  // Token management methods using cookie service
  getToken: () => {
    return cookieService.getCookie(AUTH_TOKEN_KEY);
  },

  getRefreshToken: () => {
    return cookieService.getCookie(REFRESH_TOKEN_KEY);
  },

  setToken: (token: string) => {
    return cookieService.setCookie(AUTH_TOKEN_KEY, token, 'essential', 1);
  },

  setRefreshToken: (token: string) => {
    return cookieService.setCookie(REFRESH_TOKEN_KEY, token, 'essential', 7);
  },

  clearTokens: () => {
    // Clear authentication cookies by setting them to expire immediately
    document.cookie = `${AUTH_TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict`;
    document.cookie = `${REFRESH_TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict`;
  },

  isAuthenticated: () => {
    const token = cookieService.getCookie(AUTH_TOKEN_KEY);
    return !!token;
  }
};

export default authService; 