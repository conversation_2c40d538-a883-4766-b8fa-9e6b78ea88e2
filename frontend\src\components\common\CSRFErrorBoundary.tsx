import { Component, ErrorInfo, ReactNode } from 'react';
import { logger } from '../../utils/logger';
import { csrfService } from '../../services/csrfService';

interface CSRFErrorBoundaryState {
    hasError: boolean;
    errorType: 'csrf' | 'network' | 'unknown';
    retryCount: number;
    isRetrying: boolean;
}

interface CSRFErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    maxRetries?: number;
}

class CSRFErrorBoundary extends Component<CSRFErrorBoundaryProps, CSRFErrorBoundaryState> {
    private retryTimer: NodeJS.Timeout | null = null;

    constructor(props: CSRFErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            errorType: 'unknown',
            retryCount: 0,
            isRetrying: false
        };
    }

    static getDerivedStateFromError(error: Error): Partial<CSRFErrorBoundaryState> {
        // Analyze the error to determine if it's CSRF-related
        const errorMessage = error.message.toLowerCase();
        let errorType: 'csrf' | 'network' | 'unknown' = 'unknown';

        if (errorMessage.includes('csrf') || 
            errorMessage.includes('token missing') || 
            errorMessage.includes('invalid token')) {
            errorType = 'csrf';
        } else if (errorMessage.includes('network') || 
                   errorMessage.includes('fetch') ||
                   errorMessage.includes('socket hang up')) {
            errorType = 'network';
        }

        return {
            hasError: true,
            errorType
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        logger.error('CSRF Error Boundary caught an error:', {
            error: error.message,
            stack: error.stack,
            errorInfo: errorInfo.componentStack,
            errorType: this.state.errorType
        });

        // If it's a CSRF error, clear the token
        if (this.state.errorType === 'csrf') {
            csrfService.clearToken();
        }

        // Attempt automatic recovery for certain error types
        this.attemptRecovery();
    }

    private attemptRecovery = async () => {
        const { maxRetries = 3 } = this.props;
        
        if (this.state.retryCount >= maxRetries) {
            logger.warn('Max retries exceeded for CSRF error recovery');
            return;
        }

        this.setState({ isRetrying: true });

        try {
            if (this.state.errorType === 'csrf') {
                // Try to reinitialize CSRF token
                await csrfService.initializeAfterLogin();
                logger.info('CSRF token reinitialized successfully');
            } else if (this.state.errorType === 'network') {
                // For network errors, just wait a bit
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Reset error state after successful recovery
            this.setState({
                hasError: false,
                errorType: 'unknown',
                retryCount: 0,
                isRetrying: false
            });

        } catch (recoveryError) {
            logger.error('CSRF error recovery failed:', recoveryError);
            this.setState({
                retryCount: this.state.retryCount + 1,
                isRetrying: false
            });

            // Schedule another retry if we haven't hit the limit
            if (this.state.retryCount + 1 < maxRetries) {
                this.retryTimer = setTimeout(() => {
                    this.attemptRecovery();
                }, 5000 * (this.state.retryCount + 1)); // Exponential backoff
            }
        }
    };

    private handleManualRetry = () => {
        this.setState({
            hasError: false,
            errorType: 'unknown',
            retryCount: 0,
            isRetrying: false
        });
    };

    componentWillUnmount() {
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
        }
    }

    render() {
        if (this.state.hasError) {
            // Custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback;
            }

            // Default error UI based on error type
            return (
                <div className="csrf-error-boundary" style={{
                    padding: '20px',
                    margin: '20px',
                    border: '2px solid #f5c6cb',
                    borderRadius: '8px',
                    backgroundColor: '#f8d7da',
                    color: '#721c24'
                }}>
                    <h3>🔒 Security Error Detected</h3>
                    
                    {this.state.errorType === 'csrf' && (
                        <div>
                            <p>A security token error occurred. This is usually temporary.</p>
                            <p><strong>What happened:</strong> The security token used to protect your data became invalid.</p>
                            <p><strong>What we're doing:</strong> Automatically refreshing your security tokens.</p>
                        </div>
                    )}
                    
                    {this.state.errorType === 'network' && (
                        <div>
                            <p>A network connection error occurred.</p>
                            <p><strong>What happened:</strong> Unable to connect to the server.</p>
                            <p><strong>What to try:</strong> Check your internet connection and try again.</p>
                        </div>
                    )}
                    
                    {this.state.errorType === 'unknown' && (
                        <div>
                            <p>An unexpected error occurred.</p>
                            <p><strong>What happened:</strong> Something went wrong that we're working to fix.</p>
                        </div>
                    )}

                    <div style={{ marginTop: '15px' }}>
                        {this.state.isRetrying ? (
                            <p style={{ color: '#0c5460' }}>
                                🔄 Attempting automatic recovery... (Attempt {this.state.retryCount + 1})
                            </p>
                        ) : (
                            <div>
                                <button
                                    onClick={this.handleManualRetry}
                                    style={{
                                        padding: '8px 16px',
                                        backgroundColor: '#0c5460',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        marginRight: '10px'
                                    }}
                                >
                                    Try Again
                                </button>
                                
                                <button
                                    onClick={() => window.location.reload()}
                                    style={{
                                        padding: '8px 16px',
                                        backgroundColor: '#6c757d',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                    }}
                                >
                                    Refresh Page
                                </button>
                            </div>
                        )}
                        
                        {this.state.retryCount > 0 && (
                            <p style={{ fontSize: '12px', marginTop: '10px', color: '#6c757d' }}>
                                Retry attempts: {this.state.retryCount}/{this.props.maxRetries || 3}
                            </p>
                        )}
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

export default CSRFErrorBoundary; 