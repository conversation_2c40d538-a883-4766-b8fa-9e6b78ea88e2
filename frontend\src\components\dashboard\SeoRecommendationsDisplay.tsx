import React from 'react';
import {
  <PERSON>,
  Typography,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Chip,
  Grid
} from '@mui/material';
import { SeoRecommendation } from '../../services/storeService'; // Only SeoRecommendation might be needed
import { useTranslation } from 'react-i18next';

interface SeoRecommendationsDisplayProps {
  // storeId: string; // Removed storeId as it's not used
  recommendations: SeoRecommendation[];
  isLoading: boolean;
  error: string | null;
  hasFetched: boolean;
}

// Helper functions that don't depend on component state/props can be outside
const generateKeyFromTitle = (title: string): string => {
  if (!title) return '';
  const words = title.trim().split(/\s+/);
  return words
    .map((word, index) => {
      const cleanWord = word.replace(/[^a-zA-Z0-9]/g, '');
      return index === 0 
        ? cleanWord.toLowerCase() 
        : cleanWord.charAt(0).toUpperCase() + cleanWord.slice(1).toLowerCase();
    })
    .join('');
};

const normalizeCategory = (category: string): string => {
  if (!category) return '';
  return category.toLowerCase().trim();
};

const getPriorityColor = (priority?: string): 'error' | 'warning' | 'info' | 'default' => {
  switch (priority?.toLowerCase()) {
    case 'high': return 'error';
    case 'medium': return 'warning';
    case 'low': return 'info';
    default: return 'default';
  }
};

const SeoRecommendationsDisplay: React.FC<SeoRecommendationsDisplayProps> = ({ /* storeId, */ recommendations, isLoading, error, hasFetched }) => {
  const { t } = useTranslation(); // i18n object might not be needed here anymore if lang is fully handled by Dashboard

  // Helper function that uses 't' from useTranslation needs to be inside the component
  const translatePriority = (priority?: string): string => {
    if (!priority) return '';
    switch (priority.toLowerCase()) {
      case 'high': return t('seoRecommendations.high', 'High');
      case 'medium': return t('seoRecommendations.medium', 'Medium');
      case 'low': return t('seoRecommendations.low', 'Low');
      default: return priority;
    }
  };

  if (isLoading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}><CircularProgress /></Box>;
  }

  if (error) {
    return <Alert severity="error" sx={{ my: 2 }}>{error}</Alert>;
  }

  if (hasFetched) {
    if (recommendations.length === 0) {
      return <Alert severity="info" sx={{ my: 2 }}>{t('seoRecommendations.noRecommendations', 'No SEO recommendations available at this time.')}</Alert>;
    }
    // Render recommendations if available
    return (
      <Box sx={{ mt: 0 }}>
        <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 3 }}>
          {t('seoRecommendations.cardTitle', 'AI-Powered SEO Recommendations')}
        </Typography>
        <Grid container spacing={2}>
          {recommendations.map((rec, index) => {
            const titleKey = generateKeyFromTitle(rec.title);
            const categoryNormalized = normalizeCategory(rec.category ?? '');
            return (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" component="div" gutterBottom sx={{ fontSize: '1.1rem' }}>
                      {t(`seoRecommendations.recommendations.${titleKey}.title`, rec.title)}
                    </Typography>
                    <Box sx={{ mb: 1.5 }}>
                      {rec.category && 
                        <Chip 
                          label={t(`seoRecommendations.categories.${categoryNormalized}`, rec.category)} 
                          size="small" 
                          sx={{ 
                            mr: 1, 
                            mb: 1,
                            bgcolor: (theme) => theme.palette.mode === 'dark' 
                              ? 'rgba(0, 163, 255, 0.2)'
                              : 'rgba(0, 163, 255, 0.1)',
                            color: (theme) => theme.palette.mode === 'dark'
                              ? '#00A3FF'
                              : '#0091EA',
                            border: (theme) => theme.palette.mode === 'dark'
                              ? '1px solid #00A3FF'
                              : '1px solid #0091EA',
                            '&:hover': {
                              bgcolor: (theme) => theme.palette.mode === 'dark'
                                ? 'rgba(0, 163, 255, 0.3)'
                                : 'rgba(0, 163, 255, 0.2)',
                            }
                          }} 
                        />
                      }
                      {rec.priority && 
                        <Chip 
                          label={`${t('seoRecommendations.priority', 'Priority')}: ${translatePriority(rec.priority)}`} 
                          size="small" 
                          color={getPriorityColor(rec.priority)}
                          sx={{ mb: 1 }}
                        />
                      }
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {t(`seoRecommendations.recommendations.${titleKey}.description`, rec.description)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Box>
    );
  }
  
  // If !hasFetched and not loading and no error (initial state before background fetch completes or if it failed silently before setting error)
  // Display a subtle placeholder or loading message. Or null if preferred.
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', my: 4, py: 2, color: 'text.secondary', border: '1px dashed grey', borderRadius: 1 }}>
      <Typography variant="caption">{t('seoRecommendations.loadingPlaceholder', 'Loading SEO recommendations in the background...')}</Typography>
    </Box>
  );
};

export default SeoRecommendationsDisplay; 
