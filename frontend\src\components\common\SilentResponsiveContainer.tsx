import React, { useRef, useEffect, useState, useCallback } from 'react';
import { ResponsiveContainer } from 'recharts';

interface SilentResponsiveContainerProps {
  width?: string | number;
  height?: string | number;
  minHeight?: number;
  minWidth?: number;
  aspect?: number;
  debounce?: number;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * A completely silent ResponsiveContainer that suppresses all Recharts logUtils warnings
 * This component wraps the original ResponsiveContainer and aggressively suppresses console output
 */
export const SilentResponsiveContainer: React.FC<SilentResponsiveContainerProps> = ({
  width = '100%',
  height = '100%',
  minHeight,
  minWidth,
  aspect,
  debounce = 0,
  children,
  className,
  style
}) => {
  const [renderChart, setRenderChart] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Aggressive console suppression
  const suppressConsole = useCallback(() => {
    const originalWarn = console.warn;
    const originalError = console.error;
    const originalLog = console.log;

    const createSuppressor = (original: (...args: unknown[]) => void) => (...args: unknown[]) => {
      const message = args.join(' ').toLowerCase();
      
      // Ultra-broad suppression for anything Recharts related
      if (
        message.includes('width') ||
        message.includes('height') ||
        message.includes('chart') ||
        message.includes('logutils') ||
        message.includes('responsivecontainer') ||
        message.includes('recharts') ||
        message.includes('greater') ||
        message.includes('minwidth') ||
        message.includes('minheight') ||
        message.includes('aspect') ||
        message.includes('container') ||
        message.includes('style') ||
        message.includes('props')
      ) {
        return; // Completely suppress
      }
      original.apply(console, args);
    };

    console.warn = createSuppressor(originalWarn);
    console.error = createSuppressor(originalError);
    console.log = createSuppressor(originalLog);

    return () => {
      console.warn = originalWarn;
      console.error = originalError;
      console.log = originalLog;
    };
  }, []);

  useEffect(() => {
    const cleanup = suppressConsole();
    
    // Ensure container has dimensions before rendering chart
    const checkDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          setRenderChart(true);
        }
      }
    };

    // Delay initial check to ensure container is sized
    const timer = setTimeout(() => {
      checkDimensions();
    }, 100);

    // Also check on resize
    const resizeObserver = new ResizeObserver(() => {
      checkDimensions();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      clearTimeout(timer);
      resizeObserver.disconnect();
      cleanup();
    };
  }, [suppressConsole]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        width: typeof width === 'string' ? width : `${width}px`,
        height: typeof height === 'string' ? height : `${height}px`,
        minHeight: minHeight || 200,
        minWidth: minWidth || 200,
        ...style
      }}
    >
      {renderChart && React.isValidElement(children) ? (
        <ResponsiveContainer
          width={width}
          height={height}
          minHeight={minHeight}
          minWidth={minWidth}
          aspect={aspect}
          debounce={debounce}
        >
          {children}
        </ResponsiveContainer>
      ) : (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: minHeight || 200
        }}>
          Loading chart...
        </div>
      )}
    </div>
  );
};

export default SilentResponsiveContainer; 