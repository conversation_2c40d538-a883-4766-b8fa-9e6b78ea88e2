// import { API_URL } from '../../config/api'; // API_URL no longer needed directly
import { fetchWithDeduplication, invalidateCache } from './apiService';
import { authService } from './authService';
import { logger } from '../utils/logger';

export interface MetaPermission {
  id: string;
  name: string;
  status: 'granted' | 'declined' | 'expired' | 'not_requested';
  required: boolean;
  description: string;
}

export interface PermissionsResponse {
  permissions: Record<string, string>;
  lastUpdated: string;
}

export interface FeatureAccess {
  hasAccess: boolean;
  missingPermissions: string[];
}

/**
 * Get all Meta permissions for the current store
 */
export const getMetaPermissions = async (): Promise<Record<string, string>> => {
  try {
    const token = authService.getToken();
    const storeId = localStorage.getItem('store_id');
    
    if (!token || !storeId) {
      throw new Error('Authentication required');
    }
    
    const response = await fetchWithDeduplication<PermissionsResponse>(
      // `${API_URL}/api/meta/permissions/${storeId}`,
      `/api/meta/permissions/${storeId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response.permissions || {};
  } catch (error) {
    logger.error('Error fetching Meta permissions:', error);
    return {};
  }
};

/**
 * Refresh permissions from Meta API
 */
export const refreshMetaPermissions = async (): Promise<Record<string, string>> => {
  try {
    const token = authService.getToken();
    const storeId = localStorage.getItem('store_id');
    
    if (!token || !storeId) {
      throw new Error('Authentication required');
    }
    
    // Use a POST request to force a refresh
    const response = await fetchWithDeduplication<PermissionsResponse>(
      // `${API_URL}/api/meta/permissions/${storeId}/refresh`,
      `/api/meta/permissions/${storeId}/refresh`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    // Invalidate cache for the permissions endpoint
    invalidateCache(`/api/meta/permissions/${storeId}`);
    
    return response.permissions || {};
  } catch (error) {
    logger.error('Error refreshing Meta permissions:', error);
    return {};
  }
};

/**
 * Check if a specific feature is accessible based on permissions
 */
export const checkFeatureAccess = async (featureKey: string): Promise<FeatureAccess> => {
  try {
    const token = authService.getToken();
    const storeId = localStorage.getItem('store_id');
    
    if (!token || !storeId) {
      return { hasAccess: false, missingPermissions: ['Authentication required'] };
    }
    
    const response = await fetchWithDeduplication<FeatureAccess>(
      // `${API_URL}/api/meta/permissions/${storeId}/feature/${featureKey}`,
      `/api/meta/permissions/${storeId}/feature/${featureKey}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response;
  } catch (error) {
    logger.error(`Error checking access for feature ${featureKey}:`, error);
    return { hasAccess: false, missingPermissions: ['Error checking permissions'] };
  }
};

/**
 * Get permission details with metadata
 */
export const getPermissionDetails = async (): Promise<MetaPermission[]> => {
  try {
    const token = authService.getToken();
    const storeId = localStorage.getItem('store_id');
    
    if (!token || !storeId) {
      throw new Error('Authentication required');
    }
    
    const response = await fetchWithDeduplication<MetaPermission[]>(
      // `${API_URL}/api/meta/permissions/${storeId}/details`,
      `/api/meta/permissions/${storeId}/details`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response || [];
  } catch (error) {
    logger.error('Error fetching permission details:', error);
    return [];
  }
};

/**
 * Get mapping of features to their required permissions
 */
export const getFeaturePermissionMap = async (): Promise<Record<string, string[]>> => {
  try {
    const token = authService.getToken();
    
    if (!token) {
      throw new Error('Authentication required');
    }
    
    const response = await fetchWithDeduplication<Record<string, string[]>>(
      // `${API_URL}/api/meta/permissions/feature-map`,
      `/api/meta/permissions/feature-map`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response || {};
  } catch (error) {
    logger.error('Error fetching feature permission map:', error);
    return {};
  }
}; 