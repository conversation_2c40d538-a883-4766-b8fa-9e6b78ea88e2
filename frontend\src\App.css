@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  font-family: 'Inter', system-ui, -apple-system, sans-serif !important;
}

/* Chat interface styles */
.chat-container {
  color: white;
}

/* Chat message text */
.assistant-message, 
.user-message,
.message-container p,
.message-content {
  letter-spacing: -0.01em;
}

/* Message bubbles with specific styling - TODO: Implement styles */
/* .message-bubble {
} */

/* Mode Button Styles */
.mode-button {
  transition: all 0.2s ease-in-out;
  position: relative;
  background-color: var(--mode-button-bg);
  color: var(--primary-color);
}

.mode-button.active {
  background-color: rgba(0, 163, 255, 0.15) !important; /* Lighter blue background */
  color: var(--primary-color) !important; /* Keep text blue */
  font-weight: 600 !important; /* Make text slightly bolder */
  box-shadow: inset 0 0 0 1px var(--primary-color) !important; /* Add inner border */
}

.mode-button:hover {
  transform: translateY(-2px);
  background-color: var(--mode-button-hover);
  box-shadow: 0 4px 8px var(--primary-shadow);
}

/* Specific styles for light mode */
@media (prefers-color-scheme: light) {
  .mode-button {
    border: 1px solid rgba(0, 163, 255, 0.2);
  }
  
  .mode-button.active {
    background-color: rgba(0, 163, 255, 0.15) !important;
    border-color: var(--primary-color);
  }
}

/* Image upload button and preview */
.image-upload-btn {
  transition: all 0.2s ease;
}

.image-upload-btn:hover {
  background-color: rgba(0, 163, 255, 0.1);
}

.image-preview-container {
  position: relative;
  margin: 12px auto;
  max-width: 300px;
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.remove-image-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: var(--background-paper);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remove-image-btn:hover {
  background-color: var(--background-paper-hover);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

/* Fallback for Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

/* Chat message animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.prose {
  max-width: none;
}

.prose p {
  margin: 0.5em 0;
}

.prose ul {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

/* Message animations */
main > div {
  animation: slideIn 0.3s ease-out forwards;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .w-64 {
    width: 240px;
  }
}

@media (max-width: 640px) {
  .w-64 {
    width: 200px;
  }
}

/* Chat history hover states */
.chat-history-item {
  transition: background-color 0.2s ease;
}

.chat-history-item:hover {
  background-color: var(--chat-history-hover);
}

/* Chat input field */
.chat-input-field {
  background-color: var(--input-background);
  border-radius: 24px !important;
  border: 1px solid var(--input-border-color);
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.chat-input-container {
  position: relative;
  margin-right: 24px;
  padding: 1.5rem;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--input-background);
  border: 1px solid var(--input-border-color);
  transition: all 0.2s ease;
}

.chat-input-container:hover {
  background-color: var(--input-background-hover);
  border-color: var(--input-border-color-hover);
}

/* Input field hover and focus states */
.chat-input-field:hover,
.chat-input-field:focus-within {
  background-color: var(--input-background-hover);
  border-color: var(--input-border-color-hover);
}

/* Message send button */
.send-button {
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  min-width: 36px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: var(--primary-color-hover);
  transform: scale(1.05);
}

/* Product Details Dashboard Text Styles */
.product-details {
  color: var(--text-primary);
}

/* Ensure text is visible in dark mode */
@media (prefers-color-scheme: dark) {
  .product-details,
  .product-details h1,
  .product-details h2,
  .product-details h3,
  .product-details p,
  .product-details span {
    color: rgba(255, 255, 255, 0.87); /* Standard white with slight transparency for better readability */
  }

  .product-details .secondary-text {
    color: rgba(255, 255, 255, 0.6); /* Slightly dimmed white for secondary information */
  }

  /* For any statistics or metrics */
  .product-details .metric-value {
    color: #00A3FF; /* Keep the brand blue color for important numbers */
  }

  /* For labels and headers */
  .product-details .metric-label {
    color: rgba(255, 255, 255, 0.7); /* Medium contrast for labels */
  }
}

/* --- Enhanced Markdown/Assistant Message Styling --- */
.markdown-content {
  font-size: 1.05rem;
  color: #222;
  background: #FAFAFB;
  border-radius: 16px;
  padding: 1.25em 1.5em;
  margin-bottom: 0.5em;
  text-align: left;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  line-height: 1.7;
  letter-spacing: -0.01em;
}

.markdown-content ol,
.markdown-content ul {
  margin: 0.5em 0 0.5em 1.5em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.35em;
  font-size: 1rem;
}

.markdown-content strong,
.markdown-content b {
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: -0.01em;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 700;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
  color: #0d0d0d;
  letter-spacing: -0.01em;
}

.markdown-content h1 { font-size: 1.5rem; }
.markdown-content h2 { font-size: 1.25rem; }
.markdown-content h3 { font-size: 1.1rem; }
.markdown-content h4, .markdown-content h5, .markdown-content h6 { font-size: 1rem; }

.markdown-content p {
  margin: 0.5em 0;
  font-size: 1.05rem;
  color: #222;
}

.markdown-content .summary, .markdown-content .resumen {
  margin-top: 1em;
  padding: 0.75em 1em;
  background: #f0f4fa;
  border-left: 4px solid #00A3FF;
  border-radius: 8px;
  font-weight: 500;
  color: #1a1a1a;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 1.5em 0;
}

.markdown-content {
  word-break: break-word;
}

/* Responsive for markdown-content */
@media (max-width: 600px) {
  .markdown-content {
    padding: 1em 0.5em;
    font-size: 0.98rem;
  }
}

