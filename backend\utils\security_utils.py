import hashlib
import re
import secrets
import base64
import uuid
from typing import Optional, Dict, Any, List
from fastapi import Request
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import ipaddress
import logging
import hmac
from datetime import datetime, timezone
import string

logger = logging.getLogger(__name__)

def generate_request_fingerprint(request: Request) -> str:
    """
    Generate a unique fingerprint for a request based on multiple factors.
    
    Args:
        request: FastAPI Request object
        
    Returns:
        str: SHA-256 hash representing the request fingerprint
    """
    # Gather request components for fingerprinting
    components = [
        request.method,
        str(request.url.path),
        request.client.host if request.client else "unknown",
        request.headers.get("user-agent", ""),
        request.headers.get("accept", ""),
        request.headers.get("accept-language", ""),
        request.headers.get("accept-encoding", ""),
    ]
    
    # Create fingerprint string
    fingerprint_string = "|".join(components)
    
    # Generate SHA-256 hash
    return hashlib.sha256(fingerprint_string.encode()).hexdigest()

def detect_suspicious_user_agent(user_agent: str) -> bool:
    """
    Detect potentially suspicious user agents that might indicate automated attacks.
    
    Args:
        user_agent: User agent string from request headers
        
    Returns:
        bool: True if user agent appears suspicious, False otherwise
    """
    if not user_agent:
        return True
    
    # Convert to lowercase for case-insensitive matching
    ua_lower = user_agent.lower()
    
    # Known suspicious patterns
    suspicious_patterns = [
        r'bot(?!tom)',  # Various bots (but not bottom)
        r'crawler',
        r'spider',
        r'scraper',
        r'curl',
        r'wget',
        r'python-requests',
        r'libwww',
        r'scan',
        r'test',
        r'hack',
        r'attack',
        r'exploit',
        r'injection',
        r'burp',
        r'sqlmap',
        r'nikto',
        r'nessus',
        r'openvas',
        r'masscan',
        r'nmap',
    ]
    
    # Check for suspicious patterns
    for pattern in suspicious_patterns:
        if re.search(pattern, ua_lower):
            return True
    
    # Check for very short user agents (less than 10 characters)
    if len(user_agent.strip()) < 10:
        return True
    
    # Check for unusual character patterns
    if re.search(r'[<>{}()"\']', user_agent):
        return True
    
    return False

def sanitize_sql_input(input_string: str) -> str:
    """
    Sanitize input to prevent SQL injection attacks.
    
    Args:
        input_string: Input string to sanitize
        
    Returns:
        str: Sanitized string safe for database operations
    """
    if not isinstance(input_string, str):
        return str(input_string)
    
    # Remove potentially dangerous SQL keywords and characters
    dangerous_patterns = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)',
        r'[;\'\"\\]',
        r'--',
        r'/\*.*?\*/',
        r'\bOR\b\s+\d+\s*=\s*\d+',
        r'\bAND\b\s+\d+\s*=\s*\d+',
    ]
    
    sanitized = input_string
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    # Remove extra whitespace
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()
    
    return sanitized

def validate_ip_address(ip: str) -> bool:
    """
    Validate if a string is a valid IP address (IPv4 or IPv6).
    
    Args:
        ip: IP address string to validate
        
    Returns:
        bool: True if valid IP address, False otherwise
    """
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False

def is_private_ip(ip: str) -> bool:
    """
    Check if an IP address is in a private range.
    
    Args:
        ip: IP address string
        
    Returns:
        bool: True if IP is private, False otherwise
    """
    try:
        ip_obj = ipaddress.ip_address(ip)
        return ip_obj.is_private
    except ValueError:
        return False

def generate_encryption_key() -> bytes:
    """
    Generate a secure encryption key for Fernet.
    
    Returns:
        bytes: 32-byte encryption key
    """
    return Fernet.generate_key()

def encrypt_sensitive_data(data: str, key: str) -> str:
    """
    Encrypt sensitive data using Fernet symmetric encryption.
    
    Args:
        data: Data to encrypt
        key: Base64-encoded encryption key
        
    Returns:
        str: Base64-encoded encrypted data
    """
    try:
        # Decode the key from base64
        key_bytes = base64.urlsafe_b64decode(key.encode())
        f = Fernet(key_bytes)
        
        # Encrypt the data
        encrypted_data = f.encrypt(data.encode())
        
        # Return base64-encoded encrypted data
        return base64.urlsafe_b64encode(encrypted_data).decode()
    except Exception as e:
        logger.error(f"Encryption error: {e}")
        raise ValueError("Failed to encrypt data")

def decrypt_sensitive_data(encrypted_data: str, key: str) -> str:
    """
    Decrypt sensitive data using Fernet symmetric encryption.
    
    Args:
        encrypted_data: Base64-encoded encrypted data
        key: Base64-encoded encryption key
        
    Returns:
        str: Decrypted data
    """
    try:
        # Decode the key and encrypted data from base64
        key_bytes = base64.urlsafe_b64decode(key.encode())
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        
        f = Fernet(key_bytes)
        
        # Decrypt the data
        decrypted_data = f.decrypt(encrypted_bytes)
        
        return decrypted_data.decode()
    except Exception as e:
        logger.error(f"Decryption error: {e}")
        raise ValueError("Failed to decrypt data")

def generate_csrf_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure CSRF token
    
    Args:
        length: Length of the token (default: 32)
        
    Returns:
        A secure random token string
    """
    # Use URL-safe characters (letters, digits, - and _)
    alphabet = string.ascii_letters + string.digits + '-_'
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def verify_csrf_token(token: str, session_id: str) -> bool:
    """
    Verify a CSRF token against the session.
    
    Args:
        token: CSRF token to verify
        session_id: Session ID for verification
        
    Returns:
        bool: True if token is valid, False otherwise
    """
    # In a real implementation, you would store and validate tokens
    # For now, we'll do basic validation
    if not token or not session_id:
        return False
    
    # Check token format
    if len(token) < 16:
        return False
    
    # Check for potentially malicious characters
    if re.search(r'[<>{}()"\']', token):
        return False
    
    return True

def extract_client_ip(request: Request) -> str:
    """
    Extract client IP address from request headers
    Checks for X-Forwarded-For, X-Real-IP, and falls back to client host
    """
    # Check X-Forwarded-For header (for reverse proxies)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain (original client)
        return forwarded_for.split(',')[0].strip()
    
    # Check X-Real-IP header (for nginx reverse proxy)
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # Fall back to client host
    client_host = request.client.host if request.client else "unknown"
    return client_host

def calculate_request_complexity(request: Request, body_data: Optional[Dict[str, Any]] = None) -> int:
    """
    Calculate a complexity score for a request to help with rate limiting.
    
    Args:
        request: FastAPI Request object
        body_data: Parsed request body data
        
    Returns:
        int: Complexity score (1-10, higher is more complex)
    """
    complexity = 1
    
    # Factor in HTTP method
    if request.method in ["POST", "PUT", "PATCH"]:
        complexity += 1
    elif request.method == "DELETE":
        complexity += 2
    
    # Factor in path complexity
    path_segments = len(request.url.path.split("/"))
    if path_segments > 4:
        complexity += 1
    
    # Factor in query parameters
    query_params = len(request.query_params)
    if query_params > 5:
        complexity += 1
    elif query_params > 10:
        complexity += 2
    
    # Factor in body size and complexity
    if body_data:
        if isinstance(body_data, dict):
            if len(body_data) > 10:
                complexity += 1
            if len(str(body_data)) > 1000:
                complexity += 1
        elif isinstance(body_data, list):
            if len(body_data) > 10:
                complexity += 2
    
    # Factor in specific endpoint types
    path = request.url.path.lower()
    if "/chat" in path or "/ai" in path:
        complexity += 2  # AI endpoints are expensive
    elif "/meta" in path or "/analytics" in path:
        complexity += 1  # External API calls
    
    return min(complexity, 10)  # Cap at 10

def detect_attack_patterns(request: Request, body_data: Optional[str] = None) -> List[str]:
    """
    Detect common attack patterns in requests.
    
    Args:
        request: FastAPI Request object
        body_data: Raw request body data
        
    Returns:
        List[str]: List of detected attack patterns
    """
    patterns = []
    
    # Check URL for common attack patterns
    path = request.url.path.lower()
    query = str(request.query_params).lower()
    
    # SQL Injection patterns
    sql_patterns = [
        r"'.*or.*=.*",
        r"union.*select",
        r"drop.*table",
        r"insert.*into",
        r"update.*set",
        r"delete.*from",
    ]
    
    for pattern in sql_patterns:
        if re.search(pattern, path + query, re.IGNORECASE):
            patterns.append("sql_injection")
            break
    
    # XSS patterns
    xss_patterns = [
        r"<script",
        r"javascript:",
        r"onload=",
        r"onerror=",
        r"eval\(",
    ]
    
    for pattern in xss_patterns:
        if re.search(pattern, path + query, re.IGNORECASE):
            patterns.append("xss_attempt")
            break
    
    # Path traversal patterns
    if ".." in path or "etc/passwd" in path or "windows/system32" in path:
        patterns.append("path_traversal")
    
    # Command injection patterns
    cmd_patterns = [
        r";\s*cat\s+",
        r";\s*ls\s+",
        r";\s*rm\s+",
        r";\s*wget\s+",
        r";\s*curl\s+",
    ]
    
    for pattern in cmd_patterns:
        if re.search(pattern, path + query, re.IGNORECASE):
            patterns.append("command_injection")
            break
    
    # Check body data if provided
    if body_data:
        body_lower = body_data.lower()
        
        # Check for code injection in body
        if any(re.search(pattern, body_lower) for pattern in sql_patterns):
            patterns.append("sql_injection")
        
        if any(re.search(pattern, body_lower) for pattern in xss_patterns):
            patterns.append("xss_attempt")
    
    return patterns

def generate_trace_id() -> str:
    """
    Generate a unique trace ID for request tracking.
    
    Returns:
        str: Unique trace ID
    """
    return secrets.token_hex(16)

def mask_sensitive_data(data: str, mask_char: str = "*") -> str:
    """
    Mask sensitive data for logging purposes.
    
    Args:
        data: Data to mask
        mask_char: Character to use for masking
        
    Returns:
        str: Masked data
    """
    if not data:
        return ""
    
    # Email masking
    if "@" in data and "." in data:
        parts = data.split("@")
        if len(parts) == 2:
            username = parts[0]
            domain = parts[1]
            masked_username = username[:2] + mask_char * (len(username) - 2) if len(username) > 2 else mask_char * len(username)
            return f"{masked_username}@{domain}"
    
    # Credit card-like number masking
    if re.match(r'^\d{13,19}$', data):
        return mask_char * (len(data) - 4) + data[-4:]
    
    # Generic sensitive data masking (keep first and last char)
    if len(data) > 6:
        return data[0] + mask_char * (len(data) - 2) + data[-1]
    else:
        return mask_char * len(data)

def verify_hmac_signature(data: str, signature: str, secret: str) -> bool:
    """Verify HMAC signature for webhook validation"""
    try:
        expected_signature = hmac.new(
            secret.encode(),
            data.encode(),
            hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(signature, expected_signature)
    except Exception as e:
        logger.error(f"HMAC verification error: {e}")
        return False

def sanitize_input(input_string: str) -> str:
    """Sanitize user input to prevent XSS, SQL injection, and other attacks"""
    if not input_string:
        return ""
    
    # HTML entity encoding for XSS protection
    html_chars = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '/': '&#x2F;'
    }
    
    sanitized = input_string
    for char, entity in html_chars.items():
        sanitized = sanitized.replace(char, entity)
    
    # Remove or escape SQL injection characters
    sql_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
    for sql_char in sql_chars:
        sanitized = sanitized.replace(sql_char, '')
    
    # Remove script tags and javascript: protocols
    script_patterns = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'eval\s*\('
    ]
    
    for pattern in script_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
    
    return sanitized.strip()

def detect_attack_patterns_string(content: str) -> List[str]:
    """Detect attack patterns in string content (for chat messages, etc.)"""
    patterns = []
    
    if not content:
        return patterns
    
    # SQL injection patterns
    if re.search(r'(union|select|insert|update|delete|drop|create|alter|exec|execute)', content, re.IGNORECASE):
        patterns.append("sql_injection")
    
    # XSS patterns
    if re.search(r'(<script|javascript:|onload=|onerror=|eval\()', content, re.IGNORECASE):
        patterns.append("xss_attempt")
    
    # Path traversal patterns
    if re.search(r'(\.\./|\.\.\\|/etc/passwd|windows/system32)', content, re.IGNORECASE):
        patterns.append("path_traversal")
    
    # Command injection patterns
    if re.search(r'(;\s*(cat|ls|rm|wget|curl|nc|netcat|bash|sh)\s+)', content, re.IGNORECASE):
        patterns.append("command_injection")
    
    # Template injection patterns
    if re.search(r'(\{\{|\}\}|\{%|%\})', content):
        patterns.append("template_injection")
    
    return patterns

def is_safe_redirect_url(url: str, allowed_hosts: Optional[list] = None) -> bool:
    """
    Check if a redirect URL is safe (prevent open redirects)
    
    Args:
        url: The URL to check
        allowed_hosts: List of allowed hosts (optional)
        
    Returns:
        True if the URL is safe for redirect
    """
    if not url:
        return False
    
    # Prevent data: and javascript: URLs
    if url.startswith(('data:', 'javascript:', 'vbscript:')):
        return False
    
    # Allow relative URLs
    if url.startswith('/') and not url.startswith('//'):
        return True
    
    # If allowed_hosts is provided, check against them
    if allowed_hosts:
        from urllib.parse import urlparse
        try:
            parsed = urlparse(url)
            return parsed.netloc in allowed_hosts
        except Exception:
            return False
    
    return False 