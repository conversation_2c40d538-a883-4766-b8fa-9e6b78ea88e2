#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix for stores with documents exceeding MongoDB's 16MB limit.
This script identifies large stores and splits their product data into separate documents.
"""

import os
import sys
from pymongo import MongoClient
from datetime import datetime, timezone
import logging

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

from config.database import get_mongodb_connection
from config.settings import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_and_fix_large_stores():
    """Check for stores with large documents and fix them"""
    settings = get_settings()
    
    # Connect to MongoDB
    logger.info("Connecting to MongoDB...")
    mongo_client = get_mongodb_connection()
    mongo_db = mongo_client['D-Unit-AnalysisGPT']
    
    # Get the product details cache collection
    collection = mongo_db['product_details_cache']
    
    # Find stores with many products (potential large documents)
    logger.info("Checking for stores with large product counts...")
    
    # Store 961 is known to have issues
    problem_stores = [961]
    
    for store_id in problem_stores:
        logger.info(f"Processing store {store_id}...")
        
        try:
            # Get the document
            doc = collection.find_one({"_id": str(store_id)})
            
            if doc and 'products' in doc:
                product_count = len(doc['products'])
                logger.info(f"Store {store_id} has {product_count} products")
                
                if product_count > 1000:  # Threshold for splitting
                    logger.info(f"Store {store_id} needs splitting - creating separate product collection")
                    
                    # Create a new collection for large stores' products
                    large_store_products_collection = mongo_db['large_store_products']
                    
                    # Split products into chunks of 500
                    chunk_size = 500
                    products = doc['products']
                    
                    # Remove products from main document
                    collection.update_one(
                        {"_id": str(store_id)},
                        {
                            "$set": {
                                "products": [],  # Empty the products array
                                "products_in_separate_collection": True,
                                "product_count": product_count,
                                "large_store_split_date": datetime.now(timezone.utc)
                            }
                        }
                    )
                    
                    # Insert products in chunks to the new collection
                    for i in range(0, len(products), chunk_size):
                        chunk = products[i:i + chunk_size]
                        chunk_doc = {
                            "_id": f"{store_id}_chunk_{i//chunk_size}",
                            "store_id": str(store_id),
                            "chunk_index": i // chunk_size,
                            "products": chunk,
                            "created_at": datetime.now(timezone.utc)
                        }
                        
                        large_store_products_collection.replace_one(
                            {"_id": chunk_doc["_id"]},
                            chunk_doc,
                            upsert=True
                        )
                        logger.info(f"Inserted chunk {i//chunk_size} with {len(chunk)} products")
                    
                    logger.info(f"Successfully split store {store_id} products into {(len(products) + chunk_size - 1) // chunk_size} chunks")
                    
        except Exception as e:
            logger.error(f"Error processing store {store_id}: {e}", exc_info=True)
    
    logger.info("Large store document fix completed")
    mongo_client.close()

if __name__ == "__main__":
    check_and_fix_large_stores()