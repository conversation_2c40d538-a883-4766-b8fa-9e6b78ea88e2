import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from 'recharts';
import { DemographicsAgeData, DemographicsGenderData } from '../../services/storeService';
import ChartContainer from '../common/ChartContainer';

// DemographicsAgeData and DemographicsGenderData are imported from storeService.ts
// This ensures type consistency for demographic data across all analytics and chart components.
// The chart expects either an array of DemographicsAgeData (age breakdown) or DemographicsGenderData (gender breakdown).

// Use shared types for demographic data for consistency across the project
interface DemographicsData {
  age?: DemographicsAgeData[];
  gender?: DemographicsGenderData[];
}

interface DemographicsChartProps {
  data?: DemographicsData; // Use the defined interface for type safety
  title?: string;
}

export const DemographicsChart: React.FC<DemographicsChartProps> = ({ data, title = 'Demographics Distribution' }) => {
  // If no data, return a placeholder
  if (!data) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No demographics data available</p>
      </div>
    );
  }

  // Colors for the pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A569BD', '#5DADE2', '#F5B041'];

  // Process data for the chart - prefer age data if available, otherwise gender
  const chartData = data.age || data.gender || [];
  
  // Map data to format expected by Recharts
  // Accepts both DemographicsAgeData and DemographicsGenderData
  const formattedData = chartData.map((item: DemographicsAgeData | DemographicsGenderData) => ({
    name: 'range' in item ? item.range : item.type,
    value: item.percentage
  }));

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      
      <div className="h-64 w-full">
        <ChartContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={formattedData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={80}
              fill="#8884d8"
              paddingAngle={5}
              dataKey="value"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {formattedData.map((_entry, index: number) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value: number) => [`${value}%`, 'Percentage']}
              labelFormatter={(name) => `${name}`}
            />
          </PieChart>
        </ChartContainer>
      </div>
      
      <div className="mt-4 grid grid-cols-2 gap-2">
        {formattedData.map((entry, index: number) => (
          <div key={entry.name} className="flex items-center">
            <span
              className="w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: COLORS[index % COLORS.length] }}
            ></span>
            <span className="text-sm text-gray-600">{entry.name}</span>
            <span className="text-sm text-gray-500 ml-auto">{entry.value}%</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DemographicsChart; 