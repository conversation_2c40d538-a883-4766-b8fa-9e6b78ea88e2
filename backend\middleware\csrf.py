import logging
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from services.csrf_service import csrf_service
from services.csrf_attack_detector import csrf_attack_detector
from services.auth import get_current_user
from typing import Optional
import asyncio
from config.settings import get_settings

logger = logging.getLogger(__name__)

class CSRFMiddleware(BaseHTTPMiddleware):
    """
    CSRF Protection Middleware
    Validates CSRF tokens for authenticated POST/PUT/PATCH/DELETE requests
    """
    
    def __init__(self, app, exempt_paths: Optional[list] = None):
        super().__init__(app)
        self.exempt_paths = exempt_paths or [
            "/api/auth/token",  # Login endpoint
            "/api/auth/google",  # Google auth
            "/api/auth/meta",  # Meta auth
            "/api/auth/dunit/register",  # Registration
            "/api/auth/dunit/verify-registration",  # Registration verification
            "/api/auth/forgot-password",  # Password reset
            "/api/auth/2fa/verify-login",  # 2FA verification
            "/api/auth/password-reset/request",
            "/api/auth/password-reset/verify",
            "/api/auth/2fa/enable",      # 2FA enable endpoint
            "/api/auth/2fa/disable",     # 2FA disable endpoint  
            "/api/auth/2fa/verify",      # 2FA verify endpoint
            "/api/auth/csrf-token",  # CSRF token generation (GET)
            "/api/auth/csrf-token/health",  # CSRF health check
            "/api/auth/logout",  # Logout endpoint
            "/docs",  # API documentation
            "/openapi.json",  # OpenAPI schema
            "/health",  # Health check
        ]
        self.protected_methods = ["POST", "PUT", "PATCH", "DELETE"]
    
    async def dispatch(self, request: Request, call_next):
        """Process request and validate CSRF token if needed"""
        # Early-exit bypass when CSRF protection is globally disabled via env flag
        settings = get_settings()
        if getattr(settings, "DISABLE_CSRF", False):
            return await call_next(request)
        
        try:
            # Skip CSRF validation for non-protected methods
            if request.method not in self.protected_methods:
                return await call_next(request)
            
            # Skip CSRF validation for exempt paths
            request_path = request.url.path
            if any(request_path.startswith(exempt_path) for exempt_path in self.exempt_paths):
                return await call_next(request)
            
            # Get current user from token (non-blocking)
            current_user = None
            try:
                # Extract token from Authorization header
                authorization = request.headers.get("Authorization")
                if authorization and authorization.startswith("Bearer "):
                    token = authorization.split(" ")[1]
                    current_user = await get_current_user(token)
            except Exception as e:
                logger.debug(f"Could not extract user from token: {e}")
                # If we can't get user, skip CSRF validation (let auth middleware handle it)
                return await call_next(request)
            
            # Skip CSRF validation for unauthenticated requests
            if not current_user:
                return await call_next(request)
            
            # Get CSRF token from headers
            csrf_token = request.headers.get("X-CSRF-Token")
            
            # Get CSRF token from cookies (for double submit pattern)
            csrf_cookie = None
            if "csrf_token" in request.cookies:
                csrf_cookie = request.cookies["csrf_token"]
            
            # Check if double submit pattern is enabled and available
            double_submit_enabled = csrf_service.supports_double_submit()
            
            if double_submit_enabled and csrf_token and csrf_cookie:
                # Use double submit validation
                is_valid = await csrf_service.validate_double_submit(csrf_token, csrf_cookie, current_user.email)
                if not is_valid:
                    logger.warning(f"Invalid CSRF double submit for user {current_user.email} on {request.method} {request_path}")
                    return JSONResponse(
                        status_code=403,
                        content={
                            "success": False,
                            "error": "Invalid CSRF token",
                            "detail": "The provided CSRF tokens (header and cookie) are invalid or expired"
                        }
                    )
            elif csrf_token:
                # Fallback to header-only validation
                is_valid = await csrf_service.validate_token(csrf_token, current_user.email)
                if not is_valid:
                    logger.warning(f"Invalid CSRF token for user {current_user.email} on {request.method} {request_path}")
                    
                    # Track violation for attack detection
                    try:
                        client_ip = request.client.host if request.client else "unknown"
                        user_agent = request.headers.get("User-Agent", "")
                        
                        await csrf_attack_detector.track_violation(
                            user_id=current_user.email,
                            ip_address=client_ip,
                            endpoint=request_path,
                            method=request.method,
                            violation_type="invalid_token",
                            user_agent=user_agent
                        )
                        
                        # Check if response should be escalated
                        should_escalate, reason = await csrf_attack_detector.should_escalate_response(
                            client_ip, current_user.email
                        )
                        
                        if should_escalate:
                            logger.critical(f"CSRF attack escalation: {reason}")
                    
                    except Exception as detection_error:
                        logger.error(f"CSRF attack detection error: {detection_error}")
                    
                    return JSONResponse(
                        status_code=403,
                        content={
                            "success": False,
                            "error": "Invalid CSRF token",
                            "detail": "The provided CSRF token is invalid or expired"
                        }
                    )
            elif csrf_cookie:
                # Cookie-only validation (fallback mode)
                is_valid = await csrf_service.validate_token(csrf_cookie, current_user.email)
                if not is_valid:
                    logger.warning(f"Invalid CSRF cookie token for user {current_user.email} on {request.method} {request_path}")
                    return JSONResponse(
                        status_code=403,
                        content={
                            "success": False,
                            "error": "Invalid CSRF token",
                            "detail": "The provided CSRF cookie token is invalid or expired"
                        }
                    )
            else:
                # No CSRF protection available
                logger.warning(f"Missing CSRF token for {request.method} request to {request_path} from user {current_user.email}")
                
                # Track violation for attack detection
                try:
                    client_ip = request.client.host if request.client else "unknown"
                    user_agent = request.headers.get("User-Agent", "")
                    
                    await csrf_attack_detector.track_violation(
                        user_id=current_user.email,
                        ip_address=client_ip,
                        endpoint=request_path,
                        method=request.method,
                        violation_type="missing_token",
                        user_agent=user_agent
                    )
                
                except Exception as detection_error:
                    logger.error(f"CSRF attack detection error: {detection_error}")
                
                return JSONResponse(
                    status_code=403,
                    content={
                        "success": False,
                        "error": "CSRF token missing",
                        "detail": "X-CSRF-Token header or csrf_token cookie is required for this request"
                    }
                )
            
            # CSRF token is valid, proceed with request
            logger.debug(f"CSRF token validated for user {current_user.email} on {request.method} {request_path}")
            
            # Process the request
            response = await call_next(request)
            
            # Check if this operation requires token rotation after successful completion
            if response.status_code < 400:  # Success response codes
                if csrf_service.is_sensitive_operation(request_path, request.method):
                    try:
                        # Rotate the token after sensitive operation (use header token if available, else cookie)
                        token_to_rotate = csrf_token if csrf_token else csrf_cookie
                        if token_to_rotate:
                            new_token = await csrf_service.rotate_token_on_sensitive_operation(
                                current_user.email, token_to_rotate
                            )
                            
                            # Add new token to response headers
                            response.headers["X-CSRF-Token-Rotated"] = new_token
                            response.headers["X-CSRF-Token-Rotation"] = "true"
                            
                            logger.debug(f"CSRF token rotated for user {current_user.email} after sensitive operation {request.method} {request_path}")
                        
                    except Exception as rotation_error:
                        logger.error(f"Failed to rotate CSRF token for user {current_user.email}: {rotation_error}")
                        # Don't fail the request if rotation fails
            
            return response
            
        except Exception as e:
            logger.error(f"CSRF middleware error: {e}")
            # In case of middleware errors, let the request proceed to avoid breaking the app
            # But log it for monitoring
            pass
        
        return await call_next(request) 