import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  List,
  ListItem,
  ListItemText,
  Divider,
  Tooltip,
  Theme
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { CustomerMetricsData } from '../../services/storeService';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/locationUtils';

interface CustomerAggregatedMetricsCardProps {
  metrics: CustomerMetricsData | null | undefined;
}

const CustomerAggregatedMetricsCard: React.FC<CustomerAggregatedMetricsCardProps> = ({ metrics }) => {
  const { t } = useTranslation();

  const renderDistributionList = (title: string, distribution: Record<string, number> | undefined | null, maxItemsToShow: number = 5) => {
    if (!distribution || Object.keys(distribution).length === 0) return null;
    const sortedEntries = Object.entries(distribution).sort(([, a], [, b]) => b - a);

    return (
      <Box mb={2}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>{title}</Typography>
        <List dense disablePadding>
          {sortedEntries.slice(0, maxItemsToShow).map(([key, value]) => (
            <ListItem
              key={key}
              disableGutters
              sx={(theme: Theme) => theme.palette.mode === 'dark' ? {
                py: 0.5,
                px: 1.5,
                mb: 1,
                backgroundColor: theme.palette.background.paper,
                color: '#fff',
                borderRadius: 2,
                transition: 'background 0.2s',
                '&:hover': {
                  backgroundColor: '#222',
                  color: '#fff',
                },
              } : {
                py: 0.5,
                px: 1.5,
                mb: 1
              }}
            >
              <ListItemText
                primary={key}
                secondary={`${t('common.countPrefix', 'Count:')} ${value}`}
                primaryTypographyProps={{ variant: 'body2', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
                secondaryTypographyProps={{ variant: 'caption', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
              />
            </ListItem>
          ))}
          {sortedEntries.length > maxItemsToShow && (
             <ListItem disableGutters sx={(theme: Theme) => theme.palette.mode === 'dark' ? {
               py: 0.5,
               px: 1.5,
               mb: 1,
               backgroundColor: theme.palette.background.paper,
               color: '#fff',
               borderRadius: 2,
               transition: 'background 0.2s',
               '&:hover': {
                 backgroundColor: '#222',
                 color: '#fff',
               },
             } : {
               py: 0.5,
               px: 1.5,
               mb: 1
             }}>
               <ListItemText primary="..." primaryTypographyProps={{ variant: 'body2', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }} />
             </ListItem>
          )}
        </List>
      </Box>
    );
  };

  const renderMetric = (labelKey: string, defaultValue: string, value: number | string | undefined | null, isCurrency: boolean = false, tooltipKey?: string) => {
    const displayValue = (value !== undefined && value !== null && value !== '')
      ? (isCurrency ? formatCurrency(Number(value)) : value)
      : 'N/A';
    
    return (
      <Grid item xs={6} sm={4}>
        <Box>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
            {t(labelKey, defaultValue)}
            {tooltipKey && (
              <Tooltip title={t(tooltipKey, '')} placement="top">
                <InfoOutlinedIcon fontSize="inherit" sx={{ ml: 0.5, verticalAlign: 'middle' }} />
              </Tooltip>
            )}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>{displayValue}</Typography>
        </Box>
      </Grid>
    );
  }

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('customerAggregatedMetricsCard.title', 'Aggregated Customer Metrics')}
        </Typography>
        {!metrics ? (
          <Typography variant="body2" color="text.secondary" fontStyle="italic">
            {t('customerAggregatedMetricsCard.notAvailable', 'No aggregated metrics available.')}
          </Typography>
        ) : (
          <Grid container spacing={2}>
            {/* Core Metrics */}
            {renderMetric('customerAggregatedMetricsCard.totalCustomers', 'Total Customers', metrics.total_customers)}
            {renderMetric('customerAggregatedMetricsCard.totalStoreOrders', 'Total Store Orders', metrics.total_store_orders)}
            {renderMetric('customerAggregatedMetricsCard.avgSpend', 'Avg. Spend/Customer', metrics.average_spend_per_customer, true)}
            
            {/* Cart Metrics */}
            {renderMetric('customerAggregatedMetricsCard.abandonedCarts', 'Abandoned Carts', metrics.abandoned_cart_count)}
            {renderMetric('customerAggregatedMetricsCard.abandonedCartCustomers', 'Customers (Abandoned)', metrics.abandoned_cart_customer_count)}
            {renderMetric('customerAggregatedMetricsCard.abandonedValue', 'Abandoned Value', metrics.abandoned_cart_total_value, true)}
            {renderMetric('customerAggregatedMetricsCard.pendingCarts', 'Pending Carts', metrics.pending_cart_count)}
            {renderMetric('customerAggregatedMetricsCard.pendingValue', 'Pending Value', metrics.pending_cart_total_value, true)}

            <Grid item xs={12}><Divider sx={{ my: 1 }} /></Grid>

            {/* Distributions */}
            <Grid item xs={12} md={6}>
              {renderDistributionList(t('customerAggregatedMetricsCard.countryDistribution', 'Country Distribution'), metrics.country_distribution)}
              {renderDistributionList(t('customerAggregatedMetricsCard.shippingDistribution', 'Shipping Method Distribution'), metrics.shipping_method_distribution)}
              {renderDistributionList(t('customerAggregatedMetricsCard.couponDistribution', 'Coupon Distribution'), metrics.coupon_code_distribution, 3)}
            </Grid>
            <Grid item xs={12} md={6}>
              {renderDistributionList(t('customerAggregatedMetricsCard.paymentDistribution', 'Payment Method Distribution'), metrics.payment_method_distribution)}
              {renderDistributionList(t('customerAggregatedMetricsCard.statusDistribution', 'Status Distribution'), metrics.status_distribution)}
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default CustomerAggregatedMetricsCard; 