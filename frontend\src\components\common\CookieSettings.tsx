import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Chip,
  <PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  FormControlLabel,
  IconButton,
  Paper,
  Stack,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Tune as TuneIcon,
  Campaign as MarketingIcon,
  Speed as SpeedIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import cookieService from '../../services/cookieService';
import { CookiePreferences, CookieConsent } from '../../types/cookies';
import { COOKIE_CONFIG, COOKIE_CATEGORIES } from '../../config/cookies';

const categoryIcons = {
  essential: <SecurityIcon />,
  functional: <TuneIcon />,
  analytics: <AnalyticsIcon />,
  performance: <SpeedIcon />,
  marketing: <MarketingIcon />
};

export const CookieSettings: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [preferences, setPreferences] = useState<CookiePreferences>(
    cookieService.getPreferences()
  );
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [consentRecord, setConsentRecord] = useState<CookieConsent | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useEffect(() => {
    setConsentRecord(cookieService.getConsentRecord());
  }, []);

  const handlePreferenceChange = (category: keyof CookiePreferences, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }));
  };

  const handleSavePreferences = () => {
    cookieService.updatePreferences(preferences);
    setConsentRecord(cookieService.getConsentRecord());
    setSnackbarMessage(t('cookies.settings.saved', 'Cookie preferences saved successfully'));
    setSnackbarOpen(true);
  };

  const handleResetConsent = () => {
    cookieService.resetConsent();
    setPreferences(cookieService.getPreferences());
    setConsentRecord(null);
    setSnackbarMessage(t('cookies.settings.reset', 'Cookie consent has been reset'));
    setSnackbarOpen(true);
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const getCookiesForCategory = (categoryId: string) => {
    return COOKIE_CONFIG.cookies.filter(cookie => cookie.category === categoryId);
  };

  const renderCookieTable = (categoryId: string) => {
    const cookies = getCookiesForCategory(categoryId);
    
    if (cookies.length === 0) return null;

    return (
      <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>{t('cookies.table.name', 'Cookie Name')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.purpose', 'Purpose')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.duration', 'Duration')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.type', 'Type')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.provider', 'Provider')}</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cookies.map((cookie, index) => (
              <TableRow key={`${cookie.name}-${index}`}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {cookie.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {cookie.purpose}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {cookie.duration}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={t(`cookies.types.${cookie.type}`, cookie.type)} 
                    size="small" 
                    color={cookie.type === 'first-party' ? 'primary' : 'secondary'}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {cookie.provider}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('cookies.settings.title', 'Cookie Settings')}
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        {t('cookies.settings.description', 
          'Manage your cookie preferences and view detailed information about the cookies we use. Changes will take effect immediately.'
        )}
      </Typography>

      {/* Consent Information */}
      {consentRecord && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>{t('cookies.settings.consentGiven', 'Consent Given')}:</strong>{' '}
            {consentRecord.timestamp.toLocaleString()}
            <br />
            <strong>{t('cookies.settings.consentVersion', 'Version')}:</strong>{' '}
            {consentRecord.version}
            <br />
            <strong>{t('cookies.settings.consentId', 'Consent ID')}:</strong>{' '}
            {consentRecord.consentId}
          </Typography>
        </Alert>
      )}

      {/* Action Buttons */}
      <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
        <Button
          variant="contained"
          onClick={handleSavePreferences}
          startIcon={<RefreshIcon />}
        >
          {t('cookies.settings.save', 'Save Preferences')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          onClick={handleResetConsent}
          startIcon={<DeleteIcon />}
        >
          {t('cookies.settings.resetConsent', 'Reset Consent')}
        </Button>
      </Stack>

      {/* Cookie Categories */}
      <Stack spacing={3}>
        {COOKIE_CATEGORIES.map((category) => {
          const isExpanded = expandedCategories[category.id];
          const isEnabled = preferences[category.id as keyof CookiePreferences];
          const cookieCount = getCookiesForCategory(category.id).length;
          
          return (
            <Card key={category.id} variant="outlined">
              <CardHeader
                avatar={
                  <Box sx={{ color: theme.palette.primary.main }}>
                    {categoryIcons[category.id as keyof typeof categoryIcons]}
                  </Box>
                }
                title={
                  <Box display="flex" alignItems="center">
                    <Typography variant="h6">
                      {t(`cookies.categories.${category.id}.name`, category.name)}
                    </Typography>
                    {category.essential && (
                      <Chip 
                        label={t('cookies.category.essential', 'Essential')} 
                        size="small" 
                        color="error" 
                        sx={{ ml: 1 }}
                      />
                    )}
                    <Chip 
                      label={`${cookieCount} ${t('cookies.category.cookies', 'cookies')}`}
                      size="small" 
                      variant="outlined"
                      sx={{ ml: 1 }}
                    />
                  </Box>
                }
                subheader={t(`cookies.categories.${category.id}.description`, category.description)}
                action={
                  <Box display="flex" alignItems="center">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={isEnabled}
                          onChange={(e) => handlePreferenceChange(
                            category.id as keyof CookiePreferences, 
                            e.target.checked
                          )}
                          disabled={category.essential}
                        />
                      }
                      label=""
                      sx={{ mr: 1 }}
                    />
                    <IconButton
                      onClick={() => toggleCategoryExpansion(category.id)}
                      size="small"
                    >
                      {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                }
              />
              
              <Collapse in={isExpanded}>
                <CardContent>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="subtitle2" gutterBottom>
                    {t('cookies.category.cookiesUsed', 'Cookies in this category:')}
                  </Typography>
                  {renderCookieTable(category.id)}
                </CardContent>
              </Collapse>
            </Card>
          );
        })}
      </Stack>

      {/* Legal Information */}
      <Card variant="outlined" sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('cookies.settings.legalInfo', 'Legal Information')}
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            {t('cookies.settings.legalText', 
              'This website complies with GDPR, CCPA, LGPD, PIPEDA, and other applicable privacy regulations. You have the right to:'
            )}
          </Typography>
          <Box component="ul" sx={{ ml: 2 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              {t('cookies.settings.right1', 'Withdraw consent at any time')}
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              {t('cookies.settings.right2', 'Request deletion of your data')}
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              {t('cookies.settings.right3', 'Access your personal data')}
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              {t('cookies.settings.right4', 'Data portability')}
            </Typography>
          </Box>
          
          <Box mt={2}>
            <Typography variant="body2" color="text.secondary">
              <strong>{t('cookies.settings.version', 'Configuration Version')}:</strong> {COOKIE_CONFIG.consentVersion}
              <br />
              <strong>{t('cookies.settings.supportedRegulations', 'Supported Regulations')}:</strong>{' '}
              {Object.entries(COOKIE_CONFIG.legalBasis)
                .filter(([_, enabled]) => enabled)
                .map(([regulation]) => regulation.toUpperCase())
                .join(', ')}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default CookieSettings; 