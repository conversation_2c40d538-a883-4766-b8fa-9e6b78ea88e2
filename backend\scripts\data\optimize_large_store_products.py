#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optimize large store products by reducing data for inactive/zero-revenue products.
This script reduces document size for stores like 961 by simplifying inactive products.
"""

import os
import sys
from pymongo import MongoClient
from datetime import datetime, timezone
import logging

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

from config.database import get_mongodb_connection
from config.settings import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def optimize_store_products(store_id: str):
    """Optimize products for a large store by simplifying inactive products"""
    settings = get_settings()
    
    # Connect to MongoDB
    logger.info("Connecting to MongoDB...")
    mongo_client = get_mongodb_connection()
    mongo_db = mongo_client['D-Unit-AnalysisGPT']
    
    # Get collections
    main_collection = mongo_db['product_details_cache']
    large_store_collection = mongo_db['large_store_products']
    
    logger.info(f"Optimizing products for store {store_id}...")
    
    # Get all chunks for this store
    chunks = list(large_store_collection.find({"store_id": store_id}))
    
    if not chunks:
        logger.warning(f"No chunks found for store {store_id}")
        return
    
    total_products = 0
    optimized_products = 0
    total_size_reduction = 0
    
    for chunk in chunks:
        chunk_id = chunk['_id']
        products = chunk['products']
        optimized_chunk_products = []
        
        logger.info(f"Processing chunk {chunk_id} with {len(products)} products...")
        
        for product in products:
            total_products += 1
            original_product = product.copy()
            
            # Criteria for simplification
            is_inactive = (
                product.get('revenue', 0) == 0 and 
                product.get('sales_units', 0) == 0 and
                product.get('current_stock', 0) == 0
            )
            
            is_zero_revenue = product.get('revenue', 0) == 0
            has_many_variations = len(product.get('variations', [])) > 10
            
            if is_inactive:
                # Heavily simplify completely inactive products
                simplified_product = {
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'current_price': product.get('current_price', 0),
                    'current_stock': product.get('current_stock', 0),
                    'status': 'inactive',
                    'simplified': True,
                    'original_variation_count': len(product.get('variations', [])),
                    'last_updated': datetime.now(timezone.utc)
                }
                optimized_chunk_products.append(simplified_product)
                optimized_products += 1
                
            elif is_zero_revenue and has_many_variations:
                # Reduce variations for zero-revenue products with many variations
                simplified_product = product.copy()
                
                # Keep only first 5 variations as examples
                if 'variations' in simplified_product and len(simplified_product['variations']) > 5:
                    original_count = len(simplified_product['variations'])
                    simplified_product['variations'] = simplified_product['variations'][:5]
                    simplified_product['variations_truncated'] = True
                    simplified_product['original_variation_count'] = original_count
                
                # Remove detailed price history for zero-revenue products
                if 'price_history' in simplified_product:
                    simplified_product['price_history'] = []
                
                simplified_product['simplified'] = True
                simplified_product['last_updated'] = datetime.now(timezone.utc)
                optimized_chunk_products.append(simplified_product)
                optimized_products += 1
                
            else:
                # Keep active/revenue-generating products as-is
                optimized_chunk_products.append(product)
        
        # Update the chunk with optimized products
        large_store_collection.update_one(
            {"_id": chunk_id},
            {
                "$set": {
                    "products": optimized_chunk_products,
                    "optimized_at": datetime.now(timezone.utc),
                    "optimization_applied": True
                }
            }
        )
        
        logger.info(f"Optimized chunk {chunk_id}: {len(optimized_chunk_products)} products")
    
    # Update main store document with optimization info
    main_collection.update_one(
        {"_id": store_id},
        {
            "$set": {
                "products_optimized": True,
                "optimization_date": datetime.now(timezone.utc),
                "products_simplified": optimized_products,
                "total_products": total_products
            }
        }
    )
    
    logger.info("=" * 60)
    logger.info(f"OPTIMIZATION SUMMARY FOR STORE {store_id}")
    logger.info("=" * 60)
    logger.info(f"Total products processed: {total_products}")
    logger.info(f"Products simplified: {optimized_products}")
    logger.info(f"Simplification rate: {(optimized_products/total_products)*100:.1f}%")
    logger.info(f"Estimated size reduction: ~{optimized_products * 5}KB (avg 5KB per simplified product)")
    logger.info("=" * 60)
    
    mongo_client.close()

def analyze_store_before_optimization(store_id: str):
    """Analyze store data before optimization"""
    settings = get_settings()
    
    # Connect to MongoDB
    mongo_client = get_mongodb_connection()
    mongo_db = mongo_client['D-Unit-AnalysisGPT']
    large_store_collection = mongo_db['large_store_products']
    
    # Analyze the data
    pipeline = [
        {"$match": {"store_id": store_id}},
        {"$unwind": "$products"},
        {
            "$group": {
                "_id": None,
                "total_products": {"$sum": 1},
                "zero_revenue": {"$sum": {"$cond": [{"$eq": ["$products.revenue", 0]}, 1, 0]}},
                "no_sales": {"$sum": {"$cond": [{"$eq": ["$products.sales_units", 0]}, 1, 0]}},
                "zero_stock": {"$sum": {"$cond": [{"$eq": ["$products.current_stock", 0]}, 1, 0]}},
                "inactive_products": {
                    "$sum": {
                        "$cond": [
                            {
                                "$and": [
                                    {"$eq": ["$products.revenue", 0]},
                                    {"$eq": ["$products.sales_units", 0]},
                                    {"$eq": ["$products.current_stock", 0]}
                                ]
                            },
                            1, 0
                        ]
                    }
                }
            }
        }
    ]
    
    result = list(large_store_collection.aggregate(pipeline))
    
    if result:
        stats = result[0]
        logger.info("=" * 60)
        logger.info(f"STORE {store_id} ANALYSIS")
        logger.info("=" * 60)
        logger.info(f"Total products: {stats['total_products']}")
        logger.info(f"Zero revenue: {stats['zero_revenue']} ({(stats['zero_revenue']/stats['total_products'])*100:.1f}%)")
        logger.info(f"No sales: {stats['no_sales']} ({(stats['no_sales']/stats['total_products'])*100:.1f}%)")
        logger.info(f"Zero stock: {stats['zero_stock']} ({(stats['zero_stock']/stats['total_products'])*100:.1f}%)")
        logger.info(f"Completely inactive: {stats['inactive_products']} ({(stats['inactive_products']/stats['total_products'])*100:.1f}%)")
        logger.info("=" * 60)
    
    mongo_client.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Optimize large store products')
    parser.add_argument('--store-id', default='961', help='Store ID to optimize (default: 961)')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, do not optimize')
    
    args = parser.parse_args()
    
    if args.analyze_only:
        analyze_store_before_optimization(args.store_id)
    else:
        logger.info("Starting product optimization...")
        analyze_store_before_optimization(args.store_id)
        optimize_store_products(args.store_id)
        logger.info("Optimization complete!")