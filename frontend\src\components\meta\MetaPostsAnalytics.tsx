import { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  CircularProgress, 
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import { PostMetrics, TimeRange } from '../../services/types';
import { MetaDataService } from '../../services/dataService';
import { TimeRangeFilter } from '../common/TimeRangeFilter';
import { getMetaTimeRangePresets, getDefaultMetaTimeRange, getDefaultInstagramPostsTimeRange } from '../../services/metaTimeRanges';
import { PieChart, Pie, Cell, Legend, Tooltip as RechartsTooltip } from 'recharts';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { useTranslation } from 'react-i18next';
import { MetaPage, MetaPost, InstagramPost } from '../../services/types';
import ChartContainer from '../common/ChartContainer';
import { logger } from '../../utils/logger';

interface MetaPostsAnalyticsProps {
  page?: MetaPage;
  useMockData?: boolean;
}

/**
 * Component for displaying Meta posts analytics
 */
export const MetaPostsAnalytics: React.FC<MetaPostsAnalyticsProps> = ({
  page,
  useMockData = false
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [posts, setPosts] = useState<MetaPost[]>([]);
  const [postMetrics, setPostMetrics] = useState<Record<string, PostMetrics>>({});
  // Set default timeRange based on platform
  const getInitialTimeRange = useCallback(() => {
    if (page?.platform === 'instagram') {
      return getDefaultInstagramPostsTimeRange();
    }
    return getDefaultMetaTimeRange();
  }, [page?.platform]);
  
  const [timeRange, setTimeRange] = useState<TimeRange | null>(getInitialTimeRange());

  const { t, i18n } = useTranslation();

  // Update timeRange when page platform changes
  useEffect(() => {
    setTimeRange(getInitialTimeRange());
  }, [page?.platform, getInitialTimeRange]);

  useEffect(() => {
    if (!page) return;

    const fetchPosts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Initialize empty arrays to avoid undefined errors
        let fetchedPosts: MetaPost[] = [];
        
        if (useMockData) {
          // Generate realistic mock posts data for testing
          fetchedPosts = [
            {
              id: `${page.id}_post_1`,
              message: 'Exciting product launch announcement! Check out our latest innovation.',
              created_time: new Date(Date.now() - 86400000 * 3).toISOString(),
              permalink_url: `https://${page.platform}.com/${page.id}/posts/1`,
              type: 'image',
              platform: page.platform || 'facebook',
              attachments: [
                {
                  media_type: 'image',
                  url: '/api/placeholder/product-launch.jpg'
                }
              ]
            },
            {
              id: `${page.id}_post_2`,
              message: 'Behind the scenes look at our team working on amazing projects.',
              created_time: new Date(Date.now() - 86400000 * 7).toISOString(),
              permalink_url: `https://${page.platform}.com/${page.id}/posts/2`,
              type: 'image',
              platform: page.platform || 'facebook'
            },
            {
              id: `${page.id}_post_3`,
              message: 'Tutorial video: How to get the most out of our platform.',
              created_time: new Date(Date.now() - 86400000 * 14).toISOString(),
              permalink_url: `https://${page.platform}.com/${page.id}/posts/3`,
              type: 'video',
              platform: page.platform || 'facebook',
              attachments: [
                {
                  media_type: 'video',
                  url: '/api/placeholder/tutorial-video.mp4'
                }
              ]
            }
          ];
        } else {
          // Fetch real posts data from Meta API using the correct platform
          const effectiveTimeRange = timeRange || {
            since: (() => {
              const date = new Date();
              date.setDate(date.getDate() - 30);
              return date.toISOString().split('T')[0];
            })(),
            until: new Date().toISOString().split('T')[0]
          };
          fetchedPosts = await MetaDataService.getPosts(page.id, effectiveTimeRange, page.platform, page.access_token);
        }
        
        // Ensure fetchedPosts is always an array
        fetchedPosts = Array.isArray(fetchedPosts) ? fetchedPosts : [];
        setPosts(fetchedPosts);
        
        // Fetch metrics for each post
        const metrics: Record<string, PostMetrics> = {};
        
        for (const post of fetchedPosts) {
          try {
            if (useMockData) {
              // Generate mock metrics for each post
              metrics[post.id] = {
                post_id: post.id,
                likes: Math.floor(Math.random() * 100) + 10,
                comments: Math.floor(Math.random() * 50) + 5,
                shares: post.platform === 'instagram' ? 0 : Math.floor(Math.random() * 30) + 2, // No shares for Instagram
                saved: post.platform === 'instagram' ? Math.floor(Math.random() * 40) + 5 : 0, // Only saved for Instagram
                impressions: Math.floor(Math.random() * 1000) + 100,
                reach: Math.floor(Math.random() * 800) + 80,
                engagement_rate: (Math.random() * 5 + 1)
              };
            } else {
              if (post.platform === 'instagram') {
                // Check if this is an Instagram post with metrics
                const instagramPost = post as InstagramPost;
                
                if (instagramPost.instagram_metrics) {
                  // Use Instagram metrics directly from the post with proper typing
                  metrics[post.id] = {
                    post_id: post.id,
                    likes: instagramPost.instagram_metrics.like_count,
                    comments: instagramPost.instagram_metrics.comments_count,
                    shares: 0, // Instagram doesn't have shares
                    saved: instagramPost.instagram_metrics.saved_count || 0,
                    impressions: 0, // These metrics require additional permissions
                    reach: 0,
                    engagement_rate: 0
                  };
                  
                  // If saved count isn't in the initial metrics, try to fetch it
                  if (!instagramPost.instagram_metrics.saved_count && useMockData === false) {
                    try {
                      // Fetch additional Instagram metrics if available
                      const additionalMetrics = await MetaDataService.getInstagramPostMetrics(post.id);
                      if (additionalMetrics && additionalMetrics.saved) {
                        metrics[post.id].saved = additionalMetrics.saved;
                      }
                    } catch {
                      // No variable needed since we're not using it
                      logger.warn('Could not fetch saved count for post', post.id);
                      // Continue without the saved count
                    }
                  }
                }
              } else {
                // Fetch Facebook metrics from Meta API
                const postMetrics = await MetaDataService.getPostMetrics(post.id, page.access_token);
                metrics[post.id] = postMetrics;
              }
            }
          } catch (err) {
            logger.error(`Error fetching metrics for ${post.platform} post ${post.id}:`, err);
            // Add default metrics for failed fetches
            metrics[post.id] = {
              post_id: post.id,
              likes: 0,
              comments: 0,
              shares: 0,
              impressions: 0,
              reach: 0,
              engagement_rate: 0,
              saved: 0
            };
          }
        }
        
        setPostMetrics(metrics);
      } catch (err) {
        logger.error(`Error fetching ${page?.platform || 'Meta'} posts:`, err);
        let errorMessage = t('metaDashboard.posts.errorLoad', { 
          defaultValue: `Failed to load ${page?.platform || 'Meta'} posts data. Please try again later.`, 
          platform: page?.platform || 'Meta' 
        });
        // Enhanced error handling for Facebook
        if (
          page?.platform === 'facebook' &&
          err &&
          typeof err === 'object' &&
          err !== null &&
          'originalError' in err
        ) {
          const code = (err as { originalError: { code?: number; type?: string } }).originalError.code;
          const type = (err as { originalError: { code?: number; type?: string } }).originalError.type;
          if (code === 10 || code === 200 || type === 'OAuthException') {
            errorMessage = t('metaDashboard.posts.permissionError', 'Permission error: Please refresh your Meta permissions and try again.');
          } else if (code === 4 || code === 17) {
            errorMessage = t('metaDashboard.posts.rateLimitError', {
              defaultValue: 'Facebook API rate limit reached. Please wait and try again later.'
            });
          }
        }
        setError(errorMessage);
        setPosts([]);
        setPostMetrics({});
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [page, timeRange, useMockData, t]);

  // Calculate engagement distribution for chart labels
  const calculateEngagementDistribution = useCallback(() => {
    if (!posts || posts.length === 0) return [];
    let totalLikes = 0, totalComments = 0, totalShares = 0, totalSaved = 0;
    posts.forEach(post => {
      const metrics = postMetrics[post.id] || {};
      totalLikes += metrics.likes || 0;
      totalComments += metrics.comments || 0;
      totalShares += metrics.shares || 0;
      totalSaved += metrics.saved || 0;
    });
    // Use i18n.language to ensure labels update on language change
    void i18n.language;
    if (page?.platform === 'instagram') {
      return [
        { name: t('metaDashboard.posts.likesLabel', 'Likes'), value: totalLikes, color: '#00A3FF' },
        { name: t('metaDashboard.posts.commentsLabel', 'Comments'), value: totalComments, color: '#82b8ff' },
        { name: t('metaDashboard.posts.savedLabel', 'Saved'), value: totalSaved, color: '#cce5ff' }
      ];
    } else {
      return [
        { name: t('metaDashboard.posts.likesLabel', 'Likes'), value: totalLikes, color: '#00A3FF' },
        { name: t('metaDashboard.posts.commentsLabel', 'Comments'), value: totalComments, color: '#82b8ff' },
        { name: t('metaDashboard.posts.sharesLabel', 'Shares'), value: totalShares, color: '#cce5ff' }
      ];
    }
  }, [posts, postMetrics, page, t, i18n.language]);

  // Memoized engagement data for chart labels to update on language change
  const engagementData = useMemo(() => calculateEngagementDistribution(), [calculateEngagementDistribution]);

  // Calculate post type distribution
  const calculatePostTypeDistribution = useCallback(() => {
    if (!posts || posts.length === 0) return [];
    const typeCounts: Record<string, number> = {};
    posts.forEach(post => {
      const type = post.type || 'unknown';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    // Use i18n.language and page to ensure labels update on language or page change
    void i18n.language;
    void page;
    const colors = ['#00A3FF', '#82b8ff', '#cce5ff', '#e6f3ff', '#f5faff'];
    return Object.entries(typeCounts).map(([type, count], index) => ({
      name: t(`metaDashboard.posts.type.${type.toLowerCase()}`, type.charAt(0).toUpperCase() + type.slice(1)),
      value: count,
      color: colors[index % colors.length]
    }));
  }, [posts, page, t, i18n.language]);

  // Memoized post type data for chart labels to update on language change
  const postTypeData = useMemo(() => calculatePostTypeDistribution(), [calculatePostTypeDistribution]);

  // Calculate top performing posts
  const getTopPerformingPosts = () => {
    if (posts.length === 0 || Object.keys(postMetrics).length === 0) return [];
    
    // Calculate total engagement for each post
    const postsWithEngagement = posts.map(post => {
      const metrics = postMetrics[post.id] || { likes: 0, comments: 0, shares: 0, saved: 0 };
      
      // Calculate total engagement based on platform
      const totalEngagement = page?.platform === 'instagram'
        ? (metrics.likes || 0) + (metrics.comments || 0) + (metrics.saved || 0)
        : (metrics.likes || 0) + (metrics.comments || 0) + (metrics.shares || 0);
      
      return {
        ...post,
        totalEngagement,
        metrics
      };
    });
    
    // Sort by total engagement
    return postsWithEngagement
      .sort((a, b) => b.totalEngagement - a.totalEngagement)
      .slice(0, 5); // Top 5 posts
  };

  const topPosts = getTopPerformingPosts();

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Truncate message for display
  const truncateMessage = (message: string, maxLength = 100) => {
    if (!message) return '';
    return message.length > maxLength
      ? `${message.substring(0, maxLength)}...`
      : message;
  };

  return (
    <Card variant="outlined" sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" component="div" gutterBottom>
            {t(page?.platform === 'instagram' ? 'metaDashboard.posts.instagramTitle' : 'metaDashboard.posts.facebookTitle', page?.platform === 'instagram' ? 'Instagram Posts Analytics' : 'Facebook Posts Analytics')}
          </Typography>
          <TimeRangeFilter 
            value={timeRange}
            onChange={setTimeRange}
            presets={getMetaTimeRangePresets(page?.platform || 'facebook', 'content')}
            allowCustom={true}
          />
        </Box>

        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : posts.length === 0 ? (
          <Alert 
            severity="info" 
            sx={{ mb: 2 }}
          >
            {t('metaDashboard.posts.noPostsInRange', { defaultValue: `No posts found in the selected date range (${formatDate(timeRange?.since || '')} - ${formatDate(timeRange?.until || '')}). Try adjusting the time period or check that the account has published content during this period.`, startDate: formatDate(timeRange?.since || ''), endDate: formatDate(timeRange?.until || '') })}
          </Alert>
        ) : (
          <>
            <Grid container spacing={3}>
              {/* Summary */}
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">{t('metaDashboard.posts.totalPostsLabel', 'Total Posts')}</Typography>
                      <Typography variant="h6">{posts.length}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">{t('metaDashboard.posts.avgEngagementLabel', 'Avg. Engagement')}</Typography>
                      <Typography variant="h6">
                        {posts.length > 0 
                          ? (engagementData.reduce((sum, item) => sum + item.value, 0) / posts.length).toFixed(1) 
                          : '0'}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">{t('metaDashboard.posts.periodLabel', 'Period')}</Typography>
                      <Typography variant="h6">
                        {`${formatDate(timeRange?.since || '')} - ${formatDate(timeRange?.until || '')}`}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Charts */}
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: 300 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('metaDashboard.posts.engagementDistributionTitle', 'Engagement Distribution')}
                  </Typography>
                  {engagementData.length > 0 ? (
                    <ChartContainer width="100%" height="90%" minHeight={250}>
                      <PieChart>
                        <Pie
                          data={engagementData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {engagementData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Legend />
                        <RechartsTooltip />
                      </PieChart>
                    </ChartContainer>
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '90%' }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('metaDashboard.posts.noEngagementData', 'No engagement data available')}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, height: 300 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('metaDashboard.posts.postTypeDistributionTitle', 'Post Type Distribution')}
                  </Typography>
                  {postTypeData.length > 0 ? (
                    <ChartContainer width="100%" height="90%" minHeight={250}>
                      <PieChart>
                        <Pie
                          data={postTypeData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {postTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Legend />
                        <RechartsTooltip />
                      </PieChart>
                    </ChartContainer>
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '90%' }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('metaDashboard.posts.noPostTypeData', 'No post type data available')}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>

              {/* Top Performing Posts */}
              <Grid item xs={12}>
                <Paper variant="outlined">
                  <Box sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {t('metaDashboard.posts.topPerformingPostsTitle', 'Top Performing Posts')}
                    </Typography>
                  </Box>
                  {topPosts.length > 0 ? (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>{t('metaDashboard.posts.tableHeaderPost', 'Post')}</TableCell>
                            <TableCell>{t('metaDashboard.posts.tableHeaderDate', 'Date')}</TableCell>
                            <TableCell>{t('metaDashboard.posts.tableHeaderType', 'Type')}</TableCell>
                            <TableCell align="right">{t('metaDashboard.posts.tableHeaderLikes', 'Likes')}</TableCell>
                            <TableCell align="right">{t('metaDashboard.posts.tableHeaderComments', 'Comments')}</TableCell>
                            {page?.platform === 'instagram' ? (
                              <TableCell align="right">{t('metaDashboard.posts.tableHeaderSaved', 'Saved')}</TableCell>
                            ) : (
                              <TableCell align="right">{t('metaDashboard.posts.tableHeaderShares', 'Shares')}</TableCell>
                            )}
                            <TableCell align="right">{t('metaDashboard.posts.tableHeaderTotal', 'Total')}</TableCell>
                            <TableCell align="right">{t('metaDashboard.posts.tableHeaderLink', 'Link')}</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {topPosts.map((post) => (
                            <TableRow key={post.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  {post.attachments && post.attachments.length > 0 && post.attachments[0].url && (
                                    <Avatar 
                                      src={post.attachments[0].url} 
                                      variant="rounded" 
                                      sx={{ width: 40, height: 40, mr: 1 }}
                                    />
                                  )}
                                  <Typography variant="body2">
                                    {truncateMessage(post.message || '')}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>{formatDate(post.created_time)}</TableCell>
                              <TableCell>
                                <Chip 
                                  label={t(`metaDashboard.posts.type.${(post.type || 'unknown').toLowerCase()}`, post.type ? post.type.charAt(0).toUpperCase() + post.type.slice(1) : t('common.unknown', 'Unknown'))} 
                                  size="small" 
                                  color="primary" 
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell align="right">{post.metrics.likes || 0}</TableCell>
                              <TableCell align="right">{post.metrics.comments || 0}</TableCell>
                              {page?.platform === 'instagram' ? (
                                <TableCell align="right">{post.metrics.saved || 0}</TableCell>
                              ) : (
                                <TableCell align="right">{post.metrics.shares || 0}</TableCell>
                              )}
                              <TableCell align="right">{post.totalEngagement || 0}</TableCell>
                              <TableCell align="right">
                                {post.permalink_url && (
                                  <Tooltip title={t('metaDashboard.posts.openPostTooltip', "Open post")}>
                                    <IconButton 
                                      size="small" 
                                      href={post.permalink_url} 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                    >
                                      <OpenInNewIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('metaDashboard.posts.noTopPerformingPosts', 'No posts available for the selected time period')}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </>
        )}
      </CardContent>
    </Card>
  );
}; 