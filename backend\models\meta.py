from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field

class MetaPageInsights(BaseModel):
    page_views: int = 0
    page_likes: int = 0
    estimated_engagement: int = 0  # Updated from deprecated post_engagement
    reach: int = 0

class MetaPage(BaseModel):
    id: str
    name: str
    access_token: str
    category: str
    platform: str = Field(default="facebook", description="Platform type: 'facebook' or 'instagram'")
    followers_count: Optional[int] = None
    profile_picture_url: Optional[str] = None
    facebook_page_id: Optional[str] = None  # For Instagram accounts, the linked Facebook page ID

class MetaBusinessData(BaseModel):
    business_id: str
    pages: List[MetaPage]

class MetaIntegration(BaseModel):
    connected: bool = False
    business_id: str
    pages: List[MetaPage] = []
    last_sync: Optional[datetime] = None
    insights: MetaPageInsights = MetaPageInsights()

# Models for posts and engagement
class MetaAttachment(BaseModel):
    media_type: str
    url: str

class MetaPost(BaseModel):
    id: str
    message: Optional[str] = None
    created_time: str
    permalink_url: str
    type: str = "status"
    platform: str
    attachments: Optional[List[MetaAttachment]] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None

class MetaComment(BaseModel):
    id: str
    post_id: str
    message: str
    created_time: str
    from_id: str
    from_name: str

class PostMetrics(BaseModel):
    post_id: str
    likes: int = 0
    comments: int = 0
    shares: int = 0
    impressions: int = 0
    reach: int = 0
    engagement_rate: float = 0.0
    saved: Optional[int] = None  # Instagram specific

class EngagementMetrics(BaseModel):
    total_engagement: int = 0
    engagement_rate: float = 0.0
    likes: int = 0
    comments: int = 0
    shares: int = 0
    period: str = "30d"
    daily_engagement: List[Dict[str, Any]] = []

# Models for audience and demographics
class AgeGenderBreakdown(BaseModel):
    age_range: str
    male: int = 0
    female: int = 0
    other: int = 0

class LocationBreakdown(BaseModel):
    country: str
    city: Optional[str] = None
    count: int = 0
    percentage: float = 0.0

class FollowerDemographics(BaseModel):
    age_gender: List[AgeGenderBreakdown] = []
    locations: List[LocationBreakdown] = []
    interests: List[Dict[str, Any]] = []

class FollowerGrowth(BaseModel):
    date: str
    followers_count: int = 0
    new_followers: int = 0
    unfollows: int = 0

class FollowerData(BaseModel):
    total_followers: int = 0
    growth_rate: float = 0.0
    daily_growth: List[FollowerGrowth] = []
    demographics: Optional[FollowerDemographics] = None

# Models for ad performance
class AdAccountSummary(BaseModel):
    id: str
    name: str
    status: str
    spend: float = 0.0
    impressions: int = 0
    clicks: int = 0

class AdCampaignMetrics(BaseModel):
    spend: float = 0.0
    impressions: int = 0
    reach: int = 0
    clicks: int = 0
    conversions: int = 0
    ctr: float = 0.0
    cpc: float = 0.0
    cost_per_conversion: Optional[float] = None
    roi: Optional[float] = None

class AdCampaign(BaseModel):
    id: str
    name: str
    status: str
    objective: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    budget: float = 0.0
    metrics: AdCampaignMetrics = AdCampaignMetrics()

class DailyAdMetrics(BaseModel):
    date: str
    spend: float = 0.0
    impressions: int = 0
    reach: int = 0
    clicks: int = 0
    conversions: int = 0
    ctr: float = 0.0
    cpc: float = 0.0

class AdMetricsOverview(BaseModel):
    total_spend: float = 0.0
    total_impressions: int = 0
    total_reach: int = 0
    total_clicks: int = 0
    total_conversions: int = 0
    average_cpc: float = 0.0
    average_ctr: float = 0.0
    average_cost_per_conversion: float = 0.0
    average_roi: float = 0.0
    roas: float = 0.0

class AdMetricsResponse(BaseModel):
    overview: AdMetricsOverview = AdMetricsOverview()
    daily_metrics: List[DailyAdMetrics] = []
    campaigns: List[AdCampaign] = []

# Models for insights
# Note: This MetaInsight model appears unused or potentially shadowed by a
# class with the same name in models.insights.py. Direct database interactions
# in services seem to handle the full meta_insights collection structure directly.
class MetaInsight(BaseModel):
    id: str
    type: str
    title: str
    description: str
    created_at: str
    value: Any
    trend: Optional[float] = None
    is_positive: bool = True

class MetaChatContext(BaseModel):
    """Model for storing Meta data optimized for chat context"""
    store_id: str
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    pages: List[Dict[str, Any]] = Field(default_factory=list)
    insights: List[Dict[str, Any]] = Field(default_factory=list)
    engagement_metrics: Dict[str, Any] = Field(default_factory=dict)
    audience_metrics: Dict[str, Any] = Field(default_factory=dict)
    ad_metrics: Dict[str, Any] = Field(default_factory=dict)
    is_mock_data: bool = False

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PermissionChange(BaseModel):
    """Model for tracking individual permission changes"""
    permission: str
    status: str  # "granted" or "revoked"
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class MetaPermissions(BaseModel):
    """Enhanced model for Meta permissions with history tracking"""
    store_id: str
    business_id: Optional[str] = None
    permissions: Dict[str, str] = Field(default_factory=dict)  # Current status: permission_name -> status
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    revocation_history: List[PermissionChange] = Field(default_factory=list)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class AdPerformance(BaseModel):
    """Model for individual ad performance metrics"""
    campaign_id: str
    date: str
    impressions: int = 0
    reach: int = 0
    clicks: int = 0
    ctr: float = 0.0
    cpc: float = 0.0
    spend: float = 0.0
    conversions: int = 0
    cost_per_conversion: float = 0.0
    roi: float = 0.0
    publisher_platform: Optional[str] = None
    views: Optional[int] = None

class AdPerformanceRequest(BaseModel):
    """Model for the campaign performance POST request"""
    performance: List[AdPerformance]

