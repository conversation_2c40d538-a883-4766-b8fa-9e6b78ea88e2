import React from 'react'
import ReactDOM from 'react-dom/client'
import Router from './Router'
import { AuthProvider } from './contexts/AuthContext'
import { MetaPermissionsProvider } from './contexts/MetaPermissionsContext'
import { FeedbackProvider } from './contexts/FeedbackContext'
import { GoogleOAuthProvider } from '@react-oauth/google'
import { initMetaSDK } from './services/init'
import { AppThemeProvider } from './contexts/ThemeContext'
import FeedbackModal from './components/Feedback/FeedbackModal'
import './config/axios'
import './App.css'
import './index.css'
import { validateMetaConfig } from './services/config.ts'
import { logger } from './utils/logger';
import './i18n';
import GlobalAppSpinner from './components/common/GlobalAppSpinner';

// Import Recharts override FIRST - before any other imports that might use Recharts
import './utils/logerrors';

// Log environment information in development mode
if (import.meta.env.DEV) {
  logger.debug('Running in development mode');
  logger.info('API URL:', import.meta.env.VITE_API_URL);
  logger.info('Frontend URL:', import.meta.env.VITE_FRONTEND_URL);
  
  // Add debug logging for API requests in development
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const url = args[0];
    
    // Handle Facebook SDK tracking requests that might be blocked by ad blockers
    if (typeof url === 'string' && url.includes('facebook.com/platform/impression.php')) {
      return originalFetch.apply(this, args).catch((error) => {
        // Silently handle blocked Facebook tracking requests
        if (error.message?.includes('Failed to fetch') || error.name === 'TypeError') {
          // Return a resolved promise to prevent error propagation
          return Promise.resolve(new Response('', { status: 204 }));
        }
        throw error;
      });
    }
    
    return originalFetch.apply(this, args);
  };
}

const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
logger.info('Google OAuth Client ID:', clientId);
if (!clientId) {
  throw new Error('VITE_GOOGLE_CLIENT_ID is not defined in environment variables');
}

// Initialize Meta SDK with a delay to ensure the DOM is fully loaded
logger.debug('Initializing Meta SDK from main.tsx');
setTimeout(() => {
  try {
    initMetaSDK();
    logger.info('Meta SDK initialization completed');
  } catch (error) {
    logger.error('Error initializing Meta SDK:', error);
  }
}, 500);

// Validate Meta configuration before app starts
validateMetaConfig();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <AppThemeProvider>
      <GoogleOAuthProvider clientId={clientId}>
        <FeedbackProvider>
          <AuthProvider>
            <MetaPermissionsProvider>
              <React.Suspense fallback={<GlobalAppSpinner />}>
                <Router />
              </React.Suspense>
              <FeedbackModal />
            </MetaPermissionsProvider>
          </AuthProvider>
        </FeedbackProvider>
      </GoogleOAuthProvider>
    </AppThemeProvider>
  </React.StrictMode>,
)

