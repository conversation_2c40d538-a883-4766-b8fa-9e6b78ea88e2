import React from 'react';import {  Box,  Paper,  Typography,  Button,  CircularProgress} from '@mui/material';import useMetaAuth from '../../hooks/useMetaAuth';import { logger } from '../../utils/logger';

interface MetaAuthRequiredProps {
  title?: string;
  message?: string;
  onLoginSuccess?: () => void;
  showLoginButton?: boolean;
}

/**
 * Component to show when Meta authentication is required
 * Displays a message and optionally a login button
 */
export const MetaAuthRequired: React.FC<MetaAuthRequiredProps> = ({
  title = 'Authentication Required',
  message = 'You need to connect to Meta to access this data',
  onLoginSuccess,
  showLoginButton = true
}) => {
  const { login, isCheckingAuth, error } = useMetaAuth();

  const handleLogin = async () => {
    try {
      const result = await login();
      if (result && onLoginSuccess) {
        onLoginSuccess();
      }
    } catch (err) {
      logger.error('Login failed:', err);
    }
  };

  return (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 3, 
        textAlign: 'center',
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px'
      }}
    >
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        {message}
      </Typography>
      
      {error && (
        <Typography color="error" variant="body2" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}
      
      {showLoginButton && (
        <Box sx={{ mt: 2 }}>
          {isCheckingAuth ? (
            <CircularProgress size={24} />
          ) : (
            <Button 
              variant="contained" 
              color="primary"
              onClick={handleLogin}
              startIcon={<img src="/meta-logo.png" alt="Meta" width={18} height={18} />}
            >
              Continue with Meta
            </Button>
          )}
        </Box>
      )}
    </Paper>
  );
}; 