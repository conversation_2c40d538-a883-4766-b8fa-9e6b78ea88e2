import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Button,
    IconButton,
    Typography,
    Box,
    CircularProgress
} from '@mui/material';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt';
import { useFeedback } from '../../contexts/feedback.context';
import { useAuth } from '../../contexts/AuthContext';
import { useSubmitChatFeedback } from '../../services/apiService';
import { logger } from '../../utils/logger';
import { useTranslation } from 'react-i18next';

const FeedbackModal: React.FC = () => {
  const { isFeedbackModalOpen, closeFeedbackModal, isLogoutIntent } = useFeedback();
  const { user, performLogoutActions } = useAuth(); // Get user object instead
  const { t } = useTranslation();
  const [feedbackText, setFeedbackText] = useState('');
  const [likes, setLikes] = useState(0);
  const [dislikes, setDislikes] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [likeClicked, setLikeClicked] = useState(false);
  const [dislikeClicked, setDislikeClicked] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null); // Add timeout ref
  const [modalStoreId, setModalStoreId] = useState<string | number | null>(null); // Add state for modal-specific storeId

  // Log when component mounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // --- Add logging for modal open state ---
  useEffect(() => {
    if (isFeedbackModalOpen) {
      // Reset state when modal opens
      setFeedbackText('');
      setLikes(0);
      setDislikes(0);
      setError(null);
      setIsSubmitting(false);
      setLikeClicked(false);
      setDislikeClicked(false);
      // Capture storeId from user context
      if (user && user.id_store) {
          setModalStoreId(user.id_store);
      } else {
          setModalStoreId(null); // Ensure it's reset if user is somehow missing
      }
    }
  }, [isFeedbackModalOpen, isLogoutIntent, user]); // Updated dependencies
  // ----------------------------------------

  // --- Add useEffect for automatic logout timer ---
  useEffect(() => {
    // Clear any existing timer when effect runs
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (isFeedbackModalOpen && isLogoutIntent) {
      timeoutRef.current = setTimeout(() => {
        performLogoutActions();
        closeFeedbackModal();
      }, 5000);
    }

    // Cleanup function to clear timer on unmount or if modal closes/intent changes
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isFeedbackModalOpen, isLogoutIntent, performLogoutActions, closeFeedbackModal]);
  // -----------------------------------------------

  // --- Add handler to stop timer on interaction ---
  const handleInteraction = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };
  // ------------------------------------------------

  const handleLike = useCallback(() => {
    setLikes(prev => (likeClicked ? prev -1 : prev + 1));
    setLikeClicked(!likeClicked);
    // If dislike was clicked, unclick it
    if (dislikeClicked) {
        setDislikes(prev => prev - 1);
        setDislikeClicked(false);
    }
  }, [likeClicked, dislikeClicked]);

  const handleDislike = useCallback(() => {
    setDislikes(prev => (dislikeClicked ? prev - 1 : prev + 1));
    setDislikeClicked(!dislikeClicked);
    // If like was clicked, unclick it
    if (likeClicked) {
        setLikes(prev => prev - 1);
        setLikeClicked(false);
    }
  }, [dislikeClicked, likeClicked]);

  const submitChatFeedback = useSubmitChatFeedback();
  const handleSubmit = async () => {
    if (!feedbackText.trim()) {
      setError(t('feedbackModal.errorEmptyText'));
      return;
    }
    if (!modalStoreId) {
      setError(t('feedbackModal.errorNoStore'));
      return;
    }
    setIsSubmitting(true);
    setError(null);
    try {
      await submitChatFeedback({
        feedback_text: feedbackText,
        likes: likes,
        dislikes: dislikes,
        source: 'dashboard' // Map modal/logout intent to 'dashboard'
      });

      if (isLogoutIntent) {
        performLogoutActions();
      }

      closeFeedbackModal();
    } catch (err) {
      logger.error('Failed to submit feedback:', err);
      setError(t('feedbackModal.errorSubmitFailed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      closeFeedbackModal();
    }
  }, [isSubmitting, closeFeedbackModal]);

  useEffect(() => {
    if (!isFeedbackModalOpen && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, [isFeedbackModalOpen, timeoutRef]);

  return (
    <Dialog open={isFeedbackModalOpen} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('feedbackModal.title')}</DialogTitle>
      <DialogContent onClick={handleInteraction}>
        <TextField
          autoFocus
          margin="dense"
          id="feedback"
          label={t('feedbackModal.textFieldLabel')}
          type="text"
          fullWidth
          variant="outlined"
          multiline
          rows={4}
          value={feedbackText}
          onChange={(e) => setFeedbackText(e.target.value)}
          error={!!error}
          helperText={error}
        />
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
          <IconButton onClick={handleLike} color={likeClicked ? "primary" : "default"} disabled={isSubmitting}>
            <ThumbUpAltIcon />
          </IconButton>
          <Typography sx={{ mr: 3 }}>{likes}</Typography>
          <IconButton onClick={handleDislike} color={dislikeClicked ? "secondary" : "default"} disabled={isSubmitting}>
            <ThumbDownAltIcon />
          </IconButton>
          <Typography>{dislikes}</Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="secondary" disabled={isSubmitting}>{t('feedbackModal.cancelButton')}</Button>
        <Button onClick={handleSubmit} color="primary" disabled={isSubmitting}>
          {isSubmitting ? <CircularProgress size={24} /> : t('feedbackModal.submitButton')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FeedbackModal; 