import { logger } from '../utils/logger';

export const environment = {
    apiUrl: import.meta.env.VITE_API_URL || 'https://127.0.0.1:8000',
    prodApiUrl: import.meta.env.VITE_PROD_API_URL || 'https://d1w62wlepuin1r.cloudfront.net',
    appEnv: import.meta.env.VITE_APP_ENV || 'development',
    frontendUrl: import.meta.env.VITE_FRONTEND_URL || 'https://d37am3rrp4a9iv.cloudfront.net',
    isProduction: import.meta.env.VITE_APP_ENV === 'production',
    isDevelopment: import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'development',
    disableCsrf: import.meta.env.VITE_DISABLE_CSRF === 'true'
};

// Validate production configuration
if (environment.isProduction) {
    if (!environment.prodApiUrl) {
        logger.error('Production API URL not configured! Check VITE_PROD_API_URL environment variable.');
    }
    if (!environment.frontendUrl) {
        logger.warn('Frontend URL not configured! Check VITE_FRONTEND_URL environment variable.');
    }
}

// Log environment configuration during development
if (environment.isDevelopment) {
    logger.debug('Environment Configuration:', {
        apiUrl: environment.apiUrl,
        prodApiUrl: environment.prodApiUrl,
        appEnv: environment.appEnv,
        frontendUrl: environment.frontendUrl,
        isProduction: environment.isProduction,
        isDevelopment: environment.isDevelopment
    });
} 