# NOTE: All database operations in this file must use db_analysis exclusively, except for login via db_main['store_users'].
# User/store info (name, email, business_type, etc.) must be fetched from db_analysis['active_stores_cache'] only.
# Fallback/default structures must reference only fields available in allowed db_analysis collections.

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
import asyncio
import ast # Import ast module for safe literal evaluation
import re # Added missing import

# Corrected Imports (Assuming standard project structure)
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, BulkWriteError, OperationFailure
from config.database import db_analysis
from models.feedback import ChatFeedbackInput # Import the model
from config.settings import Settings # Corrected from config.config
import traceback
from bson import ObjectId
from pymongo import DESCENDING
from utils.serialization import serialize_mongo_doc
from pymongo import ReturnDocument  # Add import for atomic credit deduction

# Configure logging using standard Python logging
logger = logging.getLogger(__name__)

settings = Settings()

async def get_store_context(store_id: str) -> dict:
    """Get context for a store, including analysis, store details, and shipping methods (store-specific and platform-wide)."""
    try:
        # Fetch main analysis and store details in parallel
        store_analysis_task = asyncio.create_task(
            db_analysis["global_analysis"].find_one({"_id": store_id})
        )
        active_store_task = asyncio.create_task(
            db_analysis["active_stores_cache"].find_one({"_id": store_id})
        )
        platform_methods_task = asyncio.create_task(get_platform_shipping_methods())

        store_analysis = await store_analysis_task
        active_store = await active_store_task
        platform_shipping_methods = await platform_methods_task
        platform_methods_by_id = {m.get("id"): m for m in platform_shipping_methods}

        if not store_analysis:
            logger.warning(f"Store analysis not found for store ID: {store_id}")
            # Fallback: Use only fields available in active_stores_cache
            store_shipping_methods = (active_store or {}).get("shipping_methods", [])
            merged_store_shipping_methods = []
            for method in store_shipping_methods:
                method_id = method.get("id")
                platform_info = platform_methods_by_id.get(method_id)
                merged = dict(method)
                if platform_info:
                    merged.update({k: v for k, v in platform_info.items() if k not in merged or not merged[k]})
                    merged["is_custom"] = False
                else:
                    merged["is_custom"] = True
                merged_store_shipping_methods.append(merged)
            logger.info(f"Returning fallback context with {len(merged_store_shipping_methods)} store and {len(platform_shipping_methods)} platform shipping methods.")
            return {
                "store": {
                    "name": (active_store or {}).get("name", "Store Analysis Pending"),
                    "business_type": (active_store or {}).get("business_type", "Tienda de comestibles"),
                    "country": (active_store or {}).get("country", {"name": "Unknown", "code": ""})
                },
                "metrics": (active_store or {}).get("metrics", {
                    "customer_count": 0,
                    "order_count": 0,
                    "product_count": 0,
                    "total_revenue": 0,
                    "avg_order_value": 0
                }),
                "order_statistics": {},
                "currency": (active_store or {}).get("currency", {
                    "code": "USD",
                    "name": "US Dollar",
                    "symbol": "$"
                }),
                "analysis": {
                    "summary": "No analysis available yet.",
                    "metrics": "",
                    "customer_analysis": "",
                    "product_performance": "",
                    "market_position": "",
                    "recommendations": ""
                },
                "shipping_methods": {
                    "store_shipping_methods": merged_store_shipping_methods,
                    "platform_shipping_methods": platform_shipping_methods
                }
            }

        # Log the found analysis for debugging
        logger.info(f"Found store analysis with metrics: {store_analysis.get('metrics', {})}")
        logger.info(f"Store currency info: {store_analysis.get('currency', {})}")

        # Merge store shipping methods with platform details
        store_shipping_methods = store_analysis.get("store", {}).get("shipping_methods", [])
        merged_store_shipping_methods = []
        for method in store_shipping_methods:
            method_id = method.get("id")
            platform_info = platform_methods_by_id.get(method_id)
            merged = dict(method)
            if platform_info:
                merged.update({k: v for k, v in platform_info.items() if k not in merged or not merged[k]})
                merged["is_custom"] = False
            else:
                merged["is_custom"] = True
            merged_store_shipping_methods.append(merged)
        logger.info(f"Returning context with {len(merged_store_shipping_methods)} store and {len(platform_shipping_methods)} platform shipping methods.")
        # Add shipping_methods to the returned context
        store_analysis["shipping_methods"] = {
            "store_shipping_methods": merged_store_shipping_methods,
            "platform_shipping_methods": platform_shipping_methods
        }

        # Clean placeholder strings from social_media values
        if "social_media" in store_analysis and isinstance(store_analysis["social_media"], dict):
            cleaned_social_media = {}
            # Define the placeholder prefix(es) to check against
            placeholder_prefixes = ("Suggest to Add",) 
            for platform, value in store_analysis["social_media"].items():
                # Check if the value is a string and starts with a known placeholder (case-insensitive check might be safer but sticking to plan)
                if isinstance(value, str) and value.strip().startswith(placeholder_prefixes):
                    cleaned_social_media[platform] = None # Replace placeholder with None
                else:
                    # Keep valid URLs or potentially other non-placeholder values
                    cleaned_social_media[platform] = value
            # Replace the original social_media dict with the cleaned one
            store_analysis["social_media"] = cleaned_social_media

        return store_analysis

    except Exception as e:
        logger.error(f"Error getting store context: {str(e)}")
        return {}

async def get_store_analysis(store_id: str) -> Optional[Dict[str, Any]]:
    """Get store analysis data, robustly merging from multiple collections."""
    try:
        # Fetch data from multiple collections in parallel for efficiency
        tasks = [
            db_analysis["global_analysis"].find_one({"_id": store_id}),
            db_analysis["store_customers_cache"].find_one({"_id": store_id}),
            db_analysis["active_stores_cache"].find_one({"_id": store_id}),
            db_analysis["store_activity_metrics"].find_one({"_id": store_id}),
            db_analysis["product_details_cache"].find_one({"_id": store_id}) # Fetch product cache for ratings/category summary
        ]
        
        results = await asyncio.gather(*tasks)
        store_analysis = results[0]
        customer_cache = results[1]
        active_store = results[2]
        store_activity = results[3]
        product_cache = results[4] # Now explicitly fetched
        
        # If global_analysis is missing, create a default structure
        if not store_analysis:
            logger.warning(f"Store analysis not found for store ID: {store_id}. Initializing default structure.")
            store_analysis = {
                "_id": store_id,
                "analysis": {},
                "metrics": {},
                "store": {},
                "customers": {},
                "currency": {},
                "social_media": {},
                "feedback": []
            }
            
        # Ensure core dictionaries exist before merging
        for key, default in [
            ("analysis", {}), ("metrics", {}), ("store", {}), 
            ("customers", {}), ("currency", {}), ("social_media", {})
        ]:
            if key not in store_analysis or not isinstance(store_analysis.get(key), dict):
                store_analysis[key] = {}
                
        if "feedback" not in store_analysis or not isinstance(store_analysis.get("feedback"), list):
            store_analysis["feedback"] = []

        # If shipping_analysis is present in the global_analysis.analysis object, 
        # extract it to include in the response
        global_analysis_shipping = store_analysis.get("analysis", {}).get("shipping_analysis", {})
        
        # Create a proper structure if shipping_analysis is not already a dictionary
        if global_analysis_shipping and not isinstance(global_analysis_shipping, dict):
            # If it's a string (like the default "Shipping analysis pending"), create a dictionary
            store_analysis["analysis"]["shipping_analysis"] = {
                "analysis_text": global_analysis_shipping,
                "recommendations": []
            }
        elif "shipping_analysis" not in store_analysis["analysis"]:
            # Ensure the key exists with default empty values
            store_analysis["analysis"]["shipping_analysis"] = {
                "analysis_text": "No shipping analysis available yet.",
                "recommendations": []
            }
            
        # Include customer_cache data if available
        if customer_cache:
            logger.debug(f"Merging data from store_customers_cache for store {store_id}")
            # Ensure analysis.demographics structure exists
            if "demographics" not in store_analysis["analysis"] or not isinstance(store_analysis["analysis"].get("demographics"), dict):
                store_analysis["analysis"]["demographics"] = {}
            
            # Transform country_distribution data
            if "country_distribution" in customer_cache and isinstance(customer_cache.get("country_distribution"), dict):
                countries = []
                total_customers_from_cache = customer_cache.get("total_customers", 0)
                for country_name, country_data in customer_cache["country_distribution"].items():
                    # Handle cases where country_data might be just a count (older format?) or a dict
                    count = 0
                    cities_dict = {}
                    if isinstance(country_data, dict):
                        count = country_data.get("count", 0)
                        cities_dict = country_data.get("cities", {})
                    elif isinstance(country_data, (int, float)):
                        count = int(country_data)
                        # No city data available in this format
                        
                    if count > 0:
                        cities_data_list = []
                        if isinstance(cities_dict, dict):
                             for city_name, city_count in cities_dict.items():
                                if city_count > 0:
                                    city_percentage = (city_count / count) * 100 if count > 0 else 0
                                    cities_data_list.append({
                                        "name": city_name,
                                        "percentage": round(city_percentage, 1)
                                    })
                        
                        country_percentage = (count / total_customers_from_cache) * 100 if total_customers_from_cache > 0 else 0
                        
                        countries.append({
                            "name": country_name,
                            "percentage": round(country_percentage, 1),
                            "cities": cities_data_list
                        })
                
                # Sort countries by percentage descending
                countries.sort(key=lambda x: x["percentage"], reverse=True)
                store_analysis["analysis"]["demographics"]["countries"] = countries
            elif "countries" not in store_analysis["analysis"]["demographics"]:
                 store_analysis["analysis"]["demographics"]["countries"] = [] # Ensure it exists even if no data
            
            # Add enhanced customer metrics safely using .get()
            store_analysis["customers"].update({
                "total": customer_cache.get("total_customers", 0),
                "with_orders": customer_cache.get("customers_with_orders_count", 0), # May be missing
                "repeat": customer_cache.get("repeat_customers_count", 0), # May be missing
                "abandoned_cart_count": customer_cache.get("abandoned_cart_count", 0),
                "abandoned_cart_total_value": customer_cache.get("abandoned_cart_total_value", 0),
                "pending_cart_count": customer_cache.get("pending_cart_count", 0),
                "pending_cart_total_value": customer_cache.get("pending_cart_total_value", 0),
                "average_spend": customer_cache.get("average_spend_per_customer", 0),
                "payment_method_distribution": customer_cache.get("payment_method_distribution", {}), 
                "shipping_method_distribution": customer_cache.get("shipping_method_distribution", {}),
                "coupon_code_distribution": customer_cache.get("coupon_code_distribution", {}), 
                "status_distribution": customer_cache.get("status_distribution", {}),
                "customers": customer_cache.get("customers", []), # Added customer list
                # --- Most Frequent Fields START ---
                "most_frequent_coupon_code": customer_cache.get("most_frequent_coupon_code", {"code": None, "count": 0}),
                "most_frequent_payment_method": customer_cache.get("most_frequent_payment_method", {"name": None, "count": 0}),
                "most_frequent_shipping_method": customer_cache.get("most_frequent_shipping_method", {"name": None, "count": 0}),
                # --- Most Frequent Fields END ---
                # --- ADDED FIELDS START ---
                "total_customers": customer_cache.get("total_customers", 0),
                "total_store_orders": customer_cache.get("total_store_orders", 0),
                "average_spend_per_customer": customer_cache.get("average_spend_per_customer", 0),
                "abandoned_cart_customer_count": customer_cache.get("abandoned_cart_customer_count", 0),
                "country_distribution": customer_cache.get("country_distribution", {}),
                # --- ADDED FIELDS END ---
            })
            # Also update top-level metrics.customer_count if available
            if "total_customers" in customer_cache:
                store_analysis["metrics"]["customer_count"] = customer_cache["total_customers"]
        
        # Include active_store data if available
        if active_store:
            logger.debug(f"Merging data from active_stores_cache for store {store_id}")
            # Update basic store information safely using .get()
            store_analysis["store"]["name"] = store_analysis["store"].get("name") or active_store.get("name")
            store_analysis["store"]["country"] = store_analysis["store"].get("country") or active_store.get("country")
            store_analysis["store"]["currency"] = store_analysis["store"].get("currency") or active_store.get("currency")
            store_analysis["store"]["business_type"] = store_analysis["store"].get("business_type") or active_store.get("business_type")
             
            # Merge metrics from active_stores_cache comprehensively
            active_metrics = active_store.get("metrics", {})
            if isinstance(active_metrics, dict):
                # Full list of metrics we want from active_stores_cache
                dashboard_metrics_keys = [
                    "total_orders", "total_revenue", "total_revenue_usd", 
                    "average_order_value", "average_order_value_usd", "total_products", 
                    "total_visits", "total_customers", "active_customers", 
                    "customers_with_abandoned_carts"
                ]
                for key in dashboard_metrics_keys:
                    if key in active_metrics:
                        # Use the value from active_stores_cache
                        store_analysis["metrics"][key] = active_metrics[key]
                    elif key not in store_analysis["metrics"]:
                        # Set default if key doesn't exist in metrics yet
                        store_analysis["metrics"][key] = 0
            
            # Add key_dates, keywords, social_media
            if "key_dates" in active_store and isinstance(active_store["key_dates"], dict):
                store_analysis["key_dates"] = active_store["key_dates"]
            # Keywords might be None or empty, handle defensively
            if "keywords" in active_store and active_store["keywords"]:
                store_analysis["keywords"] = active_store["keywords"]
            elif "keywords" not in store_analysis:
                store_analysis["keywords"] = None # Ensure key exists if not in active_store
                
            if "social_media" in active_store and isinstance(active_store["social_media"], dict):
                if "social_media" not in store_analysis or not isinstance(store_analysis["social_media"], dict):
                    store_analysis["social_media"] = {}
                # Merge/overwrite with data from active_store
                store_analysis["social_media"].update(active_store["social_media"])
            
            # Clean placeholder strings from social_media values
            if "social_media" in store_analysis and isinstance(store_analysis["social_media"], dict):
                cleaned_social_media = {}
                # Define the placeholder prefix(es) to check against
                placeholder_prefixes = ("Suggest to Add",) 
                for platform, value in store_analysis["social_media"].items():
                    # Check if the value is a string and starts with a known placeholder (case-insensitive check might be safer but sticking to plan)
                    if isinstance(value, str) and value.strip().startswith(placeholder_prefixes):
                        cleaned_social_media[platform] = None # Replace placeholder with None
                    else:
                        # Keep valid URLs or potentially other non-placeholder values
                        cleaned_social_media[platform] = value
                # Replace the original social_media dict with the cleaned one
                store_analysis["social_media"] = cleaned_social_media
                         
        # Ensure the final customer object exists even if customer_cache was empty
        if not store_analysis.get("customers") or not isinstance(store_analysis["customers"], dict):
            store_analysis["customers"] = {}
        # Always ensure all expected customer fields are present
        customer_defaults = {
            "total": store_analysis.get("metrics", {}).get("customer_count", 0),
            "with_orders": 0,
            "repeat": 0,
            "abandoned_cart_count": 0,
            "abandoned_cart_total_value": 0,
            "pending_cart_count": 0,
            "pending_cart_total_value": 0,
            "average_spend": 0,
            "status_distribution": {},
            "payment_method_distribution": {},
            "shipping_method_distribution": {},
            "coupon_code_distribution": {},
            "customers": [], # Added customer list default
            # --- ADDED DEFAULTS START ---
            "total_customers": 0,
            "total_store_orders": 0,
            "average_spend_per_customer": 0,
            "abandoned_cart_customer_count": 0,
            "country_distribution": {},
            # --- ADDED DEFAULTS END ---
            # --- Most Frequent DEFAULTS START ---
            "most_frequent_coupon_code": {"code": None, "count": 0},
            "most_frequent_payment_method": {"name": None, "count": 0},
            "most_frequent_shipping_method": {"name": None, "count": 0}
            # --- Most Frequent DEFAULTS END ---
        }
        # Merge defaults with any present values (prioritize actual data)
        for k, v in customer_defaults.items():
            if k not in store_analysis["customers"]:
                store_analysis["customers"][k] = v
        
        # Merge store_activity_metrics for recent activity
        if store_activity and isinstance(store_activity.get("activity_metrics"), dict):
            logger.debug(f"Merging data from store_activity_metrics for store {store_id}")
            activity = store_activity["activity_metrics"]
            
            # Define all the expected time-period metrics
            time_period_metrics = {
                "revenue_30d": 0, "revenue_90d": 0, "revenue_365d": 0,
                "order_count_30d": 0, "order_count_90d": 0, "order_count_365d": 0,
                "visit_count_30d": 0, "visit_count_90d": 0, "visit_count_365d": 0
            }
            
            # Merge with defaults for all time-period fields
            for key, default_value in time_period_metrics.items():
                store_analysis["metrics"][key] = activity.get(key, default_value)
                
            # Add traffic_light if available
            if "traffic_light" in activity:
                store_analysis["metrics"]["traffic_light"] = activity["traffic_light"]
                
            # Only a single avg_order_value (not period-specific)
            # NOTE: avg_order_value is now sourced from active_stores_cache, not store_activity_metrics
            # if "avg_order_value" in activity:
            #     store_analysis["metrics"]["avg_order_value"] = activity["avg_order_value"]
        else:
            logger.debug(f"No store_activity_metrics found or invalid for store {store_id}, using defaults")
            # Add default values for crucial metrics that may be missing
            for timeframe in ["30d", "90d", "365d"]:
                for metric_type in ["revenue", "order_count", "visit_count"]:
                    metric_key = f"{metric_type}_{timeframe}"
                    if metric_key not in store_analysis["metrics"]:
                        store_analysis["metrics"][metric_key] = 0
            
            # Ensure traffic_light is present
            if "traffic_light" not in store_analysis["metrics"]:
                store_analysis["metrics"]["traffic_light"] = "grey"  # Default to grey if no data is available
            
        # Fetch meta summary (followers, ad spend)
        meta_summary = {"total_followers": 0, "ad_spend_30d": 0}
        meta_context = await db_analysis["meta_chat_context"].find_one({"store_id": store_id})
        if meta_context:
            # Aggregate followers
            total_followers = 0
            if "pages" in meta_context and isinstance(meta_context["pages"], list):
                for page in meta_context["pages"]:
                    if isinstance(page, dict):
                        total_followers += page.get("followers", 0)
            meta_summary["total_followers"] = total_followers
            
            # Aggregate ad spend for last 30 days
            ad_spend_30d = 0
            if "ad_metrics" in meta_context and "metrics" in meta_context["ad_metrics"] and isinstance(meta_context["ad_metrics"]["metrics"], list):
                now = datetime.utcnow()
                for metric in meta_context["ad_metrics"]["metrics"]:
                    if not isinstance(metric, dict):
                        continue
                        
                    date = metric.get("date")
                    spend = metric.get("spend", 0)
                    if date:
                        try:
                            # --- FIX START: Handle both string and datetime dates ---
                            if isinstance(date, str):
                                metric_date = datetime.strptime(date[:10], "%Y-%m-%d")
                            elif isinstance(date, datetime):
                                # If it's already a datetime, use it directly
                                metric_date = date
                            else:
                                # Skip if the type is unexpected
                                logger.warning(f"Unexpected type for meta metric date: {type(date)}, value: {date}")
                                continue

                            if (now.replace(tzinfo=None) - metric_date.replace(tzinfo=None)).days <= 30:
                            # --- FIX END ---
                                # Spend might be None or non-numeric
                                if isinstance(spend, (int, float)):
                                    ad_spend_30d += spend
                                elif isinstance(spend, str):
                                    try:
                                        ad_spend_30d += float(spend)
                                    except ValueError:
                                        logger.warning(f"Could not convert ad spend '{spend}' to float for store {store_id}")
                        except ValueError:
                            logger.warning(f"Could not parse date '{date}' for ad metric in store {store_id}")
                        except Exception as date_parse_error:
                            logger.warning(f"Error parsing ad metric date '{date}': {date_parse_error} for store {store_id}")
            meta_summary["ad_spend_30d"] = int(ad_spend_30d)
        store_analysis["meta_summary"] = meta_summary
        
        # Merge ratings and category summary from product_cache
        if product_cache:
            logger.debug(f"Merging ratings and category summary from product_details_cache for store {store_id}")
            # Add ratings to metrics object
            store_analysis["metrics"]["store_ratings_count"] = product_cache.get("store_ratings_count", 0)
            store_analysis["metrics"]["store_average_rating"] = product_cache.get("store_average_rating", 0.0)
            
            # Extract products_online and revenue metrics from store_aggregations if available
            if "store_aggregations" in product_cache:
                store_aggregations = product_cache["store_aggregations"]
                products_online = store_aggregations.get("products_online", 0)
                store_analysis["metrics"]["products_online"] = products_online
                
                # Add gross and net revenue metrics
                store_analysis["metrics"]["total_gross_revenue"] = store_aggregations.get("gross_product_revenue", 0)
                store_analysis["metrics"]["total_net_revenue"] = store_aggregations.get("net_revenue_after_discounts", 0)
            
            # Add category summary to analysis object
            if "category_summary" in product_cache and isinstance(product_cache["category_summary"], dict):
                store_analysis["analysis"]["category_summary"] = product_cache["category_summary"]
            elif "category_summary" not in store_analysis["analysis"]:
                 store_analysis["analysis"]["category_summary"] = {} # Ensure key exists
        else:
            logger.debug(f"No product_details_cache found for store {store_id}, using defaults for ratings/category summary")
            store_analysis["metrics"]["store_ratings_count"] = 0
            store_analysis["metrics"]["store_average_rating"] = 0.0
            if "category_summary" not in store_analysis["analysis"]:
                store_analysis["analysis"]["category_summary"] = {}

        return store_analysis
        
    except Exception as e:
        logger.error(f"Error getting store analysis for store {store_id}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}") # Log full traceback
        return None # Return None on error

async def update_store_analysis(store_id: str, update_data: dict):
    """Update store analysis in global_analysis collection"""
    try:
        result = await db_analysis["global_analysis"].update_one(
            {"_id": store_id},
            {"$set": update_data}
        )
        
        if result.modified_count == 0:
            logger.warning(f"No updates made to store analysis for store ID: {store_id}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"Error updating store analysis: {str(e)}")
        return False

async def get_analysis_context(query: str) -> dict:
    """Get relevant analysis context based on the query content"""
    try:
        # Initialize context containers
        context = {
            'store_insights': [],
            'relevant_products': [],
            'relevant_orders': [],
            'relevant_customers': [],
            'relevant_stores': []
        }

        # Get relevant analyses from global_analysis
        analyses = await db_analysis.global_analysis.find(
            {"$text": {"$search": query}},
            {"score": {"$meta": "textScore"}}
        ).sort([("score", {"$meta": "textScore"})]).limit(5).to_list(length=None)

        # Add relevant analyses to context
        if analyses:
            context['store_insights'] = [
                {
                    'analysis': {
                        'summary': doc.get('analysis', {}).get('summary', ''),
                        'customer_analysis': doc.get('analysis', {}).get('customer_analysis', ''),
                        'product_performance': doc.get('analysis', {}).get('product_performance', ''),
                        'market_position': doc.get('analysis', {}).get('market_position', ''),
                        'recommendations': doc.get('analysis', {}).get('recommendations', [])
                    },
                    'metrics': doc.get('metrics', {}),
                    'store': doc.get('store', {}),
                    'last_updated': doc.get('last_updated', datetime.now(timezone.utc))
                }
                for doc in analyses
            ]

        return context

    except Exception as e:
        logger.error(f"Error getting analysis context: {str(e)}")
        return {}

async def build_complete_store_context(store_id: str) -> dict:
    try:
        # Initialize campaign_data at the start to avoid reference before assignment errors
        campaign_data = []
        
        # 1. Get base store analysis and user data
        store_analysis = await db_analysis["global_analysis"].find_one({"_id": store_id})
        store_user = await db_analysis["active_stores_cache"].find_one({"_id": store_id})
        existing_meta_context = await db_analysis["meta_chat_context"].find_one({"store_id": store_id})

        # Robust fallback for missing store_analysis
        if not store_analysis:
            logger.warning(f"No store analysis found for store ID: {store_id}, creating default context with all expected keys.")
            store_analysis = {
                "_id": store_id,
                "store": {
                    "id_store": int(store_id),
                    "name": (store_user or {}).get("name", "New Store"),
                    "email": (store_user or {}).get("email", ""),
                    "business_type": (store_user or {}).get("business_type", "New Business"),
                    "address": (store_user or {}).get("address", ""),
                    "telephone": (store_user or {}).get("telephone", ""),
                    "currency": {
                        "native": {
                            "code": "ARS",
                            "name": "Peso Argentino",
                            "symbol": "$"
                        }
                    },
                    "country": {
                        "code": "ARG",
                        "name": "Argentina"
                    },
                    "shipping_methods": []
                },
                "metrics": {
                    "total_revenue": 0,
                    "order_count": 0,
                    "avg_order_value": 0,
                    "customer_count": 0,
                    "product_count": 0,
                    "active_products": 0,
                    "total_visits": 0,
                    "unique_visitors": 0
                },
                "order_statistics": {
                    "total_orders": 0,
                    "total_sales": 0,
                    "total_sales_usd": 0,
                    "average_order_value": 0,
                    "average_order_value_usd": 0,
                    "status_distribution": {},
                    "recent_orders": []
                },
                "analysis": {
                    "summary": "¡Bienvenido a tu nueva tienda! Aquí encontrarás análisis detallados cuando comiences a tener actividad.",
                    "customer_analysis": "El análisis de clientes estará disponible cuando comiences a tener datos de clientes.",
                    "product_performance": "Las métricas de rendimiento de productos se mostrarán cuando comiences a realizar ventas.",
                    "market_position": "El análisis de posición en el mercado se generará a medida que tu tienda crezca.",
                    "shipping_analysis": "El análisis de envíos estará disponible cuando tengas datos de envíos.",
                    "competitor_analysis": "El análisis de competencia se generará a medida que recopilemos más datos sobre tu mercado.",
                    "recommendations": [
                        "Configura tus primeros productos en el catálogo",
                        "Personaliza los métodos de envío disponibles",
                        "Configura tus redes sociales para aumentar la visibilidad",
                        "Revisa las integraciones disponibles para optimizar tu operación"
                    ]
                },
                "relationships": {
                    "customers_with_orders": 0,
                    "repeat_customers": 0,
                    "customer_satisfaction": None,
                    "customer_retention_rate": None
                },
                "products": {
                    "catalog": [],
                    "categories": [],
                    "price_range": {
                        "min": 0,
                        "max": 0,
                        "average": 0
                    },
                    "performance_metrics": {
                        "total_units_sold": 0,
                        "average_price": 0,
                        "products_with_sales": 0
                    }
                },
                "social_media": {
                    "facebook": None,
                    "instagram": None,
                    "tiktok": None,
                    "youtube": None
                },
                "metadata": {
                    "store_status": "new",
                    "analysis_status": "pending",
                    "last_updated": datetime.now(timezone.utc),
                    "created_at": datetime.now(timezone.utc),
                    "data_completeness": {
                        "profile": 0,
                        "products": 0,
                        "shipping": 0,
                        "orders": 0,
                        "customers": 0
                    },
                    "is_first_time": True
                },
                "currency": {
                    "code": "ARS",
                    "name": "Peso Argentino",
                    "symbol": "$"
                },
                "feedback": []
            }

        # Ensure all expected dicts exist in store_analysis - this prevents KeyErrors later
        expected_keys = {
            "store": {}, 
            "metrics": {}, 
            "order_statistics": {}, 
            "analysis": {},
            "relationships": {}, 
            "products": {}, 
            "social_media": {}, 
            "metadata": {}, 
            "currency": {}, 
            "feedback": []
        }
        
        for key, default in expected_keys.items():
            # Initialize missing keys
            if key not in store_analysis:
                logger.debug(f"Key '{key}' missing in store_analysis for store {store_id}, initializing with default.")
                store_analysis[key] = default.copy() if isinstance(default, dict) else list(default)
            # Fix keys with wrong type
            elif not isinstance(store_analysis[key], type(default)):
                logger.warning(f"Key '{key}' has wrong type in store_analysis for store {store_id}, re-initializing with default.")
                store_analysis[key] = default.copy() if isinstance(default, dict) else list(default)

        # Get data from active_stores_cache if store_user is available
        if store_user:
            # Get metrics safely from store_user, creating an empty dict if missing
            store_metrics = store_user.get("metrics", {}) or {}
            if not isinstance(store_metrics, dict):
                logger.warning(f"Metrics has wrong type in active_stores_cache for store {store_id}, treating as empty dict")
                store_metrics = {}
                
            # Ensure metrics dict exists in store_analysis
            if not isinstance(store_analysis.get("metrics"), dict):
                store_analysis["metrics"] = {}
                
            # Update customer_count from total_customers if it doesn't exist or is zero
            if not store_analysis["metrics"].get("customer_count"):
                total_customers = store_metrics.get("total_customers", 0)
                store_analysis["metrics"]["customer_count"] = total_customers
                
            # Update product_count from total_products if it doesn't exist or is zero 
            if not store_analysis["metrics"].get("product_count"):
                total_products = store_metrics.get("total_products", 0)
                store_analysis["metrics"]["product_count"] = total_products
            
            # Update social_media safely 
            store_social_media = store_user.get("social_media", {}) or {}
            if not isinstance(store_social_media, dict):
                logger.warning(f"social_media has wrong type in active_stores_cache for store {store_id}, treating as empty dict")
                store_social_media = {}
                
            # Only update if social_media is empty in store_analysis
            if not any(store_analysis.get("social_media", {}).values()):
                store_analysis["social_media"] = store_social_media
            
            # Update currency safely
            store_currency = store_user.get("currency", {}) or {}
            if not isinstance(store_currency, dict):
                logger.warning(f"currency has wrong type in active_stores_cache for store {store_id}, treating as empty dict")
                store_currency = {}
                
            # Only update if currency is empty or has default values in store_analysis
            if not store_analysis.get("currency", {}).get("code") or store_analysis.get("currency", {}).get("code") == "ARS":
                store_analysis["currency"] = store_currency
                
            # Update key_dates if available
            if "key_dates" in store_user and isinstance(store_user["key_dates"], dict):
                store_analysis["key_dates"] = store_user["key_dates"]
                
            # Update keywords if available
            if "keywords" in store_user:
                store_analysis["keywords"] = store_user["keywords"]
                
            # Add basic store info from active_stores_cache if missing in store_analysis
            store_info = store_analysis.get("store", {})
            if not isinstance(store_info, dict):
                store_analysis["store"] = {}
                store_info = store_analysis["store"]
                
            # Update specific fields with defaults if missing
            store_info["name"] = store_info.get("name") or store_user.get("name", "Unknown Store")
            store_info["email"] = store_info.get("email") or store_user.get("email", "")
            store_info["business_type"] = store_info.get("business_type") or store_user.get("business_type", "Unknown")
            store_info["country"] = store_info.get("country") or store_user.get("country", {})
            store_info["address"] = store_info.get("address") or store_user.get("address", "")
            store_info["telephone"] = store_info.get("telephone") or store_user.get("telephone", "")

        # 2. Get complete product catalog with current metrics - use product_details_cache
        products_count_result = 0
        main_products = {}
        
        # Fetch product details from product_details_cache safely
        product_details = await db_analysis["product_details_cache"].find_one({"_id": store_id})
        if product_details:
            # Get products array with proper defensive coding
            products_array = product_details.get("products", []) or []
            if not isinstance(products_array, list):
                logger.warning(f"products field in product_details_cache is not a list for store {store_id}")
                products_array = []
            
            # Convert to dictionary for easier lookup
            for product in products_array:
                if not isinstance(product, dict):
                    continue
                    
                product_id = str(product.get("product_id", ""))
                if product_id:
                    main_products[product_id] = {
                        "id_product": product_id,
                        "name": product.get("name", f"Product {product_id}"),
                        "price": product.get("current_price", 0),
                        "current_stock": product.get("current_stock", 0),
                        "favorite_count": product.get("favorite_count", 0)
                    }
            
            # Update products count
            products_count_result = len(main_products)
            # If store_analysis doesn't have product_count, update it from products_count_result
            if not store_analysis.get("metrics", {}).get("product_count"):
                store_analysis["metrics"]["product_count"] = products_count_result
            
            # Extract products_online from store_aggregations if available
            if "store_aggregations" in product_details:
                store_aggregations = product_details["store_aggregations"]
                products_online = store_aggregations.get("products_online", 0)
                store_analysis["metrics"]["products_online"] = products_online
        
        # Merge product data, preferring enriched data
        store_products = main_products

        # 3. Get orders to calculate product metrics - keep the existing logic
        orders = []

        # Calculate product metrics from orders
        product_metrics = {}
        total_revenue = 0
        total_orders = len(orders)
        
        for order in orders:
            if not isinstance(order, dict):
                continue
                
            order_total = 0
            for product in order.get("products", []):
                if not isinstance(product, dict):
                    continue
                    
                product_id = str(product.get("id")) or str(product.get("id_product", ""))
                if not product_id:
                    continue
                    
                if product_id not in product_metrics:
                    product_info = store_products.get(product_id, {
                        "name": f"Unknown Product (ID: {product_id})",
                        "id_product": product_id,
                        "price": float(product.get("price", 0))
                    })
                    
                    product_metrics[product_id] = {
                        "id_product": product_info["id_product"],
                        "name": product_info["name"],
                        "price": product_info["price"],
                        "units_sold": 0,
                        "revenue": 0,
                        "favorites_count": 0
                    }
                
                quantity = int(product.get("quantity", 0))
                price = float(product.get("price", 0))
                
                product_metrics[product_id]["units_sold"] += quantity
                product_metrics[product_id]["revenue"] += price * quantity
                order_total += price * quantity
            
            total_revenue += order_total

        # Add any products that haven't had orders yet
        for product_id, product_info in store_products.items():
            if product_id not in product_metrics:
                product_metrics[product_id] = {
                    "id_product": product_info["id_product"],
                    "name": product_info["name"],
                    "price": product_info["price"],
                    "units_sold": 0,
                    "revenue": 0,
                    "favorites_count": 0
                }

        # Convert metrics to list and sort by revenue
        products_list = list(product_metrics.values())
        products_list.sort(key=lambda x: x["revenue"], reverse=True)

        # Update store analysis with calculated metrics if it was empty
        if not store_analysis.get('metrics', {}).get('total_revenue'):
            # Ensure all required dictionaries exist
            if 'metrics' not in store_analysis:
                store_analysis['metrics'] = {}
            if 'order_statistics' not in store_analysis:
                store_analysis['order_statistics'] = {}
                
            # Update metrics safely
            store_analysis['metrics']['total_revenue'] = total_revenue
            store_analysis['metrics']['order_count'] = total_orders
            store_analysis['metrics']['product_count'] = len(products_list)
            store_analysis['order_statistics']['total_orders'] = total_orders
            store_analysis['order_statistics']['total_sales'] = total_revenue
            if total_orders > 0:
                store_analysis['metrics']['avg_order_value'] = total_revenue / total_orders
                store_analysis['order_statistics']['average_order_value'] = total_revenue / total_orders

        # 4. Get customer data
        customers = []
        
        # SAFE ACCESS MODIFICATION: Fetch customer data from store_customers_cache
        customer_cache = await db_analysis["store_customers_cache"].find_one({"_id": store_id})
        if customer_cache:
            # Get customers array safely
            customers = customer_cache.get("customers", [])
            if not customers and "customers" not in customer_cache:
                logger.warning(f"Key 'customers' missing in store_customers_cache for store {store_id}, using default empty list.")
            
            # Update customer_count in store_analysis if it's still empty
            total_customers = customer_cache.get("total_customers", 0)
            if not store_analysis.get("metrics", {}).get("customer_count"):
                store_analysis["metrics"]["customer_count"] = total_customers
        
        customer_demographics = process_customer_demographics(customers)

        # Update customer metrics if they were empty
        if not store_analysis.get('metrics', {}).get('customer_count', 0):
            if 'metrics' not in store_analysis:
                store_analysis['metrics'] = {}
            store_analysis['metrics']['customer_count'] = len(customers)

        # 5. Get recent chat history (if available)
        recent_chats = await db_analysis["store_chats"].find({
            "store_id": store_id
        }).sort("timestamp", -1).limit(10).to_list(length=None)

        # 6. Build the complete context
        complete_context = {
            "store": {
                "details": store_analysis.get("store", {}),
                "metrics": store_analysis.get("metrics", {}),
                "order_statistics": store_analysis.get("order_statistics", {}),
                "currency": store_analysis.get("currency", {}),
            },
            "analysis": {
                "summary": store_analysis.get("analysis", {}).get("summary", ""),
                "customer_analysis": store_analysis.get("analysis", {}).get("customer_analysis", ""),
                "product_performance": store_analysis.get("analysis", {}).get("product_performance", ""),
                "market_position": store_analysis.get("analysis", {}).get("market_position", ""),
                "shipping_analysis": store_analysis.get("analysis", {}).get("shipping_analysis", ""),
                "competitor_analysis": store_analysis.get("analysis", {}).get("competitor_analysis", "")
            },
            "products": {
                "catalog": products_list,
                "total_count": len(products_list),
                "top_performers": sorted(
                    [p for p in products_list if p["units_sold"] > 0],
                    key=lambda x: x["revenue"],
                    reverse=True
                )[:5],
                "detailed_metrics": {
                    "total_revenue": total_revenue,
                    "total_units_sold": sum(p["units_sold"] for p in products_list),
                    "average_price": sum(p["price"] for p in products_list) / len(products_list) if products_list else 0,
                    "products_with_sales": len([p for p in products_list if p["units_sold"] > 0])
                }
            },
            "customers": {
                "demographics": customer_demographics,
                "total_count": len(customers),
                "active_count": store_analysis.get("relationships", {}).get("customers_with_orders", 0),
                "repeat_count": store_analysis.get("relationships", {}).get("repeat_customers", 0)
            },
            "recent_interactions": [
                {
                    "title": chat.get("title", "Untitled"),
                    "created_at": chat.get("created_at"),
                    "message_count": len(chat.get("messages", []))
                }
                for chat in recent_chats
            ],
            "shipping_methods": store_analysis.get("store", {}).get("shipping_methods", []),
            "metadata": {
                "last_updated": datetime.now(timezone.utc),
                "data_completeness": calculate_data_completeness(store_analysis),
                "is_new_store": store_analysis.get("_id") is None,
                "analysis_status": "pending" if store_analysis.get("_id") is None else "complete"
            },
            # Initialize meta context with empty values to prevent KeyError
            "meta": {
                "pages": [],
                "insights": [],
                "engagement": {},
                "audience": {},
                "ads": {},
                "is_mock_data": True,
                "campaigns": campaign_data
            }
        }

        # Preserve existing meta context if it exists
        if existing_meta_context:
            try:
                # Get pages from existing meta context
                pages = existing_meta_context.get("pages", [])
                
                # Process ad_metrics from the existing meta context
                ad_metrics = existing_meta_context.get("ad_metrics", {})
                
                # If we have pages and ad metrics, attach the ad metrics to each page
                if pages and ad_metrics:
                    # Ensure ad_metrics is properly serialized
                    ad_metrics = serialize_mongo_doc(ad_metrics)
                    
                    logger.info(f"Processing ad metrics for store {store_id}: found {len(pages)} pages and ad_metrics keys: {list(ad_metrics.keys())}")
                    
                    # Count metrics for logging
                    metrics_added = 0
                    
                    for page in pages:
                        try:
                            # Ensure the page itself is serialized
                            page = serialize_mongo_doc(page)
                            
                            page_id = page.get("id", "")
                            page_name = page.get("name", "Unknown Page")
                            logger.debug(f"Processing ad metrics for page {page_name} (ID: {page_id})")
                            
                            # Check if this page has ad metrics in the ad_metrics object
                            page_metrics = None
                            
                            # First look for direct metrics in metrics array
                            if "metrics" in ad_metrics:
                                # Handle both array objects and dictionary objects
                                metrics_array = ad_metrics.get("metrics", [])
                                if isinstance(metrics_array, list):
                                    for metric in metrics_array:
                                        if metric.get("page_id") == page_id:
                                            page_metrics = metric
                                            break
                                elif isinstance(metrics_array, dict):
                                    # Try to find a matching page_id in the dict
                                    for key, metric in metrics_array.items():
                                        if isinstance(metric, dict) and metric.get("page_id") == page_id:
                                            page_metrics = metric
                                            break
                            
                            # Handle numerically indexed arrays (where metrics are stored at numeric indexes like metrics[0])
                            if not page_metrics:
                                # Check for numeric indexes in the ad_metrics directly
                                for key, value in ad_metrics.items():
                                    if isinstance(value, dict) and key.isdigit():
                                        # This is likely a numeric index like "0", "1", etc.
                                        if value.get("page_id") == page_id:
                                            page_metrics = value
                                            logger.debug(f"Found page metrics at numeric index {key}")
                                            break
                                        
                                        # Also check if this contains campaign data
                                        if "name" in value and "spend" in value and "clicks" in value:
                                            # This looks like a campaign - see if it's for this page
                                            campaign_page_id = value.get("page_id")
                                            if campaign_page_id == page_id:
                                                if not page.get("ad_metrics"):
                                                    page["ad_metrics"] = {}
                                                
                                                # Map the fields to what the chat route expects
                                                page["ad_metrics"]["total_spend"] = value.get("spend", 0)
                                                page["ad_metrics"]["total_impressions"] = value.get("impressions", 0)
                                                page["ad_metrics"]["total_clicks"] = value.get("clicks", 0)
                                                page["ad_metrics"]["total_conversions"] = value.get("conversions", 0)
                                                page["ad_metrics"]["average_roi"] = value.get("roi", 0)
                                                
                                                # Store campaign data for specific campaign queries
                                                if not page.get("campaigns"):
                                                    page["campaigns"] = []
                                                page["campaigns"].append({
                                                    "name": value.get("name", ""),
                                                    "spend": value.get("spend", 0),
                                                    "clicks": value.get("clicks", 0),
                                                    "impressions": value.get("impressions", 0),
                                                    "conversions": value.get("conversions", 0),
                                                    "roi": value.get("roi", 0)
                                                })
                                                
                                                logger.debug(f"Added campaign data for '{value.get('name', '')}' to page {page_id}")
                                                metrics_added += 1
                            
                            # Then check if there are specific campaigns for this page
                            if not page_metrics and "campaigns" in ad_metrics:
                                for campaign in ad_metrics.get("campaigns", []):
                                    if campaign.get("page_id") == page_id:
                                        # Found a campaign for this page
                                        if not page.get("ad_metrics"):
                                            page["ad_metrics"] = {}
                                        
                                        # Map the fields to what the chat route expects
                                        page["ad_metrics"]["total_spend"] = campaign.get("spend", 0)
                                        page["ad_metrics"]["total_impressions"] = campaign.get("impressions", 0)
                                        page["ad_metrics"]["total_clicks"] = campaign.get("clicks", 0)
                                        page["ad_metrics"]["total_conversions"] = campaign.get("conversions", 0)
                                        page["ad_metrics"]["average_roi"] = campaign.get("roi", 0)
                                        
                                        # Add debug logging
                                        logger.debug(f"Added ad metrics to page {page_id} from campaign: spend={campaign.get('spend', 0)}, clicks={campaign.get('clicks', 0)}")
                                        metrics_added += 1
                            
                            # If we didn't find page-specific metrics, look for direct metrics in the document
                            if not page_metrics and not page.get("ad_metrics"):
                                # Check if we have fields directly in the ad_metrics object
                                if isinstance(ad_metrics, dict):
                                    # Try to find direct fields like clicks, spend, etc.
                                    clicks = ad_metrics.get("clicks", 0)
                                    spend = ad_metrics.get("spend", 0)
                                    cpc = ad_metrics.get("cpc", 0)
                                    conversions = ad_metrics.get("conversions", 0)
                                    
                                    # Ensure numeric values
                                    try:
                                        clicks = int(clicks) if clicks is not None else 0
                                    except (ValueError, TypeError):
                                        clicks = 0
                                        
                                    try:
                                        spend = float(spend) if spend is not None else 0
                                    except (ValueError, TypeError):
                                        spend = 0
                                        
                                    try:
                                        conversions = int(conversions) if conversions is not None else 0
                                    except (ValueError, TypeError):
                                        conversions = 0
                                    
                                    # If we found any of these fields, create ad_metrics for the page
                                    if clicks or spend or conversions:
                                        page["ad_metrics"] = {
                                            "total_spend": spend,
                                            "total_impressions": ad_metrics.get("impressions", 0),
                                            "total_clicks": clicks,
                                            "total_conversions": conversions,
                                            "average_roi": ad_metrics.get("roi", 0)
                                        }
                                        logger.debug(f"Added ad metrics to page {page_name} from top-level fields: spend={spend}, clicks={clicks}")
                                        metrics_added += 1
                        except Exception as page_error:
                            logger.error(f"Error processing page metrics for page {page.get('id', 'unknown')}: {str(page_error)}")
                            continue
                    
                    logger.info(f"Added metrics to {metrics_added} pages for store {store_id}")
                
                # Create a direct campaigns list in meta data for campaign-specific queries
                campaign_data = []
                try:
                    # Extract campaign data from any source
                    # 1. Check explicit campaigns array
                    if "campaigns" in ad_metrics:
                        for campaign in ad_metrics.get("campaigns", []):
                            campaign_data.append({
                                "name": campaign.get("name", "Unknown Campaign"),
                                "spend": campaign.get("spend", 0),
                                "clicks": campaign.get("clicks", 0),
                                "impressions": campaign.get("impressions", 0),
                                "conversions": campaign.get("conversions", 0),
                                "roi": campaign.get("roi", 0),
                                "page_id": campaign.get("page_id", "")
                            })
                            logger.debug(f"Added campaign '{campaign.get('name', '')}' to campaigns list")
                    
                    # 2. Check numeric indexes that look like campaigns
                    for key, value in ad_metrics.items():
                        if isinstance(value, dict) and key.isdigit():
                            if "name" in value and "spend" in value:
                                # This looks like a campaign
                                campaign_data.append({
                                    "name": value.get("name", "Unknown Campaign"),
                                    "spend": value.get("spend", 0),
                                    "clicks": value.get("clicks", 0),
                                    "impressions": value.get("impressions", 0),
                                    "conversions": value.get("conversions", 0),
                                    "roi": value.get("roi", 0),
                                    "page_id": value.get("page_id", "")
                                })
                                logger.debug(f"Added campaign '{value.get('name', '')}' from numeric index {key} to campaigns list")
                    
                    logger.info(f"Added {len(campaign_data)} campaigns to meta context")
                except Exception as campaign_error:
                    logger.error(f"Error processing campaigns for store {store_id}: {str(campaign_error)}")
                
                # If we have ad metrics but no pages, create a default page with the metrics
                if not pages and ad_metrics:
                    try:
                        # Ensure ad_metrics is properly serialized
                        ad_metrics = serialize_mongo_doc(ad_metrics)
                        
                        logger.info(f"No pages found for store {store_id}, but ad metrics exist. Creating a default page.")
                        
                        # Create a default page
                        default_page = {
                            "id": "default",
                            "name": "Store Page",
                            "platform": "facebook",
                            "url": "#",
                            "followers": 0,
                            "engagement_rate": 0,
                            "ad_metrics": {
                                "total_spend": ad_metrics.get("spend", 0),
                                "total_impressions": ad_metrics.get("impressions", 0),
                                "total_clicks": ad_metrics.get("clicks", 0),
                                "total_conversions": ad_metrics.get("conversions", 0),
                                "average_roi": ad_metrics.get("roi", 0)
                            }
                        }
                        
                        # Add the default page to the pages list
                        pages = [default_page]
                        logger.info(f"Created default page with ad metrics: spend={default_page['ad_metrics']['total_spend']}, clicks={default_page['ad_metrics']['total_clicks']}")
                    except Exception as default_page_error:
                        logger.error(f"Error creating default page for store {store_id}: {str(default_page_error)}")
                
                # Process insights from existing meta context
                insights = existing_meta_context.get("insights", [])
                
                # Build the Meta context
                complete_context["meta"] = {
                    "pages": pages,
                    "insights": insights,
                    "engagement": existing_meta_context.get("engagement_metrics", {}),
                    "audience": existing_meta_context.get("audience_metrics", {}),
                    "ads": existing_meta_context.get("ad_metrics", {}),
                    "is_mock_data": existing_meta_context.get("is_mock_data", False),
                    "campaigns": campaign_data
                }
                
                logger.info(f"Used existing meta_chat_context for store {store_id} with {len(pages)} pages")
            except Exception as meta_error:
                logger.error(f"Error processing meta context for store {store_id}: {str(meta_error)}")
                # Provide empty structure if meta context processing fails
                complete_context["meta"] = {
                    "pages": [],
                    "insights": [],
                    "engagement": {},
                    "audience": {},
                    "ads": {},
                    "is_mock_data": True,
                    "campaigns": []
                }
        else:
            # Provide empty structure if no meta context exists
            complete_context["meta"] = {
                "pages": [],
                "insights": [],
                "engagement": {},
                "audience": {},
                "ads": {},
                "is_mock_data": True,
                "campaigns": []
            }
            logger.info(f"No meta_chat_context found for store {store_id}, using empty structure")
        
        # Keep the product_cache fetching code since it's not related to meta
        # Fetch product details from cache
        try:
            product_cache = await db_analysis['product_details_cache'].find_one({"_id": store_id})
            if product_cache:
                products_list_cache = product_cache.get("products", [])
                # Note: We already processed products earlier, so this is just for additional processing if needed
        except Exception as product_cache_error:
            logger.error(f"Error fetching product_details_cache for store {store_id}: {str(product_cache_error)}")
            products_list_cache = []
        
        # --- Fetch Recent User Feedback --- 
        recent_feedback_summary = "No recent feedback available."
        try:
            if store_analysis and isinstance(store_analysis.get("feedback"), list) and store_analysis.get("feedback"):
                # Sort feedback by date descending and take the latest 3 entries
                sorted_feedback = sorted(
                    store_analysis.get("feedback", []), 
                    key=lambda x: x.get("created_at", datetime.min.replace(tzinfo=timezone.utc)), # Handle missing dates gracefully
                    reverse=True
                )
                latest_feedback = sorted_feedback[:3] 
                
                feedback_texts = []
                for fb in latest_feedback:
                    ts = fb.get("created_at", "Unknown date")
                    # Format timestamp for readability if it's a datetime object
                    if isinstance(ts, datetime):
                        ts_str = ts.strftime("%Y-%m-%d") 
                    else:
                        ts_str = str(ts) # Keep as string if already formatted or default
                    user = fb.get("user_email", "Unknown user")
                    feedback_texts.append(f"- On {ts_str}, {user} said: '{fb.get('text', 'N/A')}'")
                    
                if feedback_texts:
                     recent_feedback_summary = "\n".join(feedback_texts)
                
                logger.debug(f"Added recent feedback summary for store {store_id}")
                
        except Exception as feedback_error:
            logger.warning(f"Could not retrieve or process feedback for store {store_id}: {feedback_error}")
            # Keep the default "No recent feedback" message

        # Add the feedback summary to the context
        complete_context["recent_feedback"] = recent_feedback_summary
        # --- End Fetch Recent User Feedback ---

        # Fetch platform shipping methods and merge with store shipping methods
        platform_shipping_methods = await get_platform_shipping_methods()
        logger.info(f"Cross-referencing {len(platform_shipping_methods)} platform shipping methods with {len(store_analysis.get('store', {}).get('shipping_methods', []))} store shipping methods for store {store_id}.")
        platform_methods_by_id = {m.get("id"): m for m in platform_shipping_methods}
        store_shipping_methods = store_analysis.get("store", {}).get("shipping_methods", [])
        merged_store_shipping_methods = []
        for method in store_shipping_methods:
            method_id = method.get("id")
            platform_info = platform_methods_by_id.get(method_id)
            merged = dict(method)
            if platform_info:
                merged.update({k: v for k, v in platform_info.items() if k not in merged or not merged[k]})
                merged["is_custom"] = False
            else:
                merged["is_custom"] = True
            merged_store_shipping_methods.append(merged)

        complete_context["shipping_methods"] = {
            "store_shipping_methods": merged_store_shipping_methods,
            "platform_shipping_methods": platform_shipping_methods
        }

        return complete_context

    except Exception as e:
        logger.error(f"Error building complete store context: {str(e)}")
        traceback.print_exc()  # Add traceback for more detailed debugging
        return {}

def process_customer_demographics(customers: list) -> dict:
    """Process customer data to extract demographic information"""
    try:
        if not customers:
            return {"countries": [], "cities": []}

        country_counts = {}
        city_counts = {}
        total_customers = len(customers)

        # Count customers by country and city
        for customer in customers:
            country = customer.get("pais", "Unknown")
            city = customer.get("ciudad", "Unknown")

            country_counts[country] = country_counts.get(country, 0) + 1
            if country not in city_counts:
                city_counts[country] = {}
            city_counts[country][city] = city_counts[country].get(city, 0) + 1

        # Calculate percentages and format data
        demographics = {
            "countries": [],
            "total_customers": total_customers
        }

        for country, count in country_counts.items():
            country_percentage = (count / total_customers) * 100
            cities = []

            for city, city_count in city_counts[country].items():
                city_percentage = (city_count / count) * 100
                cities.append({
                    "name": city,
                    "count": city_count,
                    "percentage": round(city_percentage, 1)
                })

            demographics["countries"].append({
                "name": country,
                "count": count,
                "percentage": round(country_percentage, 1),
                "cities": sorted(cities, key=lambda x: x["count"], reverse=True)
            })

        demographics["countries"].sort(key=lambda x: x["count"], reverse=True)
        return demographics

    except Exception as e:
        logger.error(f"Error processing customer demographics: {str(e)}")
        return {"countries": [], "cities": []}

def calculate_data_completeness(store_analysis: dict) -> dict:
    """Calculate how complete the store's data is"""
    try:
        checks = {
            "has_products": bool(store_analysis.get("metrics", {}).get("product_count", 0)),
            "has_orders": bool(store_analysis.get("order_statistics", {}).get("total_orders", 0)),
            "has_customers": bool(store_analysis.get("metrics", {}).get("customer_count", 0)),
            "has_shipping_methods": bool(store_analysis.get("store", {}).get("shipping_methods", [])),
            "has_analysis": bool(store_analysis.get("analysis", {}).get("summary", "")),
        }
        
        completeness_score = sum(1 for v in checks.values() if v) / len(checks) * 100
        
        return {
            "checks": checks,
            "score": round(completeness_score, 1),
            "status": "complete" if completeness_score == 100 else "partial"
        }
    except Exception as e:
        logger.error(f"Error calculating data completeness: {str(e)}")
        return {"checks": {}, "score": 0, "status": "unknown"}

async def ensure_store_analysis(store_id: str, user_email: str, store_name: Optional[str] = None):
    """Ensure a store analysis document exists, creating a basic one if not."""
    try:
        # Check if analysis already exists
        existing_analysis = await db_analysis["global_analysis"].find_one({"_id": store_id})
        if not existing_analysis:
            # Fetch user details if needed
            store_user = await db_analysis["active_stores_cache"].find_one({"email": user_email})
            if not store_name and store_user:
                store_name = store_user.get("name")
            
            # Get initial metrics
            # Deprecated: db_main["products"] as a top-level collection is no longer used.
            products_count_result = 0
            orders = []
            customers_count_result = 0
            
            # Calculate order statistics
            total_orders = len(orders)
            total_revenue = sum(float(order.get("total", 0)) for order in orders)
            avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
            
            # Initialize store analysis document
            store_analysis = {
                "_id": store_id,
                "store": {
                    "id_store": int(store_id),
                    "name": store_name or (store_user or {}).get("name", "Store Name Pending"),
                    "email": user_email,
                    "business_type": (store_user or {}).get("business_type", "Business Type Pending"),
                    "address": (store_user or {}).get("address", ""),
                    "telephone": (store_user or {}).get("telephone", ""),
                    "currency": {
                        "native": {
                            "code": "ARS",
                            "name": "Peso Argentino",
                            "symbol": "$"
                        }
                    },
                    "country": {
                        "code": "ARG",
                        "name": "Argentina"
                    }
                },
                "metrics": {
                    "total_revenue": total_revenue,
                    "order_count": total_orders,
                    "avg_order_value": avg_order_value,
                    "customer_count": customers_count_result,
                    "product_count": products_count_result,
                    "active_products": products_count_result
                },
                "order_statistics": {
                    "total_orders": total_orders,
                    "total_sales": total_revenue,
                    "total_sales_usd": 0,  # To be calculated with exchange rate
                    "average_order_value": avg_order_value,
                    "average_order_value_usd": 0  # To be calculated with exchange rate
                },
                "analysis": {
                    "summary": "Initial analysis pending",
                    "customer_analysis": "Customer analysis pending",
                    "product_performance": "Product performance analysis pending",
                    "market_position": "Market position analysis pending",
                    "shipping_analysis": "Shipping analysis pending",
                    "competitor_analysis": "Competitor analysis pending"
                },
                "relationships": {
                    "customers_with_orders": len(set(order.get("customer", {}).get("id") for order in orders)),
                    "repeat_customers": 0,  # To be calculated
                    "customer_satisfaction": None,
                    "customer_retention_rate": None
                },
                "metadata": {
                    "store_status": "active",
                    "analysis_status": "initialized",
                    "last_updated": datetime.now(timezone.utc),
                    "created_at": datetime.now(timezone.utc),
                    "data_completeness": {
                        "profile": 100 if all([store_name, user_email]) else 50,
                        "products": 100 if products_count_result > 0 else 0,
                        "orders": 100 if total_orders > 0 else 0,
                        "customers": 100 if customers_count_result > 0 else 0
                    }
                }
            }
            
            # Insert the new analysis document
            await db_analysis["global_analysis"].insert_one(store_analysis)
            logger.info(f"Successfully initialized store analysis for store ID: {store_id}")
            
            return store_analysis
        
        # If analysis exists but metrics are empty, update them
        if not existing_analysis.get('metrics', {}).get('total_revenue'):
            # Deprecated: db_main["products"] as a top-level collection is no longer used.
            products_count_update = 0
            orders_update = []
            customers_count_update = 0
            
            total_orders_update = len(orders_update)
            total_revenue_update = sum(float(order.get("total", 0)) for order in orders_update)
            avg_order_value_update = total_revenue_update / total_orders_update if total_orders_update > 0 else 0
            
            await db_analysis["global_analysis"].update_one(
                {"_id": store_id},
                {
                    "$set": {
                        "metrics": {
                            "total_revenue": total_revenue_update,
                            "order_count": total_orders_update,
                            "avg_order_value": avg_order_value_update,
                            "customer_count": customers_count_update,
                            "product_count": products_count_update,
                            "active_products": products_count_update
                        },
                        "order_statistics": {
                            "total_orders": total_orders_update,
                            "total_sales": total_revenue_update,
                            "average_order_value": avg_order_value_update
                        },
                        "metadata.last_updated": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Re-fetch the updated analysis to return it
            existing_analysis = await db_analysis["global_analysis"].find_one({"_id": store_id})
        
        return existing_analysis
        
    except Exception as e:
        logger.error(f"Error ensuring store analysis: {str(e)}")
        return None

async def save_chat_feedback(
    store_id: str, 
    feedback_data: ChatFeedbackInput,
    user_email: str,                        # Added
    meta_user_id: Optional[str] = None      # Added
):
    """Saves structured chat feedback to the global_analysis collection."""
    try:
        logger.info(f"Saving chat feedback for store {store_id} from user {user_email}") # Updated log
        
        feedback_object_to_store = {
            "text": feedback_data.feedback_text,
            "likes": feedback_data.likes,
            "dislikes": feedback_data.dislikes,
            "source": feedback_data.source,
            "timestamp": datetime.now(timezone.utc),
            "user_email": user_email,                # Added
            "meta_user_id": meta_user_id             # Added (will be None if not provided)
        }

        update_result = await db_analysis["global_analysis"].update_one(
            {"_id": store_id},
            {
                "$push": {"feedback": feedback_object_to_store},
                # Add other fields if the document might not exist or needs defaults
                "$setOnInsert": {
                    "_id": store_id,
                    # Add default store name/email if possible, or rely on ensure_store_analysis
                }
            },
            upsert=True
        )
        
        if update_result.upserted_id:
            logger.info(f"Created new analysis document for store {store_id} while saving feedback.")
        elif update_result.modified_count == 0 and update_result.matched_count > 0:
            logger.warning(f"Feedback array push might not have occurred for store {store_id}, though document matched.")
        else:
             logger.info(f"Successfully pushed feedback for store {store_id}")

    except Exception as e:
        logger.error(f"Error saving chat feedback for store {store_id}: {str(e)}", exc_info=True)
        # Re-raise the exception to be handled by the route
        raise

# Add the new function to fetch feedback
async def get_store_feedback(store_id: str, db_analysis: AsyncIOMotorDatabase) -> List[str]:  # type: ignore[valid-type]
    """Fetches and parses the feedback array for a given store_id, returning only the text content."""
    logger.debug(f"Fetching feedback for store {store_id}")
    feedback_texts = []
    try:
        document = await db_analysis.global_analysis.find_one(
            {"_id": store_id},
            projection={"feedback": 1} # Only retrieve the feedback field
        )
        if document and 'feedback' in document and isinstance(document['feedback'], list):
            raw_feedback_list = document['feedback']
            logger.debug(f"Found raw feedback for store {store_id}: {raw_feedback_list}")
            for item in raw_feedback_list:
                if isinstance(item, dict):
                    # If it's already a dictionary
                    feedback_texts.append(item.get('text', 'Invalid dictionary entry'))
                elif isinstance(item, str):
                    # If it's a string, try to parse it
                    try:
                        # Use ast.literal_eval for safety
                        parsed_item = ast.literal_eval(item)
                        if isinstance(parsed_item, dict):
                            # If parsing succeeded and it's a dict, get the text
                            feedback_texts.append(parsed_item.get('text', str(item))) # Fallback to original string if 'text' key is missing
                        else:
                            # If parsing succeeded but it's not a dict (e.g., just a string literal), use the original string
                            feedback_texts.append(item)
                    except (ValueError, SyntaxError, MemoryError, TypeError):
                        # If literal_eval fails, treat it as a plain string
                        logger.debug(f"Could not parse feedback item as literal: {item}")
                        feedback_texts.append(item)
                else:
                    # Handle other unexpected types
                    logger.warning(f"Unexpected feedback item type ({type(item)}): {item}")
                    feedback_texts.append(str(item))
            logger.debug(f"Processed feedback texts for store {store_id}: {feedback_texts}")
            return feedback_texts
        else:
            logger.debug(f"No feedback found or field is not an array for store {store_id}")
            return []
    except ConnectionFailure:
        logger.error(f"Database connection failed while fetching feedback for store {store_id}")
        return [] # Return empty list on connection error
    except Exception as e:
        logger.error(f"Error fetching feedback for store {store_id}: {e}", exc_info=True)
        return [] # Return empty list on other errors

async def get_store_products(store_id: str, db: AsyncIOMotorDatabase, db_analysis: AsyncIOMotorDatabase, page: int = 1, page_size: int = 100, time_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:  # type: ignore[valid-type]
    # ... existing get_store_products function ...
    # (Ensure this function exists and remains unchanged)
    # Placeholder for brevity
    logger.debug(f"Fetching products for store {store_id}, page {page}, size {page_size}, time range {time_range}")
    try:
        # Example: Fetch data from product_details_cache
        # Adjusting query based on actual schema - assuming store_id is the _id in cache
        cache_doc = await db_analysis.product_details_cache.find_one({"_id": store_id})

        if not cache_doc or "products" not in cache_doc:
             logger.warning(f"No product cache found or 'products' field missing for store {store_id}")
             return {"products": [], "total_products": 0, "page": page, "page_size": page_size, "total_pages": 0}

        all_products_in_cache = cache_doc.get("products", [])
        total_products = len(all_products_in_cache)
        total_pages = (total_products + page_size - 1) // page_size if page_size > 0 else 1

        # Apply pagination
        skip = (page - 1) * page_size
        paginated_products = all_products_in_cache[skip : skip + page_size]

        # Convert ObjectId to string if needed, handle missing fields
        result_products = []
        for p in paginated_products:
            # Ensure p is a dictionary before proceeding
            if not isinstance(p, dict):
                logger.warning(f"Skipping non-dict item in product cache for store {store_id}: {p}")
                continue

            # Ensure _id is stringified - It might already be if fetched correctly
            if "_id" in p and not isinstance(p["_id"], str):
                 p["_id"] = str(p["_id"])
            # Ensure id_product exists and is a string
            if "id_product" not in p:
                 p["id_product"] = p.get("_id", "unknown") # Fallback if id_product missing
            elif not isinstance(p["id_product"], str):
                 p["id_product"] = str(p["id_product"])

            # result_products.append(ProductDetail(**p)) # Commented out - ProductDetail not defined/imported
            result_products.append(p) # Append the dictionary directly

        return {
            "products": result_products,
            "total_products": total_products,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }
    except ConnectionFailure:
        logger.error(f"Database connection failed while fetching products for store {store_id}")
        return {"products": [], "total_products": 0, "page": page, "page_size": page_size, "total_pages": 0}
    except Exception as e:
        logger.error(f"Error fetching products for store {store_id}: {e}", exc_info=True)
        return {"products": [], "total_products": 0, "page": page, "page_size": page_size, "total_pages": 0}

async def submit_feedback_service(store_id: str, db_analysis: AsyncIOMotorDatabase, feedback_data: dict):  # type: ignore[valid-type]
    # ... existing submit_feedback_service function ...
    # (Ensure this function exists and remains unchanged)
    # Placeholder for brevity
    logger.debug(f"Submitting feedback for store {store_id}")
    feedback_text = feedback_data.get("feedback") # Assuming feedback text is in 'feedback' key
    if not feedback_text:
         raise ValueError("Feedback text is required")
    try:
        result = await db_analysis.global_analysis.update_one(
            {"_id": store_id},
            {"$push": {"feedback": feedback_text}},
            upsert=True
        )
        logger.info(f"Feedback submitted for store {store_id}. Matched: {result.matched_count}, Modified: {result.modified_count}, Upserted: {result.upserted_id}")
        return result.acknowledged
    except ConnectionFailure:
        logger.error(f"Database connection failed while submitting feedback for store {store_id}")
        raise
    except Exception as e:
        logger.error(f"Error submitting feedback for store {store_id}: {e}", exc_info=True)
        raise

async def get_shipping_details_data(store_id: str) -> Dict[str, Any]:
    """Fetches shipping distribution and analysis for a given store, and cross-references with platform shipping methods."""
    try:
        # Fetch distribution from store_customers_cache
        customer_cache = await db_analysis["store_customers_cache"].find_one(
            {"_id": store_id},
            projection={
                "shipping_method_distribution": 1,
                "most_frequent_shipping_method": 1
            }
        )

        # Fetch analysis from global_analysis
        global_analysis_data = await db_analysis["global_analysis"].find_one(
            {"_id": store_id},
            projection={"analysis.shipping_analysis": 1}
        )

        shipping_distribution = customer_cache.get("shipping_method_distribution") if customer_cache else None
        most_frequent = customer_cache.get("most_frequent_shipping_method") if customer_cache else None
        shipping_analysis_obj = global_analysis_data.get("analysis", {}).get("shipping_analysis") if global_analysis_data else None

        shipping_analysis_data = None
        if shipping_analysis_obj:
            shipping_analysis_data = {
                "analysis_text": shipping_analysis_obj.get("analysis_text", "Analysis not available."),
                "recommendations": shipping_analysis_obj.get("recommendations", [])
            }

        # Fetch platform shipping methods
        platform_methods = await get_platform_shipping_methods()
        platform_methods_by_id = {m.get("id"): m for m in platform_methods}

        # Cross-reference: merge store shipping distribution with platform details
        merged_distribution = {}
        if shipping_distribution and isinstance(shipping_distribution, dict):
            for method_id, count in shipping_distribution.items():
                platform_info = platform_methods_by_id.get(method_id)
                merged_distribution[method_id] = {
                    "count": count,
                    "platform_details": platform_info,
                    "is_custom": platform_info is None
                }

        return {
            "shipping_method_distribution": merged_distribution if merged_distribution else shipping_distribution,
            "shipping_analysis": shipping_analysis_data,
            "most_frequent_shipping_method": most_frequent,
            "platform_shipping_methods": platform_methods
        }

    except Exception as e:
        logger.error(f"Error fetching shipping details for store {store_id}: {str(e)}")
        return {
            "shipping_method_distribution": None,
            "shipping_analysis": None,
            "most_frequent_shipping_method": None,
            "platform_shipping_methods": []
        }

async def get_platform_shipping_methods() -> list:
    """Fetch all active shipping methods from platform_reference_data."""
    try:
        doc = await db_analysis["platform_reference_data"].find_one({}, sort=[("last_updated", -1)])
        if not doc:
            logger.warning("platform_reference_data collection is missing or empty.")
            return []
        if "shipping_methods" not in doc or not isinstance(doc["shipping_methods"], list):
            logger.warning("No shipping_methods array found in platform_reference_data document.")
            return []
        # Filter for active shipping methods only
        active_methods = [m for m in doc["shipping_methods"] if m.get("active") is True]
        logger.info(f"Fetched {len(active_methods)} active platform shipping methods.")
        return active_methods
    except Exception as e:
        logger.error(f"Error fetching platform shipping methods: {e}")
        return []

# === Credits Helpers START ===
async def get_store_credits(store_id: str) -> int:
    """Return current credit balance for a store (0 if missing)."""
    try:
        doc = await db_analysis["active_stores_cache"].find_one(
            {"_id": store_id}, {"credits": 1}
        )
        return int(doc.get("credits", 0)) if doc else 0
    except Exception as exc:
        logger.error("Error fetching credits for store %s: %s", store_id, exc)
        return 0


async def deduct_store_credits(store_id: str, amount: int) -> bool:
    """Atomically deduct amount from store. Returns True on success, False if insufficient."""
    if amount <= 0:
        return True  # Nothing to deduct

    try:
        coll = db_analysis["active_stores_cache"]
        updated_doc = await coll.find_one_and_update(
            {"_id": store_id, "credits": {"$gte": amount}},
            {"$inc": {"credits": -amount}},
            projection={"credits": 1},
            return_document=ReturnDocument.AFTER,
        )
        if updated_doc:
            logger.info(
                "Deducted %s credits from store %s. New balance: %s",
                amount,
                store_id,
                updated_doc.get("credits"),
            )
            return True
        else:
            logger.warning(
                "Insufficient credits when attempting to deduct %s from store %s",
                amount,
                store_id,
            )
            return False
    except Exception as exc:
        logger.error("Error deducting credits for store %s: %s", store_id, exc, exc_info=True)
        return False

    # Not enough credits – raise standardized error for clients
    logger.warning("Insufficient credits for store %s (needed %s)", store_id, amount)
    from fastapi import HTTPException  # Local import to avoid circular
    raise HTTPException(status_code=402, detail="insufficient_credits")
# === Credits Helpers END ===

