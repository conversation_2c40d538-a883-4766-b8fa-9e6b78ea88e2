"""Weekly Store Credits Recharge Script

Adds 250,000 credits to every store in the `active_stores_cache` collection if
it has not been recharged during the current ISO week (Monday 00:00 UTC).

Usage (manual):
    python recharge_store_credits_weekly.py

Recommended cron entry (runs every Monday 00:00 UTC):
    0 0 * * 1 /usr/bin/python /path/to/backend/scripts/data/recharge_store_credits_weekly.py >> /var/log/dunit/recharge.log 2>&1

This script is idempotent – each store document contains a `last_recharge_date`
field so the script will only add credits once per week per store.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta

from config.database import db_analysis  # AsyncIOMotorDatabase instance

# Configure logger for standalone execution
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)

# Constants
CREDITS_INCREMENT = 250_000
COLLECTION_NAME = "active_stores_cache"


def get_start_of_iso_week(dt: datetime) -> datetime:
    """Return Monday 00:00 UTC for the week containing `dt`."""
    # Monday is 0 in isoweekday (Monday=1) but weekday() (Monday=0). We'll use iso.
    iso_weekday = dt.isoweekday()  # 1 through 7
    start_of_week = dt - timedelta(days=iso_weekday - 1, hours=dt.hour, minutes=dt.minute,
                                   seconds=dt.second, microseconds=dt.microsecond)
    return start_of_week.replace(tzinfo=timezone.utc)


async def perform_weekly_recharge():
    """Add credits to stores if not already recharged during current week."""
    now_utc = datetime.now(timezone.utc)
    week_start = get_start_of_iso_week(now_utc)

    coll = db_analysis[COLLECTION_NAME]

    # Build query – stores not recharged this week
    query = {
        "$or": [
            {"last_recharge_date": {"$lt": week_start}},
            {"last_recharge_date": {"$exists": False}},
        ]
    }

    update = {
        "$inc": {"credits": CREDITS_INCREMENT},
        "$set": {"last_recharge_date": now_utc},
    }

    logger.info("Starting weekly credits recharge – increment=%s, week_start=%s", CREDITS_INCREMENT, week_start.isoformat())

    try:
        result = await coll.update_many(query, update)
        logger.info("Recharge complete – matched=%s, modified=%s", result.matched_count, result.modified_count)
    except Exception as exc:
        logger.error("Recharge failed: %s", exc, exc_info=True)
        raise


async def _run():
    await perform_weekly_recharge()


if __name__ == "__main__":
    asyncio.run(_run()) 