"""
One-time script: assign_initial_store_credits.py

Adds a `credits` field with an initial balance of 1,000,000 to every document
in the `active_stores_cache` collection where the field is missing or below
that threshold. The script is **idempotent** – running it again will not add
additional credits beyond the defined minimum.

Usage:
$ python -m backend.scripts.data.assign_initial_store_credits
"""

from __future__ import annotations

import asyncio
import logging

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import PyMongoError

from config.database import db_analysis
from utils.logger import logger  # Re-use project logger configuration

# Constants
MIN_INITIAL_CREDITS = 1_000_000
COLLECTION_NAME = "active_stores_cache"


async def _assign_initial_credits() -> None:
    collection = db_analysis[COLLECTION_NAME]

    try:
        # Filter documents needing the field or with insufficient value
        filter_q = {
            "$or": [
                {"credits": {"$exists": False}},
                {"credits": {"$lt": MIN_INITIAL_CREDITS}},
            ]
        }

        # Build the update operation
        update_q = {
            "$set": {"credits": MIN_INITIAL_CREDITS}
        }

        result = await collection.update_many(filter_q, update_q)
        logger.info(
            "Initial credits assignment complete. Matched: %s – Modified: %s",
            result.matched_count,
            result.modified_count,
        )
    except PyMongoError as exc:
        logger.error("Error updating credits: %s", exc, exc_info=True)


def main() -> None:
    logger.info("Starting initial credits assignment script …")
    asyncio.run(_assign_initial_credits())
    logger.info("Script finished.")


if __name__ == "__main__":
    main() 