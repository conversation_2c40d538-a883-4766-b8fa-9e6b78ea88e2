#!/usr/bin/env python3
"""
Meta Permissions Setup Script

This script initializes and validates the Meta permissions system components.
It checks that all required components are in place and working correctly.

Usage:
    python setup_meta_permissions.py
"""

import os
import sys
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Add the parent directory to sys.path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Configure logging (REMOVED basicConfig)
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s'
# )
logger = logging.getLogger(__name__)

# Import required modules
try:
    from config.database import db_analysis
    from models.meta import PermissionChange, MetaPermissions
    from services.meta_permissions import (
        is_meta_related_query,
        extract_meta_data_points,
        get_available_meta_data_points,
        fetch_meta_data_for_chat,
        generate_permission_message,
        get_data_point_permission_status,
        track_permission_change,
        get_permission_history,
        META_DATA_POINT_PERMISSIONS,
        META_DATA_POINT_DESCRIPTIONS
    )
    logger.info("Successfully imported all required modules")
except ImportError as e:
    logger.error(f"Failed to import required modules: {str(e)}")
    sys.exit(1)

async def validate_meta_permissions_system() -> bool:
    """
    Validate that all components of the Meta permissions system are working correctly.
    
    Returns:
        bool: True if all validations pass, False otherwise
    """
    validation_results = {
        "imports": True,
        "data_structures": False,
        "query_detection": False,
        "data_point_extraction": False,
        "permissions_checking": False,
        "permission_history": False
    }
    
    # Check data structures
    if len(META_DATA_POINT_PERMISSIONS) > 0 and len(META_DATA_POINT_DESCRIPTIONS) > 0:
        validation_results["data_structures"] = True
        logger.info(f"✅ Data structures validation passed: {len(META_DATA_POINT_PERMISSIONS)} permission mappings, {len(META_DATA_POINT_DESCRIPTIONS)} descriptions")
    else:
        logger.error("❌ Data structures validation failed: Missing permission mappings or descriptions")
    
    # Test query detection
    test_queries = [
        ("How many Instagram followers do I have?", True),
        ("Show me my Facebook engagement rate", True),
        ("What's my most popular post?", True),
        ("How many orders did I get yesterday?", False)
    ]
    
    query_results = []
    for query, expected in test_queries:
        result = is_meta_related_query(query)
        query_results.append(result == expected)
    
    if all(query_results):
        validation_results["query_detection"] = True
        logger.info("✅ Query detection validation passed")
    else:
        logger.error("❌ Query detection validation failed")
    
    # Test data point extraction
    test_extraction = [
        ("How many followers do I have?", ["followers", "followers_count"]),
        ("What's my engagement rate?", ["engagement_rate"]),
        ("Show me my reach and impressions", ["reach", "impressions"])
    ]
    
    extraction_results = []
    for query, expected_points in test_extraction:
        extracted = extract_meta_data_points(query)
        # Check if at least one expected point is in the extracted points
        extraction_results.append(any(point in extracted for point in expected_points))
    
    if all(extraction_results):
        validation_results["data_point_extraction"] = True
        logger.info("✅ Data point extraction validation passed")
    else:
        logger.error("❌ Data point extraction validation failed")
    
    # Test permission storage and retrieval
    test_store_id = "test_store_123"
    test_permission = "instagram_basic"
    
    try:
        # Store a test permission
        await track_permission_change(test_store_id, test_permission, "granted")
        
        # Get the permission history
        history = await get_permission_history(test_store_id, test_permission)
        
        if history and len(history) > 0:
            validation_results["permission_history"] = True
            logger.info("✅ Permission history validation passed")
        else:
            logger.error("❌ Permission history validation failed: No history records found")
        
        # Test permission status checking
        status = await get_data_point_permission_status(test_store_id, "followers")
        if "available" in status and "required_permissions" in status:
            validation_results["permissions_checking"] = True
            logger.info("✅ Permission checking validation passed")
        else:
            logger.error("❌ Permission checking validation failed: Invalid status response")
        
        # Clean up test data
        await db_analysis["meta_permissions"].delete_one({"store_id": test_store_id})
        
    except Exception as e:
        logger.error(f"❌ Permission testing failed with error: {str(e)}")
    
    # Return overall validation result
    all_passed = all(validation_results.values())
    if all_passed:
        logger.info("🎉 All Meta permissions system validations passed!")
    else:
        failed = [k for k, v in validation_results.items() if not v]
        logger.error(f"⚠️ Meta permissions system validation failed in: {', '.join(failed)}")
    
    return all_passed

async def create_sample_permissions(store_id: str = "sample_store") -> None:
    """
    Create sample permissions data for testing.
    
    Args:
        store_id: The ID of the store to create sample data for
    """
    # Delete existing data
    await db_analysis["meta_permissions"].delete_one({"store_id": store_id})
    
    # Create sample permissions
    permissions = {
        "instagram_basic": "granted",
        "instagram_manage_insights": "revoked",
        "pages_read_engagement": "granted",
        "ads_read": "granted",
        "ads_management": "revoked",
        "instagram_manage_comments": "granted"
    }
    
    # Create sample history
    history = []
    
    # Add some history entries
    for permission, status in permissions.items():
        # First, add granted status
        history.append(
            PermissionChange(
                permission=permission,
                status="granted",
                timestamp=datetime.now(timezone.utc).replace(day=1)
            ).dict()
        )
        
        # For revoked permissions, add revocation entry
        if status == "revoked":
            history.append(
                PermissionChange(
                    permission=permission,
                    status="revoked",
                    timestamp=datetime.now(timezone.utc)
                ).dict()
            )
    
    # Create the permissions document
    meta_permissions = MetaPermissions(
        store_id=store_id,
        business_id="sample_business_123",
        permissions=permissions,
        last_updated=datetime.now(timezone.utc),
        revocation_history=history
    )
    
    # Insert into database
    await db_analysis["meta_permissions"].insert_one(meta_permissions.dict())
    logger.info(f"✅ Created sample permissions data for store ID: {store_id}")

async def main():
    """Main entry point"""
    logger.info("Starting Meta permissions system setup and validation")
    
    # Validate the system
    if await validate_meta_permissions_system():
        # Create sample data
        await create_sample_permissions()
        logger.info("Meta permissions system setup completed successfully")
    else:
        logger.error("Meta permissions system validation failed. Setup incomplete.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 