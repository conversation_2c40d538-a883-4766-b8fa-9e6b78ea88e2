import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  List,
  ListItem,
  ListItemText,
  Collapse,
  IconButton,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Theme
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { Customer } from '../../services/storeService';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/locationUtils';
import { format } from 'date-fns';

interface CustomerDetailViewProps {
  customer: Customer | null;
}

const CustomerDetailView: React.FC<CustomerDetailViewProps> = ({ customer }) => {
  const { t } = useTranslation();
  const [expandedOrders, setExpandedOrders] = useState<Record<number, boolean>>({});

  const toggleOrderExpansion = (orderId: number) => {
    setExpandedOrders(prev => ({ ...prev, [orderId]: !prev[orderId] }));
  };

  const renderDetailItem = (labelKey: string, defaultValue: string, value: string | number | undefined | null) => (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
      <Typography variant="body2" color="text.secondary">
        {t(labelKey, defaultValue)}
      </Typography>
      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
        {value ?? 'N/A'}
      </Typography>
    </Box>
  );

  const renderDistributionList = (title: string, distribution: Record<string, number> | undefined | null) => {
    if (!distribution || Object.keys(distribution).length === 0) return null;
    const entries = Object.entries(distribution);

    return (
      <Box mb={2}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>{title}</Typography>
        <List dense disablePadding>
          {entries.map(([key, value]) => (
            <ListItem
              key={key}
              disableGutters
              sx={theme => theme.palette.mode === 'dark' ? {
                py: 0.5,
                px: 1.5,
                mb: 1,
                backgroundColor: theme.palette.background.paper,
                color: '#fff',
                borderRadius: 2,
                transition: 'background 0.2s',
                '&:hover': {
                  backgroundColor: '#222',
                  color: '#fff',
                },
              } : {
                py: 0.5,
                px: 1.5,
                mb: 1
              }}
            >
              <ListItemText
                primary={key}
                secondary={`${t('common.countPrefix', 'Count:')} ${value}`}
                primaryTypographyProps={{ variant: 'body2', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
                secondaryTypographyProps={{ variant: 'caption', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
              />
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h6" gutterBottom>
          {t('customerDetailView.title', 'Customer Details')}
        </Typography>
        {!customer ? (
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Typography variant="body2" color="text.secondary" fontStyle="italic">
              {t('customerDetailView.placeholder', 'Select a customer from the list to view details.')}
            </Typography>
          </Box>
        ) : (
          <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
            {/* Core Details */}
            {renderDetailItem('customerDetailView.name', 'Name', customer.customer_name)}
            {renderDetailItem('customerDetailView.email', 'Email', customer.customer_email)}
            {renderDetailItem('customerDetailView.totalSpend', 'Total Spend', formatCurrency(customer.total_spend))}
            {renderDetailItem('customerDetailView.firstOrderDate', 'First Order', customer.first_order_date ? format(new Date(customer.first_order_date), 'yyyy-MM-dd') : 'N/A')}
            {renderDetailItem('customerDetailView.lastOrderDate', 'Last Order', customer.last_order_date ? format(new Date(customer.last_order_date), 'yyyy-MM-dd') : 'N/A')}
            {renderDetailItem('customerDetailView.preferredPayment', 'Pref. Payment', customer.preferred_payment_method)}
            {renderDetailItem('customerDetailView.preferredShipping', 'Pref. Shipping', customer.preferred_shipping_method)}
            {renderDetailItem('customerDetailView.country', 'Country', customer.country)}
            {renderDetailItem('customerDetailView.uniqueProducts', 'Unique Products', customer.unique_products_count)}

            <Divider sx={{ my: 2 }} />

            {/* Distributions */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2, mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                {renderDistributionList(t('customerDetailView.paymentMethods', 'Payment Methods Used'), customer.payment_methods)}
              </Box>
              <Box sx={{ flex: 1 }}>
                {renderDistributionList(t('customerDetailView.shippingMethods', 'Shipping Methods Used'), customer.shipping_methods)}
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Orders List */}
            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
              {t('customerDetailView.ordersTitle', 'Orders')}
            </Typography>
            {customer.orders && customer.orders.length > 0 ? (
              <List disablePadding>
                {customer.orders.map((order) => (
                  <React.Fragment key={order.order_id}>
                    <ListItem
                      button
                      onClick={() => toggleOrderExpansion(order.order_id)}
                      sx={theme => theme.palette.mode === 'dark' ? {
                        py: 1,
                        px: 1.5,
                        mb: 1,
                        backgroundColor: theme.palette.background.paper,
                        color: '#fff',
                        borderRadius: 2,
                        transition: 'background 0.2s',
                        display: 'flex',
                        justifyContent: 'space-between',
                        '&:hover': {
                          backgroundColor: '#222',
                          color: '#fff',
                        },
                      } : {
                        py: 1,
                        px: 1.5,
                        mb: 1,
                        display: 'flex',
                        justifyContent: 'space-between'
                      }}
                    >
                      <ListItemText
                        primary={`${t('customerDetailView.orderId', 'Order')} #${order.order_id} - ${format(new Date(order.order_date), 'yyyy-MM-dd')}`}
                        secondary={`${order.status} - ${formatCurrency(order.total)}`}
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
                        secondaryTypographyProps={{ variant: 'caption', sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
                      />
                      <IconButton edge="end" size="small">
                        {expandedOrders[order.order_id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </ListItem>
                    <Collapse in={expandedOrders[order.order_id]} timeout="auto" unmountOnExit>
                      <Box sx={{ pl: 2, pr: 1, py: 1, borderLeft: '2px solid', borderColor: 'divider', mb: 1, ml: 1 }}>
                        <Typography variant="caption" display="block" gutterBottom color="text.secondary">
                          {`${t('customerDetailView.paymentMethod', 'Payment')}: ${order.payment_method ?? 'N/A'} | ${t('customerDetailView.shippingMethod', 'Shipping')}: ${order.shipping_method ?? 'N/A'}`}
                          {order.coupon_code && ` | ${t('customerDetailView.coupon', 'Coupon')}: ${order.coupon_code} (${formatCurrency(order.coupon_amount)})`}
                        </Typography>
                        {order.products && order.products.length > 0 ? (
                          <TableContainer component={Paper} variant="outlined" sx={{ mb: 1 }}>
                            <Table size="small" aria-label="order products">
                              <TableHead>
                                <TableRow>
                                  <TableCell sx={{ py: 0.5 }}>{t('customerDetailView.productHeader', 'Product')}</TableCell>
                                  <TableCell align="right" sx={{ py: 0.5 }}>{t('customerDetailView.qtyHeader', 'Qty')}</TableCell>
                                  <TableCell align="right" sx={{ py: 0.5 }}>{t('customerDetailView.priceHeader', 'Price')}</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {order.products.map((product) => (
                                  <TableRow key={product.product_id}>
                                    <TableCell sx={{ py: 0.5 }}>{product.product_name}</TableCell>
                                    <TableCell align="right" sx={{ py: 0.5 }}>{product.quantity}</TableCell>
                                    <TableCell align="right" sx={{ py: 0.5 }}>{formatCurrency(product.price)}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        ) : (
                          <Typography variant="caption" display="block" fontStyle="italic">
                            {t('customerDetailView.noProducts', 'No product details available for this order.')}
                          </Typography>
                        )}
                      </Box>
                    </Collapse>
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary" fontStyle="italic">
                {t('customerDetailView.noOrders', 'No orders found for this customer.')}
              </Typography>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default CustomerDetailView; 