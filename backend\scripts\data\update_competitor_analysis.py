#!/usr/bin/env python3
"""
update_competitor_analysis.py - Enhanced Competitor Analysis Generator with Search Caching

This script performs deep search-based competitor analysis and updates the global_analysis collection.
It uses external search APIs (Brave Search) with intelligent caching to minimize API requests
while maintaining search quality and relevance.

Usage:
    python update_competitor_analysis.py [--store_id STORE_ID] [--limit LIMIT] [--force-refresh]

Options:
    --store_id STORE_ID    Specific store ID to analyze
    --limit LIMIT          Limit the number of stores to process
    --force-refresh        Force refresh of cached search results
"""

import os
import time
import json
import logging
import argparse
import requests
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Union, Any, Tuple
import random
import hashlib
from pymongo import MongoClient, ASCENDING
from openai import OpenAI
from dotenv import load_dotenv
from config.settings import get_settings
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('competitor_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

settings = get_settings()

class BusinessTypeMapper:
    """Maps business types to search terms and patterns"""
    
    BUSINESS_TYPE_KEYWORDS = {
        # Optical/Eyewear
        'venta de accesorios': {
            'search_terms': ['optical stores', 'ópticas', 'lentes', 'gafas', 'anteojos', 'eyewear'],
            'business_descriptors': ['óptica', 'optical', 'vision center', 'centro óptico'],
            'product_terms': ['sunglasses', 'lentes de sol', 'gafas de sol', 'prescription glasses', 'lentes de contacto'],
            'competitor_indicators': ['optical chain', 'cadena óptica', 'eyewear retailer']
        },
        
        # Artisanal/Craft items (NEW)
        'artesanías': {
            'search_terms': ['craft stores', 'artisan shops', 'handmade goods', 'artesanías', 'talleres artesanales'],
            'business_descriptors': ['artisan', 'craftsperson', 'workshop', 'taller', 'artesano'],
            'product_terms': ['handmade', 'artisanal', 'craft', 'traditional', 'folkloric', 'heritage'],
            'competitor_indicators': ['craft guild', 'artisan collective', 'handmade brand']
        },
        
        # Cutlery/Knives (NEW)
        'cuchillería': {
            'search_terms': ['cutlery stores', 'knife shops', 'cuchillerías', 'knife makers', 'blade smiths'],
            'business_descriptors': ['cutlery', 'knives', 'blades', 'cuchillería', 'knife shop'],
            'product_terms': ['knives', 'cutlery', 'blades', 'cuchillos', 'traditional knives', 'artisan knives'],
            'competitor_indicators': ['knife manufacturer', 'cutlery brand', 'blade maker']
        },
        
        # BBQ/Grilling Accessories (NEW)
        'parrilla': {
            'search_terms': ['bbq accessories', 'grilling tools', 'asado equipment', 'barbecue supplies'],
            'business_descriptors': ['bbq', 'grilling', 'barbecue', 'asado', 'parrilla'],
            'product_terms': ['grilling tools', 'bbq sets', 'barbecue accessories', 'asado equipment'],
            'competitor_indicators': ['bbq equipment', 'grilling brand', 'barbecue supplier']
        },
        
        # Combined Cutlery & BBQ (NEW)
        'cuchillería y accesorios de parrilla': {
            'search_terms': ['knife and bbq stores', 'cutlery grilling tools', 'artisan knives bbq', 'gaucho accessories'],
            'business_descriptors': ['artisan cutlery', 'gaucho knives', 'traditional knives', 'bbq cutlery'],
            'product_terms': ['gaucho knives', 'asado knives', 'grilling cutlery', 'traditional blades', 'bbq tools'],
            'competitor_indicators': ['artisan knife maker', 'traditional cutlery', 'gaucho brand']
        },
        
        # Artisanal Cutlery (NEW)
        'cuchillería artesanal': {
            'search_terms': ['artisan knives', 'handmade cutlery', 'traditional knives', 'custom knives'],
            'business_descriptors': ['artisan cutlery', 'knife maker', 'blade smith', 'custom knives'],
            'product_terms': ['handmade knives', 'artisan blades', 'traditional cutlery', 'custom knives'],
            'competitor_indicators': ['knife maker', 'blade smith', 'artisan cutlery']
        },
        
        # BBQ Accessories (NEW)
        'accesorios de parrilla': {
            'search_terms': ['bbq accessories', 'grilling equipment', 'asado tools', 'barbecue gear'],
            'business_descriptors': ['bbq equipment', 'grilling tools', 'asado accessories', 'barbecue supplies'],
            'product_terms': ['grilling tools', 'bbq equipment', 'asado accessories', 'barbecue sets'],
            'competitor_indicators': ['bbq equipment', 'grilling supplier', 'barbecue brand']
        },
        
        # Clothing/Fashion
        'tiendas de indumentaria': {
            'search_terms': ['clothing stores', 'tiendas de ropa', 'fashion retailers', 'boutiques'],
            'business_descriptors': ['boutique', 'tienda de moda', 'fashion store', 'clothing retailer'],
            'product_terms': ['vestidos', 'dresses', 'ropa', 'clothes', 'moda', 'fashion'],
            'competitor_indicators': ['fashion chain', 'cadena de ropa', 'clothing brand']
        },
        
        # Grocery/Food
        'tienda de comestibles': {
            'search_terms': ['grocery stores', 'almacenes', 'supermercados', 'organic stores', 'tiendas naturales'],
            'business_descriptors': ['almacén', 'supermercado', 'grocery', 'market', 'mercado'],
            'product_terms': ['organic', 'natural', 'vegan', 'alimentos', 'food', 'comida'],
            'competitor_indicators': ['supermarket chain', 'cadena de supermercados', 'grocery retailer']
        },
        
        # Fitness/Gym
        'gimnasio/centro de fitness': {
            'search_terms': ['gyms', 'gimnasios', 'fitness centers', 'centros deportivos'],
            'business_descriptors': ['gym', 'gimnasio', 'fitness center', 'centro fitness'],
            'product_terms': ['fitness', 'training', 'entrenamiento', 'workout', 'ejercicio'],
            'competitor_indicators': ['gym chain', 'cadena de gimnasios', 'fitness franchise']
        },
        
        # Furniture
        'hogar. mueblerías': {
            'search_terms': ['furniture stores', 'mueblerías', 'home decor', 'decoración'],
            'business_descriptors': ['mueblería', 'furniture store', 'home store', 'tienda de hogar'],
            'product_terms': ['muebles', 'furniture', 'sofá', 'mesa', 'silla', 'decoración'],
            'competitor_indicators': ['furniture chain', 'cadena de mueblerías', 'home retailer']
        },
        
        # Home Goods
        'artículos para el hogar': {
            'search_terms': ['home goods stores', 'tiendas de hogar', 'housewares', 'artículos del hogar'],
            'business_descriptors': ['home store', 'tienda del hogar', 'housewares', 'bazar'],
            'product_terms': ['electrodomésticos', 'appliances', 'cocina', 'kitchen', 'baño', 'bathroom'],
            'competitor_indicators': ['home goods chain', 'cadena del hogar', 'housewares retailer']
        },
        
        # Shoes
        'tiendas de zapatos': {
            'search_terms': ['shoe stores', 'zapaterías', 'footwear retailers', 'calzado'],
            'business_descriptors': ['zapatería', 'shoe store', 'footwear', 'calzado'],
            'product_terms': ['zapatos', 'shoes', 'zapatillas', 'sneakers', 'botas', 'boots'],
            'competitor_indicators': ['shoe chain', 'cadena de zapaterías', 'footwear brand']
        },
        
        # Electronics
        'electrónica': {
            'search_terms': ['electronics stores', 'tiendas de electrónica', 'tech stores', 'tecnología'],
            'business_descriptors': ['electronics', 'electrónica', 'tech store', 'tienda tecnología'],
            'product_terms': ['smartphones', 'computers', 'computadoras', 'tablets', 'gadgets'],
            'competitor_indicators': ['electronics chain', 'cadena electrónica', 'tech retailer']
        },
        
        # Default/General
        'default': {
            'search_terms': ['retail stores', 'tiendas', 'shops', 'comercios'],
            'business_descriptors': ['store', 'tienda', 'shop', 'comercio'],
            'product_terms': ['products', 'productos', 'items', 'artículos'],
            'competitor_indicators': ['retail chain', 'cadena comercial', 'retailer']
        }
    }
    
    @classmethod
    def get_keywords(cls, business_type: str) -> dict:
        """Get keywords for a business type, with fallback to default"""
        # Normalize business type
        business_type_lower = business_type.lower().strip()
        
        # Try exact match first
        if business_type_lower in cls.BUSINESS_TYPE_KEYWORDS:
            return cls.BUSINESS_TYPE_KEYWORDS[business_type_lower]
        
        # Try partial matches
        for key, value in cls.BUSINESS_TYPE_KEYWORDS.items():
            if key in business_type_lower or business_type_lower in key:
                return value
        
        # Check for common terms
        if any(term in business_type_lower for term in ['cuchillo', 'cuchillería', 'knife', 'cutlery', 'blade']):
            return cls.BUSINESS_TYPE_KEYWORDS['cuchillería']
        elif any(term in business_type_lower for term in ['artesanal', 'artisan', 'craft', 'handmade', 'taller']):
            return cls.BUSINESS_TYPE_KEYWORDS['artesanías']
        elif any(term in business_type_lower for term in ['parrilla', 'asado', 'bbq', 'barbecue', 'grilling']):
            return cls.BUSINESS_TYPE_KEYWORDS['parrilla']
        elif any(term in business_type_lower for term in ['óptica', 'optical', 'lentes', 'gafas']):
            return cls.BUSINESS_TYPE_KEYWORDS['venta de accesorios']
        elif any(term in business_type_lower for term in ['ropa', 'clothing', 'fashion', 'moda']):
            return cls.BUSINESS_TYPE_KEYWORDS['tiendas de indumentaria']
        elif any(term in business_type_lower for term in ['gimnasio', 'gym', 'fitness']):
            return cls.BUSINESS_TYPE_KEYWORDS['gimnasio/centro de fitness']
        elif any(term in business_type_lower for term in ['mueble', 'furniture', 'hogar']):
            return cls.BUSINESS_TYPE_KEYWORDS['hogar. mueblerías']
        elif any(term in business_type_lower for term in ['zapato', 'shoe', 'calzado']):
            return cls.BUSINESS_TYPE_KEYWORDS['tiendas de zapatos']
        elif any(term in business_type_lower for term in ['electro', 'electronic']):
            return cls.BUSINESS_TYPE_KEYWORDS['electrónica']
        elif any(term in business_type_lower for term in ['comestible', 'grocery', 'almacén', 'food']):
            return cls.BUSINESS_TYPE_KEYWORDS['tienda de comestibles']
        
        # Return default if no match
        return cls.BUSINESS_TYPE_KEYWORDS['default']

class SearchCache:
    """Handles caching of search results to minimize API requests"""
    
    def __init__(self, db, cache_duration_days: int = 7):
        """
        Initialize the search cache.
        
        Args:
            db: MongoDB database instance
            cache_duration_days: How long to keep cached results (default: 7 days)
        """
        self.collection = db['search_cache']
        self.cache_duration = timedelta(days=cache_duration_days)
        
        # Create indexes for efficient queries
        self._ensure_indexes()
        
    def _ensure_indexes(self):
        """Create necessary indexes for the cache collection"""
        try:
            # Index for query hash (primary lookup)
            self.collection.create_index("query_hash", unique=True)
            # Index for expiration (for cleanup)
            self.collection.create_index("expires_at", expireAfterSeconds=0)
            # Compound index for similarity searches
            self.collection.create_index([("query_type", ASCENDING), ("created_at", ASCENDING)])
            logger.info("Search cache indexes created/verified")
        except Exception as e:
            logger.error(f"Error creating cache indexes: {str(e)}")
    
    def _generate_query_hash(self, query: str) -> str:
        """Generate a hash for the query to use as cache key"""
        # Normalize query for better cache hits
        normalized_query = query.lower().strip()
        return hashlib.sha256(normalized_query.encode()).hexdigest()
    
    def _extract_query_type(self, query: str) -> str:
        """Extract the type of query for similarity matching"""
        # Identify query patterns
        if "competitors" in query.lower():
            return "competitor_search"
        elif "brands" in query.lower() and "stores" in query.lower():
            return "brand_search"
        elif "market leaders" in query.lower():
            return "market_analysis"
        elif any(term in query.lower() for term in ["e-commerce", "online retail"]):
            return "ecommerce_search"
        else:
            return "general_search"
    
    def get(self, query: str, max_age_days: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Get cached search results if available and not expired.
        
        Args:
            query: The search query
            max_age_days: Override default cache duration
            
        Returns:
            Cached result dict or None if not found/expired
        """
        try:
            query_hash = self._generate_query_hash(query)
            
            # Look for exact match
            cached_result = self.collection.find_one({
                "query_hash": query_hash,
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            })
            
            if cached_result:
                # Ensure both datetimes are timezone-aware
                created_at = cached_result['created_at']
                if created_at.tzinfo is None:
                    created_at = created_at.replace(tzinfo=timezone.utc)
                age = datetime.now(timezone.utc) - created_at
                logger.info(f"Cache HIT for query (age: {age.days}d {age.seconds//3600}h): {query[:50]}...")
                return cached_result
            
            # If no exact match, look for similar recent queries
            query_type = self._extract_query_type(query)
            # Get current time once for consistency
            current_time = datetime.now(timezone.utc)
            three_days_ago = current_time - timedelta(days=3)
            
            similar_results = self.collection.find({
                "query_type": query_type,
                "expires_at": {"$gt": current_time},
                "created_at": {"$gt": three_days_ago}  # Very recent
            }).sort("created_at", -1).limit(1)
            
            for similar in similar_results:
                # Check if queries are similar enough
                if self._are_queries_similar(query, similar['original_query']):
                    logger.info(f"Cache SIMILAR HIT for query: {query[:50]}...")
                    return similar
            
            logger.info(f"Cache MISS for query: {query[:50]}...")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving from cache: {str(e)}")
            return None
    
    def _are_queries_similar(self, query1: str, query2: str) -> bool:
        """Check if two queries are similar enough to share results"""
        # Extract key terms
        terms1 = set(query1.lower().split())
        terms2 = set(query2.lower().split())
        
        # Calculate Jaccard similarity
        intersection = terms1.intersection(terms2)
        union = terms1.union(terms2)
        
        if not union:
            return False
            
        similarity = len(intersection) / len(union)
        
        # Consider queries similar if they share 70% of terms
        return similarity >= 0.7
    
    def set(self, query: str, results: str, metadata: Optional[Dict] = None) -> bool:
        """
        Cache search results.
        
        Args:
            query: The search query
            results: The search results
            metadata: Additional metadata to store
            
        Returns:
            Success boolean
        """
        try:
            query_hash = self._generate_query_hash(query)
            query_type = self._extract_query_type(query)
            
            cache_entry = {
                "query_hash": query_hash,
                "original_query": query,
                "query_type": query_type,
                "results": results,
                "created_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + self.cache_duration,
                "metadata": metadata or {},
                "hit_count": 0
            }
            
            # Upsert the cache entry
            self.collection.replace_one(
                {"query_hash": query_hash},
                cache_entry,
                upsert=True
            )
            
            logger.info(f"Cached search results for query: {query[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error caching results: {str(e)}")
            return False
    
    def increment_hit_count(self, query_hash: str):
        """Increment the hit count for a cached query"""
        try:
            self.collection.update_one(
                {"query_hash": query_hash},
                {"$inc": {"hit_count": 1}}
            )
        except Exception as e:
            logger.error(f"Error incrementing hit count: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the cache"""
        try:
            total_entries = self.collection.count_documents({})
            active_entries = self.collection.count_documents({
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            })
            
            # Get most hit queries
            top_queries = list(self.collection.find({
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            }).sort("hit_count", -1).limit(5))
            
            return {
                "total_entries": total_entries,
                "active_entries": active_entries,
                "expired_entries": total_entries - active_entries,
                "top_queries": [
                    {
                        "query": q['original_query'][:50] + "...",
                        "hits": q['hit_count']
                    } for q in top_queries
                ]
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {}

class CompetitorAnalysisUpdater:
    def __init__(self, mongo_uri: Optional[str] = None, force_refresh: bool = False, max_queries_per_store: int = 8):
        """
        Initialize the competitor analysis updater
        
        Args:
            mongo_uri: MongoDB connection URI
            force_refresh: Force refresh of cached search results
            max_queries_per_store: Maximum number of search queries per store (default: 5)
        """
        self.force_refresh = force_refresh
        self.max_queries_per_store = max_queries_per_store
        self.load_config()
        self.setup_connections()
        
        # Initialize search cache
        self.search_cache = SearchCache(self.analysis_db, cache_duration_days=7)
        
        # Track API usage
        self.api_requests_made = 0
        self.cache_hits = 0
        
    def load_config(self):
        """Load configuration from environment variables"""
        load_dotenv()
        self.mongodb_uri = os.getenv('MONGODB_CONNECTION')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.brave_search_api_key = os.getenv('BRAVE_SEARCH_API_KEY')
        
        # Search API URLs
        self.brave_search_url = "https://api.search.brave.com/res/v1/web/search"
        
        if not self.mongodb_uri or not self.openai_api_key:
            raise ValueError("Missing required environment variables: MONGODB_CONNECTION and OPENAI_API_KEY must be set")
        
        if not self.brave_search_api_key:
            logger.warning("BRAVE_SEARCH_API_KEY not set, web search functionality will be limited")
        
        logger.info(f"Configuration loaded")
            
    def setup_connections(self):
        """Setup MongoDB and OpenAI connections"""
        try:
            self.client = MongoClient(self.mongodb_uri)
            self.analysis_db = self.client['D-Unit-AnalysisGPT']
            self.analysis_collection = self.analysis_db['global_analysis']
            self.openai_client = OpenAI(api_key=self.openai_api_key)
            logger.info("Connections established")
        except Exception as e:
            logger.error(f"Error setting up connections: {str(e)}")
            raise

    def perform_deep_search(self, query: str) -> str:
        """
        Perform a deep search for the given query using Brave Search API with caching.
        
        Args:
            query: Search query
            
        Returns:
            String containing the search results summary
        """
        try:
            # Check cache first unless force refresh is enabled
            if not self.force_refresh:
                cached_result = self.search_cache.get(query)
                if cached_result:
                    self.cache_hits += 1
                    self.search_cache.increment_hit_count(cached_result['query_hash'])
                    return cached_result['results']
            
            logger.info(f"Performing fresh search for query: {query}")
            
            if not self.brave_search_api_key:
                logger.warning("No Brave Search API key available, returning placeholder content")
                return f"Placeholder search results for: {query} (No API key available)"
            
            headers = {
                "Accept": "application/json",
                "X-Subscription-Token": self.brave_search_api_key
            }
            
            params = {
                "q": query,
                "count": 15,  # Increased for more comprehensive results
                "freshness": "py"  # Past year for more relevant results
            }
            
            response = requests.get(self.brave_search_url, headers=headers, params=params)
            self.api_requests_made += 1
            
            if response.status_code != 200:
                logger.error(f"Error with Brave Search API: {response.status_code} - {response.text}")
                return f"Error performing search for: {query}"
            
            search_data = response.json()
            
            # Log the raw API response for debugging
            logger.info(f"Brave Search API response status: {response.status_code}")
            logger.info(f"Raw API response keys: {list(search_data.keys())}")
            if "web" in search_data:
                logger.info(f"Number of web results: {len(search_data['web'].get('results', []))}")
            
            # Extract and format the search results with more detail
            results = []
            if "web" in search_data and "results" in search_data["web"]:
                for i, result in enumerate(search_data["web"]["results"], 1):
                    title = result.get("title", "No title")
                    description = result.get("description", "No description")
                    url = result.get("url", "No URL")
                    
                    # Extract domain name for brand identification
                    domain = url.split('/')[2] if '/' in url else url
                    
                    # Log individual results for debugging
                    logger.info(f"Result {i}: Title='{title[:50]}...', Domain='{domain}', URL='{url}'")
                    
                    results.append(f"Title: {title}\nDomain: {domain}\nDescription: {description}\nURL: {url}\n")
            
            if not results:
                logger.warning(f"No search results found for query: {query}")
                logger.warning(f"Full API response: {json.dumps(search_data, indent=2)[:1000]}...")
                return f"No search results found for: {query}"
            
            logger.info(f"Successfully formatted {len(results)} search results for query: {query}")
            
            # Format the results into a cohesive summary
            formatted_results = "Search results for: " + query + "\n\n" + "\n\n".join(results)
            
            # Cache the results
            self.search_cache.set(query, formatted_results, {
                "result_count": len(results),
                "api_response_code": response.status_code
            })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error performing deep search: {str(e)}")
            return f"Error performing search: {str(e)}"
    
    def _extract_product_categories_and_keywords(self, store_id: str) -> Tuple[List[str], List[str]]:
        """Extract product categories and keywords from store data for better search queries."""
        try:
            db = self.analysis_db
            
            categories = []
            keywords = []
            
            # Get product details
            product_details = db['product_details_cache'].find_one({'_id': store_id}) or {}
            
            # Extract categories
            if 'category_summary' in product_details:
                cat_summary = product_details['category_summary']
                if isinstance(cat_summary, dict) and 'top_categories' in cat_summary:
                    categories.extend([cat['name'] for cat in cat_summary['top_categories'] if 'name' in cat])
            
            # Extract from products
            if 'products' in product_details:
                for product in product_details['products'][:10]:  # Sample first 10 products
                    if 'categories' in product and 'store_categories' in product['categories']:
                        for cat in product['categories']['store_categories']:
                            if 'name' in cat and cat['name'] not in categories:
                                categories.append(cat['name'])
            
            # Get keywords from active store
            active_store = db['active_stores_cache'].find_one({'_id': store_id}) or {}
            if 'keywords' in active_store:
                keywords = active_store['keywords']
            
            return categories[:5], keywords[:5]  # Limit to top 5 each
            
        except Exception as e:
            logger.error(f"Error extracting categories and keywords: {str(e)}")
            return [], []
    
    def _build_full_context_for_store(self, store_id: str) -> Dict[str, Any]:
        """Build a comprehensive context dictionary for the store using all relevant collections."""
        try:
            db = self.analysis_db
            context = {}
            
            # Fetch all relevant collections
            active_store = db['active_stores_cache'].find_one({'_id': store_id}) or {}
            product_details = db['product_details_cache'].find_one({'_id': store_id}) or {}
            customer_details = db['store_customers_cache'].find_one({'_id': store_id}) or {}
            global_analysis = db['global_analysis'].find_one({'_id': store_id}) or {}
            
            # Extract key information
            context['store_name'] = active_store.get('name', 'Unknown')
            original_business_type = active_store.get('business_type', 'E-commerce')
            context['country'] = active_store.get('country', {}).get('name', 'Unknown')
            context['city'] = active_store.get('city', {}).get('name', '')
            context['total_revenue'] = active_store.get('metrics', {}).get('total_revenue', 0)
            context['total_orders'] = active_store.get('metrics', {}).get('total_orders', 0)
            context['product_count'] = product_details.get('product_count', 0)
            context['customer_count'] = customer_details.get('total_customers', 0)
            
            # Get top products
            top_products = []
            if 'products' in product_details:
                sorted_products = sorted(
                    product_details['products'], 
                    key=lambda p: p.get('revenue', 0), 
                    reverse=True
                )[:5]
                for product in sorted_products:
                    top_products.append({
                        'name': product.get('name', ''),
                        'price': product.get('current_price', 0),
                        'category': product.get('categories', {}).get('primary_category', '')
                    })
            context['top_products'] = top_products
            
            # Get categories and keywords
            categories, keywords = self._extract_product_categories_and_keywords(store_id)
            context['categories'] = categories
            context['keywords'] = keywords
            
            # SMART BUSINESS TYPE DETECTION: Use actual product data to override generic types
            context['business_type'] = self._detect_actual_business_type(original_business_type, categories, top_products)
            context['original_business_type'] = original_business_type  # Keep original for reference
            
            # Get customer distribution
            context['country_distribution'] = customer_details.get('country_distribution', {})
            
            # Social media presence
            social_media = active_store.get('social_media', {})
            context['has_facebook'] = bool(social_media.get('facebook') and 'Suggest to Add' not in str(social_media.get('facebook')))
            context['has_instagram'] = bool(social_media.get('instagram') and 'Suggest to Add' not in str(social_media.get('instagram')))
            
            return context
            
        except Exception as e:
            logger.error(f"Error building full context for store {store_id}: {str(e)}")
            return {}
    
    def _detect_actual_business_type(self, original_type: str, categories: List[str], top_products: List[dict]) -> str:
        """Detect the actual business type based on product data, not just the generic classification"""
        
        # If original type is specific enough, keep it
        specific_types = ['cuchillería', 'parrilla', 'artesanías']
        if any(specific in original_type.lower() for specific in specific_types):
            return original_type
        
        # Analyze categories to determine actual business focus
        category_text = ' '.join(categories).lower()
        product_names = ' '.join([p.get('name', '') for p in top_products]).lower()
        combined_text = f"{category_text} {product_names}"
        
        # Check for specific business patterns
        if any(term in combined_text for term in ['cuchillo', 'knife', 'gaucho', 'criollo', 'facon']):
            if any(term in combined_text for term in ['parrilla', 'asado', 'barbecue', 'grilling']):
                return 'Cuchillería y Accesorios de Parrilla'  # Knives and BBQ accessories
            else:
                return 'Cuchillería Artesanal'  # Artisanal knives
        elif any(term in combined_text for term in ['parrilla', 'asado', 'barbecue', 'grilling']):
            return 'Accesorios de Parrilla'  # BBQ accessories
        elif any(term in combined_text for term in ['artesanal', 'handmade', 'craft', 'traditional']):
            return 'Artesanías'  # Crafts
        
        # If no specific pattern found, return original
        return original_type

    def get_enhanced_search_queries_v2(self, context: Dict[str, Any]) -> List[str]:
        """Generate more effective search queries for finding real competitors using actual product data"""
        queries = []
        
        # Extract context
        store_name = context.get('store_name', '')
        business_type = context.get('business_type', 'retail')
        country = context.get('country', '')
        city = context.get('city', '')
        categories = context.get('categories', [])
        top_products = context.get('top_products', [])
        
        # Get business-specific keywords
        keywords_dict = BusinessTypeMapper.get_keywords(business_type)
        
        # NEW: Extract specific product terms from actual product data
        product_terms = self._extract_specific_product_terms(context)
        
        # Strategy 1: Product-specific searches (NEW - most important)
        if product_terms:
            for term in product_terms[:3]:  # Top 3 specific product terms
                queries.extend([
                    f'"{term}" manufacturers {country}',
                    f'"{term}" suppliers {country} wholesale',
                    f'artisanal {term} {country} handmade',
                    f'{term} retailers {country} stores',
                ])
        
        # Strategy 2: Category-specific searches with product context
        for category in categories[:2]:
            # Clean category names for better searches
            clean_category = category.replace('Para la ', '').replace('Para el ', '')
            queries.extend([
                f'"{clean_category}" specialists {country}',
                f'{clean_category} artisans {country} traditional',
                f'{clean_category} stores {country} authentic',
            ])
        
        # Strategy 3: Official business directories (refined)
        main_product_type = product_terms[0] if product_terms else keywords_dict["search_terms"][0]
        queries.extend([
            f'"{country}" {main_product_type} directory manufacturers',
            f'chamber of commerce {country} {main_product_type}',
            f'yellow pages {country} artisanal {main_product_type}',
        ])
        
        # Strategy 4: Industry associations and trade organizations
        queries.extend([
            f'{main_product_type} association {country}',
            f'artisan guild {country} {main_product_type}',
            f'traditional crafts {country} {main_product_type}',
        ])
        
        # Strategy 5: Market research and industry reports
        queries.extend([
            f'market leaders {main_product_type} {country} 2024',
            f'top {main_product_type} brands {country}',
            f'{main_product_type} industry report {country}',
        ])
        
        # Strategy 6: Local business listings with verification
        if city:
            queries.extend([
                f'"{city}" {country} {main_product_type} workshop artisan',
                f'{main_product_type} stores "{city}" {country} address',
            ])
        
        # Strategy 7: Artisanal and traditional craft searches (for craft-based businesses)
        if any(term in ' '.join(product_terms + categories).lower() for term in ['artesanal', 'criollo', 'gaucho', 'traditional']):
            queries.extend([
                f'traditional crafts {country} {main_product_type}',
                f'artisan market {country} handmade {main_product_type}',
                f'cultural heritage {country} {main_product_type}',
            ])
        
        # Strategy 8: E-commerce and online presence (exclude marketplaces)
        marketplace_exclusions = '-Amazon -MercadoLibre -eBay -AliExpress -Shopee -Etsy'
        queries.extend([
            f'{main_product_type} online store {country} {marketplace_exclusions}',
            f'buy {main_product_type} {country} direct manufacturer {marketplace_exclusions}',
        ])
        
        # Remove duplicates and limit
        unique_queries = list(dict.fromkeys(queries))
        return unique_queries[:self.max_queries_per_store]
    
    def _extract_specific_product_terms(self, context: Dict[str, Any]) -> List[str]:
        """Extract specific product terms from actual product data for targeted searches"""
        product_terms = []
        categories = context.get('categories', [])
        top_products = context.get('top_products', [])
        
        # Extract terms from categories (clean and translate key terms)
        category_mapping = {
            'Cuchillería': ['knives', 'cutlery', 'cuchillos'],
            'Para la Parrilla': ['barbecue accessories', 'grilling tools', 'asado equipment'],
            'Para el gaucho': ['gaucho knives', 'traditional knives', 'folkloric cutlery'],
            'Tablas': ['cutting boards', 'wooden boards', 'tablas de cortar'],
            'Set de Asador': ['bbq sets', 'grilling sets', 'barbecue kits'],
            'Accesorios': ['accessories', 'tools', 'implements'],
        }
        
        for category in categories:
            if category in category_mapping:
                product_terms.extend(category_mapping[category])
        
        # Extract terms from product names
        product_name_terms = {
            'Set Artesanal': ['artisanal sets', 'handmade sets', 'craft sets'],
            'Cuchillo Criollo': ['criollo knives', 'traditional knives', 'folk knives'],
            'Set Asador': ['barbecue sets', 'grilling sets', 'asado sets'],
            'Tabla': ['cutting board', 'wooden board', 'chopping board'],
            'Hacha': ['axe', 'hatchet', 'cleaver'],
        }
        
        for product in top_products:
            product_name = product.get('name', '')
            for key_term, translations in product_name_terms.items():
                if key_term.lower() in product_name.lower():
                    product_terms.extend(translations)
        
        # If no specific terms found, use general craft/artisan terms
        if not product_terms:
            product_terms = ['artisanal products', 'handmade goods', 'craft items']
        
        # Remove duplicates and return top terms
        unique_terms = list(dict.fromkeys(product_terms))
        return unique_terms[:5]

    def get_enhanced_search_queries(self, context: Dict[str, Any]) -> List[str]:
        """Generate search queries adapted to business type and country - Legacy method"""
        # Use improved method
        return self.get_enhanced_search_queries_v2(context)

    def perform_deep_search_with_validation(self, query: str) -> Tuple[str, Dict[str, Any]]:
        """
        Perform search with additional metadata extraction for validation
        
        Returns:
            Tuple of (formatted_results, metadata_dict)
        """
        try:
            # Check cache first
            if not self.force_refresh:
                cached_result = self.search_cache.get(query)
                if cached_result:
                    self.cache_hits += 1
                    self.search_cache.increment_hit_count(cached_result['query_hash'])
                    return cached_result['results'], cached_result.get('metadata', {})
            
            logger.info(f"Performing fresh search for query: {query}")
            
            if not self.brave_search_api_key:
                return "No API key available", {}
            
            headers = {
                "Accept": "application/json",
                "X-Subscription-Token": self.brave_search_api_key
            }
            
            params = {
                "q": query,
                "count": 20,  # Get more results for better coverage
                "freshness": "py",  # Past year
                "text_decorations": False,
                "spellcheck": False
            }
            
            response = requests.get(self.brave_search_url, headers=headers, params=params)
            self.api_requests_made += 1
            
            if response.status_code != 200:
                logger.error(f"API error: {response.status_code}")
                return f"Error performing search", {}
            
            search_data = response.json()
            
            # Extract results with enhanced metadata
            results = []
            metadata = {
                'total_results': 0,
                'domains_found': [],
                'result_types': []
            }
            
            if "web" in search_data and "results" in search_data["web"]:
                metadata['total_results'] = len(search_data["web"]["results"])
                
                for i, result in enumerate(search_data["web"]["results"], 1):
                    title = result.get("title", "")
                    description = result.get("description", "")
                    url = result.get("url", "")
                    domain = result.get("domain", "")
                    
                    # Extract additional metadata
                    page_age = result.get("page_age", "")
                    language = result.get("language", "")
                    
                    # Check if this is an authoritative source
                    is_authoritative = self._is_authoritative_source(domain)
                    
                    result_entry = (
                        f"Result {i}:\n"
                        f"Title: {title}\n"
                        f"Domain: {domain}\n"
                        f"URL: {url}\n"
                        f"Description: {description}\n"
                    )
                    
                    if is_authoritative:
                        result_entry += "Source Type: Authoritative\n"
                    
                    if page_age:
                        result_entry += f"Page Age: {page_age}\n"
                    
                    results.append(result_entry)
                    metadata['domains_found'].append(domain)
                    
                    # Categorize result type
                    result_type = self._categorize_result_type(domain, title, description)
                    metadata['result_types'].append(result_type)
            
            formatted_results = f"Search Query: {query}\n\n" + "\n\n".join(results)
            
            # Cache the results with metadata
            self.search_cache.set(query, formatted_results, metadata)
            
            return formatted_results, metadata
            
        except Exception as e:
            logger.error(f"Error in search: {str(e)}")
            return f"Error: {str(e)}", {}

    def _is_authoritative_source(self, domain: str) -> bool:
        """Check if domain is an authoritative source"""
        authoritative_indicators = [
            '.gov', '.gob', '.edu', 
            'chamber', 'camara',
            'yellowpages', 'paginasamarillas',
            'directory', 'directorio',
            'association', 'asociacion',
            'federation', 'federacion'
        ]
        
        domain_lower = domain.lower()
        return any(indicator in domain_lower for indicator in authoritative_indicators)

    def _categorize_result_type(self, domain: str, title: str, description: str) -> str:
        """Categorize the type of search result"""
        combined_text = f"{domain} {title} {description}".lower()
        
        if any(term in combined_text for term in ['directory', 'directorio', 'yellow pages']):
            return 'directory'
        elif any(term in combined_text for term in ['news', 'noticia', 'press']):
            return 'news'
        elif any(term in combined_text for term in ['association', 'chamber', 'federation']):
            return 'organization'
        elif any(term in combined_text for term in ['market research', 'industry report']):
            return 'research'
        elif any(term in combined_text for term in ['review', 'comparison', 'versus']):
            return 'review'
        else:
            return 'general'
    
    def extract_competitor_names_improved(self, search_results: str, country: str, business_type: str) -> List[str]:
        """Extract and validate competitor names with enhanced verification"""
        try:
            logger.info(f"Extracting competitors for {business_type} in {country}")
            
            # Get business-specific keywords
            keywords_dict = BusinessTypeMapper.get_keywords(business_type)
            business_descriptors = keywords_dict['business_descriptors']
            
            # Enhanced validation: Track evidence for each potential competitor
            competitor_evidence = {}
            
            # Pattern 1: Extract from search result blocks with strict validation
            result_blocks = search_results.split('\n\n')
            for block in result_blocks:
                if 'Title:' in block and 'Domain:' in block and 'URL:' in block:
                    # Extract all components
                    title_match = re.search(r'Title:\s*(.+?)(?:\n|$)', block)
                    domain_match = re.search(r'Domain:\s*(.+?)(?:\n|$)', block)
                    url_match = re.search(r'URL:\s*(.+?)(?:\n|$)', block)
                    desc_match = re.search(r'Description:\s*(.+?)(?:\n|$)', block)
                    
                    if all([title_match, domain_match, url_match]):
                        title = title_match.group(1) if title_match else ""
                        domain = domain_match.group(1) if domain_match else ""
                        url = url_match.group(1) if url_match else ""
                        description = desc_match.group(1) if desc_match else ""
                        
                        # Extract potential business name
                        business_name = self._extract_verified_business_name(
                            title, domain, url, description, business_descriptors
                        )
                        
                        if business_name:
                            # Verify country context in the entire block
                            if self._verify_country_operation(block, country, business_name):
                                if business_name not in competitor_evidence:
                                    competitor_evidence[business_name] = {
                                        'domain': domain,
                                        'url': url,
                                        'mentions': 0,
                                        'evidence_snippets': [],
                                        'confidence_score': 0
                                    }
                                
                                competitor_evidence[business_name]['mentions'] += 1
                                competitor_evidence[business_name]['evidence_snippets'].append({
                                    'title': title,
                                    'description': description,
                                    'url': url
                                })
            
            # Pattern 2: Cross-reference with business directories or official sources
            directory_patterns = [
                rf'(?:{country})\s+(?:business\s+directory|yellow\s+pages|páginas\s+amarillas).*?([A-Z][a-zA-Z0-9\s&\'-]+)',
                rf'(?:chamber\s+of\s+commerce|cámara\s+de\s+comercio).*?{country}.*?([A-Z][a-zA-Z0-9\s&\'-]+)',
            ]
            
            for pattern in directory_patterns:
                matches = re.findall(pattern, search_results, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    business_name = match.strip()
                    if self._is_valid_business_name_strict(business_name):
                        if business_name not in competitor_evidence:
                            competitor_evidence[business_name] = {
                                'domain': None,
                                'url': None,
                                'mentions': 0,
                                'evidence_snippets': [],
                                'confidence_score': 0
                            }
                        competitor_evidence[business_name]['confidence_score'] += 2  # Higher weight for directories
            
            # Calculate confidence scores
            validated_competitors = []
            for name, evidence in competitor_evidence.items():
                # Scoring system
                score = 0
                
                # Has a domain/URL from the target country or region
                if evidence['domain']:
                    country_domains = self._get_country_domains(country)
                    if any(tld in evidence['domain'] for tld in country_domains):
                        score += 5
                    # Also consider regional domains for Uruguay market
                    elif country == 'Uruguay' and any(tld in evidence['domain'] for tld in ['.ar', '.com.ar']):
                        score += 3  # Argentina is regionally relevant
                
                # Domain relevance (business-specific domains)
                if evidence['domain']:
                    domain_lower = evidence['domain'].lower()
                    if any(term in domain_lower for term in ['gaucho', 'knife', 'cutlery', 'craft', 'artisan']):
                        score += 2
                
                # Multiple mentions increase confidence
                score += min(evidence['mentions'] * 2, 4)  # Cap at 4 points
                
                # Evidence quality
                for snippet in evidence['evidence_snippets']:
                    # Check if description explicitly mentions the business operates in the country
                    if self._confirms_country_operation(snippet['description'], country, name):
                        score += 3
                
                evidence['confidence_score'] += score
                
                # Lower threshold for small markets like Uruguay
                if evidence['confidence_score'] >= 2:  # Much lower threshold
                    validated_competitors.append({
                        'name': name,
                        'score': evidence['confidence_score'],
                        'evidence': evidence
                    })
                    logger.info(f"Validated competitor: {name} (confidence: {evidence['confidence_score']})")
            
            # Sort by confidence score
            validated_competitors.sort(key=lambda x: x['score'], reverse=True)
            
            # Return only the names, limited to top results
            return [comp['name'] for comp in validated_competitors[:15]]
            
        except Exception as e:
            logger.error(f"Error extracting competitors: {str(e)}")
            return []

    def extract_competitor_names(self, search_results: str, country: str, business_type: str) -> List[str]:
        """Extract and validate competitor names with strict verification - Legacy method"""
        # Use improved method
        return self.extract_competitor_names_improved(search_results, country, business_type)
    
    def _extract_verified_business_name(self, title: str, domain: str, url: str, 
                                       description: str, business_descriptors: List[str]) -> Optional[str]:
        """Extract business name with multiple verification methods"""
        
        # Method 1: Extract from domain (most reliable)
        domain_name = self._extract_name_from_domain(domain)
        if domain_name and self._is_valid_business_name_strict(domain_name):
            return domain_name
        
        # Method 2: Extract from title before separators
        title_patterns = [
            r'^([A-Z][a-zA-Z0-9\s&\'-]+?)\s*[-–—|:]',  # "Business Name - Description"
            r'^([A-Z][a-zA-Z0-9\s&\'-]+?)\s+(?:' + '|'.join(business_descriptors) + ')',
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                if self._is_valid_business_name_strict(name):
                    return name
        
        return None

    def _extract_name_from_domain(self, domain: str) -> Optional[str]:
        """Extract business name from domain"""
        # Remove common prefixes and TLDs
        domain_clean = domain.lower()
        domain_clean = re.sub(r'^(www\.|https?://)', '', domain_clean)
        domain_clean = re.sub(r'\.(com|net|org|co|es|mx|ar|uy|cl|pe|br)(\.[a-z]{2})?.*$', '', domain_clean)
        
        # Convert to business name format
        if domain_clean and len(domain_clean) > 2:
            # Handle hyphenated domains
            if '-' in domain_clean:
                return ' '.join(word.capitalize() for word in domain_clean.split('-'))
            else:
                return domain_clean.capitalize()
        
        return None

    def _is_valid_business_name_strict(self, name: str) -> bool:
        """Stricter validation for business names"""
        if not name or len(name) < 3 or len(name) > 40:
            return False
        
        # Must start with capital letter
        if not name[0].isupper():
            return False
        
        # Expanded list of invalid terms
        invalid_terms = [
            'home', 'search', 'results', 'page', 'sitio', 'tienda', 'store', 'shop',
            'online', 'comprar', 'buy', 'best', 'mejor', 'top', 'lista', 'list',
            'donde', 'where', 'click', 'here', 'aquí', 'más', 'more', 'about',
            'contact', 'products', 'productos', 'services', 'servicios'
        ]
        
        name_lower = name.lower()
        
        # Check whole name isn't an invalid term
        if name_lower in invalid_terms:
            return False
        
        # Check name doesn't start with invalid terms
        for term in invalid_terms:
            if name_lower.startswith(term + ' '):
                return False
        
        # Must contain at least 2 alphabetic characters
        alpha_count = sum(1 for c in name if c.isalpha())
        if alpha_count < 2:
            return False
        
        # Shouldn't be all uppercase (except acronyms of 2-4 letters)
        if name.isupper() and len(name) > 4:
            return False
        
        return True

    def _verify_country_operation(self, text: str, country: str, business_name: str) -> bool:
        """Verify that a business is relevant to the market (more flexible for small markets)"""
        text_lower = text.lower()
        country_lower = country.lower()
        business_lower = business_name.lower()
        
        # Direct country mentions
        if country_lower in text_lower:
            return True
        
        # Regional relevance for Uruguay (gaucho culture spans Argentina/Uruguay)
        if country_lower == 'uruguay':
            regional_terms = ['argentina', 'gaucho', 'facón', 'criollo', 'south america', 'traditional']
            if any(term in text_lower for term in regional_terms):
                return True
        
        # Business type relevance
        business_terms = ['artisan', 'handcraft', 'traditional', 'knife', 'cutlery', 'blade']
        if any(term in text_lower for term in business_terms):
            return True
        
        # Look for explicit operational indicators (original logic)
        operation_patterns = [
            rf'{business_lower}.*?(?:operates?|operating|locations?|stores?|tiendas?).*?{country_lower}',
            rf'{business_lower}.*?(?:based\s+in|ubicado\s+en|headquartered\s+in).*?{country_lower}',
            rf'{country_lower}.*?(?:retailers?|stores?|chains?|companies).*?{business_lower}',
        ]
        
        for pattern in operation_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE | re.DOTALL):
                return True
        
        # Check for country-specific domains
        country_domains = self._get_country_domains(country)
        for domain in country_domains:
            if f'{business_lower}{domain}' in text_lower or f'{business_lower.replace(" ", "")}{domain}' in text_lower:
                return True
        
        return False

    def _confirms_country_operation(self, description: str, country: str, business_name: str) -> bool:
        """Check if description confirms business operates in country"""
        desc_lower = description.lower()
        
        # Strong indicators of operation
        strong_indicators = [
            f'leading {country.lower()}',
            f'largest in {country.lower()}',
            f'{country.lower()}\'s premier',
            f'throughout {country.lower()}',
            f'across {country.lower()}',
            f'{country.lower()} locations',
            f'stores in {country.lower()}',
        ]
        
        return any(indicator in desc_lower for indicator in strong_indicators)

    def _get_country_domains(self, country: str) -> List[str]:
        """Get country-specific domains"""
        country_domains = {
            'Uruguay': ['.uy', '.com.uy'],
            'Argentina': ['.ar', '.com.ar'],
            'Mexico': ['.mx', '.com.mx'],
            'Spain': ['.es', '.com.es'],
            'Brazil': ['.br', '.com.br'],
            'Chile': ['.cl', '.com.cl'],
            'Colombia': ['.co', '.com.co'],
            'Peru': ['.pe', '.com.pe']
        }
        return country_domains.get(country, [])

    def verify_analysis_competitors(self, analysis: str, verified_competitors: List[str], context: Dict[str, Any]) -> str:
        """Ensure analysis only mentions verified competitors"""
        # Create a whitelist of allowed business names
        allowed_names = set(verified_competitors)
        allowed_names.add(context.get('store_name', ''))  # Allow the store being analyzed
        
        # Add country names and common terms to avoid false positives
        country = context.get('country', '')
        if country:
            allowed_names.add(country)
        
        # Common terms that shouldn't be filtered
        common_terms = {
            'Uruguay', 'Argentina', 'Mexico', 'Spain', 'Chile', 'Colombia', 'Peru', 'Brazil',
            'The', 'This', 'That', 'These', 'Those', 'When', 'Where', 'What', 'How', 'Why',
            'Business', 'Market', 'Industry', 'Company', 'Store', 'Brand', 'Product', 'Service'
        }
        allowed_names.update(common_terms)
        
        # Find all capitalized phrases that could be business names
        potential_names = re.findall(r'\b[A-Z][a-zA-Z0-9\s&\'-]{2,30}\b', analysis)
        
        for name in potential_names:
            name = name.strip()
            # Skip common words and verified names
            if name in allowed_names or len(name) <= 2:
                continue
                
            # If it's not in allowed names and looks like a business name, replace it
            if self._looks_like_business_name(name):
                logger.warning(f"Removing unverified name from analysis: {name}")
                analysis = analysis.replace(name, "[competitor name]")
        
        return analysis

    def _looks_like_business_name(self, name: str) -> bool:
        """Check if a string looks like a business name"""
        # Business names typically:
        # - Start with capital letter
        # - May contain common business suffixes
        # - Are not common English/Spanish words
        
        business_suffixes = ['LLC', 'Inc', 'Corp', 'Ltd', 'SA', 'SRL', 'SL']
        common_words = {
            'Market', 'Industry', 'Business', 'Company', 'Store', 'Shop', 'Brand',
            'Product', 'Service', 'Customer', 'Client', 'Sales', 'Revenue', 'Profit',
            'Analysis', 'Report', 'Study', 'Research', 'Data', 'Information'
        }
        
        # If it's a common word, don't treat as business name
        if name in common_words:
            return False
            
        # If it has business suffixes, likely a business name
        if any(suffix in name for suffix in business_suffixes):
            return True
            
        # If it's 2+ words and starts with capital, might be business name
        if len(name.split()) >= 2 and name[0].isupper():
            return True
            
        return False

    def get_detailed_competitor_analysis(self, context: Dict[str, Any], search_results: str, 
                                   competitor_names: List[str]) -> str:
        """Generate competitor analysis with strict anti-hallucination measures"""
        try:
            store_name = context.get('store_name', 'Unknown')
            business_type = context.get('business_type', 'E-commerce')
            country = context.get('country', 'Unknown')
            categories = context.get('categories', [])
            
            # Get business-specific keywords for better analysis
            keywords_dict = BusinessTypeMapper.get_keywords(business_type)
            
            # Prepare competitor section with verification
            if competitor_names:
                # Create verification snippets for each competitor
                competitor_verification = []
                for comp in competitor_names[:10]:
                    # Find snippet mentioning this competitor
                    snippet_pattern = rf'.{{0,150}}{re.escape(comp)}.{{0,150}}'
                    snippet_match = re.search(snippet_pattern, search_results, re.IGNORECASE)
                    if snippet_match:
                        competitor_verification.append(
                            f"- {comp} (Found in search: '...{snippet_match.group().strip()}...')"
                        )
                    else:
                        competitor_verification.append(f"- {comp}")
                
                competitors_section = "\n".join(competitor_verification)
                analysis_mode = "competitive"
            else:
                competitors_section = "NO COMPETITORS IDENTIFIED"
                analysis_mode = "market_only"
            
            # Build context summary
            context_summary = f"""
Store: {store_name}
Type: {business_type}
Location: {country}
Categories: {', '.join(categories[:3])}
Products: {context.get('product_count', 'N/A')}
Revenue: {context.get('total_revenue', 'N/A')}
"""
            
            # Create the prompt with strict rules
            system_message = f"""You are a market analyst specializing in {business_type} in {country}.

CRITICAL RULES - VIOLATION WILL RESULT IN IMMEDIATE FAILURE:
1. You may ONLY mention business names that appear in the "VERIFIED COMPETITORS" section
2. If NO competitors are listed, you MUST NOT invent or suggest any business names
3. Every competitive claim must reference the search evidence provided
4. Focus on actionable insights based on market analysis, not speculation
5. Use industry-specific terminology for {business_type}

BUSINESS TYPE CONTEXT:
- Industry: {business_type}
- Key terms: {', '.join(keywords_dict['business_descriptors'][:3])}
- Product focus: {', '.join(keywords_dict['product_terms'][:3])}"""

            user_prompt = f"""Analyze the competitive landscape for this store:

{context_summary}

VERIFIED COMPETITORS (ONLY mention these):
{competitors_section}

ANALYSIS MODE: {analysis_mode.upper()}

Required sections:

1. **MARKET OVERVIEW for {business_type.upper()} in {country.upper()}**
   - Current market dynamics and trends
   - Customer preferences in this sector
   - Price sensitivity and purchase drivers

2. **COMPETITIVE LANDSCAPE**
   {"- Analyze the verified competitors listed above" if competitor_names else "- Describe the general competitive environment without naming specific businesses"}
   - Market positioning opportunities
   - Gaps in the current market

3. **STRATEGIC POSITIONING for {store_name.upper()}**
   - Unique value proposition opportunities
   - Target customer segments
   - Differentiation strategies

4. **ACTIONABLE RECOMMENDATIONS**
   - 5 specific actions tailored to {business_type}
   - Marketing channels most effective for this industry
   - Operational improvements specific to {country}

Remember: {"Only discuss the verified competitors listed above" if competitor_names else "Do not mention any business names since none were verified"}."""

            # Call OpenAI with strict parameters
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=2000
            )
            
            analysis = response.choices[0].message.content
            
            # Post-process to ensure no hallucinated names (very conservative approach)
            if not competitor_names:
                # Only remove clear business names, not section headers or descriptive text
                # Look for specific patterns that indicate actual business entities
                business_name_pattern = r'\b[A-Z][a-zA-Z]{2,}\s+(?:Inc|LLC|Corp|Ltd|SA|SRL|Company|Co\.)\b'
                potential_names = re.findall(business_name_pattern, analysis or "")
                
                # Also look for standalone proper nouns that could be brand names (but be very selective)
                standalone_pattern = r'\b[A-Z][a-z]{2,}[A-Z][a-z]{2,}\b'  # CamelCase names like "MegaCorp"
                standalone_names = re.findall(standalone_pattern, analysis or "")
                
                all_potential_names = potential_names + standalone_names
                
                for name in all_potential_names:
                    # Only remove if it's clearly a business name and not analysis content
                    if (name != store_name and 
                        name not in ['Uruguay', 'Argentina', 'Mexico', 'Spain', country] and
                        # Don't remove common analysis terms
                        not any(word in name.lower() for word in [
                            'market', 'industry', 'sector', 'business', 'customer', 'analysis', 'report', 
                            'strategic', 'competitive', 'gaucho', 'arte', 'actionable', 'recommendations',
                            'value', 'proposition', 'sensitivity', 'drivers', 'strategies', 'improvements',
                            'overview', 'landscape', 'positioning', 'opportunities'
                        ]) and
                        # Must be a clear business entity pattern
                        (any(corp in name for corp in ['Inc', 'LLC', 'Corp', 'Ltd', 'SA', 'SRL', 'Company']) or
                         (len(name.split()) == 1 and len(name) > 8 and name[0].isupper() and name[1:].islower() == False))
                    ):
                        # Log potential hallucination
                        logger.warning(f"Potential hallucinated business name detected and removed: {name}")
                        analysis = (analysis or "").replace(name, "[business name removed]")
            
            # Add data source note
            data_source_note = f"""

---
**Data Sources & Verification**
- Analysis based on {len(competitor_names)} verified competitors from web search
- Search coverage: {business_type} sector in {country}
"""
            
            # Add DeepSearch recommendation
            deepsearch_note = f"""

**Need Deeper Insights?**
Use D-Unit Chat Console with DeepSearch enabled to:
- Get real-time pricing comparisons
- Analyze customer reviews and sentiment
- Track competitor promotions and campaigns
- Access detailed market share data

Example queries:
- "Compare {store_name} pricing with other {business_type.lower()} in {country}"
- "What marketing strategies work best for {business_type.lower()}?"
- "Show customer pain points in the {country} {business_type.lower()} market"
"""
            
            return f"{analysis}{data_source_note}{deepsearch_note}"
            
        except Exception as e:
            logger.error(f"Error generating analysis: {str(e)}")
            return f"Error generating analysis. Please check logs for details."

    def validate_search_results_quality(self, search_results: str, business_type: str, country: str) -> Tuple[bool, str]:
        """
        Validate search results quality and relevance.
        Returns (is_valid, filtered_results)
        """
        try:
            # Get business keywords
            keywords_dict = BusinessTypeMapper.get_keywords(business_type)
            required_terms = (
                keywords_dict['search_terms'] + 
                keywords_dict['business_descriptors'] + 
                keywords_dict['product_terms']
            )
            
            # Split results into blocks
            result_blocks = search_results.split('\n\n')
            filtered_blocks = []
            relevant_count = 0
            
            for block in result_blocks:
                block_lower = block.lower()
                
                # Check for irrelevant content
                irrelevant_indicators = [
                    'netflix', 'movie', 'película', 'film', 'tv show',
                    'wikipedia', 'news article', 'blog post', 'recipe',
                    'how to', 'tutorial', 'diy', 'definition of'
                ]
                
                if any(indicator in block_lower for indicator in irrelevant_indicators):
                    logger.debug(f"Filtering out irrelevant block: {block[:100]}...")
                    continue
                
                # Check for business relevance
                has_business_term = any(
                    term.lower() in block_lower 
                    for term in required_terms
                )
                
                # Check for country relevance (more flexible for small markets)
                has_country_mention = (
                    country.lower() in block_lower or
                    any(term in block_lower for term in ['uruguay', 'uruguayan', 'south america', 'latin america', 'argentina', 'gaucho'])
                )
                
                # Check for cultural/regional relevance (specific to this business type)
                has_cultural_relevance = any(term in block_lower for term in ['facón', 'gaucho', 'asado', 'criollo', 'traditional'])
                
                # More lenient relevance criteria for small markets like Uruguay
                if has_business_term or has_country_mention or has_cultural_relevance:
                    filtered_blocks.append(block)
                    if has_business_term or has_country_mention or has_cultural_relevance:
                        relevant_count += 1
            
            # Determine if we have enough quality results (more lenient for small markets)
            is_valid = relevant_count >= 1  # At least 1 relevant result (very lenient for Uruguay)
            
            filtered_results = '\n\n'.join(filtered_blocks)
            
            if not is_valid:
                logger.warning(
                    f"Low quality search results for {business_type} in {country}. "
                    f"Only {relevant_count} relevant results found."
                )
            
            return is_valid, filtered_results
            
        except Exception as e:
            logger.error(f"Error validating search results: {str(e)}")
            return False, search_results
    
    def process_store(self, store_id: Union[str, None]) -> bool:
        """Process a store with enhanced validation and business type adaptation"""
        try:
            if not store_id:
                logger.error("Store ID cannot be None")
                return False
            
            # Get comprehensive store context
            context = self._build_full_context_for_store(store_id)
            
            if not context:
                logger.warning(f"Could not build context for store {store_id}")
                return False
            
            store_name = context.get('store_name', f"Store {store_id}")
            business_type = context.get('business_type', 'general retail')
            country = context.get('country', 'Unknown')
            
            # Skip if no valid business type
            if 'suggestion' in business_type.lower() or not business_type:
                logger.warning(f"Store {store_name} has no valid business type. Skipping.")
                return False
            
            logger.info(f"Processing {store_name} ({business_type}) in {country}")
            
            # Generate enhanced search queries
            search_queries = self.get_enhanced_search_queries(context)
            logger.info(f"Generated {len(search_queries)} queries")
            
            # Perform searches and collect results
            all_search_results = []
            all_competitor_names = set()
            valid_results_count = 0
            
            for i, query in enumerate(search_queries):
                logger.info(f"Query {i+1}/{len(search_queries)}: {query}")
                
                # Perform search
                search_result = self.perform_deep_search(query)
                
                # Validate search results
                is_valid, filtered_results = self.validate_search_results_quality(
                    search_result, business_type, country
                )
                
                if is_valid:
                    valid_results_count += 1
                    all_search_results.append(filtered_results)
                    
                    # Extract competitors with business type context
                    competitors = self.extract_competitor_names(
                        filtered_results, country, business_type
                    )
                    all_competitor_names.update(competitors)
                    
                    logger.info(f"Found {len(competitors)} competitors in this search")
                else:
                    logger.warning(f"Search results for '{query}' were not relevant enough")
                
                # Rate limiting
                if self.api_requests_made > 0:
                    time.sleep(random.uniform(1, 2))
            
            # Check if we have enough valid results
            if valid_results_count < 1:
                logger.warning(
                    f"Insufficient valid search results for {store_name}. "
                    f"Only {valid_results_count} valid results found."
                )
            
            # Combine results
            combined_results = "\n\n===== SEARCH RESULTS =====\n\n".join(all_search_results)
            
            # Sort and limit competitors
            all_competitor_names = sorted(list(all_competitor_names))[:20]
            
            logger.info(f"Total verified competitors: {len(all_competitor_names)}")
            
            # Generate analysis
            analysis = self.get_detailed_competitor_analysis(
                context, combined_results, all_competitor_names
            )
            
            # Update database
            update_data = {
                "analysis.competitor_analysis": analysis,
                "analysis.identified_competitors": all_competitor_names,
                "analysis.competitor_count": len(all_competitor_names),
                "metadata.competitor_analysis_updated": datetime.now(timezone.utc),
                "metadata.competitor_search_queries": search_queries[:5],
                "metadata.business_type_used": business_type,
                "metadata.search_quality_score": valid_results_count / len(search_queries) if search_queries else 0
            }
            
            self.analysis_collection.update_one(
                {"_id": store_id},
                {"$set": update_data}
            )
            
            logger.info(f"✓ Updated {store_name} with {len(all_competitor_names)} competitors")
            return True
            
        except Exception as e:
            logger.error(f"✗ Error processing store {store_id}: {str(e)}")
            return False
    
    def get_store_ids(self) -> List[str]:
        """
        Get all store IDs from the analysis collection.
        
        Returns:
            List of store IDs to process
        """
        try:
            # Check total documents first
            total_docs = self.analysis_collection.count_documents({})
            logger.info(f"Total documents in global_analysis collection: {total_docs}")
            
            if total_docs == 0:
                logger.warning("No documents found in global_analysis collection")
                # Try checking other collections that might have store data
                for collection_name in ['active_stores_cache', 'store_customers_cache', 'product_details_cache']:
                    try:
                        count = self.analysis_db[collection_name].count_documents({})
                        logger.info(f"  {collection_name}: {count} documents")
                        if count > 0:
                            # Get sample store IDs from this collection
                            sample_ids = [doc["_id"] for doc in self.analysis_db[collection_name].find({}, {"_id": 1}).limit(5)]
                            logger.info(f"    Sample IDs: {sample_ids}")
                    except Exception as e:
                        logger.warning(f"  Error checking {collection_name}: {str(e)}")
                return []
            
            # Check if any documents have analysis field
            docs_with_analysis = self.analysis_collection.count_documents({"analysis": {"$exists": True}})
            logger.info(f"Documents with 'analysis' field: {docs_with_analysis}")
            
            # Get some sample documents to understand the structure
            sample_docs = list(self.analysis_collection.find({}).limit(3))
            logger.info(f"Sample document structure:")
            for i, doc in enumerate(sample_docs):
                logger.info(f"  Document {i+1}: ID={doc.get('_id')}, top-level fields={list(doc.keys())}")
                if 'analysis' in doc:
                    logger.info(f"    Analysis fields: {list(doc['analysis'].keys()) if isinstance(doc['analysis'], dict) else 'not a dict'}")
            
            # Prioritize stores without competitor analysis or with old analysis
            thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
            
            # First, get ALL stores (simplified approach)
            all_stores = self.analysis_collection.find({}, {"_id": 1})
            all_store_ids = [doc["_id"] for doc in all_stores]
            logger.info(f"All store IDs found: {len(all_store_ids)}")
            
            # First, get stores without any competitor analysis
            stores_without_analysis = self.analysis_collection.find(
                {
                    "$or": [
                        {"analysis": {"$exists": False}},
                        {"analysis.competitor_analysis": {"$exists": False}},
                        {"analysis.competitor_analysis": ""},
                        {"analysis.competitor_analysis": None},
                        {"analysis.competitor_analysis": "No specific competitor analysis available."}
                    ]
                },
                {"_id": 1}
            )
            without_analysis_ids = [doc["_id"] for doc in stores_without_analysis]
            logger.info(f"Stores without competitor analysis: {len(without_analysis_ids)}")
            
            # Then, get stores with old analysis
            stores_with_old_analysis = self.analysis_collection.find(
                {
                    "metadata.competitor_analysis_updated": {"$lt": thirty_days_ago}
                },
                {"_id": 1}
            ).sort("metadata.competitor_analysis_updated", 1)  # Oldest first
            old_analysis_ids = [doc["_id"] for doc in stores_with_old_analysis]
            logger.info(f"Stores with old competitor analysis: {len(old_analysis_ids)}")
            
            store_ids = []
            store_ids.extend(without_analysis_ids)
            store_ids.extend(old_analysis_ids)
            
            # If no stores found with the specific criteria, just process all stores
            if not store_ids and all_store_ids:
                logger.info(f"No stores found with specific criteria, processing all {len(all_store_ids)} stores")
                store_ids = all_store_ids
            
            # Remove duplicates while preserving order
            seen = set()
            unique_store_ids = []
            for sid in store_ids:
                if sid not in seen:
                    seen.add(sid)
                    unique_store_ids.append(sid)
            
            logger.info(f"Final list: {len(unique_store_ids)} stores to process")
            return unique_store_ids
            
        except Exception as e:
            logger.error(f"Error getting store IDs: {str(e)}")
            return []

    def run(self, store_id: Optional[str] = None, limit: Optional[int] = None) -> None:
        """Run competitor analysis for the stores.
        
        Args:
            store_id: Optional specific store ID to process
            limit: Optional limit on number of stores to process
        """
        if store_id:
            logger.info(f"Processing single store: {store_id}")
            success = self.process_store(store_id)
            if success:
                logger.info(f"Successfully processed store {store_id}")
            else:
                logger.error(f"Failed to process store {store_id}")
            return
            
        store_ids = self.get_store_ids()
        if not store_ids:
            logger.warning("No store IDs to process.")
            return

        # Apply limit if specified
        if limit and limit > 0:
            store_ids = store_ids[:limit]
            logger.info(f"Limited processing to {limit} stores")

        successful = 0
        failed = 0
        
        for i, current_store_id in enumerate(store_ids):
            if current_store_id is None:
                logger.error("Encountered None value in store_ids list. Skipping this entry.")
                continue
            
            logger.info(f"\nProcessing store {i+1}/{len(store_ids)}")
            
            if self.process_store(current_store_id):
                successful += 1
            else:
                failed += 1
            
            # Add a delay between processing each store to avoid rate limiting
            time.sleep(random.uniform(2.0, 4.0))
        
        # Print API usage statistics
        self.print_usage_stats()
        
        logger.info(f"\nCompleted processing: {successful} successful, {failed} failed")
    
    def print_usage_stats(self):
        """Print statistics about API usage and cache performance"""
        cache_stats = self.search_cache.get_cache_stats()
        
        total_searches = self.api_requests_made + self.cache_hits
        cache_hit_rate = (self.cache_hits / total_searches * 100) if total_searches > 0 else 0
        
        logger.info("\n" + "="*50)
        logger.info("API USAGE STATISTICS")
        logger.info("="*50)
        logger.info(f"Total searches performed: {total_searches}")
        logger.info(f"API requests made: {self.api_requests_made}")
        logger.info(f"Cache hits: {self.cache_hits}")
        logger.info(f"Cache hit rate: {cache_hit_rate:.1f}%")
        logger.info(f"API requests saved: {self.cache_hits}")
        
        if self.brave_search_api_key:
            # Estimate cost savings (example: $0.001 per API request)
            estimated_savings = self.cache_hits * 0.001
            logger.info(f"Estimated cost savings: ${estimated_savings:.2f}")
        
        logger.info("\nCACHE STATISTICS")
        logger.info(f"Total cache entries: {cache_stats.get('total_entries', 0)}")
        logger.info(f"Active cache entries: {cache_stats.get('active_entries', 0)}")
        logger.info(f"Expired cache entries: {cache_stats.get('expired_entries', 0)}")
        
        if cache_stats.get('top_queries'):
            logger.info("\nMost frequently cached queries:")
            for i, q in enumerate(cache_stats['top_queries'], 1):
                logger.info(f"  {i}. {q['query']} (hits: {q['hits']})")
        
        logger.info("="*50 + "\n")

def main():
    """Main entry point"""
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Update competitor analysis with deep search and caching")
    parser.add_argument("--store_id", help="Specific store ID to process")
    parser.add_argument("--limit", type=int, help="Limit number of stores to process")
    parser.add_argument("--force-refresh", action="store_true", help="Force refresh of cached search results")
    parser.add_argument("--max-queries", type=int, default=8, help="Maximum search queries per store (default: 8)")
    args = parser.parse_args()

    try:
        # Initialize the updater
        updater = CompetitorAnalysisUpdater(force_refresh=args.force_refresh, max_queries_per_store=args.max_queries)
        
        # Process based on command line arguments
        if args.store_id:
            logger.info(f"Processing single store: {args.store_id}")
            updater.process_store(args.store_id)
        else:
            # Run for all stores with optional limit
            updater.run(limit=args.limit)
            
        logger.info("Completed processing competitor analysis updates.")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())