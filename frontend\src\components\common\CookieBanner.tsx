import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  IconButton,
  Link,
  Paper,
  Stack,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
  Slide,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Tune as TuneIcon,
  Campaign as MarketingIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { TransitionProps } from '@mui/material/transitions';
import { useTranslation } from 'react-i18next';
import cookieService from '../../services/cookieService';
import { CookiePreferences } from '../../types/cookies';
import { COOKIE_CONFIG, COOKIE_CATEGORIES, translateDuration } from '../../config/cookies';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

interface CookieBannerProps {
  open?: boolean;
  onClose?: () => void;
}

const categoryIcons = {
  essential: <SecurityIcon />,
  functional: <TuneIcon />,
  analytics: <AnalyticsIcon />,
  performance: <SpeedIcon />,
  marketing: <MarketingIcon />
};

export const CookieBanner: React.FC<CookieBannerProps> = ({ 
  open: controlledOpen, 
  onClose 
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  // Create a wrapper function to match translateDuration's expected signature
  const translateWrapper = (key: string, fallback?: string) => {
    return t(key, { defaultValue: fallback || key });
  };
  
  const [open, setOpen] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [preferences, setPreferences] = useState<CookiePreferences>(
    cookieService.getPreferences()
  );

  useEffect(() => {
    if (controlledOpen !== undefined) {
      setOpen(controlledOpen);
    } else {
      setOpen(cookieService.shouldShowBanner());
    }
  }, [controlledOpen]);

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setOpen(false);
    }
  };

  const handleAcceptAll = () => {
    cookieService.acceptAll();
    handleClose();
  };

  const handleRejectAll = () => {
    cookieService.rejectAll();
    handleClose();
  };

  const handleSavePreferences = () => {
    cookieService.giveConsent(preferences);
    handleClose();
  };

  const handlePreferenceChange = (category: keyof CookiePreferences, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }));
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const getCookiesForCategory = (categoryId: string) => {
    return COOKIE_CONFIG.cookies.filter(cookie => cookie.category === categoryId);
  };

  const renderCookieTable = (categoryId: string) => {
    const cookies = getCookiesForCategory(categoryId);
    
    if (cookies.length === 0) return null;

    return (
      <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>{t('cookies.table.name', 'Cookie Name')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.purpose', 'Purpose')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.duration', 'Duration')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.type', 'Type')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.provider', 'Provider')}</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cookies.map((cookie, index) => (
              <TableRow key={`${cookie.name}-${index}`}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {cookie.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {t(cookie.purpose, cookie.purpose)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {translateDuration(cookie.duration, translateWrapper)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={t(`cookies.types.${cookie.type}`, cookie.type)} 
                    size="small" 
                    color={cookie.type === 'first-party' ? 'primary' : 'secondary'}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {cookie.provider}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const renderBannerContent = () => (
    <Card 
      elevation={8}
      sx={{ 
        m: 2,
        maxWidth: 500,
        mx: 'auto',
        position: 'relative'
      }}
    >
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <InfoIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6" component="h2">
            {t('cookies.banner.title', 'Cookie Consent')}
          </Typography>
          <IconButton 
            size="small" 
            onClick={handleClose}
            sx={{ ml: 'auto' }}
            aria-label={t('cookies.banner.close', 'Close')}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Typography variant="body2" paragraph>
          {t('cookies.banner.description', 
            'We use cookies to enhance your experience, analyze site usage, and assist with marketing. Essential cookies are required for the site to function properly.'
          )}
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            {t('cookies.banner.legalNotice', 
              'This website complies with GDPR, CCPA, LGPD, and other privacy regulations. You can withdraw consent at any time through the cookie settings.'
            )}
          </Typography>
        </Alert>

        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Button
            variant="contained"
            onClick={handleAcceptAll}
            size="small"
          >
            {t('cookies.banner.acceptAll', 'Accept All')}
          </Button>
          <Button
            variant="outlined"
            onClick={handleRejectAll}
            size="small"
          >
            {t('cookies.banner.rejectAll', 'Reject All')}
          </Button>
          <Button
            variant="text"
            startIcon={<SettingsIcon />}
            onClick={() => setShowDetails(true)}
            size="small"
          >
            {t('cookies.banner.customize', 'Customize')}
          </Button>
        </Stack>

        <Typography variant="caption" color="text.secondary">
          {t('cookies.banner.moreInfo', 'Learn more in our')}{' '}
          <Link href="https://d-unit.world/privacy-policy/" target="_blank" rel="noopener noreferrer">
            {t('cookies.banner.privacyPolicy', 'Privacy Policy')}
          </Link>
          {' '}and{' '}
          <Link href="/cookie-policy" target="_blank" rel="noopener noreferrer">
            {t('cookies.banner.cookiePolicy', 'Cookie Policy')}
          </Link>
        </Typography>
      </CardContent>
    </Card>
  );

  const renderDetailsDialog = () => (
    <Dialog
      open={showDetails}
      onClose={() => setShowDetails(false)}
      maxWidth="md"
      fullWidth
      TransitionComponent={Transition}
      PaperProps={{
        sx: { maxHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <SettingsIcon sx={{ mr: 1 }} />
          {t('cookies.details.title', 'Cookie Preferences')}
          <IconButton 
            onClick={() => setShowDetails(false)}
            sx={{ ml: 'auto' }}
            aria-label={t('cookies.details.close', 'Close')}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Typography variant="body1" paragraph>
          {t('cookies.details.description', 
            'Manage your cookie preferences below. You can enable or disable categories of cookies, and view detailed information about each cookie we use.'
          )}
        </Typography>

        <Stack spacing={3}>
          {COOKIE_CATEGORIES.map((category) => {
            const isExpanded = expandedCategories[category.id];
            const isEnabled = preferences[category.id as keyof CookiePreferences];
            
            return (
              <Card key={category.id} variant="outlined">
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box display="flex" alignItems="center" flex={1}>
                      <Box sx={{ mr: 1, color: theme.palette.primary.main }}>
                        {categoryIcons[category.id as keyof typeof categoryIcons]}
                      </Box>
                      <Box flex={1}>
                        <Typography variant="h6">
                          {t(`cookies.categories.${category.id}.name`, category.name)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {t(`cookies.categories.${category.id}.description`, category.description)}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Box display="flex" alignItems="center">
                      <FormControlLabel
                        control={
                          <Switch
                            checked={isEnabled}
                            onChange={(e) => handlePreferenceChange(
                              category.id as keyof CookiePreferences, 
                              e.target.checked
                            )}
                            disabled={category.essential}
                          />
                        }
                        label=""
                        sx={{ mr: 1 }}
                      />
                      <IconButton
                        onClick={() => toggleCategoryExpansion(category.id)}
                        size="small"
                      >
                        {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </Box>
                  </Box>
                  
                  <Collapse in={isExpanded}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom>
                      {t('cookies.category.cookiesUsed', 'Cookies in this category:')}
                    </Typography>
                    {renderCookieTable(category.id)}
                  </Collapse>
                </CardContent>
              </Card>
            );
          })}
        </Stack>

        <Box mt={3}>
          <Typography variant="body2" color="text.secondary">
            <strong>{t('cookies.details.version', 'Consent Version')}:</strong> {COOKIE_CONFIG.consentVersion}
            <br />
            <strong>{t('cookies.details.lastUpdated', 'Last Updated')}:</strong> {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setShowDetails(false)}>
          {t('cookies.details.cancel', 'Cancel')}
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => {
            setPreferences({
              essential: true,
              analytics: false,
              performance: false,
              functional: false,
              marketing: false,
              preferences: false
            });
          }}
        >
          {t('cookies.details.rejectAll', 'Reject All Optional')}
        </Button>
        <Button 
          variant="contained" 
          onClick={handleSavePreferences}
        >
          {t('cookies.details.save', 'Save Preferences')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (!open) return null;

  return (
    <>
      <Box
        position="fixed"
        bottom={0}
        left={0}
        right={0}
        zIndex={theme.zIndex.modal}
        sx={{
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          backdropFilter: 'blur(4px)'
        }}
      >
        {renderBannerContent()}
      </Box>
      {renderDetailsDialog()}
    </>
  );
};

// New component for first-time login cookie notice
interface FirstTimeCookieNoticeProps {
  open: boolean;
  onClose: () => void;
}

export const FirstTimeCookieNotice: React.FC<FirstTimeCookieNoticeProps> = ({ 
  open, 
  onClose 
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  // Create a wrapper function to match translateDuration's expected signature
  const translateWrapper = (key: string, fallback?: string) => {
    return t(key, { defaultValue: fallback || key });
  };
  
  const [showDetails, setShowDetails] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [preferences, setPreferences] = useState<CookiePreferences>(
    cookieService.getPreferences()
  );

  const handleAcceptAll = () => {
    cookieService.acceptAll();
    onClose();
  };

  const handleManagePreferences = () => {
    setShowDetails(true);
  };

  const handleSavePreferences = () => {
    cookieService.giveConsent(preferences);
    setShowDetails(false);
    onClose();
  };

  const handlePreferenceChange = (category: keyof CookiePreferences, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }));
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const getCookiesForCategory = (categoryId: string) => {
    return COOKIE_CONFIG.cookies.filter(cookie => cookie.category === categoryId);
  };

  const renderCookieTable = (categoryId: string) => {
    const cookies = getCookiesForCategory(categoryId);
    
    if (cookies.length === 0) return null;

    return (
      <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>{t('cookies.table.name', 'Cookie Name')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.purpose', 'Purpose')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.duration', 'Duration')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.type', 'Type')}</strong></TableCell>
              <TableCell><strong>{t('cookies.table.provider', 'Provider')}</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cookies.map((cookie, index) => (
              <TableRow key={`${cookie.name}-${index}`}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {cookie.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {t(cookie.purpose, cookie.purpose)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {translateDuration(cookie.duration, translateWrapper)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={t(`cookies.types.${cookie.type}`, cookie.type)} 
                    size="small" 
                    color={cookie.type === 'first-party' ? 'primary' : 'secondary'}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {cookie.provider}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const renderDetailsDialog = () => (
    <Dialog
      open={showDetails}
      onClose={() => setShowDetails(false)}
      maxWidth="md"
      fullWidth
      TransitionComponent={Transition}
      PaperProps={{
        sx: { maxHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <SettingsIcon sx={{ mr: 1 }} />
          {t('cookies.details.title', 'Cookie Preferences')}
          <IconButton 
            onClick={() => setShowDetails(false)}
            sx={{ ml: 'auto' }}
            aria-label={t('cookies.details.close', 'Close')}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Typography variant="body1" paragraph>
          {t('cookies.details.description', 
            'Manage your cookie preferences below. You can enable or disable categories of cookies, and view detailed information about each cookie we use.'
          )}
        </Typography>

        <Stack spacing={3}>
          {COOKIE_CATEGORIES.map((category) => {
            const isExpanded = expandedCategories[category.id];
            const isEnabled = preferences[category.id as keyof CookiePreferences];
            
            return (
              <Card key={category.id} variant="outlined">
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box display="flex" alignItems="center" flex={1}>
                      <Box sx={{ mr: 1, color: theme.palette.primary.main }}>
                        {categoryIcons[category.id as keyof typeof categoryIcons]}
                      </Box>
                      <Box flex={1}>
                        <Typography variant="h6">
                          {t(`cookies.categories.${category.id}.name`, category.name)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {t(`cookies.categories.${category.id}.description`, category.description)}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Box display="flex" alignItems="center">
                      <FormControlLabel
                        control={
                          <Switch
                            checked={isEnabled}
                            onChange={(e) => handlePreferenceChange(
                              category.id as keyof CookiePreferences, 
                              e.target.checked
                            )}
                            disabled={category.essential}
                          />
                        }
                        label=""
                        sx={{ mr: 1 }}
                      />
                      <IconButton
                        onClick={() => toggleCategoryExpansion(category.id)}
                        size="small"
                      >
                        {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </Box>
                  </Box>
                  
                  <Collapse in={isExpanded}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom>
                      {t('cookies.category.cookiesUsed', 'Cookies in this category:')}
                    </Typography>
                    {renderCookieTable(category.id)}
                  </Collapse>
                </CardContent>
              </Card>
            );
          })}
        </Stack>

        <Box mt={3}>
          <Typography variant="body2" color="text.secondary">
            <strong>{t('cookies.details.version', 'Consent Version')}:</strong> {COOKIE_CONFIG.consentVersion}
            <br />
            <strong>{t('cookies.details.lastUpdated', 'Last Updated')}:</strong> {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setShowDetails(false)}>
          {t('cookies.details.cancel', 'Cancel')}
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => {
            setPreferences({
              essential: true,
              analytics: false,
              performance: false,
              functional: false,
              marketing: false,
              preferences: false
            });
          }}
        >
          {t('cookies.details.rejectAll', 'Reject All Optional')}
        </Button>
        <Button 
          variant="contained" 
          onClick={handleSavePreferences}
        >
          {t('cookies.details.save', 'Save Preferences')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (!open) return null;

  return (
    <>
      <Box
        position="fixed"
        bottom={20}
        right={20}
        zIndex={theme.zIndex.modal + 1}
        sx={{
          maxWidth: 380,
          animation: 'slideInBottomRight 0.3s ease-out',
          '@keyframes slideInBottomRight': {
            '0%': {
              transform: 'translate(100%, 100%)',
              opacity: 0,
            },
            '100%': {
              transform: 'translate(0, 0)',
              opacity: 1,
            },
          },
        }}
      >
        <Card 
          elevation={12}
          sx={{ 
            bgcolor: theme.palette.mode === 'dark' ? '#2D2D2D' : 'white',
            border: `1px solid ${theme.palette.primary.main}`,
            borderRadius: 2,
          }}
        >
          <CardContent sx={{ p: 2 }}>
            <Box display="flex" alignItems="flex-start" mb={1}>
              <InfoIcon 
                color="primary" 
                sx={{ mr: 1, mt: 0.2, fontSize: '1.2rem' }} 
              />
              <Box flex={1}>
                <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                  {t('cookies.firstTime.title', '🍪 Welcome to D-Unit!')}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5, fontSize: '0.85rem' }}>
                  {t('cookies.firstTime.message', 
                    'We use cookies to enhance your experience. You can manage your preferences anytime.'
                  )}
                </Typography>
              </Box>
              <IconButton 
                size="small" 
                onClick={onClose}
                sx={{ ml: 1, p: 0.5 }}
                aria-label={t('cookies.firstTime.close', 'Close')}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>

            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="text"
                size="small"
                onClick={handleManagePreferences}
                sx={{ 
                  fontSize: '0.75rem',
                  textTransform: 'none',
                  minWidth: 'auto',
                  px: 1,
                }}
              >
                {t('cookies.firstTime.manage', 'Manage')}
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={handleAcceptAll}
                sx={{ 
                  fontSize: '0.75rem',
                  textTransform: 'none',
                  px: 2,
                  borderRadius: 1.5,
                }}
              >
                {t('cookies.firstTime.accept', 'Accept All')}
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </Box>
      {renderDetailsDialog()}
    </>
  );
};

export default CookieBanner; 