import React from 'react';
import {
  Card, 
  CardContent, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemText, 
  Divider, 
  Chip,
  Button
} from '@mui/material';
import { ProductDetail } from '../../services/storeService'; // Assuming ProductDetail includes the variation structure
import { useTranslation } from 'react-i18next';

type Variation = NonNullable<ProductDetail['variations']>[number];

interface ProductVariationDetailCardProps {
  variations: Variation[] | null | undefined;
  currencySymbol?: string;
}

const ProductVariationDetailCard: React.FC<ProductVariationDetailCardProps> = ({ 
  variations, 
  currencySymbol = '$'
}) => {
  const { t } = useTranslation();
  const [visibleVariationsCount, setVisibleVariationsCount] = React.useState(4);
  const [showAllVariations, setShowAllVariations] = React.useState(false);

  React.useEffect(() => {
    setVisibleVariationsCount(4);
    setShowAllVariations(false);
  }, [variations]);

  const handleLoadMore = () => {
    setVisibleVariationsCount(variations?.length || 0);
    setShowAllVariations(true);
  };

  const handleShowLess = () => {
    setVisibleVariationsCount(4);
    setShowAllVariations(false);
  };

  if (!variations || variations.length === 0) {
    return (
      <Card elevation={0} variant="outlined">
        <CardContent>
          <Typography color="text.secondary">{t('variationDetail.noData', 'Select a product to see variations.')}</Typography>
        </CardContent>
      </Card>
    );
  }

  const variationsToDisplay = showAllVariations ? variations : variations.slice(0, visibleVariationsCount);

  const hasMoreVariations = variations.length > visibleVariationsCount && !showAllVariations;
  const hasLessVariations = showAllVariations && variations.length > 4;

  return (
    <Card elevation={0} variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('variationDetail.cardTitle', 'Product Variations')}
        </Typography>
        <Box sx={{ maxHeight: 300, overflowY: 'auto', pr: 1 }}>
          <List disablePadding>
            {variationsToDisplay.map((variation, index) => (
              <React.Fragment key={variation.variation_id || index}>
                <ListItem alignItems="flex-start" sx={{ py: 2 }}>
                  <ListItemText
                    secondary={
                      <React.Fragment>
                        <Typography
                          sx={{ display: 'block' }}
                          component="span"
                          variant="body2"
                          color="text.primary"
                        >
                          {t('variationDetail.stock', 'Stock:')} {variation.variation_stock}
                        </Typography>
                        <Typography
                          sx={{ display: 'block' }}
                          component="span"
                          variant="body2"
                          color="text.primary"
                        >
                          {t('variationDetail.price', 'Price:')} {currencySymbol}{variation.variation_price.toLocaleString()}
                          {variation.variation_offer && (
                            <Chip 
                              label={`${t('variationDetail.offer', 'Offer')}: ${currencySymbol}${variation.variation_offer_price?.toLocaleString()}`}
                              size="small" 
                              color="success" 
                              sx={{ ml: 1 }}
                              variant="outlined"
                            />
                          )}
                        </Typography>
                        {variation.product_description && (
                          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                             {variation.product_description}
                          </Typography>
                        )}
                        {variation.attributes && variation.attributes.length > 0 && (
                          <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {variation.attributes.map(attr => (
                               <Chip key={attr.name} label={`${attr.name}: ${attr.value}`} size="small" />
                            ))}
                          </Box>
                        )}
                      </React.Fragment>
                    }
                  />
                </ListItem>
                {index < variationsToDisplay.length - 1 && <Divider component="li" />}
              </React.Fragment>
            ))}
          </List>
        </Box>
        
        {hasMoreVariations && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Button variant="outlined" onClick={handleLoadMore}>
              {t('variationDetail.loadMore', 'Load More Variations')}
            </Button>
          </Box>
        )}
        {hasLessVariations && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Button variant="outlined" onClick={handleShowLess}>
              {t('variationDetail.showLess', 'Show Less Variations')}
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductVariationDetailCard; 