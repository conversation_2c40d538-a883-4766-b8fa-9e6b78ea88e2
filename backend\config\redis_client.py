from __future__ import annotations

"""backend/config/redis_client.py

Centralised async-Redis client factory used by services that need a shared cache
(e.g. CSRF tokens, rate limiting, etc.).  Returns `None` if `REDIS_URL` is not
configured so callers can gracefully fall back to in-memory storage in local
development.
"""

import logging
from functools import lru_cache
from typing import Optional

import redis.asyncio as redis  # type: ignore

from config.settings import get_settings

logger = logging.getLogger(__name__)


@lru_cache(maxsize=1)
def _build_connection() -> Optional["redis.Redis"]:
    settings = get_settings()
    url = settings.REDIS_URL

    if not url or url.strip() == "":
        logger.warning(
            "REDIS_URL not configured – falling back to in-process storage for cache-backed services"
        )
        return None

    logger.info("Connecting to Redis at %s", url)
    return redis.from_url(url, decode_responses=True)


def get_redis() -> Optional["redis.Redis"]:
    """Return a memoised async Redis client or ``None`` if disabled."""

    return _build_connection() 