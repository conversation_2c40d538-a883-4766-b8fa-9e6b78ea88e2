// /ui/frontend/src/utils/logger.ts
const isDev = import.meta.env.DEV;

// Basic logger implementation
export const logger = {
  log: (...args: unknown[]) => {
    if (isDev) console.log(...args);
  },
  info: (...args: unknown[]) => {
    if (isDev) console.info(...args);
  },
  warn: (...args: unknown[]) => {
    // Keep warnings in production for now, can be adjusted later
    console.warn(...args);
  },
  error: (...args: unknown[]) => {
    // Keep errors in production
    console.error(...args);
  },
  debug: (...args: unknown[]) => {
    // Debug logs only appear in development
    if (isDev) console.debug(...args);
  },
  // Use this for objects that might contain sensitive info,
  // even if we've removed the most critical ones already.
  logSensitive: (message: string, data?: unknown) => {
    if (isDev) {
      console.log(message, data);
    } else {
      // In production, log the message but redact the data.
      console.log(message, data ? '[REDACTED]' : '');
    }
  }
};

export default logger; 