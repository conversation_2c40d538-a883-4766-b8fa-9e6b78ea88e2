from datetime import datetime, timedelta, timezone

import pytest

REGISTER_ENDPOINT = "/api/auth/dunit/register"
VERIFY_ENDPOINT = "/api/auth/dunit/verify-registration"


def _register(client, payload):
    return client.post(REGISTER_ENDPOINT, json=payload)


def test_registration_success_flow(client):
    test_client, env = client

    payload = {
        "email": "<EMAIL>",
        "name": "Nuevo Usuario",
        "store_name": "Tienda X",
        "password_dunit": "PassWord123",
    }

    resp = _register(test_client, payload)
    assert resp.status_code == 200

    # Obtener código enviado
    code = env["sent_codes"].get("<EMAIL>")
    assert code is not None

    verify_resp = test_client.post(
        VERIFY_ENDPOINT,
        json={"email": "<EMAIL>", "verification_code": code},
    )
    assert verify_resp.status_code == 200
    data = verify_resp.json()
    assert data.get("access_token") is not None


def test_registration_password_too_short(client):
    test_client, _ = client
    payload = {
        "email": "<EMAIL>",
        "name": "Nuevo Usuario",
        "store_name": "Tienda X",
        "password_dunit": "short",
    }
    resp = _register(test_client, payload)
    assert resp.status_code == 400


def test_registration_user_not_found(client):
    test_client, _ = client
    payload = {
        "email": "<EMAIL>",
        "name": "Usuario Desconocido",
        "store_name": "Tienda X",
        "password_dunit": "PassWord123",
    }
    resp = _register(test_client, payload)
    assert resp.status_code == 400


def test_registration_already_registered(client):
    test_client, _ = client
    payload = {
        "email": "<EMAIL>",
        "name": "Usuario Registrado",
        "store_name": "Tienda Y",
        "password_dunit": "PassWord123",
    }
    resp = _register(test_client, payload)
    assert resp.status_code == 400


def test_verify_registration_wrong_code(client):
    test_client, env = client

    # Registrar primero
    payload = {
        "email": "<EMAIL>",
        "name": "Nuevo Usuario",
        "store_name": "Tienda X",
        "password_dunit": "PassWord123",
    }
    _register(test_client, payload)
    wrong_code = "999999"
    verify_resp = test_client.post(
        VERIFY_ENDPOINT,
        json={"email": "<EMAIL>", "verification_code": wrong_code},
    )
    assert verify_resp.status_code == 400


def test_verify_registration_code_expired(client):
    test_client, env = client

    # Registrar
    payload = {
        "email": "<EMAIL>",
        "name": "Usuario Expira",
        "store_name": "Tienda Z",
        "password_dunit": "PassWord123",
    }
    _register(test_client, payload)

    # Manipular fecha de expiración en la DB
    db_main = env["db_main"]
    pending = db_main["store_users"]._collection.find_one({"email": "<EMAIL>"})
    assert pending is not None

    expiry_past = datetime.now(timezone.utc) - timedelta(minutes=1)
    db_main["store_users"]._collection.update_one(
        {"email": "<EMAIL>"},
        {"$set": {"dunit_registration_pending.verification_code_expiry": expiry_past}},
    )

    code = env["sent_codes"].get("<EMAIL>") or "000000"

    verify_resp = test_client.post(
        VERIFY_ENDPOINT,
        json={"email": "<EMAIL>", "verification_code": code},
    )
    assert verify_resp.status_code == 400 