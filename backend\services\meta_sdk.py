"""
Enhanced Meta API Service using Official Facebook Business SDK
Replaces direct API calls with proper SDK implementation
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
import json

# Facebook Business SDK
try:
    from facebook_business.api import FacebookAdsApi
    from facebook_business.adobjects.adaccount import AdAccount
    from facebook_business.adobjects.campaign import Campaign
    from facebook_business.adobjects.page import Page
    from facebook_business.adobjects.user import User
    from facebook_business.adobjects.iguser import IGUser
    from facebook_business.exceptions import FacebookRequestError
    FACEBOOK_SDK_AVAILABLE = True
except ImportError:
    FACEBOOK_SDK_AVAILABLE = False
    logging.warning("Facebook Business SDK not available. Install with: pip install facebook-business")

from config.database import db_analysis
from config.settings import get_settings

logger = logging.getLogger(__name__)

class MetaSDKService:
    """Enhanced Meta API Service using official Facebook Business SDK"""
    
    def __init__(self):
        self.settings = get_settings()
        self.app_id = self.settings.META_APP_ID
        self.app_secret = self.settings.META_APP_SECRET
        self.api_version = "v22.0"
        self.api = None
        self.fallback_mode = False
        
    def initialize_api(self, access_token: str) -> bool:
        """Initialize Facebook Ads API with access token"""
        if not FACEBOOK_SDK_AVAILABLE:
            logger.warning("Facebook SDK not available, falling back to direct API calls")
            self.fallback_mode = True
            return False
            
        try:
            FacebookAdsApi.init(
                app_id=self.app_id,
                app_secret=self.app_secret,
                access_token=access_token,
                api_version=self.api_version
            )
            self.api = FacebookAdsApi.get_default_api()
            logger.info("Facebook Business SDK initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Facebook Business SDK: {e}")
            self.fallback_mode = True
            return False
    
    async def get_campaigns_sdk(
        self, 
        ad_account_id: str, 
        access_token: str,
        since: Optional[str] = None,
        until: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get campaign data using Facebook Business SDK
        """
        if not self.initialize_api(access_token):
            logger.warning("SDK initialization failed, falling back to direct API")
            return await self._get_campaigns_fallback(ad_account_id, access_token, since, until)
        
        try:
            # Get ad account
            account = AdAccount(f"act_{ad_account_id}")
            
            # Define fields to fetch
            campaign_fields = [
                Campaign.Field.id,
                Campaign.Field.name,
                Campaign.Field.status,
                Campaign.Field.objective,
                Campaign.Field.start_time,
                Campaign.Field.stop_time,
                Campaign.Field.daily_budget,
                Campaign.Field.lifetime_budget,
                Campaign.Field.budget_remaining,
                Campaign.Field.buying_type,
                Campaign.Field.effective_status,
                Campaign.Field.configured_status
            ]
            
            # Define insight fields
            insight_fields = [
                'spend',
                'impressions',
                'reach',
                'clicks',
                'ctr',
                'cpc',
                'actions',
                'conversions',
                'cost_per_conversion'
            ]
            
            # Set up date range
            params = {}
            if since and until:
                params['time_range'] = {
                    'since': since,
                    'until': until
                }
            
            # Get campaigns
            campaigns = account.get_campaigns(fields=campaign_fields)
            
            processed_campaigns = []
            total_spend = 0.0
            total_impressions = 0
            total_clicks = 0
            total_conversions = 0
            
            for campaign in campaigns:
                try:
                    # Get insights for this campaign
                    insights = campaign.get_insights(
                        fields=insight_fields,
                        params=params
                    )
                    
                    # Process campaign data
                    campaign_data = {
                        "id": campaign.get(Campaign.Field.id, ""),
                        "name": campaign.get(Campaign.Field.name, "Unknown Campaign"),
                        "status": campaign.get(Campaign.Field.status, "UNKNOWN"),
                        "objective": campaign.get(Campaign.Field.objective, "UNKNOWN"),
                        "start_time": campaign.get(Campaign.Field.start_time),
                        "end_time": campaign.get(Campaign.Field.stop_time),
                        "daily_budget": campaign.get(Campaign.Field.daily_budget),
                        "lifetime_budget": campaign.get(Campaign.Field.lifetime_budget),
                        "platform": "facebook"  # Will be updated based on targeting
                    }
                    
                    # Process insights
                    if insights:
                        insight = insights[0] if len(insights) > 0 else {}
                        
                        spend = float(insight.get('spend', 0))
                        impressions = int(insight.get('impressions', 0))
                        clicks = int(insight.get('clicks', 0))
                        conversions = int(insight.get('conversions', 0))
                        ctr = float(insight.get('ctr', 0))
                        cpc = float(insight.get('cpc', 0))
                        
                        campaign_data.update({
                            "spend": round(spend, 2),
                            "impressions": impressions,
                            "clicks": clicks,
                            "conversions": conversions,
                            "ctr": round(ctr, 2),
                            "cpc": round(cpc, 2),
                            "cost_per_conversion": round(spend / conversions if conversions > 0 else 0, 2),
                            "roas": 4.0  # Default estimate
                        })
                        
                        # Add to totals
                        total_spend += spend
                        total_impressions += impressions
                        total_clicks += clicks
                        total_conversions += conversions
                        
                    else:
                        # No insights data
                        campaign_data.update({
                            "spend": 0,
                            "impressions": 0,
                            "clicks": 0,
                            "conversions": 0,
                            "ctr": 0,
                            "cpc": 0,
                            "cost_per_conversion": 0,
                            "roas": 0
                        })
                    
                    processed_campaigns.append(campaign_data)
                    
                except Exception as insight_error:
                    logger.warning(f"Failed to get insights for campaign {campaign.get(Campaign.Field.id)}: {insight_error}")
                    continue
            
            # Calculate overview metrics
            overview_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            overview_cpc = (total_spend / total_clicks) if total_clicks > 0 else 0
            overview_cost_per_conversion = (total_spend / total_conversions) if total_conversions > 0 else 0
            
            response = {
                "overview": {
                    "total_spend": round(total_spend, 2),
                    "total_impressions": total_impressions,
                    "total_clicks": total_clicks,
                    "total_conversions": total_conversions,
                    "ctr": round(overview_ctr, 2),
                    "cpc": round(overview_cpc, 2),
                    "cost_per_conversion": round(overview_cost_per_conversion, 2),
                    "roas": 4.0  # Default estimate
                },
                "campaigns": processed_campaigns,
                "daily_metrics": [],
                "source": "facebook_business_sdk"
            }
            
            logger.info(f"Successfully fetched {len(processed_campaigns)} campaigns using Facebook Business SDK")
            return response
            
        except FacebookRequestError as e:
            logger.error(f"Facebook SDK request error: {e}")
            # Fall back to direct API
            return await self._get_campaigns_fallback(ad_account_id, access_token, since, until)
        except Exception as e:
            logger.error(f"Unexpected error in SDK campaign fetch: {e}")
            return await self._get_campaigns_fallback(ad_account_id, access_token, since, until)
    
    async def _get_campaigns_fallback(
        self, 
        ad_account_id: str, 
        access_token: str,
        since: Optional[str] = None,
        until: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Fallback to direct API calls if SDK fails
        """
        logger.info("Using fallback direct API for campaign data")
        
        # Import the existing service for fallback
        from services.meta import fetch_and_cache_instagram_ad_metrics
        
        # Create a mock page object for the existing function
        page_doc = {
            "access_token": access_token,
            "ad_account_id": ad_account_id,
            "platform": "facebook"
        }
        
        try:
            return await fetch_and_cache_instagram_ad_metrics(
                page_id="fallback",
                store_id="fallback", 
                page=page_doc,
                is_instagram=False,
                time_range="lifetime",
                since=since,
                until=until,
                force_refresh=True,
                max_data_age=0,
                include_organic=False
            )
        except Exception as e:
            logger.error(f"Fallback API also failed: {e}")
            return {
                "overview": {
                    "total_spend": 0,
                    "total_impressions": 0,
                    "total_clicks": 0,
                    "total_conversions": 0,
                    "ctr": 0,
                    "cpc": 0,
                    "cost_per_conversion": 0,
                    "roas": 0
                },
                "campaigns": [],
                "daily_metrics": [],
                "source": "fallback_failed"
            }
    
    async def get_instagram_campaigns_sdk(
        self,
        page_id: str,
        access_token: str,
        ad_account_id: str,
        since: Optional[str] = None,
        until: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get Instagram campaign data using SDK
        """
        if not self.initialize_api(access_token):
            logger.warning("SDK initialization failed for Instagram campaigns")
            return await self._get_campaigns_fallback(ad_account_id, access_token, since, until)
        
        try:
            # Get campaigns from ad account (same as Facebook, but filter for Instagram)
            campaign_data = await self.get_campaigns_sdk(ad_account_id, access_token, since, until)
            
            # Update platform info for Instagram
            for campaign in campaign_data.get("campaigns", []):
                campaign["platform"] = "instagram"
            
            campaign_data["source"] = "instagram_business_sdk"
            return campaign_data
            
        except Exception as e:
            logger.error(f"Error fetching Instagram campaigns via SDK: {e}")
            return await self._get_campaigns_fallback(ad_account_id, access_token, since, until)

# Global instance
meta_sdk_service = MetaSDKService()