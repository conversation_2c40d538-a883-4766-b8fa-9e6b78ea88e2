import React from 'react';
import Navigation from './Navigation';
import { MetaPermissionsProvider } from '../../contexts/MetaPermissionsContext';

/**
 * A wrapper component for Navigation that ensures it always has access to MetaPermissionsContext
 * This helps prevent the "useMetaPermissions must be used within a MetaPermissionsProvider" error
 * 
 * Since we can't reliably detect if we're within a context, we'll always provide our own
 * and let React handle deduplication of context providers.
 */
const NavigationWrapper: React.FC = () => {
  return (
    <MetaPermissionsProvider>
      <Navigation />
    </MetaPermissionsProvider>
  );
};

export default NavigationWrapper; 