import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add comprehensive security headers to all responses.
    Provides protection against common web vulnerabilities.
    """
    
    def __init__(self, app, config: Optional[Dict[str, Any]] = None):
        super().__init__(app)
        self.config = config or self._get_default_config()
        self.enabled = self.config.get("enabled", True)
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Default security headers configuration"""
        return {
            "enabled": True,
            "hsts": {
                "enabled": True,
                "max_age": 31536000,  # 1 year
                "include_subdomains": True,
                "preload": False
            },
            "csp": {
                "enabled": True,
                "policy": (
                    "default-src 'self'; "
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://connect.facebook.net https://www.facebook.com; "
                    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
                    "font-src 'self' https://fonts.gstatic.com; "
                    "img-src 'self' data: blob: https: https://*.facebook.com https://*.fbcdn.net https://*.fbsbx.com; "
                    "connect-src 'self' https://api.openai.com https://graph.facebook.com https://www.facebook.com https://*.facebook.com; "
                    "frame-src 'self' https://www.facebook.com https://web.facebook.com https://connect.facebook.net; "
                    "frame-ancestors 'self'; "
                    "base-uri 'self'; "
                    "form-action 'self'; "
                    "object-src 'none'; "
                    "upgrade-insecure-requests;"
                )
            },
            "csrf": {
                "enabled": True,
                "cookie_name": "csrf_token",
                "header_name": "X-CSRF-Token"
            },
            "cookies": {
                "secure": True,
                "http_only": True,
                "same_site": "strict"
            },
            "additional_headers": {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Referrer-Policy": "strict-origin-when-cross-origin",
                "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
                "X-Download-Options": "noopen",
                "X-Permitted-Cross-Domain-Policies": "none"
            }
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request and add security headers to response"""
        if not self.enabled:
            return await call_next(request)
        
        # Process the request
        response = await call_next(request)
        
        # Add security headers
        self._add_security_headers(response, request)
        
        return response
    
    def _add_security_headers(self, response: Response, request: Request):
        """Add all configured security headers to the response"""
        try:
            # HSTS (HTTP Strict Transport Security)
            if self.config.get("hsts", {}).get("enabled", True):
                hsts_config = self.config["hsts"]
                hsts_value = f"max-age={hsts_config.get('max_age', 31536000)}"
                if hsts_config.get("include_subdomains", True):
                    hsts_value += "; includeSubDomains"
                if hsts_config.get("preload", False):
                    hsts_value += "; preload"
                response.headers["Strict-Transport-Security"] = hsts_value
            
            # Content Security Policy
            if self.config.get("csp", {}).get("enabled", True):
                csp_policy = self.config["csp"].get("policy", "default-src 'self'")
                response.headers["Content-Security-Policy"] = csp_policy
            
            # Additional security headers
            additional_headers = self.config.get("additional_headers", {})
            for header_name, header_value in additional_headers.items():
                response.headers[header_name] = header_value
            
            # CSRF protection headers (for non-GET requests)
            if self.config.get("csrf", {}).get("enabled", True) and request.method != "GET":
                self._add_csrf_headers(response, request)
            
            # Cache control for sensitive endpoints
            if self._is_sensitive_endpoint(request.url.path):
                response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, private"
                response.headers["Pragma"] = "no-cache"
                response.headers["Expires"] = "0"
            
            # Add security metadata
            response.headers["X-Security-Gateway"] = "active"
            response.headers["X-Security-Timestamp"] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error adding security headers: {e}")
            # Don't fail the request if header addition fails
    
    def _add_csrf_headers(self, response: Response, request: Request):
        """Add CSRF protection headers"""
        try:
            csrf_config = self.config.get("csrf", {})
            
            # Define CSRF-exempt endpoints (authentication endpoints that use other security mechanisms)
            csrf_exempt_endpoints = [
                "/api/auth/token",
                "/api/auth/login", 
                "/api/auth/2fa/verify-login",
                "/api/auth/forgot-password",
                "/api/auth/reset-password",
                "/api/auth/register",
                "/api/auth/verify-email"
            ]
            
            # Check if current endpoint is exempt from CSRF protection
            is_exempt = any(request.url.path.startswith(exempt_path) for exempt_path in csrf_exempt_endpoints)
            
            # Check for CSRF token in request
            csrf_token = request.headers.get(csrf_config.get("header_name", "X-CSRF-Token"))
            
            if not csrf_token and request.method in ["POST", "PUT", "DELETE", "PATCH"] and not is_exempt:
                # Log potential CSRF attack (only for non-exempt endpoints)
                logger.warning(f"Missing CSRF token for {request.method} request to {request.url.path}")
                response.headers["X-CSRF-Warning"] = "CSRF token missing"
            elif is_exempt:
                # Add header indicating endpoint is exempt
                response.headers["X-CSRF-Exempt"] = "true"
            
        except Exception as e:
            logger.error(f"Error processing CSRF headers: {e}")
    
    def _is_sensitive_endpoint(self, path: str) -> bool:
        """Check if endpoint contains sensitive data that shouldn't be cached"""
        sensitive_patterns = [
            "/auth",
            "/admin",
            "/security",
            "/api/user",
            "/api/store"
        ]
        return any(pattern in path for pattern in sensitive_patterns)
    
    def _set_secure_cookie_attributes(self, response: Response):
        """Set secure attributes for cookies"""
        try:
            cookie_config = self.config.get("cookies", {})
            
            # This would be used when setting cookies in the application
            # The actual cookie setting happens in route handlers
            if hasattr(response, 'set_cookie'):
                # Example of how to set secure cookie attributes
                # This is more for reference - actual implementation depends on cookie usage
                secure = cookie_config.get("secure", True)
                http_only = cookie_config.get("http_only", True)
                same_site = cookie_config.get("same_site", "strict")
                
                # Add to response headers for cookie policy
                response.headers["X-Cookie-Policy"] = f"Secure={secure};HttpOnly={http_only};SameSite={same_site}"
                
        except Exception as e:
            logger.error(f"Error setting cookie attributes: {e}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update security headers configuration"""
        try:
            self.config.update(new_config)
            logger.info("Security headers configuration updated")
        except Exception as e:
            logger.error(f"Error updating security headers config: {e}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get current security headers configuration"""
        return self.config.copy()
    
    def disable(self):
        """Disable security headers middleware"""
        self.enabled = False
        logger.info("Security headers middleware disabled")
    
    def enable(self):
        """Enable security headers middleware"""
        self.enabled = True
        logger.info("Security headers middleware enabled")