"""
Instagram Business Account Integration Service
Handles proper detection, storage, and data fetching for Instagram Business Accounts
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import asyncio
import aiohttp
from motor.motor_asyncio import AsyncIOMotorCollection

from config.database import db_analysis
from services.instagram_utils import (
    InstagramAPIError,
    validate_instagram_permissions,
    validate_instagram_token,
    INSTAGRAM_PERMISSIONS,
    INSTAGRAM_ERROR_CODES
)
from services.instagram_transformer import InstagramDataTransformer
from services.instagram_cache import InstagramCacheService
from utils.serialization import serialize_mongo_list, serialize_mongo_doc

logger = logging.getLogger(__name__)

class InstagramBusinessService:
    """Service for handling Instagram Business Account integration"""
    
    @staticmethod
    async def detect_and_store_instagram_accounts(
        store_id: str, 
        facebook_pages: List[Dict[str, Any]],
        user_access_token: str
    ) -> List[Dict[str, Any]]:
        """
        Detect Instagram Business Accounts connected to Facebook Pages and store them properly
        
        Args:
            store_id: Store ID
            facebook_pages: List of Facebook Page data from Meta API
            user_access_token: User's Meta access token
            
        Returns:
            List of Instagram Business Account data
        """
        try:
            instagram_accounts = []
            
            logger.info(f"Detecting Instagram Business Accounts for store {store_id}")
            logger.debug(f"Checking {len(facebook_pages)} Facebook pages for Instagram connections")
            
            # Check each Facebook page for connected Instagram Business Account
            for fb_page in facebook_pages:
                page_id = fb_page.get("id")
                page_access_token = fb_page.get("access_token")
                
                if not page_id or not page_access_token:
                    logger.warning(f"Skipping FB page {page_id}: missing ID or access token")
                    continue
                
                try:
                    # Get Instagram Business Account connected to this page
                    ig_account = await InstagramBusinessService._get_connected_instagram_account(
                        page_id, page_access_token
                    )
                    
                    if ig_account:
                        # Enhance with additional data
                        enhanced_ig_account = await InstagramBusinessService._enhance_instagram_account_data(
                            ig_account, page_access_token
                        )
                        
                        # Store in database
                        await InstagramBusinessService._store_instagram_account(
                            store_id, enhanced_ig_account, fb_page
                        )
                        
                        instagram_accounts.append(enhanced_ig_account)
                        logger.info(f"Successfully detected and stored Instagram account {ig_account['id']} for store {store_id}")
                    else:
                        logger.debug(f"No Instagram Business Account connected to FB page {page_id}")
                        
                except Exception as e:
                    logger.error(f"Error processing FB page {page_id} for Instagram connection: {str(e)}")
                    continue
            
            logger.info(f"Successfully detected {len(instagram_accounts)} Instagram Business Accounts for store {store_id}")
            return instagram_accounts
            
        except Exception as e:
            logger.error(f"Error in detect_and_store_instagram_accounts: {str(e)}")
            return []
    
    @staticmethod
    async def _get_connected_instagram_account(
        page_id: str, 
        page_access_token: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get Instagram Business Account connected to a Facebook Page
        
        Args:
            page_id: Facebook Page ID
            page_access_token: Facebook Page access token
            
        Returns:
            Instagram account data or None if not connected
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"https://graph.facebook.com/v22.0/{page_id}"
                params = {
                    "fields": "instagram_business_account{id,username,name,profile_picture_url,followers_count,media_count}",
                    "access_token": page_access_token
                }
                
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        error_data = await response.json()
                        error = error_data.get("error", {})
                        
                        # If no Instagram account is connected, this is normal
                        if error.get("code") == 100:
                            logger.debug(f"No Instagram Business Account connected to page {page_id}")
                            return None
                        
                        logger.warning(f"Error checking Instagram connection for page {page_id}: {error}")
                        return None
                    
                    data = await response.json()
                    ig_account = data.get("instagram_business_account")
                    
                    if ig_account:
                        # Add the page access token to the Instagram account
                        ig_account["access_token"] = page_access_token
                        ig_account["parent_page_id"] = page_id
                        logger.debug(f"Found Instagram account {ig_account['id']} connected to page {page_id}")
                        return ig_account
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting connected Instagram account for page {page_id}: {str(e)}")
            return None
    
    @staticmethod
    async def _enhance_instagram_account_data(
        ig_account: Dict[str, Any], 
        access_token: str
    ) -> Dict[str, Any]:
        """
        Enhance Instagram account data with additional information
        
        Args:
            ig_account: Basic Instagram account data
            access_token: Access token for API calls
            
        Returns:
            Enhanced Instagram account data
        """
        try:
            account_id = ig_account["id"]
            
            # Get additional profile information
            async with aiohttp.ClientSession() as session:
                url = f"https://graph.facebook.com/v22.0/{account_id}"
                params = {
                    "fields": "id,username,name,biography,followers_count,follows_count,media_count,profile_picture_url,website",
                    "access_token": access_token
                }
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        enhanced_data = await response.json()
                        # Merge enhanced data with original data
                        ig_account.update(enhanced_data)
                        logger.debug(f"Enhanced Instagram account {account_id} with additional profile data")
                    else:
                        error_data = await response.json()
                        logger.warning(f"Could not enhance Instagram account {account_id}: {error_data}")
            
            # Add metadata
            ig_account.update({
                "platform": "instagram",
                "account_type": "business",
                "last_updated": datetime.utcnow().isoformat(),
                "permissions_checked": datetime.utcnow().isoformat()
            })
            
            return ig_account
            
        except Exception as e:
            logger.error(f"Error enhancing Instagram account data: {str(e)}")
            return ig_account
    
    @staticmethod
    async def _store_instagram_account(
        store_id: str, 
        ig_account: Dict[str, Any], 
        parent_fb_page: Dict[str, Any]
    ) -> None:
        """
        Store Instagram Business Account in the database
        
        Args:
            store_id: Store ID
            ig_account: Instagram account data
            parent_fb_page: Parent Facebook page data
        """
        try:
            account_id = ig_account["id"]
            
            # Prepare document for storage
            document = {
                "id": account_id,
                "store_id": store_id,
                "platform": "instagram",
                "name": ig_account.get("username", ig_account.get("name", f"Instagram Account {account_id}")),
                "access_token": ig_account["access_token"],
                "category": "INSTAGRAM_BUSINESS",
                "category_list": [{"id": "business", "name": "Business"}],
                "tasks": ["ANALYZE", "ADVERTISE"],
                "followers_count": ig_account.get("followers_count", 0),
                "follows_count": ig_account.get("follows_count", 0),
                "media_count": ig_account.get("media_count", 0),
                "profile_picture_url": ig_account.get("profile_picture_url"),
                "username": ig_account.get("username"),
                "biography": ig_account.get("biography", ""),
                "website": ig_account.get("website"),
                "parent_page_id": ig_account.get("parent_page_id"),
                "parent_page_name": parent_fb_page.get("name"),
                "last_updated": datetime.utcnow(),
                "created_at": datetime.utcnow()
            }
            
            # Clear any token expiration flags when storing new token
            if "access_token" in document and document["access_token"]:
                document["token_expired"] = False
                document["token_error"] = None
            
            # Upsert the document
            await db_analysis["meta_pages"].update_one(
                {"id": account_id, "store_id": store_id, "platform": "instagram"},
                {"$set": document},
                upsert=True
            )
            
            logger.info(f"Stored Instagram Business Account {account_id} for store {store_id}")
            
        except Exception as e:
            logger.error(f"Error storing Instagram account: {str(e)}")
    
    @staticmethod
    async def get_instagram_accounts_for_store(store_id: str) -> List[Dict[str, Any]]:
        """
        Get all Instagram Business Accounts for a store
        
        Args:
            store_id: Store ID
            
        Returns:
            List of Instagram account data
        """
        try:
            cursor = db_analysis["meta_pages"].find({
                "store_id": store_id,
                "platform": "instagram"
            })
            
            accounts = await cursor.to_list(length=None)
            logger.debug(f"Found {len(accounts)} Instagram accounts for store {store_id}")
            
            # Serialize MongoDB ObjectIds to strings for JSON response
            serialized_accounts = serialize_mongo_list(accounts)
            return serialized_accounts
            
        except Exception as e:
            logger.error(f"Error getting Instagram accounts for store {store_id}: {str(e)}")
            return []
    
    @staticmethod
    async def validate_instagram_account_access(
        account_id: str, 
        store_id: str
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Validate that we can access an Instagram Business Account
        
        Args:
            account_id: Instagram account ID
            store_id: Store ID
            
        Returns:
            Tuple of (is_valid, error_message, account_data)
        """
        try:
            # Get account from database
            account = await db_analysis["meta_pages"].find_one({
                "id": account_id,
                "store_id": store_id,
                "platform": "instagram"
            })
            
            if not account:
                return False, "Instagram Business Account not found for this store", None
            
            access_token = account.get("access_token")
            if not access_token:
                return False, "No access token available for Instagram account", None
            
            # Validate token
            token_validation = await validate_instagram_token(access_token)
            if not token_validation.get("valid"):
                error_msg = token_validation.get("error", "Token validation failed")
                return False, f"Instagram access token is invalid: {error_msg}", None
            
            # Check permissions (more graceful handling)
            try:
                required_permissions = INSTAGRAM_PERMISSIONS["insights"]
                permission_check = await validate_instagram_permissions(access_token, required_permissions)
                if not permission_check.get("valid"):
                    missing = permission_check.get("missing", [])
                    if missing:
                        return False, f"Missing Instagram permissions: {', '.join(missing)}. Please reconnect your Meta account with proper permissions.", None
                    else:
                        # Generic permission issue
                        return False, "Instagram Business Account permissions need to be refreshed. Please reconnect your Meta account.", None
            except Exception as perm_error:
                logger.warning(f"Permission check failed for Instagram account {account_id}: {str(perm_error)}")
                # Don't fail validation due to permission check issues, log and continue
                logger.info(f"Continuing validation despite permission check failure for account {account_id}")
                pass
            
            # Serialize the account data before returning
            serialized_account = serialize_mongo_doc(account)
            return True, None, serialized_account
            
        except Exception as e:
            logger.error(f"Error validating Instagram account access: {str(e)}")
            return False, f"Validation error: {str(e)}", None
    
    @staticmethod
    async def get_instagram_insights(
        account_id: str,
        store_id: str,
        metrics: Optional[List[str]] = None,
        time_range: str = "30d",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Get Instagram insights for a business account
        
        Args:
            account_id: Instagram account ID
            store_id: Store ID
            metrics: List of metrics to fetch
            time_range: Time range (7d, 30d, 90d)
            use_cache: Whether to use cache
            
        Returns:
            Instagram insights data
        """
        try:
            # Validate account access
            is_valid, error_msg, account = await InstagramBusinessService.validate_instagram_account_access(
                account_id, store_id
            )
            
            if not is_valid:
                raise InstagramAPIError(100, error_msg or "Account validation failed", "check_account")
            
            # Check cache first
            if use_cache:
                cache_key = InstagramCacheService.generate_cache_key(
                    account_id, "account_insights", {"metrics": metrics, "time_range": time_range}
                )
                cached_data = await InstagramCacheService.get_cached_data(cache_key, "account_insights")
                if cached_data:
                    logger.debug(f"Using cached Instagram insights for account {account_id}")
                    return cached_data
            
            # Set default metrics if not provided
            if not metrics:
                metrics = ["impressions", "reach", "profile_views", "website_clicks"]
            
            # Calculate time period
            end_date = datetime.now().date()
            if time_range == "7d":
                start_date = end_date - timedelta(days=7)
            elif time_range == "30d":
                start_date = end_date - timedelta(days=30)
            elif time_range == "90d":
                start_date = end_date - timedelta(days=90)
            else:
                start_date = end_date - timedelta(days=30)
            
            access_token = account["access_token"]
            
            # Fetch insights from Instagram API
            async with aiohttp.ClientSession() as session:
                url = f"https://graph.facebook.com/v22.0/{account_id}/insights"
                params = {
                    "metric": ",".join(metrics),
                    "period": "day",
                    "since": start_date.isoformat(),
                    "until": end_date.isoformat(),
                    "access_token": access_token
                }
                
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        error_data = await response.json()
                        error = error_data.get("error", {})
                        
                        # Handle specific Instagram errors
                        error_code = error.get("code", response.status)
                        error_message = error.get("message", "Unknown error")
                        
                        if error_code in INSTAGRAM_ERROR_CODES:
                            error_info = INSTAGRAM_ERROR_CODES[error_code]
                            raise InstagramAPIError(error_code, error_info["message"], error_info["action"])
                        else:
                            raise InstagramAPIError(error_code, error_message, "retry")
                    
                    insights_data = await response.json()
                    
                    # Transform the data to frontend format
                    transformed_insights = InstagramDataTransformer.transform_insights_to_frontend(
                        insights_data.get("data", [])
                    )
                    
                    # Add metadata
                    result = {
                        "insights": transformed_insights,
                        "account_id": account_id,
                        "store_id": store_id,
                        "time_range": time_range,
                        "metrics": metrics,
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "last_updated": datetime.utcnow().isoformat()
                    }
                    
                    # Cache the results
                    if use_cache:
                        await InstagramCacheService.set_cached_data(
                            cache_key, "account_insights", result, {"account_id": account_id, "store_id": store_id}
                        )
                    
                    return result
            
        except InstagramAPIError:
            raise
        except Exception as e:
            logger.error(f"Error getting Instagram insights: {str(e)}")
            raise InstagramAPIError(500, f"Failed to fetch Instagram insights: {str(e)}", "retry")
    
    @staticmethod
    async def refresh_instagram_account_tokens(store_id: str) -> Dict[str, Any]:
        """
        Refresh access tokens for all Instagram accounts for a store
        
        Args:
            store_id: Store ID
            
        Returns:
            Summary of refresh operation
        """
        try:
            accounts = await InstagramBusinessService.get_instagram_accounts_for_store(store_id)
            
            refreshed_count = 0
            failed_count = 0
            errors = []
            
            for account in accounts:
                try:
                    account_id = account["id"]
                    access_token = account.get("access_token")
                    
                    if not access_token:
                        failed_count += 1
                        errors.append(f"Account {account_id}: No access token")
                        continue
                    
                    # Validate token
                    token_validation = await validate_instagram_token(access_token)
                    if token_validation.get("valid"):
                        refreshed_count += 1
                    else:
                        failed_count += 1
                        errors.append(f"Account {account_id}: {token_validation.get('error', 'Token invalid')}")
                        
                        # Update account with invalid token status
                        await db_analysis["meta_pages"].update_one(
                            {"id": account_id, "store_id": store_id},
                            {"$set": {
                                "token_valid": False,
                                "token_error": token_validation.get("error"),
                                "last_token_check": datetime.utcnow()
                            }}
                        )
                    
                except Exception as e:
                    failed_count += 1
                    errors.append(f"Account {account.get('id', 'unknown')}: {str(e)}")
            
            return {
                "total_accounts": len(accounts),
                "refreshed": refreshed_count,
                "failed": failed_count,
                "errors": errors,
                "store_id": store_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error refreshing Instagram account tokens: {str(e)}")
            return {
                "total_accounts": 0,
                "refreshed": 0,
                "failed": 0,
                "errors": [f"Refresh operation failed: {str(e)}"],
                "store_id": store_id,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    @staticmethod
    async def cleanup_invalid_accounts(store_id: str) -> Dict[str, Any]:
        """
        Clean up Instagram accounts that are no longer valid
        
        Args:
            store_id: Store ID
            
        Returns:
            Summary of cleanup operation
        """
        try:
            # Find accounts with invalid tokens or old data
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            
            invalid_accounts = await db_analysis["meta_pages"].find({
                "store_id": store_id,
                "platform": "instagram",
                "$or": [
                    {"token_valid": False},
                    {"last_updated": {"$lt": cutoff_date}},
                    {"access_token": {"$exists": False}}
                ]
            }).to_list(length=None)
            
            removed_count = 0
            
            for account in invalid_accounts:
                try:
                    # Try to validate one more time before removing
                    access_token = account.get("access_token")
                    if access_token:
                        token_validation = await validate_instagram_token(access_token)
                        if token_validation.get("valid"):
                            # Token is actually valid, update record
                            await db_analysis["meta_pages"].update_one(
                                {"_id": account["_id"]},
                                {"$set": {
                                    "token_valid": True,
                                    "last_updated": datetime.utcnow()
                                }}
                            )
                            continue
                    
                    # Remove invalid account
                    await db_analysis["meta_pages"].delete_one({"_id": account["_id"]})
                    removed_count += 1
                    logger.info(f"Removed invalid Instagram account {account.get('id')} for store {store_id}")
                    
                except Exception as e:
                    logger.error(f"Error processing account {account.get('id')}: {str(e)}")
            
            return {
                "total_checked": len(invalid_accounts),
                "removed": removed_count,
                "store_id": store_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up invalid Instagram accounts: {str(e)}")
            return {
                "total_checked": 0,
                "removed": 0,
                "error": str(e),
                "store_id": store_id,
                "timestamp": datetime.utcnow().isoformat()
            }