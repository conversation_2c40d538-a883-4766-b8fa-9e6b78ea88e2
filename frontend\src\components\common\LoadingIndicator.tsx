import React from 'react';
import { Box, CircularProgress, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface LoadingIndicatorProps {
  mode: string | null;
  elapsedTime: number;
  stepText: string;
}

// Helper function to format mode names
const formatModeName = (mode: string | null): string => {
  if (!mode) return 'Default';
  if (mode.toLowerCase() === 'deepsearch') return 'DeepSearch';
  if (mode.toLowerCase() === 'deepersearch') return 'DeeperSearch';
  return mode.charAt(0).toUpperCase() + mode.slice(1).toLowerCase();
};

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ mode, elapsedTime, stepText }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const displayMode = formatModeName(mode);

  return (
    <Box 
      display="flex" 
      flexDirection="column" 
      alignItems="center" 
      justifyContent="center" 
      p={2}
      sx={{ color: theme.palette.text.primary }} // Use theme's text color
    >
      <CircularProgress size={30} sx={{ color: '#00A3FF', mb: 2 }} />
      {mode && (
        <Typography variant="body2" sx={{ mb: 0.5 }}>
          {t('chat.loadingIndicator.mode')} <strong>{displayMode}</strong>
        </Typography>
      )}
      <Typography variant="body2" sx={{ mb: 0.5 }}>
        {t('chat.loadingIndicator.time')} {elapsedTime}s
      </Typography>
      <Typography variant="body2" sx={{ mb: 0.5, fontStyle: 'italic' }}>
        {stepText || t('chat.loadingIndicator.defaultProcessing')} 
      </Typography>
    </Box>
  );
};

export default LoadingIndicator; 
