import React from 'react';
import { Card, CardContent, Typography, Grid, Box } from '@mui/material';
import { formatDistanceToNowStrict } from 'date-fns';
import { es, enUS } from 'date-fns/locale'; // Import Spanish and English locales
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';

interface KeyDates {
  last_order_date?: string;
  last_visit_date?: string;
  last_customer_registration_date?: string;
  last_product_added_date?: string;
}

interface KeyDatesCardProps {
  title: string;
  dates?: KeyDates;
}

const KeyDatesCard: React.FC<KeyDatesCardProps> = ({ title, dates }) => {
  const { t, i18n } = useTranslation();

  const formatDate = (dateString?: string): string => {
    if (!dateString) return t('common.notAvailable', 'N/A');
    try {
      const date = new Date(dateString);
      // Determine the locale for date-fns based on i18n.language
      const currentLanguage = i18n.language.toLowerCase();
      let dateFnsLocale = enUS; // Default to English
      if (currentLanguage.startsWith('es')) {
        dateFnsLocale = es;
      }
      // Format as relative time (e.g., "hace 5 días" or "5 days ago")
      return formatDistanceToNowStrict(date, { addSuffix: true, locale: dateFnsLocale });
    } catch (error) {
      logger.error("Error formatting date:", dateString, error);
      return t('common.invalidDate', 'Invalid Date');
    }
  };

  const dateItems = [
    { label: t('dashboard.keyDates.lastOrder', 'Last Order'), value: formatDate(dates?.last_order_date) },
    { label: t('dashboard.keyDates.lastVisit', 'Last Visit'), value: formatDate(dates?.last_visit_date) },
    { label: t('dashboard.keyDates.lastRegistration', 'Last Customer Reg.'), value: formatDate(dates?.last_customer_registration_date) },
    { label: t('dashboard.keyDates.lastProductAdded', 'Last Product Added'), value: formatDate(dates?.last_product_added_date) },
  ];

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom component="div">
          {title}
        </Typography>
        {dates ? (
          <Grid container spacing={1}>
            {dateItems.map((item, index) => (
              <Grid item xs={12} key={index}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {item.label}:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    {item.value}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Typography variant="body2" color="text.secondary">
            {t('common.noDataAvailable', 'No key dates available.')}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default KeyDatesCard; 