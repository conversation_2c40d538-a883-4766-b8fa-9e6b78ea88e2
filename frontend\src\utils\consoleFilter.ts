// Console filter utility to suppress false positive Recharts logUtils warnings
// This addresses the known issue with ResponsiveContainer dimension warnings

interface OriginalConsoleMethods {
  warn: typeof console.warn;
  error: typeof console.error;
  log: typeof console.log;
}

class ConsoleFilter {
  private originalMethods: OriginalConsoleMethods;
  private isFilterActive: boolean = false;

  // Enhanced patterns to match Recharts logUtils warnings more accurately
  private readonly rechartsWarningPatterns = [
    // Match the main warning message
    /The width\(\d*\) and height\(\d*\) of chart should be.*greater than 0/i,
    // Match full multi-line warning patterns
    /The width\(\d*\) and height\(\d*\) of chart should be[\s\S]*greater than 0[\s\S]*please check the style[\s\S]*width\(\d*%?\) and height\(\d*%?\)/i,
    // Match individual line patterns
    /please check the style of container, or the props/i,
    /width\(\d*%?\) and height\(\d*%?\)/i,
    /add a minWidth\(\d*\) or minHeight\(.*?\) or use/i,
    /aspect\(.*?\) to control the/i,
    /height and width\./i,
    // Match LogUtils reference
    /logUtils\.js:\d+/i,
    /logUtils\.js/i,
    // Broader patterns for safety
    /chart should be.*greater than 0/i,
    /ResponsiveContainer.*width.*height/i,
    // Match any message containing these specific Recharts terms together
    /width.*height.*chart.*greater.*0/i,
    /minWidth.*minHeight.*aspect.*control/i,
    // Very specific patterns for the exact error format
    /^The width\(\d+\) and height\(\d+\) of chart should be$/i,
    /^greater than 0,$/i,
    /^please check the style of container, or the props$/i,
    /^width\(\d+%?\) and height\(\d+%?\),$/i,
    /^or add a minWidth\(\d+\) or minHeight\(.*\) or use$/i,
    /^aspect\(.*\) to control the$/i,
    /^height and width\.$/i,
    // Pattern to match any combination of these words in any order
    /(?=.*width)(?=.*height)(?=.*chart)(?=.*greater)/i,
    /(?=.*logUtils)/i,
    /(?=.*minWidth)(?=.*minHeight)/i,
    /(?=.*aspect)(?=.*control)/i
  ];

  constructor() {
    // Store original console methods
    this.originalMethods = {
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      log: console.log.bind(console)
    };
  }

  /**
   * Check if a message matches Recharts logUtils warning patterns
   */
  private isRechartsLogUtilsWarning(message: string): boolean {
    // Convert message to string and normalize whitespace
    const normalizedMessage = String(message).replace(/\s+/g, ' ').trim().toLowerCase();
    
    // Quick keyword check first for performance
    const hasRechartsKeywords = (
      normalizedMessage.includes('width') && 
      normalizedMessage.includes('height') && 
      (normalizedMessage.includes('chart') || normalizedMessage.includes('logutils'))
    ) || 
    normalizedMessage.includes('logutils') ||
    (
      normalizedMessage.includes('minwidth') && 
      normalizedMessage.includes('minheight')
    );

    if (!hasRechartsKeywords) {
      return false;
    }

    const isMatch = this.rechartsWarningPatterns.some(pattern => {
      const matches = pattern.test(normalizedMessage);
      if (matches) {
        // Log successful match for debugging (using original console)
        this.originalMethods.log(`🚫 Filtered Recharts warning: ${normalizedMessage.substring(0, 100)}...`);
      }
      return matches;
    });
    
    return isMatch;
  }

  /**
   * Enhanced message processing to handle various argument formats
   */
  private processConsoleArgs(args: unknown[]): string {
    return args.map(arg => {
      if (typeof arg === 'string') return arg;
      if (typeof arg === 'object' && arg !== null) {
        try {
          return JSON.stringify(arg);
        } catch {
          return String(arg);
        }
      }
      return String(arg);
    }).join(' ');
  }

  /**
   * Custom console.warn that filters Recharts warnings
   */
  private filteredWarn = (...args: unknown[]): void => {
    const message = this.processConsoleArgs(args);
    if (!this.isRechartsLogUtilsWarning(message)) {
      this.originalMethods.warn(...args);
    }
  };

  /**
   * Custom console.error that filters Recharts warnings
   */
  private filteredError = (...args: unknown[]): void => {
    const message = this.processConsoleArgs(args);
    if (!this.isRechartsLogUtilsWarning(message)) {
      this.originalMethods.error(...args);
    }
  };

  /**
   * Custom console.log that filters Recharts warnings
   */
  private filteredLog = (...args: unknown[]): void => {
    const message = this.processConsoleArgs(args);
    if (!this.isRechartsLogUtilsWarning(message)) {
      this.originalMethods.log(...args);
    }
  };

  /**
   * Activate the console filter
   */
  activate(): void {
    if (this.isFilterActive) return;

    console.warn = this.filteredWarn;
    console.error = this.filteredError;
    console.log = this.filteredLog;
    this.isFilterActive = true;

    // Log activation (using original method to avoid filtering)
    this.originalMethods.log('📊 Console filter activated: Recharts logUtils warnings will be suppressed');
  }

  /**
   * Deactivate the console filter and restore original methods
   */
  deactivate(): void {
    if (!this.isFilterActive) return;

    console.warn = this.originalMethods.warn;
    console.error = this.originalMethods.error;
    console.log = this.originalMethods.log;
    this.isFilterActive = false;

    console.log('📊 Console filter deactivated: All console output restored');
  }

  /**
   * Get the original console methods for debugging
   */
  getOriginalMethods(): OriginalConsoleMethods {
    return this.originalMethods;
  }

  /**
   * Check if filter is currently active
   */
  isActive(): boolean {
    return this.isFilterActive;
  }

  /**
   * Test method to check if a message would be filtered
   */
  testMessage(message: string): boolean {
    return this.isRechartsLogUtilsWarning(message);
  }
}

// Create singleton instance
const consoleFilter = new ConsoleFilter();

export { consoleFilter, ConsoleFilter };
export default consoleFilter; 