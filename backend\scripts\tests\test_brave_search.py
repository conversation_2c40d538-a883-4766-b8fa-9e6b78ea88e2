#!/usr/bin/env python3
"""
Test script to verify Brave Search API is working and returning real results
"""

import os
import requests
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

# Get Brave Search API key
brave_api_key = os.getenv('BRAVE_SEARCH_API_KEY')
if not brave_api_key:
    print("ERROR: BRAVE_SEARCH_API_KEY not found in .env file")
    exit(1)

print(f"Brave Search API Key found: {brave_api_key[:10]}...")

# Test search query
query = "optical stores Uruguay Montevideo"
print(f"\nTesting search query: '{query}'")

# Make API request
headers = {
    "Accept": "application/json",
    "X-Subscription-Token": brave_api_key
}

params = {
    "q": query,
    "count": 10,
    "freshness": "py"  # Past year
}

url = "https://api.search.brave.com/res/v1/web/search"

try:
    response = requests.get(url, headers=headers, params=params)
    print(f"\nAPI Response Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"\nNumber of results: {len(data.get('web', {}).get('results', []))}")
        
        print("\n=== SEARCH RESULTS ===")
        if "web" in data and "results" in data["web"]:
            for i, result in enumerate(data["web"]["results"][:5], 1):
                title = result.get("title", "No title")
                url = result.get("url", "No URL")
                description = result.get("description", "No description")
                
                # Extract domain for brand identification
                domain = url.split('/')[2] if '/' in url and len(url.split('/')) > 2 else url
                
                print(f"\nResult {i}:")
                print(f"  Title: {title}")
                print(f"  Domain: {domain}")
                print(f"  URL: {url}")
                print(f"  Description: {description[:200]}..." if len(description) > 200 else f"  Description: {description}")
                
        # Try to extract real competitor names
        print("\n\n=== EXTRACTING COMPETITOR NAMES ===")
        search_text = json.dumps(data, indent=2)
        
        # Look for specific patterns
        import re
        
        # Look for business names in titles and descriptions
        competitor_names = set()
        
        if "web" in data and "results" in data["web"]:
            for result in data["web"]["results"]:
                # Extract from title
                title = result.get("title", "")
                # Look for store names before common words
                title_match = re.search(r'^([A-Z][a-zA-Z0-9\s&\'-]+?)\s*(?:-|\||–|\.|,)', title)
                if title_match:
                    name = title_match.group(1).strip()
                    if len(name) > 2 and name.lower() not in ['the', 'best', 'top', 'online']:
                        competitor_names.add(name)
                
                # Extract from URL domain
                url = result.get("url", "")
                if '.com.uy' in url or '.uy' in url:
                    domain_match = re.search(r'https?://(?:www\.)?([a-zA-Z0-9\-]+)\.(?:com\.)?uy', url)
                    if domain_match:
                        domain_name = domain_match.group(1).replace('-', ' ').title()
                        if len(domain_name) > 2:
                            competitor_names.add(domain_name)
        
        print("\nExtracted competitor names:")
        for name in sorted(competitor_names):
            print(f"  - {name}")
            
        # Save full response for analysis
        with open('brave_search_response.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print("\n\nFull response saved to brave_search_response.json")
        
    else:
        print(f"\nError response: {response.text}")
        
except Exception as e:
    print(f"\nError making request: {str(e)}")
    import traceback
    traceback.print_exc()