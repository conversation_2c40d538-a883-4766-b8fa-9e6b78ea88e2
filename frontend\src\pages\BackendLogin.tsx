import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Alert,
  IconButton,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext'; // Import the custom hook instead
 // Adjust path if needed
import Logo from '../assets/logo.png'; // Assuming logo is in assets
import AuthLayout from '../components/auth/AuthLayout'; // Import AuthLayout
import { useTranslation } from 'react-i18next'; // <-- Add this import
import { logger } from '../utils/logger';
import ErrorBoundary from '../components/ErrorBoundary';



const BackendLogin = () => {
  const { t } = useTranslation(); // <-- Get the t function
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { login } = useAuth(); // Use the custom hook
  const navigate = useNavigate();

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError(null);
    setLoading(true);

    if (!email || !password) {
        setError(t('adminLogin.errorRequired')); // <-- Use t()
        setLoading(false);
        return;
    }

    try {
        logger.debug('Attempting admin login...');
        // Call the login function from AuthContext
        await login(email, password);
        logger.info('Admin login successful, redirecting to /admin');
        // On successful login (no exception thrown), redirect to /admin
        navigate('/admin');

    } catch (error: unknown) {
        // Handle errors (e.g., invalid credentials, network issues)
        logger.error("Admin login failed:", error);
        
        // --- Start: Refined type-safe error handling logic ---
        let errorMessage = t('adminLogin.errorUnexpected'); // <-- Use t()

        // Check if error is an object and has a response property
        if (typeof error === 'object' && error !== null && 'response' in error) {
            const response = (error as { response?: unknown }).response;

            // Check if response is an object and has data property
            if (typeof response === 'object' && response !== null && 'data' in response) {
                const data = (response as { data?: unknown }).data;

                // Check if data is an object and has a detail property (string)
                if (typeof data === 'object' && data !== null && 'detail' in data && typeof (data as { detail?: unknown }).detail === 'string') {
                    // We have a specific API error structure with a detail string
                    const apiError = error as { response: { data: { detail: string }, status?: number } };
                    const status = apiError.response.status; // Safely access status if needed

                    // Handle specific HTTP status codes
                    if (status === 403) {
                        errorMessage = t('adminLogin.errorNoAdminPrivileges'); // <-- Use t()
                    } else if (status === 401) {
                        errorMessage = t('adminLogin.errorInvalidCredentials'); // <-- Use t()
                    } else {
                        // Use the detail message from the backend
                        errorMessage = apiError.response.data.detail || t('adminLogin.errorLoginFailedFallback'); // <-- Use t()
                    }
                }
                // If data doesn't have a string 'detail', try checking for a standard 'message' property on the error itself
                else if ('message' in error && typeof (error as { message?: unknown }).message === 'string') {
                     errorMessage = (error as { message: string }).message;
                }
                // If neither 'detail' nor 'message' is found, the default message remains.
            }
            // If response doesn't have data, try checking for a standard 'message' property on the error itself
             else if ('message' in error && typeof (error as { message?: unknown }).message === 'string') {
                errorMessage = (error as { message: string }).message;
            }
            // If response structure is unexpected, the default message remains.
        }
        // If error doesn't have a response, check directly for a standard 'message' property
        else if (typeof error === 'object' && error !== null && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {
             errorMessage = (error as { message: string }).message;
        }
        // else: error is not an object or doesn't fit expected structures, keep the default errorMessage
        // --- End: Refined type-safe error handling logic ---
        
        setError(errorMessage); // Update the state with the determined error message
        // setLoading(false); // setLoading is handled in finally block
    } finally {
        setLoading(false);
    }
  };

  return (
    <ErrorBoundary>
      <AuthLayout>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 2, sm: 4 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            borderRadius: 2,
            width: '100%',
            maxWidth: { xs: '92%', sm: '400px' },
          }}
        >
          <Box sx={{ mb: 2 }}>
              <Box
                component="img"
                src={Logo}
                alt="D-Unit Logo"
                sx={{
                  height: { xs: '40px', sm: '50px' },
                  width: 'auto',
                  objectFit: 'contain'
                }}
              />
          </Box>
          <Typography component="h1" variant="h5" sx={{ mb: 1 }}>
            {t('adminLogin.title')}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {t('adminLogin.subtitle')}
          </Typography>
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }}>
            {error && <Alert severity="error" sx={{ mb: 2, width: '100%' }}>{error}</Alert>}
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label={t('adminLogin.usernameLabel')}
              name="username"
              autoComplete="username"
              autoFocus
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label={t('adminLogin.passwordLabel')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label={t('adminLogin.togglePasswordVisibility')}
                      onClick={handleClickShowPassword}
                      edge="end"
                      disabled={loading}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2, bgcolor: '#00A3FF', '&:hover': { bgcolor: '#008dd1' } }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : t('adminLogin.signInButton')}
            </Button>
          </Box>
        </Paper>
      </AuthLayout>
    </ErrorBoundary>
  );
};

export default BackendLogin;