import os
import re
import secrets
import base64
import logging
import traceback
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, Depends, Path, Query, status
from fastapi.security import OAuth2PasswordBearer
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
import json
import httpx
import time
import threading
from functools import lru_cache

from config.database import db_main, db_analysis
from config.settings import get_settings
from models.user import User, UserInDB, TokenData
from services.store import ensure_store_analysis

# Configure logging
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Security configurations
ALGORITHM = settings.JWT_ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# User cache implementation
class UserCache:
    """In-memory user cache with TTL, thread safety, and batch-based logging"""
    
    def __init__(self, ttl_seconds: int = 300):  # 5 minutes default TTL
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.ttl_seconds = ttl_seconds
        self.lock = threading.RLock()
        self._last_cleanup = time.time()
        self.cleanup_interval = 60  # Cleanup every minute
        
        # Cache statistics and batch logging
        self.stats_lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
        self.last_stats_log = time.time()
        self.stats_log_interval = 300  # Log stats every 5 minutes
        
        # Request batch tracking (10-second windows per user)
        self.batch_tracking: Dict[str, Dict[str, Any]] = {}
        self.batch_window = 10  # 10 seconds per batch
    
    def _cleanup_expired(self):
        """Remove expired entries from cache"""
        current_time = time.time()
        if current_time - self._last_cleanup < self.cleanup_interval:
            return
            
        with self.lock:
            expired_keys = []
            for key, entry in self.cache.items():
                if current_time > entry['expires_at']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                
            self._last_cleanup = current_time
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired user cache entries")
    
    def _should_log_cache_batch(self, email: str) -> tuple[bool, int]:
        """Determine if we should log this cache hit and return batch info"""
        current_time = time.time()
        
        with self.stats_lock:
            email_key = email.lower()
            
            # Check if we have an active batch for this user
            if email_key in self.batch_tracking:
                batch_info = self.batch_tracking[email_key]
                batch_start = batch_info['start_time']
                
                # If within the batch window, increment and don't log individual hit
                if current_time - batch_start < self.batch_window:
                    batch_info['hit_count'] += 1
                    return False, batch_info['hit_count']
                else:
                    # Batch window expired, log the completed batch if it had multiple hits
                    hit_count = batch_info['hit_count']
                    if hit_count > 1:
                        logger.debug(f"Cache batch completed: {hit_count} hits for user: {email}")
                    
                    # Start new batch
                    self.batch_tracking[email_key] = {
                        'start_time': current_time,
                        'hit_count': 1
                    }
                    return True, 1
            else:
                # Start new batch for this user
                self.batch_tracking[email_key] = {
                    'start_time': current_time,
                    'hit_count': 1
                }
                return True, 1
    
    def _cleanup_old_batches(self):
        """Clean up old batch tracking entries"""
        current_time = time.time()
        with self.stats_lock:
            expired_batches = []
            for email_key, batch_info in self.batch_tracking.items():
                if current_time - batch_info['start_time'] > (self.batch_window * 2):
                    expired_batches.append(email_key)
            
            for email_key in expired_batches:
                del self.batch_tracking[email_key]
    
    def _log_periodic_stats(self):
        """Log cache statistics periodically"""
        current_time = time.time()
        
        with self.stats_lock:
            if current_time - self.last_stats_log > self.stats_log_interval:
                total_requests = self.hit_count + self.miss_count
                if total_requests > 0:
                    hit_ratio = (self.hit_count / total_requests) * 100
                    logger.info(f"Cache stats: {self.hit_count} hits, {self.miss_count} misses, {hit_ratio:.1f}% hit ratio")
                    
                    # Reset counters for next period
                    self.hit_count = 0
                    self.miss_count = 0
                
                self.last_stats_log = current_time
                
                # Also cleanup old batch tracking
                self._cleanup_old_batches()
    
    def get(self, email: str) -> Optional[User]:
        """Get user from cache if not expired"""
        with self.lock:
            self._cleanup_expired()
            entry = self.cache.get(email.lower())
            
            if entry and time.time() <= entry['expires_at']:
                # Cache hit - use batch logging
                with self.stats_lock:
                    self.hit_count += 1
                    
                should_log, batch_count = self._should_log_cache_batch(email)
                if should_log:
                    logger.debug(f"Cache hit for user: {email}")
                
                self._log_periodic_stats()
                return entry['user']
            elif entry:
                # Entry exists but expired
                del self.cache[email.lower()]
                logger.debug(f"Cache expired for user: {email}")
                
            # Cache miss
            with self.stats_lock:
                self.miss_count += 1
            self._log_periodic_stats()
            return None
    
    def put(self, email: str, user: User):
        """Store user in cache with TTL"""
        with self.lock:
            expires_at = time.time() + self.ttl_seconds
            self.cache[email.lower()] = {
                'user': user,
                'expires_at': expires_at
            }
            logger.debug(f"Cached user: {email} (expires in {self.ttl_seconds}s)")
    
    def invalidate(self, email: str):
        """Remove user from cache"""
        with self.lock:
            email_key = email.lower()
            if email_key in self.cache:
                del self.cache[email_key]
                logger.debug(f"Invalidated cache for user: {email}")
            
            # Also remove from batch tracking
            with self.stats_lock:
                if email_key in self.batch_tracking:
                    del self.batch_tracking[email_key]
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            count = len(self.cache)
            self.cache.clear()
            logger.info(f"Cleared user cache ({count} entries)")
            
            # Clear batch tracking and stats
            with self.stats_lock:
                self.batch_tracking.clear()
                self.hit_count = 0
                self.miss_count = 0
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            self._cleanup_expired()
            
            with self.stats_lock:
                active_batches = len(self.batch_tracking)
                total_requests = self.hit_count + self.miss_count
                hit_ratio = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
                
                return {
                    'entries': len(self.cache),
                    'ttl_seconds': self.ttl_seconds,
                    'last_cleanup': self._last_cleanup,
                    'hit_count': self.hit_count,
                    'miss_count': self.miss_count,
                    'hit_ratio_percent': round(hit_ratio, 1),
                    'active_batches': active_batches,
                    'batch_window_seconds': self.batch_window
                }

# Global user cache instance
user_cache = UserCache(ttl_seconds=300)  # 5 minutes TTL

# Add authentication log rate limiting to prevent spam
_auth_log_cache: Dict[str, float] = {}
_auth_log_cache_lock = threading.RLock()

def _should_log_auth_event(user_email: str, event_type: str, cooldown_seconds: int = 60) -> bool:
    """Rate limit authentication event logging (non-cache events like store access verification) to prevent spam"""
    global _auth_log_cache
    with _auth_log_cache_lock:
        cache_key = f"{user_email}:{event_type}"
        current_time = time.time()
        
        if cache_key in _auth_log_cache:
            last_logged = _auth_log_cache[cache_key]
            if current_time - last_logged < cooldown_seconds:
                return False
        
        _auth_log_cache[cache_key] = current_time
        
        # Clean up old entries periodically (more efficient cleanup)
        if len(_auth_log_cache) > 500:  # Reduced threshold for more frequent cleanup
            cutoff_time = current_time - (cooldown_seconds * 2)
            _auth_log_cache = {k: v for k, v in _auth_log_cache.items() if v > cutoff_time}
        
        return True

def generate_and_save_secret_key() -> str:
    """Generate a secure secret key and save it to .env file if possible"""
    new_key = base64.b64encode(secrets.token_bytes(32)).decode()
    try:
        env_path = '/var/app/current/.env'
        if os.path.exists(env_path):
            with open(env_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            key_updated = False
            for i, line in enumerate(lines):
                if line.startswith('# JWT_SECRET_KEY=') or line.startswith('JWT_SECRET_KEY='):
                    lines[i] = f'JWT_SECRET_KEY={new_key}\n'
                    key_updated = True
                    break
            if not key_updated:
                lines.append(f'\nJWT_SECRET_KEY={new_key}\n')
            with open(env_path, 'w', encoding='utf-8') as file:
                file.writelines(lines)
            logger.info("Generated and saved new JWT secret key")
    except Exception as e:
        logger.error(f"Error saving JWT secret key: {e}")
        logger.info("Using generated JWT secret key without saving to file")
    return new_key

def get_jwt_secret_key() -> str:
    """Get JWT secret key from environment or generate a new one"""
    secret_key = settings.JWT_SECRET_KEY
    if secret_key:
        return secret_key
    try:
        return generate_and_save_secret_key()
    except Exception as e:
        logger.error(f"Error handling JWT secret key: {e}")
        return base64.b64encode(secrets.token_bytes(32)).decode()

def verify_password(plain_password: str, stored_password: str) -> bool:
    try:
        if not plain_password or not stored_password:
            logger.warning("Empty password provided")
            return False
            
        # Log password lengths and formats (without revealing the actual passwords)
        logger.info(f"Plain password length: {len(plain_password)}")
        logger.info(f"Stored password length: {len(stored_password)}")
        
        # Clean up passwords
        plain_cleaned = plain_password.strip()
        stored_cleaned = stored_password.strip()
        
        logger.info(f"Cleaned password lengths - Plain: {len(plain_cleaned)}, Stored: {len(stored_cleaned)}")
        
        # Securely verify the plain password against the stored hash
        is_valid = pwd_context.verify(plain_cleaned, stored_cleaned)
        logger.info(f"Password verification result using bcrypt: {is_valid}")

        return is_valid
    except Exception as e:
        logger.error(f"Error verifying password: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return False

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# Cache management functions
def invalidate_user_cache(email: str):
    """Invalidate cached user data - call this when user data changes"""
    user_cache.invalidate(email)
    logger.info(f"Invalidated user cache for: {email}")

def clear_user_cache():
    """Clear all cached user data"""
    user_cache.clear()

def get_cache_stats() -> Dict[str, Any]:
    """Get comprehensive user cache statistics including batch logging info"""
    base_stats = user_cache.stats()
    
    # Add additional runtime information
    with user_cache.stats_lock:
        current_time = time.time()
        time_since_last_stats = current_time - user_cache.last_stats_log
        time_until_next_stats = max(0, user_cache.stats_log_interval - time_since_last_stats)
        
        return {
            **base_stats,
            'cache_efficiency': {
                'total_requests': base_stats['hit_count'] + base_stats['miss_count'],
                'next_stats_log_in_seconds': round(time_until_next_stats, 1),
                'stats_log_interval_seconds': user_cache.stats_log_interval
            },
            'batch_logging': {
                'active_user_batches': base_stats['active_batches'],
                'batch_window_seconds': base_stats['batch_window_seconds'],
                'reduces_log_noise': True
            }
        }

async def authenticate_user(email: str, password: str) -> Optional[User]:
    """
    Authenticate a user using email and password.
    Returns the user if authentication is successful, None otherwise.
    """
    try:
        logger.debug(f"Authentication attempt for: {email}")
        
        # Get user from database - use case-insensitive search
        email_pattern = {"$regex": f"^{re.escape(email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern},
                {"selected_meta_email": email_pattern}
            ]
        }
        
        # Use await for async database call
        user = await db_main["store_users"].find_one(query)
        
        if not user:
            logger.warning(f"User not found: {email}")
            return None

        # Check if password verification is needed
        user_pass = user.get("pass_dunit")
        if not user_pass:
            logger.warning(f"No password found for user: {email}")
            return None

        # Clean passwords (remove newlines and whitespace)
        cleaned_password = password.strip()
        cleaned_stored_password = user_pass.strip()

        # Verify password
        try:
            is_valid = pwd_context.verify(cleaned_password, cleaned_stored_password)
        except Exception as verify_error:
            logger.error(f"Password verification failed for {email}: {str(verify_error)}")
            return None

        if not is_valid:
            logger.warning(f"Invalid password for user: {email}")
            return None

        # Successful authentication - invalidate cache to ensure fresh data
        invalidate_user_cache(email)

        # Create and return user object
        user_obj = create_user_object(user)
        logger.info(f"Authentication successful for: {email}")
        return user_obj

    except Exception as e:
        logger.error(f"Authentication error for {email}: {str(e)}")
        return None

def create_user_object(user_data: Dict[str, Any]) -> User:
    """
    Create a User object from database user data, handling type conversions.
    """
    try:
        logger.debug(f"Creating User object from data using User.from_db: {user_data}")
        # Utilize the User.from_db classmethod to handle data conversion and instantiation
        user = User.from_db(user_data)
        logger.info("Successfully created User object using User.from_db")
        return user
    except Exception as e:
        logger.error(f"Error creating user object using User.from_db: {str(e)}")
        logger.error(f"User data causing error: {user_data}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise # Re-raise the exception to make the error visible

async def get_user(email: str) -> Optional[UserInDB]:
    try:
        # Search in the store_users collection in D-Unit database - Use await
        # Update query to check primary email, alternate emails and selected_meta_email
        email_pattern = {"$regex": f"^{re.escape(email)}$", "$options": "i"}
        query = {
            "$or": [
                {"email": email_pattern},
                {"alternate_emails": email_pattern},
                {"selected_meta_email": email_pattern}
            ]
        }
        user_data = await db_main["store_users"].find_one(query)
        
        if user_data:
            # Convert store_id to string if it exists
            store_id = str(user_data["id_store"]) if user_data.get("id_store") else None
            logger.info(f"Found store_id in user data: {store_id}")
            
            # First check in store_analysis collection - Use await
            store_analysis = await db_analysis["store_analysis"].find_one({
                "$or": [
                    {"document.id_store": store_id},
                    {"document.id_store": int(store_id) if store_id else None}
                ]
            })
            if store_analysis:
                logger.info(f"Found store analysis with id: {store_id}")
            else:
                # Debug: List all store_analysis documents - Fix cursor handling
                # Use await and cursor.to_list()
                cursor = db_analysis["store_analysis"].find({}, {"document.id_store": 1})
                all_stores = await cursor.to_list(length=None) 
                logger.info(f"Available store IDs in analysis: {[store.get('document', {}).get('id_store') for store in all_stores]}")
                logger.warning(f"Store {store_id} not found in analysis database")
            
            # Create UserInDB with proper type handling
            user_password = user_data.get("password", "")
            if not user_password:
                logger.warning(f"No password found for user {user_data['email']}")
                return None
                
            return UserInDB(
                email=user_data["email"],
                id_store=store_id,
                name=user_data.get("name", ""),
                hashed_password=user_password,
                active=user_data.get("active", 1),
                created_at=user_data.get("created_at"),
                updated_at=user_data.get("updated_at"),
                alternate_emails=user_data.get("alternate_emails"),
                selected_meta_email=user_data.get("selected_meta_email")
            )
        return None
    except Exception as e:
        logger.error(f"Error getting user: {str(e)}")
        return None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    try:
        # Use get_jwt_secret_key() instead of SECRET_KEY
        secret_key = get_jwt_secret_key()
        encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Error creating access token: {str(e)}")
        raise HTTPException(status_code=500, detail="Could not create access token")

async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Use get_jwt_secret_key() instead of SECRET_KEY
        secret_key = get_jwt_secret_key()
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])
        
        # Handle email with proper type checking
        email = payload.get("sub")
        if not isinstance(email, str) or not email:
            raise credentials_exception
        
        # Handle store_id with proper type checking
        store_id = payload.get("store_id")
        if store_id is not None and not isinstance(store_id, (str, int)):
            raise credentials_exception
        
        # Convert store_id to string or None for TokenData
        store_id_str = str(store_id) if store_id is not None else None
            
        token_data = TokenData(email=email, store_id=store_id_str)
    except JWTError as e:
        logger.warning(f"JWT validation failed: {e}")
        raise credentials_exception
    
    # Get user from cache or database - new batch-based caching handles logging automatically
    user_email = token_data.email
    assert user_email is not None, "Email should not be None after token validation"
    
    cached_user = user_cache.get(user_email)
    if cached_user:
        return cached_user
    
    # Cache miss - fetch from database
    logger.debug(f"Cache miss - fetching user from database: {user_email}")
    
    # Fetch from database
    user_doc = await db_main["store_users"].find_one({"email": user_email})
    if not user_doc:
        raise credentials_exception
    
    try:
        user = User.from_db(user_doc)
        logger.debug(f"Successfully created User object using User.from_db")
        
        # Cache the user - batch logging handled automatically
        user_cache.put(user_email, user)
        logger.debug(f"Cached user: {user_email} (expires in {user_cache.ttl_seconds}s)")
        
        return user
    except Exception as e:
        logger.error(f"Failed to create User object: {e}")
        raise credentials_exception

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    return current_user

def verify_google_token(token: str):
    try:
        import requests
        
        # Use the access token to get user info from Google
        userinfo_response = requests.get(
            'https://www.googleapis.com/oauth2/v3/userinfo',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        if userinfo_response.status_code != 200:
            raise ValueError(f"Failed to get user info: {userinfo_response.text}")
            
        userinfo = userinfo_response.json()
        
        # Verify required fields
        if not userinfo.get('email'):
            raise ValueError("Email not found in Google user info")
            
        if not userinfo.get('email_verified'):
            raise ValueError("Email not verified with Google")
            
        return {
            'email': userinfo['email'],
            'name': userinfo.get('name', ''),
            'picture': userinfo.get('picture', ''),
            'given_name': userinfo.get('given_name', ''),
            'family_name': userinfo.get('family_name', '')
        }
    except ValueError as e:
        logger.error(f"Invalid Google credentials: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"Invalid Google credentials: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during Google verification: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Authentication failed: {str(e)}"
        )

async def verify_meta_token(token: str, attempt_refresh: bool = False):
    try:
        url = f"https://graph.facebook.com/me?fields=id,name,email&access_token={token}"
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
        
        if response.status_code != 200:
            error_data = {}
            try:
                error_data = response.json()
            except Exception:
                logger.error(f"Failed to parse error response from Meta API: {response.text}")
                
            error_detail = error_data.get("error", {})
            error_code = error_detail.get("code")
            error_message = error_detail.get("message", "Unknown Meta API error")
            
            logger.warning(f"Meta API call failed. Status: {response.status_code}, Code: {error_code}, Msg: {error_message}, Data: {error_data}")

            # Handle Rate Limit (Code 4)
            if error_code == 4:
                 logger.error(f"Meta API rate limit reached: {response.text}")
                 raise HTTPException(
                     status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                     detail="Meta API request limit reached. Please try again later."
                 )

            # Handle Authentication Error (Code 190 - Invalid/Expired Token)
            elif error_code == 190:
                logger.error(f"Meta token invalid or expired: {response.text}")
                
                # If we're already attempting a refresh, raise the final error
                if attempt_refresh:
                    logger.error("Meta token refresh already attempted or failed. Raising 401.")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"Meta token expired or invalid after refresh attempt: {error_message}"
                    )
                
                # Attempt refresh only once
                logger.info("Attempting to refresh the Meta token")
                new_token = refresh_meta_token(token) # Assuming refresh_meta_token is synchronous or needs adjustment
                if new_token:
                    # Update the token in the database (needs async handling if DB operations are async)
                    # updated = await update_stored_meta_token(token, new_token)
                    # For now, just retry verification with the new token
                    logger.info("Meta token possibly refreshed, verifying new token.")
                    # Verify the new token works
                    try:
                        return await verify_meta_token(new_token, attempt_refresh=True)
                    except HTTPException as refresh_exc:
                        # If refresh verification fails, raise the original error
                        logger.error(f"Verification failed after token refresh: {refresh_exc.detail}")
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail=f"Meta token invalid even after refresh attempt: {error_message}"
                        )
                else:
                    logger.warning("Meta token refresh failed.")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"Meta token expired and refresh failed: {error_message}"
                    )

            # Handle other Meta API errors
            else:
                logger.error(f"Unhandled Meta API error: {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, # Or 502 Bad Gateway? Depends on cause.
                    detail=f"Meta API error ({error_code}): {error_message}"
                )
            
        # Successful response
        user_data = response.json()
        
        if not user_data.get("id"):
            logger.error("Meta token verification successful but no user ID in response")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Meta token verification succeeded but response lacked user ID."
            )
            
        logger.info(f"Meta token verified successfully for user ID: {user_data.get('id')}")    
        return user_data
        
    except HTTPException as he:
        # Re-raise HTTPExceptions correctly
        raise he
    except Exception as e:
        logger.error(f"Unexpected error during Meta token verification: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during Meta authentication: {str(e)}"
        )

def refresh_meta_token(token: str) -> Optional[str]:
    """
    Refresh a Meta access token using the fb_exchange_token flow.
    Returns the new token if successful, None otherwise.
    """
    try:
        import requests
        from config.settings import get_settings
        
        settings = get_settings()
        app_id = settings.FACEBOOK_APP_ID
        app_secret = settings.FACEBOOK_APP_SECRET
        
        if not app_id or not app_secret:
            logger.error("Missing Facebook app credentials for token refresh")
            return None
            
        # Build the token exchange URL
        exchange_url = "https://graph.facebook.com/oauth/access_token"
        params = {
            "grant_type": "fb_exchange_token",
            "client_id": app_id,
            "client_secret": app_secret,
            "fb_exchange_token": token
        }
        
        logger.info("Attempting to refresh Meta token using fb_exchange_token")
        response = requests.get(exchange_url, params=params)
        
        if response.status_code != 200:
            logger.error(f"Failed to refresh Meta token: {response.text}")
            return None
            
        # Parse the response and extract the new token
        result = response.json()
        if "access_token" not in result:
            logger.error(f"No access_token in refresh response: {result}")
            return None
            
        new_token = result["access_token"]
        logger.info("Successfully refreshed Meta token")
        
        # Return the new token
        return new_token
    except Exception as e:
        logger.error(f"Error refreshing Meta token: {str(e)}")
        return None

async def update_stored_meta_token(old_token: str, new_token: str) -> bool:
    """
    Update stored Meta token in the database.
    Returns True if successful, False otherwise.
    """
    try:
        from config.database import db_analysis
        
        # Update in meta_pages - Use await
        result = await db_analysis["meta_pages"].update_many(
            {"access_token": old_token},
            {"$set": {"access_token": new_token}}
        )
        
        # Also update in meta_integrations if present - Use await
        await db_analysis["meta_integrations"].update_many(
            {"access_token": old_token},
            {"$set": {"access_token": new_token}}
        )
        
        logger.info(f"Updated {result.modified_count} Meta page records with new token")
        
        # Return True if at least one document was updated
        return result.modified_count > 0
    except Exception as e:
        logger.error(f"Error updating Meta token in database: {str(e)}")
        return False

async def verify_store_access(user_email: str, store_id: str) -> bool:
    """
    Verify if a user (identified by email) has access to the specified store.
    Also checks for admin role.

    Parameters:
    - user_email: The email of the user
    - store_id: The ID of the store

    Returns:
    - bool: True if the user has access, False otherwise
    """
    try:
        # Use the correct collection: store_users
        user = await db_main["store_users"].find_one({"email": user_email})
        if not user:
            logger.warning(f"User with email {user_email} not found in store_users collection for store access check.")
            return False

        # Check if the user has an admin role
        # Use .get with default to handle missing role field gracefully
        if user.get("role") == "admin":
            # Rate-limited logging for admin access
            if _should_log_auth_event(user_email, f"admin_access_{store_id}", cooldown_seconds=300):
                logger.debug(f"User {user_email} verified for store {store_id} as admin.")
            return True

        # Check if the user is directly associated with the store
        if str(user.get("id_store")) == str(store_id):
            # Rate-limited logging for store access
            if _should_log_auth_event(user_email, f"store_access_{store_id}", cooldown_seconds=300):
                logger.debug(f"User {user_email} verified for store {store_id} via id_store field.")
            return True

        # Only log access denials occasionally to avoid spam
        if _should_log_auth_event(user_email, f"access_denied_{store_id}", cooldown_seconds=120):
            logger.warning(f"User {user_email} does not have access to store {store_id} (Not admin and id_store mismatch). User's store: {user.get('id_store')}")
        return False

    except Exception as e:
        logger.error(f"Error verifying store access for user {user_email} and store {store_id}: {e}", exc_info=True)
        return False # Fail securely

# NEW Dependency Function
async def verify_user_can_access_store(
    store_id: str = Path(..., description="The ID of the store being accessed"),
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    FastAPI dependency to verify if the current user can access the specified store_id.
    Allows access if user's id_store matches or if user has 'admin' role.
    Returns the current_user object if access is granted, otherwise raises HTTPException.
    """
    logger.debug(f"Store access check: user={current_user.email}, user_store={current_user.id_store}, requested_store={store_id}")

    # Use the corrected verify_store_access function
    has_access = await verify_store_access(user_email=current_user.email, store_id=store_id)

    if not has_access:
        logger.warning(f"Access denied for user {current_user.email} (role: {current_user.role}, user_store: {current_user.id_store}) to store {store_id}")
        raise HTTPException(
            status_code=403,
            detail=f"User does not have permission to access store {store_id}"
        )
    # If access is granted, return the user object for the route to use
    logger.debug(f"Store access granted for user {current_user.email} to store {store_id}")
    return current_user

# NEW Dependency Function for routes with store_id as query parameter
async def verify_user_can_access_store_query(
    store_id: str = Query(..., description="The ID of the store being accessed"),
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    FastAPI dependency to verify if the current user can access a store specified as a query parameter.
    Allows access if user's id_store matches or if user has 'admin' role.
    Returns the current_user object if access is granted, otherwise raises HTTPException.
    """
    # Use the verify_store_access function
    has_access = await verify_store_access(user_email=current_user.email, store_id=store_id)

    if not has_access:
        logger.warning(f"Access denied for user {current_user.email} (role: {current_user.role}) to store {store_id}")
        raise HTTPException(
            status_code=403,
            detail=f"User does not have permission to access store {store_id}"
        )
    # If access is granted, return the user object for the route to use
    return current_user

# Admin role checking functions
async def require_admin_role(current_user: User = Depends(get_current_user)) -> bool:
    """
    FastAPI dependency to verify if the current user has admin role.
    Raises HTTPException if user is not admin.
    Returns True if user is admin.
    """
    if not current_user:
        logger.warning("Authentication required for admin access")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    if current_user.role != "admin":
        logger.warning(f"Non-admin user '{current_user.email}' attempted to access admin route.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required for this operation"
        )
    
    logger.debug(f"Admin access granted to user: {current_user.email}")
    return True

def has_admin_role(user: User) -> bool:
    """
    Check if a user has admin role.
    Returns True if user is admin, False otherwise.
    """
    return user.role == "admin" if user else False

async def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """
    FastAPI dependency that returns the current user only if they have admin role.
    Raises HTTPException if user is not admin.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    if current_user.role != "admin":
        logger.warning(f"Non-admin user '{current_user.email}' attempted admin operation")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    return current_user

