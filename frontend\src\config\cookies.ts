import { CookieConfiguration, CookieCategory, CookieDetails } from '../types/cookies';

// Cookie categories as defined in the legal documentation
export const COOKIE_CATEGORIES: CookieCategory[] = [
  {
    id: 'essential',
    name: 'Essential Cookies',
    description: 'These cookies are necessary for the website to function and cannot be switched off. They are usually only set in response to actions made by you such as logging in or filling in forms.',
    essential: true,
    enabled: true
  },
  {
    id: 'functional',
    name: 'Functional Cookies',
    description: 'These cookies enable the website to provide enhanced functionality and personalisation such as user preferences and language settings.',
    essential: false,
    enabled: false
  },
  {
    id: 'analytics',
    name: 'Analytics Cookies',
    description: 'These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site.',
    essential: false,
    enabled: false
  },
  {
    id: 'performance',
    name: 'Performance Cookies',
    description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
    essential: false,
    enabled: false
  },
  {
    id: 'marketing',
    name: 'Marketing Cookies',
    description: 'These cookies may be set through our site by our advertising partners to build a profile of your interests.',
    essential: false,
    enabled: false
  }
];

// Duration translation utility function
export const translateDuration = (duration: string, t: (key: string, fallback?: string) => string): string => {
  return duration
    .replace(/\b(\d+)\s*hours?\b/gi, (_match, num) => {
      const unit = parseInt(num) === 1 ? 'hour' : 'hours';
      return `${num} ${t(`cookies.durations.timeUnits.${unit}`, unit)}`;
    })
    .replace(/\b(\d+)\s*days?\b/gi, (_match, num) => {
      const unit = parseInt(num) === 1 ? 'day' : 'days';
      return `${num} ${t(`cookies.durations.timeUnits.${unit}`, unit)}`;
    })
    .replace(/\b(\d+)\s*years?\b/gi, (_match, num) => {
      const unit = parseInt(num) === 1 ? 'year' : 'years';
      return `${num} ${t(`cookies.durations.timeUnits.${unit}`, unit)}`;
    })
    .replace(/\bSession\b/g, t('cookies.durations.timeUnits.session', 'Session'));
};

// Detailed cookie inventory based on legal documentation
export const COOKIE_INVENTORY: CookieDetails[] = [
  // Essential Cookies - Authentication & Security
  {
    name: 'dunit_auth_token',
    category: 'essential',
    description: 'JWT authentication token for user session management',
    duration: 'Session / 24 hours',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: true,
    purpose: 'cookies.purposes.authSession',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_refresh_token',
    category: 'essential',
    description: 'Token refresh mechanism for continuous authentication',
    duration: '7 days',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: true,
    purpose: 'cookies.purposes.sessionRenewal',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_csrf_token',
    category: 'essential',
    description: 'CSRF protection token for form submissions',
    duration: 'Session',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: true,
    purpose: 'cookies.purposes.csrfProtection',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_store_context',
    category: 'essential',
    description: 'Current store context for multi-store users',
    duration: 'Session',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: true,
    purpose: 'cookies.purposes.storeIsolation',
    provider: 'D-Unit'
  },

  // AWS CloudFront Cookies (Third-party)
  {
    name: 'CloudFront-*',
    category: 'essential',
    description: 'AWS CloudFront distribution and load balancing cookies',
    duration: 'Session',
    domain: 'cloudfront.net',
    type: 'third-party',
    essential: true,
    purpose: 'cookies.purposes.contentDelivery',
    provider: 'Amazon Web Services'
  },

  // Functional Cookies - User Preferences
  {
    name: 'dunit_user_preferences',
    category: 'functional',
    description: 'User interface preferences and settings',
    duration: '1 year',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.userPreferences',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_language',
    category: 'functional',
    description: 'User selected language preference',
    duration: '1 year',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.languageLocalization',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_theme',
    category: 'functional',
    description: 'User interface theme preference (light/dark mode)',
    duration: '1 year',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.themeCustomization',
    provider: 'D-Unit'
  },

  // Analytics Cookies (Planned)
  {
    name: 'dunit_analytics_session',
    category: 'analytics',
    description: 'Analytics session identifier for usage tracking',
    duration: 'Session',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.usageAnalytics',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_page_views',
    category: 'analytics',
    description: 'Page view tracking for navigation analysis',
    duration: '30 days',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.navigationPatterns',
    provider: 'D-Unit'
  },

  // Performance Cookies (Planned)
  {
    name: 'dunit_performance_timing',
    category: 'performance',
    description: 'Website performance metrics and load times',
    duration: 'Session',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.performanceMonitoring',
    provider: 'D-Unit'
  },
  {
    name: 'dunit_error_tracking',
    category: 'performance',
    description: 'Error monitoring and debugging information',
    duration: '7 days',
    domain: undefined, // Let browser handle domain automatically
    type: 'first-party',
    essential: false,
    purpose: 'cookies.purposes.errorTracking',
    provider: 'D-Unit'
  },

  // Meta/Facebook Integration Cookies (Third-party)
  {
    name: 'fbsr_*',
    category: 'functional',
    description: 'Facebook SDK session cookies for Meta integration',
    duration: 'Session',
    domain: '.facebook.com',
    type: 'third-party',
    essential: false,
    purpose: 'cookies.purposes.metaIntegration',
    provider: 'Meta Platforms, Inc.'
  },
  {
    name: 'fb_access_token',
    category: 'functional',
    description: 'Facebook access token for API integration',
    duration: '2 hours',
    domain: '.facebook.com',
    type: 'third-party',
    essential: false,
    purpose: 'cookies.purposes.facebookTools',
    provider: 'Meta Platforms, Inc.'
  }
];

// Main cookie configuration
export const COOKIE_CONFIG: CookieConfiguration = {
  categories: COOKIE_CATEGORIES,
  cookies: COOKIE_INVENTORY,
  consentVersion: '1.0.0',
  requiredNoticeVersion: '1.0.0',
  legalBasis: {
    gdpr: true,
    ccpa: true,
    lgpd: true,
    pipeda: true
  }
};

// Default cookie preferences
export const DEFAULT_COOKIE_PREFERENCES = {
  essential: true,
  analytics: false,
  performance: false,
  functional: false,
  marketing: false,
  preferences: false
};

// Cookie consent storage keys
export const COOKIE_CONSENT_KEY = 'dunit_cookie_consent';
export const COOKIE_PREFERENCES_KEY = 'dunit_cookie_preferences';
export const COOKIE_CONSENT_VERSION_KEY = 'dunit_cookie_consent_version';
export const COOKIE_BANNER_DISMISSED_KEY = 'dunit_cookie_banner_dismissed';

/**
 * CSRF Cookie Configuration
 * Defines secure cookie settings for CSRF protection
 */

export interface CSRFCookieConfig {
    name: string;
    maxAge: number; // in seconds
    httpOnly: boolean;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    path: string;
}

/**
 * Default CSRF cookie configuration
 */
export const DEFAULT_CSRF_COOKIE_CONFIG: CSRFCookieConfig = {
    name: 'csrf_token',
    maxAge: 30 * 60, // 30 minutes
    httpOnly: true,  // Prevent XSS access to cookie
    secure: true,    // HTTPS only in production
    sameSite: 'strict', // CSRF protection
    path: '/'
};

/**
 * Development CSRF cookie configuration (less strict for local development)
 */
export const DEV_CSRF_COOKIE_CONFIG: CSRFCookieConfig = {
    ...DEFAULT_CSRF_COOKIE_CONFIG,
    secure: false  // Allow HTTP in development
};

/**
 * Get appropriate CSRF cookie configuration based on environment
 */
export function getCSRFCookieConfig(): CSRFCookieConfig {
    const isDevelopment = import.meta.env.DEV;
    return isDevelopment ? DEV_CSRF_COOKIE_CONFIG : DEFAULT_CSRF_COOKIE_CONFIG;
}

/**
 * Utility to set a CSRF cookie with proper configuration
 * Note: This function is deprecated and should not be used.
 * CSRF cookies should only be set by the backend for security.
 */
export function setCSRFCookie(_value: string): void {
    console.error('DEPRECATED: setCSRFCookie() should not be used. CSRF cookies must be set by the backend for security.');
    throw new Error('Client-side CSRF cookie setting is not allowed for security reasons');
}

/**
 * Utility to clear CSRF cookie
 */
export function clearCSRFCookie(): void {
    const config = getCSRFCookieConfig();
    document.cookie = `${config.name}=; Max-Age=0; Path=${config.path}; ${config.secure ? 'Secure;' : ''} SameSite=${config.sameSite}`;
}

/**
 * Get CSRF cookie value
 */
export function getCSRFCookie(): string | null {
    const config = getCSRFCookieConfig();
    const cookies = document.cookie.split(';');
    
    for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === config.name) {
            return decodeURIComponent(value);
        }
    }
    
    return null;
}

/**
 * Check if CSRF cookie exists and is valid
 */
export function hasValidCSRFCookie(): boolean {
    const cookieValue = getCSRFCookie();
    return cookieValue !== null && cookieValue.length >= 16; // Minimum token length
} 