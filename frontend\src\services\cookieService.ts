import {   CookiePreferences,   CookieConsent,   CookieUpdateEvent } from '../types/cookies';import {   DEFAULT_COOKIE_PREFERENCES,  COOKIE_CONSENT_KEY,  COOKIE_PREFERENCES_KEY,  COOKIE_CONSENT_VERSION_KEY,  COOKIE_BANNER_DISMISSED_KEY,  COOKIE_CONFIG} from '../config/cookies';import { logger } from '../utils/logger';

class CookieService {
  private static instance: CookieService;
  private consentGiven: boolean = false;
  private currentPreferences: CookiePreferences = DEFAULT_COOKIE_PREFERENCES;

  private constructor() {
    this.initializeFromStorage();
  }

  public static getInstance(): CookieService {
    if (!CookieService.instance) {
      CookieService.instance = new CookieService();
    }
    return CookieService.instance;
  }

  /**
   * Initialize cookie service from localStorage
   */
  private initializeFromStorage(): void {
    try {
      const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
      const storedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY);
      const storedVersion = localStorage.getItem(COOKIE_CONSENT_VERSION_KEY);

      if (storedConsent && storedPreferences && storedVersion) {
        const consent: CookieConsent = JSON.parse(storedConsent);
        this.currentPreferences = JSON.parse(storedPreferences);
        
        // Check if consent version is current
        if (consent.version === COOKIE_CONFIG.consentVersion) {
          this.consentGiven = true;
          this.applyCookiePreferences(this.currentPreferences);
        } else {
          // Version mismatch, reset consent
          this.resetConsent();
        }
      }
        } catch (error) {      logger.error('Error initializing cookie service:', error);      this.resetConsent();    }
  }

  /**
   * Check if consent has been given
   */
  public hasConsent(): boolean {
    return this.consentGiven;
  }

  /**
   * Check if banner should be shown
   */
  public shouldShowBanner(): boolean {
    return !this.hasConsent() && !this.isBannerDismissed();
  }

  /**
   * Check if banner was dismissed without consent
   */
  private isBannerDismissed(): boolean {
    return localStorage.getItem(COOKIE_BANNER_DISMISSED_KEY) === 'true';
  }

  /**
   * Get current cookie preferences
   */
  public getPreferences(): CookiePreferences {
    return { ...this.currentPreferences };
  }

  /**
   * Update cookie preferences and apply them
   */
  public updatePreferences(preferences: CookiePreferences): void {
    this.currentPreferences = { ...preferences };
    this.applyCookiePreferences(preferences);
    this.savePreferences();
    this.dispatchUpdateEvent();
  }

  /**
   * Give consent with current preferences
   */
  public giveConsent(preferences?: CookiePreferences): void {
    if (preferences) {
      this.currentPreferences = { ...preferences };
    }

    const consent: CookieConsent = {
      timestamp: new Date(),
      version: COOKIE_CONFIG.consentVersion,
      preferences: this.currentPreferences,
      consentId: this.generateConsentId(),
      userAgent: navigator.userAgent
    };

    this.consentGiven = true;
    this.saveConsent(consent);
    this.savePreferences();
    this.applyCookiePreferences(this.currentPreferences);
    this.dispatchUpdateEvent();

    // Remove banner dismissed flag if exists
    localStorage.removeItem(COOKIE_BANNER_DISMISSED_KEY);
  }

  /**
   * Reject all non-essential cookies
   */
  public rejectAll(): void {
    const essentialOnlyPreferences: CookiePreferences = {
      essential: true,
      analytics: false,
      performance: false,
      functional: false,
      marketing: false,
      preferences: false
    };

    this.giveConsent(essentialOnlyPreferences);
  }

  /**
   * Accept all cookies
   */
  public acceptAll(): void {
    const allAcceptedPreferences: CookiePreferences = {
      essential: true,
      analytics: true,
      performance: true,
      functional: true,
      marketing: true,
      preferences: true
    };

    this.giveConsent(allAcceptedPreferences);
  }

  /**
   * Dismiss banner without consent (for CCPA compliance)
   */
  public dismissBanner(): void {
    localStorage.setItem(COOKIE_BANNER_DISMISSED_KEY, 'true');
  }

  /**
   * Reset all consent and preferences
   */
  public resetConsent(): void {
    this.consentGiven = false;
    this.currentPreferences = DEFAULT_COOKIE_PREFERENCES;
    
    localStorage.removeItem(COOKIE_CONSENT_KEY);
    localStorage.removeItem(COOKIE_PREFERENCES_KEY);
    localStorage.removeItem(COOKIE_CONSENT_VERSION_KEY);
    localStorage.removeItem(COOKIE_BANNER_DISMISSED_KEY);

    // Clear non-essential cookies
    this.clearNonEssentialCookies();
    this.dispatchUpdateEvent();
  }

  /**
   * Get consent record for audit purposes
   */
  public getConsentRecord(): CookieConsent | null {
    try {
      const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
      return storedConsent ? JSON.parse(storedConsent) : null;
        } catch (error) {      logger.error('Error retrieving consent record:', error);      return null;    }
  }

  /**
   * Apply cookie preferences by enabling/disabling cookies
   */
  private applyCookiePreferences(preferences: CookiePreferences): void {
    // Essential cookies are always enabled
    this.setCookieCategory('essential', true);
    
    // Apply non-essential cookie preferences
    this.setCookieCategory('analytics', preferences.analytics);
    this.setCookieCategory('performance', preferences.performance);
    this.setCookieCategory('functional', preferences.functional);
    this.setCookieCategory('marketing', preferences.marketing);
    this.setCookieCategory('preferences', preferences.preferences);
  }

  /**
   * Enable or disable cookies for a specific category
   */
  private setCookieCategory(category: string, enabled: boolean): void {
    // Dispatch custom event for cookie category changes
    const event = new CustomEvent('cookieCategoryUpdate', {
      detail: { category, enabled, preferences: this.currentPreferences }
    });
    window.dispatchEvent(event);

    if (!enabled) {
      this.clearCookiesByCategory(category);
    }
  }

  /**
   * Clear cookies for a specific category
   */
  private clearCookiesByCategory(category: string): void {
    const cookiesToClear = COOKIE_CONFIG.cookies.filter(
      cookie => cookie.category === category && !cookie.essential
    );

    cookiesToClear.forEach(cookie => {
      this.deleteCookie(cookie.name, cookie.domain);
    });
  }

  /**
   * Clear all non-essential cookies
   */
  private clearNonEssentialCookies(): void {
    const nonEssentialCategories = ['analytics', 'performance', 'functional', 'marketing', 'preferences'];
    nonEssentialCategories.forEach(category => {
      this.clearCookiesByCategory(category);
    });
  }

  /**
   * Clear all cookies, including essential ones, for aggressive cleanup
   */
  public clearAllCookies(): void {
    COOKIE_CONFIG.cookies.forEach(cookie => {
      this.deleteCookie(cookie.name, cookie.domain);
    });
  }

  /**
   * Delete a specific cookie
   */
  private deleteCookie(name: string, domain?: string): void {
    const cookieString = domain 
      ? `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain};`
      : `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    document.cookie = cookieString;
  }

  /**
   * Save consent to localStorage
   */
  private saveConsent(consent: CookieConsent): void {
    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consent));
      localStorage.setItem(COOKIE_CONSENT_VERSION_KEY, consent.version);
        } catch (error) {      logger.error('Error saving consent:', error);    }
  }

  /**
   * Save preferences to localStorage
   */
  private savePreferences(): void {
    try {
      localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(this.currentPreferences));
        } catch (error) {      logger.error('Error saving preferences:', error);    }
  }

  /**
   * Generate unique consent ID
   */
  private generateConsentId(): string {
    return `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Dispatch cookie update event
   */
  private dispatchUpdateEvent(): void {
    const event: CookieUpdateEvent = new CustomEvent('cookiePreferencesUpdated', {
      detail: {
        category: 'all',
        enabled: this.consentGiven,
        preferences: this.currentPreferences
      }
    });
    window.dispatchEvent(event);
  }

  /**
   * Check if a specific category is enabled
   */
  public isCategoryEnabled(category: keyof CookiePreferences): boolean {
    if (!this.consentGiven) {
      return category === 'essential';
    }
    return this.currentPreferences[category];
  }

  /**
   * Get all available cookies for a category
   */
  public getCookiesByCategory(category: string) {
    return COOKIE_CONFIG.cookies.filter(cookie => cookie.category === category);
  }

  /**
   * Manual cookie setting with consent check
   */
  public setCookie(name: string, value: string, category: keyof CookiePreferences, days?: number): boolean {
    if (!this.isCategoryEnabled(category)) {
      logger.warn(`Cookie ${name} not set: category ${category} not consented`);
      return false;
    }

    let expires = '';
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = `; expires=${date.toUTCString()}`;
    }

    // Use appropriate SameSite setting based on cookie purpose
    const sameSite = this.getSameSiteAttribute(name);
    const secure = window.location.protocol === 'https:' ? '; secure' : '';

    document.cookie = `${name}=${value || ''}${expires}; path=/${secure}; samesite=${sameSite}`;
    return true;
  }

  /**
   * Get appropriate SameSite attribute based on cookie name/purpose
   */
  private getSameSiteAttribute(cookieName: string): string {
    // Meta/Facebook related cookies need 'none' for cross-origin authentication
    if (cookieName.includes('fb_') ||
        cookieName.includes('meta_') ||
        cookieName.includes('facebook_')) {
      return 'none';
    }
    if (cookieName === 'dunit_auth_token') {
      return 'lax';
    }

    // CSRF tokens should be strict
    if (cookieName.includes('csrf')) {
      return 'strict';
    }

    // Default to lax for other cookies
    return 'lax';
  }

  /**
   * Get cookie value
   */
  public getCookie(name: string): string | null {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  /**
   * Check if this is the first time the user is logging in (for cookie notice)
   */
  public isFirstTimeLogin(): boolean {
    return localStorage.getItem('first_login_cookie_notice_shown') !== 'true';
  }

  /**
   * Mark that the first-time cookie notice has been shown
   */
  public markFirstTimeNoticeShown(): void {
    localStorage.setItem('first_login_cookie_notice_shown', 'true');
  }

  /**
   * Reset first-time notice (for testing or admin purposes)
   */
  public resetFirstTimeNotice(): void {
    localStorage.removeItem('first_login_cookie_notice_shown');
  }
}

// Export singleton instance
export const cookieService = CookieService.getInstance();
export default cookieService;