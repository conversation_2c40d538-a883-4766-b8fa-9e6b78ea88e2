# MongoDB Collection Analysis Summary

This document summarizes the structure, purpose, and relationships of collections across the D-Unit MongoDB databases (`D-Unit` and `D-Unit-AnalysisGPT`) based on codebase analysis, README files, and sample data queries.

**Last Updated:** June 10, 2025

# Embedding-Based Context Retrieval for Chat Assistant

The D-Unit platform uses a cache-based embedding system to enhance the chat assistant's ability to provide relevant, data-driven responses. This system leverages vector embeddings stored in the D-Unit-AnalysisGPT database, enabling the assistant to retrieve and reference the most pertinent information for each user query.

**Few of the Collections Involved:**
- `product_details_cache_embeddings`: Embeddings for detailed product snapshots per store
- `store_customers_cache_embeddings`: Embeddings for customer data per store
- `active_stores_cache_embeddings`: Embeddings for comprehensive store-level data

**Impact:**
- Chat responses are contextually enriched with real, up-to-date information from the store's own data
- The system performs vector similarity calculations in Python, optimized for efficiency
- All existing chat and Meta-related functionalities remain fully supported and backward compatible

# Unified Context Construction for AI Analysis and Competitor Analysis

As of 2025, D-Unit's analysis and competitor analysis processes use a unified, comprehensive context construction approach. This means that all major cache, meta, and analysis collections in the `D-Unit-AnalysisGPT` database are queried and integrated to build a rich context for each store. This context is used for both global analysis (business, product, customer, shipping, and operational insights) and for enhanced competitor analysis (including web search results when available).

**Key Points:**
- The following collections are always included in the context for each store:
  - `active_stores_cache`, `product_details_cache`, `store_customers_cache`, `platform_reference_data`
  - All Meta-related collections: `meta_pages`, `meta_posts`, `meta_post_metrics`, `meta_comments`, `meta_followers`, `meta_demographics`, `meta_insights`, `meta_ad_accounts`, `meta_ad_campaigns`, `meta_ad_metrics`, `meta_sales_correlation`, `meta_chat_context`
  - `store_chats` (for chat and customer interaction context)
  - The full `global_analysis` document for the store
- The context is constructed as a structured string, summarizing key metrics, product and customer data, social and ad performance, chat history, and AI-generated insights.
- If any collection is missing or empty for a store, a log entry is generated, but the analysis continues using all available data. This ensures robust, non-intrusive operation and clear traceability of data coverage.
- The same context is now used for both global analysis and competitor analysis, ensuring consistency and depth in all AI-driven recommendations and insights.
- This approach replaces earlier, more limited context construction methods and results in significantly improved analysis quality and actionable recommendations.

## [UPDATE 2025-04] Enhanced Store Context for Chat Assistant

The chat assistant now extracts and includes richer context from the following collections:

- **active_stores_cache**: Store context now includes total orders, total revenue, total customers, total products, currency code/symbol, and last order date, in addition to name, business type, and country.
- **store_customers_cache**: Store context now includes total customers, average spend per customer, most frequent shipping method, abandoned cart count, and a sample of up to 5 customers with their preferred shipping/payment methods and country.
- **product_details_cache**: Store context now includes total product count, average store rating, and for each matching product, the primary category and units sold, in addition to name, price, stock, and description.

This enhancement allows the chat assistant to provide more accurate, data-driven responses to queries about store operations, customer behavior, and product performance.

## [UPDATE 2025-04] All Customer Fields in Chat Context
- When a user requests details for a specific customer (e.g., top spender), the chat assistant now injects the **entire customer object** from MongoDB into the LLM context and user-facing reply.
- This includes all fields: customer_id, customer_email, customer_name, total_orders, total_spend, first_order_date, last_order_date, preferred_payment_method, preferred_shipping_method, orders (with product details), country, and any other present fields.
- The context string is pretty-printed as JSON for maximum detail and LLM interpretability.
- Debug logging records the full injected object for traceability.

## Database: D-Unit (Main Database)

*   **Default Name:** `D-Unit` (Configurable via `MONGODB_MAIN_DB` in `backend/config/settings.py`)
*   **Purpose:** Holds users data, migrated directly from the `lanube` MySQL database or kept in sync. Intended to mirror the structure of the original SQL tables.
*   **Note:** This database is not directly accessible via the current MongoDB connection tool, which connects to `D-Unit-AnalysisGPT` by default.

### 1. `store_users`

*   **Purpose:** Stores login information and basic details for users who manage specific stores (store owners/staff), migrated from `lanube.store_users`.
*   **Source Script:** Populated/updated by `backend/scripts/update_store_users.py`.
*   **Key Fields:** `_id` (ObjectID), `id_store` (Number), `id_user` (Number, original SQL PK), `name` (String), `last_name` (String/null), `email` (String), `password` (String, hashed - bcrypt), `active` (boolean), `created_at` (ISODate), `updated_at` (ISODate), `cod` (String/null), `alternate_emails` (Array), `migration_info` (Object: `migrated_at`, `source`), `selected_meta_email` (String).
*   **Relationships:** Linked to `stores` collection via `id_store`.

## Database: D-Unit-AnalysisGPT (Analysis Database)

*   **Default Name:** `D-Unit-AnalysisGPT` (Configurable via `MONGODB_ANALYSIS_DB` in `backend/config/settings.py`)
*   **Purpose:** Holds AI-generated insights, chat data, Meta integration data, and cached analysis results derived from the main data. This appears to be the most actively used database by the backend application logic and update scripts.

# [Schema Deviations: D-Unit-AnalysisGPT Collections]

This section summarizes the current deviations between the actual MongoDB schema (as sampled) and the documentation for D-Unit-AnalysisGPT collections. This is a working list for future documentation updates and schema alignment.

## Verified Deviations (as of June 10, 2025):

- **platform_reference_data**
  - ✅ Verified: Present in the database and matches documented structure with `faqs_active` array and `shipping_methods` array.

- **meta_ad_campaigns**
  - ✅ Verified: Documented fields such as `budget_settings`, `targeting`, `ad_sets`, `performance_metrics`, `conversion_metrics`, `metadata` are not present in sampled documents. Actual schema only includes: `id`, `page_id`, `store_id`, `name`, `status`, `objective`, `start_time`, `end_time`, `budget`, `spend`, `currency`, `platform`, `results`, `cost_per_result`, `updated_at`, `conversion_rate`, `correlation_data`, `correlation_metric`.

- **meta_ad_metrics**
  - Documented nested `metrics` object (with `engagement`, `video_metrics`, `link_metrics`, `demographic_breakdown`) is not present in sampled documents. Sampled docs have only top-level fields: `campaign_id`, `page_id`, `store_id`, `date`, `impressions`, `reach`, `clicks`, `conversions`, `spend`, `ctr`, `cpc`, `cost_per_conversion`, `currency`, `platform`, `updated_at`.

- **meta_insights**
  - Documented fields like `data_points`, `segments` are not present in sampled docs. These may be optional or not used in current data.

- **active_stores_cache**
  - ✅ Verified: Has `currency.native` nested structure instead of flat `currency` object. Also has `currency.usd_rate` and `currency.converted_to_usd` fields. Missing fields: `contact` (as object), `business_details`, `performance_metrics`, `integration_status`, `settings`. The `address` field is a string, not an object.

- **store_activity_metrics**
  - Documented fields like `activity_metrics.daily`, `activity_metrics.weekly`, `activity_metrics.monthly`, `traffic_light`, `efficiency_metrics`, `metadata` are not present in sampled docs. Instead, `activity_metrics` is a flat object with summary fields.

- **meta_pages**
  - ✅ Verified: Documented fields like `status`, `error_log`, `last_sync` are not present in sampled docs. Some fields (e.g., `instagram_business_account`) are present but with fewer subfields than documented.

- **store_customers_cache**
  - ✅ Verified: Sampled documents confirm the presence of deeply nested fields as documented, including `customers` array with nested `orders` and `products`, and all summary/aggregation fields. No major deviations detected; structure matches documentation.

- **product_details_cache**
  - No sample data available (collection may be empty in test environment), but structure is expected to match documentation.

- **Undocumented Collections Found:**
  - `budget_alerts` - Budget monitoring and alerting system
  - `rate_limit_records` - API rate limiting tracking
  - `threat_alerts` - Security threat monitoring
  - `cost_tracking` - Cost management and tracking
  - `search_cache` - Search optimization cache
  - `security_events` - Security event logging

---

### 2. `meta_pages`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores connected Facebook/Instagram page information
*   **Key Fields (Actual Schema):**
  - `_id`: ObjectId
  - `id`: String (Platform page ID)
  - `store_id`: String
  - `name`: String
  - `access_token`: String
  - `category`: String
  - `platform`: String ("facebook" | "instagram")
  - `business_id`: String
  - `instagram_business_account`: Object (Optional)
    - `id`: String
    - `username`: String
    - `name`: String
  - `updated_at`: ISODate
  - `username`: String (Optional, for Instagram pages)
*   **Indexes:** Not specified, likely `id`, `store_id`, `platform`.
*   **Relationships:** Linked to store (`store_id`), `meta_posts`, `meta_ad_campaigns`, etc.

### 3. `meta_posts`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores individual posts from connected Facebook or Instagram pages
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `id`: String (Platform-specific post ID)
  - `page_id`: String
  - `store_id`: String
  - `message`: String
  - `created_time`: ISODate
  - `permalink_url`: String
  - `type`: String ("photo" | "video" | "status" | "link" | "offer")
  - `platform`: String ("facebook" | "instagram")
  - `attachments`: Array of Objects
    - `media_type`: String
    - `url`: String
  - `is_mock`: Boolean
*   **Indexes:** `post_id`, `page_id`, `created_time`
*   **Relationships:** Referenced by `meta_post_metrics`, `meta_comments`

### 4. `meta_post_metrics`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores engagement metrics for Meta posts
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `post_id`: String
  - `page_id`: String
  - `store_id`: String
  - `likes`: Number
  - `comments`: Number
  - `shares`: Number
  - `impressions`: Number
  - `reach`: Number
  - `engagement_rate`: Number
  - `saved`: Number
  - `date`: ISODate
  - `is_mock`: Boolean
*   **Indexes:** `post_id`, `page_id`, `date`
*   **Relationships:** References `meta_posts`

### 5. `meta_comments`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores user comments on Meta posts
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `id`: String (Comment ID)
  - `post_id`: String
  - `page_id`: String
  - `store_id`: String
  - `message`: String
  - `created_time`: ISODate
  - `from`: Object
    - `id`: String
    - `name`: String
  - `sentiment_score`: Number
  - `is_mock`: Boolean
*   **Indexes:** `post_id`, `page_id`, `created_time`
*   **Relationships:** References `meta_posts`

### 6. `meta_followers`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores follower metrics for Meta pages
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `page_id`: String
  - `store_id`: String
  - `total`: Number
  - `date`: ISODate
  - `net_follows`: Array of Objects
    - `date`: ISODate
    - `value`: Number
  - `growth_rate`: Number
  - `is_mock`: Boolean
*   **Indexes:** `page_id`, `store_id`, `date`
*   **Relationships:** Referenced by `meta_chat_context`

### 7. `meta_demographics`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores audience demographic data for Meta pages
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `page_id`: String
  - `store_id`: String
  - `age_ranges`: Array of Objects
    - `range`: String (e.g., "18-24", "25-34", etc.)
    - `percentage`: Number
  - `gender`: Array of Objects
    - `type`: String ("male" | "female")
    - `percentage`: Number
  - `top_locations`: Array of Objects
    - `country`: String
    - `city`: String
    - `percentage`: Number
  - `date`: ISODate
  - `is_mock`: Boolean
*   **Indexes:** `page_id`, `store_id`, `date`
*   **Relationships:** Referenced by `meta_chat_context`

### 8. `meta_ad_campaigns`
*   **Purpose:** Stores detailed advertising campaign data and performance
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`, `backend/scripts/update_meta_contexts_scheduled.py`, and `backend/scripts/update_meta_sales_correlation.py`
*   **Key Fields (Actual Schema):**
  - `_id`: ObjectId
  - `id`: String (Campaign ID)
  - `page_id`: String
  - `store_id`: String
  - `name`: String
  - `status`: String ("ACTIVE" | "PAUSED" | "DELETED" | "ARCHIVED" | "COMPLETED")
  - `objective`: String
  - `start_time`: ISODate
  - `end_time`: ISODate
  - `budget`: Number
  - `spend`: Number
  - `currency`: String
  - `platform`: String
  - `results`: Number
  - `cost_per_result`: Number
  - `updated_at`: ISODate
  - `conversion_rate`: Number
  - `correlation_data`: Array of Objects
    - `date`: String
    - `conversion_rate`: Number
    - `sales_value`: Number
  - `correlation_metric`: Number
*   **Indexes:** `id`, `page_id`, `store_id`, `status`, `start_time`, `end_time`
*   **Relationships:** Linked to `meta_pages` (via `page_id`), store (via `store_id`), `meta_ad_metrics` (via `id`)
  
### 9. `meta_ad_metrics`
*   **Purpose:** Stores granular advertising metrics and performance data
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`, `backend/scripts/update_meta_contexts_scheduled.py`, and `backend/scripts/update_meta_sales_correlation.py`
*   **Key Fields (Actual Schema):**
  - `_id`: ObjectId
  - `campaign_id`: String
  - `page_id`: String
  - `store_id`: String
  - `date`: ISODate
  - `impressions`: Number
  - `reach`: Number
  - `clicks`: Number
  - `conversions`: Number
  - `spend`: Number
  - `ctr`: Number
  - `cpc`: Number
  - `cost_per_conversion`: Number
  - `currency`: String
  - `platform`: String
  - `updated_at`: ISODate
*   **Indexes:** `campaign_id`, `page_id`, `store_id`, `date`
*   **Relationships:** Linked to `meta_ad_campaigns` (via `campaign_id`), `meta_pages` (via `page_id`)

### 10. `meta_insights`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores AI-generated insights from Meta data analysis
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `id`: String (Insight ID)
  - `page_id`: String
  - `store_id`: String
  - `insight_type`: String
  - `title`: String
  - `insight_text`: String
  - `confidence_score`: Number (0-1)
  - `recommendations`: Array of String (or Objects, structure varies)
  - `timestamp`: ISODate
  - `source_data_type`: String
  - `source_data_id`: Array of String
  - `topics`: Array of String
  - `sentiment_score`: Number
  - `is_mock`: Boolean
*   **Indexes:** `store_id`, `page_id`, `insight_type`, `timestamp`
*   **Relationships:** Referenced by `meta_chat_context`

### 11. `active_stores_cache`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Provides a comprehensive, aggregated snapshot of a store's key details, metrics, and configurations
*   **Source Script:** Updated by `backend/scripts/update_active_stores.py`
*   **Key Fields (Actual Schema):**
  - `_id`: String (Store ID)
  - `name`: String
  - `email`: String
  - `url_store`: String
  - `created_at`: ISODate
  - `updated_at`: ISODate
  - `country`: Object
    - `code`: String
    - `name`: String
  - `currency`: Object
    - `native`: Object
      - `code`: String
      - `name`: String
      - `symbol`: String
    - `usd_rate`: Number
    - `converted_to_usd`: Boolean
  - `telephone`: Object
    - `number`: String
    - `country_code`: String | null
    - `formatted`: String
  - `address`: String
  - `business_type`: String
  - `metrics`: Object
    - `total_orders`: Number
    - `total_revenue`: Number
    - `total_revenue_usd`: Number
    - `average_order_value`: Number
    - `average_order_value_usd`: Number
    - `total_products`: Number
    - `total_visits`: Number
    - `total_customers`: Number
    - `active_customers`: Number
    - `customers_with_abandoned_carts`: Number
  - `key_dates`: Object
    - `last_order_date`: ISODate
    - `last_visit_date`: ISODate
    - `last_customer_registration_date`: ISODate
    - `last_product_added_date`: ISODate
  - `social_media`: Object
    - `facebook`: String
    - `instagram`: String
    - `tiktok`: String
    - `x_twitter`: String
    - `youtube`: String
    - `pixel_id`: String
  - `keywords`: String
  - `last_updated_script`: String
*   **Indexes:** `_id`, `name`, `email`
*   **Relationships:** Linked to store (via `_id`). Aggregates data from multiple collections

### 12. `store_activity_metrics`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Tracks store activity levels (logins, visits, orders, revenue) over 30/90/365 days and assigns a "traffic light" status
*   **Source Script:** Updated by `backend/scripts/update_store_activity_metrics.py`
*   **Key Fields:**
  - `_id`: String (Store ID)
  - `name`: String
  - `email`: String
  - `active`: Boolean
  - `activity_metrics`: Object
    - `last_login_date`: ISODate/null
    - `login_count_30d`: Number
    - `login_count_90d`: Number
    - `login_count_365d`: Number
    - `last_visit_date`: ISODate/null
    - `visit_count_30d`: Number
    - `visit_count_90d`: Number
    - `visit_count_365d`: Number
    - `last_order_date`: ISODate/null
    - `order_count_30d`: Number
    - `order_count_90d`: Number
    - `order_count_365d`: Number
    - `revenue_30d`: Number
    - `revenue_90d`: Number
    - `revenue_365d`: Number
    - `avg_order_value`: Number
    - `last_activity_date`: ISODate/null
    - `days_since_last_activity`: Number
  - `comparison_metrics`: Object
    - `avg_order_value`: Number
    - `potential_revenue_loss`: Number
    - `comparison_to_green_stores`: Object
      - `visits_difference_pct`: Number
      - `orders_difference_pct`: Number
      - `revenue_difference_pct`: Number
      - `login_difference_pct`: Number
    - `login_based_revenue_metrics`: Object
      - `login_count_30d`: Number
      - `revenue_per_login`: Number
      - `login_deficit`: Number
      - `login_based_potential_loss`: Number
  - `created_at`: ISODate
  - `updated_at`: ISODate
  - `last_updated_script`: ISODate/String
*   **Indexes:** `_id`, `name`, `active`, `activity_metrics.traffic_light`
*   **Relationships:** Linked to store (via `_id`). Aggregates data from `stores`, `store_orders`, `store_visits`, `login_attempts`. **[2025-05-27] Used by `get_store_analysis` service to provide time-windowed metrics to the main analysis endpoint.**

### 13. `meta_sales_correlation`
*   **Purpose:** Stores analysis of correlation between Meta activities and sales performance
*   **Source Script:** Updated by `backend/scripts/update_meta_sales_correlation.py`
*   **Key Fields:**
  - `_id`: String (store_id)
  - `enhanced_campaigns`: Array
  - `last_updated`: ISODate
  - `sales_data`: Object
    - `summary`: Object
      - `total_days`: Number
      - `total_orders`: Number
      - `total_units`: Number
    - `daily_sales`: Array of Objects
      - `date`: String
      - `orders`: Number
      - `units`: Number
      - `revenue`: Number
    - `product_sales`: Array of Objects
      - `date`: String
      - `product_id`: String
      - `product_name`: String
      - `units_sold`: Number
      - `revenue`: Number

### 14. `store_customers_cache`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores aggregated customer data and metrics per store for quick access and analysis
*   **Source Script:** Updated by `backend/scripts/update_customers_relationships.py`
*   **Key Fields:**
  - `_id`: String (store_id)
  - `abandoned_cart_count`: Number
  - `abandoned_cart_customer_count`: Number
  - `abandoned_cart_total_value`: Number
  - `average_spend_per_customer`: Number
  - `country_distribution`: Object (country: count)
  - `coupon_code_distribution`: Object (coupon_code: count)
  - `customers`: Array of Objects
    - `store_id`: Number
    - `customer_id`: Number
    - `customer_email`: String
    - `customer_ids`: Array of Numbers
    - `customer_name`: String
    - `total_orders`: Number
    - `total_spend`: Number
    - `first_order_date`: ISODate
    - `last_order_date`: ISODate
    - `preferred_payment_method`: String
    - `preferred_shipping_method`: String
    - `orders`: Array of Objects
      - `order_id`: Number
      - `order_date`: ISODate
      - `total`: Number
      - `status`: String
      - `payment_method`: String
      - `shipping_method`: String
      - `shipping_cost`: Number
      - `order_item_total`: Number
      - `products`: Array of Objects
        - `product_id`: Number
        - `product_name`: String
        - `quantity`: Number
        - `price`: Number
      - `coupon_code`: String | null
      - `coupon_amount`: Number
    - `payment_methods`: Object (method: count)
    - `shipping_methods`: Object (method: count)
    - `country`: String
    - `unique_products_count`: Number
  - `last_customer_aggregation_update`: ISODate
  - `last_updated`: ISODate
  - `most_frequent_coupon_code`: Object
    - `code`: String | null
    - `count`: Number
  - `most_frequent_payment_method`: Object
    - `name`: String
    - `count`: Number
  - `most_frequent_shipping_method`: Object
    - `name`: String
    - `count`: Number
  - `only_paid_orders`: Boolean (Optional)
  - `payment_method_distribution`: Object (method: count)
  - `pending_cart_count`: Number
  - `pending_cart_total_value`: Number
  - `shipping_method_distribution`: Object (method: count)
  - `status_distribution`: Object (status: count)
  - `total_customers`: Number
  - `total_store_orders`: Number
*   **Indexes:** `_id`, `"customers.customer_email"`, `"customers.customer_id"`, `last_updated`
*   **Relationships:** Primary document for customer analysis, referenced by `global_analysis`

**[2025-04] UPDATE:**
- Customer ranking queries (best/worst) are now directly supported in chat context construction. When such a query is detected, the relevant customer details are injected and preserved in the LLM prompt, ensuring accurate and actionable responses.
- Debug logging for context traceability is implemented to verify the exact context string sent to the LLM for these queries.

### 15. `product_details_cache`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores cached product information and metrics for quick access
*   **Source Script:** Updated by `backend/scripts/update_product_details.py`
*   **Key Fields:**
  - `_id`: String (store_id)
  - `overall_last_updated`: ISODate
  - `product_count`: Number
  - `products`: Array of Objects
    - `product_id`: String
    - `name`: String
    - `sales_units`: Number
    - `revenue`: Number
    - `current_price`: Number
    - `current_stock`: Number
    - `favorite_count`: Number
    - `related_product_count`: Number
    - `currency_symbol`: String
    - `last_updated`: ISODate
    - `variations`: Array of Objects
      - `variation_id`: String
      - `sku`: String
      - `variation_stock`: Number
      - `variation_price`: Number
      - `variation_offer`: Boolean
      - `variation_offer_price`: Number
      - `product_code`: String
      - `product_description`: String
      - `attributes`: Array of Objects
        - `name`: String
        - `value`: String
    - `current_stock_variations`: Number
    - `categories`: Object
      - `is_featured`: Boolean
      - `is_on_sale`: Boolean
      - `store_categories`: Array of Objects
        - `id`: String
        - `name`: String
      - `subcategories`: Array of Objects
        - `id`: String
        - `name`: String
      - `primary_category`: String | null
    - `sales_details`: Array of Objects
      - `date`: String
      - `units_sold`: Number
      - `revenue`: Number
  - `store_ratings_count`: Number
  - `store_average_rating`: Number
  - `variations_last_updated`: ISODate
  - `categories_last_updated`: ISODate
  - `category_summary`: Object
    - `total_products_with_categories`: Number
    - `unique_categories`: Number
    - `unique_subcategories`: Number
    - `top_categories`: Array of Objects
      - `name`: String
      - `product_count`: Number
    - `top_featured_categories`: Array of Objects
      - `name`: String
      - `featured_count`: Number
    - `top_sale_categories`: Array of Objects
      - `name`: String
      - `sale_count`: Number
    - `category_distribution`: Object
  - `sales_by_date`: Array of Objects
    - `date`: String
    - `total_revenue`: Number
    - `total_units`: Number
    - `order_count`: Number
    - `product_count`: Number
*   **Indexes:** `_id`, `"products.product_id"`, `"products.category"`, `overall_last_updated`
*   **Relationships:** Referenced by `global_analysis`, `meta_sales_correlation`
*   **Note:** This collection may be empty in test environments.

### 16. `global_analysis`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Central repository for comprehensive analysis results per store
*   **Source Script:** Updated by `backend/scripts/update_meta_sales_correlation.py` and `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: String (store_id)
  - `analysis`: Object
    - `summary`: String
    - `metrics`: String
    - `customer_analysis`: String
    - `product_performance`: String
    - `market_position`: String
    - `social_media_strategy`: String
    - `recommendations`: Array of Strings
    - `competitor_analysis`: String
    - `generated_at`: ISODate
    - `shipping_analysis`: Object
      - `analysis_text`: String
      - `recommendations`: Array of String
  - `metadata`: Object
    - `customer_count`: Number
    - `order_count`: Number
    - `product_count`: Number
    - `last_updated`: ISODate
    - `shipping_analysis_updated`: ISODate
    - `competitor_analysis_updated`: ISODate
  - `metrics`: Object
    - `total_orders`: Number
    - `total_revenue`: Number
*   **Indexes:** `_id`, `"metadata.generated_at"`, `last_updated`
*   **Relationships:** References data from multiple collections for comprehensive analysis

### 17. `meta_chat_context`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores consolidated Meta platform data for chat context
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `store_id`: String
  - `pages`: Array of Objects
    - `id`: String
    - `name`: String
    - `platform`: String ("facebook" | "instagram")
    - `url`: String
    - `followers`: Number
    - `engagement_rate`: Number
  - `insights`: Array of Objects
    - `type`: String
    - `text`: String
    - `date`: ISODate
  - `engagement_metrics`: Object
    - `total_likes`: Number
    - `total_comments`: Number
    - `total_shares`: Number
    - `total_engagement`: Number
  - `audience_metrics`: Object
    - `total_followers`: Number
  - `ad_metrics`: Object
    - `metrics`: Array of Objects
      - `campaign_id`: String
      - `page_id`: String
      - `store_id`: String
      - `date`: ISODate
      - `impressions`: Number
      - `reach`: Number
      - `clicks`: Number
      - `conversions`: Number
      - `spend`: Number
      - `ctr`: Number
      - `cpc`: Number
      - `cost_per_conversion`: Number
      - `currency`: String
      - `platform`: String
      - `updated_at`: ISODate
    - `accounts`: Array of Objects
      - `id`: String
      - `account_id`: String
      - `business_id`: String
      - `currency`: String
      - `name`: String
      - `store_id`: String
      - `timezone`: String
      - `updated_at`: ISODate
    - `campaigns`: Array of Objects
      - `id`: String
      - `page_id`: String
      - `store_id`: String
      - `name`: String
      - `status`: String
      - `objective`: String
      - `start_time`: ISODate
      - `end_time`: ISODate
      - `budget`: Number
      - `spend`: Number
      - `currency`: String
      - `platform`: String
      - `results`: Number
      - `cost_per_result`: Number
      - `updated_at`: ISODate
      - `conversion_rate`: Number
      - `correlation_data`: Array of Objects
        - `date`: String
        - `conversion_rate`: Number
        - `sales_value`: Number
      - `correlation_metric`: Number
  - `is_mock_data`: Boolean
  - `last_updated`: ISODate
  - `managed_by_script`: Boolean
  - `_metadata`: Object
    - `created_by`: String
    - `created_at`: ISODate
    - `last_updated_at`: ISODate
    - `version`: String
*   **Indexes:** `store_id`, `last_updated`
*   **Relationships:** Aggregates data from multiple Meta collections
*   **Usage for Chat:** The primary source for Meta context in chat responses (via `get_meta_context_for_store` and `get_minimal_meta_context` in `services/chat.py`). These functions first check this collection for a fresh document (within TTL). If found, the cached context is used directly. If missing or stale, they fall back to dynamically querying individual Meta collections.

### 18. `meta_ad_accounts`
*   **Purpose:** Stores Meta advertising account information for stores
*   **Source Script:** Updated by `backend/scripts/update_meta_context_from_collections.py` and `backend/scripts/update_meta_contexts_scheduled.py`
*   **Key Fields:**
  - `_id`: ObjectId
  - `id`: String (Meta account ID)
  - `account_id`: String
  - `business_id`: String
  - `currency`: String
  - `name`: String
  - `store_id`: String
  - `timezone`: String
  - `updated_at`: ISODate

### 19. `store_chats`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Stores chat conversations between users and the AI assistant
*   **Source Script:** Updated in real-time by chat service
*   **Key Fields:**
  - `_id`: ObjectId
  - `store_id`: String
  - `conversation_id`: String
  - `messages`: Array of Objects
    - `role`: String ("user" | "assistant")
    - `content`: String
    - `created_at`: ISODate
  - `title`: String
  - `user_id`: String
  - `full_title`: String
  - `created_at`: ISODate
  - `updated_at`: ISODate
*   **Indexes:** `store_id`, `conversation_id`, `user_id`, `created_at`
*   **Relationships:** Referenced by `meta_chat_context`

### 20. `platform_reference_data`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Holds reference data like FAQs and available shipping methods for the platform.
*   **Source Script:** Likely maintained manually or via specific admin scripts (not identified in core update scripts).
*   **Key Fields:**
  - `_id`: ObjectId
  - `faqs_active`: Array of Objects
    - `id_faq`: Number
    - `question`: String
    - `answer`: String
    - `slug`: String
    - `active`: Number (1 for active)
    - `created_at`: ISODate
    - `updated_at`: ISODate
  - `shipping_methods`: Array of Objects
    - `id_shipping`: Number
    - `name`: String
    - `price`: Number
    - `active`: Number (1 for active)
    - `created_at`: ISODate
    - `updated_at`: ISODate
    - `deleted_at`: ISODate | null
    - `created_by`: String | null
    - `updated_by`: String | null
    - `deleted_by`: String | null
  - `last_updated`: ISODate
*   **Indexes:** Not specified.
*   **Relationships:** Likely referenced by other services needing FAQ or shipping method lists.

### 21. `budget_alerts`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Monitors and alerts on budget usage and spending thresholds
*   **Key Fields:**
  - `_id`: ObjectId
  - `alert_id`: String (Unique identifier for the alert)
  - `store_id`: String
  - `alert_type`: String (e.g., "startup_test", "daily_budget", "monthly_budget")
  - `current_amount`: Number
  - `threshold`: Number
  - `percentage_used`: Number
  - `timestamp`: ISODate
  - `escalated`: Boolean
*   **Indexes:** Likely `store_id`, `alert_type`, `timestamp`
*   **Relationships:** Linked to stores for budget monitoring

### 22. `rate_limit_records`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Tracks API rate limiting to prevent abuse and ensure fair usage
*   **Key Fields:** Not sampled, but likely includes:
  - `_id`: ObjectId
  - `api_endpoint`: String
  - `store_id`: String | null
  - `user_id`: String | null
  - `timestamp`: ISODate
  - `request_count`: Number
  - `window_start`: ISODate
  - `window_end`: ISODate
*   **Indexes:** Likely `api_endpoint`, `store_id`, `timestamp`
*   **Relationships:** May be linked to stores or users

### 23. `threat_alerts`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Security threat monitoring and alerting system
*   **Key Fields:** Not sampled, but likely includes:
  - `_id`: ObjectId
  - `threat_id`: String
  - `threat_type`: String
  - `severity`: String
  - `store_id`: String | null
  - `description`: String
  - `detected_at`: ISODate
  - `resolved`: Boolean
  - `resolved_at`: ISODate | null
*   **Indexes:** Likely `threat_type`, `severity`, `store_id`, `detected_at`
*   **Relationships:** May be linked to stores or security_events

### 24. `cost_tracking`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Tracks costs associated with various operations and services
*   **Key Fields:** Not sampled, but likely includes:
  - `_id`: ObjectId
  - `store_id`: String
  - `cost_type`: String
  - `amount`: Number
  - `currency`: String
  - `period`: String
  - `timestamp`: ISODate
  - `description`: String
*   **Indexes:** Likely `store_id`, `cost_type`, `timestamp`
*   **Relationships:** Linked to stores for cost management

### 25. `search_cache`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Caches search results for performance optimization
*   **Key Fields:** Not sampled, but likely includes:
  - `_id`: ObjectId
  - `query_hash`: String
  - `query_text`: String
  - `results`: Array
  - `store_id`: String | null
  - `created_at`: ISODate
  - `expires_at`: ISODate
  - `hit_count`: Number
*   **Indexes:** Likely `query_hash`, `store_id`, `expires_at`
*   **Relationships:** May be linked to stores

### 26. `security_events`
*   **Database:** `D-Unit-AnalysisGPT`
*   **Purpose:** Logs security-related events for audit and monitoring
*   **Key Fields:** Not sampled, but likely includes:
  - `_id`: ObjectId
  - `event_type`: String
  - `severity`: String
  - `store_id`: String | null
  - `user_id`: String | null
  - `ip_address`: String
  - `user_agent`: String
  - `description`: String
  - `metadata`: Object
  - `timestamp`: ISODate
*   **Indexes:** Likely `event_type`, `severity`, `store_id`, `timestamp`
*   **Relationships:** May be linked to stores, users, or threat_alerts

### Embeddings System Architecture

#### Vector Database Implementation
- Storage: MongoDB (chosen over dedicated vector DBs like Pinecone for unified management)
- Vector Format: 512-dimensional float16 compressed arrays
- Compression: Custom binary compression reducing storage by ~50%
- Batch Size: 100 documents per embedding generation request
- Rate Limiting: 50 requests/minute to OpenAI's embedding API

#### Source Document Processing
```python
# Example source document structure and processing:
{
    "text_content": "Original text from source collection",
    "metadata": {
        "store_id": "store_123",
        "category": "product_description",
        "language": "en",
        # Additional metadata specific to source
    },
    "timestamps": {
        "created_at": "2024-01-01T00:00:00Z",
        "last_modified": "2024-01-02T00:00:00Z"
    }
}

# Processed into embedding document:
{
    "_id": "ObjectId(...)",
    "e": Binary(...),  # Compressed 512-dim vector
    "m": {
        "type": "collection_name",
        "hash": "content_hash",
        "dimensions": 512,
        "model": "text-embedding-3-small",
        "created_at": "ISODate(...)",
        "active": true,
        "source_id": "original_doc_id",
        "store_id": "store_123",
        "language": "en",
        "content_type": "product_description"
    }
}
```

#### Text Preprocessing Pipeline
1. Content Extraction:
   ```python
   def extract_content(doc):
       """
       Extracts relevant text based on collection type:
       - Products: name + description + attributes
       - Posts: message + caption + hashtags
       - Comments: text + sentiment
       - Metrics: formatted key metrics as text
       """
   ```

2. Language Detection:
   ```python
   def detect_language(text):
       """
       Returns ISO language code
       Affects: chunking, stopwords, embedding context
       """
   ```

3. Text Chunking:
   ```python
   def chunk_text(text, max_tokens=8000):
       """
       Splits text while preserving:
       - Semantic boundaries
       - Key phrases
       - Metadata connections
       Returns: List[Dict[str, Any]]
       """
   ```

#### Embedding Generation Logic
```python
async def generate_embeddings(chunks):
    """
    Key steps:
    1. Validate chunk size (8k token limit)
    2. Add collection-specific context
    3. Call OpenAI with retry logic
    4. Compress vectors (float16)
    5. Store with metadata
    """
```

#### Search Implementation
```python
async def semantic_search(
    query: str,
    collection: str,
    store_id: str,
    limit: int = 5,
    score_threshold: float = 0.7
) -> List[Dict]:
    """
    1. Generate query embedding
    2. Cosine similarity search
    3. Filter by store_id and threshold
    4. Fetch original documents
    5. Return ranked results
    """
```

#### Collection-Specific Processing

1. Product Details (`product_details_cache_embeddings`):
   ```python
   # Embedding content template:
   f"""
   Product: {name}
   Description: {description}
   Category: {category}
   Key Features: {', '.join(features)}
   Specifications: {specs_text}
   Price Range: {price_range}
   """
   ```

2. Meta Posts (`meta_posts_embeddings`):
   ```python
   # Embedding content template:
   f"""
   Post Type: {post_type}
   Message: {message}
   Hashtags: {hashtags}
   Media Type: {media_type}
   Engagement Context: {engagement_metrics}
   Campaign Association: {campaign_id if campaign_id else 'organic'}
   """
   ```

3. Customer Data (`store_customers_cache_embeddings`):
   ```python
   # Embedding content template:
   f"""
   Segment: {segment}
   Purchase History: {purchase_summary}
   Preferences: {preferences}
   Interaction History: {interaction_summary}
   CLV: {customer_lifetime_value}
   """
   ```

#### Update Triggers and Synchronization

1. Real-time Updates:
   - New content creation
   - Significant content edits
   - Status changes
   - Engagement threshold reached

2. Batch Updates:
   - Daily metrics aggregation
   - Weekly trend analysis
   - Monthly performance summaries

3. Force Updates:
   - Model version changes
   - Schema updates
   - Data corrections

#### Error Handling and Monitoring

1. Error Categories:
   ```python
   class EmbeddingError(Exception):
       RATE_LIMIT = "rate_limit"
       TOKEN_LIMIT = "token_limit"
       API_ERROR = "api_error"
       PROCESSING_ERROR = "processing_error"
   ```

2. Monitoring Metrics:
   - Generation latency
   - Success/failure rates
   - Token usage
   - API costs
   - Search performance
   - Cache hit rates

#### Integration Points

1. Chat System:
   ```python
   async def enrich_chat_context(
       message: str,
       store_id: str
   ) -> Dict[str, Any]:
       """
       1. Analyze message intent
       2. Select relevant collections
       3. Perform semantic search
       4. Merge and rank results
       5. Format for LLM context
       """
   ```

2. Analysis Pipeline:
   ```python
   async def gather_analysis_context(
       store_id: str,
       analysis_type: str
   ) -> Dict[str, Any]:
       """
       1. Define context scope
       2. Query relevant embeddings
       3. Aggregate results
       4. Format for analysis
       """
   ```

### Embeddings Collections

All embedding collections follow this standardized structure:
*   **Key Fields:**
  - `_id`: ObjectId
  - `e`: Binary (compressed_embedding_vector)
  - `m`: Object (metadata)
    - `type`: String (collection_name)
    - `hash`: String (content_hash)
    - `dimensions`: Number (default: 512)
    - `model`: String (default: "text-embedding-3-small")
    - `created_at`: ISODate
    - `active`: Boolean
  - `store_id`: String
  - `source_id`: String
  - `last_updated`: ISODate
*   **Note:** Some embedding collections may be empty in test environments or for stores without corresponding source data.

#### Core Cache Embeddings Collections:

1. `product_details_cache_embeddings`
   - Purpose: Vector embeddings for product snapshots
   - Source: `product_details_cache`
   - Updates: When product data changes

2. `store_customers_cache_embeddings`
   - Purpose: Vector embeddings for customer data
   - Source: `store_customers_cache`
   - Updates: When customer profiles change

3. `active_stores_cache_embeddings`
   - Purpose: Vector embeddings for store-level data
   - Source: `active_stores_cache`
   - Updates: Daily with store updates

#### Meta Platform Embeddings
4. `meta_pages_embeddings`
   - Purpose: Embeddings for page content and metrics
   - Source: `meta_pages`
   - Updates: When page details change

5. `meta_posts_embeddings`
   - Purpose: Embeddings for post content
   - Source: `meta_posts`
   - Updates: With new/edited posts

6. `meta_post_metrics_embeddings`
   - Purpose: Embeddings for engagement data
   - Source: `meta_post_metrics`
   - Updates: With metric changes

7. `meta_comments_embeddings`
   - Purpose: Embeddings for comment content
   - Source: `meta_comments`
   - Updates: With new comments

8. `meta_followers_embeddings`
   - Purpose: Embeddings for follower patterns
   - Source: `meta_followers`
   - Updates: Daily

9. `meta_demographics_embeddings`
   - Purpose: Embeddings for audience insights
   - Source: `meta_demographics`
   - Updates: Weekly

10. `meta_insights_embeddings`
    - Purpose: Embeddings for platform insights
    - Source: `meta_insights`
    - Updates: With new insights

11. `meta_ad_accounts_embeddings`
    - Purpose: Embeddings for ad account data
    - Source: `meta_ad_accounts`
    - Updates: With account changes

12. `meta_ad_campaigns_embeddings`
    - Purpose: Embeddings for campaign data
    - Source: `meta_ad_campaigns`
    - Updates: With campaign changes

13. `meta_ad_metrics_embeddings`
    - Purpose: Embeddings for ad performance
    - Source: `meta_ad_metrics`
    - Updates: Daily

14. `meta_sales_correlation_embeddings`
    - Purpose: Embeddings for sales-ad correlations
    - Source: `meta_sales_correlation`
    - Updates: With correlation analysis

15. `meta_chat_context_embeddings`
    - Purpose: Embeddings for chat contexts
    - Source: `meta_chat_context`
    - Updates: With new chat contexts

#### Analysis & Activity Embeddings
16. `store_activity_metrics_embeddings`
    - Purpose: Embeddings for store engagement
    - Source: `store_activity_metrics`
    - Updates: Daily with activity updates

---

## Database Information

- **MongoDB Version:** 8.0.9 (Enterprise)
- **Storage Engine:** WiredTiger
- **Default Connection:** Points to `D-Unit-AnalysisGPT` database
- **Cluster Type:** MongoDB Atlas (based on hostname pattern)
