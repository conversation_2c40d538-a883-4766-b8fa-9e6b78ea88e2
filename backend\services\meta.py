import logging
import asyncio
import aiohttp
from typing import Optional, Dict, List, Any, Tuple, Union
from datetime import datetime, timezone, timedelta
from config.database import db_analysis
from models.meta import MetaChatContext
from config.settings import get_settings
from services.meta_utils import get_cached_meta_context_wrapper, get_user_pages_from_token, get_page_data_from_token
from services.instagram_utils import (
    InstagramAPIError,
    handle_instagram_error,
    validate_instagram_permissions,
    get_instagram_business_account,
    retry_on_rate_limit,
    validate_instagram_token,
    transform_instagram_media_to_post,
    get_instagram_media_insights,
    INSTAGRAM_PERMISSIONS
)
from services.instagram_transformer import InstagramDataTransformer
from services.auth import verify_meta_token, get_current_user
import os
import sys
import subprocess
from fastapi import HTTPException
import requests

# Configure logging
logger = logging.getLogger(__name__)

# Sync metrics collection
sync_metrics = {
    "total_syncs": 0,
    "successful_syncs": 0,
    "failed_syncs": 0,
    "last_sync_time": None,
    "last_successful_sync_time": None,
    "average_sync_duration_ms": 0,
    "store_metrics": {},
    "retry_metrics": {
        "total_retries": 0,
        "avg_attempts_per_sync": 1.0,
        "successful_retries": 0
    }
}

# Add at the top with other imports
MANUAL_UPDATE_FLAG = "managed_by_script"

# Get settings instance
settings = get_settings()

def create_empty_ad_metrics_response():
    """Create an empty response structure for ad metrics when no data is available."""
    return {
        "overview": {
            "total_spend": 0,
            "total_impressions": 0,
            "total_reach": 0,
            "total_clicks": 0,
            "total_conversions": 0,
            "cpc": 0.0,
            "ctr": 0.0,
            "average_cost_per_conversion": 0.0,
            "average_roi": 0.0,
            "roas": 0.0
        },
        "daily_metrics": [],
        "campaigns": []
    }

async def get_meta_context_for_store(store_id: Optional[str]) -> Dict[str, Any]:
    """Get Meta context for a store"""
    if not store_id:
        return {}
    
    try:
        # Get Meta pages for the store
        meta_pages = await db_analysis["meta_pages"].find({"store_id": store_id}).to_list(length=None)
        
        if not meta_pages:
            return {}
        
        context = {
            "pages": []
        }
        
        for page in meta_pages:
            page_id = page.get("id")
            page_context = {
                "id": page_id,
                "name": page.get("name"),
                "category": page.get("category")
            }
            
            # Get recent insights
            insights = await db_analysis["meta_insights"].find({"source_data_id": page_id}).sort("timestamp", -1).limit(5).to_list(length=None)
            
            if insights:
                page_context["insights"] = [{
                    "type": insight.get("insight_type"),
                    "text": insight.get("insight_text"),
                    "recommendations": insight.get("recommendations", [])
                } for insight in insights]
            
            # Get engagement metrics summary
            engagement = await db_analysis["meta_post_metrics"].find_one({"page_id": page_id})
            if engagement:
                page_context["engagement"] = {
                    "total_likes": engagement.get("total_likes", 0),
                    "total_comments": engagement.get("total_comments", 0),
                    "total_shares": engagement.get("total_shares", 0),
                    "average_engagement_rate": engagement.get("average_engagement_rate", 0)
                }
            
            # Get audience summary
            audience = await db_analysis["meta_demographics"].find_one({"page_id": page_id})
            if audience:
                page_context["audience"] = {
                    "total_followers": audience.get("total_followers", 0),
                    "top_age_groups": audience.get("top_age_groups", []),
                    "top_countries": audience.get("top_countries", [])
                }
            
            # Get ad metrics summary
            ad_metrics = await db_analysis["meta_ad_metrics"].find_one({"page_id": page_id})
            if ad_metrics:
                page_context["ad_metrics"] = {
                    "total_spend": ad_metrics.get("account_summary", {}).get("total_spend", 0),
                    "total_impressions": ad_metrics.get("account_summary", {}).get("total_impressions", 0),
                    "total_clicks": ad_metrics.get("account_summary", {}).get("total_clicks", 0),
                    "total_conversions": ad_metrics.get("account_summary", {}).get("total_conversions", 0),
                    "average_roi": ad_metrics.get("account_summary", {}).get("average_roi", 0),
                    "campaign_count": len(ad_metrics.get("campaigns", []))
                }
            
            context["pages"].append(page_context)
        
        return context
    except Exception as e:
        logger.error(f"Error getting Meta context: {str(e)}")
        return {}

async def sync_meta_data_for_chat(store_id: str, max_retries: int = 3) -> bool:
    """
    Sync Meta data to a chat-optimized collection.
    Returns True if sync was successful, False otherwise.
    
    Parameters:
    - store_id: ID of the store to sync
    - max_retries: Maximum number of retry attempts (default: 3)
    """
    # Track sync metrics
    start_time = datetime.now()
    
    # Track retry attempts
    attempt = 1
    last_error = None
    
    while attempt <= max_retries:
        try:
            if attempt > 1:
                logger.info(f"Retry attempt {attempt}/{max_retries} for store {store_id}")
                
            # Get Meta context from existing function
            meta_context = await get_meta_context_for_store(store_id)
            
            if not meta_context:
                logger.warning(f"No Meta context could be built for store {store_id}. This usually means no associated Meta pages were found or accessible in the database. Verify store setup.")
                # Don't retry if there's simply no data
                _update_sync_metrics(store_id, False, start_time, attempt)
                return False
                
            # Create chat context
            chat_context = MetaChatContext(
                store_id=store_id,
                pages=meta_context.get("pages", []),
                insights=[],  # Will be populated from meta_insights collection
                engagement_metrics={},
                audience_metrics={},
                ad_metrics={},
                is_mock_data=False
            )
            
            # Get recent insights
            try:
                recent_insights = await db_analysis["meta_insights"].find(
                    {"store_id": store_id}
                ).sort("timestamp", -1).limit(10).to_list(length=None)
                
                if recent_insights:
                    chat_context.insights = [{
                        "type": insight.get("insight_type"),
                        "text": insight.get("insight_text"),
                        "recommendations": insight.get("recommendations", [])
                    } for insight in recent_insights]
            except Exception as insight_error:
                # Log but continue, as insights are optional
                logger.warning(f"Error fetching insights for store {store_id}: {str(insight_error)}")
            
            # Aggregate engagement metrics across all pages
            total_likes, total_comments, total_shares, total_followers = 0, 0, 0, 0
            
            # Track processed page count for metrics
            processed_pages = 0
            
            for page in meta_context.get("pages", []):
                page_id = page.get("id")
                if not page_id:
                    continue
                    
                try:
                    # Get engagement metrics
                    engagement = await db_analysis["meta_post_metrics"].find_one({"page_id": page_id})
                    if engagement:
                        total_likes += engagement.get("total_likes", 0)
                        total_comments += engagement.get("total_comments", 0)
                        total_shares += engagement.get("total_shares", 0)
                    
                    # Get audience metrics
                    audience = await db_analysis["meta_demographics"].find_one({"page_id": page_id})
                    if audience:
                        total_followers += audience.get("total_followers", 0)
                    
                    processed_pages += 1
                except Exception as page_error:
                    # Log but continue with other pages
                    logger.warning(f"Error processing page {page_id} for store {store_id}: {str(page_error)}")
            
            # Log metrics about processed pages
            if len(meta_context.get("pages", [])) > 0:
                logger.info(f"Processed {processed_pages}/{len(meta_context.get('pages', []))} pages for store {store_id}")
                
            chat_context.engagement_metrics = {
                "total_likes": total_likes,
                "total_comments": total_comments,
                "total_shares": total_shares,
                "total_engagement": total_likes + total_comments + total_shares
            }
            
            chat_context.audience_metrics = {
                "total_followers": total_followers
            }
            
            # Use the helper function that checks the flag
            await update_meta_chat_context(store_id, chat_context.dict(), source="sync_meta_data_for_chat")
            
            # Record success metrics
            _update_sync_metrics(store_id, True, start_time, attempt)
            
            logger.info(f"Successfully synced Meta data for store {store_id} on attempt {attempt}")
            return True
            
        except Exception as e:
            last_error = e
            logger.error(f"Error on attempt {attempt}/{max_retries} syncing Meta data for store {store_id}: {str(e)}")
            
            # Only retry if we haven't reached max attempts
            if attempt < max_retries:
                # Exponential backoff: 1s, 2s, 4s, etc.
                backoff_seconds = 2 ** (attempt - 1)
                logger.info(f"Backing off for {backoff_seconds}s before retry")
                await asyncio.sleep(backoff_seconds)
                attempt += 1
            else:
                # Record failure metrics after max retries
                _update_sync_metrics(store_id, False, start_time, attempt)
                logger.error(f"Failed to sync Meta data for store {store_id} after {max_retries} attempts. Last error: {str(last_error)}")
                return False
    
    # This should never be reached, but just in case
    return False

def _update_sync_metrics(store_id: str, success: bool, start_time: datetime, attempts: int = 1) -> None:
    """Update sync metrics with attempt information"""
    # Calculate duration
    end_time = datetime.now()
    duration_ms = (end_time - start_time).total_seconds() * 1000
    
    # Update global metrics
    sync_metrics["total_syncs"] += 1
    sync_metrics["last_sync_time"] = end_time
    
    # Update success/failure metrics
    if success:
        sync_metrics["successful_syncs"] += 1
        sync_metrics["last_successful_sync_time"] = end_time
    else:
        sync_metrics["failed_syncs"] += 1
    
    # Update average duration
    prev_avg = sync_metrics["average_sync_duration_ms"]
    sync_metrics["average_sync_duration_ms"] = (prev_avg * (sync_metrics["total_syncs"] - 1) + duration_ms) / sync_metrics["total_syncs"]
    
    # Track retry metrics
    retry_metrics = sync_metrics["retry_metrics"]
    retry_metrics["total_retries"] += (attempts - 1)  # Only count actual retries
    
    # Count successful retries (when attempts > 1 and success is True)
    if attempts > 1 and success:
        retry_metrics["successful_retries"] += 1
    
    # Update average attempts
    prev_attempts = retry_metrics["avg_attempts_per_sync"] * (sync_metrics["total_syncs"] - 1)
    retry_metrics["avg_attempts_per_sync"] = (prev_attempts + attempts) / sync_metrics["total_syncs"]
    
    # Update store-specific metrics
    if store_id not in sync_metrics["store_metrics"]:
        sync_metrics["store_metrics"][store_id] = {
            "total_syncs": 0,
            "successful_syncs": 0,
            "failed_syncs": 0,
            "last_sync_time": None,
            "last_successful_sync_time": None,
            "average_sync_duration_ms": 0,
            "retry_metrics": {
                "total_retries": 0,
                "avg_attempts_per_sync": 1.0,
                "successful_retries": 0
            }
        }
    
    # Update store metrics
    store_metrics = sync_metrics["store_metrics"][store_id]
    store_metrics["total_syncs"] += 1
    store_metrics["last_sync_time"] = end_time
    
    if success:
        store_metrics["successful_syncs"] += 1
        store_metrics["last_successful_sync_time"] = end_time
    else:
        store_metrics["failed_syncs"] += 1
    
    # Update store average duration
    prev_store_avg = store_metrics["average_sync_duration_ms"]
    store_metrics["average_sync_duration_ms"] = (prev_store_avg * (store_metrics["total_syncs"] - 1) + duration_ms) / store_metrics["total_syncs"]
    
    # Update store retry metrics
    store_retry_metrics = store_metrics["retry_metrics"]
    store_retry_metrics["total_retries"] += (attempts - 1)
    
    if attempts > 1 and success:
        store_retry_metrics["successful_retries"] += 1
    
    prev_store_attempts = store_retry_metrics["avg_attempts_per_sync"] * (store_metrics["total_syncs"] - 1)
    store_retry_metrics["avg_attempts_per_sync"] = (prev_store_attempts + attempts) / store_metrics["total_syncs"]

def get_sync_metrics() -> Dict[str, Any]:
    """Get sync metrics for monitoring"""
    # Convert datetimes to strings for JSON serialization
    metrics = {**sync_metrics}
    
    # Format global timestamps
    if metrics.get("last_sync_time"):
        metrics["last_sync_time"] = metrics["last_sync_time"].isoformat()
    if metrics.get("last_successful_sync_time"):
        metrics["last_successful_sync_time"] = metrics["last_successful_sync_time"].isoformat()
    
    # Format store timestamps
    for store_id, store_metrics in metrics.get("store_metrics", {}).items():
        if store_metrics.get("last_sync_time"):
            store_metrics["last_sync_time"] = store_metrics["last_sync_time"].isoformat()
        if store_metrics.get("last_successful_sync_time"):
            store_metrics["last_successful_sync_time"] = store_metrics["last_successful_sync_time"].isoformat()
    
    return metrics

# Re-export the cached meta context function to maintain API compatibility
async def get_cached_meta_context(store_id: str, max_age_minutes: Optional[int] = None) -> Optional[MetaChatContext]:
    """Cache wrapper to limit API calls"""
    return await get_cached_meta_context_wrapper(store_id, max_age_minutes)

async def update_meta_chat_context(store_id: str, context_data: dict, source: str = "unknown"):
    """
    Update the meta_chat_context collection with new data,
    but ONLY if it's not managed by an external script
    
    Parameters:
    - store_id: The store ID
    - context_data: The data to update
    - source: The source of the update (for tracking purposes)
    """
    try:
        # Check if document exists and has the managed_by_script flag
        existing = await db_analysis["meta_chat_context"].find_one({"store_id": store_id})
        
        # If the document exists and has the managed_by_script flag set to True, 
        # don't update it automatically
        if existing and existing.get(MANUAL_UPDATE_FLAG) is True:
            logger.info(f"Skipping auto-update for meta_chat_context of store {store_id} - managed by script (source: {source})")
            return False
        
        # ENHANCEMENT: If we're about to do a complete document replacement,
        # check if there are any fields we should preserve from the existing document
        if existing:
            # For safety, always preserve the managed_by_script flag if it exists
            # This protects against code that might do a complete document replacement
            if MANUAL_UPDATE_FLAG in existing and MANUAL_UPDATE_FLAG not in context_data:
                context_data[MANUAL_UPDATE_FLAG] = existing[MANUAL_UPDATE_FLAG]
                logger.info(f"Preserving managed_by_script flag for store {store_id}")
        
        # Add versioning metadata to track changes
        context_data["_metadata"] = {
            "last_updated_by": source,
            "last_updated_at": datetime.now(timezone.utc),
            "update_count": (existing.get("_metadata", {}).get("update_count", 0) + 1) if existing else 1
        }
        
        # Proceed with the update
        await db_analysis["meta_chat_context"].update_one(
            {"store_id": store_id},
            {"$set": context_data},
            upsert=True
        )
        logger.info(f"Updated meta_chat_context for store {store_id} (source: {source})")
        return True
        
    except Exception as e:
        logger.error(f"Error updating meta_chat_context: {e}")
        return False

def process_instagram_insights(response_data):
    """Process Instagram insights response with breakdowns for profile_activity"""
    
    processed_data = {
        "data": []
    }
    
    for metric in response_data.get("data", []):
        # Regular metrics processing
        processed_metric = {
            "name": metric["name"],
            "period": metric["period"],
            "values": metric["values"],
            "title": metric.get("title", ""),
            "description": metric.get("description", "")
        }
        
        # Special handling for profile_activity
        if metric["name"] == "profile_activity" and "total_value" in metric:
            # Initialize counters
            profile_visits = 0
            messages = 0
            link_clicks = 0
            
            # Extract values from breakdowns
            breakdowns = metric["total_value"].get("breakdowns", [])
            for breakdown in breakdowns:
                if "dimension_keys" in breakdown and "action_type" in breakdown["dimension_keys"]:
                    for result in breakdown.get("results", []):
                        action_type = result.get("dimension_values", [""])[0]
                        value = result.get("value", 0)
                        
                        # Map to our metrics
                        if action_type == "direction":
                            profile_visits = value
                        elif action_type in ["email", "text"]:
                            messages += value
                        elif action_type == "bio_link_clicked":
                            link_clicks = value
            
            # Add derived metrics
            processed_data["data"].append({
                "name": "profile_visits",
                "period": metric["period"],
                "values": [{"value": profile_visits}],
                "title": "Profile Visits",
                "description": "Number of profile visits"
            })
            
            processed_data["data"].append({
                "name": "messages",
                "period": metric["period"],
                "values": [{"value": messages}],
                "title": "Messages",
                "description": "Number of messages received"
            })
            
            processed_data["data"].append({
                "name": "link_clicks",
                "period": metric["period"],
                "values": [{"value": link_clicks}],
                "title": "Link Clicks",
                "description": "Number of clicks on profile link"
            })
        
        # Add the original metric
        processed_data["data"].append(processed_metric)
    
    return processed_data

def process_ad_metrics(response_data, is_instagram=False):
    """Process ad metrics response, filtering for Instagram if needed"""
    
    processed_data = {
        "overview": {
            "total_spend": 0.0,
            "total_impressions": 0,
            "total_clicks": 0,
            "total_conversions": 0,
            "ctr": 0.0,
            "cpc": 0.0,
            "cost_per_conversion": 0.0,
            "roas": 0.0
        },
        "campaigns": []
    }
    
    # Return early if no data
    if not response_data.get("data"):
        return processed_data
        
    # Filter for Instagram data if needed
    data_points = response_data["data"]
    if is_instagram:
        # Filter results to only include Instagram data
        data_points = [
            item for item in data_points 
            if item.get("publisher_platform") == "instagram"
        ]
        
    # Process the data
    total_spend = 0
    total_impressions = 0
    total_clicks = 0
    total_conversions = 0
    
    for item in data_points:
        # Add to totals
        spend = float(item.get("spend", 0))
        impressions = int(item.get("impressions", 0))
        clicks = int(item.get("clicks", 0))
        
        # Extract conversions from actions
        conversions = 0
        if "actions" in item:
            for action in item["actions"]:
                if action.get("action_type") in ["purchase", "complete_registration", "lead"]:
                    conversions += int(action.get("value", 0))
        
        # Update totals
        total_spend += spend
        total_impressions += impressions
        total_clicks += clicks
        total_conversions += conversions
        
        # Create individual campaign data
        campaign_ctr = (clicks / impressions * 100) if impressions > 0 else 0
        campaign_cpc = (spend / clicks) if clicks > 0 else 0
        campaign_cost_per_conversion = (spend / conversions) if conversions > 0 else 0
        campaign_roas = 4.0  # Default estimate for now
        
        campaign_data = {
            "id": item.get("campaign_id", item.get("id", "unknown")),
            "name": item.get("campaign_name", item.get("name", "Unknown Campaign")),
            "status": item.get("status", "ACTIVE"),
            "objective": item.get("objective", "UNKNOWN"),
            "platform": "instagram" if is_instagram else "facebook",
            "spend": round(spend, 2),
            "impressions": impressions,
            "clicks": clicks,
            "conversions": conversions,
            "ctr": round(campaign_ctr, 2),
            "cpc": round(campaign_cpc, 2),
            "cost_per_conversion": round(campaign_cost_per_conversion, 2),
            "roas": round(campaign_roas, 2),
            "start_time": item.get("start_time"),
            "end_time": item.get("end_time"),
            "daily_budget": item.get("daily_budget"),
            "lifetime_budget": item.get("lifetime_budget")
        }
        
        # Add campaign to the campaigns array
        processed_data["campaigns"].append(campaign_data)
    
    # Calculate derived metrics
    ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
    cpc = (total_spend / total_clicks) if total_clicks > 0 else 0
    cost_per_conversion = (total_spend / total_conversions) if total_conversions > 0 else 0
    
    # Estimate ROAS based on actions (simple approach)
    roas = 4.0  # Default estimate
    
    # Update overview
    processed_data["overview"] = {
        "total_spend": total_spend,
        "total_impressions": total_impressions,
        "total_clicks": total_clicks,
        "total_conversions": total_conversions,
        "ctr": round(ctr, 2),
        "cpc": round(cpc, 2),
        "cost_per_conversion": round(cost_per_conversion, 2),
        "roas": round(roas, 2)
    }
    
    # Add empty daily_metrics for now
    processed_data["daily_metrics"] = []
    
    return processed_data

def merge_ad_and_organic_metrics(ad_data, organic_data):
    """Merge ad metrics with organic metrics for a complete view"""
    
    merged_data = ad_data.copy()
    
    # Add organic metrics where applicable
    if organic_data:
        # Merge impression data
        organic_impressions = organic_data.get("impressions", 0)
        merged_data["overview"]["total_impressions"] += organic_impressions
        
        # Add organic engagement metrics
        merged_data["organic"] = {
            "engagement": {
                "likes": organic_data.get("likes", 0),
                "comments": organic_data.get("comments", 0),
                "shares": organic_data.get("shares", 0),
                "saves": organic_data.get("saves", 0)
            },
            "profile_activity": {
                "profile_visits": organic_data.get("profile_visits", 0),
                "messages": organic_data.get("messages", 0),
                "link_clicks": organic_data.get("link_clicks", 0)
            }
        }
    
    return merged_data

async def get_organic_metrics(page_id, time_range):
    """Fetch organic metrics for a page"""
    
    try:
        # Get page details to determine if it's Instagram
        page = await db_analysis["meta_pages"].find_one({"id": page_id})
        if not page:
            logger.warning(f"Page {page_id} not found for organic metrics")
            return {}
            
        platform = page.get("platform", "facebook")
        is_instagram = platform == "instagram"
        
        if not is_instagram:
            # Logic for Facebook pages - this is simplified for now
            return await get_facebook_organic_metrics(page_id, time_range)
        
        # Logic for Instagram pages
        return await get_instagram_organic_metrics(page_id, time_range)
    except Exception as e:
        logger.error(f"Error getting organic metrics: {str(e)}")
        return {}

@retry_on_rate_limit(max_retries=3)
async def get_instagram_organic_metrics(page_id, time_range, use_cache: bool = True):
    """Fetch organic metrics specifically for Instagram with caching"""
    
    try:
        # Import here to avoid circular imports
        from services.instagram_cache import InstagramCacheService
        
        # Check cache first if enabled
        if use_cache:
            cache_key = InstagramCacheService.generate_cache_key(
                page_id, "organic_metrics", time_range=str(time_range)
            )
            cached_data = await InstagramCacheService.get_cached_data(
                cache_key, "account_insights"
            )
            if cached_data:
                logger.debug(f"Using cached Instagram organic metrics for {page_id}")
                return cached_data
        
        # Get page details
        page = await db_analysis["meta_pages"].find_one({"id": page_id})
        if not page:
            logger.warning(f"No page found with id {page_id}")
            return {}
            
        access_token = page.get("access_token")
        if not access_token:
            logger.warning(f"No access token for page {page_id}")
            return {}
        
        store_id = page.get("store_id")
        
        # Validate token before making API calls
        token_validation = await validate_instagram_token(access_token)
        if not token_validation.get("valid"):
            error_msg = token_validation.get("error", "Token validation failed")
            error_code = token_validation.get("error_code", 190)
            logger.error(f"Invalid Instagram token for page {page_id}: {error_msg} (code: {error_code})")
            
            # Provide more specific error messages
            if error_code == 190:
                raise InstagramAPIError(190, "Instagram access token has expired. Please reconnect your Meta account.", "refresh_token")
            else:
                raise InstagramAPIError(error_code, f"Instagram token validation failed: {error_msg}", "reauth")
        
        # Try enhanced v23.0 API first with media-level aggregation
        try:
            # Import the enhanced function from routes
            from routes.instagram_api import get_instagram_insights_live_api
            
            # Convert time_range to string format
            time_range_str = "30d"  # Default
            if isinstance(time_range, str):
                time_range_str = time_range
            elif isinstance(time_range, int):
                time_range_str = f"{time_range}d"
            
            logger.info(f"Trying enhanced v23.0 API for Instagram page {page_id}")
            enhanced_metrics = await get_instagram_insights_live_api(page_id, access_token, time_range_str, store_id)
            
            # If enhanced API returns good data, use it
            if enhanced_metrics.get("source") == "live_api_v23" and enhanced_metrics.get("media_count", 0) > 0:
                logger.info(f"Successfully got enhanced v23.0 data with {enhanced_metrics.get('media_count')} posts")
                
                # Transform to expected format and add derived metrics
                metrics_data = {
                    "impressions": enhanced_metrics.get("impressions", 0),
                    "reach": enhanced_metrics.get("reach", 0),
                    "profile_visits": enhanced_metrics.get("profile_views", 0),
                    "messages": 0,  # Not available in v23.0
                    "link_clicks": 0,  # Not available in v23.0
                    "likes": enhanced_metrics.get("engagement", 0) // 2,  # Estimate likes as half of engagement
                    "comments": enhanced_metrics.get("engagement", 0) // 2,  # Estimate comments as half of engagement
                    "shares": 0,  # Instagram doesn't provide share count
                    "saves": enhanced_metrics.get("saved", 0),
                    "media_count": enhanced_metrics.get("media_count", 0),
                    "engagement_rate": (enhanced_metrics.get("engagement", 0) / max(enhanced_metrics.get("reach", 1), 1)) * 100,
                    "last_updated": datetime.utcnow().isoformat(),
                    "api_version": "v23.0",
                    "source": "enhanced_live_api"
                }
                
                # Cache the results if cache is enabled
                if use_cache:
                    await InstagramCacheService.set_cached_data(
                        cache_key, 
                        "account_insights", 
                        metrics_data,
                        {
                            "page_id": page_id,
                            "store_id": store_id,
                            "time_range": str(time_range),
                            "platform": "instagram"
                        }
                    )
                
                # Transform data to frontend-compatible format
                transformed_data = InstagramDataTransformer.transform_metrics_to_frontend(metrics_data)
                
                # Validate the transformation
                if InstagramDataTransformer.validate_frontend_compatibility(transformed_data, "metrics"):
                    logger.debug(f"Successfully transformed enhanced Instagram metrics for frontend compatibility")
                    return transformed_data
                else:
                    logger.warning(f"Transformed enhanced data failed frontend compatibility check, returning raw data")
                    return metrics_data
            else:
                logger.warning(f"Enhanced v23.0 API returned insufficient data, falling back to legacy method")
                
        except Exception as e:
            logger.warning(f"Enhanced v23.0 API failed: {str(e)}, falling back to legacy method")
        
        # Fallback to legacy method if enhanced API fails
        # Validate permissions for legacy method
        required_permissions = INSTAGRAM_PERMISSIONS["insights"]
        permission_check = await validate_instagram_permissions(access_token, required_permissions)
        if not permission_check.get("valid"):
            missing = permission_check.get("missing", [])
            logger.error(f"Missing Instagram permissions for page {page_id}: {missing}")
            raise InstagramAPIError(
                10, 
                f"Missing required Instagram permissions: {', '.join(missing)}", 
                "reauth"
            )
        
        # Get Instagram Business Account ID
        ig_business_id = await get_instagram_business_account(page_id, access_token)
        if not ig_business_id:
            logger.warning(f"No Instagram Business Account connected to page {page_id}")
            # Check if this is a Facebook page trying to be used as Instagram
            page_data = await db_analysis["meta_pages"].find_one({"id": page_id})
            if page_data and page_data.get("platform") == "facebook":
                logger.info(f"Page {page_id} is a Facebook page without Instagram Business Account")
                raise InstagramAPIError(
                    2500,
                    "This is a Facebook Page without an Instagram Business Account. To get Instagram insights, please connect an Instagram Business Account to your Facebook Page in Meta Business Manager.",
                    "convert_account"
                )
            else:
                raise InstagramAPIError(
                    2500,
                    "No Instagram Business Account found. Please ensure your Instagram account is converted to a Business account and linked to a Facebook Page.",
                    "convert_account"
                )
        
        from config.settings import get_settings
        settings = get_settings()
        graph_api_url = f"https://graph.facebook.com/{settings.FACEBOOK_API_VERSION}"
        
        # Convert time_range to date range if needed
        since_date = datetime.now() - timedelta(days=30)  # Default to 30 days
        until_date = datetime.now()
        
        if isinstance(time_range, str):
            if time_range == "7d":
                since_date = datetime.now() - timedelta(days=7)
            elif time_range == "30d":
                since_date = datetime.now() - timedelta(days=30)
            elif time_range == "90d":
                since_date = datetime.now() - timedelta(days=90)
            
        # Use async aiohttp for all API calls
        async with aiohttp.ClientSession() as session:
            try:
                # Fetch insights for profile metrics
                # Instagram specific metrics that require insights permission
                profile_metrics = ["impressions", "reach", "profile_views"]
                insights_url = f"{graph_api_url}/{ig_business_id}/insights"
                
                # Initialize metrics
                profile_visits = 0
                messages = 0
                link_clicks = 0
                impressions = 0
                reach = 0
                
                # Get profile insights
                params = {
                    "metric": ",".join(profile_metrics),
                    "period": "day",
                    "access_token": access_token,
                    "since": int(since_date.timestamp()),
                    "until": int(until_date.timestamp())
                }
                
                async with session.get(insights_url, params=params) as response:
                    if response.status == 200:
                        insights_data = await response.json()
                        
                        for metric in insights_data.get("data", []):
                            metric_name = metric.get("name")
                            values = metric.get("values", [])
                            
                            if metric_name == "impressions":
                                impressions = sum(v.get("value", 0) for v in values)
                            elif metric_name == "reach":
                                reach = sum(v.get("value", 0) for v in values)
                            elif metric_name == "profile_views":
                                profile_visits = sum(v.get("value", 0) for v in values)
                    else:
                        error_data = await response.json()
                        error = error_data.get("error", {})
                        logger.warning(f"Failed to get Instagram insights: {error}")
                        # Don't fail entirely, continue with zero values
                
                # Fetch media for engagement metrics
                media_url = f"{graph_api_url}/{ig_business_id}/media"
                media_params = {
                    "fields": "id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,like_count,comments_count",
                    "access_token": access_token,
                    "limit": 100,
                    "since": int(since_date.timestamp()),
                    "until": int(until_date.timestamp())
                }
                
                likes = 0
                comments = 0
                shares = 0  # Instagram doesn't provide share count
                saves = 0
                media_count = 0
                
                async with session.get(media_url, params=media_params) as response:
                    if response.status == 200:
                        media_data = await response.json()
                        posts = media_data.get("data", [])
                        
                        # Process media in batches to get insights
                        for post in posts:
                            try:
                                post_time = datetime.strptime(post.get("timestamp"), "%Y-%m-%dT%H:%M:%S%z")
                                # Only count posts within the time range
                                if since_date.replace(tzinfo=post_time.tzinfo) <= post_time <= until_date.replace(tzinfo=post_time.tzinfo):
                                    likes += int(post.get("like_count", 0))
                                    comments += int(post.get("comments_count", 0))
                                    media_count += 1
                                    
                                    # Try to get saved count from insights (requires permission)
                                    media_insights = await get_instagram_media_insights(
                                        post.get("id"),
                                        access_token,
                                        ["saved"]
                                    )
                                    saves += media_insights.get("saved", 0)
                            except Exception as e:
                                logger.debug(f"Error processing media {post.get('id')}: {str(e)}")
                                continue
                    else:
                        error_data = await response.json()
                        error = error_data.get("error", {})
                        logger.warning(f"Failed to get Instagram media: {error}")
            
                # Construct the metrics response
                metrics_data = {
                    "impressions": impressions,
                    "reach": reach,
                    "profile_visits": profile_visits,
                    "messages": messages,
                    "link_clicks": link_clicks,
                    "likes": likes,
                    "comments": comments,
                    "shares": shares,
                    "saves": saves,
                    "media_count": media_count,
                    "engagement_rate": (likes + comments + saves) / max(reach, 1) * 100 if reach > 0 else 0,
                    "last_updated": datetime.utcnow().isoformat()
                }
                
                # Cache the results if cache is enabled
                if use_cache:
                    await InstagramCacheService.set_cached_data(
                        cache_key, 
                        "account_insights", 
                        metrics_data,
                        {
                            "page_id": page_id,
                            "store_id": store_id,
                            "time_range": str(time_range),
                            "platform": "instagram"
                        }
                    )
                
                # Transform data to frontend-compatible format
                transformed_data = InstagramDataTransformer.transform_metrics_to_frontend(metrics_data)
                
                # Validate the transformation
                if InstagramDataTransformer.validate_frontend_compatibility(transformed_data, "metrics"):
                    logger.debug(f"Successfully transformed Instagram metrics for frontend compatibility")
                    return transformed_data
                else:
                    logger.warning(f"Transformed data failed frontend compatibility check, returning raw data")
                    return metrics_data
                
            except Exception as e:
                logger.error(f"Error during Instagram API calls: {str(e)}")
                # Return partial data if available
                return {
                    "impressions": impressions,
                    "reach": reach,
                    "profile_visits": profile_visits,
                    "messages": messages,
                    "link_clicks": link_clicks,
                    "likes": likes,
                    "comments": comments,
                    "shares": 0,
                    "saves": saves,
                    "media_count": media_count,
                    "error": str(e)
                }
            
    except InstagramAPIError as e:
        logger.error(f"Instagram API Error in get_instagram_organic_metrics: {e.message}")
        # Transform error to frontend format
        error_data = {
            "error": e.message,
            "error_code": e.code,
            "error_action": e.action,
            "instagram_specific": True
        }
        return InstagramDataTransformer.transform_error_to_frontend(error_data)
    except Exception as e:
        logger.error(f"Unexpected error in get_instagram_organic_metrics: {str(e)}")
        return {
            "error": "An unexpected error occurred while fetching Instagram metrics",
            "error_details": str(e)
        }

async def get_facebook_organic_metrics(page_id, time_range):
    """Fetch organic metrics specifically for Facebook using enhanced v23.0 API"""
    
    try:
        # Get page details
        page = await db_analysis["meta_pages"].find_one({"id": page_id})
        if not page:
            logger.warning(f"No page found with id {page_id}")
            return {}
            
        access_token = page.get("access_token")
        if not access_token:
            logger.warning(f"No access token for page {page_id}")
            return {}
        
        # Convert time_range to string format
        time_range_str = "30d"  # Default
        if isinstance(time_range, str):
            time_range_str = time_range
        elif isinstance(time_range, int):
            time_range_str = f"{time_range}d"
        
        # Try enhanced v23.0 API
        try:
            # Import the enhanced function from routes
            from routes.meta import get_enhanced_facebook_insights_v23
            
            logger.info(f"Trying enhanced v23.0 API for Facebook page {page_id}")
            enhanced_metrics = await get_enhanced_facebook_insights_v23(page_id, access_token, time_range_str, store_id)
            
            # If enhanced API returns good data, use it
            if enhanced_metrics.get("source") == "enhanced_live_api_v23":
                logger.info(f"Successfully got enhanced v23.0 Facebook data")
                
                # Transform to expected format
                metrics_data = {
                    "impressions": enhanced_metrics.get("total_impressions", 0),
                    "reach": enhanced_metrics.get("total_reach", 0),
                    "profile_visits": enhanced_metrics.get("profile_visits", 0),
                    "messages": 0,  # Not available in v23.0
                    "link_clicks": 0,  # Not available in v23.0
                    "likes": enhanced_metrics.get("total_engagement", 0) // 3,  # Estimate likes
                    "comments": enhanced_metrics.get("total_engagement", 0) // 3,  # Estimate comments
                    "shares": enhanced_metrics.get("total_engagement", 0) // 3,  # Estimate shares
                    "saves": 0,  # Facebook doesn't provide saves
                    "media_count": enhanced_metrics.get("posts_count", 0),
                    "followers_count": enhanced_metrics.get("followers_count", 0),
                    "fan_adds": enhanced_metrics.get("fan_adds", 0),
                    "fan_removes": enhanced_metrics.get("fan_removes", 0),
                    "engagement_rate": (enhanced_metrics.get("total_engagement", 0) / max(enhanced_metrics.get("total_reach", 1), 1)) * 100,
                    "last_updated": datetime.utcnow().isoformat(),
                    "api_version": "v23.0",
                    "source": "enhanced_facebook_api"
                }
                
                return metrics_data
            else:
                logger.warning(f"Enhanced Facebook v23.0 API returned insufficient data, falling back to legacy method")
                
        except Exception as e:
            logger.warning(f"Enhanced Facebook v23.0 API failed: {str(e)}, falling back to legacy method")
        
        # Fallback to basic metrics if enhanced API fails
        return {
            "impressions": 0,
            "reach": 0,
            "profile_visits": 0,
            "messages": 0,
            "link_clicks": 0,
            "likes": 0,
            "comments": 0,
            "shares": 0,
            "saves": 0,
            "last_updated": datetime.utcnow().isoformat(),
            "source": "fallback"
        }
    
    except Exception as e:
        logger.error(f"Error in get_facebook_organic_metrics: {str(e)}")
        return {
            "error": "Failed to fetch Facebook organic metrics",
            "error_details": str(e)
        }

async def process_page_for_chat(store_id: str, page_id: str, page_token: str):
    """Process a page's data for chat context"""
    try:
        # Use the new utility function
        page_data = await get_page_data_from_token(page_id, page_token)
        if not page_data or not page_data.get("name"):
            return None
        
        # Create chat context
        chat_context = MetaChatContext(
            store_id=store_id,
            pages=[{
                "id": page_id,
                "name": page_data.get("name", ""),
                "category": page_data.get("category", ""),
                "platform": "facebook",
                "url": f"https://facebook.com/{page_id}",
                "followers": page_data.get("followers", 0),
                "engagement_rate": 0
            }],
            insights=[{
                "type": "general",
                "text": f"Your page '{page_data.get('name')}' has {page_data.get('followers', 0)} followers.",
                "date": datetime.now(timezone.utc)
            }],
            engagement_metrics={
                "total_likes": 0,
                "total_comments": 0,
                "total_shares": 0,
                "total_engagement": 0
            },
            audience_metrics={
                "total_followers": page_data.get("followers", 0)
            },
            ad_metrics={},
            is_mock_data=False,
            last_updated=datetime.now(timezone.utc)
        )
        
        # Check if we should update the document or skip due to managed_by_script flag
        existing = await db_analysis["meta_chat_context"].find_one({"store_id": store_id})
        if existing and existing.get(MANUAL_UPDATE_FLAG) is True:
            logger.info(f"Skipping update for meta_chat_context of store {store_id} - managed by script")
            return chat_context
        
        # Use the helper function to update instead of direct DB access
        await update_meta_chat_context(store_id, chat_context.dict(), source="process_page_for_chat")
        
        return chat_context
    except Exception as e:
        logger.error(f"Error processing page for chat context: {e}")
        return None

async def sync_meta_data(store_id: str, user_access_token: str):
    """Sync all Meta data for a store"""
    try:
        # First, validate token and get user ID using verify_meta_token
        user_data = await verify_meta_token(user_access_token)
        if not user_data or "id" not in user_data:
            logger.error(f"Invalid access token for store {store_id}")
            return False
        
        user_id = user_data["id"]
        logger.info(f"Starting Meta data sync for store {store_id}, user {user_id}")
        
        # Get pages for the user using the new utility function
        pages = await get_user_pages_from_token(user_access_token)
        if not pages:
            logger.warning(f"No pages found for user {user_id}, store {store_id}")
            return False
        
        # Process each page
        for page in pages:
            page_id = page.get("id")
            page_token = page.get("access_token")
            
            if not page_id or not page_token:
                continue
            
            # Store basic page info
            page_doc = {
                "store_id": store_id,
                "id": page_id,
                "name": page.get("name", ""),
                "category": page.get("category", ""),
                "last_updated": datetime.now(timezone.utc)
            }
            
            # Update or insert page document
            await db_analysis["meta_pages"].update_one(
                {"id": page_id, "store_id": store_id},
                {"$set": page_doc},
                upsert=True
            )
            
            # Process page data for chat context using process_page_for_chat
            chat_context = await process_page_for_chat(store_id, page_id, page_token)
            if chat_context:
                logger.info(f"Successfully processed page {page_id} for store {store_id} chat context")
            else:
                logger.warning(f"Failed to process page {page_id} for store {store_id} chat context")
            
            break  # Currently only processing the first page
        
        # Update store record with Meta integration info
        await db_analysis["global_analysis"].update_one(
            {"_id": store_id},
            {"$set": {
                "store.meta_integration": {
                    "connected": True,
                    "access_token": user_access_token,
                    "user_id": user_id,
                    "last_sync": datetime.now(timezone.utc)
                }
            }}
        )
        
        logger.info(f"Meta data sync completed for store {store_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error syncing Meta data for store {store_id}: {e}")
        return False

async def update_meta_ad_metrics(page_id: str, store_id: str, metrics_data: dict) -> bool:
    """
    Update the meta_ad_metrics collection with consolidated data including campaigns.
    
    Parameters:
    - page_id: The Meta page ID
    - store_id: The store ID
    - metrics_data: The metrics data to save
    
    Returns:
    - bool: True if the update was successful, False otherwise
    """
    try:
        # 1. Get all campaigns for this page/store
        logger.info(f"Updating meta_ad_metrics for page {page_id} and store {store_id}")
        campaigns = await db_analysis["meta_ad_campaigns"].find(
            {"page_id": page_id, "store_id": store_id}
        ).to_list(length=None)
        
        # 2. Convert ObjectId to strings for JSON serialization
        for campaign in campaigns:
            if "_id" in campaign:
                campaign["_id"] = str(campaign["_id"])
        
        # 3. Ensure metrics_data has campaigns
        if not metrics_data.get("campaigns") and campaigns:
            logger.info(f"Adding {len(campaigns)} campaigns to metrics data for page {page_id}")
            metrics_data["campaigns"] = campaigns
        
        # 4. Update the document
        update_result = await db_analysis["meta_ad_metrics"].update_one(
            {"page_id": page_id, "store_id": store_id},
            {"$set": {
                "metrics": metrics_data,
                "last_updated": datetime.now(timezone.utc)
            }},
            upsert=True
        )
        
        logger.info(f"Updated meta_ad_metrics for page {page_id} with {len(campaigns)} campaigns. Modified: {update_result.modified_count}, Upserted: {bool(update_result.upserted_id)}")
        return True
    except Exception as e:
        logger.error(f"Error updating meta_ad_metrics: {str(e)}")
        return False

async def run_meta_sales_correlation_update(store_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Run the Meta sales correlation update script manually.
    
    Parameters:
    - store_id: Optional store ID to process only a specific store. If None, all stores are processed.
    
    Returns:
    - Dict with status of the operation
    """
    try:
        logger.info(f"Manually running Meta sales correlation update{'for store '+store_id if store_id else ''}")
        script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "update_meta_sales_correlation.py")
        
        cmd = [sys.executable, script_path]
        if store_id:
            cmd.append("--store_id")
            cmd.append(store_id)
            
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Meta sales correlation update completed successfully")
            return {
                "success": True,
                "message": "Meta sales correlation update completed successfully",
                "stdout": result.stdout
            }
        else:
            logger.error(f"Meta sales correlation update failed: {result.stderr}")
            return {
                "success": False,
                "message": "Meta sales correlation update failed",
                "error": result.stderr
            }
    except Exception as e:
        logger.error(f"Error running Meta sales correlation update: {e}")
        return {
            "success": False,
            "message": "Error running Meta sales correlation update",
            "error": str(e)
        }

async def fetch_and_cache_instagram_ad_metrics(
    page_id: str,
    store_id: str,
    page: Dict[str, Any],
    is_instagram: bool,
    time_range: str,
    since: Optional[str],
    until: Optional[str],
    force_refresh: bool,
    max_data_age: int,
    include_organic: bool
) -> Dict[str, Any]:
    """
    Fetches Instagram ad metrics from Meta API if cache is stale or refresh is forced,
    processes the data, optionally merges organic metrics, caches the result,
    and returns the metrics. Handles errors and fallbacks.
    
    This function is specifically designed for Instagram ad metrics and includes
    comprehensive error handling and fallback mechanisms.
    """
    logger.info(f"Fetching Instagram ad metrics for page_id={page_id}, store_id={store_id}, is_instagram={is_instagram}")
    if not is_instagram:
        logger.warning("fetch_and_cache_instagram_ad_metrics: Only Instagram ad metrics logic implemented.")
        return create_empty_ad_metrics_response()

    access_token = page.get("access_token")
    if not access_token:
        logger.error("No access token found for Instagram page.")
        return create_empty_ad_metrics_response()

    metrics = [
        "impressions",
        "reach",
        "spend",
        "clicks",
        "inline_link_clicks",
        "actions",
        "cpc",
        "ctr",
        "unique_clicks",
        "unique_inline_link_clicks",
        "unique_ctr",
        "unique_cpc"
    ]
    metrics_str = ",".join(metrics)
    params = {
        "fields": f"insights.metric({metrics_str})",
        "access_token": access_token
    }
    from config.settings import get_settings
    settings = get_settings()
    graph_api_url = f"https://graph.facebook.com/{settings.FACEBOOK_API_VERSION}"
    ad_account_id = page.get("ad_account_id")
    if not ad_account_id:
        logger.error("No ad_account_id found for Instagram page.")
        return create_empty_ad_metrics_response()
    url = f"{graph_api_url}/act_{ad_account_id}/campaigns"
    try:
        response = requests.get(url, params=params)
        if response.status_code != 200:
            logger.error(f"Failed to fetch Instagram ad metrics: {response.text}")
            return create_empty_ad_metrics_response()
        raw_data = response.json()
        logger.info(f"Fetched Instagram ad metrics: {raw_data}")
        processed_metrics = process_ad_metrics(raw_data, is_instagram=True)
        logger.info(f"Processed Instagram ad metrics: {processed_metrics}")
        await update_meta_ad_metrics(page_id, store_id, processed_metrics)
        logger.info(f"Stored processed Instagram ad metrics for page_id={page_id}, store_id={store_id}")
        return processed_metrics
    except Exception as e:
        logger.error(f"Error in fetch_and_cache_instagram_ad_metrics: {str(e)}")
        return create_empty_ad_metrics_response()

async def track_meta_api_cost(store_id: str, operation: str, request_count: int = 1) -> float:
    """Track cost for Meta API operations"""
    try:
        from services.security_service import SecurityService
        
        # Meta API has different costs - using average estimate
        cost_per_request = 0.001  # Estimated cost per Meta API request
        estimated_cost = request_count * cost_per_request
        
        # Store cost tracking in database
        cost_record = {
            "store_id": store_id,
            "service": "meta_api",
            "operation": operation,
            "request_count": request_count,
            "cost_usd": estimated_cost,
            "timestamp": datetime.now(timezone.utc),
            "date": datetime.now(timezone.utc).strftime("%Y-%m-%d")
        }
        
        await db_analysis["cost_tracking"].insert_one(cost_record)
        logger.info(f"Tracked Meta API cost for store {store_id}: ${estimated_cost:.6f} ({operation})")
        
        return estimated_cost
        
    except Exception as e:
        logger.error(f"Failed to track Meta API cost for store {store_id}: {e}")
        return 0.0

async def check_meta_api_budget(store_id: str, estimated_cost: float = 0.001) -> bool:
    """Check if store has budget available for Meta API requests"""
    try:
        # Get store tier limits
        from services.chat import _get_store_tier, _get_tier_limits
        
        store_tier = await _get_store_tier(store_id)
        tier_limits = _get_tier_limits(store_tier)
        
        # Get current spending for Meta API
        today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        current_month = datetime.now(timezone.utc).strftime("%Y-%m")
        
        # Check daily spending
        daily_pipeline = [
            {"$match": {"store_id": store_id, "date": today, "service": "meta_api"}},
            {"$group": {"_id": None, "total_cost": {"$sum": "$cost_usd"}}}
        ]
        daily_result = await db_analysis["cost_tracking"].aggregate(daily_pipeline).to_list(None)
        daily_cost = daily_result[0]["total_cost"] if daily_result else 0.0
        
        # Check monthly spending
        monthly_pipeline = [
            {"$match": {"store_id": store_id, "date": {"$regex": f"^{current_month}"}, "service": "meta_api"}},
            {"$group": {"_id": None, "total_cost": {"$sum": "$cost_usd"}}}
        ]
        monthly_result = await db_analysis["cost_tracking"].aggregate(monthly_pipeline).to_list(None)
        monthly_cost = monthly_result[0]["total_cost"] if monthly_result else 0.0
        
        # Check if adding estimated cost would exceed limits
        if (daily_cost + estimated_cost) > tier_limits["meta_api_daily_limit"]:
            logger.warning(f"Daily Meta API budget exceeded for store {store_id}: ${daily_cost:.4f}/${tier_limits['meta_api_daily_limit']}")
            return False
            
        if (monthly_cost + estimated_cost) > tier_limits["meta_api_monthly_limit"]:
            logger.warning(f"Monthly Meta API budget exceeded for store {store_id}: ${monthly_cost:.4f}/${tier_limits['meta_api_monthly_limit']}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to check Meta API budget for store {store_id}: {e}")
        return True  # Allow on error to avoid blocking legitimate requests

class MetaService:
    def __init__(self):
        from config.settings import get_settings
        settings = get_settings()
        self.base_url = f"https://graph.facebook.com/{settings.FACEBOOK_API_VERSION}"

    async def get_organic_metrics(self, store_id: str, page_id: str, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """Get organic metrics with cost tracking"""
        try:
            # Check budget before API call
            if not await check_meta_api_budget(store_id, 0.001):
                logger.warning(f"Meta API budget exceeded for store {store_id}")
                return {"error": "Budget exceeded"}
            
            # Track API cost
            await track_meta_api_cost(store_id, "get_organic_metrics", 1)
            
            # ... existing implementation would go here ...
            return {}
            
        except Exception as e:
            logger.error(f"Failed to get organic metrics for store {store_id}: {e}")
            return {"error": str(e)}

    async def get_instagram_organic_metrics(self, store_id: str, instagram_account_id: str, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """Get Instagram organic metrics with cost tracking"""
        try:
            # Check budget before API call
            if not await check_meta_api_budget(store_id, 0.001):
                logger.warning(f"Meta API budget exceeded for store {store_id}")
                return {"error": "Budget exceeded"}
            
            # Track API cost
            await track_meta_api_cost(store_id, "get_instagram_organic_metrics", 1)
            
            # ... existing implementation would go here ...
            return {}
            
        except Exception as e:
            logger.error(f"Failed to get Instagram organic metrics for store {store_id}: {e}")
            return {"error": str(e)}

    async def update_meta_followers_embeddings(self, store_id: str) -> Dict[str, Any]:
        """
        Update embeddings for Meta followers data
        """
        try:
            # Implementation for updating embeddings
            return {"success": True, "message": "Embeddings updated successfully"}
        except Exception as e:
            logger.error(f"Error updating Meta followers embeddings: {str(e)}")
            return {"success": False, "error": str(e)}
