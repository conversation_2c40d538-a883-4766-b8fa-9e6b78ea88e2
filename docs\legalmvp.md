# Documentación Legal MVP de D-Unit

## 1. Descripción General de Recolección y Procesamiento de Datos

### A. Datos del Usuario
- Información Básica:
  - Correo electrónico
  - Nombre
  - Contraseña (encriptada)
  - ID de Tienda
  - Estado activo
  - Marcas de tiempo de creación/actualización

### B. Datos de la Tienda
- Información de la Empresa:
  - Nombre de la empresa
  - Tipo de negocio
  - Información de contacto (correo electrónico, teléfono)
  - Dirección
  - Nombre del contacto
  - DNI/Identificación fiscal
  - Sitio web

### C. Datos del Cliente
- Información Personal:
  - Nombre y apellido
  - Correo electrónico
  - Ubicación (país, provincia, ciudad)
  - Estado y actividad
  - Marcas de tiempo de creación/actualización

### D. Datos de Pedidos
- Información de Transacción:
  - ID del pedido
  - Marcas de tiempo de creación/actualización
  - Estado
  - Detalles del cliente
  - Detalles de la tienda
  - Método de pago
  - Método de envío
  - Productos pedidos
  - Cantidades y precios

### E. Datos de Productos
- Información del Producto:
  - Nombre
  - Descripción
  - Precio
  - Categorías/Subcategorías
  - Variaciones
  - Niveles de stock
  - Asociación con tienda

## 2. Stack Tecnológico y Herramientas

### Tecnologías Principales
1. **Bases de Datos**
   - MongoDB Atlas (Base de datos principal)
   - MySQL (Datos fuente)
   - Base de datos vectorial (para embeddings)

2. **IA y Aprendizaje Automático**
   - API de OpenAI
   - Embeddings vectoriales para búsqueda semántica
   - RAG (Generación Aumentada por Recuperación)

3. **Tecnologías Backend**
   - Python 3.11.9
   - FastAPI
   - Autenticación JWT
   - Python-dotenv

4. **Tecnologías Frontend**
   - React 18+
   - TypeScript 4.9+
   - Vite 4.0+
   - Material-UI
   - Next UI
   - Tailwind CSS

5. **Desarrollo y Despliegue**
   - Git (Control de versiones)
   - AWS EC2 (Despliegue en la nube)
   - AWS CloudFront
   - AWS S3

## 3. Arquitectura de Base de Datos

D-Unit emplea una arquitectura MongoDB de cuatro niveles:

### 1. D-Unit (Datos Crudos)
- Propósito: Almacenamiento de datos crudos de 38 tablas originales
- Contenido:
  - Estructura original de datos
  - Fuente de verdad
  - Datos históricos completos
  - Almacenamiento de datos sensibles (DNI, dirección completa, números telefónicos, etc.)
- Colecciones:
  - store_users
  - stores
  - products
  - orders
  - customers
  - Y 90 tablas originales más
- Seguridad:
  - Almacenamiento encriptado de datos sensibles
  - Controles estrictos de acceso
  - Registro completo de auditoría

### 3. D-Unit-AnalysisGPT
- Propósito: Análisis e insights generados por IA
- Contenido:
  - Resultados de análisis de mercado
  - Análisis de competencia
  - Inteligencia de negocios
  - Métricas de rendimiento
- Características:
  - Análisis en tiempo real
  - Tendencias históricas
  - Insights predictivos
- Aislamiento de Datos:
  - Acceso específico por tienda
  - Particionamiento estricto de datos
  - Aislamiento de análisis individual por tienda
  - Sin exposición de datos entre tiendas
- Medidas de Seguridad:
  - Autenticación a nivel de tienda
  - Verificación de acceso a datos
  - Limitación del alcance de análisis

### 4. D-Unit-Embeddings
- Propósito: Almacenamiento de embeddings vectoriales
- Contenido:
  - Vectores de búsqueda semántica
  - Embeddings de documentos
  - Matrices de similitud
- Características:
  - Búsqueda rápida por similitud
  - Coincidencia semántica
  - Soporte RAG

## 4. Medidas de Seguridad

### A. Autenticación y Autorización
1. **Autenticación de Usuario**
   - Autenticación basada en JWT
   - Encriptación de contraseñas
   - Mecanismo de renovación de tokens
   - Gestión de sesiones

2. **Control de Acceso**
   - Control de acceso basado en roles (RBAC)
   - Aislamiento de datos por tienda
   - Protección de endpoints API
   - Gestión de permisos

### B. Protección de Datos
1. **Encriptación de Datos**
   - Encriptación de datos sensibles
   - Manejo seguro de conexiones
   - Configuración de variables de entorno
   - Gestión de claves API

2. **Seguridad de Infraestructura**
   - Características de seguridad de MongoDB Atlas
   - Grupos de seguridad AWS
   - Lista blanca de IPs
   - Auditorías regulares de seguridad

### C. Medidas de Cumplimiento
1. **Privacidad de Datos**
   - Protección de datos personales
   - Registro de acceso a datos
   - Gestión de consentimiento de usuarios
   - Políticas de retención de datos

2. **Registro de Auditoría**
   - Registro de actividades
   - Seguimiento de cambios
   - Monitoreo de accesos
   - Registro de incidentes de seguridad

## 5. Sistemas Automatizados

### A. Servicios de Actualización
1. **Servicio Windows**
   - Ciclo de actualización de 12 horas
   - Sincronización automatizada de datos
   - Rotación de logs
   - Manejo de errores

2. **Servicio Linux/Nube**
   - Programación basada en Cron
   - Monitoreo continuo
   - Recuperación automática
   - Optimización de rendimiento

### B. Sistemas de Monitoreo
1. **Verificación de Datos**
   - Verificaciones de consistencia
   - Validación de datos
   - Detección de errores
   - Reportes automatizados

2. **Monitoreo de Rendimiento**
   - Seguimiento de uso de recursos
   - Monitoreo de tiempos de respuesta
   - Seguimiento de tasa de errores
   - Verificaciones de salud del sistema

## 6. Flujo de Procesamiento de Datos

### A. Importación Inicial de Datos
1. Conversión de SQL a JSON
2. Inicialización de base de datos
3. Importación de datos crudos
4. Verificación de datos

### B. Enriquecimiento de Datos
1. Procesamiento y optimización de datos
2. Exportación de datos mejorados
3. Importación de datos enriquecidos
4. Verificación y validación

### C. Generación de Análisis
1. Creación de embeddings vectoriales
2. Procesamiento de análisis de mercado
3. Análisis de competencia
4. Generación de inteligencia de negocios

### D. Actualizaciones Continuas
1. Sincronización regular de datos
2. Actualizaciones automatizadas de análisis
3. Monitoreo en tiempo real
4. Optimización de rendimiento

## 7. Gestión del Ciclo de Vida de la Información

### A. Fase de Recolección de Datos
1. **Registro de Usuario y Creación de Tienda**
   - Recolección de información básica del usuario
   - Recopilación de información de configuración de tienda
   - Datos de configuración inicial
   - Creación de credenciales de autenticación

2. **Datos de Operación de Tienda**
   - Información de catálogo de productos
   - Datos de gestión de inventario
   - Detalles de precios y variaciones
   - Asignaciones de categorías y subcategorías

3. **Adquisición de Datos de Clientes**
   - Información de registro de clientes
   - Iniciación de historial de compras
   - Recolección de detalles de contacto
   - Iniciación de seguimiento de preferencias

4. **Datos de Transacciones**
   - Captura de información de pedidos
   - Datos de procesamiento de pagos
   - Recolección de detalles de envío
   - Registro de historial de compras

### B. Fase de Procesamiento y Almacenamiento de Datos
1. **Procesamiento Inicial**
   - Validación y limpieza de datos
   - Estandarización de formato
   - Mapeo de relaciones
   - Verificación de integridad
   - Identificación y manejo de datos sensibles

2. **Transformación de Datos**
   - Conversión de datos crudos a JSON
   - Mapeo de estructura de base de datos
   - Normalización de campos
   - Establecimiento de relaciones
   - Filtrado y eliminación de datos sensibles
   - Transformaciones que preservan la privacidad

3. **Almacenamiento Multi-nivel**
   - Almacenamiento de datos crudos (D-Unit)
     * Datos completos con información sensible
     * Almacenamiento encriptado
     * Acceso limitado
   - Creación de datos enriquecidos (D-Unit-Enriched)
     * Datos sanitizados sin información sensible
     * Perfiles de clientes anonimizados
     * Solo datos necesarios para el negocio
   - Generación de análisis (D-Unit-AnalysisGPT)
     * Análisis específico por tienda
     * Acceso aislado por tienda
     * Sin exposición de datos entre tiendas
   - Almacenamiento de embeddings (D-Unit-Embeddings)
     * Representaciones vectoriales que preservan la privacidad
     * Sin inclusión de datos sensibles

4. **Controles de Acceso a Datos**
   - Autenticación específica por tienda
   - Gestión de acceso basada en roles
   - Aplicación de aislamiento de datos
   - Registro y monitoreo de accesos

### C. Fase de Uso y Procesamiento de Datos
1. **Uso Operacional**
   - Procesamiento de transacciones diarias
   - Gestión de inventario
   - Manejo de interacciones con clientes
   - Cumplimiento de pedidos
   - Aislamiento de datos por tienda

2. **Procesamiento Analítico**
   - Generación de inteligencia de negocios
   - Creación de análisis de mercado
   - Cálculo de métricas de rendimiento
   - Análisis de tendencias
   - Analíticas aisladas por tienda

3. **IA y Aprendizaje Automático**
   - Generación de embeddings vectoriales
   - Análisis de similitud
   - Procesamiento del motor de recomendaciones
   - Analítica predictiva
   - Procesamiento que preserva la privacidad
   - Medidas de prevención de alucinaciones

4. **Reportes y Visualización**
   - Preparación de datos para dashboard
   - Generación de reportes
   - Visualización de analíticas
   - Visualización de métricas de rendimiento

### D. Fase de Mantenimiento de Datos
1. **Actualizaciones Regulares**
   - Sincronización programada de datos
   - Procesamiento de actualizaciones en tiempo real
   - Operaciones de actualización de caché
   - Mantenimiento de índices

2. **Verificación de Datos**
   - Verificaciones de integridad
   - Verificación de consistencia
   - Detección y corrección de errores
   - Aseguramiento de calidad

3. **Optimización de Rendimiento**
   - Optimización de consultas
   - Optimización de almacenamiento
   - Análisis de patrones de acceso
   - Ajuste de asignación de recursos

4. **Mantenimiento de Seguridad**
   - Actualizaciones de control de acceso
   - Aplicación de parches de seguridad
   - Rotación de claves de encriptación
   - Ejecución de auditorías de seguridad

### E. Retención y Archivado de Datos
1. **Gestión de Datos Activos**
   - Mantenimiento de datos actuales
   - Preservación de historial reciente
   - Gestión de datos de usuarios activos
   - Manejo de datos operacionales

2. **Proceso de Archivado**
   - Identificación de datos históricos
   - Creación de archivos
   - Implementación de compresión
   - Transferencia a almacenamiento frío

3. **Políticas de Retención**
   - Cumplimiento de requisitos legales
   - Evaluación de necesidades del negocio
   - Determinación de duración de almacenamiento
   - Establecimiento de protocolos de acceso

4. **Gestión de Archivos**
   - Organización de datos archivados
   - Implementación de control de acceso
   - Gestión de procesos de recuperación
   - Optimización de almacenamiento

### F. Eliminación y Remoción de Datos
1. **Disparadores de Eliminación de Datos**
   - Solicitudes de eliminación de cuenta de usuario
   - Vencimiento del período de retención
   - Cumplimiento de requisitos legales
   - Finalización del propósito comercial

2. **Proceso de Eliminación**
   - Identificación de datos
   - Limpieza de relaciones
   - Manejo de eliminación en cascada
   - Procedimientos de verificación

3. **Eliminación Permanente**
   - Ejecución de eliminación segura
   - Limpieza de almacenamiento físico
   - Eliminación de respaldos
   - Limpieza de archivos

4. **Verificación de Eliminación**
   - Confirmación de eliminación
   - Creación de registro de auditoría
   - Verificación de cumplimiento
   - Actualización de documentación

### G. Seguridad Entre Fases
1. **Control de Acceso**
   - Aplicación de autenticación
   - Gestión de autorización
   - Verificación de permisos
   - Monitoreo de actividades

2. **Protección de Datos**
   - Implementación de encriptación
   - Aplicación de protocolos de seguridad
   - Protección de privacidad
   - Mantenimiento de integridad de datos

3. **Auditoría y Cumplimiento**
   - Registro de actividades
   - Monitoreo de cumplimiento
   - Evaluación de seguridad
   - Aplicación de políticas

4. **Gestión de Incidentes**
   - Detección de brechas
   - Ejecución de protocolo de respuesta
   - Gestión de proceso de recuperación
   - Mantenimiento de documentación

## 8. Software y Licenciamiento

### A. Sistemas Actualmente Implementados

1. **Sistemas de Base de Datos**
   - MongoDB Atlas
     * Versión: Última estable
     * Licencia: Server Side Public License (SSPL)
     * Componentes: MongoDB Server, Atlas Search, Atlas Data Lake
     * Uso: Base de datos principal para datos enriquecidos y análisis
   - MySQL
     * Versión: 8.0+
     * Licencia: GPL-2.0
     * Uso: Sistema de datos fuente de La Nube

2. **IA y Aprendizaje Automático**
   - API de OpenAI
     * Versión: Última estable
     * Licencia: Propietaria (Comercial)
     * Componentes: API GPT-4, API de Embeddings
     * Uso: Implementación de análisis y RAG
   - Embeddings Vectoriales
     * Uso: Búsqueda semántica y análisis
     * Implementación: Módulos Python personalizados

3. **Núcleo Backend**
   - Python 3.11.9 (Licencia PSF)
   - FastAPI (MIT)
   - Autenticación JWT
   - Python-dotenv
   - Controladores MongoDB (Apache-2.0)
   - SQLAlchemy (MIT)

4. **Núcleo Frontend**
   - React 18+ (MIT)
   - TypeScript 4.9+ (Apache-2.0)
   - Vite 4.0+ (MIT)
   - Material-UI (MIT)
   - Next UI (MIT)
   - Tailwind CSS (MIT)

5. **Infraestructura en la Nube**
   - AWS EC2 (Propietaria)
   - AWS S3 (Propietaria)
   - AWS CloudFront (Propietaria)

6. **Herramientas de Procesamiento de Datos**
   - Scripts Python Personalizados (Internos)

7. **Implementación de Seguridad**
   - Autenticación basada en JWT
   - Encriptación de contraseñas
   - Configuración basada en variables de entorno
   - Características de seguridad de MongoDB Atlas
   - Grupos de seguridad AWS
   - Lista blanca de IPs

### B. Desarrollo y Despliegue
1. **Control de Versiones**
   - Git (GPL-2.0)
   - GitHub Actions (MIT)

2. **Servicios AWS**
   - EC2 (Propietaria)
   - S3 (Propietaria)
   - CloudFront (Propietaria)
   - AWS SDK (Apache-2.0)

3. **Herramientas de Desarrollo**
   - Visual Studio Code (MIT)

### C. Herramientas Opcionales/Futuras

1. **Desarrollo y Containerización**
   - Docker
   - Docker Compose 
   - Herramientas de orquestación de contenedores

2. **Frameworks de Pruebas**
   - Pruebas Backend
     * pytest
     * pytest-asyncio
     * unittest
   - Pruebas Frontend
     * Jest
     * React Testing Library
     * Cypress

3. **Monitoreo Avanzado** 
   - AWS CloudWatch
   - Monitoreo avanzado de MongoDB
   - Recolección de métricas personalizadas
   - Seguimiento de rendimiento

4. **Sistemas de Documentación** (Consideración futura)
   - Sphinx
   - JSDoc
   - Swagger/OpenAPI
   - ReDoc

5. **Herramientas Adicionales de Seguridad**
   - Implementación de OAuth2

### D. Cumplimiento de Licencias

1. **Implementación Actual**
   - Auditorías regulares de dependencias
   - Verificación de compatibilidad de licencias
   - Cumplimiento de código abierto
   - Escaneo de vulnerabilidades de seguridad

## 9. Infraestructura de Servidores y Detalles de Procesamiento

### A. Infraestructura en la Nube (AWS)
1. **Servidores de Producción Principales**
   - Proveedor: Amazon Web Services (AWS)
   - Empresa: Amazon Web Services, Inc.
   - Ubicación: Región AWS Sudamérica (São Paulo)
   - Acuerdo de Nivel de Servicio (SLA): https://aws.amazon.com/legal/service-level-agreements/
   - Contacto Técnico: Soporte AWS (Plan Enterprise Support)
   - Características de Seguridad:
     * AWS Shield para protección DDoS
     * AWS WAF (Firewall de Aplicaciones Web)
     * Aislamiento VPC
     * Grupos de seguridad y NACLs
     * Encriptación en reposo y en tránsito
     * Actualizaciones regulares de seguridad
   - Cumplimiento:
     * SOC 1/2/3
     * ISO 27001
     * PCI DSS Level 1

2. **Servidores de Base de Datos (MongoDB Atlas)**
   - Proveedor: MongoDB, Inc.
   - Ubicación: Región AWS Sudamérica (São Paulo)
   - Acuerdo de Nivel de Servicio (SLA): https://www.mongodb.com/legal/service-level-agreement
   - Contacto Técnico: Soporte MongoDB
   - Características de Seguridad:
     * Aislamiento de red
     * Lista blanca de IPs
     * Conexión VPC
     * Auditoría de base de datos
     * Encriptación en reposo (FIPS 140-2)
     * Encriptación TLS/SSL en tránsito
   - Cumplimiento:
     * SOC 2 Type II
     * ISO 27001
     * Preparado para HIPAA

3. **Entrega de Contenido (AWS CloudFront)**
   - Proveedor: Amazon Web Services (AWS)
   - Ubicación: Ubicaciones Edge Globales
   - Acuerdo de Nivel de Servicio (SLA): https://aws.amazon.com/legal/service-level-agreements/
   - Contacto Técnico: Soporte AWS
   - Características de Seguridad:
     * Protección DDoS
     * Encriptación SSL/TLS
     * Encriptación a nivel de campo
     * Controles de acceso
     * Identidad de acceso de origen
   - Cumplimiento:
     * Hereda marco de cumplimiento AWS

### B. Entorno de Desarrollo y Pruebas
1. **Servidores de Desarrollo**
   - Ubicación: Infraestructura interna
   - Propósito: Solo desarrollo y pruebas
   - Características de Seguridad:
     * Red de desarrollo aislada
     * Acceso restringido al equipo de desarrollo
     * Actualizaciones regulares de seguridad
     * Anonimización de datos de desarrollo

2. **Entorno de Staging**
   - Ubicación: Región AWS Sudamérica (São Paulo)
   - Propósito: Pruebas pre-producción
   - Características de Seguridad:
     * Refleja configuraciones de seguridad de producción
     * Acceso restringido
     * Uso de datos sanitizados
     * Auditorías regulares de seguridad

### C. Ubicaciones de Procesamiento de Datos
1. **Procesamiento de Datos Principal**
   - Ubicación: Región AWS Sudamérica (São Paulo)
   - Propósito: Procesamiento principal de la aplicación
   - Tipos de Datos Procesados:
     * Datos de usuarios
     * Datos de tiendas
     * Datos de transacciones
     * Datos analíticos

2. **Procesamiento de IA (OpenAI)**
   - Proveedor: OpenAI
   - Ubicación: Infraestructura segura en la nube de OpenAI
   - Acuerdo de Servicio: https://openai.com/policies/terms-of-use
   - Características de Seguridad:
     * Autenticación API
     * Transmisión encriptada de datos
     * Sin retención de datos
     * Procesamiento que preserva la privacidad

### D. Estándares de Seguridad y Cumplimiento
1. **Medidas Generales de Seguridad**
   - Monitoreo de infraestructura 24/7
   - Evaluaciones regulares de seguridad
   - Plan de respuesta a incidentes
   - Procedimientos de recuperación ante desastres
   - Planificación de continuidad del negocio

2. **Control de Acceso**
   - Control de acceso basado en roles (RBAC)
   - Autenticación multifactor (MFA)
   - Revisiones regulares de acceso
   - Registro de auditoría
   - Gestión de sesiones

3. **Protección de Datos**
   - Encriptación de extremo a extremo
   - Clasificación de datos
   - Procedimientos de respaldo de datos
   - Políticas de retención
   - Procesos seguros de eliminación

4. **Marco de Cumplimiento**
   - Auditorías regulares de cumplimiento
   - Mantenimiento de certificaciones de seguridad
   - Actualizaciones y revisiones de políticas

## 10. Uso y Gestión de Cookies

### A. Sistema de Gestión de Cookies Implementado (2025)

El sistema D-Unit cuenta con un sistema completo de gestión de cookies implementado para que cumpla con lo siguiente:

1. **Cumplimiento Legal Integral**
   - **GDPR** (Reglamento General de Protección de Datos) - Unión Europea
   - **CCPA** (Ley de Privacidad del Consumidor de California) - California, EE.UU.
   - **LGPD** (Lei Geral de Proteção de Dados) - Brasil
   - **PIPEDA** (Ley de Protección de Información Personal y Documentos Electrónicos) - Canadá

2. **Características Principales del Sistema**
   - **Distinción entre Cookies Esenciales y Opcionales**: Separación clara entre cookies necesarias para el funcionamiento y cookies opcionales con controles granulares
   - **Interfaz Profesional**: Banner estilo CookieYes y panel de configuraciones con integración Material-UI
   - **Control Granular**: Cinco categorías de cookies (esenciales, funcionales, analíticas, rendimiento, marketing) con controles individuales
   - **Información Detallada**: Tablas completas mostrando nombres, propósitos, duración, tipos y proveedores de cookies
   - **Registro de Auditoría**: Seguimiento completo del consentimiento con IDs únicos, timestamps y versioning
   - **Soporte Multiidioma**: Implementación completa en inglés y español
   - **Arquitectura Orientada a Eventos**: Actualizaciones en tiempo real de preferencias con sincronización de estado

3. **Categorías de Cookies Implementadas**

   #### Cookies Esenciales (Esenciales - Siempre Habilitadas)
   - **dunit_auth_token**: Token JWT de autenticación para sesiones seguras de usuario
   - **dunit_refresh_token**: Mecanismo de renovación de tokens para autenticación sin interrupciones
   - **dunit_csrf_token**: Protección contra ataques de falsificación de solicitudes entre sitios
   - **dunit_store_context**: Contexto específico de tienda y preferencias
   - **CloudFront-***: Cookies de distribución AWS CloudFront para entrega de contenido

   #### Cookies Funcionales (Opcionales - Controlables por el Usuario)
   - **dunit_user_preferences**: Preferencias de interfaz de usuario y configuraciones
   - **dunit_language**: Preferencia de idioma seleccionado para internacionalización
   - **dunit_theme**: Preferencia de modo claro/oscuro del tema
   - **dunit_meta_email**: Email seleccionado para integración de autenticación Meta
   - **dunit_selected_store**: Contexto de tienda actualmente seleccionada

   #### Cookies Analíticas (Opcionales - Controlables por el Usuario)
   - **dunit_session_id**: Seguimiento anónimo de sesión para analíticas de uso
   - **dunit_page_views**: Seguimiento de vistas de página para optimización de aplicación
   - **dunit_user_flow**: Patrones de navegación del usuario para mejoras de UX

   #### Cookies de Rendimiento (Opcionales - Controlables por el Usuario)
   - **dunit_performance_metrics**: Monitoreo de rendimiento de aplicación
   - **dunit_error_tracking**: Reporte de errores e información de depuración
   - **dunit_load_times**: Seguimiento de tiempos de carga para optimización

   #### Cookies de Marketing (Opcionales - Controlables por el Usuario)
   - **Meta Facebook Pixel**: Seguimiento de publicidad y conversiones de Facebook
   - **Google Analytics**: Analíticas web y seguimiento de comportamiento de usuario
   - **Integraciones de Terceros**: Cookies de seguimiento de servicios externos

4. **Implementación Técnica**
   - **Componentes UI**: CookieBanner y CookieSettings con Material-UI
   - **Servicio de Cookies**: Patrón singleton para gestión centralizada de cookies
   - **Hook Personalizado**: useCookieConsent para integración de componentes
   - **Sistema de Tipos**: Interfaces TypeScript completas para seguridad de tipos
   - **Configuración Centralizada**: Inventario completo de cookies con metadatos
   - **Integración con Autenticación**: Migración de localStorage a cookies seguras

5. **Funcionalidades de Cumplimiento**
   - **Registro de Auditoría**: IDs únicos de consentimiento con timestamps UTC precisos
   - **Control de Versiones**: Seguimiento de versiones de consentimiento para cumplimiento regulatorio
   - **Documentación Legal**: Enlaces directos a políticas de privacidad
   - **Registros de Acceso**: Historia accesible de todas las acciones de consentimiento del usuario
   - **Información de Procesamiento**: Explicación clara de recolección y uso de datos

### B. Integración con Sistema de Autenticación

El sistema de gestión de cookies está completamente integrado con el sistema de autenticación:

1. **Seguridad Mejorada**
   - **Migración desde localStorage**: Cambio de localStorage a cookies seguras httpOnly para tokens de autenticación
   - **Protección CSRF**: Tokens CSRF dedicados para prevención de falsificación de solicitudes
   - **Transmisión Segura**: Todas las cookies de autenticación usan configuraciones secure y sameSite
   - **Alcance de Dominio**: Restricciones apropiadas de dominio y ruta para aislamiento de seguridad

2. **Arquitectura del Sistema**
   - **Almacenamiento Basado en Cookies**: Servicio de autenticación usa el servicio de cookies para gestión de tokens
   - **Conciencia de Preferencias**: Flujo de autenticación respeta las preferencias de cookies del usuario
   - **Validación de Consentimiento**: Operaciones de autenticación verifican consentimiento apropiado antes de proceder

### C. Experiencia del Usuario

1. **Flujo del Banner**
   - **Visita Inicial**: Banner aparece con aviso legal claro y opciones
   - **Información Esencial**: Explicación de cookies esenciales vs opcionales
   - **Selección de Preferencias**: Controles granulares para cada categoría de cookies
   - **Información Detallada**: Secciones expandibles con tablas completas de cookies
   - **Registro de Consentimiento**: Generación de ID único de consentimiento con registro de auditoría

2. **Gestión de Configuraciones**
   - **Acceso Persistente**: Configuraciones disponibles a través de página/componente dedicado
   - **Estado Actual**: Visualización de preferencias activas e historial de consentimiento
   - **Opciones de Modificación**: Actualizaciones fáciles de preferencias con efecto inmediato
   - **Documentación Legal**: Acceso a política de privacidad e información legal

### D. Características Técnicas Avanzadas

1. **Sistema de Eventos**
   - **Actualizaciones en Tiempo Real**: Sistema de eventos personalizado para cambios de preferencias
   - **Sincronización de Estado**: Notificación inmediata de cambios de consentimiento
   - **Eventos de Categoría**: Eventos individuales para habilitar/deshabilitar categorías
   - **Eventos de Auditoría**: Seguimiento de todas las acciones relacionadas con consentimiento

2. **Estrategia de Almacenamiento**
   - **Datos de Consentimiento**: Almacenados en localStorage para persistencia entre sesiones
   - **Valores de Cookies**: Gestionados a través de API document.cookie con codificación apropiada
   - **Registro de Auditoría**: Historia completa de cambios de consentimiento con timestamps

3. **Consideraciones de Rendimiento**
   - **Patrón Singleton**: Instancia única del servicio de cookies para eficiencia
   - **Throttling de Eventos**: Manejo apropiado de eventos para prevenir problemas de rendimiento
   - **Carga Lazy**: Banner solo se carga cuando el estado de consentimiento está indeterminado

### E. Monitoreo y Mantenimiento

1. **Métricas de Cumplimiento**
   - **Tasas de Consentimiento**: Seguimiento de aceptación/rechazo de cookies por categoría
   - **Registro de Auditoría**: Registros detallados de todas las acciones de consentimiento
   - **Monitoreo de Versiones**: Seguimiento de actualizaciones de versiones de consentimiento
   - **Métricas de Rendimiento**: Impacto del sistema de gestión de cookies en rendimiento

2. **Directrices de Desarrollo**
   - **Agregar Nuevas Cookies**: Proceso documentado para agregar cookies al inventario
   - **Mejores Prácticas**: Privacidad por diseño, comunicación clara, auditorías regulares
   - **Validación de Consentimiento**: Siempre validar consentimiento antes de procesar cookies opcionales
   - **Monitoreo de Rendimiento**: Supervisar impacto del sistema en rendimiento de aplicación

Esta implementación completa garantiza el cumplimiento legal total y proporciona una experiencia de usuario transparente y controlable para la gestión de cookies en la plataforma D-Unit. 