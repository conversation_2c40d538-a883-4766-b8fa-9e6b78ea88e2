import { environment } from './environment';

export const API_URL = environment.isProduction ? environment.prodApiUrl : environment.apiUrl;
// Global switch to disable CSRF protection (read from env)
export const DISABLE_CSRF = environment.disableCsrf;

// Add auth-specific URL helpers
export const AUTH_BASE_URL = `${API_URL}/api/auth`;

export const getApiUrl = (endpoint: string) => {
    const baseUrl = API_URL.endsWith('/') ? API_URL.slice(0, -1) : API_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/${cleanEndpoint}`;
};

export const getAuthUrl = (endpoint: string) => {
    const baseUrl = AUTH_BASE_URL.endsWith('/') ? AUTH_BASE_URL.slice(0, -1) : AUTH_BASE_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/${cleanEndpoint}`;
}; 