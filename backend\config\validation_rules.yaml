# Validation Rules Configuration for D-Unit API
# This file defines validation policies for different endpoints and request types

global_rules:
  # General request size and format limits
  max_request_size: "10MB"
  max_header_size: "8KB"
  max_url_length: 2048
  max_query_params: 50
  
  # Allowed content types
  allowed_content_types:
    - "application/json"
    - "multipart/form-data"
    - "application/x-www-form-urlencoded"
    - "text/plain"
  
  # Required headers for all requests
  required_headers:
    - "User-Agent"
  
  # Security headers validation
  security_headers:
    block_suspicious_user_agents: true
    require_referer_for_forms: false
    validate_origin: true
  
  # Input sanitization
  sanitization:
    trim_whitespace: true
    remove_null_bytes: true
    normalize_unicode: true
    max_nested_depth: 10
  
  # Common validation patterns
  patterns:
    email: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    phone: "^\\+?[1-9]\\d{1,14}$"
    uuid: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    store_id: "^[0-9]+$"
    safe_string: "^[a-zA-Z0-9\\s\\-_.@]+$"

# Endpoint-specific validation rules
endpoint_rules:
  # Authentication endpoints
  "/api/auth/token":
    max_request_size: "1MB"
    required_fields:
      - "username"
      - "password"
    field_validation:
      username:
        max_length: 255
        pattern: "email"
        required: true
      password:
        max_length: 128
        min_length: 1
        required: true
    rate_limit_on_failure: true
    log_failed_attempts: true

  "/api/auth/2fa/verify-login":
    max_request_size: "512KB"
    required_fields:
      - "email"
      - "code"
    field_validation:
      email:
        pattern: "email"
        max_length: 255
        required: true
      code:
        pattern: "^[0-9]{6}$"
        required: true
    rate_limit_on_failure: true

  "/api/auth/forgot-password":
    max_request_size: "512KB"
    required_fields:
      - "email"
    field_validation:
      email:
        pattern: "email"
        max_length: 255
        required: true
    rate_limit_on_failure: true

  # Chat endpoints
  "/api/chat/**":
    max_request_size: "5MB"
    field_validation:
      message:
        max_length: 5000
        min_length: 1
        profanity_filter: true
        required: true
      store_id:
        pattern: "store_id"
        required: true
      session_id:
        pattern: "uuid"
        required: false
    content_filtering:
      block_code_injection: true
      block_html_tags: true
      block_sql_patterns: true
      max_urls_per_message: 3
    suspicious_pattern_detection: true

  # Meta API endpoints
  "/api/meta/**":
    max_request_size: "2MB"
    required_query_params:
      - "store_id"
    field_validation:
      store_id:
        pattern: "store_id"
        required: true
      page_id:
        pattern: "^[0-9]+$"
        required: false
      since:
        pattern: "^\\d{4}-\\d{2}-\\d{2}$"
        required: false
      until:
        pattern: "^\\d{4}-\\d{2}-\\d{2}$"
        required: false
    validate_date_ranges: true

  # Store endpoints
  "/api/store/**":
    max_request_size: "3MB"
    field_validation:
      store_id:
        pattern: "store_id"
        required: true
      name:
        max_length: 255
        pattern: "safe_string"
        required: false
      description:
        max_length: 1000
        required: false
    validate_store_access: true

  # Admin endpoints
  "/api/admin/**":
    max_request_size: "10MB"
    admin_only: true
    field_validation:
      store_id:
        pattern: "store_id"
        required: false
      user_email:
        pattern: "email"
        required: false
    audit_log: true
    validate_admin_permissions: true

  # Insights endpoints
  "/api/insights/**":
    max_request_size: "5MB"
    field_validation:
      store_id:
        pattern: "store_id"
        required: true
      metrics:
        type: "object"
        max_nested_depth: 5
        required: false
      time_range:
        pattern: "^(7d|30d|90d|1y|lifetime)$"
        required: false
    validate_complex_queries: true

# File upload validation
file_upload_rules:
  # General file upload limits
  max_file_size: "50MB"
  max_files_per_request: 10
  
  # Allowed file types
  allowed_extensions:
    images:
      - ".jpg"
      - ".jpeg"
      - ".png"
      - ".gif"
      - ".webp"
    documents:
      - ".pdf"
      - ".doc"
      - ".docx"
      - ".txt"
    data:
      - ".json"
      - ".csv"
      - ".xlsx"
  
  # File content validation
  validate_file_content: true
  scan_for_malware: false  # Enable if antivirus integration available
  check_file_headers: true

# Business rule validation
business_rules:
  # User registration rules
  user_registration:
    min_password_length: 8
    require_special_characters: true
    require_numbers: true
    require_uppercase: true
    max_failed_attempts: 5
    lockout_duration_minutes: 15

  # Store management rules
  store_management:
    max_stores_per_user: 10
    min_store_name_length: 3
    max_store_name_length: 100
    allowed_store_types:
      - "ecommerce"
      - "retail"
      - "service"
      - "restaurant"

  # API usage rules
  api_usage:
    max_concurrent_requests: 10
    max_query_complexity: 100
    require_api_key_for_external: false

# Data type validation
data_types:
  string:
    max_length: 1000
    encoding: "utf-8"
    normalize: true
  
  integer:
    min_value: -2147483648
    max_value: 2147483647
  
  float:
    min_value: -999999999.99
    max_value: 999999999.99
    max_decimal_places: 2
  
  boolean:
    strict: true  # Only allow true/false, not 1/0
  
  array:
    max_length: 1000
    max_nested_depth: 5
  
  object:
    max_properties: 100
    max_nested_depth: 10

# Security validation
security_rules:
  # SQL injection protection
  sql_injection:
    enabled: true
    patterns:
      - "\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\\b"
      - "'.*OR.*=.*"
      - "UNION.*SELECT"
      - "--"
      - "/\\*.*\\*/"
  
  # XSS protection
  xss_protection:
    enabled: true
    patterns:
      - "<script"
      - "javascript:"
      - "onload="
      - "onerror="
      - "eval\\("
  
  # Path traversal protection
  path_traversal:
    enabled: true
    patterns:
      - "\\.\\."
      - "/etc/passwd"
      - "windows/system32"
  
  # Command injection protection
  command_injection:
    enabled: true
    patterns:
      - ";\\s*(cat|ls|rm|wget|curl)\\s+"
      - "\\|\\s*(cat|ls|rm)\\s+"
      - "`.*`"
      - "\\$\\(.*\\)"

# Custom validation functions
custom_validations:
  # Email domain validation
  email_domain_whitelist:
    enabled: false
    domains: []
  
  # IP geolocation validation
  ip_geolocation:
    enabled: false
    allowed_countries: []
    blocked_countries:
      - "XX"  # Unknown country code
  
  # Content moderation
  content_moderation:
    profanity_filter: true
    spam_detection: true
    inappropriate_content: true

# Error handling for validation
error_handling:
  # Response format for validation errors
  return_detailed_errors: false  # Set to false in production
  log_validation_failures: true
  include_field_names: true
  include_error_codes: true
  
  # Error messages
  generic_error_message: "Invalid input provided"
  field_required_message: "This field is required"
  field_invalid_message: "Invalid value for this field"
  
  # Rate limiting on validation failures
  rate_limit_on_failures: true
  max_validation_failures: 10
  failure_window_minutes: 5 