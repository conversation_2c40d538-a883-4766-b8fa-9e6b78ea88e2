import React, { useState, useMemo } from 'react';
import {
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TablePagination,
  TextField,
  InputAdornment,
  Box,
  Typography
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { Customer } from '../../services/storeService';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/locationUtils';
import { format } from 'date-fns'; // For date formatting

interface CustomerListProps {
  customers: Customer[] | null | undefined;
  onSelectCustomer: (customer: Customer | null) => void;
}

const CustomerList: React.FC<CustomerListProps> = ({ customers, onSelectCustomer }) => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [searchText, setSearchText] = useState('');
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
    setPage(0); // Reset page when search changes
  };

  const filteredCustomers = useMemo(() => {
    if (!customers) return [];
    
    // Filter first
    const filtered = customers.filter(customer =>
      customer.customer_name?.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.customer_email?.toLowerCase().includes(searchText.toLowerCase())
    );
    
    // Then sort by last_order_date descending (most recent first)
    return filtered.sort((a, b) => {
      // Handle null/undefined dates by treating them as very old
      const dateA = a.last_order_date ? new Date(a.last_order_date) : new Date(0);
      const dateB = b.last_order_date ? new Date(b.last_order_date) : new Date(0);
      
      // Compare timestamps (descending order)
      return dateB.getTime() - dateA.getTime();
    });

  }, [customers, searchText]);

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleRowClick = (customer: Customer) => {
    setSelectedCustomerId(customer.customer_id);
    onSelectCustomer(customer);
  };

  // Avoid layout shift when filtering results in zero results
  const paginatedCustomers = filteredCustomers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Paper elevation={2} sx={{ height: '100%' }}>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          {t('customerList.title', 'Customers')}
        </Typography>
        <TextField
          size="small"
          placeholder={t('customerList.searchPlaceholder', 'Search by name or email...')}
          value={searchText}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ width: '40%' }}
        />
      </Box>
      <TableContainer>
        <Table stickyHeader aria-label="customer list table">
          <TableHead>
            <TableRow>
              <TableCell>{t('customerList.header.name', 'Name')}</TableCell>
              <TableCell>{t('customerList.header.email', 'Email')}</TableCell>
              <TableCell align="right">{t('customerList.header.totalOrders', 'Total Orders')}</TableCell>
              <TableCell align="right">{t('customerList.header.totalSpend', 'Total Spend')}</TableCell>
              <TableCell>{t('customerList.header.lastOrder', 'Last Order Date')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedCustomers.length > 0 ? (
              paginatedCustomers.map((customer) => (
                <TableRow
                  hover
                  key={customer.customer_id}
                  onClick={() => handleRowClick(customer)}
                  selected={selectedCustomerId === customer.customer_id}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>{customer.customer_name}</TableCell>
                  <TableCell>{customer.customer_email}</TableCell>
                  <TableCell align="right">{customer.total_orders}</TableCell>
                  <TableCell align="right">{formatCurrency(customer.total_spend)} {/* Use actual currency symbol */}</TableCell>
                  <TableCell>
                    {customer.last_order_date ? format(new Date(customer.last_order_date), 'yyyy-MM-dd') : 'N/A'}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  {t('customerList.noResults', 'No customers found')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={filteredCustomers.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

export default CustomerList; 