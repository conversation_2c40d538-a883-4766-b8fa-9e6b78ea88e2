import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  Divider,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { useTranslation } from 'react-i18next';
import { CustomerMetricsData } from '../../services/storeService';

interface CouponPaymentShippingCardProps {
  data: CustomerMetricsData | null | undefined;
}

const CouponPaymentShippingCard: React.FC<CouponPaymentShippingCardProps> = ({ data }) => {
  const { t } = useTranslation();

  // State for expanding distributions
  const [showAllCoupons, setShowAllCoupons] = useState(false);
  const [showAllPayments, setShowAllPayments] = useState(false);
  const [showAllShipping, setShowAllShipping] = useState(false);

  const mostFrequentCoupon = data?.most_frequent_coupon_code;
  const mostFrequentPayment = data?.most_frequent_payment_method;
  const mostFrequentShipping = data?.most_frequent_shipping_method;

  // Function to render a single distribution section with expansion
  const renderExpandableDistribution = (
    titleKey: string,
    defaultTitle: string,
    distribution: Record<string, number> | null | undefined,
    showAll: boolean,
    setShowAll: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    const title = t(titleKey, defaultTitle);
    if (!distribution || Object.keys(distribution).length === 0) {
      return (
        <ListItem
          sx={{
            borderRadius: '8px',
            border: '1px solid',
            borderColor: (theme) => theme.palette.divider,
            mb: 1,
            p: 1,
          }}
        >
          <ListItemText primary={title} secondary={t('common.notAvailable', 'Not Available')} />
        </ListItem>
      );
    }

    const sortedEntries = Object.entries(distribution).sort(([, countA], [, countB]) => countB - countA);
    const itemsToShow = showAll ? sortedEntries : sortedEntries.slice(0, 3);

    return (
      <React.Fragment>
        <ListItem
          sx={{
            // No border for the title itself, but maintain consistency if needed for spacing
            // mb: 0.5, // Adjust if title needs different spacing
          }}
        >
          <ListItemText primary={<Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>{title}</Typography>} />
        </ListItem>
        {itemsToShow.map(([key, value]) => (
          <ListItem 
            key={key} 
            dense 
            sx={{
              pl: 4,
              borderRadius: '8px',
              border: '1px solid',
              borderColor: (theme) => theme.palette.divider,
              mb: 1,
              p: 1,
            }}
          >
            <ListItemText primary={key} secondary={`${value} ${t('common.uses', 'uses')}`} />
          </ListItem>
        ))}
        {sortedEntries.length > 3 && (
          <ListItem sx={{ display: 'flex', justifyContent: 'center', pt: 0.5 }}>
            <IconButton 
              size="small" 
              onClick={() => setShowAll(!showAll)} 
              aria-label={showAll ? t('common.showLess', 'Show Less') : t('common.showMore', 'Show More')}
              sx={{ 
                // Optional: Add styling for circular background like in the image
                // bgcolor: 'action.hover',
                // '&:hover': {
                //   bgcolor: 'action.selected'
                // }
              }}
            >
              {showAll ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </ListItem>
        )}
      </React.Fragment>
    );
  };

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <Typography variant="h6" gutterBottom>
          {t('couponPaymentShippingCard.title', 'Usage Patterns')}
        </Typography>

        {/* Most Frequent Section */}
        <Box mb={2}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }} gutterBottom>
            {t('couponPaymentShippingCard.mostFrequentTitle', 'Most Frequent')}
          </Typography>
          <List dense disablePadding>
            <ListItem
              sx={{
                borderRadius: '8px',
                border: '1px solid',
                borderColor: (theme) => theme.palette.divider,
                mb: 1,
                p: 1,
              }}
            >
              <ListItemText 
                primary={t('couponPaymentShippingCard.mostFrequentCoupon', 'Coupon:')} 
                secondary={mostFrequentCoupon?.code ? `${mostFrequentCoupon.code} (${mostFrequentCoupon.count} ${t('common.uses', 'uses')})` : t('common.notAvailable', 'Not Available')}
              />
            </ListItem>
            <ListItem
              sx={{
                borderRadius: '8px',
                border: '1px solid',
                borderColor: (theme) => theme.palette.divider,
                mb: 1,
                p: 1,
              }}
            >
              <ListItemText 
                primary={t('couponPaymentShippingCard.mostFrequentPayment', 'Payment Method:')}
                secondary={mostFrequentPayment?.name ? `${mostFrequentPayment.name} (${mostFrequentPayment.count} ${t('common.uses', 'uses')})` : t('common.notAvailable', 'Not Available')}
              />
            </ListItem>
            <ListItem
              sx={{
                borderRadius: '8px',
                border: '1px solid',
                borderColor: (theme) => theme.palette.divider,
                mb: 1,
                p: 1,
              }}
            >
              <ListItemText 
                primary={t('couponPaymentShippingCard.mostFrequentShipping', 'Shipping Method:')}
                secondary={mostFrequentShipping?.name ? `${mostFrequentShipping.name} (${mostFrequentShipping.count} ${t('common.uses', 'uses')})` : t('common.notAvailable', 'Not Available')}
              />
            </ListItem>
          </List>
        </Box>

        <Divider sx={{ my: 1 }} />

        {/* Distributions Section */}
        <Box flexGrow={1} sx={{ overflowY: 'auto' }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }} gutterBottom>
             {t('couponPaymentShippingCard.distributionTitle', 'Distributions')}
          </Typography>
          <List dense disablePadding>
            {renderExpandableDistribution(
              'couponPaymentShippingCard.couponDistribution',
              'Coupon Codes',
              data?.coupon_code_distribution,
              showAllCoupons,
              setShowAllCoupons
            )}
            {renderExpandableDistribution(
              'couponPaymentShippingCard.paymentDistribution',
              'Payment Methods',
              data?.payment_method_distribution,
              showAllPayments,
              setShowAllPayments
            )}
            {renderExpandableDistribution(
              'couponPaymentShippingCard.shippingDistribution',
              'Shipping Methods',
              data?.shipping_method_distribution,
              showAllShipping,
              setShowAllShipping
            )}
          </List>
        </Box>
      </CardContent>
    </Card>
  );
};

export default CouponPaymentShippingCard; 