import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from 'recharts';
import { useStoreShippingAnalysis, ShippingAnalysisResponse, ShippingMethodDistributionItem } from '../../services/apiService';
import { useParams } from 'react-router-dom';
import LoadingSpinner from '../common/LoadingSpinner';
import { useTheme } from '@mui/material';
import { Typography, List, ListItem, ListItemText, ListItemIcon } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';
import { logger } from '../../utils/logger';

// Define Props interface
interface ShippingAnalysisProps {
  storeId?: string;
  storeAnalysisData?: {
    analysis?: {
      shipping_analysis?: {
        analysis_text?: string;
        recommendations?: string[];
        recommendations_es?: string[];
      }
    }
  } | null;
}

// Colors for the pie chart
const COLORS = ['#0D6EFD', '#6610F2', '#6F42C1', '#D63384', '#DC3545', '#FD7E14', '#FFC107', '#198754'];

// Update component signature to accept props
const ShippingAnalysis: React.FC<ShippingAnalysisProps> = ({ storeId: propStoreId, storeAnalysisData }) => {
  // Get storeId from URL params as a fallback
  const { storeId: paramsStoreId } = useParams<{ storeId: string }>();
  
  // Determine the effective storeId (prop takes precedence)
  const effectiveStoreId = propStoreId || paramsStoreId;

  const [shippingData, setShippingData] = useState<ShippingAnalysisResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const theme = useTheme();
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  const fetchShippingAnalysis = useStoreShippingAnalysis();

  // Memoize fetch function to prevent infinite loop
  const fetchShippingData = useCallback(async (storeId: string) => {
    if (!storeId) {
      setError("Store ID is required to fetch shipping data.");
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      const data = await fetchShippingAnalysis(storeId);
      setShippingData(data);
    } catch (err) {
      logger.error("Failed to fetch shipping data:", err);
      setError("Failed to fetch shipping data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [fetchShippingAnalysis]); // Add fetchShippingAnalysis dependency

  useEffect(() => {
    if (!effectiveStoreId) {
      setError("Store ID is required.");
      setIsLoading(false);
      return;
    }
    fetchShippingData(effectiveStoreId);
  }, [effectiveStoreId, fetchShippingData]);

  // Use the new array structure for chart data
  const usageData: ShippingMethodDistributionItem[] = shippingData?.shipping_method_distribution || [];

  if (isLoading) {
    return <LoadingSpinner text="Loading shipping data..." />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (!shippingData || !shippingData.shipping_method_distribution || shippingData.shipping_method_distribution.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No shipping method data available</p>
      </div>
    );
  }

  const isEmpty = usageData.length === 0;

  // Directly use storeAnalysisData for analysis text and recommendations
  const shippingAnalysisDetails = storeAnalysisData?.analysis?.shipping_analysis;

  interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{ payload: ShippingMethodDistributionItem }>;
  }
  const CustomTooltipContent = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload; 
      const methodName = data.name;
      const methodValue = data.count;

      return (
        <div style={{
          background: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
          color: '#FFFFFF',
          borderRadius: '8px',
          padding: '8px 12px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
          whiteSpace: 'nowrap',
          fontSize: '12px', 
        }}>
          <p style={{ margin: 0 }}>
            {`${t('shippingAnalysis.centralLabel.methodText', 'Método de envío')}: ${methodName} - ${methodValue.toLocaleString()} ${t('shippingAnalysis.ordersSuffix', 'pedidos')}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const recommendations = currentLang.startsWith('es') && shippingAnalysisDetails?.recommendations_es
    ? shippingAnalysisDetails.recommendations_es
    : shippingAnalysisDetails?.recommendations;

  return (
    <div className="space-y-8">
      {/* Shipping Methods Usage Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div
          className="rounded-lg shadow-sm p-6"
          style={{ backgroundColor: theme.palette.background.paper }}
        >
          <h3 
            className="text-lg font-semibold mb-4" 
            style={{ color: theme.palette.text.primary }}
          >
            {t('shippingAnalysis.usageTitle', 'Shipping Methods Usage')}
          </h3>
          {isEmpty ? (
            <div className="flex items-center justify-center h-64">
              <p style={{ color: theme.palette.text.secondary }}>
                No shipping methods distribution data available
              </p>
            </div>
          ) : (
            <>
              <div className="h-64 w-full" style={{ minHeight: '250px', width: '100%' }}>
                <ChartContainer width="100%" height="100%" minHeight={250}>
                  <PieChart>
                    <Pie
                      data={usageData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="count"
                    >
                      {usageData.map((_entry: ShippingMethodDistributionItem, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltipContent />} cursor={{ fill: 'transparent' }} />
                  </PieChart>
                </ChartContainer>
              </div>
              <div className="mt-4">
                {usageData.map((entry: ShippingMethodDistributionItem, index: number) => (
                  <div key={entry.name} className="flex items-center mb-2">
                    <span
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></span>
                    <span
                      className="text-sm"
                      style={{ color: theme.palette.mode === 'dark' ? theme.palette.common.white : '#000' }}
                    >
                      {entry.name}
                    </span>
                    <span
                      className="text-sm ml-auto"
                      style={{ color: theme.palette.mode === 'dark' ? theme.palette.common.white : '#000' }}
                    >
                      {entry.count.toLocaleString()} {t('shippingAnalysis.ordersSuffix', 'orders')}
                    </span>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Shipping Analysis Text & Recommendations */}
        <div
          className="lg:col-span-2 rounded-lg shadow-sm p-6"
          style={{ backgroundColor: theme.palette.background.paper }}
        >
          <h3 
            className="text-lg font-semibold mb-4" 
            style={{ color: theme.palette.text.primary }}
          >
            {t('shippingAnalysis.recommendationsTitle', 'Recommendations:')}
          </h3>

          {/* Display Recommendations Only */}
          {recommendations && recommendations.length > 0 ? (
            <List dense disablePadding>
              {recommendations.map((rec: string, index: number) => (
                <ListItem
                  key={index}
                  disableGutters
                  sx={theme.palette.mode === 'dark' ? {
                    alignItems: 'flex-start',
                    py: 0.5,
                    backgroundColor: theme.palette.background.paper,
                    color: '#fff',
                    borderRadius: 2,
                    transition: 'background 0.2s',
                    mb: 1,
                    '&:hover': {
                      backgroundColor: '#222',
                      color: '#fff',
                    },
                  } : {
                    alignItems: 'flex-start',
                    py: 0.5,
                    mb: 1
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 'auto', mr: 1, mt: '0.2em', color: theme.palette.mode === 'dark' ? '#fff' : 'text.secondary' }}>
                    <ArrowRightIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={rec}
                    primaryTypographyProps={{
                      variant: 'body2',
                      style: { color: theme.palette.mode === 'dark' ? '#fff' : theme.palette.text.secondary }
                    }}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" sx={{ mt: 3 }} style={{ color: theme.palette.text.secondary, fontStyle: 'italic' }}>
              {t('dashboard.shippingAnalysis.noRecommendations', 'No shipping recommendations available.')}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShippingAnalysis; 