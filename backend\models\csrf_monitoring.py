from datetime import datetime, timezone
from typing import Dict, List, Optional, Set
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum

class ViolationType(str, Enum):
    """CSRF violation types"""
    MISSING_TOKEN = "missing_token"
    INVALID_TOKEN = "invalid_token"
    EXPIRED_TOKEN = "expired_token"
    DOUBLE_SUBMIT_MISMATCH = "double_submit_mismatch"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"

class SeverityLevel(str, Enum):
    """Security event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AttackType(str, Enum):
    """CSRF attack pattern types"""
    BRUTE_FORCE = "brute_force"
    DISTRIBUTED = "distributed"
    SYSTEMATIC = "systematic"
    REPLAY = "replay"
    COORDINATED = "coordinated"

class ThreatLevel(str, Enum):
    """Threat assessment levels"""
    MINIMAL = "minimal"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"

class CSRFViolationEvent(BaseModel):
    """Database model for CSRF violation events"""
    id: Optional[str] = Field(None, alias="_id")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    user_id: str
    user_email: Optional[str] = None
    ip_address: str
    endpoint: str
    method: str
    violation_type: ViolationType
    user_agent: str = ""
    referer: Optional[str] = None
    severity: SeverityLevel
    request_headers: Optional[Dict] = None
    session_id: Optional[str] = None
    geographical_location: Optional[Dict] = None  # Country, region, etc.
    response_action: Optional[str] = None  # Action taken (blocked, logged, etc.)
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        }

class CSRFAttackPattern(BaseModel):
    """Database model for detected attack patterns"""
    id: Optional[str] = Field(None, alias="_id")
    pattern_id: str
    attack_type: AttackType
    severity: SeverityLevel
    threat_level: ThreatLevel
    start_time: datetime
    end_time: datetime
    duration_minutes: float
    total_violations: int
    unique_ips: int
    unique_users: int
    affected_endpoints: List[str]
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    
    # Attack characteristics
    primary_ip_addresses: List[str]
    primary_user_agents: List[str]
    geographical_distribution: Optional[Dict] = None
    
    # Response information
    mitigation_actions: List[str] = Field(default_factory=list)
    investigation_status: str = "pending"  # pending, investigating, resolved, false_positive
    analyst_notes: Optional[str] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    detected_by_system: bool = True
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        }

class CSRFMonitoringConfig(BaseModel):
    """Configuration model for CSRF monitoring settings"""
    id: Optional[str] = Field(None, alias="_id")
    
    # Detection thresholds
    violation_threshold: int = 10
    ip_threshold: int = 5
    user_threshold: int = 3
    time_window_minutes: int = 15
    
    # Attack pattern detection
    brute_force_threshold: int = 10
    distributed_attack_ips_threshold: int = 3
    systematic_attack_threshold: int = 5
    
    # Adaptive thresholds
    enable_adaptive_thresholds: bool = True
    adaptive_threshold_factor: float = 0.5
    adaptive_learning_window_hours: int = 24
    
    # Response actions
    auto_block_enabled: bool = False
    auto_block_duration_minutes: int = 60
    alert_threshold: SeverityLevel = SeverityLevel.HIGH
    
    # Monitoring settings
    enable_real_time_analysis: bool = True
    enable_geographical_tracking: bool = True
    retention_days: int = 90
    
    # Notification settings
    email_alerts_enabled: bool = True
    webhook_alerts_enabled: bool = False
    webhook_url: Optional[str] = None
    alert_recipients: List[str] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: Optional[str] = None
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        }

class CSRFThreatAssessment(BaseModel):
    """Model for comprehensive threat assessment"""
    id: Optional[str] = Field(None, alias="_id")
    assessment_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    time_period_hours: int
    
    # Summary statistics
    total_violations: int
    unique_ips: int
    unique_users: int
    total_patterns: int
    
    # Threat analysis
    overall_threat_level: ThreatLevel
    threat_score: float = Field(..., ge=0.0, le=100.0)
    risk_factors: List[str] = Field(default_factory=list)
    
    # Top threats
    top_violation_types: Dict[str, int]
    top_targeted_endpoints: Dict[str, int]
    top_attacking_ips: Dict[str, int]
    
    # Active patterns
    active_attack_patterns: List[str]
    high_severity_patterns: int
    
    # Recommendations
    recommended_actions: List[str] = Field(default_factory=list)
    security_recommendations: List[str] = Field(default_factory=list)
    
    # Trends
    violation_trend: str  # increasing, decreasing, stable
    severity_trend: str  # escalating, de-escalating, stable
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        }

class CSRFBlockedIP(BaseModel):
    """Model for tracking blocked IP addresses"""
    id: Optional[str] = Field(None, alias="_id")
    ip_address: str
    blocked_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    block_expires_at: Optional[datetime] = None
    block_reason: str
    violation_count: int
    last_violation_time: datetime
    
    # Metadata
    blocked_by_system: bool = True
    blocked_by_user: Optional[str] = None
    manual_review_required: bool = False
    whitelist_requested: bool = False
    
    # Geographic info
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        }

class CSRFSecurityEvent(BaseModel):
    """General security event model for CSRF-related incidents"""
    id: Optional[str] = Field(None, alias="_id")
    event_id: str
    event_type: str  # csrf_violation, attack_pattern_detected, ip_blocked, etc.
    severity: SeverityLevel
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Event details
    title: str
    description: str
    affected_resources: List[str] = Field(default_factory=list)
    source_ip: Optional[str] = None
    user_id: Optional[str] = None
    
    # Response tracking
    response_required: bool = False
    response_status: str = "pending"  # pending, in_progress, resolved, ignored
    assigned_to: Optional[str] = None
    response_notes: Optional[str] = None
    
    # Related data
    related_violation_ids: List[str] = Field(default_factory=list)
    related_pattern_ids: List[str] = Field(default_factory=list)
    
    # Metadata
    created_by: str = "system"
    resolved_at: Optional[datetime] = None
    resolution_time_minutes: Optional[float] = None
    
    class Config:
        use_enum_values = True
        populate_by_name = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        } 