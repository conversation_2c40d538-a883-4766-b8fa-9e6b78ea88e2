import os
import time
import logging
import traceback
from pymongo import MongoClient
from fastapi import HTTPException
from config.settings import get_settings
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import asyncio

# Configure logging
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

def get_mongodb_connection(max_retries=5, retry_delay=5):
    """Get MongoDB connection with proper error handling and retry logic"""
    for attempt in range(max_retries):
        try:
            # Get MongoDB credentials from environment variables
            MONGODB_CONNECTION = settings.MONGODB_CONNECTION
            if not MONGODB_CONNECTION:
                logger.error(f"MongoDB connection string not found in environment variables. Attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    logger.info(f"Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                    continue
                raise HTTPException(
                    status_code=500,
                    detail="Database configuration missing"
                )

            # Log connection attempt (without credentials)
            logger.info(f"Attempting MongoDB connection (attempt {attempt + 1}/{max_retries})")
            
            # Initialize the client with proper settings
            client = MongoClient(
                MONGODB_CONNECTION,
                serverSelectionTimeoutMS=120000,
                connectTimeoutMS=120000,
                socketTimeoutMS=120000,
                maxPoolSize=50,
                retryWrites=True,
                retryReads=True
            )

            # Test the connection
            client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")

            return client
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB (attempt {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Waiting {retry_delay} seconds before retry...")
                time.sleep(retry_delay)
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to connect to database after multiple attempts. Please check the server logs."
                )

# Initialize MongoDB client with retries
try:
    mongodb_client = get_mongodb_connection()
    
    # Check if mongodb_client is not None before accessing databases
    if mongodb_client is None:
        logger.error("MongoDB client is None, cannot initialize database connections")
        raise HTTPException(
            status_code=500,
            detail="Failed to initialize database connections"
        )
    
    # Create async motor client from the same connection string
    async_mongodb_client = AsyncIOMotorClient(settings.MONGODB_CONNECTION)
    
    # Add type hints with proper AsyncIOMotorDatabase types
    # db_main is ONLY to be used for user authentication (login) purposes. All other data access must use db_analysis or allowed collections.
    db_main: AsyncIOMotorDatabase = async_mongodb_client[settings.MONGODB_MAIN_DB]
    db_analysis: AsyncIOMotorDatabase = async_mongodb_client[settings.MONGODB_ANALYSIS_DB]

    logger.info("Initializing database connections:")
    logger.info(f"Main DB: {db_main.name} (for user authentication and core data)")
    logger.info(f"Analysis DB: {db_analysis.name} (for analysis and chat data)")

except Exception as e:
    logger.error(f"Database initialization error: {str(e)}")
    logger.error(f"Full traceback: {traceback.format_exc()}")
    raise HTTPException(
        status_code=500,
        detail="Failed to initialize database connections. Please check the server logs."
    )

# Define async verification functions for database connections and collections
async def verify_connections():
    """Verify database connections by sending ping commands"""
    await db_main.command('ping')
    await db_analysis.command('ping')
    logger.info("Successfully connected to all databases")

async def check_main_collections():
    """Check required collections in main database (only 'store_users' is required)"""
    main_collections = await db_main.list_collection_names()
    logger.info(f"Available collections in {db_main.name}: {main_collections}")
    
    # Check for required collections
    required_collections = ['store_users']
    for collection in required_collections:
        if collection in main_collections:
            count = await db_main[collection].count_documents({})
            logger.info(f"Found {collection} collection with {count} documents")
        else:
            logger.warning(f"Required collection {collection} not found in {db_main.name}")

    # Removed checks for deprecated 'stores' and 'products' collections as they are no longer required.

async def check_analysis_collections():
    """Check collections in analysis database"""
    analysis_collections = await db_analysis.list_collection_names()
    logger.info(f"Available collections in {db_analysis.name}: {analysis_collections}")

# Main async function to verify all database connections and collections
async def verify_database_setup():
    """Perform comprehensive database verification"""
    try:
        # Verify connections first
        await verify_connections()
        
        # Then check collections
        await check_main_collections()
        await check_analysis_collections()
        
        logger.info("Database verification completed successfully")
        return True
    except Exception as e:
        logger.error(f"Database verification error: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail="Failed to verify database. Please check the server logs."
        )

# Helper function to check if a collection exists in a database
async def collection_exists(db: AsyncIOMotorDatabase, collection_name: str) -> bool:
    """Check if a collection exists in the given database by awaiting the list_collection_names coroutine"""
    collections = await db.list_collection_names()
    return collection_name in collections

