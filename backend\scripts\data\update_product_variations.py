import os
import sys
import logging
import mysql.connector
import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional, cast
import argparse

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    PRODUCT_DETAILS_CACHE_COLLECTION = 'product_details_cache'
    
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    
    # Add constants for store filtering to match update_active_stores.py
    INACTIVE_STORE_EXCEPTIONS = getattr(settings, 'INACTIVE_STORE_EXCEPTIONS', [])
    NEW_STORE_CUTOFF_YEAR = getattr(settings, 'NEW_STORE_CUTOFF_YEAR', 2025)
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# --- Helper Functions ---

def get_mysql_connection():
    """Establish and return a MySQL connection."""
    try:
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=10
        )
        logger.info("MySQL connection successful")
        return mysql_conn
    except mysql.connector.Error as err:
        logger.error(f"MySQL connection error: {err}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error establishing MySQL connection: {e}", exc_info=True)
        return None

def get_stores_to_process(mysql_conn) -> List[Dict[str, Any]]:
    """
    Fetches active stores from the MySQL database, using the same criteria as
    update_active_stores.py to ensure consistency.
    """
    stores: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available for get_stores_to_process.")
        return stores
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            exception_clause = f"OR s.id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
            
            query = f"""
            SELECT 
                s.id_store, s.symbol, s.name
            FROM stores s
            WHERE 
                (s.active = 1
                {exception_clause}
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
            ORDER BY s.id_store
            """
            cursor.execute(query)
            stores = cursor.fetchall()
            logger.info(f"Found {len(stores)} stores to process using consistent criteria.")
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching stores: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching stores from MySQL: {e}", exc_info=True)
    return stores

def get_product_variations_for_store(store_id: int, mysql_conn) -> Dict[str, List[Dict[str, Any]]]:
    """
    Fetches all product variations for a specific store from MySQL.
    Only includes variations for ACTIVE, NON-DELETED products and ACTIVE variations.
    Groups variations by product_id.
    
    Note: Since store_ordered_products doesn't track variations specifically,
    we can only include the variation's catalog prices, not historical sale prices.
    
    Returns a dictionary with product_id as key and a list of variations as value.
    """
    variations_by_product: Dict[str, List[Dict[str, Any]]] = {}
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_product_variations_for_store (store: {store_id}).")
        return variations_by_product
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # First, get statistics about products and variations
            stats_query = """
            SELECT 
                COUNT(DISTINCT p.id_product) as total_products,
                COUNT(DISTINCT CASE WHEN p.active = 1 AND p.deleted_at IS NULL THEN p.id_product END) as active_products,
                COUNT(DISTINCT CASE WHEN p.active = 0 THEN p.id_product END) as inactive_products,
                COUNT(DISTINCT CASE WHEN p.deleted_at IS NOT NULL THEN p.id_product END) as deleted_products,
                COUNT(DISTINCT pv.id_product_variation) as total_variations,
                COUNT(DISTINCT CASE WHEN p.active = 1 AND p.deleted_at IS NULL AND pv.active = 1 THEN pv.id_product_variation END) as active_variations
            FROM products p
            LEFT JOIN product_variations pv ON p.id_product = pv.id_product
            WHERE p.id_store = %s
            """
            cursor.execute(stats_query, (store_id,))
            stats = cursor.fetchone()
            
            if stats:
                logger.info(f"Store {store_id} variation stats: "
                           f"{stats['active_products']} active products (out of {stats['total_products']} total, "
                           f"{stats['inactive_products']} inactive, {stats['deleted_products']} deleted), "
                           f"{stats['active_variations']} active variations out of {stats['total_variations']} total")
            
            # Standard query without variation price history (not available in schema)
            query = """
            SELECT
                p.id_product,
                pv.id_product_variation,
                pv.sku,
                pv.stock AS variation_stock,
                pv.price AS variation_price,
                pv.offer AS variation_offer,
                pv.offer_price AS variation_offer_price,
                pa.id_attribute,
                a.name AS attribute_name,
                av.value AS attribute_value,
                p.code AS product_code,
                p.description AS product_description
            FROM products p
            JOIN product_variations pv ON p.id_product = pv.id_product
            JOIN product_attributes pa ON pv.id_product_variation = pa.id_product_variation
            JOIN attribute_variations av ON pa.id_attribute_value = av.id_attribute_variation
            JOIN attributes a ON pa.id_attribute = a.id_attribute
            WHERE p.id_store = %s 
                AND p.active = 1              -- Only active products
                AND p.status = 1              -- Only online/visible products
                AND p.deleted_at IS NULL      -- Exclude soft-deleted products
                AND pv.active = 1             -- Only active variations
            ORDER BY p.id_product, pv.id_product_variation, a.name
            """
            
            cursor.execute(query, (store_id,))
            results = cursor.fetchall()
            
            logger.info(f"Fetched {len(results)} product variation attribute records for active products in store {store_id}")
            
            # Process and structure the data
            current_product_id = None
            current_variation_id = None
            current_variation = None
            
            for row in results:
                # Cast each row to Dict to satisfy the type checker
                row_dict = cast(Dict[str, Any], row)
                
                product_id = str(row_dict.get('id_product', ''))
                variation_id = str(row_dict.get('id_product_variation', ''))
                
                # If this is a new product_id, initialize its entry in the dictionary
                if product_id not in variations_by_product:
                    variations_by_product[product_id] = []
                
                # If this is a new variation or the first one
                if variation_id != current_variation_id:
                    # If we have a previous variation to save (not the first one)
                    if current_variation is not None and current_product_id is not None:
                        variations_by_product[current_product_id].append(current_variation)
                    
                    # Create a new variation object
                    current_variation = {
                        'variation_id': variation_id,
                        'sku': row_dict.get('sku', ''),
                        'variation_stock': int(row_dict.get('variation_stock', 0) or 0),
                        'variation_price': float(row_dict.get('variation_price', 0.0) or 0.0),
                        'variation_offer': bool(row_dict.get('variation_offer', 0)),
                        'variation_offer_price': float(row_dict.get('variation_offer_price', 0.0) or 0.0),
                        'product_code': row_dict.get('product_code', ''),
                        'product_description': row_dict.get('product_description', ''),
                        'attributes': []
                    }
                    
                    current_product_id = product_id
                    current_variation_id = variation_id
                
                # Add attribute to the current variation
                if current_variation is not None:  # Extra safety check
                    current_variation['attributes'].append({
                        'name': row_dict.get('attribute_name', ''),
                        'value': row_dict.get('attribute_value', '')
                    })
            
            # Don't forget to add the last variation processed
            if current_variation is not None and current_product_id is not None:
                variations_by_product[current_product_id].append(current_variation)
            
            logger.info(f"Structured data for {len(variations_by_product)} active products with variations for store {store_id}")
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching product variations for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching product variations for store {store_id}: {e}", exc_info=True)
    
    return variations_by_product

def update_cache_with_variations(store_id: int, variations_by_product: Dict[str, List[Dict[str, Any]]], mongo_db) -> bool:
    """
    Updates the product_details_cache collection with variation data.
    Adds the variations to each product in the products array.
    
    Parameters:
    - store_id: The store ID
    - variations_by_product: Dictionary mapping product_id to a list of variation objects
    - mongo_db: MongoDB database connection
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Check if mongo_db is None first
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Fetch the existing document for this store
        existing_doc = collection.find_one({"_id": str(store_id)})
        
        if existing_doc is None:
            logger.warning(f"Cache document for store {store_id} not found. Variations cannot be added. Run update_product_details.py first?")
            return False
        
        if 'products' not in existing_doc or not isinstance(existing_doc['products'], list):
            logger.error(f"Cache document for store {store_id} does not have a valid 'products' array.")
            return False
        
        # Check if the cache contains only active products
        active_products_only = existing_doc.get('active_products_only', False)
        if not active_products_only:
            logger.warning(f"Cache document for store {store_id} may contain inactive products. "
                         f"Consider running update_product_details.py with the latest version first.")
        
        # Iterate through products and add variation data
        modified_count = 0
        products_without_variations = 0
        
        for product_dict in existing_doc['products']:
            if not isinstance(product_dict, dict):
                continue
                
            product_id = str(product_dict.get('product_id', ''))
            
            if not product_id:
                continue
                
            # Look up variations for this product
            variations = variations_by_product.get(product_id, [])
            
            # Calculate total stock from all variations
            total_variation_stock = sum(variation.get('variation_stock', 0) for variation in variations)
            
            # Add variations and total variation stock to the product dictionary
            product_dict['variations'] = variations
            product_dict['current_stock_variations'] = total_variation_stock
            
            if variations:
                modified_count += 1
            else:
                products_without_variations += 1
        
        # Update the document in MongoDB
        update_result = collection.update_one(
            {"_id": str(store_id)},
            {
                "$set": {
                    "products": existing_doc['products'],
                    "variations_last_updated": datetime.now(timezone.utc),
                    "variations_active_only": True  # Flag to indicate variations are filtered for active products only
                }
            }
        )
        
        if update_result.modified_count > 0:
            logger.info(f"Updated product details cache with variations for store {store_id}. "
                       f"Modified {modified_count} products with variations, "
                       f"{products_without_variations} products have no variations.")
        else:
            logger.info(f"No changes needed for product details cache for store {store_id} (data identical or no products with variations).")
        
        return update_result.acknowledged
        
    except Exception as e:
        logger.error(f"Error updating MongoDB cache with variations for store {store_id}: {e}", exc_info=True)
        return False

def update_cache_with_variations_split_stores(store_id: int, variations_by_product: Dict[str, List[Dict[str, Any]]], mongo_db) -> bool:
    """
    Updates the product_details_cache collection with variation data for stores that have been split.
    Works with the large_store_products collection for stores with products_in_separate_collection flag.
    """
    try:
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get collections
        main_collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        large_store_collection = mongo_db.get('large_store_products')
        
        # Check if this store has been split
        main_doc = main_collection.find_one({"_id": str(store_id)})
        
        if not main_doc:
            logger.warning(f"Cache document for store {store_id} not found.")
            return False
            
        # Check if products are in separate collection
        if main_doc.get('products_in_separate_collection', False):
            logger.info(f"Store {store_id} has products in separate collection. Processing chunks...")
            
            if not large_store_collection:
                logger.error(f"large_store_products collection not found for split store {store_id}")
                return False
            
            # Process each chunk
            chunks = list(large_store_collection.find({"store_id": str(store_id)}))
            total_modified = 0
            total_without_variations = 0
            
            for chunk in chunks:
                chunk_id = chunk['_id']
                products = chunk.get('products', [])
                modified_in_chunk = 0
                
                logger.info(f"Processing chunk {chunk_id} with {len(products)} products...")
                
                for product_dict in products:
                    if not isinstance(product_dict, dict):
                        continue
                    
                    # Skip simplified products (they've been optimized already)
                    if product_dict.get('simplified', False):
                        continue
                        
                    product_id = str(product_dict.get('product_id', ''))
                    
                    if not product_id:
                        continue
                        
                    # Look up variations for this product
                    variations = variations_by_product.get(product_id, [])
                    
                    # Calculate total stock from all variations
                    total_variation_stock = sum(variation.get('variation_stock', 0) for variation in variations)
                    
                    # Add variations and total variation stock to the product dictionary
                    product_dict['variations'] = variations
                    product_dict['current_stock_variations'] = total_variation_stock
                    
                    if variations:
                        modified_in_chunk += 1
                        total_modified += 1
                    else:
                        total_without_variations += 1
                
                # Update the chunk
                large_store_collection.update_one(
                    {"_id": chunk_id},
                    {
                        "$set": {
                            "products": products,
                            "variations_last_updated": datetime.now(timezone.utc),
                            "variations_active_only": True
                        }
                    }
                )
                
                logger.info(f"Updated chunk {chunk_id} with {modified_in_chunk} products with variations")
            
            # Update main document metadata
            main_collection.update_one(
                {"_id": str(store_id)},
                {
                    "$set": {
                        "variations_last_updated": datetime.now(timezone.utc),
                        "variations_active_only": True,
                        "variations_updated_in_chunks": True
                    }
                }
            )
            
            logger.info(f"Updated variations for split store {store_id}: {total_modified} products with variations, "
                       f"{total_without_variations} without variations")
            return True
            
        else:
            # Store not split, use regular update method
            logger.info(f"Store {store_id} is not split. Using regular update method...")
            return False  # Let the caller handle with regular method
            
    except Exception as e:
        logger.error(f"Error updating variations for split store {store_id}: {e}", exc_info=True)
        return False

def update_cache_with_variations_batched(store_id: int, variations_by_product: Dict[str, List[Dict[str, Any]]], mongo_db) -> bool:
    """
    Updates the product_details_cache collection with variation data in batches to avoid timeouts.
    Used for stores with very large numbers of variations (like store 961).
    
    Parameters:
    - store_id: The store ID
    - variations_by_product: Dictionary mapping product_id to a list of variation objects
    - mongo_db: MongoDB database connection
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Check if mongo_db is None first
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Fetch the existing document for this store
        existing_doc = collection.find_one({"_id": str(store_id)})
        
        if existing_doc is None:
            logger.warning(f"Cache document for store {store_id} not found. Variations cannot be added. Run update_product_details.py first?")
            return False
        
        if 'products' not in existing_doc or not isinstance(existing_doc['products'], list):
            logger.error(f"Cache document for store {store_id} does not have a valid 'products' array.")
            return False
        
        # Check if the cache contains only active products
        active_products_only = existing_doc.get('active_products_only', False)
        if not active_products_only:
            logger.warning(f"Cache document for store {store_id} may contain inactive products. "
                         f"Consider running update_product_details.py with the latest version first.")
        
        # Process products in batches of 50
        product_count = len(existing_doc['products'])
        batch_size = 50
        total_batches = (product_count + batch_size - 1) // batch_size  # Ceiling division
        modified_count = 0
        products_without_variations = 0
        success = True
        
        logger.info(f"Processing {product_count} products in {total_batches} batches for store {store_id}")
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, product_count)
            batch_products = existing_doc['products'][start_idx:end_idx]
            
            logger.info(f"Processing batch {batch_num + 1}/{total_batches} (products {start_idx}-{end_idx-1}) for store {store_id}")
            
            # Store 961 special handling with more detailed logging
            if store_id == 961:
                logger.info(f"Store 961 special handling: Processing batch {batch_num + 1}/{total_batches} - progress: {int((batch_num + 1) / total_batches * 100)}%")
            
            # Update products in this batch
            batch_modified = 0
            for product_dict in batch_products:
                if not isinstance(product_dict, dict):
                    continue
                    
                product_id = str(product_dict.get('product_id', ''))
                
                if not product_id:
                    continue
                    
                # Look up variations for this product
                variations = variations_by_product.get(product_id, [])
                
                # Calculate total stock from all variations
                total_variation_stock = sum(variation.get('variation_stock', 0) for variation in variations)
                
                # Add variations and total variation stock to the product dictionary
                product_dict['variations'] = variations
                product_dict['current_stock_variations'] = total_variation_stock
                
                if variations:
                    batch_modified += 1
                    modified_count += 1
                else:
                    products_without_variations += 1
            
            # Update just this batch in MongoDB
            update_operations = {}
            for i, product in enumerate(batch_products):
                update_operations[f"products.{start_idx + i}"] = product
            
            update_operations["variations_last_updated"] = datetime.now(timezone.utc)
            update_operations["variations_active_only"] = True  # Flag to indicate variations are filtered
            
            update_result = collection.update_one(
                {"_id": str(store_id)},
                {"$set": update_operations}
            )
            
            if not update_result.acknowledged:
                logger.error(f"Failed to update batch {batch_num + 1} for store {store_id}")
                success = False
            else:
                logger.info(f"Batch {batch_num + 1} updated with {batch_modified} products with variations")
                
            # Small delay between batches to prevent overloading the database
            time.sleep(1)
        
        logger.info(f"Completed updating {modified_count} products with variations for store {store_id} in {total_batches} batches. "
                   f"{products_without_variations} products have no variations.")
        return success
        
    except Exception as e:
        logger.error(f"Error updating MongoDB cache with variations for store {store_id} in batches: {e}", exc_info=True)
        return False

# --- Main Execution ---

def main():
    """Main execution function."""
    # Process command-line arguments
    parser = argparse.ArgumentParser(description='Update product variations in the product_details_cache collection')
    parser.add_argument('--store_id', type=str, help='Process only a specific store ID')
    args = parser.parse_args()
    
    specific_store_id = args.store_id
    
    logger.info("Starting product variations update script")
    logger.info("NOTE: This script now fetches variations only for ACTIVE, NON-DELETED products with ACTIVE variations.")
    
    if specific_store_id:
        logger.info(f"Processing only store ID: {specific_store_id}")
    else:
        logger.info("Processing all stores")
    
    mysql_conn = None
    mongo_client = None
    processed_count = 0
    failed_count = 0
    total_variations_added = 0
    
    try:
        # 1. Establish Connections
        logger.info(f"Connecting to MySQL host: {MYSQL_HOST}, database: {MYSQL_DB_LANUBE}")
        mysql_conn = get_mysql_connection()
        if mysql_conn is None:
            logger.error("Failed to establish MySQL connection. Exiting.")
            return

        logger.info("Connecting to MongoDB...")
        mongo_client = get_mongodb_connection()
        if mongo_client is None:
            logger.error("Failed to establish MongoDB connection. Exiting.")
            return

        try:
            # Try to set parameters if supported
            if mongo_client:
                mongo_client.admin.command('setParameter', 1, socketTimeoutMS=120000)
                mongo_client.admin.command('setParameter', 1, connectTimeoutMS=120000)
                logger.info("MongoDB timeout parameters increased")
        except Exception as e:
            logger.warning(f"MongoDB timeout parameters not adjustable: {e}")
            # Continue without timeout adjustment

        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"MongoDB connection successful (DB: {MONGO_DB_ANALYSIS_NAME})")

        # 2. Get stores to process
        if specific_store_id:
            # If specific store_id is provided, just process that one
            store = None
            with mysql_conn.cursor(dictionary=True) as cursor:
                # Use the same filtering criteria as get_stores_to_process
                exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
                exception_clause = f"OR id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
                
                query = f"""
                SELECT id_store, symbol, name  
                FROM stores
                WHERE id_store = %s 
                    AND ((active = 1 {exception_clause} OR YEAR(created_at) >= {NEW_STORE_CUTOFF_YEAR})
                    AND LOWER(name) NOT LIKE '%test%'
                    AND LOWER(name) NOT LIKE '%demo%')
                """
                cursor.execute(query, (specific_store_id,))
                store = cursor.fetchone()
            
            if store:
                # Cast store to Dict to satisfy the type checker
                store_dict = cast(Dict[str, Any], store)
                stores = [store_dict]
                
                store_name = store_dict.get('name', 'Unknown')
                store_id_str = str(store_dict.get('id_store', 'Unknown'))
                logger.info(f"Found store: {store_name} (ID: {store_id_str})")
            else:
                logger.error(f"Store with ID {specific_store_id} not found or not matching processing criteria")
                return
        else:
            # Otherwise get all active stores
            stores = get_stores_to_process(mysql_conn)
            
        if not stores:
            logger.warning("No active stores found to process. Exiting.")
            return

        # 3. Process each store
        logger.info(f"Processing {len(stores)} stores...")
        for store in stores:
            store_id = None
            try:
                # Get store ID
                if not isinstance(store, dict):
                    logger.warning(f"Store data is not a dictionary: {store}. Skipping.")
                    failed_count += 1
                    continue

                if 'id_store' not in store:
                    logger.warning(f"Skipping store entry due to missing 'id_store': {store}")
                    failed_count += 1
                    continue
                
                store_id_val = store['id_store']
                
                # Convert to int
                try:
                    store_id = int(str(store_id_val))
                except (ValueError, TypeError):
                    logger.warning(f"Cannot convert store ID '{store_id_val}' to integer. Skipping store.")
                    failed_count += 1
                    continue
                
                store_name = store.get('name', 'Unknown Store')
                logger.info(f"--- Processing store ID: {store_id} ({store_name}) ---")

                # 4. Get product variations for this store
                variations_by_product = get_product_variations_for_store(store_id, mysql_conn)
                
                # Only update if we have variations data
                if variations_by_product:
                    # Check if this is a large store (store 961 or other with > 10,000 variations)
                    variation_count = sum(len(variations) for variations in variations_by_product.values())
                    logger.info(f"Store {store_id} has {variation_count} total active variations across {len(variations_by_product)} active products")
                    total_variations_added += variation_count
                    
                    # 5. Update MongoDB cache with variations
                    # Check if this is a large store that might be split
                    if store_id == 961 or variation_count > 10000:
                        logger.info(f"Checking if store {store_id} has been split...")
                        
                        # Try split store method first
                        success = update_cache_with_variations_split_stores(store_id, variations_by_product, mongo_db)
                        
                        if not success:
                            # If not split, try batched method
                            logger.info(f"Store {store_id} not split. Using batched processing...")
                            success = update_cache_with_variations_batched(store_id, variations_by_product, mongo_db)
                    else:
                        # Regular stores use normal method
                        success = update_cache_with_variations(store_id, variations_by_product, mongo_db)
                    
                    if success:
                        processed_count += 1
                        logger.info(f"Successfully updated variations for store {store_id}")
                    else:
                        failed_count += 1
                        logger.warning(f"Failed to update variations for store {store_id}")
                else:
                    logger.info(f"No active variations found for active products in store {store_id}")
                    # We won't count this as a failure, but we won't increment processed_count either
                    # It's simply a store without variations for active products

            except Exception as e:
                logger.error(f"Error processing store {store_id}: {e}", exc_info=True)
                failed_count += 1

        logger.info("--- Store processing finished ---")

    except mysql.connector.Error as db_err:
        logger.error(f"Database connection error: {db_err}")
    except Exception as e:
        logger.error(f"Unexpected error during script execution: {e}", exc_info=True)
    finally:
        # Close connections
        logger.info("Closing database connections...")
        if mysql_conn and mysql_conn.is_connected():
            try:
                mysql_conn.close()
                logger.info("MySQL connection closed.")
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
        if mongo_client:
            try:
                mongo_client.close()
                logger.info("MongoDB connection closed.")
            except Exception as e:
                logger.error(f"Error closing MongoDB connection: {e}")

        logger.info("--- Script Summary ---")
        logger.info(f"Successfully processed/updated variations for: {processed_count} stores")
        logger.info(f"Failed to process/update variations for: {failed_count} stores")
        logger.info(f"Total active variations added across all stores: {total_variations_added}")
        logger.info("Script finished.")

if __name__ == "__main__":
    main()