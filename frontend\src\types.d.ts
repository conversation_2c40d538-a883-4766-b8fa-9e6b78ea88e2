/**
 * This is a shim declaration file to handle libraries that import from a bare 'types' module.
 * It's a workaround for the TypeScript error: "Cannot find module 'types' or its corresponding type declarations."
 * 
 * Based on the error in MetaDashboard.tsx and based on similar issues in libraries like react-toastify,
 * this file declares an empty module 'types' to satisfy TypeScript.
 */

declare module 'types' {
  // This empty declaration tells TypeScript that the 'types' module exists
  // but doesn't provide any specific exports
}

// Add TypeScript interfaces for our global objects
interface Window {
  isVerticalNav?: boolean;
  isChatPage?: boolean;
}

// Custom events for navigationModeChanged
interface NavigationModeChangedDetail {
  isVerticalNav: boolean;
  isChatPage: boolean;
}

interface NavigationModeChangedEvent extends CustomEvent {
  detail: NavigationModeChangedDetail;
}

// Continue with existing types if the file already exists 