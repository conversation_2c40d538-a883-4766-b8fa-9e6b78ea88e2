import React from 'react';
import { 
  Box, 
  Typography, 
  // Paper, 
  // Grid, 
  // List, 
  // ListItem, 
  // ListItemText,
  // Chip,
  // Card, 
  // CardContent,
  // Tooltip,
  IconButton,
  Collapse,
  // CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  List,
  ListItem,
  ListItemText,
  Button
} from '@mui/material';
import { 
  // MonetizationOn as MonetizationOnIcon, 
  // TrendingUp as TrendingUpIcon, 
  // TrendingDown as TrendingDownIcon, 
  // CompareArrows as CompareArrowsIcon, 
  // Info as InfoIcon, 
  // CheckCircle as CheckCircleIcon,
  // Error as ErrorIcon,
  // Warning as WarningIcon
} from '@mui/icons-material';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
// import * as d3 from 'd3'; // Removed unused import
// import { useTheme } from '@mui/material/styles'; // Removed unused import
// import { Theme } from '@mui/material/styles'; // Removed unused import
import { ProductDetail, ProductSalesDetail } from '../../services/storeService'; // Import ProductDetail and ProductSalesDetail
import { format, parseISO } from 'date-fns';
import { useTranslation } from 'react-i18next';

interface RosenProductDetailsProps {
  products: ProductDetail[];
  onProductSelect: (product: ProductDetail) => void; // Callback for selection
  selectedProductFromDashboard: ProductDetail | null; // New prop
  productSalesHistoryFromDashboard: ProductSalesDetail[] | null | undefined; // New prop
}

// Internal Row component to handle collapsible details
const ProductRow: React.FC<{ 
  row: ProductDetail;
  onSelect: (product: ProductDetail) => void;
  productSalesHistory: ProductSalesDetail[] | null | undefined; // New prop
  isSelected: boolean; // New prop
}> = ({ row, onSelect, productSalesHistory, isSelected }) => {
  const [open, setOpen] = React.useState(false);
  const [visibleSalesCount, setVisibleSalesCount] = React.useState(5); // State for visible sales
  const [showAllSales, setShowAllSales] = React.useState(false); // State to toggle all sales
  const { t } = useTranslation();

  React.useEffect(() => {
    setOpen(isSelected);
    // Reset visible sales count when selection changes
    if (!isSelected) {
      setVisibleSalesCount(5);
      setShowAllSales(false);
    }
  }, [isSelected]);

  const handleRowClick = () => {
    onSelect(row);
  };

  const handleLoadMore = () => {
    setVisibleSalesCount(productSalesHistory?.length || 0); // Show all sales
    setShowAllSales(true);
  };

  const handleShowLess = () => {
    setVisibleSalesCount(5);
    setShowAllSales(false);
  };

  const sortedSalesHistory = productSalesHistory ? [...productSalesHistory].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()) : [];
  const salesToDisplay = showAllSales ? sortedSalesHistory : sortedSalesHistory.slice(0, visibleSalesCount);

  const hasMoreSales = sortedSalesHistory.length > visibleSalesCount && !showAllSales;
  const hasLessSales = showAllSales && sortedSalesHistory.length > 5;

  // Basic formatter, consider moving to utils
  const formatNumber = (num: number | undefined | null) => {
    if (num === undefined || num === null) return 'N/A';
    return num.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(parseISO(dateString), 'yyyy-MM-dd');
    } catch (_e) {
      return dateString; // Return original if parsing fails
    }
  };

  return (
    <React.Fragment>
      <TableRow 
        sx={{ '& > *': { borderBottom: 'unset' }, cursor: 'pointer' }}
        onClick={handleRowClick} // Select product on row click
        hover
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(_e) => { _e.stopPropagation(); setOpen(!open); }} // Prevent row click when expanding
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {row.name}
        </TableCell>
        <TableCell align="right">{formatNumber(row.sales_units)}</TableCell>
        <TableCell align="right">{row.currency_symbol}{formatNumber((row as any).gross_revenue || 0)}</TableCell>
        <TableCell align="right">{row.currency_symbol}{formatNumber((row as any).net_revenue || 0)}</TableCell>
        <TableCell align="right">{row.currency_symbol}{formatNumber(row.current_price)}</TableCell>
        <TableCell align="right">{formatNumber(row.current_stock)}</TableCell>
        <TableCell align="right">{formatNumber(row.favorite_count)}</TableCell>
        <TableCell align="right">{row.categories?.primary_category || (row.categories?.store_categories && row.categories.store_categories.length > 0 ? row.categories.store_categories[0].name : 'N/A')}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={9}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                {t('productDetails.detailsTitle', 'Details')} - {row.name}
              </Typography>
              <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                {t('productDetails.salesDetailsTitle', 'Sales Details')}
              </Typography>
              {(salesToDisplay && salesToDisplay.length > 0) ? (
                <Box sx={{ maxHeight: 200, overflowY: 'auto', pr: 1 }}>
                  <List dense disablePadding>
                    {salesToDisplay.map((sale, index) => (
                      <ListItem key={index} sx={{ py: 0.5 }}>
                        <ListItemText
                          primary={`${t('productDetails.salesDate', 'Date')}: ${formatDate(sale.date)}`}
                          secondary={`${t('productDetails.salesUnits', 'Units')}: ${formatNumber(sale.units_sold)} | ${t('productDetails.salesRevenue', 'Revenue')}: ${row.currency_symbol || '$'}${formatNumber(sale.revenue)}`}
                          primaryTypographyProps={{ variant: 'body2' }}
                          secondaryTypographyProps={{ variant: 'caption' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  {t('productDetails.noSalesDetails', 'No sales details available.')}
                </Typography>
              )}

              {hasMoreSales && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button variant="outlined" onClick={handleLoadMore}>
                    {t('productDetails.loadMoreSales', 'Load More Sales')}
                  </Button>
                </Box>
              )}
              {hasLessSales && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button variant="outlined" onClick={handleShowLess}>
                    {t('productDetails.showLessSales', 'Show Less Sales')}
                  </Button>
                </Box>
              )}
              {/* Variations details could go here */}
              {/* Categories details could go here */}
              {/* Sales details summary/chart could go here */}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
};

export const RosenProductDetails: React.FC<RosenProductDetailsProps> = ({ 
  products = [], 
  onProductSelect, 
  selectedProductFromDashboard, // New prop
  productSalesHistoryFromDashboard // New prop
}) => {
  const { t } = useTranslation();

  // This internal state is no longer needed as selection is managed by Dashboard.tsx
  // const [selectedProductInternal, setSelectedProductInternal] = React.useState<ProductDetail | null>(null);

  const handleInternalProductSelect = (product: ProductDetail) => {
    // No longer toggle internal state, simply pass up to parent
    onProductSelect(product); 
  };

  if (!products || products.length === 0) {
    return <Typography>{t('productDetails.noProducts', 'No product details available.')}</Typography>;
  }

  return (
    <TableContainer component={Paper} elevation={0} variant="outlined">
      <Table aria-label="collapsible table" size="small">
        <TableHead>
          <TableRow>
            <TableCell />
            <TableCell>{t('productDetails.header.product', 'Product')}</TableCell>
            <TableCell align="right">{t('productDetails.header.salesUnits', 'Units Sold')}</TableCell>
            <TableCell align="right">{t('productDetails.header.grossRevenue', 'Ingresos Brutos')}</TableCell>
            <TableCell align="right">{t('productDetails.header.netRevenue', 'Ingresos netos (Despues de Descuentos)')}</TableCell>
            <TableCell align="right">{t('productDetails.header.price', 'Price')}</TableCell>
            <TableCell align="right">{t('productDetails.header.stock', 'Stock')}</TableCell>
            <TableCell align="right">{t('productDetails.header.favorites', 'Favorites')}</TableCell>
            <TableCell align="right">{t('productDetails.header.primaryCategory', 'Primary Category')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {products.map((product) => (
            <ProductRow 
              key={product.product_id} 
              row={product} 
              onSelect={handleInternalProductSelect} 
              productSalesHistory={selectedProductFromDashboard?.product_id === product.product_id ? productSalesHistoryFromDashboard : undefined} // Pass sales details from dashboard prop
              isSelected={selectedProductFromDashboard?.product_id === product.product_id} // Pass selection status from dashboard prop
            />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}; 



