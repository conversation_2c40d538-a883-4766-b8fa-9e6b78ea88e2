// Canonical source for Meta (Facebook/Instagram) types and interfaces for the frontend.
// All Meta-related types should be defined or imported here to ensure consistency and avoid duplication.
// If you need to add or update Meta types, do so in this file.

export interface MetaAuthResponse {
  authResponse: {
    accessToken: string;
    userID: string;
    expiresIn: number;
    grantedScopes?: string;
  };
  status: 'connected' | 'not_authorized' | 'unknown';
}

export interface MetaLoginResponse {
  token: string;
  user: {
    email: string;
    id_store: string;
    name: string;
  };
}

export interface MetaBusinessAccount {
  id: string;
  name: string;
  role: string;
}

export interface MetaPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  category_list: Array<{ id: string; name: string }>;
  tasks: string[];
  platform?: 'facebook' | 'instagram';
  followers_count?: number;
  profile_picture_url?: string;
  media_count?: number;
  fan_count?: number;
}

export enum MetaErrorType {
  AUTH_FAILED = 'AUTH_FAILED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INSUFFICIENT_DATA = 'INSUFFICIENT_DATA',
  INVALID_METRIC = 'INVALID_METRIC',
  RATE_LIMIT = 'RATE_LIMIT',
  NO_DATA = 'NO_DATA',
  SECURITY_ERROR = 'SECURITY_ERROR',
  SDK_NOT_LOADED = 'SDK_NOT_LOADED',
  SDK_NOT_READY = 'SDK_NOT_READY',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface MetaError {
  type: MetaErrorType;
  message: string;
  originalError?: unknown;
}

export const META_PERMISSIONS = {
  facebook: [
    'pages_show_list',
    'pages_read_engagement',
    'pages_manage_metadata',
    'pages_read_user_content',
    'public_profile',
    'email',
    'business_management',
    'ads_management',
    'ads_read',
    'instagram_basic',
    'instagram_manage_insights'
  ],
  instagram: [
    'instagram_basic',
    'instagram_manage_insights',
    'instagram_content_publish',
    'pages_show_list',
    'pages_read_engagement',
    'public_profile',
    'email',
    'business_management'
  ]
};

export interface PermissionInfo {
  nameKey: string;  // Translation key for human-readable name
  descriptionKey: string;  // Translation key for what this permission allows
  impactKey: string;  // Translation key for what would be lost if revoked
  isRequired: boolean;  // Is this permission critical
  category: 'page' | 'business' | 'content' | 'instagram' | 'advertising' | 'other';  // For grouping
}

export const PERMISSION_DESCRIPTIONS: Record<string, PermissionInfo> = {
  'pages_show_list': {
    nameKey: 'metaPermissions.details.pages_show_list.name',
    descriptionKey: 'metaPermissions.details.pages_show_list.description',
    impactKey: 'metaPermissions.details.pages_show_list.impact',
    isRequired: true,
    category: 'page'
  },
  'pages_read_engagement': {
    nameKey: 'metaPermissions.details.pages_read_engagement.name',
    descriptionKey: 'metaPermissions.details.pages_read_engagement.description',
    impactKey: 'metaPermissions.details.pages_read_engagement.impact',
    isRequired: true,
    category: 'page'
  },
  'pages_manage_metadata': {
    nameKey: 'metaPermissions.details.pages_manage_metadata.name',
    descriptionKey: 'metaPermissions.details.pages_manage_metadata.description',
    impactKey: 'metaPermissions.details.pages_manage_metadata.impact',
    isRequired: false,
    category: 'page'
  },
  'pages_read_user_content': {
    nameKey: 'metaPermissions.details.pages_read_user_content.name',
    descriptionKey: 'metaPermissions.details.pages_read_user_content.description',
    impactKey: 'metaPermissions.details.pages_read_user_content.impact',
    isRequired: false,
    category: 'content'
  },
  'public_profile': {
    nameKey: 'metaPermissions.details.public_profile.name',
    descriptionKey: 'metaPermissions.details.public_profile.description',
    impactKey: 'metaPermissions.details.public_profile.impact',
    isRequired: true,
    category: 'other'
  },
  'email': {
    nameKey: 'metaPermissions.details.email.name',
    descriptionKey: 'metaPermissions.details.email.description',
    impactKey: 'metaPermissions.details.email.impact',
    isRequired: false,
    category: 'other'
  },
  'business_management': {
    nameKey: 'metaPermissions.details.business_management.name',
    descriptionKey: 'metaPermissions.details.business_management.description',
    impactKey: 'metaPermissions.details.business_management.impact',
    isRequired: true,
    category: 'business'
  },
  'ads_management': {
    nameKey: 'metaPermissions.details.ads_management.name',
    descriptionKey: 'metaPermissions.details.ads_management.description',
    impactKey: 'metaPermissions.details.ads_management.impact',
    isRequired: false,
    category: 'advertising'
  },
  'ads_read': {
    nameKey: 'metaPermissions.details.ads_read.name',
    descriptionKey: 'metaPermissions.details.ads_read.description',
    impactKey: 'metaPermissions.details.ads_read.impact',
    isRequired: false,
    category: 'advertising'
  },
  'instagram_basic': {
    nameKey: 'metaPermissions.details.instagram_basic.name',
    descriptionKey: 'metaPermissions.details.instagram_basic.description',
    impactKey: 'metaPermissions.details.instagram_basic.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_manage_insights': {
    nameKey: 'metaPermissions.details.instagram_manage_insights.name',
    descriptionKey: 'metaPermissions.details.instagram_manage_insights.description',
    impactKey: 'metaPermissions.details.instagram_manage_insights.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_content_publish': {
    nameKey: 'metaPermissions.details.instagram_content_publish.name',
    descriptionKey: 'metaPermissions.details.instagram_content_publish.description',
    impactKey: 'metaPermissions.details.instagram_content_publish.impact',
    isRequired: false,
    category: 'instagram'
  },
  'read_insights': {
    nameKey: 'metaPermissions.details.read_insights.name',
    descriptionKey: 'metaPermissions.details.read_insights.description',
    impactKey: 'metaPermissions.details.read_insights.impact',
    isRequired: false,
    category: 'page'
  },
  'catalog_management': {
    nameKey: 'metaPermissions.details.catalog_management.name',
    descriptionKey: 'metaPermissions.details.catalog_management.description',
    impactKey: 'metaPermissions.details.catalog_management.impact',
    isRequired: false,
    category: 'business'
  },
  'pages_manage_cta': {
    nameKey: 'metaPermissions.details.pages_manage_cta.name',
    descriptionKey: 'metaPermissions.details.pages_manage_cta.description',
    impactKey: 'metaPermissions.details.pages_manage_cta.impact',
    isRequired: false,
    category: 'page'
  },
  'pages_manage_instant_articles': {
    nameKey: 'metaPermissions.details.pages_manage_instant_articles.name',
    descriptionKey: 'metaPermissions.details.pages_manage_instant_articles.description',
    impactKey: 'metaPermissions.details.pages_manage_instant_articles.impact',
    isRequired: false,
    category: 'page'
  },
  'read_page_mailboxes': {
    nameKey: 'metaPermissions.details.read_page_mailboxes.name',
    descriptionKey: 'metaPermissions.details.read_page_mailboxes.description',
    impactKey: 'metaPermissions.details.read_page_mailboxes.impact',
    isRequired: false,
    category: 'page'
  },
  'pages_messaging': {
    nameKey: 'metaPermissions.details.pages_messaging.name',
    descriptionKey: 'metaPermissions.details.pages_messaging.description',
    impactKey: 'metaPermissions.details.pages_messaging.impact',
    isRequired: false,
    category: 'page'
  },
  'pages_messaging_phone_number': {
    nameKey: 'metaPermissions.details.pages_messaging_phone_number.name',
    descriptionKey: 'metaPermissions.details.pages_messaging_phone_number.description',
    impactKey: 'metaPermissions.details.pages_messaging_phone_number.impact',
    isRequired: false,
    category: 'page'
  },
  'pages_messaging_subscriptions': {
    nameKey: 'metaPermissions.details.pages_messaging_subscriptions.name',
    descriptionKey: 'metaPermissions.details.pages_messaging_subscriptions.description',
    impactKey: 'metaPermissions.details.pages_messaging_subscriptions.impact',
    isRequired: false,
    category: 'page'
  },
  'instagram_manage_comments': {
    nameKey: 'metaPermissions.details.instagram_manage_comments.name',
    descriptionKey: 'metaPermissions.details.instagram_manage_comments.description',
    impactKey: 'metaPermissions.details.instagram_manage_comments.impact',
    isRequired: false,
    category: 'instagram'
  },
  'leads_retrieval': {
    nameKey: 'metaPermissions.details.leads_retrieval.name',
    descriptionKey: 'metaPermissions.details.leads_retrieval.description',
    impactKey: 'metaPermissions.details.leads_retrieval.impact',
    isRequired: false,
    category: 'business'
  },
  'whatsapp_business_management': {
    nameKey: 'metaPermissions.details.whatsapp_business_management.name',
    descriptionKey: 'metaPermissions.details.whatsapp_business_management.description',
    impactKey: 'metaPermissions.details.whatsapp_business_management.impact',
    isRequired: false,
    category: 'business'
  },
  'instagram_manage_messages': {
    nameKey: 'metaPermissions.details.instagram_manage_messages.name',
    descriptionKey: 'metaPermissions.details.instagram_manage_messages.description',
    impactKey: 'metaPermissions.details.instagram_manage_messages.impact',
    isRequired: false,
    category: 'instagram'
  },
  'pages_manage_ads': {
    nameKey: 'metaPermissions.details.pages_manage_ads.name',
    descriptionKey: 'metaPermissions.details.pages_manage_ads.description',
    impactKey: 'metaPermissions.details.pages_manage_ads.impact',
    isRequired: false,
    category: 'advertising'
  },
  'pages_manage_posts': {
    nameKey: 'metaPermissions.details.pages_manage_posts.name',
    descriptionKey: 'metaPermissions.details.pages_manage_posts.description',
    impactKey: 'metaPermissions.details.pages_manage_posts.impact',
    isRequired: false,
    category: 'content'
  },
  'instagram_shopping_tag_products': {
    nameKey: 'metaPermissions.details.instagram_shopping_tag_products.name',
    descriptionKey: 'metaPermissions.details.instagram_shopping_tag_products.description',
    impactKey: 'metaPermissions.details.instagram_shopping_tag_products.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_branded_content_creator': {
    nameKey: 'metaPermissions.details.instagram_branded_content_creator.name',
    descriptionKey: 'metaPermissions.details.instagram_branded_content_creator.description',
    impactKey: 'metaPermissions.details.instagram_branded_content_creator.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_branded_content_brand': {
    nameKey: 'metaPermissions.details.instagram_branded_content_brand.name',
    descriptionKey: 'metaPermissions.details.instagram_branded_content_brand.description',
    impactKey: 'metaPermissions.details.instagram_branded_content_brand.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_branded_content_ads_brand': {
    nameKey: 'metaPermissions.details.instagram_branded_content_ads_brand.name',
    descriptionKey: 'metaPermissions.details.instagram_branded_content_ads_brand.description',
    impactKey: 'metaPermissions.details.instagram_branded_content_ads_brand.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_manage_events': {
    nameKey: 'metaPermissions.details.instagram_manage_events.name',
    descriptionKey: 'metaPermissions.details.instagram_manage_events.description',
    impactKey: 'metaPermissions.details.instagram_manage_events.impact',
    isRequired: false,
    category: 'instagram'
  },
  'instagram_manage_upcoming_events': {
    nameKey: 'metaPermissions.details.instagram_manage_upcoming_events.name',
    descriptionKey: 'metaPermissions.details.instagram_manage_upcoming_events.description',
    impactKey: 'metaPermissions.details.instagram_manage_upcoming_events.impact',
    isRequired: false,
    category: 'instagram'
  },
  'manage_fundraisers': {
    nameKey: 'metaPermissions.details.manage_fundraisers.name',
    descriptionKey: 'metaPermissions.details.manage_fundraisers.description',
    impactKey: 'metaPermissions.details.manage_fundraisers.impact',
    isRequired: false,
    category: 'other'
  },
  'publish_video': {
    nameKey: 'metaPermissions.details.publish_video.name',
    descriptionKey: 'metaPermissions.details.publish_video.description',
    impactKey: 'metaPermissions.details.publish_video.impact',
    isRequired: false,
    category: 'content'
  },
  'private_computation_access': {
    nameKey: 'metaPermissions.details.private_computation_access.name',
    descriptionKey: 'metaPermissions.details.private_computation_access.description',
    impactKey: 'metaPermissions.details.private_computation_access.impact',
    isRequired: false,
    category: 'other'
  },
  'attribution_read': {
    nameKey: 'metaPermissions.details.attribution_read.name',
    descriptionKey: 'metaPermissions.details.attribution_read.description',
    impactKey: 'metaPermissions.details.attribution_read.impact',
    isRequired: false,
    category: 'advertising'
  },
  'page_events': {
    nameKey: 'metaPermissions.details.page_events.name',
    descriptionKey: 'metaPermissions.details.page_events.description',
    impactKey: 'metaPermissions.details.page_events.impact',
    isRequired: false,
    category: 'page'
  },
  'commerce_account_read_settings': {
    nameKey: 'metaPermissions.details.commerce_account_read_settings.name',
    descriptionKey: 'metaPermissions.details.commerce_account_read_settings.description',
    impactKey: 'metaPermissions.details.commerce_account_read_settings.impact',
    isRequired: false,
    category: 'business'
  },
  'commerce_account_manage_orders': {
    nameKey: 'metaPermissions.details.commerce_account_manage_orders.name',
    descriptionKey: 'metaPermissions.details.commerce_account_manage_orders.description',
    impactKey: 'metaPermissions.details.commerce_account_manage_orders.impact',
    isRequired: false,
    category: 'business'
  },
  'commerce_account_read_orders': {
    nameKey: 'metaPermissions.details.commerce_account_read_orders.name',
    descriptionKey: 'metaPermissions.details.commerce_account_read_orders.description',
    impactKey: 'metaPermissions.details.commerce_account_read_orders.impact',
    isRequired: false,
    category: 'business'
  },
  'commerce_account_read_reports': {
    nameKey: 'metaPermissions.details.commerce_account_read_reports.name',
    descriptionKey: 'metaPermissions.details.commerce_account_read_reports.description',
    impactKey: 'metaPermissions.details.commerce_account_read_reports.impact',
    isRequired: false,
    category: 'business'
  },
  'whatsapp_business_messaging': {
    nameKey: 'metaPermissions.details.whatsapp_business_messaging.name',
    descriptionKey: 'metaPermissions.details.whatsapp_business_messaging.description',
    impactKey: 'metaPermissions.details.whatsapp_business_messaging.impact',
    isRequired: false,
    category: 'business'
  },

  'whatsapp_business_manage_events': {
    nameKey: 'metaPermissions.details.whatsapp_business_manage_events.name',
    descriptionKey: 'metaPermissions.details.whatsapp_business_manage_events.description',
    impactKey: 'metaPermissions.details.whatsapp_business_manage_events.impact',
    isRequired: false,
    category: 'business'
  },
  'pages_manage_engagement': {
    nameKey: 'metaPermissions.details.pages_manage_engagement.name',
    descriptionKey: 'metaPermissions.details.pages_manage_engagement.description',
    impactKey: 'metaPermissions.details.pages_manage_engagement.impact',
    isRequired: false,
    category: 'page'
  }
};

// Define the set of valid permission keys for the PERMISSION_IMPACT_MAP
export type MetaPermissionKey = 
  | 'instagram_basic'
  | 'instagram_manage_insights'
  | 'instagram_manage_comments'
  | 'instagram_branded_content_brand'
  | 'pages_read_engagement'
  | 'pages_read_user_content'
  | 'ads_read'
  | 'ads_management';

// New interfaces for Meta data extraction

/**
 * Time range preset types
 */
export type TimeRangePreset = '7d' | '28d' | '30d' | '90d' | '180d' | '1y' | 'week' | 'month' | 'quarter' | 'lifetime' | 'custom';

/**
 * Time range for fetching data
 */
export interface TimeRange {
  since: string;
  until: string;
  start_date?: string; // Alternative to 'since' for snake_case consistency
  end_date?: string;   // Alternative to 'until' for snake_case consistency
  preset?: TimeRangePreset;
}

export interface MetaPost {
  id: string;
  message?: string;
  created_time: string;
  permalink_url: string;
  type?: 'image' | 'video' | 'carousel_album';
  status_type?: string;
  media_product_type?: 'FEED' | 'STORY' | 'REELS' | 'AD' | string;
  platform: 'facebook' | 'instagram';
  attachments?: Array<{
    media_type: string;
    url: string;
    id?: string;
  }>;
}

/**
 * Extended interface for Instagram posts that includes Instagram-specific metrics
 */
export interface InstagramPost extends MetaPost {
  platform: 'instagram';
  instagram_metrics?: {
    like_count: number;
    comments_count: number;
    saved_count?: number;
    impressions?: number;
    reach?: number;
  };
}

export interface MetaComment {
  id: string;
  post_id: string;
  message: string;
  created_time: string;
  from: {
    id: string;
    name: string;
  };
}

export interface PostMetrics {
  post_id: string;
  likes: number;
  comments: number;
  shares: number;
  impressions: number;
  reach: number;
  engagement_rate: number;
  saved?: number; // Instagram specific
}

export interface EngagementMetrics {
  page_id: string;
  period: string;
  total_likes: number;
  total_comments: number;
  total_shares: number;
  total_impressions: number;
  average_engagement_rate: number;
  engagement_by_type: {
    [key: string]: number;
  };
  engagement_trend: {
    date: string;
    value: number;
  }[];
}

/**
 * Response structure from Instagram Business Account API
 * Used when querying /${pageId} with fields=followers_count,follows_count,media_count
 */
export interface InstagramBusinessAccountResponse {
  id: string;
  followers_count?: number;
  follows_count?: number;
  media_count?: number;
  username?: string;
  name?: string;
  biography?: string;
  profile_picture_url?: string;
  website?: string;
  is_verified?: boolean;
  // Include potential additional fields from Graph API
  [key: string]: string | number | boolean | undefined;
}

export interface FollowerData {
  page_id: string;
  total: number;
  net_follows: {
    date: string;
    value: number;
  }[];
  growth_rate: number;
}

export interface FollowerDemographics {
  page_id: string;
  age_ranges: {
    range: string;
    percentage: number;
  }[];
  gender: {
    type: string;
    percentage: number;
  }[];
  top_locations: {
    country: string;
    city: string;
    percentage: number;
  }[];
}

export interface MetaAdAccount {
  id: string;
  name: string;
  account_status: number;
  amount_spent: number;
  currency: string;
}

export interface MetaAdCampaign {
  id: string;
  name: string;
  status: string;
  objective: string;
  start_time: string;
  end_time?: string;
  budget: number;
  daily_budget?: number;
  lifetime_budget?: number;
  spend?: number;
  impressions?: number;
  clicks?: number;
  conversions?: number;
  conversion_rate: number;
  correlation_metric: number;
  correlation_data: Array<{
    date: string;
    conversion_rate: number;
    sales_value: number;
    sales?: number;
    revenue?: number;
  }>;
}

/**
 * Simplified campaign data returned by AdMetricsResponse
 */
export interface MetaCampaignBasic {
  id: string;
  name: string;
  status: string;
  spend: number;
  impressions: number;
  clicks: number;
  conversions: number;
}

export interface AdPerformance {
  campaign_id: string;
  date: string;
  impressions: number;
  reach: number;
  clicks: number;
  ctr: number;
  cpc: number;
  spend: number;
  conversions: number;
  cost_per_conversion: number;
  roi: number;
  publisher_platform?: string;
  platform_position?: string;
  views?: number;
}

/**
 * Instagram-specific metrics interface
 */
export interface InstagramMetrics {
  organic_reach: number;
  organic_impressions: number;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    saves: number;
  };
  profile_activity: {
    visits: number;
    messages: number;
    link_clicks: number;
  };
}

/**
 * Combined metrics for Instagram
 */
export interface InstagramAdMetrics {
  ad_metrics: {
    spend: number;
    impressions: number;
    reach: number;
    clicks: number;
    conversions: number;
    ctr: number;
    cpc: number;
    cost_per_conversion: number;
    roi: number;
    account_currency?: string;
  };
  organic_metrics: InstagramMetrics;
  combined: {
    total_impressions: number;
    total_reach: number;
  };
  source: 'ads_only' | 'organic_only' | 'combined';
  daily_metrics?: Array<{
    date: string;
    spend: number;
    impressions: number;
    clicks: number;
    conversions: number;
  }>;
  campaigns?: MetaCampaignBasic[];
}

// Insight interfaces for RAG processing

/**
 * Meta Insight interface for AI-generated insights
 */
export interface MetaInsight {
  id: string;
  page_id?: string;
  store_id?: string;
  type?: string;
  title?: string;
  text?: string;
  confidence?: number;
  created_at?: string;
  recommendations?: string[];
  is_mock?: boolean;
  insight_type: string;
  insight_text: string;
  timestamp: string;
  source_data_type: string;
  source_data_id: string;
  confidence_score?: number;
}

/**
 * Content Insight interface for insights generated from post content
 */
export interface ContentInsight extends MetaInsight {
  insight_type: 'content_analysis';
  post_ids: string[];
  topics?: string[];
  sentiment_score?: number;
}

/**
 * Comment Insight interface for insights generated from comments
 */
export interface CommentInsight extends MetaInsight {
  insight_type: 'comment_analysis';
  comment_ids: string[];
  sentiment_distribution?: {
    positive: number;
    neutral: number;
    negative: number;
  };
}

/**
 * Correlation Insight interface for insights correlating different data sources
 */
export interface CorrelationInsight extends MetaInsight {
  insight_type: 'correlation';
  correlation_score: number;
  data_source_a: string;
  data_source_b: string;
}

/**
 * Advanced Correlation Insight interface for multi-dimensional correlation analysis
 */
export interface AdvancedCorrelationInsight extends MetaInsight {
  insight_type: 'advanced_correlation';
  correlation_metrics: Record<string, Record<string, number>>;
  time_lag: number;
  segment?: string;
  dimensions?: string[];
  page_id?: string;
  correlation_data?: {
    dimensions: string[];
    values: number[][];
    time_lag: number;
    segment: string;
  };
}

/**
 * Forecast Insight interface for predictive analytics
 */
export interface ForecastInsight extends MetaInsight {
  insight_type: 'forecast';
  metric_name: string;
  forecast_values: Array<{
    date: string;
    value: number;
  }>;
  page_id?: string;
  forecast_data?: {
    metric: string;
    historical: Array<{
      date: string;
      value: number;
    }>;
    forecast: Array<{
      date: string;
      value: number;
      lower_bound?: number;
      upper_bound?: number;
    }>;
    accuracy?: number;
    trend?: string;
  };
  confidence_intervals?: Array<{
    date: string;
    lower: number;
    upper: number;
  }>;
  forecast_accuracy?: number;
  forecast_period: string;
}

/**
 * MetaAudience interface for audience demographics data
 */
export interface MetaAudience {
  age_gender: Record<string, number>;
  countries: Record<string, number>;
  cities?: Record<string, number>;
  languages: Record<string, number>;
  interests: Record<string, number>;
  total_followers?: number;
}

/**
 * Extended MetaInsight interface for metrics data
 * This is used by the MetaMetricsPanel component
 */
export interface MetricInsight {
  name?: string; // Optional: name of the metric (e.g., 'reach', 'impressions')
  value: number | Record<string, number> | Array<number | Record<string, number>> | Record<string, Array<number | Record<string, number>>>; // Can be a single value or a breakdown
  end_time: string; // ISO 8601 date string
}

export interface StoreSalesData {
  total_sales: number;
  order_count: number;
  avg_order_value: number;
  daily_data: Array<{
    date: string;
    sales: number;
    orders: number;
    revenue?: number;
  }>;
}

// Replace MetricType type alias with enum
export enum MetricType {
  PAGE_IMPRESSIONS = 'page_impressions',
  PAGE_ENGAGEMENT = 'page_engagement',
  PAGE_FANS = 'page_fans',
  PAGE_VIEWS = 'page_views',
  PAGE_VIEWS_TOTAL = 'page_views_total',
  PAGE_IMPRESSIONS_UNIQUE = 'page_impressions_unique',
  POST_INSIGHTS = 'post_insights', // Added to support its usage
  // Add any other metric types used as string literals elsewhere if they should be part of this enum
  TOTAL_REACH = 'total_reach', // Example, was in an earlier version of MetricType enum
  POST_ENGAGEMENT = 'post_engagement', // Example
  VIDEO_VIEWS = 'video_views', // Example
  PROFILE_VIEWS = 'profile_views', // Example
  FOLLOWER_COUNT = 'follower_count' // Example
}

export interface MetricMap {
  [key: string]: string; // Allow string indexing with type safety
}

// Generic API response interfaces
export interface MetaApiErrorDetails {
  code: number;
  message: string;
  subcode?: number;
  type?: string;
}

export interface MetaApiResponse<T> {
  data?: T;
  error?: MetaApiErrorDetails;
}

export interface MetaApiPaginatedResponse<T> extends MetaApiResponse<T[]> {
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
    next?: string;
    previous?: string;
  };
}

// Facebook specific responses
export interface DebugTokenData {
  app_id: string;
  type: string;
  application: string;
  data_access_expires_at: number;
  expires_at: number;
  is_valid: boolean;
  scopes?: string[];
  user_id?: string;
}

export type DebugTokenResponse = MetaApiResponse<DebugTokenData>;

export interface FacebookPermission {
  permission: string;
  status: string;
}

export type FacebookPermissionsResponse = MetaApiResponse<FacebookPermission[]>;

// Callback function types for FB API
export type FacebookApiCallback<T> = (response: MetaApiResponse<T>) => void;

declare global {
  interface Window {
    FB: {
      init: (params: {
        appId: string;
        cookie: boolean;
        xfbml: boolean;
        version: string;
      }) => void;
      login: (callback: (response: MetaAuthResponse) => void, params: { 
        scope: string;
        return_scopes?: boolean;
      }) => void;
      api: (
        path: string,
        method?: string,
        params?: Record<string, unknown>,
        callback?: (response: Record<string, unknown>) => void
      ) => void;
      getLoginStatus: (callback: (response: MetaAuthResponse) => void) => void;
      logout: (callback: () => void) => void;
    };
    fbAsyncInit: () => void;
    fbPromise: Promise<unknown>;
  }
}

/**
 * Structure for Instagram follower demographics data
 * This is returned from the Business Discovery API or Insights API
 */
export interface InstagramFollowerDemographics {
  // Age demographic breakdown
  age_ranges: {
    range: string;     // Age range (e.g. "18-24", "25-34")
    percentage: number; // Percentage of followers in this range
  }[];
  
  // Gender demographic breakdown
  gender: {
    type: string;       // Gender category (e.g. "male", "female", "other")
    percentage: number; // Percentage of followers of this gender
  }[];
  
  // Geographic distribution by country 
  countries: {
    name: string;      // Country name
    percentage: number; // Percentage of followers from this country
  }[];
  
  // Geographic distribution by city
  cities: {
    name: string;      // City name
    percentage: number; // Percentage of followers from this city
  }[];
}

/**
 * Structure for a single Facebook Insight value
 * This represents a data point for a specific date/time
 */
export interface FacebookInsightValue {
  value: number;
  end_time: string;
}

/**
 * Structure for a Facebook Insight metric
 * This is used for page_fans, page_fan_adds, and page_fan_removes
 */
export interface FacebookInsightMetric {
  name: string;
  period: string;
  values: FacebookInsightValue[];
  title?: string;
  description?: string;
  id?: string;
}

/**
 * Structure for Facebook Page Insights API response
 * This is returned when querying /${pageId}/insights with metrics like page_fans
 */
export interface FacebookPageInsightsResponse {
  data: FacebookInsightMetric[];
  paging?: {
    previous?: string;
    next?: string;
  };
}

/**
 * Parameters for Facebook Page Insights API requests
 */
export interface FacebookPageInsightsParams extends Record<string, unknown> {
  metric: string;
  period: string;
  since?: string;
  until?: string;
  access_token?: string;
}

/**
 * Structure for a single Instagram Post Insight value
 * This represents the value of a metric at a specific point
 */
export interface InstagramPostInsightValue {
  value: number;
}

/**
 * Structure for an Instagram Post Insight data point
 * This represents a single metric with its values
 */
export interface InstagramPostInsightData {
  name: string; // e.g., 'reach', 'impressions', 'likes', 'comments', 'saved', 'shares', 'total_interactions', 'messaging_conversations_started'
  period: string; // e.g., 'lifetime'
  values: InstagramPostInsightValue[];
  title: string;
  description: string;
  id: string; // e.g., {media_id}/insights/{metric}/{period}
}

/**
 * Structure for the Instagram Post Insights API response
 * This is returned when querying /{media_id}/insights
 */
export interface InstagramPostInsightsResponse {
  data: InstagramPostInsightData[];
  // Add paging if needed, though usually not required for lifetime post metrics
}

/**
 * Structure for processed, structured Instagram post insights data
 * This is returned by our service function and used by UI components
 */
export interface ProcessedInstagramPostInsights {
  media_id: string;
  reach?: number;
  impressions?: number;
  likes?: number; // Often available directly on media object, but can be fetched via insights too
  comments?: number; // Often available directly on media object
  saved?: number;
  shares?: number; // May not always be available
  total_interactions?: number;
  messaging_conversations_started?: number;
  // Add other relevant metrics based on API documentation and needs
}

// Define FacebookDailyAdMetric interface for daily_metrics
export interface FacebookDailyAdMetric {
  date: string;
  spend: number;
  views: number;
  reach: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
  impressions: number; // Add impressions field
} 