from pydantic import BaseModel, Field
from typing import Optional

class ChatFeedbackInput(BaseModel):
    feedback_text: str = Field(..., description="The feedback text provided by the user.")
    likes: int = Field(0, ge=0, description="Number of likes.")
    dislikes: int = Field(0, ge=0, description="Number of dislikes.")
    source: str = Field("chat", description="Source of the feedback (chat or dashboard)") 