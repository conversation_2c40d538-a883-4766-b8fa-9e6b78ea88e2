from datetime import datetime
from typing import List, Optional, Dict, Union, Any
from pydantic import BaseModel, Field

class MetaInsightBase(BaseModel):
    """Base model for Meta insights"""
    insight_type: str
    insight_text: str
    source_data_type: str
    source_data_id: str
    confidence_score: Optional[float] = None
    recommendations: Optional[List[str]] = None
    is_mock: bool = False

class ContentInsightCreate(MetaInsightBase):
    """Model for creating content insights"""
    insight_type: str = "content_analysis"
    post_ids: List[str]
    topics: Optional[List[str]] = None
    sentiment_score: Optional[float] = None

class CommentInsightCreate(MetaInsightBase):
    """Model for creating comment insights"""
    insight_type: str = "comment_analysis"
    comment_ids: List[str]
    sentiment_distribution: Optional[Dict[str, float]] = None

class EngagementInsightCreate(MetaInsightBase):
    """Model for creating engagement insights"""
    insight_type: str = "engagement"
    engagement_metrics: Dict[str, Union[float, int]]

class AudienceInsightCreate(MetaInsightBase):
    """Model for creating audience insights"""
    insight_type: str = "audience"
    audience_metrics: Dict[str, Union[float, int, Dict[str, float]]]

class CorrelationInsightCreate(MetaInsightBase):
    """Model for creating correlation insights"""
    insight_type: str = "correlation"
    correlation_score: float
    data_source_a: str
    data_source_b: str

class MetaInsight(MetaInsightBase):
    """Model for Meta insights with ID and timestamp"""
    id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

class ContentInsight(MetaInsight, ContentInsightCreate):
    """Complete content insight model"""
    pass

class CommentInsight(MetaInsight, CommentInsightCreate):
    """Complete comment insight model"""
    pass

class EngagementInsight(MetaInsight, EngagementInsightCreate):
    """Complete engagement insight model"""
    pass

class AudienceInsight(MetaInsight, AudienceInsightCreate):
    """Complete audience insight model"""
    pass

class CorrelationInsight(MetaInsight, CorrelationInsightCreate):
    """Complete correlation insight model"""
    pass

class AdvancedCorrelationInsightCreate(MetaInsightBase):
    """Model for creating advanced correlation insights"""
    insight_type: str = "advanced_correlation"
    correlation_metrics: Dict[str, Dict[str, float]]
    time_lag: int = 0
    segment: Optional[str] = None
    dimensions: Optional[List[str]] = None

class ForecastInsightCreate(MetaInsightBase):
    """Model for creating forecast insights"""
    insight_type: str = "forecast"
    metric_name: str
    forecast_values: List[Dict[str, float]]
    confidence_intervals: Optional[List[Dict[str, Dict[str, float]]]] = None
    forecast_accuracy: Optional[float] = None
    forecast_period: str

class AdvancedCorrelationInsight(MetaInsight, AdvancedCorrelationInsightCreate):
    """Complete advanced correlation insight model"""
    pass

class ForecastInsight(MetaInsight, ForecastInsightCreate):
    """Complete forecast insight model"""
    pass

class ProcessPostsRequest(BaseModel):
    """Request model for processing posts"""
    posts: List[Dict[str, Any]]

class ProcessCommentsRequest(BaseModel):
    """Request model for processing comments"""
    comments: List[Dict[str, Any]]

class EngagementInsightsRequest(BaseModel):
    """Request model for engagement insights"""
    metrics: Dict[str, Any]

class AudienceInsightsRequest(BaseModel):
    """Request model for audience insights"""
    followers: Dict[str, Any]
    demographics: Dict[str, Any]

class AdInsightsRequest(BaseModel):
    """Request model for ad insights"""
    performance: List[Dict[str, Any]]

class CorrelatePostsSalesRequest(BaseModel):
    """Request model for correlating posts with sales"""
    posts: List[Dict[str, Any]]
    sales: Dict[str, Any]

class AdvancedCorrelationRequest(BaseModel):
    """Request model for advanced correlation analysis"""
    meta_data: Dict[str, Any]
    store_data: Dict[str, Any]
    time_lag: Optional[int] = 0
    segment: Optional[str] = None
    dimensions: Optional[List[str]] = None

class ForecastRequest(BaseModel):
    """Request model for forecasting metrics"""
    metric_type: str
    historical_data: List[Dict[str, Any]]
    forecast_periods: int = 30
    confidence_level: Optional[float] = 0.95

class SEORecommendation(BaseModel):
    title: str
    description: str
    priority: Optional[str] = None # e.g., 'High', 'Medium', 'Low'
    category: Optional[str] = None # e.g., 'Content', 'Keywords', 'Technical'

class SEOInsightsResponse(BaseModel):
    recommendations: List[SEORecommendation]
    generated_at: datetime
    model_used: str

    class Config:
        protected_namespaces = ()

class CategorySummary(BaseModel):
    total_products_with_categories: Optional[int] = None
    unique_categories: Optional[int] = None
    unique_subcategories: Optional[int] = None
    top_categories: Optional[List[Dict[str, Union[str, int]]]] = None
    top_featured_categories: Optional[List[Dict[str, Union[str, int]]]] = None
    top_sale_categories: Optional[List[Dict[str, Union[str, int]]]] = None
    category_distribution: Optional[Dict[str, float]] = None 