"""
Serialization utilities for MongoDB objects.

This module provides functions to safely serialize MongoDB documents
for JSON responses, including handling of ObjectId fields.
"""

import json
from datetime import datetime
from bson import ObjectId
from typing import Any, Dict, List, Union

class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles MongoDB ObjectId and datetime objects."""
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

def serialize_mongo_doc(doc: Union[Dict[str, Any], ObjectId, datetime, List[Any], Any]) -> Any:
    """
    Recursively convert all ObjectId fields in a MongoDB document to strings.
    
    Args:
        doc: MongoDB document or value to serialize
        
    Returns:
        Serialized document with ObjectId converted to strings
    """
    if doc is None:
        return None
        
    # Handle different types directly for better recursion
    if isinstance(doc, ObjectId):
        return str(doc)
    elif isinstance(doc, datetime):
        return doc.isoformat()
    elif isinstance(doc, list):
        return serialize_mongo_list(doc)
    elif isinstance(doc, dict):
        result = {}
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, dict):
                result[key] = serialize_mongo_doc(value)
            elif isinstance(value, list):
                result[key] = serialize_mongo_list(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result
    else:
        # Return primitive types as is
        return doc

def serialize_mongo_list(items: List[Any]) -> List[Any]:
    """
    Recursively convert all ObjectId fields in a list of MongoDB items to strings.
    
    Args:
        items: List of MongoDB items to serialize
        
    Returns:
        Serialized list with ObjectId converted to strings
    """
    if items is None:
        return None
        
    result = []
    for item in items:
        if isinstance(item, ObjectId):
            result.append(str(item))
        elif isinstance(item, dict):
            result.append(serialize_mongo_doc(item))
        elif isinstance(item, list):
            result.append(serialize_mongo_list(item))
        elif isinstance(item, datetime):
            result.append(item.isoformat())
        else:
            result.append(item)
    return result

def json_serialize(obj: Union[Dict[str, Any], List[Any]]) -> str:
    """
    Serialize an object to JSON with proper handling of MongoDB types.
    
    Args:
        obj: Object to serialize
        
    Returns:
        JSON string
    """
    return json.dumps(obj, cls=JSONEncoder) 