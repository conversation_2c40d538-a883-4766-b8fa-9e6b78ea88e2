#!/bin/bash

# CloudFront Configuration Validation Script
# Checks all files for correct CloudFront domain usage

echo "🔍 CloudFront Configuration Validation"
echo "========================================"

# Define the correct domains
FRONTEND_DOMAIN="d37am3rrp4a9iv.cloudfront.net"
BACKEND_DOMAIN="d1w62wlepuin1r.cloudfront.net"

echo "✅ Expected domains:"
echo "   Frontend: https://$FRONTEND_DOMAIN"
echo "   Backend:  https://$BACKEND_DOMAIN"
echo ""

# Function to search for patterns
search_pattern() {
    local pattern=$1
    local description=$2
    echo "🔍 Searching for: $description"
    
    # Search in all relevant files
    grep -r "$pattern" . \
        --include="*.ts" \
        --include="*.js" \
        --include="*.yaml" \
        --include="*.yml" \
        --include="*.json" \
        --include="*.py" \
        --include="*.sh" \
        --include="*.md" \
        --exclude-dir=node_modules \
        --exclude-dir=dist \
        --exclude-dir=.git \
        --exclude-dir=venv \
        2>/dev/null || echo "   ✅ Not found (good)"
    echo ""
}

# Check for wrong domains
echo "🚨 Checking for INCORRECT domains:"
search_pattern "d37am3rrp4a9lv" "Wrong frontend domain (lv instead of iv)"
search_pattern "dlw62wlepuinlr" "Wrong backend domain (missing 1s)"

# Check for correct domains
echo "✅ Checking for CORRECT domains:"
search_pattern "$FRONTEND_DOMAIN" "Correct frontend domain"
search_pattern "$BACKEND_DOMAIN" "Correct backend domain"

# Check specific configuration files
echo "📋 Configuration Files Check:"
echo ""

echo "1. Kubernetes ConfigMap:"
if grep -q "$FRONTEND_DOMAIN" backend/d-unit-backend-configmap.yaml && \
   grep -q "$BACKEND_DOMAIN" backend/d-unit-backend-configmap.yaml; then
    echo "   ✅ ConfigMap has correct domains"
else
    echo "   ❌ ConfigMap missing correct domains"
fi

echo "2. Frontend Environment:"
if grep -q "$BACKEND_DOMAIN" frontend/src/config/environment.ts; then
    echo "   ✅ Frontend environment has correct backend domain"
else
    echo "   ❌ Frontend environment missing correct backend domain"
fi

echo "3. Production Environment Files:"
if [ -f "frontend/.env.production" ]; then
    echo "   📄 Found frontend/.env.production"
    if grep -q "$BACKEND_DOMAIN" frontend/.env.production; then
        echo "   ✅ Has correct backend domain"
    else
        echo "   ❌ Missing correct backend domain"
    fi
else
    echo "   ⚠️  No frontend/.env.production file found"
fi

if [ -f "backend/.env.production" ]; then
    echo "   📄 Found backend/.env.production"
    if grep -q "$FRONTEND_DOMAIN" backend/.env.production; then
        echo "   ✅ Has correct frontend domain"
    else
        echo "   ❌ Missing correct frontend domain"
    fi
else
    echo "   ⚠️  No backend/.env.production file found"
fi

echo ""
echo "🎯 Summary:"
echo "   Frontend domain: $FRONTEND_DOMAIN"
echo "   Backend domain:  $BACKEND_DOMAIN"
echo ""
echo "💡 Tip: Update your deployment scripts with these exact domains"
echo "   Also update CloudFront distribution IDs if needed"