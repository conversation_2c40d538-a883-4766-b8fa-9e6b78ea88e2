#!/usr/bin/env python3
"""
Test script to verify logging configuration works correctly.
Run this to test if third-party library logs are properly filtered.
"""

import logging
import logging.config
import os
from config.logging_config import LOGGING_CONFIG

# Apply the logging configuration
logging.config.dictConfig(LOGGING_CONFIG)

# Test different loggers
logger = logging.getLogger(__name__)
passlib_logger = logging.getLogger('passlib.handlers.bcrypt')
httpcore_logger = logging.getLogger('httpcore.connection')
openai_logger = logging.getLogger('openai._base_client')
app_logger = logging.getLogger('services.auth')

def test_logging():
    """Test various log levels from different loggers"""
    print("Testing logging configuration...")
    print("=" * 50)
    
    # Test application logs (should appear)
    logger.info("✅ Application INFO log - should appear")
    logger.warning("⚠️ Application WARNING log - should appear")
    logger.error("❌ Application ERROR log - should appear")
    
    # Test service logs (should appear)
    app_logger.info("✅ Service INFO log - should appear")
    app_logger.debug("🔍 Service DEBUG log - may not appear depending on level")
    
    # Test third-party library logs (should be filtered)
    print("\nTesting third-party library logs (should be filtered):")
    passlib_logger.debug("🚫 Passlib DEBUG - should NOT appear")
    passlib_logger.info("🚫 Passlib INFO - should NOT appear")
    passlib_logger.warning("⚠️ Passlib WARNING - should appear")
    
    httpcore_logger.debug("🚫 Httpcore DEBUG - should NOT appear")
    httpcore_logger.info("🚫 Httpcore INFO - should NOT appear") 
    httpcore_logger.warning("⚠️ Httpcore WARNING - should appear")
    
    openai_logger.debug("🚫 OpenAI DEBUG - should NOT appear")
    openai_logger.info("🚫 OpenAI INFO - should NOT appear")
    openai_logger.warning("⚠️ OpenAI WARNING - should appear")
    
    # Check if log files were created
    logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
    app_log_path = os.path.join(logs_dir, 'application.log')
    error_log_path = os.path.join(logs_dir, 'errors.log')
    
    print(f"\nChecking log files:")
    print(f"Logs directory: {logs_dir}")
    print(f"Application log exists: {os.path.exists(app_log_path)}")
    print(f"Error log exists: {os.path.exists(error_log_path)}")
    
    if os.path.exists(app_log_path):
        print(f"Application log size: {os.path.getsize(app_log_path)} bytes")
    
    if os.path.exists(error_log_path):
        print(f"Error log size: {os.path.getsize(error_log_path)} bytes")
    
    print("\n" + "=" * 50)
    print("Logging test completed!")
    print("If third-party DEBUG/INFO logs appeared above, the configuration needs adjustment.")

if __name__ == "__main__":
    test_logging() 