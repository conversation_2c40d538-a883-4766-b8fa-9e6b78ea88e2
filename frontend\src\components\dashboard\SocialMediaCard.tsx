import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Link,
  Box,
  Collapse,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Facebook, 
  Instagram, 
  YouTube, 
  Link as LinkIcon,
  Twitter as TwitterIcon
} from '@mui/icons-material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

import { StoreAnalysis, Analysis } from '../../services/storeService';
import { useTranslation } from 'react-i18next';
import { getLocalizedDbText } from '../../utils/localizationUtils';

// Define base URLs for social media platforms
const platformBaseUrls: { [key: string]: string } = {
  facebook: 'https://www.facebook.com/',
  instagram: 'https://www.instagram.com/',
  tiktok: 'https://www.tiktok.com/',
  x_twitter: 'https://twitter.com/', // Using twitter.com as it redirects
  youtube: 'https://www.youtube.com/',
  // Add other platforms if necessary
};

interface SocialMediaCardProps {
  title: string;
  socialMedia?: StoreAnalysis['social_media'];
  analysisData: Analysis | null | undefined;
}

const platformIcons: { [key: string]: React.ElementType } = {
  facebook: Facebook,
  instagram: Instagram,
  tiktok: LinkIcon, // Using LinkIcon as fallback for TikTok
  x_twitter: TwitterIcon,
  youtube: YouTube,
  pixel_id: LinkIcon
};

const platformLabels: { [key: string]: string } = {
  facebook: 'Facebook',
  instagram: 'Instagram',
  tiktok: 'TikTok',
  x_twitter: 'X (Twitter)',
  youtube: 'YouTube',
  pixel_id: 'Meta Pixel ID'
};

// Helper function to format strategy text with bullet points
const formatStrategyText = (text: string): React.ReactNode => {
  if (!text) return null;

  const lines = text.split('\\n'); // Split by newline first
  const elements: React.ReactNode[] = [];
  let currentSectionPoints: string[] = [];

  // Helper to add accumulated points under a section to the elements
  const flushCurrentSectionPoints = (keyPrefix: string) => {
    if (currentSectionPoints.length > 0) {
      // Join points, then remove leading bullets from each line in the joined string
      const pointsText = currentSectionPoints.join('\\n').replace(/^[•*-]\\s*/gm, '');
      if (pointsText.trim()) { // Only add if there's actual content after cleaning
        elements.push(
          <Typography key={`${keyPrefix}-points`} variant="body2" paragraph sx={{ whiteSpace: 'pre-line', pl: 2, mb: 1 }}>
            {pointsText}
          </Typography>
        );
      }
      currentSectionPoints = [];
    }
  };

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    // Regex to identify lines that act as headers (e.g., "Recomendaciones:", "Estado actual:")
    // This regex looks for a line that is not just a bullet point itself and ends with a colon.
    // It also allows for lines that might just be a title without points immediately after.
    const isSectionHeader = /^[A-Za-zÀ-ÿ0-9 ]+:\\s*$/.test(trimmedLine) && !/^[•*-]/.test(trimmedLine);

    if (isSectionHeader) {
      flushCurrentSectionPoints(`section-${index -1}`); // Flush previous section points before starting a new header
      elements.push(
        <Typography
          key={`header-${index}`}
          variant="subtitle1"
          sx={{ fontWeight: 'medium', mt: elements.length > 0 ? 2 : 0, mb: 0.5 }}
        >
          {trimmedLine}
        </Typography>
      );
    } else if (trimmedLine) { // Only add non-empty lines that are not headers
      currentSectionPoints.push(trimmedLine); // Keep original line; cleaning happens in flushCurrentSectionPoints
    }
  });

  flushCurrentSectionPoints('final-section'); // Flush any remaining points

  if (elements.length === 0 && text.trim()) {
    // Fallback: if no headers were identified, but there is text, render it as a single block.
    // This also handles texts that might be a single paragraph or a list without explicit headers.
    const cleanedText = text.replace(/^[•*-]\\s*/gm, ''); // Remove bullets from the whole text
    if (cleanedText.trim()) { // Only render if there's content after cleaning
      return (
        <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
          {cleanedText}
        </Typography>
      );
    }
    return null; // If no content after cleaning, render nothing
  }
  
  // If, after all processing, no elements were generated (e.g., input was only whitespace or only empty bullet points)
  if (elements.length === 0) return null; 

  return <>{elements}</>;
};

// Helper function to check if a profile URL is valid
const isValidProfileUrl = (url: string | undefined | null): boolean => {
  return !!url && url.trim() !== '';
};

const SocialMediaCard: React.FC<SocialMediaCardProps> = ({ title, socialMedia, analysisData }) => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const [strategyExpanded, setStrategyExpanded] = useState(false);

  const toggleStrategy = () => setStrategyExpanded(!strategyExpanded);

  // Use the helper function to get the localized strategy text
  const strategyText = getLocalizedDbText(analysisData, 'social_media_strategy', currentLang);

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>{title}</Typography>
        {socialMedia && Object.entries(socialMedia).map(([platform, profileUrl]) => {
          const IconComponent = platformIcons[platform] || LinkIcon;
          const label = platformLabels[platform] || platform;

          // Handle Pixel ID display separately
          if (platform === 'pixel_id') {
            return (
              <Box key={platform} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <IconComponent sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" sx={{ mr: 1 }}>{label}:</Typography>
                {profileUrl ? (
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>{profileUrl}</Typography>
                ) : (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    {t('socialMediaCard.notAvailable', 'Not Available')}
                  </Typography>
                )}
              </Box>
            );
          }

          const hasValidProfile = isValidProfileUrl(profileUrl);
          
          // Explicitly calculate the URL for the href attribute
          let actualUrl = platformBaseUrls[platform] || '#'; // Default to base URL
          if (hasValidProfile) {
            actualUrl = profileUrl!.startsWith('http') ? profileUrl! : `https://${profileUrl}`;
          }
          
          // Explicitly calculate the text to display inside the link
          const displayText = hasValidProfile
            ? t('socialMediaCard.visitProfile', 'Visit Profile')
            : t('socialMediaCard.officialPage', 'Official Page');

          return (
            <Box key={platform} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <IconComponent sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" sx={{ mr: 1, minWidth: '100px' }}>{label}:</Typography>
              <Link
                href={actualUrl} // CRITICAL: Ensure href uses the calculated URL
                target="_blank"
                rel="noopener noreferrer"
                variant="body2"
                sx={{ 
                  fontWeight: 'medium',
                  textDecoration: 'underline',
                  color: 'primary.main',
                  '&:hover': { 
                    opacity: 0.8 
                  }
                }}
              >
                {displayText} {/* CRITICAL: Ensure the text inside the link uses the display text */}
              </Link>
            </Box>
          );
        })}

        {strategyText && (
          <Box sx={{ mt: 2, borderTop: '1px solid', borderColor: 'divider', pt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                  {t('socialMediaCard.strategyTitle', 'Social Media Strategy')}
                </Typography>
                <Tooltip title={t('socialMediaCard.strategyTooltip', 'AI-generated summary of the social media approach.')}>
                  <InfoOutlinedIcon fontSize="small" sx={{ ml: 0.5, color: 'text.secondary' }} />
                </Tooltip>
              </Box>
              <IconButton onClick={toggleStrategy} size="small">
                {strategyExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>
            <Collapse in={strategyExpanded}>
              <Box sx={{ mt: 1 }}>
                {formatStrategyText(strategyText)}
              </Box>
            </Collapse>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SocialMediaCard; 