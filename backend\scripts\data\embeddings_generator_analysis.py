import os
import time
import json
import logging
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Union, Any, Callable, TypeVar, ParamSpec, Awaitable, cast
from dotenv import load_dotenv
from pymongo import MongoClient
from pymongo.errors import BulkWriteError
from pymongo.operations import UpdateOne
from openai import OpenAI, AsyncOpenAI
import numpy as np
from bson.binary import Binary
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from hashlib import md5
from dataclasses import dataclass, field
import re
import traceback
from functools import wraps, lru_cache
import multiprocessing
import hashlib
from tqdm.asyncio import tqdm

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler('embeddings_analysis.log'), # Log to a separate file
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Type variables for generic decorator
P = ParamSpec('P')
T = TypeVar('T')
AsyncFunc = Callable[P, Awaitable[T]]

RETRY_CONFIG = {
    'attempts': 3,
    'min_wait': 4,
    'max_wait': 10,
    'multiplier': 1
}

# Text templates for better embedding quality
TEXT_TEMPLATES = {
    'meta_pages': "Page: {name} | Platform: {platform} | Category: {category} | Store: {store_id}",
    'meta_posts': "Post: {message} | Type: {type} | Platform: {platform} | Page: {page_id}",
    'meta_post_metrics': "Post Metrics: Likes:{likes} Comments:{comments} Shares:{shares} Impressions:{impressions} Engagement:{engagement_rate} Post:{post_id}",
    'meta_comments': "Comment: {message} | Sentiment: {sentiment_score} | Post: {post_id}",
    'meta_followers': "Followers: {total} | Growth: {growth_rate} | Page: {page_id}",
    'meta_demographics': "Demographics: Age:{age_ranges} Gender:{gender} Locations:{top_locations} Page:{page_id}",
    'meta_ad_campaigns': "Ad Campaign: {name} | Status: {status} | Objective: {objective} | Page: {page_id}",
    'meta_ad_metrics': "Ad Metrics: Impressions:{impressions} Clicks:{clicks} Conversions:{conversions} Spend:{spend} Campaign:{campaign_id}",
    'meta_insights': "Insight: {title} | {insight_text} | Recommendations: {recommendations}",
    'active_stores_cache': "Store Cache: {name} | Type: {business_type} | Metrics: {metrics}",
    'store_activity_metrics': "Store Activity: {name} | Status: {active} | Email: {email}",
}

def extract_template_fields(template: str) -> List[str]:
    """Extract field names from a template string."""
    return re.findall(r'\{(\w+)\}', template)

def create_retry_decorator() -> Callable[[AsyncFunc[P, T]], AsyncFunc[P, T]]:
    """Create appropriate retry decorator based on whether tenacity is available"""
    try:
        from tenacity import retry, stop_after_attempt, wait_exponential

        def tenacity_retry(func: AsyncFunc[P, T]) -> AsyncFunc[P, T]:
            @retry(
                stop=stop_after_attempt(RETRY_CONFIG['attempts']),
                wait=wait_exponential(
                    multiplier=RETRY_CONFIG['multiplier'],
                    min=RETRY_CONFIG['min_wait'],
                    max=RETRY_CONFIG['max_wait']
                )
            )
            @wraps(func)
            async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
                return await func(*args, **kwargs)
            return wrapper

        return tenacity_retry

    except ImportError:
        logger.warning("tenacity not installed. Using simple retry implementation.")

        def simple_retry(func: AsyncFunc[P, T]) -> AsyncFunc[P, T]:
            @wraps(func)
            async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
                for attempt in range(RETRY_CONFIG['attempts']):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        if attempt == RETRY_CONFIG['attempts'] - 1:
                            logger.error(f"Final retry attempt failed: {str(e)}")
                            raise
                        wait_time = min(
                            RETRY_CONFIG['max_wait'],
                            RETRY_CONFIG['min_wait'] * (RETRY_CONFIG['multiplier'] ** attempt)
                        )
                        logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}")
                        await asyncio.sleep(wait_time)
                # This line should ideally not be reached if the loop raises, but added for completeness
                raise RuntimeError("Retry mechanism failed unexpectedly.")
            return wrapper

        return simple_retry

# Create the retry decorator
retry_with_backoff = create_retry_decorator()

@dataclass
class EmbeddingConfig:
    """Configuration for analysis embedding generation."""
    model: str = "text-embedding-3-large"  # Changed to large model for better quality
    dimensions: int = 1024  # Maximum 3072
    batch_size: int = 1000
    api_batch_size: int = 100  # Reduced for larger embeddings
    max_concurrent: int = 10
    max_concurrent_collections: int = 3  # Parallel collection processing
    retry_attempts: int = 3
    min_retry_wait: int = 4
    max_retry_wait: int = 10
    max_text_length: int = 8000  # Maximum text length before truncation
    # Keep general exclude fields, specific ones handled in text generation
    exclude_fields: List[str] = field(default_factory=lambda: [
        "email", "phone", "address", "password", "credit_card", "access_token",
        "_id", "created_at", "updated_at", "last_updated"
    ])
    use_process_pool: bool = True
    process_pool_workers: int = max(1, multiprocessing.cpu_count() - 1)
    cache_size: int = 10000 # For embedding cache
    compression_level: int = 1
    checkpoint_interval: int = 100  # Save checkpoint every N batches

class AnalysisEmbeddingsGenerator:
    """Generates embeddings for collections in the D-Unit-AnalysisGPT database."""
    
    def __init__(self, config: Optional[EmbeddingConfig] = None):
        """Initialize the analysis embeddings generator with configuration."""
        self.config = config or EmbeddingConfig()
        self.validate_config()
        self.load_config()
        self.setup_connections()
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent)
        self.collection_semaphore = asyncio.Semaphore(self.config.max_concurrent_collections)
        self.embedding_cache = {}  # For embedding cache
        self.process_pool = ProcessPoolExecutor(max_workers=self.config.process_pool_workers) if self.config.use_process_pool else None
        self.shutdown_event = asyncio.Event()
        
        # Metrics tracking
        self.metrics = {
            'embeddings_generated': 0,
            'api_calls': 0,
            'errors': 0,
            'cache_hits': 0,
            'documents_processed': 0,
            'documents_skipped': 0
        }

    def validate_config(self):
        """Validate configuration parameters."""
        valid_dimensions = {
            "text-embedding-3-small": [512, 1536],
            "text-embedding-3-large": [256, 1024, 3072]
        }
        
        model_dims = valid_dimensions.get(self.config.model, [])
        if self.config.dimensions not in model_dims:
            raise ValueError(f"Invalid dimensions {self.config.dimensions} for model {self.config.model}. Valid: {model_dims}")
        
        assert self.config.batch_size > 0, "Batch size must be positive"
        assert self.config.api_batch_size <= 100, "API batch size too large for text-embedding-3-large"
        assert self.config.max_text_length > 0, "Max text length must be positive"
        
        logger.info(f"Configuration validated - Model: {self.config.model}, Dimensions: {self.config.dimensions}")

    def load_config(self):
        """Load configuration from environment variables."""
        # Load .env file from backend directory (parent of scripts/data)
        env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env.development')
        if os.path.exists(env_path):
            load_dotenv(env_path)
        else:
            # Fallback to searching in current directory
            load_dotenv()
        
        self.mongodb_uri = os.getenv('MONGODB_CONNECTION')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.analysis_db_name = os.getenv('MONGODB_ANALYSIS_DB', 'D-Unit-AnalysisGPT')

        if not self.mongodb_uri or not self.openai_api_key:
            raise ValueError("Missing required MongoDB or OpenAI environment variables")

        logger.info(f"Config loaded - Model: {self.config.model}, Dimensions: {self.config.dimensions}")
        logger.info(f"Analysis DB: {self.analysis_db_name}")

    def setup_connections(self):
        """Setup MongoDB and OpenAI connections with error handling."""
        try:
            self.client = MongoClient(self.mongodb_uri, serverSelectionTimeoutMS=5000)
            self.client.admin.command('ping')  # Test connection

            if self.analysis_db_name is None:
                raise ValueError("Analysis DB name (MONGODB_ANALYSIS_DB) is not set")
            self.analysis_db = self.client[self.analysis_db_name]

            self.openai_client = OpenAI(api_key=self.openai_api_key)
            self.async_openai_client = AsyncOpenAI(api_key=self.openai_api_key)

            logger.info("Successfully connected to MongoDB and initialized OpenAI clients")
        except Exception as e:
            logger.error(f"Error setting up connections: {str(e)}")
            raise

    def clean_text(self, text: str) -> str:
        """Normalize and clean text for embedding generation."""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s.,!?-]', '', text) # Keep basic punctuation
        
        # Enforce max length
        if len(text) > self.config.max_text_length:
            text = text[:self.config.max_text_length] + "..."
            
        return text.strip()

    @lru_cache(maxsize=10000)
    def compute_doc_hash(self, doc_str: str) -> str:
        """Cached version of document hash computation."""
        return md5(doc_str.encode()).hexdigest()

    def _validate_dimensions(self, dimensions: Optional[int] = None) -> int:
        """Validate and return appropriate dimensions value."""
        target_dimensions = dimensions if dimensions is not None else self.config.dimensions
        if not isinstance(target_dimensions, int) or target_dimensions <= 0:
            logger.warning(f"Invalid dimensions value: {target_dimensions}. Using config: {self.config.dimensions}")
            return self.config.dimensions
        if target_dimensions > self.config.dimensions:
            logger.warning(f"Requested {target_dimensions} exceeds max {self.config.dimensions}. Using max.")
            return self.config.dimensions
        return target_dimensions

    @retry_with_backoff
    async def generate_embeddings_batch(self, texts: List[str], dimensions: Optional[int] = None) -> List[List[float]]:
        """Generate embeddings for a batch of texts with retry logic and caching."""
        try:
            # Check cache first
            cache_key = hashlib.md5("".join(texts).encode()).hexdigest()
            if cache_key in self.embedding_cache:
                self.metrics['cache_hits'] += 1
                return self.embedding_cache[cache_key]
            
            async with self.semaphore:
                dims = self._validate_dimensions(dimensions)
                cleaned_texts = [self.clean_text(str(text)) for text in texts if text and str(text).strip()]
                if not cleaned_texts:
                    logger.warning("No valid texts in batch after cleaning.")
                    return []
                
                if self.async_openai_client is None:
                     raise RuntimeError("Async OpenAI client not initialized.")

                self.metrics['api_calls'] += 1
                response = await self.async_openai_client.embeddings.create(
                    model=self.config.model,
                    input=cleaned_texts,
                    dimensions=dims
                )
                
                embeddings = [data.embedding for data in response.data]
                
                # Cache results (with size limit)
                if len(self.embedding_cache) < self.config.cache_size:
                    self.embedding_cache[cache_key] = embeddings
                    
                return embeddings
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Error generating embeddings batch: {str(e)}")
            raise

    def normalize_l2(self, x: Union[List[float], np.ndarray]) -> np.ndarray:
        """Normalize vector to unit L2 norm."""
        x = np.array(x)
        if x.ndim == 1:
            norm = np.linalg.norm(x)
            return x / norm if norm > 0 else x
        else:
            norm = np.linalg.norm(x, 2, axis=1, keepdims=True)
            return np.where(norm == 0, x, x / norm)

    def compress_embedding(self, embedding: np.ndarray) -> Binary:
        """Compress embedding array using float16 storage."""
        try:
            # For text-embedding-3-large, just use float16 without packbits
            # as the embeddings are already high-dimensional
            embedding_f16 = embedding.astype(np.float16)
            return Binary(embedding_f16.tobytes())
        except Exception as e:
            logger.error(f"Error compressing embedding: {str(e)}")
            # Fallback to float32 if needed
            return Binary(embedding.astype(np.float32).tobytes())

    def decompress_embedding(self, binary_data: Binary, dimensions: Optional[int] = None) -> Optional[np.ndarray]:
        """Decompress stored embedding back to numpy array."""
        target_dims = dimensions if dimensions else self.config.dimensions
        try:
            # Try direct float16 interpretation (most common case)
            embedding_f16 = np.frombuffer(binary_data, dtype=np.float16)
            if len(embedding_f16) == target_dims:
                return embedding_f16
            
            # Try float32 as fallback
            embedding_f32 = np.frombuffer(binary_data, dtype=np.float32)
            if len(embedding_f32) == target_dims:
                return embedding_f32.astype(np.float16)
                
            logger.warning(f"Embedding size mismatch: expected {target_dims}, got {len(embedding_f16)}")
            return None
        except Exception as e:
            logger.error(f"Error decompressing embedding: {str(e)}")
            return None

    def ensure_string(self, value: Any) -> str:
        """Safely convert values to strings."""
        if value is None:
            return ""
        if isinstance(value, (int, float)):
            return str(value)
        if isinstance(value, dict):
            # Basic dict representation
            return ", ".join(f"{k}: {self.ensure_string(v)}" for k, v in value.items())
        if isinstance(value, list):
            # Limit list representation to avoid excessive length
            return ", ".join(self.ensure_string(item) for item in value[:10]) + ("..." if len(value) > 10 else "")
        return str(value)

    def _get_analysis_embedding_text(self, collection_name: str, doc: Dict) -> str:
        """Generate cleaned text for embedding using templates for better quality."""
        try:
            if not doc or not isinstance(doc, dict):
                logger.warning("Empty or invalid document received for analysis embedding text generation")
                return ""
            doc_copy = doc.copy()

            # Exclude sensitive/large fields FIRST
            if collection_name == 'store_activity_metrics':
                exclude_specific = [
                    'activity_metrics', 'comparison_metrics', 'login_based_revenue_metrics',
                    'last_updated_script', 'created_at', 'updated_at', 'last_updated', 'metrics',
                    'key_dates', 'customers', 'orders', 'products'
                ]
                for field in exclude_specific:
                    doc_copy.pop(field, None)

            # General exclusion for configured fields
            for field in self.config.exclude_fields:
                doc_copy.pop(field, None)

            # Use template if available
            template = TEXT_TEMPLATES.get(collection_name)
            if template:
                fields = extract_template_fields(template)
                values = {k: self.ensure_string(doc_copy.get(k, '')) for k in fields}
                text = template.format(**values)
            else:
                # Enhanced fallback logic for non-templated collections
                if collection_name == 'meta_sales_correlation':
                    # Summarize instead of full data
                    summary = doc_copy.get('sales_data', {}).get('summary', {})
                    campaign_count = len(doc_copy.get('enhanced_campaigns', []))
                    text = f"Sales Correlation: Total Orders:{self.ensure_string(summary.get('total_orders'))} Revenue:{self.ensure_string(summary.get('total_revenue'))} Campaigns:{campaign_count}"
                elif collection_name == 'product_details_cache':
                    # Summarize products
                    products_summary = f"{self.ensure_string(doc_copy.get('product_count'))} products, Avg Rating: {self.ensure_string(doc_copy.get('store_average_rating'))}"
                    text = f"Product Cache: {products_summary} | Categories: {self.ensure_string(doc_copy.get('category_summary'))}"
                elif collection_name == 'store_customers_cache':
                    # Summarize customers
                    customer_summary = f"{self.ensure_string(doc_copy.get('total_customers'))} customers, Avg Spend: {self.ensure_string(doc_copy.get('average_spend_per_customer'))}"
                    text = f"Customer Cache: {customer_summary} | Country Dist: {self.ensure_string(doc_copy.get('country_distribution'))}"
                elif collection_name == 'global_analysis':
                    text = f"Global Analysis: Summary:{self.ensure_string(doc_copy.get('analysis', {}).get('summary'))} Metrics:{self.ensure_string(doc_copy.get('metrics'))}"
                elif collection_name == 'meta_chat_context':
                    context_summary = f"{len(doc_copy.get('pages', []))} pages, {len(doc_copy.get('insights', []))} insights"
                    text = f"Meta Chat Context: {context_summary} | Engagement: {self.ensure_string(doc_copy.get('engagement_metrics'))}"
                else:
                    # Improved fallback: prioritize important fields
                    important_fields = ['name', 'title', 'type', 'status', 'category', 'platform']
                    text_parts = []
                    
                    # Add important fields first
                    for field in important_fields:
                        if field in doc_copy:
                            text_parts.append(f"{field}: {self.ensure_string(doc_copy[field])}")
                    
                    # Add other fields up to length limit
                    for k, v in doc_copy.items():
                        if k not in important_fields:
                            part = f"{k}: {self.ensure_string(v)}"
                            if len(", ".join(text_parts + [part])) < self.config.max_text_length:
                                text_parts.append(part)
                            else:
                                break
                    
                    text = ", ".join(text_parts)

            return self.clean_text(text)
        except Exception as e:
            logger.error(f"Error generating analysis embedding text for {collection_name}: {str(e)}")
            return ""

    def enumerate_analysis_collections(self) -> List[str]:
        """Return a list of all non-system collections in D-Unit-AnalysisGPT."""
        if self.analysis_db is None:
            logger.error("Analysis DB connection not initialized.")
            return []
        # Exclude system collections and potential embeddings collections
        return [name for name in self.analysis_db.list_collection_names() 
                if not name.startswith('system.') and not name.endswith('_embeddings')]

    def _cursor_to_batches(self, cursor, batch_size: int):
        """Convert cursor to batches of documents."""
        batch = []
        for doc in cursor:
            batch.append(doc)
            if len(batch) >= batch_size:
                yield batch
                batch = []
        if batch:
            yield batch

    @retry_with_backoff
    async def save_embeddings_with_retry(self, collection_name: str, bulk_operations: List):
        """Save embeddings with retry logic for transient failures."""
        target_coll = f"{collection_name}_embeddings"
        await asyncio.to_thread(
            self.analysis_db[target_coll].bulk_write,
            bulk_operations,
            ordered=False
        )

    async def _process_analysis_collection_batch_async(self, collection_name: str, documents: List[Dict], 
                                                     dimensions: Optional[int] = None, 
                                                     existing_hashes: Optional[Dict[str, str]] = None):
        """Process a batch of D-Unit-AnalysisGPT documents for embedding generation asynchronously."""
        if existing_hashes is None:
            existing_hashes = {}
            
        try:
            valid_docs = []
            texts_to_process = []
            
            for doc in documents:
                if self.shutdown_event.is_set():
                    logger.info("Shutdown requested, stopping batch processing")
                    return
                    
                doc_id = str(doc.get('_id', ''))
                if not doc_id:
                    logger.warning(f"Skipping document in {collection_name} due to missing _id")
                    continue
                    
                doc_hash = self.compute_doc_hash(json.dumps(doc, sort_keys=True, default=str))
                current_hash = existing_hashes.get(doc_id)
                
                if current_hash is None or current_hash != doc_hash:
                    text = self._get_analysis_embedding_text(collection_name, doc)
                    if text:
                        texts_to_process.append(text)
                        valid_docs.append((doc_id, doc, doc_hash))
                        self.metrics['documents_processed'] += 1
                    elif current_hash is None:
                        logger.warning(f"Document {doc_id} in {collection_name} generated no text for embedding.")
                        self.metrics['documents_skipped'] += 1
                else:
                    self.metrics['documents_skipped'] += 1
                    
            if not valid_docs:
                logger.debug(f"No new/updated documents to process in this batch for {collection_name}")
                return
                
            embeddings = []
            for i in range(0, len(texts_to_process), self.config.api_batch_size):
                batch_texts = texts_to_process[i:i + self.config.api_batch_size]
                batch_embeddings = await self.generate_embeddings_batch(batch_texts, dimensions)
                if batch_embeddings:
                    embeddings.extend(batch_embeddings)
                    self.metrics['embeddings_generated'] += len(batch_embeddings)
                await asyncio.sleep(0.1)  # Rate limiting
                
            if len(embeddings) != len(valid_docs):
                logger.error(f"Critical: Mismatch between docs ({len(valid_docs)}) and embeddings ({len(embeddings)})")
                self.metrics['errors'] += 1
                return
                
            bulk_operations = []
            for (doc_id, doc, doc_hash), embedding in zip(valid_docs, embeddings):
                embedding_array = np.array(embedding, dtype=np.float32)
                normalized_embedding = self.normalize_l2(embedding_array)
                compressed_embedding = self.compress_embedding(normalized_embedding)
                
                metadata = {
                    "type": collection_name,
                    "active": bool(doc.get("active", True)),
                    "hash": doc_hash,
                    "model": self.config.model,
                    "dimensions": len(embedding),
                    "created_at": datetime.now(timezone.utc)
                }
                
                # Use conditional update to avoid race conditions
                bulk_operations.append(
                    UpdateOne(
                        {
                            "_id": doc_id,
                            "$or": [
                                {"m.hash": {"$ne": doc_hash}},
                                {"m.hash": {"$exists": False}}
                            ]
                        },
                        {"$set": {
                            "_id": doc_id,
                            "e": compressed_embedding,
                            "m": metadata
                        }},
                        upsert=True
                    )
                )
                
            if bulk_operations:
                try:
                    await self.save_embeddings_with_retry(collection_name, bulk_operations)
                    logger.info(f"Successfully saved {len(bulk_operations)} embeddings for {collection_name}")
                except Exception as e:
                    logger.error(f"Failed to save embeddings for {collection_name}: {str(e)}")
                    self.metrics['errors'] += 1
                    
        except Exception as e:
            logger.error(f"Error processing batch for {collection_name}: {str(e)}\n{traceback.format_exc()}")
            self.metrics['errors'] += 1

    async def _load_existing_hashes(self, collection_name: str) -> Dict[str, str]:
        """Load all existing document hashes for a collection."""
        target_coll = f"{collection_name}_embeddings"
        existing_hashes = {}
        
        try:
            cursor = self.analysis_db[target_coll].find({}, {"m.hash": 1})
            for doc in cursor:
                existing_hashes[str(doc['_id'])] = doc.get('m', {}).get('hash')
            logger.info(f"Loaded {len(existing_hashes)} existing hashes for {collection_name}")
        except Exception as e:
            logger.warning(f"Could not load existing hashes for {collection_name}: {e}")
            
        return existing_hashes

    async def _process_single_analysis_collection(self, collection_name: str, dimensions: Optional[int]):
        """Process a single analysis collection with checkpointing and progress tracking."""
        checkpoint_file = f"checkpoint_{collection_name}.json"
        last_processed_id = None
        
        try:
            async with self.collection_semaphore:
                if self.analysis_db is None:
                    logger.error(f"Analysis DB connection lost before processing {collection_name}.")
                    return
                    
                # Load checkpoint if exists
                if os.path.exists(checkpoint_file):
                    try:
                        with open(checkpoint_file, 'r') as f:
                            checkpoint = json.load(f)
                            last_processed_id = checkpoint.get('last_id')
                            logger.info(f"Resuming {collection_name} from checkpoint: {last_processed_id}")
                    except Exception as e:
                        logger.warning(f"Could not load checkpoint for {collection_name}: {e}")
                
                logger.info(f"Processing {collection_name} collection from D-Unit-AnalysisGPT")
                collection = self.analysis_db[collection_name]
                
                # Count total documents
                query = {"_id": {"$gt": last_processed_id}} if last_processed_id else {}
                total_docs = collection.count_documents(query)
                
                if total_docs == 0:
                    logger.info(f"No documents to process in {collection_name}")
                    return
                    
                # Sample for very large collections
                if total_docs > 1_000_000:
                    logger.warning(f"Collection {collection_name} has {total_docs} documents. Consider sampling.")
                    
                # Load existing hashes upfront
                existing_hashes = await self._load_existing_hashes(collection_name)
                
                # Use cursor for memory efficiency
                cursor = collection.find(query).sort("_id", 1).batch_size(self.config.batch_size)
                
                total_batches = (total_docs + self.config.batch_size - 1) // self.config.batch_size
                
                with tqdm(total=total_batches, desc=f"Processing {collection_name}") as pbar:
                    batch_count = 0
                    for batch in self._cursor_to_batches(cursor, self.config.batch_size):
                        if self.shutdown_event.is_set():
                            logger.info(f"Shutdown requested, stopping {collection_name} processing")
                            break
                            
                        await self._process_analysis_collection_batch_async(
                            collection_name, batch, dimensions, existing_hashes
                        )
                        
                        batch_count += 1
                        pbar.update(1)
                        
                        # Save checkpoint periodically
                        if batch_count % self.config.checkpoint_interval == 0:
                            last_doc_id = str(batch[-1].get('_id', ''))
                            checkpoint_data = {
                                'collection': collection_name,
                                'last_id': last_doc_id,
                                'timestamp': datetime.now(timezone.utc).isoformat(),
                                'progress': f"{batch_count}/{total_batches}"
                            }
                            with open(checkpoint_file, 'w') as f:
                                json.dump(checkpoint_data, f)
                                
                # Remove checkpoint file on completion
                if os.path.exists(checkpoint_file):
                    os.remove(checkpoint_file)
                    
                logger.info(f"Completed processing {collection_name}")
                
        except Exception as e:
            logger.error(f"Error processing collection {collection_name}: {str(e)}")
            logger.debug(traceback.format_exc())

    async def process_analysis_collections(self, dimensions: Optional[int] = None):
        """Process all D-Unit-AnalysisGPT collections with parallel processing."""
        try:
            collection_names = self.enumerate_analysis_collections()
            logger.info(f"Found {len(collection_names)} collections in Analysis DB to process.")
            
            # Sort collections by priority (smaller ones first for quick wins)
            priority_collections = ['meta_pages', 'meta_ad_accounts', 'meta_insights']
            other_collections = [c for c in collection_names if c not in priority_collections]
            sorted_collections = priority_collections + other_collections
            
            # Process collections with controlled concurrency
            tasks = []
            for collection_name in sorted_collections:
                if self.shutdown_event.is_set():
                    logger.info("Shutdown requested, stopping collection processing")
                    break
                    
                task = asyncio.create_task(
                    self._process_single_analysis_collection(collection_name, dimensions)
                )
                tasks.append(task)
                
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Log any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Collection {sorted_collections[i]} failed: {result}")
                    
            logger.info("Finished processing all analysis collections.")
            self._log_metrics()
            
        except Exception as e:
            logger.error(f"Error processing analysis collections: {str(e)}\n{traceback.format_exc()}")

    def _log_metrics(self):
        """Log final metrics."""
        logger.info("=== Embedding Generation Metrics ===")
        for key, value in self.metrics.items():
            logger.info(f"{key}: {value}")
        logger.info("===================================")

    async def shutdown(self):
        """Signal shutdown to all tasks."""
        logger.info("Initiating graceful shutdown...")
        self.shutdown_event.set()

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cleanup resources."""
        logger.info("Cleaning up resources...")
        
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
            
        if hasattr(self, 'client') and self.client:
            self.client.close()
            
        # Clear caches
        self.embedding_cache.clear()
        self.compute_doc_hash.cache_clear()
        
        logger.info("Cleanup completed")

# --- Main Execution --- #
async def main_async():
    """Asynchronous main function to run the embedding generation process."""
    try:
        # Configuration for text-embedding-3-large
        config = EmbeddingConfig(
            model="text-embedding-3-large",
            dimensions=3072,  # Maximum dimensions for better quality
            batch_size=500,   # Smaller batches for larger embeddings
            api_batch_size=50,  # Smaller API batches
            max_concurrent=5,   # Reduced concurrency for larger model
            max_concurrent_collections=2,  # Process 2 collections at once
            retry_attempts=3,
            max_text_length=8000,
            checkpoint_interval=50  # More frequent checkpoints
        )

        async with AnalysisEmbeddingsGenerator(config=config) as generator:
            logger.info("Starting D-Unit-AnalysisGPT embedding generation with text-embedding-3-large...")
            start_time = time.time()

            # Run the main processing function
            await generator.process_analysis_collections()

            duration = time.time() - start_time
            logger.info(f"D-Unit-AnalysisGPT embedding generation completed in {duration:.2f} seconds.")

    except ValueError as ve:
        logger.error(f"Configuration error: {str(ve)}")
    except KeyboardInterrupt:
        logger.info("Process interrupted by user, initiating shutdown...")
        if 'generator' in locals():
            await generator.shutdown()
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    try:
        # Use asyncio.run to execute the async main function
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
    except Exception as e:
        # Catch any potential errors during asyncio.run itself
        logger.critical(f"Critical error during script execution: {e}")
        logger.critical(traceback.format_exc())