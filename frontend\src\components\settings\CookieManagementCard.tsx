import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  <PERSON>lapse,
  Divider,
  FormControlLabel,
  IconButton,
  Stack,
  Switch,
  Typography,
  Alert,
  Snackbar,
  Tooltip,
  Badge,
  Link
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Tune as TuneIcon,
  Campaign as MarketingIcon,
  Speed as SpeedIcon,
  <PERSON><PERSON> as CookieIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import cookieService from '../../services/cookieService';
import { CookiePreferences, CookieConsent } from '../../types/cookies';
import { COOKIE_CONFIG, COOKIE_CATEGORIES, translateDuration } from '../../config/cookies';

const categoryIcons = {
  essential: <SecurityIcon />,
  functional: <TuneIcon />,
  analytics: <AnalyticsIcon />,
  performance: <SpeedIcon />,
  marketing: <MarketingIcon />
};

const categoryColors = {
  essential: '#f44336', // Red - always required
  functional: '#2196f3', // Blue
  analytics: '#ff9800', // Orange
  performance: '#4caf50', // Green
  marketing: '#9c27b0' // Purple
};

export const CookieManagementCard: React.FC = () => {
  const { t } = useTranslation();
  
  // Create a wrapper function to match translateDuration's expected signature
  const translateWrapper = (key: string, fallback?: string) => {
    return t(key, { defaultValue: fallback || key });
  };
  
  const [preferences, setPreferences] = useState<CookiePreferences>(
    cookieService.getPreferences()
  );
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [consentRecord, setConsentRecord] = useState<CookieConsent | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'info' | 'warning' | 'error'>('success');

  useEffect(() => {
    setConsentRecord(cookieService.getConsentRecord());
  }, []);

  const handlePreferenceChange = (category: keyof CookiePreferences, enabled: boolean) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    const newPreferences = {
      ...preferences,
      [category]: enabled
    };
    setPreferences(newPreferences);
    
    // Auto-save preferences immediately
    cookieService.updatePreferences(newPreferences);
    setConsentRecord(cookieService.getConsentRecord());
    
    setSnackbarMessage(
      enabled 
        ? t('cookies.settings.categoryEnabled', `${category} cookies enabled`)
        : t('cookies.settings.categoryDisabled', `${category} cookies disabled`)
    );
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleAcceptAll = () => {
    const allAcceptedPreferences: CookiePreferences = {
      essential: true,
      analytics: true,
      performance: true,
      functional: true,
      marketing: true,
      preferences: true
    };
    
    setPreferences(allAcceptedPreferences);
    cookieService.acceptAll();
    setConsentRecord(cookieService.getConsentRecord());
    
    setSnackbarMessage(t('cookies.settings.allAccepted', 'All cookies accepted'));
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleRejectOptional = () => {
    const essentialOnlyPreferences: CookiePreferences = {
      essential: true,
      analytics: false,
      performance: false,
      functional: false,
      marketing: false,
      preferences: false
    };
    
    setPreferences(essentialOnlyPreferences);
    cookieService.rejectAll();
    setConsentRecord(cookieService.getConsentRecord());
    
    setSnackbarMessage(t('cookies.settings.optionalRejected', 'Optional cookies rejected'));
    setSnackbarSeverity('info');
    setSnackbarOpen(true);
  };

  const handleResetConsent = () => {
    cookieService.resetConsent();
    setPreferences(cookieService.getPreferences());
    setConsentRecord(null);
    
    setSnackbarMessage(t('cookies.settings.reset', 'Cookie consent has been reset'));
    setSnackbarSeverity('warning');
    setSnackbarOpen(true);
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const getCookiesForCategory = (categoryId: string) => {
    return COOKIE_CONFIG.cookies.filter(cookie => cookie.category === categoryId);
  };

  const getEnabledCookiesCount = () => {
    return Object.entries(preferences).filter(([_key, value]) => value).length;
  };

  const getTotalCookiesCount = () => {
    return COOKIE_CATEGORIES.length;
  };

  const renderCompactCookieList = (categoryId: string) => {
    const cookies = getCookiesForCategory(categoryId);
    
    if (cookies.length === 0) return null;

    return (
      <Box sx={{ mt: 2, ml: 2 }}>
        {cookies.map((cookie, index) => (
          <Tooltip 
            key={`${cookie.name}-${index}`}
            title={
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {cookie.name}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
                  <strong>{t('cookies.table.purpose', 'Purpose')}:</strong> {t(cookie.purpose, cookie.purpose)}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
                  <strong>{t('cookies.table.duration', 'Duration')}:</strong> {translateDuration(cookie.duration, translateWrapper)}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block', mb: 0.5 }}>
                  <strong>{t('cookies.table.type', 'Type')}:</strong> {t(`cookies.types.${cookie.type}`, cookie.type)}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block' }}>
                  <strong>{t('cookies.table.provider', 'Provider')}:</strong> {cookie.provider}
                </Typography>
              </Box>
            }
            placement="top"
            arrow
          >
            <Chip
              label={cookie.name}
              size="small"
              variant="outlined"
              color={cookie.type === 'first-party' ? 'primary' : 'secondary'}
              sx={{ 
                mr: 0.5, 
                mb: 0.5, 
                fontFamily: 'monospace',
                fontSize: '0.75rem',
                cursor: 'help'
              }}
            />
          </Tooltip>
        ))}
      </Box>
    );
  };

  return (
    <Box className="bg-gray-800 rounded-lg shadow-md p-4">
      {/* Cookie Management Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
        <CookieIcon sx={{ color: '#00A3FF' }} />
        <Typography variant="h6">
          {t('cookies.settings.title', 'Cookie Management')}
        </Typography>
        <Badge 
          badgeContent={`${getEnabledCookiesCount()}/${getTotalCookiesCount()}`} 
          color="primary"
          sx={{ ml: 1 }}
        />
      </Box>
      
      <Divider sx={{ mb: 3 }} />

      {/* Consent Status */}
      {consentRecord ? (
        <Alert 
          severity="success" 
          icon={<CheckCircleIcon />}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            <strong>{t('cookies.settings.consentGiven', 'Consent Given')}:</strong>{' '}
            {consentRecord.timestamp.toLocaleString()}
            {' '}({t('cookies.settings.version', 'Version')} {consentRecord.version})
          </Typography>
        </Alert>
      ) : (
        <Alert 
          severity="warning" 
          icon={<CancelIcon />}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            {t('cookies.settings.noConsent', 'No cookie consent given yet')}
          </Typography>
        </Alert>
      )}

      {/* Quick Actions */}
      <Stack direction="row" spacing={1} sx={{ mb: 3 }}>
        <Button
          variant="contained"
          size="small"
          onClick={handleAcceptAll}
          sx={{ textTransform: 'none' }}
        >
          {t('cookies.banner.acceptAll', 'Accept All')}
        </Button>
        <Button
          variant="outlined"
          size="small"
          onClick={handleRejectOptional}
          sx={{ textTransform: 'none' }}
        >
          {t('cookies.banner.rejectAll', 'Reject Optional')}
        </Button>
        <Button
          variant="text"
          size="small"
          onClick={() => setShowDetails(!showDetails)}
          endIcon={showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          sx={{ textTransform: 'none' }}
        >
          {t('cookies.settings.details', 'Details')}
        </Button>
      </Stack>

      {/* Cookie Categories - Compact View */}
      <Stack spacing={2}>
        {COOKIE_CATEGORIES.map((category) => {
          const isEnabled = preferences[category.id as keyof CookiePreferences];
          const cookieCount = getCookiesForCategory(category.id).length;
          
          return (
            <Card key={category.id} variant="outlined" sx={{ bgcolor: 'background.paper' }}>
              <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
                <Box className="flex flex-col sm:flex-row justify-between sm:items-center gap-3" >
                  <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                    <Box 
                      sx={{ 
                        mr: 1, 
                        color: categoryColors[category.id as keyof typeof categoryColors] || '#00A3FF',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      {categoryIcons[category.id as keyof typeof categoryIcons]}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {t(`cookies.categories.${category.id}.name`, category.name)}
                        </Typography>
                        {category.essential && (
                          <Chip 
                            label={t('cookies.category.essential', 'Required')} 
                            size="small" 
                            color="error" 
                            sx={{ height: '18px', fontSize: '0.65rem' }}
                          />
                        )}
                        <Badge 
                          badgeContent={cookieCount} 
                          color="secondary"
                          sx={{ 
                            '& .MuiBadge-badge': { 
                              fontSize: '0.6rem',
                              height: '16px',
                              minWidth: '16px'
                            }
                          }}
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        {t(`cookies.categories.${category.id}.description`, category.description)}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={isEnabled}
                          onChange={(e) => handlePreferenceChange(
                            category.id as keyof CookiePreferences, 
                            e.target.checked
                          )}
                          disabled={category.essential}
                          size="small"
                          color="primary"
                        />
                      }
                      label=""
                      sx={{ m: 0 }}
                    />
                    <IconButton
                      size="small"
                      onClick={() => toggleCategoryExpansion(category.id)}
                      sx={{ ml: 0.5 }}
                    >
                      {expandedCategories[category.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                </Box>
                
                <Collapse in={expandedCategories[category.id]}>
                  {renderCompactCookieList(category.id)}
                </Collapse>
              </CardContent>
            </Card>
          );
        })}
      </Stack>

      {/* Detailed Information - Expandable */}
      <Collapse in={showDetails}>
        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            {t('cookies.settings.legalInfo', 'Legal Information')}
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            {t('cookies.settings.legalText', 
              'This website complies with GDPR, CCPA, LGPD, PIPEDA, and other applicable privacy regulations. You have the right to:'
            )}
          </Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="caption" color="text.secondary">
              {t('cookies.settings.right1', 'Withdraw consent at any time')}
            </Typography>
            <Typography component="li" variant="caption" color="text.secondary">
              {t('cookies.settings.right2', 'Request deletion of your data')}
            </Typography>
            <Typography component="li" variant="caption" color="text.secondary">
              {t('cookies.settings.right3', 'Access your personal data')}
            </Typography>
            <Typography component="li" variant="caption" color="text.secondary">
              {t('cookies.settings.right4', 'Data portability')}
            </Typography>
          </Box>
          
          <Box sx={{ mt: 2 }}>
            <Link 
              href="https://d-unit.world/privacy-policy/" 
              target="_blank" 
              rel="noopener noreferrer"
              sx={{ 
                color: 'primary.main', 
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <Typography variant="caption">
                📋 {t('cookies.settings.privacyPolicyLink', 'View our Privacy Policy')}
              </Typography>
            </Link>
          </Box>
          
          <Box sx={{ mt: 1 }}>
            <Link 
              href="/cookie-policy" 
              sx={{ 
                color: 'primary.main', 
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <Typography variant="caption">
                🍪 {t('cookiePolicy.title', 'Cookie Policy')}
              </Typography>
            </Link>
          </Box>
          
          <Box sx={{ mt: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              <strong>{t('cookies.settings.version', 'Configuration Version')}:</strong> {COOKIE_CONFIG.consentVersion}
            </Typography>
            <Divider orientation="vertical" flexItem />
            <Typography variant="caption" color="text.secondary">
              <strong>{t('cookies.settings.supportedRegulations', 'Supported Regulations')}:</strong> GDPR, CCPA, LGPD, PIPEDA
            </Typography>
          </Box>
          
          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              color="error"
              size="small"
              onClick={handleResetConsent}
              startIcon={<DeleteIcon />}
              sx={{ textTransform: 'none' }}
            >
              {t('cookies.settings.resetConsent', 'Reset All Cookie Settings')}
            </Button>
          </Box>
        </Box>
      </Collapse>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert 
          onClose={() => setSnackbarOpen(false)} 
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CookieManagementCard; 