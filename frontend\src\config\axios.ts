import axios from 'axios';
import { API_URL } from './api';
// Import authService for consistent token management
import { authService } from '../services/authService';
import { csrfService } from '../services/csrfService';
import { logger } from '../utils/logger';

// Extend the InternalAxiosRequestConfig type to include our custom property
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    originalComplete?: () => void;
  }
  
  // Also extend AxiosRequestConfig to avoid TypeScript errors
  interface AxiosRequestConfig {
    originalComplete?: () => void;
  }
}

// Configure axios defaults
axios.defaults.baseURL = API_URL;
axios.defaults.withCredentials = true; // Enable sending cookies with cross-origin requests

// Configure axios to accept self-signed certificates in development
if (import.meta.env.DEV) {
  // Browser-compatible approach for handling self-signed certificates
  // Instead of using Node.js require('https').Agent which doesn't work in the browser
  logger.debug('Development mode: Configured to accept self-signed certificates');
  
  // In the browser, we can't directly configure certificate validation
  // Instead, we'll handle the specific error cases that might occur
  axios.interceptors.request.use(
    (config) => {
      // Add a custom header for development environments
      config.headers['X-Dev-Mode'] = 'true';
      return config;
    },
    (error) => Promise.reject(error)
  );
}

// Request deduplication
const pendingRequests = new Map();

// Add request interceptor for deduplication
axios.interceptors.request.use(
  (config) => {
    // Skip deduplication for certain endpoints that might have legitimate duplicate requests
    const skipDeduplication = [
      '/api/meta/pages',
      '/metrics/fallback',
      '/api/meta/instagram',
      '/api/instagram',
      '/validate',
      '/insights',
      '/profile'
    ].some(path => config.url?.includes(path));
    
    if (!skipDeduplication) {
      // Generate a request key
      const requestKey = `${config.method || 'get'}:${config.url}:${JSON.stringify(config.params)}:${JSON.stringify(config.data)}`;
      
      // Check if we have a pending request
      if (pendingRequests.has(requestKey)) {
        logger.debug(`Duplicate request detected: ${requestKey}`);
        const controller = new AbortController();
        config.signal = controller.signal;
        controller.abort('Duplicate request cancelled');
      }
      
      // Store this request
      pendingRequests.set(requestKey, true);
      
      // Add cleanup when request is complete
      const originalComplete = () => {
        pendingRequests.delete(requestKey);
      };
      
      // Store the original complete handlers
      config.originalComplete = originalComplete;
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Add request interceptor to handle authentication and CSRF tokens
axios.interceptors.request.use(
  async (config) => {
    // Add authentication token if available
    const token = authService.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add CSRF token if needed (for non-GET requests, excluding auth endpoints)
    if (config.method && config.method.toLowerCase() !== 'get') {
      // Skip CSRF token for authentication endpoints
      const isAuthEndpoint = config.url?.includes('/auth/') || 
                            config.url?.includes('/login') || 
                            config.url?.includes('/register') ||
                            config.url?.includes('/csrf-token');
      
      if (!isAuthEndpoint) {
        try {
          const csrfToken = await csrfService.getToken();
          if (csrfToken) {
            config.headers['X-CSRF-Token'] = csrfToken;
          }
        } catch (csrfError) {
          // Log CSRF token fetch failure but don't block the request
          logger.warn('Failed to fetch CSRF token:', csrfError);
          // Continue without CSRF token - the server should handle this gracefully
        }
      }
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle common errors and cleanup pending requests
axios.interceptors.response.use(
  (response) => {
    // Clean up pending request
    if (response.config.originalComplete) {
      response.config.originalComplete();
    }
    
    // Handle CSRF token rotation
    try {
      // Create a Response-like object for CSRF service compatibility
      const responseAdapter = {
        headers: {
          get: (name: string) => response.headers[name.toLowerCase()] || null
        }
      } as Response;
      csrfService.handleTokenRotation(responseAdapter);
    } catch (error) {
      logger.warn('Failed to handle CSRF token rotation:', error);
    }
    
    // Handle CSRF cookie updates from response
    try {
      const cookieTokenSet = response.headers['x-csrf-cookie-set'];
      if (cookieTokenSet === 'true') {
        console.debug('CSRF cookie updated from server response');
      }
    } catch (error) {
      logger.warn('Failed to handle CSRF cookie update:', error);
    }
    
    return response;
  },
  async (error) => {
    // Clean up pending request even on error
    if (error.config?.originalComplete) {
      error.config.originalComplete();
    }
    
    // Handle certificate errors in development mode
    if (import.meta.env.DEV && error.message && (
        error.message.includes('certificate') || 
        error.message.includes('SSL') || 
        error.message.includes('self signed')
    )) {
      logger.warn('SSL Certificate Error in development mode:', error.message);
      // In a real app, you might want to show a user-friendly message here
    }
    
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      const requestUrl = error.config?.url || '';
      
      // Check if this is an Instagram API request
      if (requestUrl.includes('/api/instagram/') || requestUrl.includes('/instagram/')) {
        // For Instagram API errors, attempt automatic token refresh
        logger.warn('Instagram API 401 error - checking if safe to refresh token:', requestUrl);
        
        // Check if we're in Meta dashboard initialization phase - avoid logout loops
        const isInitializing = sessionStorage.getItem('meta_dashboard_initializing') === 'true';
        if (isInitializing) {
          logger.warn('Skipping Instagram token refresh during Meta dashboard initialization to prevent logout loops');
          error.isInstagramAuthError = true;
          return Promise.reject(error);
        }
        
        try {
          // Import InstagramBusinessService dynamically to avoid circular dependencies
          const { InstagramBusinessService } = await import('../services/instagramBusinessService');
          
          // Attempt token refresh
          const refreshResult = await InstagramBusinessService.handleExpiredInstagramToken();
          
          if (refreshResult.success) {
            logger.info('Instagram token refreshed successfully, retrying request');
            // Retry the original request with the new token
            return axios.request(error.config);
          } else {
            logger.error('Instagram token refresh failed');
            // Add a flag to indicate this is an Instagram auth error
            error.isInstagramAuthError = true;
            return Promise.reject(error);
          }
        } catch (refreshError) {
          logger.error('Error during Instagram token refresh:', refreshError);
          error.isInstagramAuthError = true;
          return Promise.reject(error);
        }
      }
      
      // For other 401 errors, proceed with logout
      if (window.location.pathname !== '/login') {
        authService.clearTokens();
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export default axios; 