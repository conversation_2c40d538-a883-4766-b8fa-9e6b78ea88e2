import * as React from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Paper, 
  Divider,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  LinearProgress
} from '@mui/material';
import { 
  MetricType,
  MetaPage, 
  MetaErrorType 
} from '../../services/types';
import { MetaAuthService } from '../../services/auth';
import { InstagramBusinessService } from '../../services/instagramBusinessService';
import { MetaLoginButton } from './MetaLoginButton';
import { MetaBusinessConnect } from './MetaBusinessConnect';
import { MetaPostsAnalytics } from './MetaPostsAnalytics';
import { MetaAudienceDemographics } from './MetaAudienceDemographics';
import { MetaMetricsPanel } from './MetaMetricsPanel';
import { MetaOverviewPanel } from './MetaOverviewPanel';
import { MetaAdMetricsPanel } from './MetaAdMetricsPanel';
import { MetaCampaignsComparison } from './MetaCampaignsComparison';
import { MetaPermissionsPanel } from './MetaPermissionsPanel';
import { RestrictedWidget } from './RestrictedWidget';
import NavigationWrapper from '../common/NavigationWrapper';
import PageContainer from '../common/PageContainer';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';
import { META_AUTH_STATE_CHANGE } from '../../services/authChecker';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '@mui/material/styles';
import { logger } from '../../utils/logger';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`meta-tabpanel-${index}`}
      aria-labelledby={`meta-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Define interfaces for errors and Facebook responses
interface MetaError {
  type?: MetaErrorType;
  message?: string;
  originalError?: unknown;
}

/**
 * Meta Dashboard Component
 * Integrates all Meta analytics components
 */
export const MetaDashboard: React.FC = () => {
  const { t } = useTranslation();
  
  // Helper function to handle missing translations by providing a default
  const tWithDefault = (key: string, defaultValue: string): string => {
    const translation = t(key, defaultValue);
    // Check if the translation is the same as the key (missing translation)
    // But only if the key contains a dot (namespace)
    if (translation === key && key.includes('.')) {
      return defaultValue;
    }
    return translation;
  };
  
  const [isLoggedIn, setIsLoggedIn] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);
  const [authError, setAuthError] = React.useState<boolean>(false);
  const [pages, setPages] = React.useState<MetaPage[]>([]);
  const [selectedPage, setSelectedPage] = React.useState<MetaPage | null>(null);
  const [tabValue, setTabValue] = React.useState(0);
  const [usingMockData, setUsingMockData] = React.useState<boolean>(false);
  const { refreshPermissions, isLoading: permissionsLoading } = useMetaPermissions();
  const [refreshingPermissions, setRefreshingPermissions] = React.useState<boolean>(false);
  const [permissionRefreshSuccess, setPermissionRefreshSuccess] = React.useState<boolean>(false);
  const [isInitializing, setIsInitializing] = React.useState<boolean>(true);
  const [fetchingBusinessAccounts, setFetchingBusinessAccounts] = React.useState<boolean>(false);
  const [fetchingBusinessData, setFetchingBusinessData] = React.useState<boolean>(false);
  const [rateLimited, setRateLimited] = React.useState<boolean>(false);
  const [permissionsRefreshedOnLoad, setPermissionsRefreshedOnLoad] = React.useState<boolean>(false);
  const lastFetchTimeRef = React.useRef<number>(0);
  const initializationAttempted = React.useRef<boolean>(false);
  const initializationInProgress = React.useRef<boolean>(false);
  const { metaLogin } = useAuth();
  const theme = useTheme();

  React.useEffect(() => {
    logger.debug(`State changed - loading: ${loading}, isInitializing: ${isInitializing}, fetchingBusinessAccounts: ${fetchingBusinessAccounts}, fetchingBusinessData: ${fetchingBusinessData}, error: ${error ? 'true' : 'false'}, isLoggedIn: ${isLoggedIn}`);
  }, [loading, isInitializing, fetchingBusinessAccounts, fetchingBusinessData, error, isLoggedIn]);

  const handleRefreshPermissions = React.useCallback(async () => {
    try {
      if (rateLimited) {
        logger.debug('API rate limited, skipping permissions refresh');
        setError('API rate limit reached. Please try again in a minute.');
        return;
      }
      setRefreshingPermissions(true);
      setError(null);
      logger.debug('Manually refreshing permissions from dashboard...');
      await refreshPermissions(true).catch(err => {
        logger.error('Error refreshing permissions:', err);
        const metaErr = err as MetaError;
        if (metaErr?.message && metaErr.message.includes('request limit reached')) {
          logger.warn('Rate limit reached during permissions refresh');
          setRateLimited(true);
          setTimeout(() => setRateLimited(false), 60000);
          throw err;
        }
      });
      setPermissionRefreshSuccess(true);
      setTimeout(() => {
        setPermissionRefreshSuccess(false);
      }, 3000);
    } catch (err: unknown) {
      logger.error('Error refreshing permissions:', err);
      const metaErr = err as MetaError;
      if (metaErr?.message && metaErr.message.includes('request limit reached')) {
        setError('API rate limit reached. Please try again in a minute.');
      } else {
        setError('Failed to refresh permissions. Please try again.');
      }
    } finally {
      setRefreshingPermissions(false);
    }
  }, [rateLimited, refreshPermissions]);

  const handleLoginAgain = async () => {
    try {
      setLoading(true);
      setError(null);
      setAuthError(false);
      
      logger.debug('Attempting to re-login with Meta...');
      
      const response = await MetaAuthService.login('facebook');
      
      // Check response and handle auth with backend
      if (response && response.authResponse) {
        logger.debug('FB SDK Re-login successful, handling auth with backend...');
        await MetaAuthService.handleMetaAuth(response, 'facebook');
        logger.debug('Backend auth handled, refreshing permissions...');
        await handleRefreshPermissions(); // Explicitly refresh permissions
        logger.debug('Permissions refreshed, fetching pages...');
        setIsLoggedIn(true); // State updates after successful auth
        setUsingMockData(false);
        await fetchPages(); // Call fetchPages after handleMetaAuth and permissions refresh
      } else {
        // Handle cases where login response is invalid or lacks authResponse
        logger.error('Meta SDK login response invalid or missing authResponse:', response);
        throw new Error(t('metaDashboard.error.loginResponse', 'Invalid response from Meta SDK login'));
      }
    } catch (error: unknown) { // Explicitly type catch variable
      logger.error('Re-login failed:', error);
      // Check if it's a MetaError and use its message
      const errorMessage = (error && typeof error === 'object' && 'message' in error)
        ? (error as { message: string }).message
        : t('metaDashboard.errorReloginFailed', 'Failed to re-authenticate with Meta.');
      setError(errorMessage);
      // Optionally set authError true again if the error type indicates it
      if (error && typeof error === 'object' && 'type' in error && (error as { type: MetaErrorType }).type === MetaErrorType.AUTH_FAILED) {
        setAuthError(true);
      }
    } finally {
      setLoading(false);
    }
  };
  
  const fetchPages = React.useCallback(async () => {
    try {
      if (fetchingBusinessAccounts) {
        logger.debug('Already fetching business accounts, skipping duplicate request');
        return;
      }
      
      if (rateLimited) {
        logger.debug('API rate limited, skipping business account fetch');
        setError(t('metaDashboard.errorRateLimit'));
        return;
      }
      
      const now = Date.now();
      const timeSinceLastFetch = now - lastFetchTimeRef.current;
      if (timeSinceLastFetch < 5000 && lastFetchTimeRef.current > 0) {
        logger.debug(`Throttling API calls: Only ${timeSinceLastFetch}ms since last fetch, need 5000ms`);
        return;
      }
      
      lastFetchTimeRef.current = now;
      
      setLoading(true);
      setFetchingBusinessData(true);
      setError(null);
      setAuthError(false);
      setFetchingBusinessAccounts(true);
      logger.debug('Fetching Meta pages...');
      
      const accounts = await MetaAuthService.getBusinessAccounts().catch(err => {
        if (err.message && err.message.includes('request limit reached')) {
          logger.warn('API rate limit reached, pausing requests for 60 seconds');
          setRateLimited(true);
          
          setTimeout(() => {
            setRateLimited(false);
          }, 60000);
        }
        return [];
      });
      
      logger.debug('Business accounts fetched:', accounts);
      
      let noAccounts = false;
      if (!accounts || accounts.length === 0) {
        logger.warn('No business accounts found, but continuing with authentication flow');
        noAccounts = true;
      }
      
      let combinedPages: MetaPage[] = [];
      let hasError = false;
      let fbPages: MetaPage[] = []; // Declare fbPages in the correct scope
      
      if (!noAccounts) {
        const businessId = accounts[0].id;
        
        try {
          fbPages = await MetaAuthService.getPages(businessId);
          if (fbPages && fbPages.length > 0) {
            logger.debug('Facebook pages fetched:', fbPages);
            
            const pagesWithTokens = fbPages.filter(page => !!page.access_token);
            logger.debug(`${pagesWithTokens.length} of ${fbPages.length} Facebook pages have access tokens`);
            
            if (pagesWithTokens.length === 0) {
              logger.warn('WARNING: No Facebook pages have access tokens! This will cause API errors.');
            }
            
            const enrichedFbPages = fbPages.map(page => ({
              ...page,
              platform: 'facebook' as const
            }));
            combinedPages = [...combinedPages, ...enrichedFbPages];
          }
        } catch (fbError: unknown) {
          logger.error('Error fetching Facebook pages:', fbError);
          if ((fbError as MetaError).type === 'AUTH_FAILED') {
            setAuthError(true);
            throw fbError;
          }
          hasError = true;
        }
        
        try {
          // Use enhanced Instagram Business Service for proper detection
          logger.debug('Starting enhanced Instagram Business Account detection...');
          
          // Get Facebook pages with tokens for Instagram detection
          const fbPagesWithTokens = fbPages?.filter(page => !!page.access_token) || [];
          
          if (fbPagesWithTokens.length > 0) {
            // Get user access token for Instagram detection
            const userToken = MetaAuthService.getAccessToken();
            
            if (userToken) {
              logger.debug('Detecting Instagram Business Accounts using enhanced service...');
              
              const igDetectionResult = await InstagramBusinessService.detectAndConnectInstagramAccounts(
                fbPagesWithTokens,
                userToken
              );
              
              if (igDetectionResult.success && igDetectionResult.accounts.length > 0) {
                logger.info(`Successfully detected ${igDetectionResult.accounts.length} Instagram Business Accounts`);
                
                // Transform to MetaPage format for UI compatibility
                const igPages = igDetectionResult.accounts.map(account => ({
                  id: account.id,
                  name: account.username || account.name,
                  access_token: account.access_token,
                  category: 'INSTAGRAM_BUSINESS',
                  category_list: [{ id: 'business', name: 'Business' }],
                  tasks: ['ANALYZE', 'ADVERTISE'],
                  platform: 'instagram' as const,
                  followers_count: account.followers_count,
                  media_count: account.media_count,
                  profile_picture_url: account.profile_picture_url
                }));
                
                combinedPages = [...combinedPages, ...igPages];
                
                logger.debug('Enhanced Instagram accounts added to combined pages:', igPages.length);
              } else {
                logger.warn('No Instagram Business Accounts detected or connection failed');
                if (igDetectionResult.errors.length > 0) {
                  logger.warn('Instagram detection errors:', igDetectionResult.errors);
                }
              }
            } else {
              logger.warn('No user access token available for Instagram detection');
            }
          } else {
            logger.debug('No Facebook pages with tokens available for Instagram detection');
          }
          
          // Fallback to original method if enhanced detection fails
          let existingIgAccounts: any[] = [];
          try {
            existingIgAccounts = await InstagramBusinessService.getInstagramAccounts();
          } catch (igAccountsError) {
            logger.warn('Failed to get existing Instagram accounts, continuing without them:', igAccountsError);
            existingIgAccounts = [];
          }
          
          if (existingIgAccounts.length > 0) {
            logger.debug('Found existing Instagram accounts in database:', existingIgAccounts.length);
            
            const existingIgPages = existingIgAccounts.map(account => ({
              id: account.id,
              name: account.username || account.name,
              access_token: account.access_token,
              category: 'INSTAGRAM_BUSINESS',
              category_list: [{ id: 'business', name: 'Business' }],
              tasks: ['ANALYZE', 'ADVERTISE'],
              platform: 'instagram' as const,
              followers_count: account.followers_count,
              media_count: account.media_count,
              profile_picture_url: account.profile_picture_url
            }));
            
            // Merge with combined pages, avoiding duplicates
            const existingIds = new Set(combinedPages.map(page => page.id));
            const newIgPages = existingIgPages.filter(page => !existingIds.has(page.id));
            combinedPages = [...combinedPages, ...newIgPages];
            
            logger.debug('Added existing Instagram accounts from database:', newIgPages.length);
          }
          
        } catch (igError: unknown) {
          logger.error('Error in enhanced Instagram detection:', igError);
          
          // Try fallback to original method
          try {
            logger.debug('Attempting fallback Instagram account detection...');
            const igAccounts = await MetaAuthService.getInstagramAccounts(businessId);
            if (igAccounts && igAccounts.length > 0) {
              logger.debug('Fallback Instagram accounts fetched:', igAccounts.length);
              
              const enrichedIgAccounts = igAccounts.map(account => ({
                ...account,
                platform: 'instagram' as const
              }));
              combinedPages = [...combinedPages, ...enrichedIgAccounts];
            }
          } catch (fallbackError) {
            logger.error('Fallback Instagram detection also failed:', fallbackError);
          }
          
          if ((igError as MetaError).type === 'AUTH_FAILED') {
            setAuthError(true);
            throw igError;
          }
          hasError = true;
        }
      }
      
      if (combinedPages.length > 0) {
        // Filter for unique IDs
        const uniqueAccountsMap = new Map<string, MetaPage>();
        combinedPages.forEach(account => {
          uniqueAccountsMap.set(account.id, account);
        });
        const uniquePages = Array.from(uniqueAccountsMap.values());

        logger.debug('Setting unique Meta pages:', uniquePages);
        setPages(uniquePages);
        // Set selected page only if uniquePages is not empty
        setSelectedPage(uniquePages.length > 0 ? uniquePages[0] : null);
        
        if (hasError) {
          setError(t('metaDashboard.errorPartialFetch'));
        } else {
          setError(null);
        }
      } else {
        if (noAccounts) {
          setError(t('metaDashboard.errorNoBusinessAccounts'));
        } else {
          setError(t('metaDashboard.errorNoPagesFound'));
        }
        setPages([]);
        setSelectedPage(null);
      }
    } catch (error: unknown) {
      const metaError = error as MetaError;
      logger.error('Error fetching pages:', metaError);
      if (metaError.type === 'AUTH_FAILED') {
        setError(t('metaDashboard.errorAuthFailedGeneral'));
        setAuthError(true);
      } else {
        setError(metaError.message || t('metaDashboard.errorFetchPagesFallback'));
      }
      setPages([]);
      setSelectedPage(null);
    } finally {
      setLoading(false);
      setFetchingBusinessAccounts(false);
      setFetchingBusinessData(false);
      setIsInitializing(false);
      logger.debug('Meta initialization complete');
      // Don't reset initializationAttempted here - let component unmount handle it
      initializationInProgress.current = false;
      setPermissionsRefreshedOnLoad(false);
    }
  }, [rateLimited, t]); // Remove fetchingBusinessAccounts dependency to prevent loops
  
  const checkMetaLoginStatus = React.useCallback(async () => {
    try {
      logger.debug('Checking Meta login status...');
      
      setLoading(true);
      setError(null);
      
      const isLoggedIn = await MetaAuthService.hasValidSession();
      logger.debug('Meta login status check result:', isLoggedIn);
      
      setIsLoggedIn(isLoggedIn);
      
      const event = new CustomEvent(META_AUTH_STATE_CHANGE, { 
        detail: { isAuthenticated: isLoggedIn }
      });
      document.dispatchEvent(event);
      
      if (isLoggedIn) {
        await fetchPages();
      }
    } catch (err) {
      logger.error('Error checking Meta login status:', err);
      setError(t('metaDashboard.errorCheckStatus'));
    } finally {
      // Don't set loading/initializing false here, let runInitialization handle it
      // setLoading(false);
      // setIsInitializing(false);
    }
  }, [fetchPages, t]);

  // Define a dedicated function to handle successful Meta login
  const handleSuccessfulLogin = React.useCallback(async (platform: 'facebook' | 'instagram') => {
    try {
      // Sync Meta login with global AuthContext
      const token = MetaAuthService.getAccessToken();
      if (token) {
        await metaLogin(token, platform);
        logger.debug('MetaDashboard: Called metaLogin from AuthContext');
      }
      
      logger.info(`Meta login successful with ${platform}, initializing dashboard...`);
      setLoading(true);
      setError(null);
      setAuthError(false);
      
      // Update login state
      setIsLoggedIn(true);
      setUsingMockData(false);
      
      // Explicitly refresh permissions first
      logger.debug('Refreshing Meta permissions after login...');
      await handleRefreshPermissions();
      
      // Fetch pages directly, regardless of backend hasPagesOrAccounts flag
      logger.debug('Fetching Meta pages after login...');
      await fetchPages();
      
      if (!isLoggedIn) {
        logger.warn('public_profile permission is NOT granted after login!');
        // Restore the fallback logic:
        // Check if pages are still empty after trying to fetch
        if (pages.length === 0) {
          logger.warn('Fallback Triggered: public_profile missing and no pages found after login. Setting error state.');
          setError('Unable to fetch Meta pages. Please check your Meta permissions or try reconnecting.');
          setLoading(false);
          return; // Exit the function
        }
      }
      logger.info('Successfully completed post-login initialization');
      
      // Instead of forcing a reload, trigger a state refresh
      logger.debug('Refreshing Meta dashboard state after successful login.');
      await handleRefreshPermissions();
      initializationAttempted.current = false; // Allow re-initialization if needed
    } catch (error: unknown) {
      logger.error('Error during post-login initialization:', error);
      const errorMessage = (error && typeof error === 'object' && 'message' in error)
        ? (error as { message: string }).message
        : t('metaDashboard.errorInitializationFailed', 'Failed to initialize after login.');
      setError(errorMessage);
      
      if (error && typeof error === 'object' && 'type' in error && 
         (error as { type: MetaErrorType }).type === MetaErrorType.AUTH_FAILED) {
        setAuthError(true);
      }
    } finally {
      setLoading(false);
    }
  }, [handleRefreshPermissions, fetchPages, t, metaLogin, isLoggedIn, pages.length]);

  // Define the stable initialization function using useCallback
  const runInitialization = React.useCallback(async () => {
    if (rateLimited || initializationAttempted.current || initializationInProgress.current) {
      logger.debug('Skipping initialization: already attempted, in progress, or rate limited');
      setLoading(false);
      setIsInitializing(false);
      return;
    }

    initializationAttempted.current = true;
    initializationInProgress.current = true;
    setLoading(true); // Ensure loading is set at the start
    setIsInitializing(true); // Ensure initializing is set at the start
    
    // Set flag to prevent logout loops during initialization
    sessionStorage.setItem('meta_dashboard_initializing', 'true');

    try {
      logger.debug('Running Meta dashboard initialization...');

      // Check if there's a "meta_email_updated" flag in localStorage
      const metaEmailUpdated = localStorage.getItem('meta_email_updated');
      if (metaEmailUpdated === 'true') {
        logger.debug('Detected meta_email_updated flag, will force refresh connection');
        // Clear the flag
        localStorage.removeItem('meta_email_updated');

        // Force a new login if already logged in
        if (await MetaAuthService.hasValidSession()) {
          logger.debug('Found existing session, logging out first to refresh with new email');
          await MetaAuthService.logout();
          setIsLoggedIn(false); // Update login state after logout
        }
      }

      await checkMetaLoginStatus(); // This will fetch pages if logged in
    } catch (error: unknown) {
      logger.error('Error in initialization:', error);
      setError(t('metaDashboard.errorInitializationFailed'));
    } finally {
      setLoading(false);
      setIsInitializing(false);
      // Don't reset initializationAttempted here - only reset on unmount or explicit re-initialization
      initializationInProgress.current = false;
      // Clear initialization flag to allow token refresh again
      sessionStorage.removeItem('meta_dashboard_initializing');
      logger.debug('Meta initialization process finished.');
    }
  }, [checkMetaLoginStatus, rateLimited, t]);

  // useEffect hook for initial component mount
  React.useEffect(() => {
    let isMounted = true;
    logger.debug('MetaDashboard mounted. Setting up initial run.');

    const timeoutId = setTimeout(() => {
      if (isMounted) {
        runInitialization();
      }
    }, 1000); // Delay initialization slightly

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
      // Reset initialization flags on unmount to allow fresh initialization on remount
      initializationAttempted.current = false;
      initializationInProgress.current = false;
      // Clear initialization flag on unmount
      sessionStorage.removeItem('meta_dashboard_initializing');
      logger.debug('MetaDashboard unmounted.');
    };
  }, [runInitialization]); // Depend only on the stable runInitialization function

  const handlePageChange = (event: SelectChangeEvent<string>) => {
    const pageId = event.target.value;
    const page = pages.find(p => p.id === pageId) || null;
    
    if (page) {
      if (!page.platform) {
        if (page.category === 'INSTAGRAM_ACCOUNT' || page.category?.includes('Instagram')) {
          page.platform = 'instagram';
        } else {
          page.platform = 'facebook';
        }
      }
      
      logger.debug(`Selected page: ${page.name}, Platform: ${page.platform}, ID: ${page.id}`);
      logger.debug(`Page platform for permission checks: ${page.platform || 'facebook'}`);
      
      if (page.access_token) {
        logger.debug(`Page has access token (starts with): ${page.access_token.substring(0, 10)}...`);
      } else {
        logger.warn(`WARNING: Selected page has no access token! This will cause API errors.`);
      }
      
      if (page.platform === 'instagram') {
        logger.debug('This is an Instagram page - Instagram-specific permissions will be checked');
        logger.debug('Instagram widgets use these permission mappings:');
        logger.debug('- Page Engagement: Only requires instagram_basic');
        logger.debug('- Page Impressions, Followers, Views: Require instagram_basic AND instagram_manage_insights');
        logger.debug('- Instagram Insights tab: Uses specific mappings for each widget');
      } else {
        logger.debug('This is a Facebook page - Facebook-specific permissions will be checked');
        logger.debug('Facebook widgets use the standard permission mappings');
      }
      
      refreshPermissions(true).catch(err => {
        logger.error('Error refreshing permissions on page change:', err);
      });
    }
    
    setSelectedPage(page);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const renderPageOptions = () => {
    return (
      <Box mb={3}>
        <FormControl fullWidth variant="outlined">
          <InputLabel id="page-select-label">{t('metaDashboard.selectPageLabel', 'Select Page')}</InputLabel>
          <Select
            labelId="page-select-label"
            id="page-select"
            value={selectedPage ? selectedPage.id : ''}
            onChange={handlePageChange}
            label={t('metaDashboard.selectPageLabel', 'Select Page')}
            disabled={loading || pages.length === 0}
          >
            {pages.map((page) => (
              <MenuItem key={page.id} value={page.id}>
                {page.name} ({page.platform === 'facebook' ? 'Facebook' : 'Instagram'})
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Box mt={1}>
          <Typography variant="caption" color="text.secondary">
            {t('metaDashboard.altEmailInfo', 'Want to use a different email for Meta login? You can select your preferred email in the Settings page.')}
          </Typography>
        </Box>
      </Box>
    );
  };

  const checkTokenStatus = React.useCallback(async () => {
    if (!isLoggedIn) return;
    
    if (rateLimited) {
      logger.debug('API rate limited, skipping token status check');
      return;
    }
    
    try {
      logger.debug('Checking Meta token status...');
      const isValid = await MetaAuthService.checkTokenStatus().catch(err => {
        if (err && err.message && err.message.includes('request limit reached')) {
          logger.warn('Rate limit reached during token status check');
          setRateLimited(true);
          setTimeout(() => setRateLimited(false), 60000);
        }
        return false;
      });
      
      logger.debug('Token status check result:', isValid);
      
      if (!isValid) {
        logger.warn('Meta token is invalid or expired');
        setAuthError(true);
        setError('Your Meta access token has expired. Please log in again to continue.');
      }
    } catch (error) {
      logger.error('Error checking token status:', error);
    }
  }, [isLoggedIn, rateLimited]);
  
  React.useEffect(() => {
    if (isLoggedIn && !usingMockData && !isInitializing && !rateLimited) {
      checkTokenStatus();
    }
  }, [isLoggedIn, usingMockData, checkTokenStatus, isInitializing, rateLimited]);

  const handleDisconnect = async () => {
    setLoading(true);
    setError(null);
    try {
      await MetaAuthService.logout();
      setIsLoggedIn(false);
      setPages([]);
      setSelectedPage(null);
      setAuthError(false);
      logger.info('Successfully disconnected from Meta.');
      initializationAttempted.current = false;
      initializationInProgress.current = false;
      setPermissionsRefreshedOnLoad(false);
    } catch (error) {
      logger.error('Failed to disconnect from Meta:', error);
      setError(t('metaDashboard.errorDisconnectFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshPages = () => {
    logger.debug('Manual page refresh requested');
    setRateLimited(false);
    lastFetchTimeRef.current = 0;
    fetchPages();
  };

  const renderNoAccountsSection = () => {
    logger.debug('Rendering no accounts section, isLoggedIn =', isLoggedIn);
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Paper variant="outlined" sx={{ p: 4, maxWidth: 600, mx: 'auto' }}>
          <Typography variant="h6" gutterBottom>
            Connect to Meta
          </Typography>
          <Alert severity="info" sx={{ mb: 3 }}>
            {t('metaDashboard.noAccounts.description')}
          </Alert>
          <MetaBusinessConnect />
          <Box sx={{ mt: 2 }}>
            <Button
              component={Link}
              to="https://business.facebook.com/settings"
              target="_blank"
              rel="noopener noreferrer"
              variant="outlined"
              sx={{ mt: 2 }}
            >
              {t('metaDashboard.noAccounts.goToSettingsButton')}
            </Button>
          </Box>
        </Paper>
      </Box>
    );
  };

  React.useEffect(() => {
    const isFullyLoaded = !loading && !isInitializing && !fetchingBusinessAccounts && !fetchingBusinessData && isLoggedIn;
    
    if (isFullyLoaded && !permissionsRefreshedOnLoad && !refreshingPermissions) {
      logger.debug('Meta Dashboard fully loaded. Triggering automatic permission refresh.');
      // Use setTimeout to break out of the render cycle
      const timeoutId = setTimeout(() => {
        handleRefreshPermissions();
        setPermissionsRefreshedOnLoad(true);
      }, 500);
      
      return () => clearTimeout(timeoutId);
    }
  }, [loading, isInitializing, fetchingBusinessAccounts, fetchingBusinessData, isLoggedIn, permissionsRefreshedOnLoad, refreshingPermissions]); // Remove handleRefreshPermissions from deps

  // Loading state
  if (loading) {
    return (
      <PageContainer>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: 'calc(100vh - 200px)' }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            {t('metaDashboard.loading.title')}
          </Typography>
          <Typography 
            variant="h6"
            sx={{ color: theme.palette.mode === 'dark' ? 'grey.400' : 'grey.600', mt: 1, mb: 2, fontWeight: 'normal' }}
          >
            {isInitializing ? t('metaDashboard.loading.initializing') : t('metaDashboard.loading.title')}
          </Typography>
          <LinearProgress sx={{ width: '80%', maxWidth: '300px' }} />
        </Box>
      </PageContainer>
    );
  }

  // Not logged in state
  if (!isLoggedIn) {
    return (
      <>
        <NavigationWrapper />
        <PageContainer>
          <Box 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center', 
              minHeight: 'calc(100vh - 120px)', // Adjust height to account for nav/padding
              textAlign: 'center', 
              p: 3 
            }}
          >
            <Paper 
              elevation={3} 
              sx={{ 
                p: { xs: 3, sm: 4 }, 
                borderRadius: 2,
                maxWidth: 600,
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              <img 
                src="/meta.png" // Assuming you have a meta logo in public folder
                alt="Meta Logo"
                style={{ 
                  width: '80px', 
                  marginBottom: '16px',
                  borderRadius: '12px'
                }}
              />
              <Typography variant="h5" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
                {t('metaDashboard.connectMetaTitle', 'Connect Your Meta Account')}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                {t('metaDashboard.loginViaMain', 'To access Meta analytics, please first connect your Meta account through our main login page.')}
              </Typography>
              <Button
                component={Link}
                to="/login?next=/meta"
                variant="contained"
                color="primary"
                size="large"
                sx={{ 
                  minWidth: '200px',
                  py: 1.5,
                  backgroundColor: '#00A3FF',
                  '&:hover': {
                    backgroundColor: '#007acc'
                  }
                }}
              >
                {t('metaDashboard.goToLoginButton', 'Go to Login Page')}
              </Button>
            </Paper>
          </Box>
        </PageContainer>
      </>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh' }}>
      <NavigationWrapper />
      <PageContainer>
        <Box sx={{ py: 4 }}>
          <Typography 
            variant="h4" 
            component="h1" 
            gutterBottom
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(90deg, rgba(0,163,255,1) 0%, rgba(0,212,255,1) 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              letterSpacing: '1px',
              textTransform: 'uppercase',
              mb: 2 // Add some margin below the title
            }}
          >
            {tWithDefault('metaDashboard.title', 'Meta Analytics Dashboard')}
          </Typography>
          
          <Divider sx={{ my: 3 }} />
          
          {loading || isInitializing || fetchingBusinessData || fetchingBusinessAccounts ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Paper variant="outlined" sx={{ p: 4, maxWidth: 600, mx: 'auto' }}>
                <Typography variant="h6" gutterBottom>
                  {isInitializing ? tWithDefault('metaDashboard.loading.initializing', 'Initializing data...') : tWithDefault('metaDashboard.loading.title', 'Loading Meta Dashboard')}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress size={40} />
                </Box>
                {/* <Typography variant="body2" color="text.secondary">
                  {fetchingBusinessAccounts ? 
                    "Loading your Meta business accounts..." : 
                    "Checking authentication status and loading your Meta data..."}
                </Typography> */}
              </Paper>
            </Box>
          ) : !isLoggedIn && !error?.includes("No business accounts found") ? (
            <Paper elevation={0} sx={{ p: 4, mb: 4, borderRadius: 2 }}>
              <Box sx={{ textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
                <Typography variant="h5" gutterBottom>
                  Connect to Meta
                </Typography>
                
                <Typography variant="body1" sx={{ mb: 3 }}>
                  {t('metaDashboard.promptLogin')}
                </Typography>
                
                <Alert severity="info" sx={{ mt: 3, textAlign: 'left' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Using a different email for Meta login?
                  </Typography>
                  <Typography variant="body2">
                    If the email associated with your Facebook/Instagram account is different from your D-Unit login email,
                    you can add it in your <Link to="/settings" style={{ fontWeight: 'bold' }}>Settings</Link> page.
                    This will allow you to use that email for Meta authentication.
                  </Typography>
                </Alert>
                
                <MetaLoginButton onSuccess={handleSuccessfulLogin} />
                
                {authError && (
                  <Box sx={{ mt: 3 }}>
                    <Button 
                      variant="outlined" 
                      color="primary" 
                      onClick={handleLoginAgain}
                      disabled={loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Try Logging In Again'}
                    </Button>
                  </Box>
                )}
                
                {error && (
                  <Alert severity="error" sx={{ mt: 3 }}>
                    {error}
                  </Alert>
                )}
              </Box>
            </Paper>
          ) : error && !authError && error.includes("No business accounts found") && !isLoggedIn ? (
            renderNoAccountsSection()
          ) : (
            <>
              {authError && (
                <Box sx={{ my: 4 }}>
                  <Alert 
                    severity="warning" 
                    action={
                      <Button
                        color="primary"
                        variant="contained"
                        size="medium"
                        onClick={handleLoginAgain}
                      >
                        {t('metaDashboard.loginAgainButton')}
                      </Button>
                    }
                  >
                    <Typography variant="subtitle1" component="div" gutterBottom>
                      Your Meta session has expired or is invalid
                    </Typography>
                    <Typography variant="body2">
                      This usually happens when your access token expires (typically after 60 days) or permissions have been revoked.
                      Click the button to log in again and ensure you grant all requested permissions.
                    </Typography>
                  </Alert>
                </Box>
              )}
              
              {error && !authError && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}
              
              {permissionRefreshSuccess && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {t('metaDashboard.successPermissionsRefreshed')}
                </Alert>
              )}
              
              {isLoggedIn && (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: { xs: 'column', md: 'row' }, 
                  justifyContent: 'space-between', 
                  alignItems: { xs: 'stretch', md: 'center' }, 
                  gap: 2,
                  mb: 3 
                }}>
                  {renderPageOptions()}
                  
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: { xs: 'column', sm: 'row' }, 
                    gap: 1 
                  }}>
                    <Button 
                      variant="outlined" 
                      color="primary" 
                      onClick={handleRefreshPages}
                      disabled={loading}
                      fullWidth={!selectedPage}
                    >
                      {loading ? 'Refreshing...' : t('metaDashboard.refreshPagesButton')}
                    </Button>
                    
                    {selectedPage && (
                      <>
                        <Button
                          variant="outlined"
                          color="secondary" 
                          onClick={handleRefreshPermissions}
                          disabled={refreshingPermissions || permissionsLoading}
                        >
                          {refreshingPermissions ? 'Refreshing...' : t('metaDashboard.refreshPermissionsButton')}
                        </Button>
                        
                        <Button
                          variant="outlined"
                          color="error" 
                          onClick={handleDisconnect}
                        >
                          {t('metaDashboard.disconnectButton')}
                        </Button>
                      </>
                    )}
                  </Box>
                </Box>
              )}
              
              {isLoggedIn && !selectedPage && pages.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <MetaBusinessConnect 
                    onConnect={(businessId, fetchedPages) => {
                      logger.debug('Business connected:', businessId);
                      logger.debug('Pages fetched:', fetchedPages);
                      setPages(prev => [...prev, ...fetchedPages]);
                      if (fetchedPages.length > 0 && !selectedPage) {
                        setSelectedPage(fetchedPages[0]);
                      }
                    }}
                    onError={setError}
                    isUserLoggedIn={isLoggedIn}
                  />
                </Box>
              )}
              
              {isLoggedIn && !selectedPage && pages.length > 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Paper variant="outlined" sx={{ p: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Select a Page
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Please select a Facebook page or Instagram account from the dropdown above to view analytics.
                    </Typography>
                  </Paper>
                </Box>
              )}
              
              {selectedPage && (
                <>
                  <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">{t('metaDashboard.pageHeader', 'Page')}</Typography>
                        <Typography variant="h6">{selectedPage.name}</Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">{t('metaDashboard.categoryHeader', 'Category')}</Typography>
                        <Typography variant="h6">{selectedPage.category || 'N/A'}</Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">{t('metaDashboard.idHeader', 'ID')}</Typography>
                        <Typography variant="h6">{selectedPage.id}</Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                  
                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs 
                      value={tabValue} 
                      onChange={handleTabChange} 
                      aria-label="meta analytics tabs"
                      variant="scrollable"
                      scrollButtons="auto"
                    >
                      <Tab label={tWithDefault('metaDashboard.tabs.overview', 'Overview')} />
                      <Tab label={tWithDefault('metaDashboard.tabs.posts', 'Posts')} />
                      <Tab label={tWithDefault('metaDashboard.tabs.audience', 'Audience')} />
                      <Tab label={tWithDefault('metaDashboard.tabs.adMetricsTab', 'Ad Metrics')} />
                      <Tab label={tWithDefault('metaDashboard.tabs.campaigns', 'Campaigns')} />
                      <Tab label={tWithDefault('metaDashboard.tabs.permissions', 'Permissions')} />
                    </Tabs>
                  </Box>
                  
                  {selectedPage && !selectedPage.access_token && selectedPage.platform === 'facebook' && (
                    <Alert severity="warning" sx={{ mt: 2, mb: 1 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Missing Page Access Token
                      </Typography>
                      <Typography variant="body2">
                        This page doesn't have a valid access token, which is required to fetch metrics data.
                        Try disconnecting and reconnecting with Meta, making sure to grant all requested permissions.
                        If you're using an Instagram account, make sure it's linked to a Facebook page you manage.
                      </Typography>
                    </Alert>
                  )}
                  
                  <TabPanel value={tabValue} index={0}>
                    {/* Overview Summary Cards - For both Instagram and Facebook */}
                    {selectedPage ? (
                      <MetaOverviewPanel page={selectedPage} />
                    ) : (
                      <Alert severity="info" sx={{ m: 2 }}>
                        Please select a page to view overview metrics.
                      </Alert>
                    )}
                    
                    {/* Detailed Charts - Temporarily disabled to test MetaOverviewPanel */}
                    {false && <Grid container spacing={3} sx={{ mt: 3 }}>
                      <Grid item xs={12} md={6}>
                        <RestrictedWidget 
                          featureKey="page_impressions" 
                          title={selectedPage?.platform === 'instagram' 
                            ? tWithDefault('metaDashboard.common.instagramReachChartTitle', 'Reach') 
                            : tWithDefault('metaDashboard.common.pageImpressionsChartTitle', 'Page Impressions')}
                          platform={selectedPage?.platform || 'facebook'}
                        >
                          <MetaMetricsPanel 
                            page={selectedPage || undefined}
                            insightType={MetricType.PAGE_IMPRESSIONS}
                            permissionFeatureKey="page_impressions"
                            titleKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramReachChartTitle" 
                              : "metaDashboard.common.pageImpressionsChartTitle"}
                            descriptionKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.pageImpressionsChartDescIG" 
                              : "metaDashboard.common.pageImpressionsChartDesc"}
                          />
                        </RestrictedWidget>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <RestrictedWidget 
                          featureKey="page_engagement" 
                          title={selectedPage?.platform === 'instagram' 
                            ? tWithDefault('metaDashboard.common.instagramEngagementChartTitle', 'Engagement') 
                            : tWithDefault('metaDashboard.common.pageEngagementChartTitle', 'Page Engagement')}
                          platform={selectedPage?.platform || 'facebook'}
                        >
                          <MetaMetricsPanel 
                            page={selectedPage || undefined}
                            insightType={MetricType.PAGE_ENGAGEMENT}
                            permissionFeatureKey="page_engagement"
                            titleKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramEngagementChartTitle" 
                              : "metaDashboard.common.pageEngagementChartTitle"}
                            descriptionKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramEngagementChartDesc" 
                              : "metaDashboard.common.pageEngagementChartDescFB"}
                            chartType="bar"
                          />
                        </RestrictedWidget>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <RestrictedWidget 
                          featureKey="page_fans" 
                          title={selectedPage?.platform === 'instagram' 
                            ? tWithDefault('metaDashboard.common.instagramFollowersChartTitle', 'Followers') 
                            : tWithDefault('metaDashboard.common.pageFollowersChartTitle', 'Page Followers')}
                          platform={selectedPage?.platform || 'facebook'}
                        >
                          <MetaMetricsPanel 
                            page={selectedPage || undefined}
                            insightType={MetricType.PAGE_FANS}
                            permissionFeatureKey="page_fans"
                            titleKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramFollowersChartTitle" 
                              : "metaDashboard.common.pageFollowersChartTitle"}
                            descriptionKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramFollowersChartDesc" 
                              : "metaDashboard.common.pageFollowersChartDesc"}
                          />
                        </RestrictedWidget>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <RestrictedWidget 
                          featureKey="page_views" 
                          title={selectedPage?.platform === 'instagram' 
                            ? tWithDefault('metaDashboard.common.instagramProfileViewsChartTitle', 'Profile Views') 
                            : tWithDefault('metaDashboard.common.pageViewsChartTitle', 'Page Views')}
                          platform={selectedPage?.platform || 'facebook'}
                        >
                          <MetaMetricsPanel 
                            page={selectedPage || undefined}
                            insightType={MetricType.PAGE_VIEWS_TOTAL}
                            permissionFeatureKey="page_views"
                            titleKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramProfileViewsChartTitle" 
                              : "metaDashboard.common.pageViewsChartTitle"}
                            descriptionKey={selectedPage?.platform === 'instagram' 
                              ? "metaDashboard.common.instagramProfileViewsChartDesc" 
                              : "metaDashboard.common.pageViewsChartDesc"}
                            chartType="bar"
                          />
                        </RestrictedWidget>
                      </Grid>
                    </Grid>}
                  </TabPanel>
                  
                  <TabPanel value={tabValue} index={1}>
                    <RestrictedWidget 
                      featureKey="posts_analytics" 
                      title={tWithDefault('metaDashboard.common.postsAnalyticsChartTitle', 'Posts Analytics')}
                      platform={selectedPage?.platform || 'facebook'}
                    >
                      <MetaPostsAnalytics page={selectedPage} useMockData={usingMockData} />
                    </RestrictedWidget>
                  </TabPanel>
                  
                  <TabPanel value={tabValue} index={2}>
                    <RestrictedWidget 
                      featureKey="audience_demographics" 
                      title={tWithDefault('metaDashboard.common.audienceDemographicsChartTitle', 'Audience Demographics')}
                      platform={selectedPage?.platform || 'facebook'}
                    >
                      <MetaAudienceDemographics page={selectedPage} useMockData={usingMockData} />
                    </RestrictedWidget>
                  </TabPanel>
                  
                  <TabPanel value={tabValue} index={3}>
                    <RestrictedWidget 
                      featureKey="ad_metrics" 
                      title={tWithDefault('metaDashboard.common.adMetricsChartTitle', 'Ad Metrics')}
                      platform={selectedPage?.platform || 'facebook'}
                    >
                      <MetaAdMetricsPanel page={selectedPage} />
                    </RestrictedWidget>
                  </TabPanel>
                  
                  <TabPanel value={tabValue} index={4}>
                    <RestrictedWidget 
                      featureKey="campaigns_comparison" 
                      title={tWithDefault('metaDashboard.common.campaignsComparisonChartTitle', 'Campaigns Comparison')}
                      platform={selectedPage?.platform || 'facebook'}
                    >
                      <MetaCampaignsComparison page={selectedPage} useMockData={usingMockData} />
                    </RestrictedWidget>
                  </TabPanel>
                  
                  <TabPanel value={tabValue} index={5}>
                    <MetaPermissionsPanel page={selectedPage} useMockData={usingMockData} />
                  </TabPanel>
                </>
              )}
            </>
          )}
        </Box>
      </PageContainer>
    </Box>
  );
}; 



