# D-Unit Backend Documentation

AI-driven e-commerce analytics platform backend built with FastAPI, MongoDB, and MySQL.

## Quick Start

### Prerequisites
- Python 3.11.9
- MongoDB (local or Atlas)
- MySQL with La Nube data
- Environment variables (see `.env.template`)

### Setup & Commands
```bash
# Setup
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
pip install -r requirements-test.txt  # For testing

# Run server (HTTPS on https://127.0.0.1:8000)
python main.py

# Run tests
pytest                    # All tests
pytest -m unit           # Unit tests only
pytest --cov=. --cov-report=html  # With coverage
```

## Architecture Overview

### Technology Stack
- **Framework**: FastAPI with async support
- **Databases**: MongoDB Atlas (primary), MySQL (La Nube source)
- **Authentication**: JWT + bcrypt
- **AI**: OpenAI GPT-4 models (GPT-4.1-mini, o4-mini, GPT-4.1, GPT-4.1-nano)
- **Search**: Brave Search, Google Custom Search
- **Deployment**: AWS Elastic Beanstalk + CloudFront

### Core Components
1. **Security Gateway**: 4-layer protection (headers, CSRF, rate limiting, validation)
2. **API Modules**: Auth, Store, Meta, Chat, Insights, Admin, Security
3. **Data Pipeline**: 11 scripts transforming MySQL → MongoDB → AI embeddings
4. **Background Tasks**: Meta sync, cost tracking, security monitoring

## Security Architecture

### Security Layers
1. **SecurityHeadersMiddleware**: HSTS, CSP, X-Frame-Options
2. **CSRFMiddleware**: Token validation with rotation on sensitive operations
3. **SecurityGatewayMiddleware**: Threat detection, rate limiting, cost control
4. **RequestValidationMiddleware**: Input sanitization, SQL/XSS protection

### Security Features
- **Rate Limiting**: Token bucket, sliding window, endpoint-specific
- **Cost Control**: Real-time OpenAI API tracking with budget enforcement
- **Threat Detection**: Attack pattern recognition, automated blocking
- **Monitoring**: security_events, cost_tracking, budget_alerts collections
- **Authentication**: JWT with role-based access (user/admin)

### Access Control
- `verify_user_can_access_store`: Store-specific endpoint protection
- `get_current_active_admin_user`: Admin role enforcement
- Centralized password hashing with bcrypt

## API Reference

### Endpoint Structure
```
/api/auth/*          # Authentication, registration, 2FA
/api/store/{id}/*    # Store data, products, analytics
/api/meta/*          # Facebook/Instagram integration
/api/chat/{id}/*     # AI chat (multipart/form-data)
/api/insights/{id}/* # AI-generated insights
/api/admin/*         # Admin functions (role-protected)
/api/security/*      # Security monitoring
```

### Key Endpoints by Module

#### Authentication (`/api/auth`)
- `POST /api/auth/token` - Login with email/password
- `POST /api/auth/2fa/verify-login` - 2FA verification
- `POST /api/auth/google` - Google OAuth login
- `POST /api/auth/dunit/register` - User registration
- `POST /api/auth/forgot-password` - Password reset
- `GET /api/auth/user/me` - Current user profile

#### Store Management (`/api/store/{store_id}`)
- `GET /api/store/{id}/product-list` - Paginated products (from cache)
- `GET /api/store/{id}/analysis` - Store analytics with recent activity
- `GET /api/store/{id}/customers` - Customer data (legacy/stub)
- `GET /api/store/{id}/sales` - Time-series sales data
- `POST /api/store/{id}/feedback` - Submit user feedback
- `GET /api/store/{id}/seo-recommendations` - AI SEO suggestions

#### Meta Integration (`/api/meta`)
- `POST /api/meta/connect` - Connect Meta business account
- `GET /api/meta/{store_id}/pages` - List connected pages
- `GET /api/meta/pages/{page_id}/ad-metrics` - Ad performance
- `GET /api/meta/{store_id}/pages/{page_id}/audience` - Demographics
- `POST /api/meta/sync/{store_id}` - Manual data sync
- `GET /api/meta/permissions/{store_id}` - Permission status

#### Chat System (`/api/chat/{store_id}`)
- `POST /api/chat/{store_id}` - Send message (multipart/form-data)
- `GET /api/chat/history/{store_id}` - Conversation history
- `PUT /api/chat/{conversation_id}/rename` - Rename conversation
- `DELETE /api/chat/{conversation_id}` - Delete conversation

#### Insights (`/api/insights`)
- `POST /api/insights/process-posts` - Content analysis
- `POST /api/insights/engagement` - Engagement insights
- `POST /api/insights/correlate-posts-sales` - Attribution analysis
- `POST /api/insights/forecast` - Predictive analytics

### Request Standards
- **Auth**: Bearer token in `Authorization` header
- **CSRF**: `X-CSRF-Token` header for state changes
- **Pagination**: `page`, `pageSize` parameters
- **Time Filtering**: `timeRange`, `since`, `until`

### Error Response Format
```json
{
  "detail": "Human-readable message",
  "code": "ERROR_CODE", 
  "trace_id": "uuid",
  "metadata": { "field": "name", "suggestion": "fix" }
}
```

### Meta API Fallback System
- Primary: Meta Graph API with page access tokens
- Fallback: MongoDB cached data when API fails
- Direct endpoints: `/api/meta/direct-metrics`, `/api/meta/ad-metrics`
- Caching: TTL-based cleanup with time-aware retrieval

## Database Schema

### MongoDB Collections
- **D-Unit Database**: User auth, operational data
  - `store_users`: Authentication and profiles
  - `active_stores_cache`: Store metrics and revenue
  - `product_details_cache`: Product performance
  - `store_customers_cache`: Customer analytics
  - `store_activity_metrics`: Time-based metrics

- **D-Unit-AnalysisGPT Database**: Analytics and AI
  - `global_analysis`: AI business insights
  - `meta_*`: Facebook/Instagram data
  - `*_embeddings`: Vector embeddings for search
  - `store_chats`: Conversation history
  - `security_*`: Security monitoring

### Revenue Calculation
- **Valid Orders**: Status 2 (Pago), 5 (Pedido Entregado), 7 (Completo)
- **Formula**: `sum(item_prices) + shipping_cost - order_discount`
- **Currency**: Local with USD conversion

## Meta Integration

### Features
- Facebook/Instagram page connection
- Granular permission management
- Scheduled data synchronization (30 min)
- Mock data for development (store_id: "566")

### Permission System
- Data point mapping (followers → instagram_basic)
- Permission history tracking
- Partial data access when limited permissions
- Clear error messages for missing permissions

### Key Endpoints
- `POST /api/meta/connect`: Connect account
- `GET /api/meta/pages`: List pages
- `GET /api/meta/pages/{id}/metrics`: Performance data
- `GET /api/meta/permissions/{store_id}`: Permission status

## Chat System

### Features
- **Modes**: Default (GPT-4-mini), Think (o4-mini), DeepSearch (Brave), DeeperSearch (Google)
- **Multimodal**: Text + image analysis
- **Context**: Store data, Meta metrics, customer insights
- **Storage**: Conversations in `store_chats` collection

### API Usage
```bash
POST /api/chat/{store_id}
Content-Type: multipart/form-data

Fields:
- message: User text
- conversation_id: Existing thread (optional)
- mode: Processing mode (optional)
- image: Upload file (optional)
```

## Data Pipeline

### Script Execution Order
1. `update_store_users.py` - Sync MySQL users → MongoDB
2. `update_active_stores.py` - Identify active stores
3. `update_product_details.py` - Product performance cache
4. `update_product_variations.py` - SKU variations
5. `update_product_categories.py` - Category enrichment
6. `update_meta_sales_correlation.py` - Ad campaign correlation
7. `update_customers_relationships.py` - Customer analytics
8. `update_store_activity_metrics.py` - Activity tracking
9. `market_analyzer.py` - AI business analysis
10. `update_competitor_analysis.py` - Competitive insights
11. `embeddings_generator_analysis.py` - Vector embeddings

## Testing Infrastructure

### Test Categories
| Category | Purpose | Markers |
|----------|---------|---------|
| Unit | Component isolation | `pytest -m unit` |
| Integration | Component interaction | `pytest -m integration` |
| Security | Vulnerability testing | `pytest -m security` |
| Load | Performance testing | `pytest -m load` |
| Cost | Budget enforcement | `pytest -m cost` |

### Key Testing Features
- Async support with pytest-asyncio
- Mock fixtures for database isolation
- Security scanning with Bandit
- Performance benchmarking
- Coverage reporting

## Configuration

### Environment Variables
```env
# Core
MONGODB_CONNECTION=
JWT_SECRET_KEY=
OPENAI_API_KEY=

# Meta Integration
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=

# Search APIs
BRAVE_SEARCH_API_KEY=
GOOGLE_CUSTOM_SEARCH_API_KEY=

# Security
CSRF_SECRET_KEY=
SECURITY_ENCRYPTION_KEY=
```

### Store Filtering
- **Exception List**: 94 manually active stores
- **Auto-Include**: Stores created ≥ 2025
- **Activity Criteria**: Orders, visits, products, updates

### AI Models
- **Default**: gpt-4-mini
- **Think**: o4-mini
- **Embeddings**: text-embedding-3-small

## Deployment

### Production (AWS)

#### Architecture
- **Application**: EKS (Kubernetes) with ECR images
- **Alternative**: Elastic Beanstalk with Gunicorn + 3 Uvicorn workers
- **Frontend**: S3 + CloudFront CDN
- **SSL**: Load balancer termination
- **Monitoring**: CloudWatch integration

#### EKS Deployment Process
```bash
# Build and push image
docker build -t d-unit-backend:latest .
aws ecr get-login-password --region sa-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.sa-east-1.amazonaws.com
docker tag d-unit-backend:latest <account-id>.dkr.ecr.sa-east-1.amazonaws.com/d-unit-backend:latest
docker push <account-id>.dkr.ecr.sa-east-1.amazonaws.com/d-unit-backend:latest

# Deploy to EKS
aws eks --region sa-east-1 update-kubeconfig --name d-unit-backend
kubectl apply -f d-unit-backend-secrets.yaml
kubectl apply -f d-unit-backend-configmap.yaml
kubectl apply -f d-unit-backend-deployment.yaml
kubectl apply -f d-unit-backend-service.yaml

# Verify deployment
kubectl get pods
kubectl get svc
```

#### Frontend Deployment (S3 + CloudFront)
```bash
# Build and deploy frontend
cd frontend
rm -rf node_modules dist
npm install
npm run build
aws s3 sync dist/ s3://d-unit-frontend-staging --delete
aws cloudfront create-invalidation --distribution-id E2EWNJ9W953OH9 --paths "/*"
```

### Development
- Uvicorn with auto-reload
- Self-signed SSL certificates in `/certs`
- Debug logging enabled
- Hot reload for rapid development

## Monitoring & Logging

### Logging Configuration
- Centralized in `config/logging_config.py`
- Sensitive data filtering
- Environment-based levels (DEBUG/INFO)
- Structured logging with trace IDs

### Security Monitoring
- Real-time threat detection
- Cost tracking per store
- Rate limit violations
- Automated alerts for critical events

## Troubleshooting

### Common Issues
1. **Meta Rate Limits**: Implement exponential backoff
2. **Data Sync Delays**: Check script schedules (30min-24h)
3. **CSRF Errors**: Ensure token refresh on client
4. **Mock Data**: Store 566 contains test data

### Recent Fixes
- Meta permissions dictionary access (April 2024)
- Password hashing migration (August 2024)
- CSRF token rotation (September 2024)

## Internationalization (i18n)

### Frontend Translations
- **Framework**: i18next with react-i18next
- **Languages**: English (en), Spanish (es)
- **Location**: `frontend/public/locales/{lang}/translation.json`
- **Detection**: Browser language → localStorage → fallback to English

### Usage Pattern
```javascript
import { useTranslation } from 'react-i18next';
const { t } = useTranslation();
return <h1>{t('dashboard.title')}</h1>;
```

### Adding New Languages
1. Create directory: `frontend/public/locales/{lang}/`
2. Copy and translate `translation.json`
3. Add language to `supportedLngs` in `i18n.ts`
4. Update language selector component

### Meta Dashboard Translations
- Use `metaDashboard.*` namespace for Meta components
- Include error messages, chart labels, no-data states
- Example: `t('metaDashboard.audience.errorMetricsUnavailable')`

## Development Guidelines

### Code Organization
- **Routes**: Request/response handling
- **Services**: Business logic
- **Models**: Data structures
- **Scripts**: Data processing pipeline

### Best Practices
- Use centralized auth dependencies (`verify_user_can_access_store`)
- Follow revenue calculation standards (status 2,5,7 only)
- Maintain test coverage >90%
- Document API changes
- Use i18n for all user-facing text

### Security Requirements
- Never store plaintext passwords
- Use CSRF tokens for state changes
- Validate all inputs with Pydantic
- Log security events with trace IDs
- Use Meta page access tokens for API calls

### Performance Guidelines
- Use MongoDB indexes for frequent queries
- Implement caching for expensive operations
- Batch process large datasets
- Use async/await for I/O operations

## License

Proprietary - All rights reserved

## Credits System

The **Credits System** controls access to resource-intensive chat features.

| Mode | Credits Deducted |
|------|------------------|
| Default chat | 10,000 |
| Think | 20,000 |
| Deep Search / Deeper Search | 40,000 |

Rules:
1. Each store document (`active_stores_cache`) now has a **`credits`** integer at the root level.
2. Initial migration sets every store to **1,000,000** credits.
3. Requests with insufficient credits receive **HTTP 402 – "Insufficient credits"**.
4. Credits are deducted atomically only after a successful chat response.
5. Low-balance (<100,000) is logged but not blocked.
6. Endpoint `GET /api/store/{store_id}/credits` returns the current balance.

Migration Script:
```
python -m backend.scripts.data.assign_initial_store_credits
```
Run once after deploying the code. It is idempotent.


## Store Credits Recharge

### Weekly Automatic Recharge
The script `backend/scripts/data/recharge_store_credits_weekly.py` credits **+250&nbsp;000** units to every store document in `active_stores_cache` once per ISO-week.  
It is **idempotent**: each store keeps a `last_recharge_date` timestamp; the script skips stores already credited during the current week.

Example cron (UTC):
```
0 0 * * 1 /usr/bin/python /opt/dunit/backend/scripts/data/recharge_store_credits_weekly.py >> /var/log/dunit/recharge.log 2>&1
```

### Manual Addition Endpoint
```
POST /api/admin/store/{store_id}/add-credits
```
Body:
```json
{ "amount": 10000 }
```
Response:
```json
{ "credits": 110000 }
```
Only authenticated admin users may call this route. The operation is atomic and logged (`credits` field incremented via `$inc`).