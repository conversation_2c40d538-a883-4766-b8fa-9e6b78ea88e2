import React from 'react';
import {
  Card, 
  CardContent, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemText, 
  Chip,
  Grid,
  Theme
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CategorySummary } from '../../services/storeService';

interface ProductCategorySummaryCardProps {
  categorySummary: CategorySummary | null;
}

const ProductCategorySummaryCard: React.FC<ProductCategorySummaryCardProps> = ({ categorySummary }) => {
  const { t } = useTranslation();

  if (!categorySummary) {
    return (
      <Card elevation={0} variant="outlined">
        <CardContent>
          <Typography color="text.secondary">{t('categorySummary.noData', 'No category summary data available.')}</Typography>
        </CardContent>
      </Card>
    );
  }

  type ListItemType = { name: string; product_count?: number; featured_count?: number; sale_count?: number };
  const renderList = (title: string, items: ListItemType[] | undefined, countKey: string) => {
    if (!items || items.length === 0) return null;
    return (
      <Box mb={2}>
        <Typography variant="subtitle2" gutterBottom>{title}</Typography>
        <List dense disablePadding>
          {items.slice(0, 5).map((item) => (
            <ListItem
              key={item.name}
              sx={theme => theme.palette.mode === 'dark' ? {
                py: 0.5,
                px: 1.5,
                mb: 1,
                backgroundColor: theme.palette.background.paper,
                color: '#fff',
                borderRadius: 2,
                transition: 'background 0.2s',
                '&:hover': {
                  backgroundColor: '#222',
                  color: '#fff',
                },
              } : {
                py: 0.5,
                px: 1.5,
                mb: 1
              }}
            >
              <ListItemText
                primary={item.name}
                secondary={`${item[countKey as keyof ListItemType]} ${countKey.includes('count') ? t('categorySummary.productsSuffix', 'products') : t('categorySummary.itemsSuffix', 'items')}`}
                primaryTypographyProps={{ sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
                secondaryTypographyProps={{ sx: { color: (theme: Theme) => theme.palette.mode === 'dark' ? '#fff' : undefined } }}
              />
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  const renderDistribution = (distribution: Record<string, number> | undefined) => {
    if (!distribution || Object.keys(distribution).length === 0) return null;
    const sortedDistribution = Object.entries(distribution).sort(([, a], [, b]) => b - a);
    return (
      <Box mb={2}>
        <Typography variant="subtitle2" gutterBottom>{t('categorySummary.distributionTitle', 'Category Distribution')}</Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {sortedDistribution.slice(0, 10).map(([name, value]) => (
            <Chip key={name} label={`${name}: ${value.toFixed(1)}%`} size="small" variant="outlined" />
          ))}
        </Box>
      </Box>
    );
  };

  return (
    <Card elevation={0} variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('categorySummary.cardTitle', 'Category Summary')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Typography variant="body2">{t('categorySummary.uniqueCats', 'Unique Categories:')} {categorySummary.unique_categories ?? 'N/A'}</Typography>
            <Typography variant="body2">{t('categorySummary.uniqueSubCats', 'Unique Subcategories:')} {categorySummary.unique_subcategories ?? 'N/A'}</Typography>
            <Typography variant="body2">{t('categorySummary.totalWithCats', 'Products w/ Categories:')} {categorySummary.total_products_with_categories ?? 'N/A'}</Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            {renderDistribution(categorySummary.category_distribution)}
          </Grid>
          <Grid item xs={12} sm={4}>
            {renderList(t('categorySummary.topCats', 'Top Categories'), categorySummary.top_categories, 'product_count')}
          </Grid>
          <Grid item xs={12} sm={4}>
            {renderList(t('categorySummary.featuredCats', 'Top Featured Categories'), categorySummary.top_featured_categories, 'featured_count')}
          </Grid>
          <Grid item xs={12} sm={4}>
            {renderList(t('categorySummary.saleCats', 'Top On Sale Categories'), categorySummary.top_sale_categories, 'sale_count')}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ProductCategorySummaryCard; 