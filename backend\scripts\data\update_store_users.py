#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>ript to migrate store user data from MySQL (lanube.store_users) 
to MongoDB (D-Unit.store_users).

Fetches specified fields and adds an 'alternate_emails' field.
Handles duplicate id_store values by selecting the most appropriate record.
"""

import os
import sys
import logging
import mysql.connector
import pymongo
from pymongo.errors import ConnectionFailure, PyMongoError
from datetime import datetime, timezone
import traceback
from typing import Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Configuration from environment variables ---
# MySQL Configuration
MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
MYSQL_USER = os.getenv('MYSQL_USER', 'root')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
MYSQL_DB_LANUBE = os.getenv('MYSQL_DB_LANUBE', 'lanube')
MYSQL_TABLE = 'store_users'

# MongoDB Configuration
MONGO_URI = os.getenv('MONGODB_CONNECTION')
MONGO_DB_NAME = os.getenv('MONGO_DB_NAME', 'D-Unit')
MONGO_COLLECTION_NAME = "store_users"

# Fields to select from MySQL
MYSQL_FIELDS = "id_store, id_user, name, last_name, email, password, active, created_at, updated_at, cod, cod_confir"

# --- Setup Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def safe_get(row: Any, key: str, default: Any = None) -> Any:
    """Safely get a value from a row object regardless of its type."""
    try:
        # Try direct dictionary access first
        if isinstance(row, dict) and key in row:
            return row[key]
        # Fallback to attribute access
        return getattr(row, key, default)
    except (KeyError, AttributeError):
        return default

def select_best_record(rows):
    """
    Select the best record from multiple records with the same id_store.
    
    Selection criteria (in order of priority):
    1. Active records (active=1) preferred over inactive
    2. Non-null cod values preferred over null ones
    3. Most recently updated record
    4. Original record (lowest id_user) if all else is equal
    """
    if not rows:
        return None
    if len(rows) == 1:
        return rows[0]
    
    # First, prioritize active records
    active_rows = [row for row in rows if safe_get(row, 'active') == 1]
    if active_rows:
        candidate_rows = active_rows
    else:
        candidate_rows = rows
    
    # Next, prioritize records with non-null cod
    non_null_cod_rows = [row for row in candidate_rows if safe_get(row, 'cod') is not None]
    if non_null_cod_rows:
        candidate_rows = non_null_cod_rows
    
    # Finally, sort by updated_at (most recent first), then by id_user (lowest first)
    sorted_rows = sorted(
        candidate_rows, 
        key=lambda r: (
            safe_get(r, 'updated_at') or datetime.min.replace(tzinfo=timezone.utc),
            -1 * (safe_get(r, 'id_user') or 0)
        ),
        reverse=True
    )
    
    return sorted_rows[0]

# --- Main Migration Function ---
def migrate_store_users():
    """Connects to databases, fetches data, and migrates it."""
    mysql_conn = None
    mongo_client = None
    inserted_count = 0
    skipped_count = 0
    error_count = 0
    duplicate_count = 0
    processed_count = 0
    validation_skipped_count = 0
    store_records = {}  # Dictionary to group records by id_store

    # Validate required environment variables
    missing_vars = []
    for var_name in ['MONGODB_CONNECTION']:
        if os.getenv(var_name) is None:
            missing_vars.append(var_name)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file or set these environment variables")
        sys.exit(1)

    try:
        # --- Connect to MySQL ---
        logger.info(f"Connecting to MySQL: {MYSQL_HOST}:{MYSQL_PORT} DB: {MYSQL_DB_LANUBE}")
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            connect_timeout=10
        )
        mysql_cursor = mysql_conn.cursor(dictionary=True) # Fetch rows as dictionaries
        logger.info("Connected to MySQL.")

        # --- Connect to MongoDB ---
        logger.info(f"Connecting to MongoDB: {MONGO_URI} DB: {MONGO_DB_NAME}")
        try:
            mongo_client = pymongo.MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
            # The ismaster command is cheap and does not require auth.
            mongo_client.admin.command('ismaster') 
            mongo_db = mongo_client[MONGO_DB_NAME]
            mongo_collection = mongo_db[MONGO_COLLECTION_NAME]
            # Add unique constraint handling
            try:
                mongo_collection.create_index([("id_store", 1), ("email", 1)], unique=True)
                logger.info("Created unique index on (id_store, email)")
            except Exception as e:
                logger.warning(f"Could not create unique index: {e}")
            
            # Optional: Create an index on id_store for faster lookups if needed
            # Consider if duplicates should be prevented or handled differently.
            mongo_collection.create_index("id_store", unique=False) # Not unique as _id is the unique key
            logger.info(f"Connected to MongoDB database '{MONGO_DB_NAME}', collection '{MONGO_COLLECTION_NAME}'.")
        except ConnectionFailure as e:
            logger.error(f"MongoDB Connection Error: {e}")
            if mysql_conn and mysql_conn.is_connected():
                 mysql_conn.close()
            sys.exit(1)

        # --- Fetch data from MySQL ---
        # CHANGE: Filter out soft-deleted records
        query = f"SELECT {MYSQL_FIELDS} FROM {MYSQL_TABLE} WHERE deleted_at IS NULL ORDER BY id_store, active DESC, updated_at DESC"
        logger.info(f"Executing MySQL query: {query}")
        mysql_cursor.execute(query)
        logger.info("Fetching MySQL data...")
        
        # First, group all records by id_store to handle duplicates
        mysql_rows = mysql_cursor.fetchall()
        logger.info(f"Fetched {len(mysql_rows)} total rows from MySQL (excluding soft-deleted records).")
        
        # Group by id_store
        for row in mysql_rows:
            processed_count += 1
            id_store = safe_get(row, "id_store")
            if id_store not in store_records:
                store_records[id_store] = []
            store_records[id_store].append(row)
        
        logger.info(f"Found {len(store_records)} unique store IDs out of {processed_count} total records.")
        
        # Select the best record for each id_store
        deduplicated_rows = []
        for id_store, rows in store_records.items():
            if len(rows) > 1:
                duplicate_count += len(rows) - 1
                best_record = select_best_record(rows)
                deduplicated_rows.append(best_record)
                logger.debug(f"Selected best record from {len(rows)} duplicates for store ID {id_store}")
            else:
                deduplicated_rows.append(rows[0])
        
        logger.info(f"After deduplication: {len(deduplicated_rows)} records to process (eliminated {duplicate_count} duplicates)")
        
        # Process the deduplicated records
        for row in deduplicated_rows:
            try:
                # Use our safe accessor method to get values from row
                id_store = safe_get(row, "id_store")
                id_user = safe_get(row, "id_user")
                email = safe_get(row, "email")
                
                # CHANGE: Add email validation
                if not email:
                    logger.warning(f"Skipping record with id_user {id_user} - missing email")
                    validation_skipped_count += 1
                    continue
                
                # Check if store already exists in MongoDB based on id_store
                existing_doc = mongo_collection.find_one({"id_store": id_store})
                
                if existing_doc:
                    # logger.info(f"Skipping id_store {id_store} - already exists in MongoDB.")
                    skipped_count += 1
                    continue # Skip if already present

                # Prepare the document for MongoDB
                mongo_doc = {
                    "id_store": id_store,
                    "id_user": id_user,
                    "name": safe_get(row, "name"),
                    "last_name": safe_get(row, "last_name"),
                    "email": email,
                    "password": safe_get(row, "password"),
                    "active": bool(safe_get(row, "active")), # Ensure boolean
                    "created_at": safe_get(row, "created_at"), 
                    "updated_at": safe_get(row, "updated_at"),
                    "cod": safe_get(row, "cod"),
                    "cod_confir": safe_get(row, "cod_confir"),  # Added this field
                    "alternate_emails": [], # Add the empty array field
                    "migration_info": { # Add some metadata about the migration
                         "migrated_at": datetime.now(timezone.utc),
                         "source": "mysql_lanube_store_users"
                    }
                }

                # Insert the document
                mongo_collection.insert_one(mongo_doc)
                inserted_count += 1
                
                if inserted_count % 100 == 0: # Log progress periodically
                     logger.info(f"Inserted {inserted_count} documents...")

            except Exception as e:
                logger.error(f"Error processing row for id_store {safe_get(row, 'id_store')}, id_user {safe_get(row, 'id_user')}: {e}", exc_info=False) # Set exc_info=True for full traceback
                error_count += 1
                    
        logger.info("Finished processing all records.")

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error: {err}")
        traceback.print_exc()
    except PyMongoError as e:
         logger.error(f"MongoDB Error: {e}")
         traceback.print_exc()
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        traceback.print_exc()
    finally:
        # --- Close connections ---
        if mysql_cursor:
            mysql_cursor.close()
            logger.info("MySQL cursor closed.")
        if mysql_conn and mysql_conn.is_connected():
            mysql_conn.close()
            logger.info("MySQL connection closed.")
        if mongo_client:
            mongo_client.close()
            logger.info("MongoDB connection closed.")

        # --- Final Summary ---
        logger.info("\n--- Migration Summary ---")
        logger.info(f"Total records fetched from MySQL: {processed_count}")
        logger.info(f"Unique store IDs found: {len(store_records)}")
        logger.info(f"Duplicate records eliminated: {duplicate_count}")
        logger.info(f"Documents successfully inserted into MongoDB: {inserted_count}")
        logger.info(f"Documents skipped (already existed): {skipped_count}")
        logger.info(f"Documents skipped (validation failed): {validation_skipped_count}")
        logger.info(f"Errors during migration: {error_count}")
        logger.info("--- End of Script ---")


# --- Script Execution ---
if __name__ == "__main__":
    logger.info("Starting MySQL to MongoDB store_users migration script...")
    migrate_store_users()
    logger.info("Migration script finished.")