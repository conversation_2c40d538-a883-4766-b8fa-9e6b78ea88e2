# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container at /app
COPY requirements.txt ./

# Install any needed packages specified in requirements.txt
# --no-cache-dir: Disables the cache to keep the image size smaller
# --upgrade: Ensures pip, setuptools, and wheel are up-to-date
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the container at /app
COPY . .

# Make port 8000 available to the world outside this container
EXPOSE 8000

# Define environment variable to indicate running in production/docker
ENV ENVIRONMENT=production

# Run main.py without SSL using uvicorn (let AWS LB handle HTTPS)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]