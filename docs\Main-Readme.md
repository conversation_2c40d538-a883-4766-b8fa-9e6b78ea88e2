# D-Unit: AI-Driven E-Commerce Analytics Platform

---
## Latest Operational, Security, and UI/UX Improvements (2024-2025)

- **Enterprise-Grade Security Gateway (2025):**
  - **4-Layer Security Architecture**: OWASP Top 10 protection with security headers, central threat detection, intelligent rate limiting, and comprehensive request validation.
  - **Advanced Cost Management**: Real-time OpenAI cost tracking with store-tier based budget controls (Free, Basic, Premium, Enterprise) and emergency stop mechanisms.
  - **Intelligent Threat Detection**: Real-time attack pattern recognition with automated response for SQL injection, XSS, command injection, and path traversal attacks.
  - **Performance Optimization**: Thread-safe operations with in-memory caching, 35+ database indexes, and TTL-based cleanup for optimal performance.
  - **Production Ready**: 97% complete with all critical components operational, comprehensive monitoring, and security event logging.

- **Comprehensive Testing Infrastructure (2025):**
  - **100% Complete Testing Framework**: 35 test tasks across 7 phases covering unit tests, integration tests, load tests, security tests, and cost tracking tests.
  - **CI/CD Pipeline**: Complete GitHub Actions workflow with multi-Python version testing, MongoDB/Redis services, coverage reporting, and performance regression testing.
  - **5-Category Testing**: Unit testing (8 components), integration testing (4 workflows), load testing (4 scenarios), security testing (4 attack types), and cost tracking testing (3 validation areas).
  - **Advanced Monitoring**: Test execution monitoring, performance metrics collection, security scanning automation, and dashboard generation capabilities.
  - **Production Validation**: 20/20 tests passing (100% success rate) with comprehensive coverage of all security gateway components.

- **Security Enhancements:**
  - Passwords are now securely encrypted using bcrypt, reducing risk of exposure and improving compliance.
  - Role-Based Access Control (RBAC) is enforced, with clear separation between admin and regular users, and consistent access checks across the platform.
  - Cross-store access protection and enhanced security logging are in place.
  - The password reset process remains unchanged, but all new passwords are protected with enhanced security.

- **Cookie Management System (2025):**
  - **Comprehensive Legal Compliance:** Implemented GDPR, CCPA, LGPD, and PIPEDA compliant cookie management system with essential vs optional cookie distinction.
  - **Professional UI Components:** CookieYes-like banner and settings interface with Material-UI integration, detailed cookie information tables, and multi-language support (English/Spanish).
  - **Granular Cookie Control:** Five cookie categories (essential, functional, analytics, performance, marketing) with individual enable/disable controls and detailed descriptions.
  - **Enhanced Authentication Security:** Migrated from localStorage to secure, httpOnly cookie-based authentication using the cookie management service.
  - **Audit Trail & Compliance:** Complete consent versioning with unique consent IDs, timestamp tracking, IP address recording, and user agent logging for legal compliance.
  - **Event-Driven Architecture:** Real-time preference updates across components with custom event dispatching and state synchronization.
  - **Bilingual Translation System:** Complete English/Spanish translation support for 14 cookie purposes, 5 categories, and time duration units (hour/hours, day/days, year/years) with translateDuration utility function.
  - **Cookie Policy Page:** Comprehensive legal documentation page with full cookie inventory table, regulatory compliance information, interactive features, and navigation integration.
  - **Enhanced User Experience:** Seamless navigation from cookie policy to settings page with automatic scrolling to cookie management section, links to privacy policy and cookie policy in settings interface.
  - **Professional Legal Documentation:** Complete cookie inventory with detailed purpose descriptions, category classifications, duration specifications, provider information, and regulatory framework compliance (GDPR, CCPA, LGPD, PIPEDA).
  - **Advanced Cookie Management:** Cookie chips display with detailed tooltips showing translated information, comprehensive settings interface with expandable categories, reset functionality, and legal rights information.

- **Admin Portal & Access:**
  - Dedicated admin login at `/backend` with simplified authentication and direct access to the Admin Portal.
  - Admins can view and select any store for analysis, with search and pagination features for large store lists.
  - Improved error handling and messaging for admin authentication and access.

- **Meta Integration & Analytics:**
  - Improved Meta (Facebook/Instagram) integration, including robust permission handling, error feedback, and support for alternate emails.
  - Enhanced Meta dashboard with better date range filtering, visual indicators, and consistent formatting.
  - Improved campaign and ad metrics visualization, with dual-axis charts, tooltips, and ROI analysis.
  - **NEW: Social Media Analytics Dashboard (January 2025)**: Comprehensive `/socialmedia` page with dedicated Facebook, Instagram, and Marketing tools tabs, granular feature toggles, real-time metrics with growth tracking, and full Meta Graph API integration.

- **UI/UX Improvements:**
  - Standardized color scheme and chart components for a consistent look and feel.
  - Enhanced loading indicators, message rendering, and chat history management in the chat interface.
  - Dark mode support, improved responsive layouts, and better error handling throughout the frontend.
  - **Enhanced Chat Input Styling (May 2024):**
    - Improved chat input container with rounded corners and subtle left/right borders for better visual distinction.
    - Custom border colors that adapt to both light and dark themes using CSS variables.
    - Increased vertical expansion capacity for the input field to better accommodate larger messages.
    - Subtle visual separation between the message list and input area for improved readability.
    - Maintained consistent styling across the application while enhancing the chat experience.
  - **Footer Link Enhancements:**
    - Added a hyperlink to the 'La Nube' logo, directing users to https://lanube.cloud/.
    - Made the 'D-Unit' text in the copyright notice a hyperlink to https://d-unit.world/.

- **Deployment & Monitoring:**
  - Comprehensive AWS deployment guide for backend (Elastic Beanstalk) and frontend (S3 + CloudFront).
  - Security best practices for SSL/TLS, environment variable management, and API protection.
  - CloudWatch integration for monitoring, logging, and alerting.

- **Troubleshooting & Maintenance:**
  - Improved logging and error reporting for both backend and frontend.
  - Detailed troubleshooting notes for Meta permissions and authentication issues.
  - Ongoing linter compliance, code quality improvements, and TypeScript optimizations.

For more details, see the full sections below.

---

## 🛡️ Enterprise-Grade Security Infrastructure (2025)

### **4-Layer Security Gateway Architecture**

D-Unit implements a comprehensive security infrastructure that provides enterprise-level protection:

#### **Layer 1: Security Headers Middleware**
- **OWASP Top 10 Protection**: Complete coverage of web application security risks
- **HTTP Security Headers**: HSTS, CSP, X-Frame-Options, X-Content-Type-Options
- **Content Security Policy**: Prevents XSS attacks with strict CSP rules
- **Click-jacking Protection**: X-Frame-Options and frame-ancestors directives

#### **Layer 2: CSRF Middleware**
- **Advanced CSRF Protection**: Token rotation on sensitive operations
- **Secure Token Management**: HttpOnly, Secure, SameSite cookie attributes
- **Attack Detection**: Real-time CSRF attack pattern recognition
- **Token Lifecycle Management**: Automatic refresh and validation

#### **Layer 3: Security Gateway Middleware**
- **Central Threat Detection**: Real-time attack pattern recognition
- **Automated Response**: Dynamic blocking of malicious requests
- **Attack Pattern Analysis**: SQL injection, XSS, command injection detection
- **Security Event Logging**: Comprehensive audit trail for compliance

#### **Layer 4: Rate Limiting & Validation**
- **Intelligent Rate Limiting**: Multi-algorithm support with complexity scoring
- **Request Validation**: Input sanitization and content filtering
- **Performance Optimization**: In-memory caching with TTL-based cleanup
- **Dynamic Configuration**: Hot-reload security rules via YAML

### **Advanced Cost Management System**

#### **Real-time OpenAI Cost Tracking**
- **Per-Model Cost Monitoring**: Accurate tracking with validation
- **Store-Tier Budget Controls**:
  - **Free Tier**: $0-$10 monthly budget
  - **Basic Tier**: $10-$50 monthly budget
  - **Premium Tier**: $50-$200 monthly budget
  - **Enterprise Tier**: $200+ monthly budget

#### **Emergency Protection Mechanisms**
- **95% Budget Threshold**: Automated warnings and notifications
- **Emergency Stop**: Automatic service suspension at budget limits
- **Cost Optimization**: Real-time recommendations and usage analytics
- **Budget Enforcement**: Hard limits with grace period handling

### **Production-Ready Infrastructure**

#### **Database Optimization**
- **35+ Indexes**: Optimized MongoDB queries for performance
- **TTL-based Cleanup**: Automatic data lifecycle management
- **Thread-Safe Operations**: High-concurrency support
- **Connection Pooling**: Efficient database connection management

#### **Security Configuration**
```yaml
# cost_limits.yaml
store_tiers:
  free: { monthly_limit: 10.00, warning_threshold: 0.95 }
  basic: { monthly_limit: 50.00, warning_threshold: 0.95 }
  premium: { monthly_limit: 200.00, warning_threshold: 0.95 }
  enterprise: { monthly_limit: 1000.00, warning_threshold: 0.95 }

# rate_limits.yaml
endpoints:
  "/api/chat/*": { requests: 10, window: 60, algorithm: "sliding_window" }
  "/api/meta/*": { requests: 100, window: 60, algorithm: "token_bucket" }

# validation_rules.yaml
request_validation:
  max_payload_size: 10MB
  allowed_content_types: ["application/json", "multipart/form-data"]
  sanitization_rules: ["strip_html", "escape_sql", "validate_json"]
```

## 🧪 Comprehensive Testing Infrastructure (2025)

### **5-Category Testing Framework**

#### **1. Unit Tests (8 Components)**
- **Middleware Testing**: Security headers, CSRF, rate limiting, validation
- **Service Testing**: Authentication, cost tracking, security services
- **Model Testing**: Data validation, security models
- **Utility Testing**: Helper functions, security utilities

#### **2. Integration Tests (4 Workflows)**
- **Security Workflow**: End-to-end security middleware integration
- **Cost Integration**: Budget enforcement and tracking validation
- **Database Integration**: MongoDB connection and data flow testing
- **Middleware Flow**: Request lifecycle through all security layers

#### **3. Load Tests (4 Scenarios)**
- **Concurrent Requests**: High-traffic simulation and stress testing
- **Rate Limiting Load**: Validation under peak load conditions
- **Performance Benchmarks**: Response time and throughput measurement
- **Memory Usage**: Resource consumption under load

#### **4. Security Tests (4 Attack Types)**
- **Injection Protection**: SQL injection, XSS, command injection testing
- **Authentication Security**: JWT validation, session management
- **Attack Detection**: Malicious payload recognition and response
- **Vulnerability Scanning**: Automated security assessment

#### **5. Cost Tracking Tests (3 Validation Areas)**
- **Budget Enforcement**: Hard limit validation and emergency stops
- **Cost Optimization**: Usage tracking and recommendation accuracy
- **Billing Accuracy**: OpenAI cost calculation and validation

### **Advanced CI/CD Pipeline**

#### **GitHub Actions Workflow**
```yaml
# .github/workflows/test.yml
- Multi-Python Version Testing: 3.9, 3.10, 3.11
- Service Integration: MongoDB and Redis containers
- Coverage Reporting: Codecov integration with >90% targets
- Performance Monitoring: Automated regression detection
- Security Scanning: Bandit vulnerability detection
```

#### **Test Execution Examples**
```bash
# Real-world testing scenarios
pytest tests/security/test_attack_detection.py  # ✅ Blocks 50+ attack patterns
pytest tests/cost/test_budget_enforcement.py    # ✅ Prevents cost overruns
pytest tests/load/test_concurrent_requests.py   # ✅ Handles 1000+ concurrent users
pytest tests/unit/middleware/test_rate_limiter.py # ✅ Rate limiting accuracy
```

#### **Production Safety Benefits**
- **100% Test Success Rate**: All 20 security gateway tests passing
- **Change Protection**: Immediate feedback on code modifications
- **Production Safety**: Prevents broken code deployment
- **Performance Tracking**: Continuous performance monitoring
- **Security Validation**: Automated vulnerability scanning

---
## Project Overview

D-Unit is an AI-driven analytics platform for e-commerce, enhancing Shopify-style stores like "La Nube." It processes store data, enriches it with market intelligence, and delivers actionable insights via chat and dashboards.

### Core Purpose
- Provide deep business performance insights.
- Offer AI-powered sales optimization recommendations.
- Enable competitive analysis (e.g., Amazon, MercadoLibre).
- Identify top products and growth opportunities.
- Integrate social media analytics with e-commerce data.

### Key Features
- Competitive pricing analysis
- Product performance tracking
- Customer segmentation
- AI-powered recommendations (RAG enabled)
- Automated data synchronization
- Meta (Facebook/Instagram) integration with AI insights
- Cross-platform correlation analysis
- Dynamic time-based data filtering
- Interactive demographic visualization
- Optimized database queries (pagination, projection)
- Resilient data fetching (exponential backoff)
- **Chat Capabilities:** Multimodal input (text/image), multiple AI modes (Default, Think, DeepSearch, DeeperSearch with Brave/Google).
- **Admin Tools:** Dedicated admin portal, store selection, and secure password reset.
- **SEO Recommendations:** AI-generated SEO insights and actions.
- **Enterprise Security:** 4-layer security gateway with OWASP Top 10 protection.
- **Comprehensive Testing:** 100% test coverage with automated CI/CD pipeline.

### Analytical Foundation

D-Unit leverages AI for deep analytical insights, combining:
- **Quantitative & Qualitative Analysis:** Integrates numerical data (sales, engagement) and qualitative data (feedback, sentiment).
- **Statistical Modeling:** Uses regression and other methods to identify key business drivers.
- **Contextual Insights:** Blends automated analysis with store owner expertise for actionable strategies.

### Meta Dashboard Features
- **Overview:** Page impressions, engagement, follower growth, and page views with time-filtered visualization.
- **Posts Analytics:** Engagement and post type distribution, top posts, and performance trends.
- **Audience Demographics:** Age, gender, geography, language, and interests.
- **Ad Metrics:** Campaign performance, ROI, daily metrics, and objective comparison.
- **Campaigns Comparison:** Cross-campaign analysis, correlation, and impact assessment.

### Visual Design
- **Color Scheme**:
  - Primary: Light Blue (#00A3FF)
  - Secondary: Lighter Blue (#82b8ff)
  - Tertiary: Very Light Blue (#cce5ff)
  - Consistent across all visualizations
- **Chart Components**:
  - Interactive tooltips
  - Responsive layouts
  - Dynamic data updates
  - Smooth transitions

### Time Range Filtering
- **Preset Options**:
  - Last 7 days ('7d')
  - Last 30 days ('30d')
  - Last 90 days ('90d')
  - Last 180 days ('180d')
  - Last year ('1y')
  - Custom date range
- **Features**:
  - Real-time data updates
  - Consistent filtering across all sections
  - Preserved filter state between tabs
  - Custom date range selection
  - Proper error handling
  - Loading state indicators

### Dashboard Features
- **Time Range Filtering:** Dynamic filtering across all sections (daily, weekly, monthly, yearly).
- **Customer Demographics:** Interactive pie charts, country/city breakdowns, and time-filtered trends.
- **Customer Insights:** Behavior analysis, active customer tracking, geographic distribution, cart abandonment, and conversion optimization.
- **Product Analysis:** Cached product details, historical sales, revenue, price, and stock; performance charts and paginated browsing.
- **Competitor Analysis:** Market position tracking, price trends, and competitive landscape analysis.
- **Shipping Analytics:** Method usage, performance metrics, and time-based trends.

## Meta Integration Features

### Authentication and Data Access
- Facebook Login integration for secure authentication
- Business account selection and page access
- Secure token management and permission handling
- Meta Login Email Selection: Users can add alternate emails and select which one to use for Meta authentication via the Settings page.

### Data Extraction
- Page insights and metrics (impressions, reach, engagement)
- Post content and engagement data
- Audience demographics and interests
- Ad performance metrics (when available)

### AI-Powered Insights (RAG)
- Content analysis with engagement patterns
- Sentiment analysis of comments
- Correlation between social media activity and sales
- Audience segmentation and targeting recommendations

### Chat Interface
- Meta-aware context building for relevant responses
- Specialized handling of Meta-related queries
- Cross-platform insights connecting social media with sales
- Natural language responses to complex analytics questions

### Meta Integration Data Flow

1. **Authentication**: Store owner logs in with Meta (Facebook Login), system receives access token and permissions, token is stored securely.
2. **Data Collection**: System fetches business accounts, pages, posts, comments, engagement metrics, audience demographics, and ad campaign data (if available).
3. **Data Storage**: Data is stored in dedicated MongoDB collections, organized by store_id for isolation and indexed for efficient querying.
4. **Data Processing**: RAG service analyzes collected data, generates insights using AI models, and creates actionable recommendations.
5. **Data Presentation**: Insights are displayed in the Meta dashboard and chat interface, with metrics visualized in charts and graphs.
6. **Data Refresh**: System periodically refreshes data from Meta API, updates metrics, and maintains historical data for trend analysis.

### MongoDB Collections Usage

- **meta_pages**: Connected Facebook/Instagram pages
- **meta_posts**: Social media posts and metadata
- **meta_post_metrics**: Engagement metrics for posts
- **meta_comments**: Comments on posts
- **meta_followers**: Follower count and growth
- **meta_demographics**: Audience demographic information
- **meta_ad_campaigns**: Ad campaign data
- **meta_ad_metrics**: Ad performance metrics
- **meta_insights**: AI-generated insights
- **global_analysis.meta_integration**: Connection status and metrics for each store

## Database Schema

D-Unit uses a two-tier MongoDB architecture:
- **D-Unit (Raw Data):** Original data from La Nube
- **D-Unit-AnalysisGPT:** AI-generated insights, embeddings, chat, and Meta analytics

### Key Collections
- **meta_pages:** Connected Facebook/Instagram pages
- **meta_posts:** Social media posts
- **meta_post_metrics:** Engagement metrics for posts
- **meta_comments:** Post comments
- **meta_followers:** Follower statistics
- **meta_demographics:** Audience demographics
- **meta_ad_campaigns:** Ad campaign data
- **meta_ad_metrics:** Ad performance metrics
- **meta_insights:** AI-generated insights
- **global_analysis.meta_integration:** Meta connection status and metrics for each store

## 📱 Social Media Analytics Dashboard (January 2025)

### Overview
A comprehensive social media analytics platform (`/socialmedia`) that transforms how D-Unit users understand and optimize their Facebook and Instagram presence. This enterprise-grade feature provides real-time insights, cross-platform correlation, and AI-powered recommendations for social media optimization.

### **🎯 Business Impact**
- **ROI Tracking**: Direct correlation between social media spend and e-commerce sales
- **Audience Optimization**: Detailed demographics for targeted marketing campaigns  
- **Content Performance**: AI-powered insights on what content drives engagement and conversions
- **Competitive Intelligence**: Social media performance benchmarking
- **Budget Optimization**: Real-time ad spend tracking with performance recommendations

### Key Components

#### Frontend Implementation
- **Main Component**: `SocialMedia.tsx` - A tabbed interface with three main sections:
  - Facebook Analytics
  - Instagram Analytics  
  - Marketing & Business Tools
- **Feature Toggles**: Granular control to enable/disable specific metrics
- **Time Range Filtering**: Integrated with existing `TimeRangeFilter` component
- **Responsive Design**: Mobile-friendly with Material-UI components
- **Real-time Data Refresh**: Manual refresh capability with loading states

#### Backend Architecture
- **Routes**: `/api/socialmedia/*` endpoints organized by platform
- **Service Layer**: `SocialMediaService` class handling all Meta Graph API interactions
- **Security**: Integrated with 4-layer security gateway
- **Database**: Uses MongoDB `D-Unit-AnalysisGPT` for caching and data storage

### Facebook Analytics Features

#### User & Page Insights
- **User Profile**: Display user information, timezone, friend count
- **Page Metrics**: 
  - Fan count with growth tracking
  - Page views and impressions
  - Reach and engagement rates
  - All metrics include percentage change indicators

#### Content Performance
- **Posts Analysis**:
  - Recent posts with engagement metrics
  - Like, comment, and share counts
  - Time-based performance tracking
- **Events Management**: Upcoming events with attendance tracking
- **Messenger Statistics**: 
  - Total conversations
  - Response rate and average response time

### Instagram Analytics Features

#### Profile & Account Insights
- **Profile Overview**: Username, bio, follower/following counts, post count
- **Account Metrics**:
  - Profile views and website clicks
  - Reach and impressions
  - All with growth tracking

#### Content & Audience Analytics
- **Media Performance**:
  - Posts, Reels, and Carousel analytics
  - Engagement metrics (likes, comments, reach, saves)
  - Media type indicators
- **Stories Insights**: Impressions, reach, exits, replies
- **Audience Demographics**:
  - Geographic distribution (top countries/cities)
  - Age ranges with percentage breakdowns
  - Gender distribution

### Marketing & Business Tools

#### Advertising Analytics
- **Cross-Platform Ad Spend**: Facebook vs Instagram breakdown
- **Performance Metrics**:
  - Total impressions and clicks
  - Average CTR with change tracking
  - Campaign-level performance
- **Active Campaigns**: Status, objectives, spend, and results

#### E-commerce Integration
- **Product Catalog Management**:
  - Total products with stock status
  - Facebook Shops enablement status
  - Instagram Shopping integration
- **Meta Pixel Tracking**:
  - Page views and add-to-cart events
  - Purchase tracking and conversion rates

### Technical Implementation Details

#### API Integration
- **Facebook Graph API Endpoints**:
  - `/me` - User information
  - `/{page-id}/insights` - Page analytics
  - `/{page-id}/posts` - Content performance
  - `/{page-id}/events` - Event management
- **Instagram Graph API**:
  - `/{ig-user-id}` - Profile data
  - `/{ig-user-id}/insights` - Account metrics
  - `/{ig-user-id}/media` - Content analytics
  - Audience demographics via lifetime metrics

#### Data Flow
1. **Authentication**: Uses existing Meta auth from `useMetaAuth` hook
2. **Data Fetching**: Parallel API calls for optimal performance
3. **State Management**: React hooks for local state
4. **Error Handling**: Comprehensive error states with user feedback

#### Performance Optimizations
- **Conditional Fetching**: Only fetch enabled features
- **Parallel Requests**: `Promise.all()` for concurrent API calls
- **Data Caching**: MongoDB caching layer for frequently accessed data
- **Lazy Loading**: Components render only when visible

### Security & Permissions

#### Meta Permissions Required
- `pages_read_engagement` - Page engagement metrics
- `pages_read_user_content` - Post and content data
- `instagram_basic` - Basic Instagram access
- `instagram_manage_insights` - Instagram analytics
- `ads_read` - Advertising metrics (optional)
- `ads_management` - Campaign management (optional)

#### Security Features
- **Token Management**: Secure storage in MongoDB
- **Rate Limiting**: Integrated with security gateway
- **Input Validation**: All user inputs sanitized
- **HTTPS Only**: Enforced for all Meta API calls

### User Experience Enhancements

#### Navigation Integration
- Added "Social Media" link to main navigation
- Support for both icon-only (mobile) and full navigation
- Bilingual support (English/Spanish)

#### Visual Design
- **Metric Cards**: Consistent design with value, change percentage, and sub-metrics
- **Color Coding**: 
  - Green for positive growth
  - Red for negative trends
  - Platform-specific icon usage
- **Loading States**: Skeleton loaders during data fetch
- **Empty States**: Informative messages when no data available

### Development Considerations

#### TypeScript Implementation
- **Strict Typing**: Full TypeScript support with interfaces
- **Type Safety**: Proper typing for all API responses
- **Error Boundaries**: Type-safe error handling

#### Code Organization
- **Modular Structure**: Separate components for each dashboard
- **Reusable Components**: `MetricCard` shared across platforms
- **Service Layer**: Centralized API logic in `SocialMediaService`

#### Future Extensibility
- **Additional Platforms**: Architecture supports adding Twitter, LinkedIn, etc.
- **Custom Metrics**: Easy to add new metric types
- **Export Functionality**: Foundation for data export features
- **Webhook Support**: Ready for real-time updates

### API Endpoints Created

#### Facebook Endpoints
- `GET /api/socialmedia/facebook/user`
- `GET /api/socialmedia/facebook/page`
- `GET /api/socialmedia/facebook/page/insights`
- `GET /api/socialmedia/facebook/posts`
- `GET /api/socialmedia/facebook/events`
- `GET /api/socialmedia/facebook/messenger/stats`

#### Instagram Endpoints
- `GET /api/socialmedia/instagram/profile`
- `GET /api/socialmedia/instagram/insights`
- `GET /api/socialmedia/instagram/media`
- `GET /api/socialmedia/instagram/stories`

#### Marketing Endpoints
- `GET /api/socialmedia/marketing/ads`
- `GET /api/socialmedia/marketing/campaigns`
- `GET /api/socialmedia/marketing/audiences`
- `GET /api/socialmedia/marketing/catalog`
- `GET /api/socialmedia/marketing/pixel`

#### Utility Endpoints
- `GET /api/socialmedia/metrics/{platform}`
- `POST /api/socialmedia/refresh`

### Integration with Existing Features

#### Seamless Authentication
- Uses existing Meta authentication flow
- No additional login required
- Respects existing permissions

#### Consistent UI/UX
- Follows D-Unit design patterns
- Uses existing component library
- Maintains dark mode support

#### Data Correlation
- Ready for integration with sales data
- Foundation for cross-platform insights
- Supports existing chat context

## Project Context

### Business Background
D-Unit builds on La Nube, which allows store owners to manage online stores, list products, process orders, and handle customer relationships.

### Problem Statement
Store owners need better tools for:
- Understanding business performance
- Assessing market positioning
- Optimizing products and pricing
- Improving sales strategies
- Analyzing competitors
- Integrating social media with e-commerce strategy

### Solution
D-Unit adds an AI-powered analytics layer that:
1. Processes La Nube data
2. Enriches it with external market data
3. Generates insights and recommendations
4. Provides an interactive chat interface
5. Integrates social media analytics with e-commerce data

## Common Development Commands

### Frontend (from `/frontend` directory)
```bash
npm install          # Install dependencies
npm run dev          # Start development server (HTTPS on https://localhost:5173)
npm run build        # Build for production
npm run lint         # Run ESLint
npm run preview      # Preview production build
```

### Backend (from `/backend` directory)
```bash
python -m venv venv                    # Create virtual environment
source venv/bin/activate               # Activate venv (Linux/Mac)
venv\Scripts\activate                  # Activate venv (Windows)
pip install -r requirements.txt        # Install dependencies
python main.py                         # Run development server (HTTPS on https://127.0.0.1:8000)

# Code quality
flake8                                # Run Python linter
```

### Testing Infrastructure (from `/backend` directory)
```bash
# Install test dependencies
pip install -r requirements-test.txt   # Install test dependencies

# Run tests by category (using pytest markers)
pytest -m unit                        # Unit tests only (8 components)
pytest -m integration                 # Integration tests only (4 workflows)
pytest -m load                       # Load tests only (4 scenarios)
pytest -m security                   # Security tests only (4 attack types)
pytest -m cost                      # Cost tracking tests only (3 validation areas)
pytest -m "not slow"                # Skip slow tests
pytest -m "unit and not slow"       # Fast unit tests only

# Run specific test suites
pytest tests/unit/middleware/         # Test all middleware components
pytest tests/security/               # Test security infrastructure
pytest tests/cost/                   # Test cost management system
pytest tests/integration/            # Test component integration

# Coverage and reporting
pytest --cov=.                      # Run tests with coverage
pytest --cov=. --cov-report=html    # Generate HTML coverage report
pytest --tb=short                   # Short traceback format
pytest -v                           # Verbose output

# Performance and load testing
pytest tests/load/ -v               # Performance benchmarks
pytest tests/load/test_rate_limiting_load.py # Rate limiting load tests
```

### Database Scripts (from `/backend` directory)
```bash
# Data pipeline scripts - should be run in specific order:
python scripts/data/update_store_users.py              # 1. Sync user data from MySQL to MongoDB
python scripts/data/update_active_stores.py            # 2. Update active stores cache
python scripts/data/update_product_details.py          # 3. Create product cache
python scripts/data/update_product_variations.py       # 4. Add variations to products
python scripts/data/update_product_categories.py       # 5. Add categories to products
python scripts/data/update_meta_sales_correlation.py   # 6. Correlate Meta ads with sales
python scripts/data/update_customers_relationships.py  # 7. Build customer analytics
python scripts/data/update_store_activity_metrics.py   # 8. Track store activity metrics
python scripts/data/market_analyzer.py                 # 9. Generate AI business analysis
python scripts/data/update_competitor_analysis.py      # 10. Add competitor insights
python scripts/data/embeddings_generator_analysis.py   # 11. Generate vector embeddings

# Administrative Scripts
python scripts/admin/diagnose_collections.py           # Diagnose database collections
python scripts/admin/reset_admin_password.py           # Reset admin password
python scripts/admin/setup_meta_permissions.py         # Setup Meta API permissions

# Database Management Scripts
python scripts/database/meta_db_init.py                # Initialize Meta database collections
python scripts/database/migrate_meta_format.py         # Migrate Meta data format
python scripts/database/sync_shipping_faqs_to_mongo.py # Sync shipping FAQs
python scripts/database/translate_mongo_collections_async.py # Translate collections

# Development Utilities
python scripts/dev/CSRF_COOKIE_FIX.py                  # CSRF cookie debugging
```

### Deployment Commands
```bash
# Frontend deployment (S3 + CloudFront)
npm run build                        # Build for production
aws s3 sync dist/ s3://your-bucket  # Deploy to S3

# Backend deployment (Elastic Beanstalk / Kubernetes)
eb deploy                           # Deploy to Elastic Beanstalk
kubectl apply -f backend/deployment/ # Deploy to Kubernetes
```

## System Architecture

D-Unit uses a modular, cloud-based architecture:
- **Backend:** FastAPI (Python 3.11.9), MongoDB Atlas, OpenAI (gpt-4o-mini)
- **Frontend:** React 18+, TypeScript, Vite
- **Deployment:** AWS (Elastic Beanstalk, S3, CloudFront), Kubernetes
- **Data Analysis:** NumPy, Pandas, SciPy
- **Social Media:** Meta Graph API (v22.0)
- **Security:** 4-Layer Security Gateway with OWASP Top 10 protection
- **Testing:** 5-Category comprehensive test suite with CI/CD pipeline

### Data Flow
1. Data from La Nube SQL is converted to JSON and imported into MongoDB.
2. Embeddings and AI analysis are generated and stored in a separate database.
3. The backend exposes RESTful APIs for analytics, chat, and Meta integration.
4. The frontend consumes these APIs for dashboards, chat, and admin tools.

## Project Structure

D-Unit/
  ├── .contextfiles
  ├── .cursorignore
  ├── .cursorindexingignore
  ├── .env.template
  ├── .gitignore
  ├── .python-version
  ├── .repomixignore
  ├── codebase.xml
  ├── D-Unit.code-workspace
  ├── pyrightconfig.json
  ├── README.md
  ├── repomix-instruction.md
  ├── repomix.config.json
  ├── CLAUDE.md           # Project instructions for Claude Code
  ├── docs/               # Project-level documentation
  │   ├── Main-Readme.md  # This comprehensive project overview
  │   ├── general-fixes.md # General fixes and improvements
  │   └── legalmvp.md     # Legal and MVP documentation
  ├── backend/
  │   ├── __init__.py
  │   ├── application.py  # WSGI entry point for deployment
  │   ├── main.py         # FastAPI application setup and router registration
  │   ├── pytest.ini      # Comprehensive test configuration with markers
  │   ├── requirements.txt # Python dependencies
  │   ├── requirements-test.txt # Test-specific dependencies
  │   ├── certs/          # SSL certificates
  │   ├── config/         # Application configuration files
  │   │   ├── __init__.py
  │   │   ├── config_manager.py  # Central configuration management
  │   │   ├── database.py        # MongoDB connection setup
  │   │   ├── settings.py        # Pydantic settings configuration
  │   │   ├── logging_config.py  # Standardized logging setup
  │   │   ├── meta_permissions.py # Meta API permissions config
  │   │   ├── cost_limits.yaml   # Advanced cost management config
  │   │   ├── rate_limits.yaml   # Intelligent rate limiting config
  │   │   └── validation_rules.yaml # Request validation rules
  │   ├── cron/           # Cron job scripts
  │   │   └── update_meta_contexts.sh
  │   ├── deployment/     # Kubernetes and container deployment
  │   │   ├── Dockerfile
  │   │   ├── Procfile
  │   │   └── d-unit-backend-service.yaml # Kubernetes service config
  │   ├── docs/           # Backend-specific documentation
  │   │   ├── Backend-Readme.md
  │   │   ├── Scripts-Readme.md
  │   │   ├── mongodb-collections.md
  │   │   └── sql-tables.md
  │   ├── logs/           # Application logs directory
  │   ├── middleware/     # 4-Layer Security Gateway
  │   │   ├── __init__.py
  │   │   ├── cors.py           # CORS configuration
  │   │   ├── security_headers.py # OWASP Top 10 protection
  │   │   ├── csrf.py           # Advanced CSRF protection
  │   │   ├── security_gateway.py # Central threat detection
  │   │   ├── rate_limiter.py   # Intelligent rate limiting
  │   │   ├── validation.py     # Request validation
  │   │   └── cost_control.py   # Real-time cost tracking
  │   ├── models/         # Pydantic data models
  │   │   ├── __init__.py
  │   │   ├── chat.py      # Chat interaction models
  │   │   ├── csrf_monitoring.py # CSRF monitoring models
  │   │   ├── feedback.py  # User feedback models
  │   │   ├── insights.py  # Business insights models
  │   │   ├── meta.py      # Meta API data models
  │   │   ├── security.py  # Security event models
  │   │   └── user.py      # User authentication models
  │   ├── routes/         # API route definitions
  │   │   ├── __init__.py
  │   │   ├── admin.py           # Administrative endpoints
  │   │   ├── auth.py            # Authentication endpoints
  │   │   ├── chat.py            # AI chat endpoints
  │   │   ├── insights.py        # Business insights endpoints
  │   │   ├── meta.py            # Meta integration endpoints
  │   │   ├── meta_direct.py     # Direct Meta API endpoints
  │   │   ├── meta_permissions.py # Meta permissions endpoints
  │   │   ├── security.py        # Security monitoring endpoints
  │   │   ├── social_media.py    # Social media analytics endpoints
  │   │   └── store.py           # Store data endpoints
  │   ├── scripts/        # Organized utility and maintenance scripts
  │   │   ├── __init__.py
  │   │   ├── admin/      # Administrative tools
  │   │   │   ├── __init__.py
  │   │   │   ├── diagnose_collections.py
  │   │   │   ├── reset_admin_password.py
  │   │   │   └── setup_meta_permissions.py
  │   │   ├── data/       # Data pipeline scripts (run in sequence)
  │   │   │   ├── __init__.py
  │   │   │   ├── embeddings_generator_analysis.py
  │   │   │   ├── market_analyzer.py
  │   │   │   ├── update_active_stores.py
  │   │   │   ├── update_competitor_analysis.py
  │   │   │   ├── update_customers_relationships.py
  │   │   │   ├── update_meta_context_from_collections.py
  │   │   │   ├── update_meta_contexts_scheduled.py
  │   │   │   ├── update_meta_sales_correlation.py
  │   │   │   ├── update_product_categories.py
  │   │   │   ├── update_product_details.py
  │   │   │   ├── update_product_variations.py
  │   │   │   ├── update_store_activity_metrics.py
  │   │   │   └── update_store_users.py
  │   │   ├── database/   # Database management scripts
  │   │   │   ├── __init__.py
  │   │   │   ├── meta_db_init.py
  │   │   │   ├── migrate_meta_format.py
  │   │   │   ├── sync_shipping_faqs_to_mongo.py
  │   │   │   └── translate_mongo_collections_async.py
  │   │   ├── dev/        # Development utilities
  │   │   │   ├── __init__.py
  │   │   │   └── CSRF_COOKIE_FIX.py
  │   │   ├── tests/      # Test scripts
  │   │   │   ├── __init__.py
  │   │   │   ├── test_brave_search.py
  │   │   │   ├── test_brave_simple.py
  │   │   │   ├── test_business_mapper.py
  │   │   │   ├── test_competitor_analysis.py
  │   │   │   ├── test_competitor_extraction.py
  │   │   │   ├── test_competitor_query.py
  │   │   │   ├── test_csrf_production.py
  │   │   │   └── test_logging.py
  │   │   └── test_monitoring.py
  │   ├── services/       # Business logic services
  │   │   ├── __init__.py
  │   │   ├── auth.py              # Authentication service
  │   │   ├── chat.py              # AI chat processing
  │   │   ├── csrf_attack_detector.py # CSRF attack detection
  │   │   ├── csrf_service.py      # CSRF token lifecycle
  │   │   ├── email.py             # Email service (2FA, notifications)
  │   │   ├── insights.py          # Business insights generation
  │   │   ├── meta.py              # Meta data processing
  │   │   ├── meta_permissions.py  # Meta permissions management
  │   │   ├── meta_utils.py        # Meta API utilities
  │   │   ├── py.typed             # Type hint marker
  │   │   ├── scheduled_tasks.py   # Background task processing
  │   │   ├── security_service.py  # Security event management
  │   │   ├── seo_service.py       # SEO analysis and recommendations
  │   │   ├── social_media.py      # Social media analytics service
  │   │   └── store.py             # Store data processing
  │   ├── tests/          # Comprehensive 5-Category Test Suite
  │   │   ├── __init__.py
  │   │   ├── conftest.py         # Test configuration and fixtures
  │   │   ├── meta_permissions_test.py
  │   │   ├── cost/               # Cost tracking tests (3 validation areas)
  │   │   │   ├── __init__.py
  │   │   │   ├── test_budget_enforcement.py
  │   │   │   ├── test_cost_optimization.py
  │   │   │   └── test_cost_tracking.py
  │   │   ├── helpers/            # Test utilities and helpers
  │   │   │   ├── __init__.py
  │   │   │   ├── attack_payloads.py
  │   │   │   ├── mock_database.py
  │   │   │   ├── performance_utils.py
  │   │   │   └── test_client.py
  │   │   ├── integration/        # Integration tests (4 workflows)
  │   │   │   ├── __init__.py
  │   │   │   ├── test_cost_integration.py
  │   │   │   ├── test_database_integration.py
  │   │   │   ├── test_middleware_flow.py
  │   │   │   └── test_security_workflow.py
  │   │   ├── load/               # Load tests (4 scenarios)
  │   │   │   ├── __init__.py
  │   │   │   ├── test_concurrent_requests.py
  │   │   │   ├── test_performance_benchmarks.py
  │   │   │   └── test_rate_limiting_load.py
  │   │   ├── security/           # Security tests (4 attack types)
  │   │   │   ├── __init__.py
  │   │   │   ├── test_attack_detection.py
  │   │   │   ├── test_auth_security.py
  │   │   │   ├── test_injection_protection.py
  │   │   │   └── test_vulnerability_scanning.py
  │   │   ├── unit/               # Unit tests (8 components)
  │   │   │   ├── __init__.py
  │   │   │   ├── middleware/     # Middleware component tests
  │   │   │   │   ├── __init__.py
  │   │   │   │   ├── test_basic.py
  │   │   │   │   ├── test_basic_marked.py
  │   │   │   │   ├── test_cost_control.py
  │   │   │   │   ├── test_rate_limiter.py
  │   │   │   │   ├── test_rate_limiter_full.py
  │   │   │   │   ├── test_security_gateway.py
  │   │   │   │   ├── test_security_headers.py
  │   │   │   │   └── test_validation.py
  │   │   │   ├── models/         # Model component tests
  │   │   │   │   ├── __init__.py
  │   │   │   │   └── test_security_models.py
  │   │   │   ├── services/       # Service component tests
  │   │   │   │   ├── __init__.py
  │   │   │   │   └── test_security_service.py
  │   │   │   └── utils/          # Utility component tests
  │   │   │       ├── __init__.py
  │   │   │       └── test_security_utils.py
  │   │   ├── test-results/       # Test execution results
  │   │   └── validate-cloudfront-config.sh
  │   ├── utils/          # Utility functions
  │   │   ├── __init__.py
  │   │   ├── logger.py           # Standardized logging utilities
  │   │   ├── security_utils.py   # Security helper functions
  │   │   ├── serialization.py    # MongoDB serialization utilities
  │   │   └── translation_utils.py # Translation and localization
  │   └── venv/           # Python virtual environment
  ├── frontend/
  │   ├── index.html      # Main HTML entry point for the SPA
  │   ├── package.json    # NPM package manifest (dependencies, scripts)
  │   ├── package-lock.json # NPM dependency lock file
  │   ├── build/          # Production build output
  │   ├── certs/          # SSL certificates for HTTPS development
  │   ├── config/         # Frontend configuration files
  │   │   ├── distribution-config.json # CloudFront distribution config
  │   │   ├── eslint.config.js    # ESLint configuration
  │   │   ├── mime-types-config.json # MIME types configuration
  │   │   ├── postcss.config.js   # PostCSS configuration
  │   │   ├── tailwind.config.js  # Tailwind CSS configuration
  │   │   ├── tsconfig.app.json   # TypeScript config for application
  │   │   ├── tsconfig.json       # Base TypeScript configuration
  │   │   ├── tsconfig.node.json  # TypeScript config for Node.js environment
  │   │   └── vite.config.ts      # Vite build tool configuration
  │   ├── dist/           # Build output directory (generated)
  │   ├── docs/           # Frontend-specific documentation
  │   │   └── Frontend-Readme.md
  │   ├── node_modules/   # NPM dependencies (generated)
  │   ├── public/         # Static assets served directly
  │   │   ├── dunit.mp4
  │   │   ├── faviconV2.png
  │   │   ├── index.html
  │   │   ├── logo.png
  │   │   ├── logonube.png
  │   │   ├── meta.png
  │   │   ├── vite.svg
  │   │   └── locales/    # Internationalization files
  │   │       ├── en/
  │   │       │   └── translation.json # English translations
  │   │       └── es/
  │   │           └── translation.json # Spanish translations
  │   └── src/            # Main source code for the frontend application
  │       ├── App.tsx
  │       ├── App.css
  │       ├── main.tsx
  │       ├── Router.tsx
  │       ├── i18n.ts     # Internationalization setup
  │       ├── theme.ts    # Material-UI theme configuration
  │       ├── index.css
  │       ├── types.d.ts
  │       ├── vite-env.d.ts
  │       ├── assets/     # Static assets
  │       ├── components/ # React components
  │       │   ├── Dashboard.tsx
  │       │   ├── ErrorBoundary.tsx
  │       │   ├── ScrollToTop.tsx
  │       │   ├── Feedback/      # User feedback components
  │       │   ├── auth/          # Authentication components
  │       │   ├── charts/        # Data visualization components
  │       │   ├── common/        # Shared UI components
  │       │   │   ├── CSRFErrorBoundary.tsx
  │       │   │   ├── CookieBanner.tsx      # GDPR/CCPA compliant banner
  │       │   │   ├── CookieSettings.tsx    # Cookie management interface
  │       │   │   ├── GlobalAppSpinner.tsx
  │       │   │   ├── LanguageSelector.tsx
  │       │   │   ├── LoadingSpinner.tsx
  │       │   │   ├── Navigation.tsx
  │       │   │   └── TimeRangeFilter.tsx
  │       │   ├── dashboard/     # Analytics dashboard components
  │       │   ├── legal/         # Legal compliance components
  │       │   │   └── CookiePolicyPage.tsx  # Comprehensive cookie policy
  │       │   ├── meta/          # Meta (Facebook/Instagram) components
  │       │   │   ├── MetaDashboard.tsx
  │       │   │   ├── MetaPermissionsPanel.tsx
  │       │   │   ├── InstagramPermissionAlert.tsx
  │       │   │   └── RestrictedWidget.tsx
  │       │   └── settings/      # User settings components
  │       │       ├── CookieManagementCard.tsx
  │       │       ├── EditProfile.tsx
  │       │       ├── Settings.tsx
  │       │       └── TwoFactorAuth.tsx
  │       ├── config/     # Configuration files
  │       │   ├── api.ts         # API endpoint configuration
  │       │   ├── axios.ts       # Axios HTTP client setup
  │       │   ├── cookies.ts     # Cookie management configuration
  │       │   └── environment.ts # Environment configuration
  │       ├── contexts/   # React context providers
  │       │   ├── AuthContext.tsx
  │       │   ├── FeedbackContext.tsx
  │       │   ├── MetaPermissionsContext.tsx
  │       │   ├── ThemeContext.tsx
  │       │   └── feedback.context.ts
  │       ├── hooks/      # Custom React hooks
  │       │   ├── useCookieConsent.ts
  │       │   ├── useCsrfErrorRecovery.ts
  │       │   ├── useMetaAuth.ts
  │       │   └── useSecureForm.ts
  │       ├── pages/      # Page-level components
  │       │   ├── AdminPortal.tsx
  │       │   ├── BackendLogin.tsx
  │       │   ├── CookiePolicy.tsx    # Cookie policy page wrapper
  │       │   ├── SocialMedia.tsx     # Social media analytics dashboard
  │       │   └── StoreView.tsx
  │       ├── services/   # Service layer for API communication
  │       │   ├── apiService.ts       # Core API service
  │       │   ├── auth.ts            # Authentication utilities
  │       │   ├── authChecker.ts     # Authentication validation
  │       │   ├── authService.ts     # Authentication service
  │       │   ├── config.ts          # Service configuration
  │       │   ├── cookieService.ts   # Cookie management service
  │       │   ├── csrfService.ts     # CSRF protection service
  │       │   ├── dataService.ts     # Data fetching service
  │       │   ├── formService.ts     # Form handling service
  │       │   ├── httpClient.ts      # HTTP client with interceptors
  │       │   ├── init.ts            # Service initialization
  │       │   ├── metaApiProxy.ts    # Meta API proxy service
  │       │   ├── metaStoreService.ts # Meta store data service
  │       │   ├── metaTimeRanges.ts  # Meta time range utilities
  │       │   ├── permissions.ts     # Permission management
  │       │   ├── storeService.ts    # Store data service
  │       │   └── types.ts           # Service type definitions
  │       ├── types/      # TypeScript type definitions
  │       │   ├── common.ts
  │       │   └── cookies.ts
  │       ├── util/       # Utility functions
  │       │   ├── metaTypeGuards.ts  # Meta API type guards
  │       │   └── permissionHelpers.ts # Permission helper functions
  │       └── utils/      # Additional utilities
  │           ├── authUtils.ts       # Authentication utilities
  │           ├── consoleFilter.ts   # Console output filtering
  │           ├── errorUtils.ts      # Error handling utilities
  │           ├── localizationUtils.ts # Localization helpers
  │           ├── locationUtils.ts   # Location-based utilities
  │           ├── logerrors.ts       # Error logging
  │           ├── logger.ts          # Client-side logging
  │           ├── metricCards.ts     # Metric card configurations
  │           └── validation.ts      # Input validation utilities
  ├── pyrightconfig.json # Pyright (Python type checker) configuration
  └── repomix.config.json # Repomix configuration file

## Root Directory Files
- `.python-version` - Specifies Python 3.11.9 for consistent development environments using pyenv.
- `pyrightconfig.json` - Python type checking configuration for better code quality. Details might include `venvPath`, `pythonPath`, and `typeCheckingMode`.
- `.env.template` - Template for root environment variables, often used by data processing or utility scripts.
- `requirements.txt` - Core project dependencies (details often listed under Setup or Dependencies section).

### Backend Folder Details

#### `backend/config/`
- `__init__.py` - Package initialization file
- `database.py` - MongoDB connection setup with retry logic and async verification functions
- `settings.py` - Central configuration using Pydantic settings with environment variable loading
- `logging_config.py` - Logging configuration for application-wide consistent log formatting
- `logging.py` - Simplified logging setup for components that need basic logging
- `meta_permissions.py` - Meta API permissions configuration and validation

#### `backend/routes/`
- `__init__.py` - Package initialization file
- `store.py` - API endpoints for store data and analytics
- `chat.py` - API endpoints for AI chat interface functionality
- `meta.py` - API endpoints for Meta (Facebook/Instagram) integration
- `auth.py` - API endpoints for authentication and user management
- `meta_direct.py` - Direct API endpoints for Meta data without store context
- `insights.py` - API endpoints for business insights and analytics
- `admin.py` - API endpoints for administrative functions
- `meta_permissions.py` - API endpoints for Meta permissions management

#### `backend/services/`
- `__init__.py` - Service module initialization
- `chat.py` - Chat processing and AI interaction logic
- `meta.py` - Meta data processing and synchronization
- `store.py` - Store data processing and business logic
- `meta_permissions.py` - Meta permissions validation and management
- `auth.py` - Authentication service with token handling
- `seo_service.py` - SEO analysis and recommendations generation
- `insights.py` - Business insights generation and analysis
- `meta_utils.py` - Utility functions for Meta API integration
- `scheduled_tasks.py` - Background tasks and scheduled processes
- `email.py` - Email sending functionality for notifications and 2FA

#### `backend/middleware/`
- `__init__.py` - Package initialization file
- `cors.py` - CORS configuration for cross-origin requests

#### `backend/models/`
- `__init__.py` - Package initialization file
- `meta.py` - Pydantic models for Meta API data
- `insights.py` - Pydantic models for business insights
- `feedback.py` - Pydantic models for user feedback
- `chat.py` - Pydantic models for chat interactions
- `user.py` - Pydantic models for user data and authentication

#### `backend/utils/`
- `__init__.py` - Package initialization file
- `logger.py` - Custom logging utilities for standardized logging
- `jwt.py` - JWT token generation and validation
- `serialization.py` - Data serialization utilities for MongoDB

#### `backend/cron/`
- `update_meta_contexts.sh` - Shell script for scheduled Meta data updates

#### `backend/.ebextensions/`
- `01_packages.config` - AWS Elastic Beanstalk package installation configuration
- `02_python.config` - Python environment configuration for Elastic Beanstalk
- `03_nginx.config` - Nginx server configuration for Elastic Beanstalk
- `04_healthcheck.config` - Health check configuration for Elastic Beanstalk

#### Top-level backend files
- `__init__.py` - Backend package initialization
- `application.py` - WSGI application entry point for AWS Elastic Beanstalk
- `main.py` - FastAPI application initialization and API router registration
- `meta_db_init.py` - Database initialization script for Meta collections
- `Procfile` - Process configuration for deployment
- `requirements.txt` - Python package dependencies

### Frontend SRC Folder Details

#### `frontend/src/components/`
- `Dashboard.tsx` - Main dashboard component rendering store analytics overview
- `ScrollToTop.tsx` - Utility component for scrolling to top of page
- `ErrorBoundary.tsx` - React error boundary for graceful error handling

#### `frontend/src/config/`
- `environment.ts` - Environment configuration for different deployment environments
- `axios.ts` - Axios HTTP client configuration with interceptors
- `api.ts` - API endpoint configuration and base URLs

#### `frontend/src/contexts/`
- `MetaPermissionsContext.tsx` - Context provider for Meta API permissions
- `feedback.context.ts` - Feedback context type definitions
- `FeedbackContext.tsx` - Context provider for user feedback system
- `AuthContext.tsx` - Authentication context with login/logout functionality
- `ThemeContext.tsx` - Theme context provider for light/dark mode support

#### `frontend/src/services/`
- `axiosConfig.ts` - Axios instance configuration with auth headers
- `metaStoreService.ts` - Service for Meta (Facebook/Instagram) data retrieval and processing
- `generalStoreService.ts` - General store data access service
- `apiService.ts` - Core API service with reusable request methods
- `storeService.ts` - Store-specific data access and processing
- `authService.ts` - Authentication service for login/logout and token management
- `ragService.ts` - Retrieval Augmented Generation service for AI features

#### `frontend/src/pages/`
- `StoreView.tsx` - Store overview page showing key metrics
- `AdminPortal.tsx` - Administrative interface for system management
- `BackendLogin.tsx` - Backend administration login page

#### `frontend/src/utils/`
- `metricCards.ts` - Configuration for metric cards display
- `logger.ts` - Logging utilities for client-side logging
- `locationUtils.ts` - Utilities for location-based functionality

#### `frontend/src/hooks/`
- `useMetaAuth.ts` - Custom hook for Meta authentication

#### Root source files
- `App.tsx` - Main application component with routing
- `App.css` - Global application styles
- `main.tsx` - Application entry point with React rendering
- `Router.tsx` - Application routing configuration
- `i18n.ts` - Internationalization configuration
- `theme.ts` - UI theme configuration with Material UI theming
- `index.css` - Global CSS styles and typography
- `types.d.ts` - Global TypeScript type definitions
- `vite-env.d.ts` - Vite-specific TypeScript declarations

## Setup Instructions

### Prerequisites
- Python 3.11.9
- Node.js 16+ (tested on 16.14.0)
- MongoDB Atlas (5.0+)
- OpenAI API key (gpt-4o-mini compatible)
- Meta Developer Account with Facebook App
- Git, Rust, Visual Studio Build Tools (for some Python packages)

### 1. Clone Repository
```bash
git clone https://github.com/D-Unit912/D-Unit
cd D-Unit
```

### 2. Backend Setup
```bash
cd backend
python -m venv venv
source venv/Scripts/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### 4. Configure Environment Variables
- Create a `.env` file in the root directory and in both `backend/` and `frontend/` as needed, using the provided `.env.template` files as a reference.
- Ensure all required variables (MongoDB, OpenAI, Meta, JWT, etc.) are set for each environment.

### Data Processing Workflow
- See the 'Data Processing Workflow' section for scripts to convert, import, and enrich data.

## Deployment Guide

### AWS Deployment (Traditional)

#### Backend (Elastic Beanstalk)
```bash
# Install EB CLI
pip install awsebcli

# Initialize and deploy
eb init d-unit-backend
eb create production
eb deploy

# Environment variables via EB console:
# - JWT_SECRET_KEY
# - OPENAI_API_KEY
# - MONGODB_URI
# - META_APP_SECRET
```

#### Frontend (S3 + CloudFront)
```bash
# Build and deploy
npm run build
aws s3 sync dist/ s3://d-unit-frontend-bucket
aws cloudfront create-invalidation --distribution-id ABCD123 --paths "/*"

# Configure S3 bucket policy and CloudFront distribution
# Use frontend/config/distribution-config.json as reference
```

### Kubernetes Deployment (Modern)

#### Backend (Container/Kubernetes)
```bash
# Build and push container
cd backend
docker build -t d-unit-backend .
docker tag d-unit-backend:latest ************.dkr.ecr.sa-east-1.amazonaws.com/d-unit-backend:latest
docker push ************.dkr.ecr.sa-east-1.amazonaws.com/d-unit-backend:latest

# Deploy to Kubernetes
kubectl apply -f backend/deployment/d-unit-backend-service.yaml
kubectl apply -f backend/deployment/d-unit-backend-configmap.yaml
kubectl apply -f backend/deployment/d-unit-backend-secrets.yaml

# Monitor deployment
kubectl get pods -l app=d-unit-backend
kubectl logs -f deployment/d-unit-backend
```

#### Environment Configuration
```yaml
# backend/deployment/d-unit-backend-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: d-unit-backend-secrets
type: Opaque
stringData:
  JWT_SECRET_KEY: "your-jwt-secret"
  OPENAI_API_KEY: "your-openai-key"
  MONGODB_URI: "your-mongodb-connection"
  META_APP_SECRET: "your-meta-secret"
```

### Production Checklist

#### Security Configuration
- ✅ **HTTPS Enforcement**: All environments use SSL/TLS
- ✅ **Environment Variables**: Secrets managed via AWS Secrets Manager or Kubernetes Secrets
- ✅ **CORS Configuration**: Proper origins whitelisted
- ✅ **Security Headers**: HSTS, CSP, X-Frame-Options configured
- ✅ **Rate Limiting**: Per-endpoint limits configured
- ✅ **Cost Limits**: Budget enforcement active

#### Monitoring & Observability
- ✅ **CloudWatch/Prometheus**: Application metrics and logs
- ✅ **Health Checks**: Kubernetes liveness and readiness probes
- ✅ **Error Tracking**: Comprehensive error logging and alerting
- ✅ **Performance Monitoring**: Response time and throughput tracking
- ✅ **Security Events**: Attack detection and incident response

#### Database & Storage
- ✅ **MongoDB Atlas**: Production cluster with backups
- ✅ **Connection Pooling**: Optimized database connections
- ✅ **Indexes**: 35+ production indexes for performance
- ✅ **TTL Cleanup**: Automatic data lifecycle management

### Deployment Commands Summary
```bash
# Traditional AWS
eb deploy                                    # Deploy backend to Elastic Beanstalk
aws s3 sync dist/ s3://bucket               # Deploy frontend to S3

# Modern Kubernetes
kubectl apply -f backend/deployment/        # Deploy all Kubernetes resources
docker build -t d-unit-backend .           # Build container image
helm upgrade d-unit ./charts/d-unit        # Deploy via Helm (if using charts)

# Monitoring
kubectl logs -f deployment/d-unit-backend   # View backend logs
aws logs tail /aws/elasticbeanstalk/        # View EB logs
```

## Core Functionality

D-Unit provides:
- **Data Enrichment:** Adds product performance history, customer behavior patterns, market positioning, and social media engagement metrics.
- **Vector Search:** Uses embeddings for product similarity, market trend analysis, customer preference matching, and content similarity analysis.
- **AI Analysis:** Delivers product optimization, store performance metrics, customer segmentation, and social media content analysis.
- **Recommendations:** Generates actionable suggestions based on historical data, market trends, competition, and social media performance.

## User Interface Features

- **Dashboard:** Displays key metrics (sales, products, customers) and AI recommendations.
- **Product Analysis:** Tracks performance, provides competitive insights, and shows cached product details.
- **Customer Analytics:** Segments customers, shows purchase history, and visualizes demographics.
- **Meta Analytics:** Presents Facebook/Instagram performance metrics, audience insights, post analytics, and ad performance.
- **Settings:** Manage company profile, password, 2FA, and Meta login email.
- **Chat Interface:** Query-based insights across all platforms with conversation history and multimodal input (text and images).
- **Cookie Management:** GDPR/CCPA compliant cookie banner and settings interface with granular category controls, detailed cookie information tables, consent tracking, and multi-language support.

## Meta Integration Features

### Authentication and Data Access
- Facebook Login integration for secure authentication
- Business account selection and page access
- Secure token management and permission handling

### Data Extraction
- Page insights and metrics (impressions, reach, engagement)
- Post content and engagement data
- Audience demographics and interests
- Ad performance metrics (when available)

### AI-Powered Insights (RAG)
- Content analysis with engagement patterns
- Sentiment analysis of comments
- Correlation between social media activity and sales
- Audience segmentation and targeting recommendations

### Chat Interface
- Meta-aware context building for relevant responses
- Specialized handling of Meta-related queries
- Cross-platform insights connecting social media with sales
- Natural language responses to complex analytics questions

### Meta Integration Data Flow

1. **Authentication**: Store owner logs in with Meta (Facebook Login), system receives access token and permissions, token is stored securely.
2. **Data Collection**: System fetches business accounts, pages, posts, comments, engagement metrics, audience demographics, and ad campaign data (if available).
3. **Data Storage**: Data is stored in dedicated MongoDB collections, organized by store_id for isolation and indexed for efficient querying.
4. **Data Processing**: RAG service analyzes collected data, generates insights using AI models, and creates actionable recommendations.
5. **Data Presentation**: Insights are displayed in the Meta dashboard and chat interface, with metrics visualized in charts and graphs.
6. **Data Refresh**: System periodically refreshes data from Meta API, updates metrics, and maintains historical data for trend analysis.

### MongoDB Collections Usage

- **meta_pages**: Connected Facebook/Instagram pages
- **meta_posts**: Social media posts and metadata
- **meta_post_metrics**: Engagement metrics for posts
- **meta_comments**: Comments on posts
- **meta_followers**: Follower count and growth
- **meta_demographics**: Audience demographic information
- **meta_ad_campaigns**: Ad campaign data
- **meta_ad_metrics**: Ad performance metrics
- **meta_insights**: AI-generated insights
- **global_analysis.meta_integration**: Connection status and metrics for each store

## Retrieval Augmented Generation (RAG)

- **Purpose:** Enhances the AI's ability to answer user questions by providing relevant, up-to-date context directly from the store's data.
- **Mechanism:**
  1. **Embedding Search:** When a user query is received, the system generates an embedding vector for the query text.
  2. **Similarity Search:** This query vector is compared against pre-computed embedding vectors stored for key data collections (e.g., `product_details_cache`, `store_customers_cache`, `active_stores_cache`) for the specific store.
  3. **Context Retrieval:** Documents with the highest similarity scores (top 8, prioritizing product details for product-related queries) are retrieved.
  4. **Context Formatting:** The content of these retrieved documents is formatted and summarized.
     - **Product Context Enhancement:** For product-related queries, the system extracts keywords and filters the full product list from `product_details_cache` based on these keywords (or includes all products for generic queries). All matching products are included in the context (up to a character limit of 3000), with a clear header indicating the total count and a truncation message if the list is too long.
  5. **Prompt Injection:** The formatted context (including the schema overview, persona prompt, additional data like store analysis or Meta info, and the RAG results) is prepended to the system prompt sent to the LLM.
- **Benefits:** Allows the AI to answer questions based on the specific store's current products, customers, and performance, rather than relying solely on its general knowledge.
- **Implementation:** Primarily handled by the `_search_with_embeddings` and `get_chat_response` functions in `backend/services/chat.py`.

## API Endpoints

The backend provides a RESTful API for all major platform features. Key endpoints include:

### Core Platform Endpoints
- **Auth:** `/api/auth/*` (authentication, user management, 2FA)
  - `POST /api/auth/login` - User login
  - `POST /api/auth/register` - User registration
  - `GET /api/auth/csrf-token` - CSRF token retrieval
  - `POST /api/auth/verify-2fa` - Two-factor authentication
- **Admin:** `/api/admin/*` (administrative functions)
  - `GET /api/admin/stores` - List all stores
  - `POST /api/admin/password-reset` - Admin password reset
- **Security:** `/api/security/*` (security monitoring and events)
  - `GET /api/security/events` - Security event logs
  - `POST /api/security/report` - Security incident reporting

### Store-Specific Endpoints
- **Store Analytics:** `/api/store/{store_id}/*`
  - `GET /api/store/{store_id}/products` - Product analytics and performance
  - `GET /api/store/{store_id}/customers` - Customer demographics and behavior
  - `GET /api/store/{store_id}/metrics` - Store performance metrics
  - `GET /api/store/{store_id}/analysis` - AI-generated business analysis
- **Chat Interface:** `/api/chat/{store_id}/*`
  - `POST /api/chat/{store_id}/message` - Send chat message to AI
  - `GET /api/chat/{store_id}/history` - Retrieve chat conversation history
- **Insights:** `/api/insights/{store_id}/*`
  - `GET /api/insights/{store_id}/recommendations` - AI business recommendations
  - `GET /api/insights/{store_id}/seo` - SEO analysis and suggestions

### Meta (Facebook/Instagram) Integration
- **Meta Core:** `/api/meta/{store_id}/*`
  - `GET /api/meta/{store_id}/pages` - Connected Facebook/Instagram pages
  - `GET /api/meta/{store_id}/posts/{post_id}/metrics` - Post engagement metrics
  - `GET /api/meta/{store_id}/permissions` - Meta API permissions status
- **Meta Direct:** `/api/meta-direct/*` (direct Meta API access)
  - `GET /api/meta-direct/user` - Meta user information
  - `GET /api/meta-direct/pages` - Available pages for connection

### Social Media Analytics (NEW 2025)
- **Facebook Analytics:** `/api/socialmedia/facebook/*`
  - `GET /api/socialmedia/facebook/user` - User profile and information
  - `GET /api/socialmedia/facebook/page` - Page overview and metrics
  - `GET /api/socialmedia/facebook/page/insights` - Page performance insights
  - `GET /api/socialmedia/facebook/posts` - Posts with engagement data
  - `GET /api/socialmedia/facebook/events` - Upcoming events and attendance
  - `GET /api/socialmedia/facebook/messenger/stats` - Messenger statistics
- **Instagram Analytics:** `/api/socialmedia/instagram/*`
  - `GET /api/socialmedia/instagram/profile` - Profile overview and metrics
  - `GET /api/socialmedia/instagram/insights` - Account performance insights
  - `GET /api/socialmedia/instagram/media` - Media posts and engagement
  - `GET /api/socialmedia/instagram/stories` - Stories performance metrics
- **Marketing Tools:** `/api/socialmedia/marketing/*`
  - `GET /api/socialmedia/marketing/ads` - Cross-platform ad performance
  - `GET /api/socialmedia/marketing/campaigns` - Active campaigns and results
  - `GET /api/socialmedia/marketing/audiences` - Audience demographics
  - `GET /api/socialmedia/marketing/catalog` - Product catalog management
  - `GET /api/socialmedia/marketing/pixel` - Meta Pixel tracking data
- **Utility Endpoints:** `/api/socialmedia/*`
  - `GET /api/socialmedia/metrics/{platform}` - Platform-specific metrics
  - `POST /api/socialmedia/refresh` - Refresh social media data

### Security & Cost Management
- **CSRF Protection:** All POST/PUT/DELETE endpoints require `X-CSRF-Token` header
- **Rate Limiting:** Intelligent rate limiting with per-endpoint configuration
- **Cost Tracking:** Real-time OpenAI usage tracking with budget enforcement
- **Authentication:** JWT-based authentication with secure cookie management

*Note: Most endpoints interacting with store data use `{store_id}` as a path parameter for clear resource association and access control. All endpoints are protected by the 4-layer security gateway.*

## Recent Updates

### Environment Configuration Improvements
- Improved MongoDB connection handling and environment file loading.
- Separated environment configurations for backend, frontend, and utility scripts for better isolation and security.

### Performance Optimizations
- Pagination added to Product Details chart for better usability.
- Embeddings generator script optimized with parallel processing, enhanced caching, and reduced storage usage.

### UI Improvements
- Added global scroll-to-top button and enhanced component reusability.
- Improved pagination and loading indicators for product details.
- Enabled video background and dark mode toggle on authentication pages.
- Fixed minor text, style, and layout inconsistencies for a more polished user experience.

### Logging & Monitoring

D-Unit uses a standardized logging approach:
- **Backend:** Python's `logging` module is centrally configured for structured, environment-based logging with masking for sensitive data.
- **Frontend:** A custom utility replaces `console.*` calls for environment-aware logging (suppresses `DEBUG` in production).
This unified approach enhances observability and simplifies debugging.

## Troubleshooting Notes

- For Meta permissions/authentication issues, check for correct token usage and permission scopes. Use the Meta Permissions Panel for diagnostics.
- Backend errors should be reviewed in the logs (see Logging & Monitoring section). Validation errors now return proper client error codes (e.g., 400) instead of generic 500 errors.
- If the Meta API is unavailable, the system will automatically use cached data from MongoDB.
- For UI/UX issues, use browser DevTools to inspect logs and network requests.
- See the backend and frontend readme folders for more detailed troubleshooting guides.

## Cookie Management System Technical Implementation

### Files Modified/Created
- **frontend/src/config/cookies.ts:** Updated with translation keys for all cookie purposes, added translateDuration utility function for time unit translation
- **frontend/public/locales/en/translation.json:** Added comprehensive English translations for cookie purposes, categories, durations, and policy content
- **frontend/public/locales/es/translation.json:** Added complete Spanish translations for all cookie-related content including time units
- **frontend/src/components/common/CookieBanner.tsx:** Enhanced with translation support for cookie purposes and durations
- **frontend/src/components/settings/CookieManagementCard.tsx:** Added translated tooltips, enhanced cookie display, and legal policy links
- **frontend/src/components/legal/CookiePolicyPage.tsx:** New comprehensive cookie policy page with legal documentation and navigation
- **frontend/src/pages/CookiePolicy.tsx:** Route wrapper for cookie policy page
- **frontend/src/Router.tsx:** Added public route for cookie policy access
- **frontend/src/components/settings/Settings.tsx:** Added cookie management section ID for navigation targeting

### Translation System Features
- **14 Cookie Purposes:** Authentication, session management, CSRF protection, store isolation, content delivery, user preferences, localization, theme customization, analytics, navigation patterns, performance monitoring, error tracking, Meta integration, Facebook tools
- **5 Cookie Categories:** Essential (required), Functional, Analytics, Performance, Marketing with individual control switches
- **Time Duration Translation:** Automatic conversion of "2 hours" → "2 horas", "7 days" → "7 días", "1 year" → "1 año"
- **Complete Bilingual Support:** All cookie-related UI elements, descriptions, legal text, and policy content available in English and Spanish

### Legal Compliance Features
- **Regulatory Framework Support:** GDPR (EU), CCPA (California), LGPD (Brazil), PIPEDA (Canada)
- **Cookie Inventory Table:** Complete listing with name, purpose, category, duration, type (first/third-party), provider information
- **User Rights Documentation:** Access, rectification, erasure, data portability, consent withdrawal, objection rights
- **Legal Basis Information:** Consent, legitimate interest, contractual necessity classifications
- **Privacy Integration:** Links to privacy policy (external) and cookie policy (internal) in settings interface

### Navigation & User Experience
- **Seamless Policy Access:** Cookie policy button in banner and settings redirects to `/cookie-policy` page
- **Settings Integration:** "Gestionar Preferencias de Cookies" button navigates to settings page and scrolls to cookie management section
- **Interactive Elements:** Expandable cookie categories, detailed tooltips, reset functionality, professional Material-UI design
- **Responsive Design:** Mobile-friendly interface with proper spacing, touch-friendly controls, and accessible navigation

### Technical Architecture
- **Translation Function:** `translateDuration(duration, t)` with regex-based replacement for time units
- **Event-Driven Updates:** Real-time preference synchronization across components
- **Material-UI Integration:** Professional design with consistent theming, icons, and responsive layouts
- **Router Integration:** Public routes for policy access, proper navigation handling, scroll-to-section functionality

## 🚀 Latest Technical Achievements (2025)

### **Enterprise-Grade Security Gateway (97% Complete)**
D-Unit now features a production-ready security infrastructure that rivals enterprise-level platforms:

#### **4-Layer Security Architecture**
1. **Security Headers Layer**: OWASP Top 10 protection, HSTS, CSP, CSRF prevention
2. **Security Gateway Layer**: Central threat detection, attack pattern recognition, automated response
3. **Rate Limiting Layer**: Intelligent rate limiting with multi-algorithm support and complexity scoring
4. **Request Validation Layer**: Input sanitization, content filtering, comprehensive validation rules

#### **Advanced Cost Management**
- **Real-time OpenAI Cost Tracking**: Per-model cost monitoring with accuracy validation
- **Store-Tier Budget Controls**: Free ($0-$10), Basic ($10-$50), Premium ($50-$200), Enterprise ($200+)
- **Emergency Stop Mechanisms**: 95% budget threshold protection with automated shutdowns
- **Cost Optimization**: Automated recommendations and usage analytics

#### **Production-Ready Infrastructure**
- **35+ Database Indexes**: Optimized MongoDB queries with TTL-based cleanup
- **Thread-Safe Operations**: High-concurrency support with in-memory caching
- **Comprehensive Monitoring**: Security event logging, threat detection, audit trails
- **Dynamic Configuration**: Hot-reload security rules with YAML-based configuration

### **Comprehensive Testing Infrastructure (100% Complete)**
D-Unit implements industry-standard testing practices ensuring bulletproof reliability:

#### **5-Category Testing Framework**
1. **Unit Tests (8 components)**: Individual middleware and service component testing
2. **Integration Tests (4 workflows)**: Component interaction and workflow validation
3. **Load Tests (4 scenarios)**: Performance and scalability under production load
4. **Security Tests (4 attack types)**: Vulnerability detection and attack simulation
5. **Cost Tracking Tests (3 validation areas)**: Budget enforcement and billing accuracy

#### **Advanced CI/CD Pipeline**
- **GitHub Actions Workflow**: Multi-Python version testing (3.9, 3.10, 3.11)
- **Service Integration**: MongoDB and Redis containers for realistic testing
- **Coverage Reporting**: Codecov integration with >90% coverage targets
- **Performance Monitoring**: Automated regression detection with benchmarking
- **Security Scanning**: Bandit integration for vulnerability detection

#### **Test Automation Benefits**
- **100% Test Success Rate**: All 20 security gateway tests passing
- **Change Protection**: Immediate feedback on code modifications
- **Production Safety**: Prevents broken code from reaching users
- **Performance Tracking**: Monitors app performance over time
- **Security Validation**: Continuous vulnerability scanning

#### **Real-World Impact**
```bash
# Example: Developer changes rate limiting code
python -m pytest tests/unit/middleware/test_rate_limiter.py
# ❌ Tests fail → Rate limiting broken BEFORE deployment
# ✅ All pass → Safe to deploy

# Example: Cost calculation modification
python -m pytest tests/cost/
# ❌ Tests fail → Cost tracking broken, could lose money
# ✅ All pass → Billing accuracy maintained
```

### **Combined Technical Excellence**
The integration of enterprise security and comprehensive testing creates:

1. **Production Confidence**: Every security component validated with automated testing
2. **Operational Safety**: Cost tracking prevents revenue loss, security prevents attacks
3. **Development Velocity**: Developers can make changes safely with instant feedback
4. **Enterprise Readiness**: Security and testing standards that meet enterprise requirements
5. **Scalability Foundation**: Infrastructure ready for high-traffic production environments

---

### Analytical Foundation

D-Unit leverages AI for deep analytical insights, combining:
- **Quantitative & Qualitative Analysis:** Integrates numerical data (sales, engagement) and qualitative data (feedback, sentiment).
- **Statistical Modeling:** Uses regression and other methods to identify key business drivers.
- **Contextual Insights:** Blends automated analysis with store owner expertise for actionable strategies.

### Meta Dashboard Features
- **Overview:** Page impressions, engagement, follower growth, and page views with time-filtered visualization.
- **Posts Analytics:** Engagement and post type distribution, top posts, and performance trends.
- **Audience Demographics:** Age, gender, geography, language, and interests.
- **Ad Metrics:** Campaign performance, ROI, daily metrics, and objective comparison.
- **Campaigns Comparison:** Cross-campaign analysis, correlation, and impact assessment.

### Visual Design
- **Color Scheme**:
  - Primary: Light Blue (#00A3FF)
  - Secondary: Lighter Blue (#82b8ff)
  - Tertiary: Very Light Blue (#cce5ff)
  - Consistent across all visualizations
- **Chart Components**:
  - Interactive tooltips
  - Responsive layouts
  - Dynamic data updates
  - Smooth transitions

### Time Range Filtering
- **Preset Options**:
  - Last 7 days ('7d')
  - Last 30 days ('30d')
  - Last 90 days ('90d')
  - Last 180 days ('180d')
  - Last year ('1y')
  - Custom date range
- **Features**:
  - Real-time data updates
  - Consistent filtering across all sections
  - Preserved filter state between tabs
  - Custom date range selection
  - Proper error handling
  - Loading state indicators

### Dashboard Features
- **Time Range Filtering:** Dynamic filtering across all sections (daily, weekly, monthly, yearly).
- **Customer Demographics:** Interactive pie charts, country/city breakdowns, and time-filtered trends.
- **Customer Insights:** Behavior analysis, active customer tracking, geographic distribution, cart abandonment, and conversion optimization.
- **Product Analysis:** Cached product details, historical sales, revenue, price, and stock; performance charts and paginated browsing.
- **Competitor Analysis:** Market position tracking, price trends, and competitive landscape analysis.
- **Shipping Analytics:** Method usage, performance metrics, and time-based trends.
