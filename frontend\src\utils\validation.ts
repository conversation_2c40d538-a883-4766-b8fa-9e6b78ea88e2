/**
 * Unified validation utilities for consistent validation across the application
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validation patterns and rules
 */
export const ValidationPatterns = {  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,  PHONE: /^[+]?[1-9][\d]{0,15}$/,  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/,  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,} as const;

/**
 * Common validation messages
 */
export const ValidationMessages = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Please enter a valid email address',
  EMAIL_TOO_LONG: 'Email address is too long (maximum 254 characters)',
  COMPANY_NAME_INVALID: 'Company name must be at least 2 characters long',
  COMPANY_NAME_TOO_LONG: 'Company name is too long (maximum 100 characters)',
  PHONE_INVALID: 'Please enter a valid phone number',
  URL_INVALID: 'Please enter a valid URL',
  PASSWORD_WEAK: 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character',
} as const;

/**
 * Validation utility class with standardized validation methods
 */
export class ValidationUtils {
  /**
   * Validate email address
   */
  static validateEmail(email: string): ValidationResult {
    if (!email) {
      return { isValid: false, error: ValidationMessages.REQUIRED };
    }

    if (email.length > 254) {
      return { isValid: false, error: ValidationMessages.EMAIL_TOO_LONG };
    }

    if (!ValidationPatterns.EMAIL.test(email)) {
      return { isValid: false, error: ValidationMessages.EMAIL_INVALID };
    }

    return { isValid: true };
  }

  /**
   * Validate company name
   */
  static validateCompanyName(companyName: string): ValidationResult {
    if (!companyName || companyName.trim().length === 0) {
      return { isValid: false, error: ValidationMessages.REQUIRED };
    }

    if (companyName.trim().length < 2) {
      return { isValid: false, error: ValidationMessages.COMPANY_NAME_INVALID };
    }

    if (companyName.length > 100) {
      return { isValid: false, error: ValidationMessages.COMPANY_NAME_TOO_LONG };
    }

    return { isValid: true };
  }

  /**
   * Validate phone number
   */
  static validatePhone(phone: string): ValidationResult {
    if (!phone) {
      return { isValid: false, error: ValidationMessages.REQUIRED };
    }

    if (!ValidationPatterns.PHONE.test(phone)) {
      return { isValid: false, error: ValidationMessages.PHONE_INVALID };
    }

    return { isValid: true };
  }

  /**
   * Validate URL
   */
  static validateUrl(url: string): ValidationResult {
    if (!url) {
      return { isValid: false, error: ValidationMessages.REQUIRED };
    }

    if (!ValidationPatterns.URL.test(url)) {
      return { isValid: false, error: ValidationMessages.URL_INVALID };
    }

    return { isValid: true };
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): ValidationResult {
    if (!password) {
      return { isValid: false, error: ValidationMessages.REQUIRED };
    }

    if (!ValidationPatterns.PASSWORD.test(password)) {
      return { isValid: false, error: ValidationMessages.PASSWORD_WEAK };
    }

    return { isValid: true };
  }

  /**
   * Validate required field
   */
  static validateRequired(value: string | null | undefined, fieldName: string = 'field'): ValidationResult {
    if (!value || (typeof value === 'string' && value.trim().length === 0)) {
      return { isValid: false, error: `${fieldName} is required` };
    }

    return { isValid: true };
  }

  /**
   * Validate string length
   */
  static validateLength(
    value: string,
    minLength: number,
    maxLength: number,
    fieldName: string = 'field'
  ): ValidationResult {
    if (value.length < minLength) {
      return { isValid: false, error: `${fieldName} must be at least ${minLength} characters long` };
    }

    if (value.length > maxLength) {
      return { isValid: false, error: `${fieldName} cannot exceed ${maxLength} characters` };
    }

    return { isValid: true };
  }

  /**
   * Validate multiple rules and return first error found
   */
  static validateMultiple(...validations: ValidationResult[]): ValidationResult {
    for (const validation of validations) {
      if (!validation.isValid) {
        return validation;
      }
    }
    return { isValid: true };
  }
}

/**
 * Legacy function compatibility for email validation
 * @deprecated Use ValidationUtils.validateEmail instead
 */
export const validateEmail = (email: string): string => {
  const result = ValidationUtils.validateEmail(email);
  return result.isValid ? '' : result.error || '';
};

/**
 * Legacy function compatibility for company name validation
 * @deprecated Use ValidationUtils.validateCompanyName instead
 */
export const validateCompanyName = (companyName: string): string => {
  const result = ValidationUtils.validateCompanyName(companyName);
  return result.isValid ? '' : result.error || '';
}; 