import React from 'react';
import {
  Paper,
  Typography,
  Alert,
  AlertTitle,
  Button
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface ErrorDisplayProps {
  error: string | null;
  title?: string;
  retry?: () => void;
  variant?: 'inline' | 'fullWidth' | 'minimal';
  severity?: 'error' | 'warning' | 'info';
  className?: string;
}

/**
 * A reusable component for displaying errors in the application.
 * It supports different variants and includes a retry button if a retry function is provided.
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title = 'Error',
  retry,
  variant = 'inline',
  severity = 'error',
  className
}) => {
  if (!error) return null;

  // For minimal variant, just return an Alert
  if (variant === 'minimal') {
    return (
      <Alert 
        severity={severity} 
        className={className}
        action={retry ? (
          <Button 
            color="inherit" 
            size="small" 
            onClick={retry}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        ) : undefined}
      >
        {error}
      </Alert>
    );
  }

  // For inline variant, return an Alert with a title
  if (variant === 'inline') {
    return (
      <Alert 
        severity={severity} 
        className={className}
        action={retry ? (
          <Button 
            color="inherit" 
            size="small" 
            onClick={retry}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        ) : undefined}
      >
        <AlertTitle>{title}</AlertTitle>
        {error}
      </Alert>
    );
  }

  // For fullWidth variant, return a more prominent error display
  return (
    <Paper 
      className={className}
      sx={{ 
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 2,
        width: '100%',
        border: severity === 'error' ? '1px solid #f44336' : undefined
      }}
    >
      <ErrorOutlineIcon color={severity} sx={{ fontSize: 48 }} />
      
      <Typography variant="h6" color={`${severity}.main`} align="center">
        {title}
      </Typography>
      
      <Typography variant="body1" color="text.secondary" align="center">
        {error}
      </Typography>
      
      {retry && (
        <Button 
          variant="outlined" 
          color={severity}
          onClick={retry}
          startIcon={<RefreshIcon />}
          sx={{ mt: 1 }}
        >
          Try Again
        </Button>
      )}
    </Paper>
  );
}; 