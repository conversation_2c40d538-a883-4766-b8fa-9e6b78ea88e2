#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to update the store activity metrics collection in MongoDB for LaNube dashboard
This script tracks login frequency, visit patterns, and order activity for all stores
Implements a traffic light system to indicate engagement levels

Enhanced Version (2025-04):
- Added store_users.updated_at tracking as a more reliable proxy for user activity
- Improved traffic light calculation to prioritize reliable metrics (visits, orders, user activity)
- Includes 30-day, 90-day, and 365-day metrics for comprehensive analysis
- Added timezone-awareness for all datetime comparisons to prevent errors

Note on Login Tracking:
- Original login tracking via login_attempts table is maintained for historical purposes
- This data is limited to 8 stores with login history (last entries from 2022)
- User activity from store_users.updated_at is now the preferred indicator for user engagement
"""

import os
import sys
import logging
import mysql.connector
import pymongo
from pymongo.errors import BulkWriteError, PyMongoError
from datetime import datetime, timezone, timedelta
import traceback
import json
from typing import Dict, List, Optional, Union, Any, Tuple

# Constants - Activity Metrics
ACTIVITY_PERIOD_SHORT_DAYS = 30  # Short-term activity period (30 days)
ACTIVITY_PERIOD_LONG_DAYS = 90   # Long-term activity period (90 days)
ACTIVITY_PERIOD_YEAR_DAYS = 365  # Full year activity period (365 days)

# Traffic Light Thresholds
LOGIN_THRESHOLD_GREEN = 4        # At least weekly login for green status
VISIT_THRESHOLD_GREEN = 20       # Good visit volume for green status
ORDER_THRESHOLD_GREEN = 1        # Recent orders for green status

LOGIN_THRESHOLD_YELLOW = 1       # At least one login in last 30 days for yellow
VISIT_THRESHOLD_YELLOW = 5       # Some visits for yellow status
DAYS_INACTIVE_THRESHOLD_YELLOW = 60  # Activity within 60 days for yellow

# Database configurations from environment variables
MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
MYSQL_USER = os.getenv('MYSQL_USER', 'root')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
MYSQL_DB_LANUBE = os.getenv('MYSQL_DB_LANUBE', 'lanube')

# Valid order statuses (configurable)
# Document: 1=Pending, 2=Paid, 5=Delivered, 6=Cancelled, 7=Complete
VALID_ORDER_STATUSES = os.getenv('VALID_ORDER_STATUSES', '2,5,7').split(',')

# Batch processing size
BATCH_SIZE = 1000

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("--- EXECUTING update_store_activity_metrics.py ---")

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
    logger.info("Successfully imported settings")
except ImportError as e:
    # Use print for early errors before logging might be configured
    logger.error(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.")
    sys.exit(1)
except Exception as e:
    logger.error(f"FATAL: Error initializing settings: {e}")
    sys.exit(1)

# Logging removed - using print statements instead

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    TARGET_COLLECTION_NAME = 'store_activity_metrics'
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    # MongoDB settings
    MONGO_CONNECTION_STRING = settings.MONGODB_CONNECTION
    MONGO_DB_NAME = settings.MONGODB_MAIN_DB
    logger.info("Successfully loaded all required settings")
    logger.info(f"Using order statuses: {VALID_ORDER_STATUSES}")
except AttributeError as e:
    logger.error(f"FATAL: Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# --- Helper Functions ---

def safe_infinity_value(value: Union[float, int, None]) -> Union[int, None]:
    """Convert infinity values to None or large number for MongoDB compatibility."""
    if value is None:
        return None
    if value == float('inf'):
        return 999999  # Large number instead of infinity
    return int(value) if isinstance(value, float) else value

def fetch_all_stores_basic_data(mysql_conn) -> List[Dict[str, Any]]:
    """Fetches basic data for all stores (active and inactive).
    
    Args:
        mysql_conn: MySQL connection object.
        
    Returns:
        A list of dictionaries, each containing basic data for one store.
    """
    all_stores_data_list: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("ERROR: MySQL connection is not available for fetch_all_stores_basic_data.")
        return all_stores_data_list
        
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query_all_stores = """
            SELECT 
                s.id_store, s.name, s.email, s.active, s.created_at, s.updated_at
            FROM 
                stores s
            ORDER BY 
                s.id_store;
            """
            logger.info("INFO: Executing query to fetch data for all stores.")
            cursor.execute(query_all_stores)
            all_stores_data_list = cursor.fetchall()
            logger.info(f"INFO: Fetched basic data for {len(all_stores_data_list)} stores.")
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching store data: {err}")
        return []
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching store data: {e}")
        return []
        
    return all_stores_data_list

def fetch_store_login_activity(store_id: int, mysql_conn) -> Dict[str, Any]:
    """Fetches login activity data for a specific store.
    
    Args:
        store_id: The ID of the store to fetch login data for.
        mysql_conn: MySQL connection object.
        
    Returns:
        A dictionary containing login activity metrics.
    """
    login_metrics = {
        'last_login_date': None,
        'login_count_30d': 0,
        'login_count_90d': 0,
        'login_count_365d': 0
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.info(f"ERROR: MySQL connection is not available for fetch_store_login_activity (Store ID: {store_id}).")
        return login_metrics
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query_login_activity = """
            SELECT 
                la.time as login_time
            FROM 
                store_users su
            JOIN 
                login_attempts la ON su.id_user = la.id_user
            WHERE 
                su.id_store = %s
                AND la.time >= DATE_SUB(NOW(), INTERVAL 365 DAY)
            ORDER BY 
                la.time DESC;
            """
            
            cursor.execute(query_login_activity, (store_id,))
            login_data = cursor.fetchall()
            
            # Calculate metrics if we have login data
            if login_data:
                # Get the most recent login time
                login_metrics['last_login_date'] = login_data[0]['login_time']
                
                # Calculate counts for different time periods
                now = datetime.now(timezone.utc)
                thirty_days_ago = now - timedelta(days=ACTIVITY_PERIOD_SHORT_DAYS)
                ninety_days_ago = now - timedelta(days=ACTIVITY_PERIOD_LONG_DAYS)
                
                # Count logins in the last 30 days
                login_metrics['login_count_30d'] = sum(
                    1 for login in login_data 
                    if login['login_time'] and login['login_time'].replace(tzinfo=timezone.utc) >= thirty_days_ago
                )
                
                # Count logins in the last 90 days
                login_metrics['login_count_90d'] = sum(
                    1 for login in login_data 
                    if login['login_time'] and login['login_time'].replace(tzinfo=timezone.utc) >= ninety_days_ago
                )
                
                # Count logins in the last 365 days
                login_metrics['login_count_365d'] = len(login_data)
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching login activity for store ID {store_id}: {err}")
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching login activity for store ID {store_id}: {e}")
    
    return login_metrics

def fetch_store_visit_metrics_batched(store_id: int, mysql_conn) -> Dict[str, Any]:
    """Fetches visit activity data for a specific store using batch processing.
    
    Args:
        store_id: The ID of the store to fetch visit data for.
        mysql_conn: MySQL connection object.
        
    Returns:
        A dictionary containing visit activity metrics.
    """
    visit_metrics = {
        'last_visit_date': None,
        'visit_count_30d': 0,
        'visit_count_90d': 0,
        'visit_count_365d': 0
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.info(f"ERROR: MySQL connection is not available for fetch_store_visit_metrics (Store ID: {store_id}).")
        return visit_metrics
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # First, get the most recent visit
            query_last_visit = """
            SELECT 
                created_at as visit_time
            FROM 
                store_visits
            WHERE 
                id_store = %s
                AND created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)
            ORDER BY 
                created_at DESC
            LIMIT 1;
            """
            
            cursor.execute(query_last_visit, (store_id,))
            last_visit = cursor.fetchone()
            
            if last_visit:
                visit_metrics['last_visit_date'] = last_visit['visit_time']
            
            # Get visit counts for different periods
            now = datetime.now(timezone.utc)
            
            # Count visits in the last 30 days
            query_30d = """
            SELECT COUNT(*) as count
            FROM store_visits
            WHERE id_store = %s
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            """
            cursor.execute(query_30d, (store_id,))
            result_30d = cursor.fetchone()
            visit_metrics['visit_count_30d'] = result_30d['count'] if result_30d else 0
            
            # Count visits in the last 90 days
            query_90d = """
            SELECT COUNT(*) as count
            FROM store_visits
            WHERE id_store = %s
                AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            """
            cursor.execute(query_90d, (store_id,))
            result_90d = cursor.fetchone()
            visit_metrics['visit_count_90d'] = result_90d['count'] if result_90d else 0
            
            # Count visits in the last 365 days
            query_365d = """
            SELECT COUNT(*) as count
            FROM store_visits
            WHERE id_store = %s
                AND created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)
            """
            cursor.execute(query_365d, (store_id,))
            result_365d = cursor.fetchone()
            visit_metrics['visit_count_365d'] = result_365d['count'] if result_365d else 0
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching visit metrics for store ID {store_id}: {err}")
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching visit metrics for store ID {store_id}: {e}")
    
    return visit_metrics

def fetch_store_order_metrics_batched(store_id: int, mysql_conn) -> Dict[str, Any]:
    """Fetches order activity data for a specific store using batch processing.
    
    Args:
        store_id: The ID of the store to fetch order data for.
        mysql_conn: MySQL connection object.
        
    Returns:
        A dictionary containing order activity metrics.
    """
    order_metrics = {
        'last_order_date': None,
        'order_count_30d': 0,
        'order_count_90d': 0,
        'order_count_365d': 0,
        'revenue_30d': 0.0,
        'revenue_90d': 0.0,
        'revenue_365d': 0.0,
        'avg_order_value': 0.0
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.info(f"ERROR: MySQL connection is not available for fetch_store_order_metrics (Store ID: {store_id}).")
        return order_metrics
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Build status filter
            status_filter = ','.join(VALID_ORDER_STATUSES)
            
            # First, get the most recent order
            query_last_order = f"""
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       created_at,
                       id_order_status
                FROM store_orders
                WHERE id_store = %s
                UNION ALL
                SELECT id_order            AS id_store_order,
                       id_store,
                       date                AS created_at,
                       id_status           AS id_order_status
                FROM orders
                WHERE id_store = %s
            )
            SELECT created_at AS order_time
            FROM all_orders
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)
              AND id_order_status IN ({status_filter})
            ORDER BY created_at DESC
            LIMIT 1;
            """
            
            cursor.execute(query_last_order, (store_id, store_id))
            last_order = cursor.fetchone()
            
            if last_order:
                order_metrics['last_order_date'] = last_order['order_time']
            
            # Get aggregated metrics for different periods
            query_metrics = f"""
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       subtotal,
                       created_at,
                       id_order_status
                FROM store_orders
                WHERE id_store = %s
                UNION ALL
                SELECT id_order            AS id_store_order,
                       id_store,
                       amount              AS subtotal,
                       date                AS created_at,
                       id_status           AS id_order_status
                FROM orders
                WHERE id_store = %s
            )
            SELECT 
                COUNT(DISTINCT CASE WHEN ao.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                    THEN ao.id_store_order END) AS order_count_30d,
                COUNT(DISTINCT CASE WHEN ao.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY) 
                    THEN ao.id_store_order END) AS order_count_90d,
                COUNT(DISTINCT ao.id_store_order) AS order_count_365d,
                
                SUM(CASE WHEN ao.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                    THEN COALESCE(sop.price, 0) * COALESCE(sop.qty, 0) ELSE 0 END) AS revenue_30d,
                SUM(CASE WHEN ao.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY) 
                    THEN COALESCE(sop.price, 0) * COALESCE(sop.qty, 0) ELSE 0 END) AS revenue_90d,
                SUM(COALESCE(sop.price, 0) * COALESCE(sop.qty, 0)) AS revenue_365d
            FROM all_orders ao
            LEFT JOIN store_ordered_products sop ON ao.id_store_order = sop.id_store_order
            WHERE ao.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)
              AND ao.id_order_status IN ({status_filter})
            """
            
            cursor.execute(query_metrics, (store_id, store_id))
            metrics = cursor.fetchone()
            
            if metrics:
                order_metrics['order_count_30d'] = int(metrics['order_count_30d'] or 0)
                order_metrics['order_count_90d'] = int(metrics['order_count_90d'] or 0)
                order_metrics['order_count_365d'] = int(metrics['order_count_365d'] or 0)
                order_metrics['revenue_30d'] = float(metrics['revenue_30d'] or 0)
                order_metrics['revenue_90d'] = float(metrics['revenue_90d'] or 0)
                order_metrics['revenue_365d'] = float(metrics['revenue_365d'] or 0)
                
                # Calculate average order value based on yearly data
                if order_metrics['order_count_365d'] > 0:
                    order_metrics['avg_order_value'] = order_metrics['revenue_365d'] / order_metrics['order_count_365d']
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching order metrics for store ID {store_id}: {err}")
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching order metrics for store ID {store_id}: {e}")
    
    return order_metrics

def fetch_green_stores_aggregate_metrics(mysql_conn) -> Dict[str, Any]:
    """Fetches aggregate metrics for green status stores to use as benchmark.
    
    Args:
        mysql_conn: MySQL connection object.
        
    Returns:
        A dictionary containing aggregate metrics for high-activity stores.
    """
    green_metrics = {
        'avg_order_count': 0,
        'avg_revenue': 0.0,
        'avg_visits': 0,
        'avg_login_count': 0  # Added for login metrics
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("ERROR: MySQL connection is not available for fetch_green_stores_aggregate_metrics.")
        return green_metrics
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Build status filter
            status_filter = ','.join(VALID_ORDER_STATUSES)
            
            query_green_metrics = f"""
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       created_at,
                       id_order_status
                FROM store_orders
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                  AND id_order_status IN ({status_filter})
                UNION ALL
                SELECT id_order              AS id_store_order,
                       id_store,
                       date                  AS created_at,
                       id_status             AS id_order_status
                FROM orders
                WHERE date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                  AND id_status IN ({status_filter})
            )
            SELECT 
                AVG(total_orders) AS avg_order_count,
                AVG(total_revenue) AS avg_revenue,
                AVG(total_visits) AS avg_visits
            FROM (
                SELECT 
                    ao.id_store,
                    COUNT(DISTINCT ao.id_store_order) AS total_orders,
                    SUM(COALESCE(sop.price,0)*COALESCE(sop.qty,0)) AS total_revenue,
                    (SELECT COUNT(*) FROM store_visits sv WHERE sv.id_store = ao.id_store AND sv.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) AS total_visits
                FROM all_orders ao
                LEFT JOIN store_ordered_products sop ON ao.id_store_order = sop.id_store_order
                GROUP BY ao.id_store
            ) AS active_stores;
            """
            
            cursor.execute(query_green_metrics)
            result = cursor.fetchone()
            
            if result:
                green_metrics['avg_order_count'] = float(result['avg_order_count'] or 0)
                green_metrics['avg_revenue'] = float(result['avg_revenue'] or 0)
                green_metrics['avg_visits'] = float(result['avg_visits'] or 0)
            
            # Get average login count for green stores
            query_green_login = """
            SELECT 
                AVG(login_count) as avg_login_count
            FROM (
                SELECT 
                    su.id_store,
                    COUNT(la.id) as login_count
                FROM 
                    store_users su
                JOIN 
                    login_attempts la ON su.id_user = la.id_user
                WHERE 
                    la.time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY 
                    su.id_store
            ) as store_logins;
            """
            
            cursor.execute(query_green_login)
            login_result = cursor.fetchone()
            
            if login_result:
                green_metrics['avg_login_count'] = float(login_result['avg_login_count'] or 0)
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching green store metrics: {err}")
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching green store metrics: {e}")
    
    return green_metrics

def calculate_activity_metrics(store_data, login_data, visit_data, order_data, user_activity_data) -> Dict[str, Any]:
    """Processes and combines data to calculate overall activity metrics.
    
    Args:
        store_data: Basic store information
        login_data: Login activity metrics
        visit_data: Visit activity metrics
        order_data: Order activity metrics
        user_activity_data: User activity metrics based on store_users.updated_at
        
    Returns:
        A dictionary containing combined activity metrics.
    """
    activity_metrics = {
        # Login metrics (historical)
        'last_login_date': login_data.get('last_login_date'),
        'login_count_30d': login_data.get('login_count_30d', 0),
        'login_count_90d': login_data.get('login_count_90d', 0),
        'login_count_365d': login_data.get('login_count_365d', 0),
        
        # Visit metrics
        'last_visit_date': visit_data.get('last_visit_date'),
        'visit_count_30d': visit_data.get('visit_count_30d', 0),
        'visit_count_90d': visit_data.get('visit_count_90d', 0),
        'visit_count_365d': visit_data.get('visit_count_365d', 0),
        
        # Order metrics
        'last_order_date': order_data.get('last_order_date'),
        'order_count_30d': order_data.get('order_count_30d', 0),
        'order_count_90d': order_data.get('order_count_90d', 0),
        'order_count_365d': order_data.get('order_count_365d', 0),
        'revenue_30d': order_data.get('revenue_30d', 0.0),
        'revenue_90d': order_data.get('revenue_90d', 0.0),
        'revenue_365d': order_data.get('revenue_365d', 0.0),
        'avg_order_value': order_data.get('avg_order_value', 0.0),
        
        # User activity metrics (based on store_users.updated_at)
        'last_user_activity_date': user_activity_data.get('last_user_activity_date'),
        'days_since_last_user_activity': safe_infinity_value(user_activity_data.get('days_since_last_user_activity', 999999))
    }
    
    # Calculate days since last activity from all sources
    now = datetime.now(timezone.utc)
    
    # Find the most recent activity date among login, visit, order, and user activity
    last_activity_dates = [
        d for d in [
            activity_metrics['last_login_date'],
            activity_metrics['last_visit_date'],
            activity_metrics['last_order_date'],
            activity_metrics['last_user_activity_date']
        ] if d is not None
    ]
    
    if last_activity_dates:
        last_activity_date = max(last_activity_dates)
        # Convert to datetime with timezone if not already
        if isinstance(last_activity_date, datetime) and last_activity_date.tzinfo is None:
            last_activity_date = last_activity_date.replace(tzinfo=timezone.utc)
            
        activity_metrics['last_activity_date'] = last_activity_date
        activity_metrics['days_since_last_activity'] = (now - last_activity_date).days
    else:
        activity_metrics['last_activity_date'] = None
        activity_metrics['days_since_last_activity'] = None  # No activity recorded
    
    # Calculate traffic light status with adjusted priorities
    traffic_light, reason = determine_traffic_light(
        visit_count_30d=activity_metrics['visit_count_30d'],
        order_count_30d=activity_metrics['order_count_30d'],
        days_since_last_user_activity=activity_metrics['days_since_last_user_activity'],
        days_since_last_activity=activity_metrics['days_since_last_activity']
    )
    
    activity_metrics['traffic_light'] = traffic_light
    activity_metrics['traffic_light_reason'] = reason
    
    return activity_metrics

def determine_traffic_light(visit_count_30d, order_count_30d, days_since_last_user_activity, days_since_last_activity):
    """Determine traffic light status based on activity metrics, with emphasis on
    reliable metrics like visits, orders, and last user activity.
    
    Args:
        visit_count_30d: Number of visits in the last 30 days
        order_count_30d: Number of orders in the last 30 days
        days_since_last_user_activity: Days since last recorded user activity
        days_since_last_activity: Days since last recorded activity
        
    Returns:
        A tuple of (traffic_light_status, reason)
    """
    # Handle None/infinity values
    if days_since_last_user_activity is None:
        days_since_last_user_activity = 999999
    if days_since_last_activity is None:
        days_since_last_activity = 999999
    
    # Green: High activity (at least one of these reliable indicators)
    if (visit_count_30d >= VISIT_THRESHOLD_GREEN or  # Good visit volume
        order_count_30d >= ORDER_THRESHOLD_GREEN or  # Recent orders
        days_since_last_user_activity <= 30):        # User activity within 30 days
        return "green", "Store shows regular activity"
    
    # Yellow: Moderate activity
    elif (visit_count_30d >= VISIT_THRESHOLD_YELLOW or            # Some visits
          order_count_30d >= 1 or                                 # At least one order
          days_since_last_user_activity <= DAYS_INACTIVE_THRESHOLD_YELLOW):  # User activity within 60 days
        return "yellow", "Store shows occasional activity but could improve"
    
    # Red: Low or no activity
    else:
        return "red", "Store shows little to no recent activity"

def calculate_comparison_metrics(activity_metrics, green_metrics):
    """Calculate potential losses and comparison to green stores.
    
    Args:
        activity_metrics: The store's activity metrics
        green_metrics: Aggregate metrics for green status stores
        
    Returns:
        A dictionary containing comparison metrics.
    """
    comparison_metrics = {
        'avg_order_value': activity_metrics.get('avg_order_value', 0.0),
        'potential_revenue_loss': 0.0,
        'comparison_to_green_stores': {
            'visits_difference_pct': 0.0,
            'orders_difference_pct': 0.0,
            'revenue_difference_pct': 0.0,
            'login_difference_pct': 0.0  # Added login difference percentage
        },
        # New login-based revenue metrics
        'login_based_revenue_metrics': {
            'login_count_30d': activity_metrics.get('login_count_30d', 0),
            'revenue_per_login': 0.0,
            'login_deficit': 0.0,
            'login_based_potential_loss': 0.0
        }
    }
    
    # Only calculate potential loss for yellow and red stores
    if activity_metrics.get('traffic_light') in ['yellow', 'red']:
        # Calculate difference between green store metrics and this store
        if green_metrics.get('avg_order_count', 0) > 0:
            orders_diff = green_metrics.get('avg_order_count', 0) - activity_metrics.get('order_count_30d', 0)
            if orders_diff > 0:
                # Potential revenue loss = missed orders * store's average order value
                comparison_metrics['potential_revenue_loss'] = orders_diff * activity_metrics.get('avg_order_value', 0.0)
                
                # If the store has no orders, use the green store average instead
                if activity_metrics.get('avg_order_value', 0.0) == 0.0:
                    comparison_metrics['potential_revenue_loss'] = orders_diff * (green_metrics.get('avg_revenue', 0.0) / max(green_metrics.get('avg_order_count', 1), 1))
    
    # Calculate percentage differences (with division by zero protection)
    if green_metrics.get('avg_visits', 0) > 0:
        comparison_metrics['comparison_to_green_stores']['visits_difference_pct'] = (
            (green_metrics.get('avg_visits', 0) - activity_metrics.get('visit_count_30d', 0)) / 
            max(green_metrics.get('avg_visits', 1), 1) * 100
        )
    
    if green_metrics.get('avg_order_count', 0) > 0:
        comparison_metrics['comparison_to_green_stores']['orders_difference_pct'] = (
            (green_metrics.get('avg_order_count', 0) - activity_metrics.get('order_count_30d', 0)) / 
            max(green_metrics.get('avg_order_count', 1), 1) * 100
        )
    
    if green_metrics.get('avg_revenue', 0) > 0:
        comparison_metrics['comparison_to_green_stores']['revenue_difference_pct'] = (
            (green_metrics.get('avg_revenue', 0) - activity_metrics.get('revenue_30d', 0)) / 
            max(green_metrics.get('avg_revenue', 1), 1) * 100
        )
    
    # Calculate login-based metrics
    if green_metrics.get('avg_login_count', 0) > 0:
        # Calculate login difference percentage
        comparison_metrics['comparison_to_green_stores']['login_difference_pct'] = (
            (green_metrics.get('avg_login_count', 0) - activity_metrics.get('login_count_30d', 0)) / 
            max(green_metrics.get('avg_login_count', 1), 1) * 100
        )
        
        # Calculate login deficit
        login_deficit = green_metrics.get('avg_login_count', 0) - activity_metrics.get('login_count_30d', 0)
        comparison_metrics['login_based_revenue_metrics']['login_deficit'] = max(0, login_deficit)
        
        # Calculate revenue per login
        login_count_30d = activity_metrics.get('login_count_30d', 0)
        revenue_30d = activity_metrics.get('revenue_30d', 0.0)
        
        if login_count_30d > 0:
            # Calculate based on store's own data
            revenue_per_login = revenue_30d / login_count_30d
        else:
            # If no logins, estimate from green stores' data
            green_login_count = green_metrics.get('avg_login_count', 0)
            green_revenue = green_metrics.get('avg_revenue', 0.0)
            revenue_per_login = green_revenue / max(green_login_count, 1) if green_login_count > 0 else 0.0
        
        comparison_metrics['login_based_revenue_metrics']['revenue_per_login'] = revenue_per_login
        
        # Calculate potential revenue loss due to login deficit
        login_based_potential_loss = comparison_metrics['login_based_revenue_metrics']['login_deficit'] * revenue_per_login
        comparison_metrics['login_based_revenue_metrics']['login_based_potential_loss'] = login_based_potential_loss
    
    return comparison_metrics

def structure_mongo_document(store_data, activity_metrics, comparison_metrics):
    """Format data for MongoDB storage.
    
    Args:
        store_data: Basic store information
        activity_metrics: Processed activity metrics
        comparison_metrics: Comparison metrics against green stores
        
    Returns:
        A dictionary formatted for MongoDB document storage.
    """
    # Replace infinity values with None or large numbers
    days_since_activity = safe_infinity_value(activity_metrics.get('days_since_last_activity'))
    days_since_user_activity = safe_infinity_value(activity_metrics.get('days_since_last_user_activity'))
    
    mongo_doc = {
        "_id": str(store_data.get('id_store')),
        "name": store_data.get('name', 'Unknown Store'),
        "email": store_data.get('email'),
        "active": bool(store_data.get('active', False)),
        "activity_metrics": {
            # Login metrics
            "last_login_date": activity_metrics.get('last_login_date'),
            "login_count_30d": activity_metrics.get('login_count_30d', 0),
            "login_count_90d": activity_metrics.get('login_count_90d', 0),
            "login_count_365d": activity_metrics.get('login_count_365d', 0),
            
            # Visit metrics
            "last_visit_date": activity_metrics.get('last_visit_date'),
            "visit_count_30d": activity_metrics.get('visit_count_30d', 0),
            "visit_count_90d": activity_metrics.get('visit_count_90d', 0),
            "visit_count_365d": activity_metrics.get('visit_count_365d', 0),
            
            # Order metrics
            "last_order_date": activity_metrics.get('last_order_date'),
            "order_count_30d": activity_metrics.get('order_count_30d', 0),
            "order_count_90d": activity_metrics.get('order_count_90d', 0),
            "order_count_365d": activity_metrics.get('order_count_365d', 0),
            
            # Revenue metrics
            "revenue_30d": activity_metrics.get('revenue_30d', 0.0),
            "revenue_90d": activity_metrics.get('revenue_90d', 0.0),
            "revenue_365d": activity_metrics.get('revenue_365d', 0.0),
            
            # Other metrics
            "avg_order_value": activity_metrics.get('avg_order_value', 0.0),
            "last_activity_date": activity_metrics.get('last_activity_date'),
            "days_since_last_activity": days_since_activity,
            "traffic_light": activity_metrics.get('traffic_light', 'red'),
            "traffic_light_reason": activity_metrics.get('traffic_light_reason', 'No recent activity'),
            
            # User activity metrics
            "last_user_activity_date": activity_metrics.get('last_user_activity_date'),
            "days_since_last_user_activity": days_since_user_activity
        },
        "comparison_metrics": comparison_metrics,
        # Add login-based revenue metrics at top level for easier access
        "login_based_revenue_metrics": comparison_metrics.get('login_based_revenue_metrics', {
            'login_count_30d': 0,
            'revenue_per_login': 0.0,
            'login_deficit': 0.0,
            'login_based_potential_loss': 0.0
        }),
        "created_at": store_data.get('created_at'),
        "updated_at": store_data.get('updated_at'),
        "last_updated_script": datetime.now(timezone.utc).isoformat()
    }
    
    return mongo_doc

def update_mongo_store_activity_collection(store_documents):
    """Update the MongoDB collection with store activity metrics.
    
    Args:
        store_documents: List of store documents to update in MongoDB
        
    Returns:
        Number of stores updated/inserted
    """
    try:
        # Establish connection to MongoDB
        logger.info(f"Connecting to MongoDB: {MONGO_CONNECTION_STRING}")
        client = pymongo.MongoClient(MONGO_CONNECTION_STRING)
        
        # Access the analysis database
        db = client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"Connected to MongoDB database: {MONGO_DB_ANALYSIS_NAME}")
        
        # Get or create the target collection
        collection = db[TARGET_COLLECTION_NAME]
        logger.info(f"Using collection: {TARGET_COLLECTION_NAME}")
        
        # Create useful indexes if they don't exist
        try:
            collection.create_index([("activity_metrics.traffic_light", pymongo.ASCENDING)])
            collection.create_index([("name", pymongo.ASCENDING)])
            collection.create_index([("active", pymongo.ASCENDING)])
            collection.create_index([("activity_metrics.last_activity_date", pymongo.DESCENDING)])
            collection.create_index([("activity_metrics.days_since_last_activity", pymongo.ASCENDING)])
            logger.info("Successfully created/verified indexes")
        except Exception as index_err:
            logger.warning(f"Warning creating indexes: {str(index_err)}")
        
        # Bulk update operations
        bulk_operations = []
        for doc in store_documents:
            bulk_operations.append(
                pymongo.ReplaceOne(
                    {"_id": doc["_id"]},
                    doc,
                    upsert=True
                )
            )
        
        # Execute bulk write if we have operations
        if bulk_operations:
            result = collection.bulk_write(bulk_operations)
            updated_count = result.upserted_count + result.modified_count
            logger.info(f"MongoDB update complete. Updated {updated_count} documents.")
            
            # Close connection
            client.close()
            return updated_count
        
        # Close connection
        client.close()
        logger.info("No documents to update in MongoDB.")
        return 0
        
    except PyMongoError as e:
        logger.info(f"MongoDB error: {str(e)}")
        return 0
    except Exception as e:
        logger.info(f"Unexpected error updating MongoDB: {str(e)}")
        return 0

def fetch_store_user_activity(store_id: int, mysql_conn) -> Dict[str, Any]:
    """Fetches the most recent user activity for a store based on store_users.updated_at
    
    Args:
        store_id: The ID of the store to check
        mysql_conn: MySQL connection object
        
    Returns:
        Dictionary with last user activity timestamp and days since last activity
    """
    user_activity_metrics: Dict[str, Any] = {
        'last_user_activity_date': None,
        'days_since_last_user_activity': None  # Default to None instead of infinity
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.info(f"ERROR: MySQL connection is not available for fetch_store_user_activity (Store ID: {store_id}).")
        return user_activity_metrics
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query_user_activity = """
            SELECT 
                MAX(updated_at) as last_user_activity
            FROM 
                store_users
            WHERE 
                id_store = %s
            """
            
            cursor.execute(query_user_activity, (store_id,))
            result = cursor.fetchone()
            
            if result and result['last_user_activity']:
                # Store the timestamp
                user_activity_metrics['last_user_activity_date'] = result['last_user_activity']
                
                # Calculate days since last activity
                now = datetime.now(timezone.utc)
                last_activity = result['last_user_activity']
                
                # Ensure timezone awareness
                if isinstance(last_activity, datetime) and last_activity.tzinfo is None:
                    last_activity = last_activity.replace(tzinfo=timezone.utc)
                
                user_activity_metrics['days_since_last_user_activity'] = (now - last_activity).days
            
    except mysql.connector.Error as err:
        logger.info(f"ERROR: MySQL Error fetching user activity for store ID {store_id}: {err}")
    except Exception as e:
        logger.info(f"ERROR: Unexpected error fetching user activity for store ID {store_id}: {e}")
    
    return user_activity_metrics

# --- Main function to run the script ---
def main():
    """Main function to execute the script workflow."""
    mysql_conn = None
    try:
        logger.info("Script starting...")
        logger.info("Starting update_store_activity_metrics.py script with enhanced store user activity tracking")
        
        # Connect to MySQL
        logger.info(f"Connecting to MySQL at {MYSQL_HOST}:{MYSQL_PORT}")
        try:
            mysql_conn = mysql.connector.connect(
                host=MYSQL_HOST,
                user=MYSQL_USER,
                password=MYSQL_PASSWORD,
                database=MYSQL_DB_LANUBE,
                port=MYSQL_PORT,
                connect_timeout=10
            )
            logger.info("MySQL connection established")
        except mysql.connector.Error as err:
            logger.info(f"MySQL connection error: {err}. Exiting script.")
            sys.exit(1)
        except Exception as conn_e:
            logger.info(f"Unexpected MySQL connection error: {conn_e}. Exiting script.")
            sys.exit(1)

        # Step 1: Fetch all stores (both active and inactive)
        logger.info("Fetching basic data for all stores.")
        all_stores_data = fetch_all_stores_basic_data(mysql_conn)
        logger.info(f"Retrieved basic data for {len(all_stores_data)} stores.")

        if not all_stores_data:
            logger.info("No stores found. Exiting.")
            if mysql_conn and mysql_conn.is_connected(): 
                mysql_conn.close()
            sys.exit(0)

        # Step 2: Calculate benchmark metrics from green (high-activity) stores
        logger.info("Fetching benchmark metrics from high-activity stores.")
        green_metrics = fetch_green_stores_aggregate_metrics(mysql_conn)
        logger.info(f"Benchmark metrics: avg orders: {green_metrics.get('avg_order_count')}, avg revenue: {green_metrics.get('avg_revenue')}, avg visits: {green_metrics.get('avg_visits')}")
        
        # Step 3: Process each store
        mongo_documents = []
        stores_processed = 0
        green_count = 0
        yellow_count = 0
        red_count = 0
        
        # Track user activity metrics
        stores_with_user_activity = 0
        stores_with_recent_user_activity = 0  # Activity within 30 days
        
        for store_data in all_stores_data:
            stores_processed += 1
            store_id = store_data.get('id_store')
            
            if store_id is None:
                logger.info(f"Skipping store with missing ID: {store_data.get('name', 'Unknown')}")
                continue
                
            logger.info(f"Processing store {store_id} ({stores_processed}/{len(all_stores_data)}): {store_data.get('name')}")
            
            # Fetch activity data for this store
            login_data = fetch_store_login_activity(store_id, mysql_conn)
            visit_data = fetch_store_visit_metrics_batched(store_id, mysql_conn)  # Using batched version
            order_data = fetch_store_order_metrics_batched(store_id, mysql_conn)  # Using batched version
            
            # Fetch user activity data for this store
            user_activity_data = fetch_store_user_activity(store_id, mysql_conn)
            
            # Track user activity statistics
            if user_activity_data.get('last_user_activity_date'):
                stores_with_user_activity += 1
                days_since = user_activity_data.get('days_since_last_user_activity')
                if days_since is not None and days_since <= 30:
                    stores_with_recent_user_activity += 1
                    
                if stores_processed % 100 == 0 or stores_processed == 1:
                    logger.info(f"Store {store_id} user activity: {user_activity_data.get('last_user_activity_date')} ({days_since} days ago)")
            
            # Calculate activity metrics and traffic light status
            activity_metrics = calculate_activity_metrics(store_data, login_data, visit_data, order_data, user_activity_data)
            
            # Calculate comparison metrics against high-activity stores
            comparison_metrics = calculate_comparison_metrics(activity_metrics, green_metrics)
            
            # Structure data for MongoDB
            mongo_doc = structure_mongo_document(store_data, activity_metrics, comparison_metrics)
            mongo_documents.append(mongo_doc)
            
            # Count by traffic light status
            if activity_metrics.get('traffic_light') == 'green':
                green_count += 1
            elif activity_metrics.get('traffic_light') == 'yellow':
                yellow_count += 1
            else:  # 'red'
                red_count += 1
            
            # Log progress every 100 stores
            if stores_processed % 100 == 0:
                logger.info(f"Processed {stores_processed} stores...")
        
        logger.info(f"Finished processing all {stores_processed} stores.")
        logger.info(f"Traffic light distribution: Green: {green_count}, Yellow: {yellow_count}, Red: {red_count}")
        logger.info(f"User activity stats: {stores_with_user_activity} stores with recorded user activity")
        logger.info(f"User activity stats: {stores_with_recent_user_activity} stores with recent user activity (30 days)")
        
        # Step 4: Update MongoDB collection
        if mongo_documents:
            logger.info(f"Updating MongoDB store_activity_metrics collection with {len(mongo_documents)} documents.")
            updated_count = update_mongo_store_activity_collection(mongo_documents)
            logger.info(f"MongoDB update complete: {updated_count} stores updated.")
        else:
            logger.info("No store documents to update in MongoDB.")
        
        logger.info("Script completed successfully with enhanced user activity tracking")
        
    except Exception as e:
        logger.info(f"Fatal error in main execution: {e}")
        traceback.print_exc()
        sys.exit(1)
    finally:
        # Ensure connection is closed
        if mysql_conn and mysql_conn.is_connected():
            try: 
                mysql_conn.close()
                logger.info("MySQL connection closed")
            except: 
                pass

# Execute main function when script is run directly
if __name__ == "__main__":
    main()