{"CallerReference": "d-unit-frontend-distribution", "Comment": "Distribution for d-unit-frontend", "Origins": {"Quantity": 1, "Items": [{"Id": "S3-d-unit-frontend", "DomainName": "d-unit-frontend.s3.sa-east-1.amazonaws.com", "S3OriginConfig": {"OriginAccessIdentity": ""}}]}, "DefaultCacheBehavior": {"TargetOriginId": "S3-d-unit-frontend", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 3600, "MinTTL": 0, "MaxTTL": 86400, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}}}, "CacheBehaviors": {"Quantity": 2, "Items": [{"PathPattern": "*.js", "TargetOriginId": "S3-d-unit-frontend", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 31536000, "MinTTL": 31536000, "MaxTTL": 31536000, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}}}, {"PathPattern": "*.css", "TargetOriginId": "S3-d-unit-frontend", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 31536000, "MinTTL": 31536000, "MaxTTL": 31536000, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}}}]}, "CustomErrorResponses": {"Quantity": 1, "Items": [{"ErrorCode": 403, "ResponsePagePath": "/index.html", "ResponseCode": "200", "ErrorCachingMinTTL": 300}]}, "Enabled": true, "DefaultRootObject": "index.html", "PriceClass": "PriceClass_All", "ViewerCertificate": {"CloudFrontDefaultCertificate": true, "MinimumProtocolVersion": "TLSv1.2_2021"}}