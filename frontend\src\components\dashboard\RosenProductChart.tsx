import React, { useEffect, useRef } from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';
import * as d3 from 'd3';

interface ProductData {
  name: string;
  revenue: number;
  sales_units: number;
}

interface RosenProductChartProps {
  products: ProductData[];
  title?: string;
  height?: number;
}

export const RosenProductChart: React.FC<RosenProductChartProps> = ({
  products,
  title = 'Product Performance',
  height = 300
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current || !products.length) return;

    // Clear previous chart
    d3.select(chartRef.current).selectAll('*').remove();

    // Set up dimensions
    const margin = { top: 20, right: 20, bottom: 40, left: 60 };
    const width = chartRef.current.clientWidth - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(chartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add background grid
    svg.append('g')
      .attr('class', 'grid')
      .attr('opacity', 0.05)
      .call(d3.axisLeft(d3.scaleLinear().range([chartHeight, 0]))
        .tickSize(-width)
        .tickFormat(() => '')
      );

    // Set up scales
    const x = d3.scaleBand()
      .domain(products.map(d => d.name))
      .range([0, width])
      .padding(0.5);

    const y = d3.scaleLinear()
      .domain([0, d3.max(products, d => d.revenue) || 0])
      .range([chartHeight, 0])
      .nice();

    // Add X axis
    svg.append('g')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x))
      .selectAll('text')
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('fill', '#666');

    // Add Y axis
    svg.append('g')
      .call(d3.axisLeft(y)
        .ticks(5)
        .tickFormat(d => `${(d as number).toLocaleString()}`)
      )
      .selectAll('text')
      .style('font-size', '12px')
      .style('fill', '#666');

    // Add bars
    svg.selectAll('.bar')
      .data(products)
      .join('rect')
      .attr('class', 'bar')
      .attr('x', d => x(d.name) || 0)
      .attr('y', chartHeight)
      .attr('width', x.bandwidth())
      .attr('height', 0)
      .attr('fill', '#00A3FF')
      .attr('opacity', 0.9)
      .transition()
      .duration(800)
      .attr('y', d => y(d.revenue))
      .attr('height', d => chartHeight - y(d.revenue));

    // Add tooltips
    const tooltip = d3.select(chartRef.current)
      .append('div')
      .attr('class', 'tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background-color', 'white')
      .style('border', '1px solid rgba(224, 231, 255, 0.8)')
      .style('padding', '8px 12px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('box-shadow', '0 2px 4px rgba(0,0,0,0.1)');

    const showTooltip = (event: MouseEvent, d: ProductData) => {
      tooltip
        .style('visibility', 'visible')
        .html(`
          <strong>${d.name}</strong><br/>
          Revenue: ${d.revenue.toLocaleString()} UYU<br/>
          Sales: ${d.sales_units} units
        `)
        .style('left', `${event.offsetX + 10}px`)
        .style('top', `${event.offsetY - 10}px`);
    };

    const hideTooltip = () => {
      tooltip.style('visibility', 'hidden');
    };

    // Add tooltip events to bars
    svg.selectAll('.bar')
      .on('mouseover', function(event, d) { 
        d3.select(this)
          .attr('opacity', 1)
          .attr('fill', '#82b8ff');
        showTooltip(event, d as ProductData); 
      })
      .on('mousemove', function(event, d) { showTooltip(event, d as ProductData); })
      .on('mouseout', function() { 
        d3.select(this)
          .attr('opacity', 0.9)
          .attr('fill', '#00A3FF');
        hideTooltip(); 
      });

  }, [products, height]);

  return (
    <Card elevation={1} sx={{ mt: 2 }}>
      <CardContent>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        <Box 
          ref={chartRef} 
          sx={{ 
            height: height, 
            width: '100%',
            position: 'relative'
          }}
        />
      </CardContent>
    </Card>
  );
}; 