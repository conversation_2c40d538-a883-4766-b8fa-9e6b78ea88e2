from datetime import datetime
from typing import Optional, Union, List, Dict, Any
from pydantic import BaseModel, EmailStr, Field, field_validator, constr

class User(BaseModel):
    email: EmailStr
    id_store: Optional[str] = None
    name: Optional[str] = None
    password: Optional[str] = None
    pass_dunit: Optional[str] = None
    active: Optional[int] = 1
    role: Optional[str] = "user"
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    deleted_by: Optional[str] = None
    cod: Optional[str] = None
    cod_confirm: Optional[int] = 0
    orderId_domain: Optional[str] = None
    alternate_emails: Optional[List[str]] = None
    selected_meta_email: Optional[str] = None
    two_factor_enabled: Optional[bool] = False
    
    # Security-related fields
    login_attempts: Optional[int] = 0
    last_login_ip: Optional[str] = None
    last_login_at: Optional[str] = None
    account_locked_until: Optional[str] = None
    security_events: Optional[List[str]] = Field(default_factory=list)
    security_preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)
    failed_login_count: Optional[int] = 0
    last_failed_login: Optional[str] = None
    password_changed_at: Optional[str] = None
    security_questions: Optional[Dict[str, str]] = Field(default_factory=dict)
    trusted_devices: Optional[List[str]] = Field(default_factory=list)
    session_timeout_minutes: Optional[int] = 30

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
        
        @classmethod
        def get_properties(cls):
            return [prop for prop in cls.__annotations__.keys()]
        
    @classmethod
    def from_db(cls, db_data: dict) -> 'User':
        """
        Create a User instance from database data, handling type conversions.
        """
        # Create a copy of the data to avoid modifying the original
        data = db_data.copy()
        
        # Convert datetime fields to strings
        for date_field in ['created_at', 'updated_at', 'deleted_at', 'last_login_at', 'account_locked_until', 'last_failed_login', 'password_changed_at']:
            if isinstance(data.get(date_field), datetime):
                data[date_field] = data[date_field].strftime("%Y-%m-%d %H:%M:%S")
        
        # Convert numeric fields to strings where needed
        string_fields = ['id_store', 'cod', 'updated_by', 'created_by', 'deleted_by']
        for field in string_fields:
            if field in data and data[field] is not None:
                data[field] = str(data[field])
        
        # Map cod_confir to cod_confirm
        if 'cod_confir' in data:
            data['cod_confirm'] = int(data.pop('cod_confir'))
        
        # Ensure alternate_emails is a list
        if 'alternate_emails' in data and data['alternate_emails'] is not None:
            if isinstance(data['alternate_emails'], str):
                import ast
                try:
                    parsed = ast.literal_eval(data['alternate_emails'])
                    if isinstance(parsed, list):
                        data['alternate_emails'] = parsed
                    else:
                        data['alternate_emails'] = [data['alternate_emails']]
                except (SyntaxError, ValueError):
                    data['alternate_emails'] = [data['alternate_emails']]
            elif not isinstance(data['alternate_emails'], list):
                data['alternate_emails'] = [str(data['alternate_emails'])]
            data['alternate_emails'] = [str(email) for email in data['alternate_emails']]
        
        # Ensure security_events is a list
        if 'security_events' in data and data['security_events'] is not None:
            if not isinstance(data['security_events'], list):
                data['security_events'] = []
        
        # Ensure trusted_devices is a list
        if 'trusted_devices' in data and data['trusted_devices'] is not None:
            if not isinstance(data['trusted_devices'], list):
                data['trusted_devices'] = []
        
        # Ensure security_preferences is a dict
        if 'security_preferences' in data and data['security_preferences'] is not None:
            if not isinstance(data['security_preferences'], dict):
                data['security_preferences'] = {}
        
        # Ensure security_questions is a dict
        if 'security_questions' in data and data['security_questions'] is not None:
            if not isinstance(data['security_questions'], dict):
                data['security_questions'] = {}
        
        # Ensure selected_meta_email is a string if it exists
        if 'selected_meta_email' in data and data['selected_meta_email'] is not None:
            data['selected_meta_email'] = str(data['selected_meta_email'])
        
        # Add default role if not present
        data['role'] = data.get('role', 'user') 

        # Remove any fields that are not in the model definitions (User + UserInDB)
        # Combine fields from User and UserInDB for check
        base_model_fields = set(cls.__annotations__.keys())
        # Consider fields from UserInDB as well if applicable, though it inherits
        # If UserInDB adds fields not in User, list them explicitly or check inheritance chain
        allowed_fields = base_model_fields.union({'hashed_password'}) # Add fields from UserInDB if necessary
        data = {k: v for k, v in data.items() if k in allowed_fields or k == '_id'} # Keep _id if needed elsewhere
                
        # Ensure all remaining fields have correct basic types based on User model annotations
        for field, type_hint in cls.__annotations__.items():
            if field in data and data[field] is not None:
                field_type_str = str(type_hint)
                if 'Optional[str]' in field_type_str or field_type_str == 'str':
                    data[field] = str(data[field])
                elif 'Optional[int]' in field_type_str or field_type_str == 'int':
                    # Be careful with int conversion, handle potential errors
                    try:
                        data[field] = int(data[field])
                    except (ValueError, TypeError):
                        # Decide on handling: log error, set to default, keep original? 
                        # Setting to None if conversion fails, adjust as needed
                        # data[field] = None 
                        pass # Keep original for now if int conversion fails unexpectedly
                elif 'Optional[List[str]]' in field_type_str:
                     # Already handled specific list conversion for alternate_emails
                    pass
                elif 'Optional[bool]' in field_type_str or field_type_str == 'bool':
                     data[field] = bool(data[field])
                     
        # Filter data again strictly by User model fields before final instantiation
        user_model_fields = set(User.__annotations__.keys())
        final_data = {k: v for k, v in data.items() if k in user_model_fields}

        return cls(**final_data)
    
    def is_account_locked(self) -> bool:
        """Check if account is currently locked"""
        if not self.account_locked_until:
            return False
        
        try:
            lock_until = datetime.strptime(self.account_locked_until, "%Y-%m-%d %H:%M:%S")
            return datetime.now() < lock_until
        except (ValueError, TypeError):
            return False
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security-related information summary"""
        return {
            "login_attempts": self.login_attempts or 0,
            "failed_login_count": self.failed_login_count or 0,
            "last_login_ip": self.last_login_ip,
            "last_login_at": self.last_login_at,
            "account_locked": self.is_account_locked(),
            "two_factor_enabled": self.two_factor_enabled or False,
            "security_events_count": len(self.security_events or []),
            "trusted_devices_count": len(self.trusted_devices or []),
            "session_timeout": self.session_timeout_minutes or 30
        }

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None
    store_id: Optional[str] = None

class TwoFactorResponse(BaseModel):
    requires_2fa: bool
    message: str

# Define a Union type for login response
# This allows the login endpoint to return either a Token or a TwoFactorResponse
LoginResponse = Union[Token, TwoFactorResponse]

class UserRegistration(BaseModel):
    email: EmailStr
    password: str
    name: str
    store_name: str

class Enable2FARequest(BaseModel):
    email: EmailStr

class Verify2FARequest(BaseModel):
    email: EmailStr
    code: str
    action: str = "enable"  # Default to enable if not specified

class Verify2FALoginRequest(BaseModel):
    email: str
    code: str

class PasswordChangeRequest(BaseModel):
    old_password: str
    new_password: str
    confirm_password: str

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

# Password reset verification request
class PasswordResetVerifyRequest(BaseModel):
    email: EmailStr
    verification_code: constr(min_length=6, max_length=6, pattern=r"^\d{6}$")
    new_password: constr(min_length=8)
    confirm_password: str

class DUnitRegistration(BaseModel):
    email: EmailStr
    name: str
    store_name: str
    password_dunit: str

class VerifyDUnitRegistration(BaseModel):
    email: EmailStr
    verification_code: str

class CompanyProfileUpdate(BaseModel):
    company_name: Optional[str] = None
    business_type: Optional[str] = None
    contact_name: Optional[str] = None
    tax_id: Optional[str] = None
    dni: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None

class GoogleAuthRequest(BaseModel):
    credential: str

class MetaAuthRequest(BaseModel):
    credential: str

