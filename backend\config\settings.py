import os
import json
from functools import lru_cache
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
env = os.getenv("ENVIRONMENT", "development")
env_file = f".env.{env}"
load_dotenv(env_file)

class Settings(BaseSettings):
    # MongoDB settings
    MONGODB_CONNECTION: str = os.getenv("MONGODB_CONNECTION", "")
    MONGODB_MAIN_DB: str = os.getenv("MONGODB_MAIN_DB", "D-Unit")
    MONGODB_ANALYSIS_DB: str = os.getenv("MONGODB_ANALYSIS_DB", "D-Unit-AnalysisGPT")
    MONGODB_USERNAME: str = os.getenv("MONGODB_USERNAME", "")
    MONGODB_PASSWORD: str = os.getenv("MONGODB_PASSWORD", "")
    
    # Frontend URL setting (for redirects, links in emails, etc.)
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:5173")
    
    # MySQL settings (for Lanube)
    MYSQL_HOST: str = os.getenv("MYSQL_HOST", "localhost")
    MYSQL_USER: str = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DB_LANUBE: str = os.getenv("MYSQL_DB_LANUBE", "lanube")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    
    # Store filtering settings
    # List of store IDs that should be included even if inactive
    # First try to get from environment, otherwise use hardcoded list
    INACTIVE_STORE_EXCEPTIONS: list = json.loads(os.getenv("INACTIVE_STORE_EXCEPTIONS", "[]")) if os.getenv("INACTIVE_STORE_EXCEPTIONS") else [
        # High revenue stores (>300,000)
        10,    # Diego galey
        4,     # Elfiko
        349,   # Ingeniería y Servicios
        503,   # MoMa Shoes
        1018,  # Arte y Gaucho
        573,   # Tienda Online de ENEA
        757,   # mari oneill
        947,   # Belrrus
        470,   # BIKINIS DEEP
        627,   # Online Car
        854,   # Kala
        195,   # <EMAIL>
        263,   # Linduras
        1094,  # Angelina Brand
        687,   # Ignacia uy
        927,   # Sacramento Jeans
        591,   # VAFRUTA
        
        # Stores with recent activity (2024 onwards)
        904,   # La Cucha
        1107,  # PalomitayJoyeria
        1105,  # Aym
        889,   # Siena
        1092,  # Amapola
        749,   # Avopunta
        57,    # Palermo Design
        381,   # Love Sushi
        1067,  # Calzados Veloz
        769,   # Planeta Juguetes
        793,   # Second Chance by Angelina Martinez
        745,   # Flor de Venus
        725,   # Ramona Warrior
        1044,  # Espacio Jacinta
        1038,  # Bermudez
        841,   # Shine
        1011,  # SINS
        1003,  # Saturnia
        1004,  # Laura Joyas y Accesorios
        1131,  # puravida
        998,   # PADMA
        1023,  # Demkra
        995,   # Lore Garcia
        991,   # Simple Tienda & Taller
        1112,  # Donizetti
        1074,  # Big Bang Joyas
        463,   # Pecas Baby Accesorios
        983,   # donlucky
        981,   # Delicias_de_Algodon
        978,   # Melania Dress
        975,   # Estampas Locas
        1005,  # Alexis Ruiz Calzados
        972,   # Flores de Jazmin
        1163,  # Fabi Cocina
        1010,  # Alas
        926,   # Chic Woman
        973,   # MaR insumos
        146,   # IMA
        1122,  # Abelarda.uy
        924,   # Tienda Pippo's
    ]
    
    # Stores created in or after this year will be included even if inactive
    NEW_STORE_CUTOFF_YEAR: int = int(os.getenv("NEW_STORE_CUTOFF_YEAR", "2025"))
    
    # JWT settings
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "")
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # OpenAI settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_DEFAULT_MODEL: str = os.getenv("OPENAI_DEFAULT_MODEL", "gpt-4.1-mini")
    OPENAI_THINK_MODEL: str = os.getenv("OPENAI_THINK_MODEL", "o4-mini")
    OPENAI_DEEPSEARCH_MODEL: str = os.getenv("OPENAI_DEEPSEARCH_MODEL", "o4-mini")
    
    # Search API settings
    BRAVE_SEARCH_API_KEY: str = os.getenv("BRAVE_SEARCH_API_KEY", "")
    GOOGLE_CUSTOM_SEARCH_API_KEY: str = os.getenv("GOOGLE_CUSTOM_SEARCH_API_KEY", "")
    GOOGLE_CUSTOM_SEARCH_ENGINE_ID: str = os.getenv("GOOGLE_CUSTOM_SEARCH_ENGINE_ID", "")
    SEARCH_RESULT_COUNT: int = int(os.getenv("SEARCH_RESULT_COUNT", "5"))
    
    # Google Auth settings
    GOOGLE_CLIENT_ID: str = os.getenv("GOOGLE_CLIENT_ID", "")
    GOOGLE_CLIENT_SECRET: str = os.getenv("GOOGLE_CLIENT_SECRET", "")
    
    # Meta/Facebook API settings
    META_APP_ID: str = os.getenv("META_APP_ID", "")
    META_APP_SECRET: str = os.getenv("META_APP_SECRET", "")
    
    # Email settings
    EMAIL_HOST: str = "smtp.gmail.com"
    EMAIL_PORT: int = 587
    EMAIL_USERNAME: str = os.getenv("EMAIL_USERNAME", "")
    EMAIL_APP_PASSWORD: str = os.getenv("EMAIL_APP_PASSWORD", "")
    
    # Server settings
    PORT: str = os.getenv("PORT", "8000")
    HOST: str = os.getenv("HOST", "127.0.0.1")
    
    # Meta sync settings
    META_SYNC_INTERVAL_MINUTES: int = int(os.getenv("META_SYNC_INTERVAL_MINUTES", "30"))
    META_SYNC_CACHE_MAX_AGE_MINUTES: int = int(os.getenv("META_SYNC_CACHE_MAX_AGE_MINUTES", "60"))
    META_SYNC_ENABLED: bool = os.getenv("META_SYNC_ENABLED", "True").lower() in ("true", "1", "t")
    
    # Environment settings
    ENVIRONMENT: str = env
    
    # Meta API settings
    FACEBOOK_APP_ID: str = os.getenv("FACEBOOK_APP_ID", "")
    FACEBOOK_APP_SECRET: str = os.getenv("FACEBOOK_APP_SECRET", "")
    FACEBOOK_API_VERSION: str = os.getenv("FACEBOOK_API_VERSION", "v23.0")
    
    # CORS settings - hardcoded list for safety
    CORS_ORIGINS: list = json.loads(os.getenv("CORS_ORIGINS", '["http://localhost:5173", "http://127.0.0.1:5173", "http://localhost:3000", "http://127.0.0.1:3000", "https://localhost:5173", "https://127.0.0.1:5173", "https://localhost:3000", "https://127.0.0.1:3000"]'))
    
    # Security Gateway settings
    SECURITY_ENABLED: bool = os.getenv("SECURITY_ENABLED", "True").lower() in ("true", "1", "t")
    RATE_LIMITING_ENABLED: bool = os.getenv("RATE_LIMITING_ENABLED", "True").lower() in ("true", "1", "t")
    VALIDATION_ENABLED: bool = os.getenv("VALIDATION_ENABLED", "True").lower() in ("true", "1", "t")
    COST_CONTROL_ENABLED: bool = os.getenv("COST_CONTROL_ENABLED", "True").lower() in ("true", "1", "t")
    SECURITY_HEADERS_ENABLED: bool = os.getenv("SECURITY_HEADERS_ENABLED", "True").lower() in ("true", "1", "t")
    MONITORING_ENABLED: bool = os.getenv("MONITORING_ENABLED", "True").lower() in ("true", "1", "t")
    THREAT_DETECTION_ENABLED: bool = os.getenv("THREAT_DETECTION_ENABLED", "True").lower() in ("true", "1", "t")
    
    # Redis configuration for distributed rate limiting
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_ENABLED: bool = os.getenv("REDIS_ENABLED", "False").lower() in ("true", "1", "t")
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "100"))
    REDIS_TIMEOUT: int = int(os.getenv("REDIS_TIMEOUT", "5"))
    
    # Security monitoring settings
    SECURITY_LOG_LEVEL: str = os.getenv("SECURITY_LOG_LEVEL", "INFO")
    SECURITY_EVENT_RETENTION_DAYS: int = int(os.getenv("SECURITY_EVENT_RETENTION_DAYS", "90"))
    SECURITY_ALERT_EMAIL: str = os.getenv("SECURITY_ALERT_EMAIL", "")
    SECURITY_WEBHOOK_URL: str = os.getenv("SECURITY_WEBHOOK_URL", "")
    
    # Threat detection parameters
    BRUTE_FORCE_THRESHOLD: int = int(os.getenv("BRUTE_FORCE_THRESHOLD", "15"))
    BRUTE_FORCE_WINDOW_MINUTES: int = int(os.getenv("BRUTE_FORCE_WINDOW_MINUTES", "15"))
    SUSPICIOUS_ACTIVITY_THRESHOLD: int = int(os.getenv("SUSPICIOUS_ACTIVITY_THRESHOLD", "10"))
    BOT_DETECTION_ENABLED: bool = os.getenv("BOT_DETECTION_ENABLED", "True").lower() in ("true", "1", "t")
    
    # Cost control settings
    DEFAULT_DAILY_COST_LIMIT: float = float(os.getenv("DEFAULT_DAILY_COST_LIMIT", "50.0"))
    DEFAULT_MONTHLY_COST_LIMIT: float = float(os.getenv("DEFAULT_MONTHLY_COST_LIMIT", "1000.0"))
    EMERGENCY_STOP_THRESHOLD: float = float(os.getenv("EMERGENCY_STOP_THRESHOLD", "0.95"))
    COST_ALERT_THRESHOLD: float = float(os.getenv("COST_ALERT_THRESHOLD", "0.8"))
    
    # Security encryption settings
    SECURITY_ENCRYPTION_KEY: str = os.getenv("SECURITY_ENCRYPTION_KEY", "")
    CSRF_SECRET_KEY: str = os.getenv("CSRF_SECRET_KEY", "")
    SECURITY_TOKEN_EXPIRY_MINUTES: int = int(os.getenv("SECURITY_TOKEN_EXPIRY_MINUTES", "60"))
    
    # CSRF Enhancement Settings
    CSRF_ROTATION_ENABLED: bool = os.getenv("CSRF_ROTATION_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_DOUBLE_SUBMIT_ENABLED: bool = os.getenv("CSRF_DOUBLE_SUBMIT_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_MONITORING_ENABLED: bool = os.getenv("CSRF_MONITORING_ENABLED", "True").lower() in ("true", "1", "t")
    
    # CSRF Token Settings
    CSRF_TOKEN_TTL_MINUTES: int = int(os.getenv("CSRF_TOKEN_TTL_MINUTES", "30"))
    CSRF_COOKIE_SECURE: bool = os.getenv("CSRF_COOKIE_SECURE", "True").lower() in ("true", "1", "t")
    CSRF_COOKIE_HTTPONLY: bool = os.getenv("CSRF_COOKIE_HTTPONLY", "True").lower() in ("true", "1", "t")
    CSRF_COOKIE_SAMESITE: str = os.getenv("CSRF_COOKIE_SAMESITE", "strict")
    
    # CSRF Attack Detection Settings
    CSRF_VIOLATION_THRESHOLD: int = int(os.getenv("CSRF_VIOLATION_THRESHOLD", "10"))
    CSRF_IP_THRESHOLD: int = int(os.getenv("CSRF_IP_THRESHOLD", "5"))
    CSRF_USER_THRESHOLD: int = int(os.getenv("CSRF_USER_THRESHOLD", "3"))
    CSRF_TIME_WINDOW_MINUTES: int = int(os.getenv("CSRF_TIME_WINDOW_MINUTES", "15"))
    
    # CSRF Attack Pattern Detection
    CSRF_BRUTE_FORCE_THRESHOLD: int = int(os.getenv("CSRF_BRUTE_FORCE_THRESHOLD", "10"))
    CSRF_DISTRIBUTED_ATTACK_IPS_THRESHOLD: int = int(os.getenv("CSRF_DISTRIBUTED_ATTACK_IPS_THRESHOLD", "3"))
    CSRF_SYSTEMATIC_ATTACK_THRESHOLD: int = int(os.getenv("CSRF_SYSTEMATIC_ATTACK_THRESHOLD", "5"))
    
    # CSRF Adaptive Thresholds
    CSRF_ADAPTIVE_THRESHOLDS_ENABLED: bool = os.getenv("CSRF_ADAPTIVE_THRESHOLDS_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_ADAPTIVE_THRESHOLD_FACTOR: float = float(os.getenv("CSRF_ADAPTIVE_THRESHOLD_FACTOR", "0.5"))
    CSRF_ADAPTIVE_LEARNING_WINDOW_HOURS: int = int(os.getenv("CSRF_ADAPTIVE_LEARNING_WINDOW_HOURS", "24"))
    
    # CSRF Response Actions
    CSRF_AUTO_BLOCK_ENABLED: bool = os.getenv("CSRF_AUTO_BLOCK_ENABLED", "False").lower() in ("true", "1", "t")
    CSRF_AUTO_BLOCK_DURATION_MINUTES: int = int(os.getenv("CSRF_AUTO_BLOCK_DURATION_MINUTES", "60"))
    CSRF_ALERT_THRESHOLD: str = os.getenv("CSRF_ALERT_THRESHOLD", "high")
    
    # CSRF Monitoring Settings
    CSRF_REAL_TIME_ANALYSIS_ENABLED: bool = os.getenv("CSRF_REAL_TIME_ANALYSIS_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_GEOGRAPHICAL_TRACKING_ENABLED: bool = os.getenv("CSRF_GEOGRAPHICAL_TRACKING_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_RETENTION_DAYS: int = int(os.getenv("CSRF_RETENTION_DAYS", "90"))
    
    # CSRF Notification Settings
    CSRF_EMAIL_ALERTS_ENABLED: bool = os.getenv("CSRF_EMAIL_ALERTS_ENABLED", "True").lower() in ("true", "1", "t")
    CSRF_WEBHOOK_ALERTS_ENABLED: bool = os.getenv("CSRF_WEBHOOK_ALERTS_ENABLED", "False").lower() in ("true", "1", "t")
    CSRF_WEBHOOK_URL: str = os.getenv("CSRF_WEBHOOK_URL", "")
    CSRF_ALERT_RECIPIENTS: list = json.loads(os.getenv("CSRF_ALERT_RECIPIENTS", "[]"))
    
    # CSRF Sensitive Operations (JSON list of endpoints that trigger rotation)
    CSRF_SENSITIVE_OPERATIONS: list = json.loads(os.getenv("CSRF_SENSITIVE_OPERATIONS", '[]')) if os.getenv("CSRF_SENSITIVE_OPERATIONS") else [
        "/api/auth/logout",
        "/api/auth/change-password",
        "/api/auth/reset-password",
        "/api/user/delete",
        "/api/user/update-profile",
        "/api/store/settings",
        "/api/admin",
        "/api/security"
    ]
    
    # Rate limiting defaults
    DEFAULT_RATE_LIMIT_PER_MINUTE: int = int(os.getenv("DEFAULT_RATE_LIMIT_PER_MINUTE", "60"))
    DEFAULT_RATE_LIMIT_PER_HOUR: int = int(os.getenv("DEFAULT_RATE_LIMIT_PER_HOUR", "1000"))
    DEFAULT_RATE_LIMIT_PER_DAY: int = int(os.getenv("DEFAULT_RATE_LIMIT_PER_DAY", "10000"))

    # Toggle to disable CSRF protection globally (use with caution)
    DISABLE_CSRF: bool = os.getenv("DISABLE_CSRF", "False").lower() in ("true", "1", "t")

    class Config:
        env_file = env_file
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields in the environment

@lru_cache()
def get_settings():
    return Settings()