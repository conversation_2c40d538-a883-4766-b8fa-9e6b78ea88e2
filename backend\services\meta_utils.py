"""
Meta Utilities for Shared Functionality

This module contains shared utility functions and wrappers for Meta-related services
to prevent circular dependencies between meta.py and meta_permissions.py.
"""

import logging
import asyncio
import aiohttp
from typing import Optional, Dict, List, Any
from datetime import datetime, timezone, timedelta
from config.database import db_analysis
from models.meta import MetaChatContext
from config.settings import get_settings

# Configure logging
logger = logging.getLogger(__name__)

settings = get_settings()
GRAPH_API_URL = "https://graph.facebook.com/v22.0"

_meta_context_cache = {}

async def get_cached_meta_context_wrapper(store_id: str, max_age_minutes: Optional[int] = None) -> Optional[MetaChatContext]:
    """
    Wrapper function for get_cached_meta_context to break circular dependency.
    
    This function provides a safe way to access cached Meta context 
    without creating circular imports between meta.py and meta_permissions.py.
    
    Args:
        store_id: The ID of the store to get context for
        max_age_minutes: Maximum age of cache in minutes before refreshing
        
    Returns:
        MetaChatContext object or None if no valid cache exists
    """
    # Get max age from settings if not specified
    if max_age_minutes is None:
        max_age_minutes = settings.META_SYNC_CACHE_MAX_AGE_MINUTES
    
    cache_key = store_id
    current_time = datetime.now(timezone.utc)

    if cache_key in _meta_context_cache:
        cached_data, timestamp = _meta_context_cache[cache_key]
        cache_age = current_time - timestamp
        if cache_age < timedelta(minutes=max_age_minutes):
            logger.info(f"Using cached Meta context for store {store_id} (age: {cache_age})")
            return cached_data

    # If not in cache or expired, fetch from DB
    try:
        db_context = await db_analysis["meta_chat_context"].find_one({"store_id": store_id})
        if db_context:
            # Ensure 'pages' is a list of dictionaries
            if 'pages' in db_context and isinstance(db_context['pages'], list):
                # Further validation can be added here if needed
                pass
            else:
                # Handle case where 'pages' is missing or not a list
                db_context['pages'] = []
                logger.warning(f"Pages data missing or invalid for store {store_id}, defaulting to empty list.")

            context = MetaChatContext(**db_context)
            _meta_context_cache[cache_key] = (context, current_time)
            logger.info(f"Fetched Meta context from DB and cached for store {store_id}")
            return context
        else:
            logger.warning(f"No Meta context found in DB for store {store_id}")
            return None
    except Exception as e:
        logger.error(f"Error fetching Meta context from DB for store {store_id}: {e}")
        return None

# New function: get_user_pages_from_token
async def get_user_pages_from_token(access_token: str) -> List[Dict[str, Any]]:
    """Fetches user's pages directly from Meta API using an access token."""
    pages = []
    try:
        async with aiohttp.ClientSession() as session:
            # Fetch pages connected to the user token
            async with session.get(
                f"{GRAPH_API_URL}/me/accounts",
                params={
                    "access_token": access_token,
                    "fields": "id,name,access_token,category,platform" # Adjust fields as needed
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    pages = data.get("data", [])
                    logger.info(f"Successfully fetched {len(pages)} pages for token.")
                else:
                    error_text = await response.text()
                    logger.error(f"Error fetching user pages from Meta API: {response.status} - {error_text}")
                    # Optionally raise an exception or return empty list based on desired error handling
    except Exception as e:
        logger.error(f"Exception fetching user pages from Meta API: {str(e)}")
        # Optionally raise or return empty
    return pages

# New function: get_page_data_from_token
async def get_page_data_from_token(page_id: str, access_token: str, fields: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
    """Fetches specific page data from Meta API using page ID and token."""
    if fields is None:
        fields = ["id", "name", "category", "followers_count"] # Default fields needed by process_page_for_chat

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{GRAPH_API_URL}/{page_id}",
                params={
                    "access_token": access_token,
                    "fields": ",".join(fields)
                }
            ) as response:
                if response.status == 200:
                    page_data = await response.json()
                    logger.info(f"Successfully fetched data for page {page_id}.")
                    return page_data
                else:
                    error_text = await response.text()
                    logger.error(f"Error fetching page data from Meta API for {page_id}: {response.status} - {error_text}")
                    return None
    except Exception as e:
        logger.error(f"Exception fetching page data for {page_id} from Meta API: {str(e)}")
        return None 