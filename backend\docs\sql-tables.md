# SQL Table Analysis Summary (lanube Database)

This document summarizes the inferred structure, purpose, and relationships of tables in the `lanube` database based on examining the first row of data for each.

**Disclaimer:** This analysis is based on limited data (`SELECT * ... LIMIT 1`) and inferred relationships from column names. It does not represent a definitive database schema documentation.

**Last Verified:** June 10, 2025  
**Total Tables:** 112

---

## 1. `attributes`

*   **Columns:** `id_attribute`, `name`, `description`, `id_store`, `active`, `created_at`, `updated_at`
*   **Row Count:** 5,082
*   **Inferred Purpose:** Defines product attributes (like "Color", "Size").
*   **Used by Scripts:** Yes (`update_product_variations.py`)
*   **Potential Relationships:** May be linked to `stores` via `id_store` (though the sample value was null). Likely linked to `attribute_variations`.

---

## 2. `attribute_variations`

*   **Columns:** `id_attribute_variation`, `id_attribute`, `value`, `active`, `created_at`, `updated_at`
*   **Row Count:** 11,342
*   **Inferred Purpose:** Defines the specific values for an attribute (e.g., "Rojo" for "Color").
*   **Used by Scripts:** Yes (`update_product_variations.py`)
*   **Potential Relationships:** Linked to `attributes` via `id_attribute`. Likely used in conjunction with products/product variations (potentially linking to `product_attributes` via `id_attribute_variation`).

---

## 3. `categories`

*   **Columns:** `id_category`, `name`, `description`, `active`, `updated_at`, `created_at`, `deleted_at`, `update_by`, `create_by`, `delete_by`
*   **Row Count:** 5
*   **Inferred Purpose:** Defines general product categories.
*   **Used by Scripts:** Yes (`update_product_categories.py`)
*   **Potential Relationships:** Likely linked to `products` (perhaps via a joining table like `products_subcategories` or `store_categories`). Linked to by `subcategories` via `id_category`.

---

## 4. `ciudad` (City)

*   **Columns:** `CiudadID`, `CiudadNombre`, `PaisCodigo`, `CiudadDistrito`, `CiudadPoblacion`
*   **Row Count:** 4,254
*   **Inferred Purpose:** Stores city information.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to countries via `PaisCodigo` (likely referencing the `countries` table's `code` column). Used for addresses (customers, stores).

---

## 5. `countries`

*   **Columns:** `id_country`, `code`, `name`, `capital`, `province`, `area`, `population`
*   **Row Count:** 195
*   **Inferred Purpose:** Stores country information.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `ciudad` via `code` (`PaisCodigo` in `ciudad`). Used for addresses. Possibly linked to `prefixes`.

---

## 6. `favorites_products`

*   **Columns:** `id_favorite_product`, `id_store_customer`, `id_product`, `active`, `created_at`, `updated_at`, `delete_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 554
*   **Inferred Purpose:** Junction table linking customers to products they have marked as favorites.
*   **Used by Scripts:** Yes (`update_product_details.py`)
*   **Potential Relationships:** Linked to `store_customers` via `id_store_customer`. Linked to `products` via `id_product`.

---

## 7. `headings`

*   **Columns:** `id_heading`, `name`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 118
*   **Inferred Purpose:** Seems to define broad business categories or sections (like "Informática"). Its specific use isn't immediately clear without more context.
*   **Used by Scripts:** Yes (`update_active_stores.py`)
*   **Potential Relationships:** Uncertain from this single row. Might link to `stores` via `id_heading`.

---

## 8. `invoices`

*   **Columns:** `id_invoice`, `id_store`, `date_emission`, `date_started`, `date_finished`, `expiration_date`, `amount`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1,019
*   **Inferred Purpose:** Stores invoice information, likely for store services or subscriptions.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to by `payments` via `id_invoice`.

---

## 9. `orders`

*   **Columns:** `id_order`, `id_store`, `id_payment_method`, `amount`, `date`, `id_status`, `payment_active`, `response_paypal`, `active`, `created_at`, `updated_at`
*   **Row Count:** 1,246
*   **Inferred Purpose:** Contains main order information. *Note: This seems distinct from `store_orders` which was mentioned in the README scripts and seems more heavily used.*
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to `payment_methods` via `id_payment_method`. Linked to an order status table (likely `orders_statuses`) via `id_status`. Linked to by `order_plans` via `id_order`. May link to customers and order items (details potentially in other tables).

---

## 10. `orders_statuses`

*   **Columns:** `id_order_status`, `name`, `description`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `deleted_by`
*   **Row Count:** 7
*   **Inferred Purpose:** Defines the different statuses an order can have (e.g., "Nuevo", "Pago", "Completo").
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to by the `orders` table (via `id_status`) and likely the `store_orders` table (via `id_order_status`).

---

## 11. `order_plans`

*   **Columns:** `id_order_plan`, `id_order`, `id_plan`, `qty`, `price`, `active`, `type_currency`, `created_at`, `updated_at`
*   **Row Count:** 1,237
*   **Inferred Purpose:** Appears to link orders to specific subscription plans or potentially order line items related to plans.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `orders` via `id_order`. Linked to a `plans` table (not in the original list) via `id_plan`. May link to `type_currencys` via `type_currency`.

---

## 12. `payments`

*   **Columns:** `id_payment`, `id_invoice`, `id_payment_method`, `id_store`, `date`, `amount`, `response_data` (JSON), `status`, `created_at`, `updated_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 935
*   **Inferred Purpose:** Records payment transactions, likely related to invoices. Contains detailed response data from payment processors (like Stripe in the sample).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `invoices` via `id_invoice`. Linked to `payment_methods` via `id_payment_method`. Linked to `stores` via `id_store`.

---

## 13. `payment_methods`

*   **Columns:** `id_payment_method`, `name`, `image_path`, `is_credentials`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `update_by`, `deleted_by`
*   **Row Count:** 13
*   **Inferred Purpose:** Defines available payment methods (e.g., "Mercadopago", "Stripe").
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to by `orders` and `payments` tables via `id_payment_method`. Also likely used by `store_orders`. Linked to by `payment_method_stores`.

---

## 14. `payment_method_stores`

*   **Columns:** `id_payment_method_store`, `id_payment_method`, `id_store`, `client_id`, `secret_key`, `email_paypal`, `name_banca`, `number_banca`, `token_public`, `token_private`, `login_dlocal`, `trans_key_dlocal`, `code_qr`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** Not sampled
*   **Inferred Purpose:** Configures specific payment methods for particular stores, including credentials, API keys, and payment processor-specific settings.
*   **Used by Scripts:** Unknown
*   **Potential Relationships:** Linked to `payment_methods` via `id_payment_method`. Linked to `stores` via `id_store`.

---

## 15. `prefixes`

*   **Columns:** `prefixe_id`, `name`, `nom`, `iso2`, `iso3`, `phone_code`, `code`
*   **Row Count:** 248
*   **Inferred Purpose:** Stores country-related information, specifically phone codes and ISO codes. Seems somewhat redundant with the `countries` table but focuses on phone prefixes.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Could potentially link to `countries` via ISO codes. Used for phone number formatting (e.g., in `stores`). Referenced by `stores` via `prefixe_id`.

---

## 16. `products`

*   **Columns:** `id_product`, `name`, `code`, `description`, `image`, `featured`, `offer`, `id_category` (null in sample), `price`, `price_offer`, `variations` (boolean), `stock` (null in sample), `id_store` (null in sample), `height`, `width`, `depth`, `weight`, `guide`, `stock_min`, `stock_critical`, `is_schedule`, `active`, `status`, `order`, `category_order`, `subcategory_order`, `sub_subcategory_order`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`, `mercadolibre_status`, `link_mercadolibre`, `item_id`, `share`, `lapso`, `type` (many columns)
*   **Row Count:** 32,046
*   **Inferred Purpose:** The main table for product information. Contains details, pricing, stock (though nullable), dimensions, status, etc.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`, `update_meta_sales_correlation.py`, `update_product_categories.py`, `update_product_details.py`, `update_product_variations.py`)
*   **Potential Relationships:** Linked to by `favorites_products`, `related_products` (twice), `store_ordered_products`, `product_variations`, `product_attributes`. Links to `categories` (via `id_category` or junction table `products_subcategories`). May link to `stores` (via `id_store`).

---

## 17. `products_subcategories`

*   **Columns:** `id_product_subcategory`, `id_product`, `id_category` (null in sample), `id_subcategory`, `store_sub_subcategorie_id` (null in sample), `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 88,456
*   **Inferred Purpose:** Junction table linking products to subcategories (and potentially main categories and store-specific sub-subcategories).
*   **Used by Scripts:** Yes (`update_product_categories.py`)
*   **Potential Relationships:** Linked to `products` via `id_product`. Linked to `subcategories` via `id_subcategory`. Potentially links to `categories` via `id_category` and `store_sub_subcategories` via `store_sub_subcategorie_id`.

---

## 18. `ratings_products`

*   **Columns:** `id_ratings_products`, `ip_costumer`, `store_id`, `ratings`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 2,108
*   **Inferred Purpose:** Records product ratings (potentially store-level based on sample, but README suggests product-level usage aggregated by scripts). Identified by customer IP and store ID.
*   **Used by Scripts:** Yes (`update_product_details.py`)
*   **Potential Relationships:** Linked to `stores` via `store_id`.

---

## 19. `related_products`

*   **Columns:** `id_related_product`, `id_product`, `id_secondary_product`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 7
*   **Inferred Purpose:** Defines relationships between products (e.g., "upsell", "cross-sell"). Links a primary product (`id_product`) to a related secondary product (`id_secondary_product`).
*   **Used by Scripts:** Yes (`update_product_details.py`)
*   **Potential Relationships:** Linked to `products` twice (via `id_product` and `id_secondary_product`).

---

## 20. `shipping_code_prices`

*   **Columns:** `shipping_code_price`, `store_id`, `shipping_id`, `postal_code`, `price`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 6,142
*   **Inferred Purpose:** Defines shipping prices based on postal codes for specific shipping methods within a store.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `shipping_methods` via `shipping_id`.

---

## 21. `shipping_methods`

*   **Columns:** `id_shipping`, `name`, `price` (0 in sample, maybe base price?), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 14
*   **Inferred Purpose:** Defines available shipping carriers or methods (e.g., "OCA").
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to by `shipping_code_prices` (via `shipping_id`), `shipping_method_stores` (via `id_shipping`), and `store_orders` (via `id_shipping`).

---

## 22. `shipping_method_stores`

*   **Columns:** `id_shipping_store`, `id_shipping`, `id_store`, `client_id`, `secret_key`, `whatsapp_arg`, `code_postal`, `street`, `city`, `province_code`, `country_code`, `active`, ... (many credential/config-related columns for specific shipping providers like FedEx, Ahiva, NubeFlash)
*   **Row Count:** 1,700
*   **Inferred Purpose:** Configures specific shipping methods (`id_shipping`) for particular stores (`id_store`), including credentials, API keys, and pickup addresses.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `shipping_methods` via `id_shipping`. Linked to `stores` via `id_store`.

---

## 23. `stores`

*   **Columns:** `id_store`, `name`, `email`, `password` (hashed), `address`, `country` (null), `telephone`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `url_store`, `domain`, `domain_cert`, `sub_domain`, `sub_domain_cert`, `cod`, `province`, `type_currency_id`, `status`, `id_heading`, `name_currency`, `symbol`, `code_currency`, `rut`, `tax`, `corporate_name`, `city`, `date_birth`, `gender`, `prefixe_id`, `date_plan_notify`, `metadata`, `update_sub_domain`, `update_domain`, `language`, `default_language`, ...
*   **Row Count:** 1,287
*   **Inferred Purpose:** The main table for store information, including login, contact details, domain settings, status, and potentially billing/plan details.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`, `update_meta_sales_correlation.py`, `update_product_categories.py`, `update_product_details.py`, `update_product_variations.py`, `update_active_stores.py`, `update_store_activity_metrics.py`, `update_store_users.py`)
*   **Potential Relationships:** Linked to almost every other store-specific table (e.g., `store_orders`, `store_customers`, `store_configurations`, `products` (nullable `id_store`), `invoices`, `payments`, `shipping_method_stores`, `store_categories`, `store_subcategories`, `store_sub_subcategories`, `store_users`, `ratings_products`, `store_cupons`, `store_visits`) via `id_store`. May link to `type_currencys` via `type_currency_id`. Links to `headings` via `id_heading`. Links to `prefixes` via `prefixe_id`.

---

## 24. `store_categories`

*   **Columns:** `id_category` (confusing name, likely primary key for this table, not a foreign key), `name`, `image`, `name_image`, `id_store`, `ml_id` (MercadoLibre ID?), `order`, `link`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 4,037
*   **Inferred Purpose:** Defines categories *specific* to a store, distinct from the global `categories` table.
*   **Used by Scripts:** Yes (`update_product_categories.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to by `store_subcategories` via `id_category`. Used to categorize products within a store, likely linking to `products`.

---

## 25. `store_configurations`

*   **Columns:** `id_configuration`, `id_store`, `whatsapp`, `facebook`, `instagram`, `fiscal_key`, `pixel_id`, `keywords`, `website_description`, `schedule`, `bookings`, `order_preparation`, `analytics_google`, `image_qr`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `tiktok`, `x_twitter`, `pinterest`, `youtube`
*   **Row Count:** 1,260
*   **Inferred Purpose:** Stores configuration settings for a specific store, including social media links, tracking IDs, SEO info, and business hours.
*   **Used by Scripts:** Yes (`update_active_stores.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 26. `store_cupons` (Coupons)

*   **Columns:** `id_cupon`, `id_store`, `code`, `name`, `amount`, `status`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 365
*   **Inferred Purpose:** Defines discount coupons for a specific store.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Used in `store_orders` (via `id_cupon`).

---

## 27. `store_customers`

*   **Columns:** `id_store_customer`, `id_store`, `name`, `last_name`, `email`, `address`, `telephone`, `dni` (Document ID), `pais`, `provincia`, `ciudad`, `password` (nullable), `status`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `pack_id`, `uber_telephono`, `uber_direccion`, `uber_description`
*   **Row Count:** 13,705
*   **Inferred Purpose:** Stores customer information specific to a store.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to by `store_orders` and `favorites_products` via `id_store_customer`.

---

## 28. `store_ordered_products`

*   **Columns:** `id_store_order_product`, `id_store_order`, `id_product`, `price`, `qty`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `shipping`, `order_mercadolibre`
*   **Row Count:** 36,162
*   **Inferred Purpose:** Acts as the line items for store orders, linking an order to the products included, their quantity, and the price at the time of order.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`, `update_meta_sales_correlation.py`, `update_product_details.py`)
*   **Potential Relationships:** Linked to `store_orders` via `id_store_order`. Linked to `products` via `id_product`. Linked to by `store_order_product_attributes` via `id_store_order_product`.

---

## 29. `store_orders`

*   **Columns:** `id_store_order`, `num_order`, `postal_code`, `id_store`, `id_store_customer`, `id_payment_method`, `id_shipping`, `cost_shipping`, `subtotal`, `id_order_status`, `active`, `invoice_number`, `clarification`, `description`, `was_processed`, `date` (null), `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `service_type`, `response_ahiva`, `tracking_id`, `guide_number`, `json_confirm`, `json_pagopar`, `pack_id`, `response_pagopar`, `response_ues`, `response_nubeflash`, `discount`, `id_cupon`, `preparation_time`, `json_dlocal`, `hour_service`, `date_service` (many response/status columns for shipping/payment providers)
*   **Row Count:** 17,862
*   **Inferred Purpose:** Contains the main information for orders placed within a specific store. This seems to be the primary orders table used by the analysis scripts mentioned in the README.
*   **Used by Scripts:** Yes (`update_customers_relationships.py`, `update_meta_sales_correlation.py`, `update_product_details.py`, `update_store_activity_metrics.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to `store_customers` via `id_store_customer`. Linked to `payment_methods` via `id_payment_method`. Linked to `shipping_methods` via `id_shipping`. Linked to `orders_statuses` via `id_order_status`. Linked to `store_cupons` via `id_cupon`. Parent table for `store_ordered_products`.

---

## 30. `store_visits`

*   **Columns:** `id_store_visits`, `id_store`, `ip_address`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 200,588
*   **Inferred Purpose:** Logs visits to a store front, identified by IP address.
*   **Used by Scripts:** Yes (`update_store_activity_metrics.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 31. `product_attributes`

*   **Columns:** `id_product_attribute`, `id_product_variation`, `id_product`, `id_attribute`, `id_attribute_value`, `active`, `created_at`, `updated_at`
*   **Row Count:** 553,488
*   **Inferred Purpose:** Appears to link a specific product variation to its defining attribute and attribute value.
*   **Used by Scripts:** Yes (`update_product_variations.py`)
*   **Potential Relationships:** Linked to `product_variations` via `id_product_variation`. Linked to `products` via `id_product`. Linked to `attributes` via `id_attribute`. Linked to `attribute_variations` via `id_attribute_value` (assuming `id_attribute_value` corresponds to `id_attribute_variation`).

---

## 32. `product_variations`

*   **Columns:** `id_product_variation`, `id_product`, `stock`, `price`, `offer`, `sku`, `offer_price`, `active`, `created_at`, `updated_at`
*   **Row Count:** 306,559
*   **Inferred Purpose:** Defines specific variations of a product, each potentially having its own SKU, stock level, and price.
*   **Used by Scripts:** Yes (`update_product_variations.py`)
*   **Potential Relationships:** Linked to `products` via `id_product`. Linked to by `product_attributes` via `id_product_variation`.

---

## 33. `store_order_product_attributes`

*   **Columns:** `id_order_product_attribute`, `id_store_order_product`, `id_attribute_variation`, `active`, `created_at`, `updated_at`
*   **Row Count:** 28,180
*   **Inferred Purpose:** Links a specific line item in a store order (`store_ordered_products`) to the specific attribute variation that was chosen for that item (e.g., linking the ordered T-shirt line item to the "Red" variation).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_ordered_products` via `id_store_order_product`. Linked to `attribute_variations` via `id_attribute_variation`.

---

## 34. `store_subcategories`

*   **Columns:** `id_subcategory` (likely PK for this table), `id_category` (FK to `store_categories`), `name`, `id_store`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 3,631
*   **Inferred Purpose:** Defines subcategories *specific* to a store, nested under a store's main categories.
*   **Used by Scripts:** Yes (`update_product_categories.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to `store_categories` via `id_category`. Linked to by `store_sub_subcategories` via `id_subcategory`. Used to categorize products, likely linking to `products`.

---

## 35. `store_sub_subcategories`

*   **Columns:** `store_sub_subcategorie_id`, `id_subcategory` (FK to `store_subcategories`), `name`, `id_store`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1,163
*   **Inferred Purpose:** Defines a third level of categorization specific to a store, nested under store subcategories.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to `store_subcategories` via `id_subcategory`. Linked to by `products_subcategories` via `store_sub_subcategorie_id`.

---

## 36. `store_users`

*   **Columns:** `id_user`, `id_store`, `name`, `last_name`, `dni`, `email`, `password` (hashed), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`, `cod`, `cod_confir`, `orderId_domain`
*   **Row Count:** 1,296
*   **Inferred Purpose:** Stores login information for users who can manage a specific store (store owners/staff), distinct from `store_customers`.
*   **Used by Scripts:** Yes (`update_store_activity_metrics.py`, `update_store_users.py`)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Used by the `update_store_users.py` script.

---

## 37. `subcategories`

*   **Columns:** `id_subcategory`, `id_category` (FK to `categories`), `name`, `active`, `created_at`, `updated_at`, `deleted_at`, `update_by`, `create_by`, `delete_by`
*   **Row Count:** 5
*   **Inferred Purpose:** Defines global subcategories, nested under the global `categories`. Distinct from `store_subcategories`.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `categories` via `id_category`. Linked to by `products_subcategories` via `id_subcategory`.

---

## 38. `type_currencys` (Currencies)

*   **Columns:** `type_currency_id`, `name`, `symbol`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 3
*   **Inferred Purpose:** Defines different types of currencies available in the system (e.g., Guarani, USD).
*   **Used by Scripts:** Yes (`update_active_stores.py`)
*   **Potential Relationships:** Linked to by `stores` (via `type_currency_id`) and potentially `order_plans` (via `type_currency`).

---

## 39. `amount_affiliates`

*   **Columns:** Unknown (Query returned no rows)
*   **Row Count:** 0
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Inferred Purpose:** Unknown (Query returned no rows)

---

## 40. `audits`

*   **Columns:** Unknown (Query returned no rows)
*   **Row Count:** 0
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Inferred Purpose:** Unknown (Query returned no rows)

---

## 41. `aws_certificate_manager`

*   **Columns:** `aws_certificate_id`, `id_store`, `certificate_arn`, `type_certificate`, `name`, `value`, `validate`, `id_certificate_zone`, `active`, `created_at`, `updated_at`
*   **Row Count:** 153
*   **Inferred Purpose:** Manages SSL certificate information from AWS Certificate Manager, likely linking certificates to stores.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store` (nullable). May link to `aws_hosted_zone` via `id_certificate_zone`.

---

## 42. `aws_hosted_zone`

*   **Columns:** `aws_hosted_zone_id`, `id_store`, `hosted_zone_id`, `name` (domain name), `change_info_id`, `dns_1`, `dns_2`, `dns_3`, `dns_4`, `active`, `created_at`, `updated_at`
*   **Row Count:** 159
*   **Inferred Purpose:** Stores information about DNS zones hosted in AWS Route 53, likely linking domains/zones to stores.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store` (nullable).

---

## 43. `aws_subdomain_history`

*   **Columns:** `aws_subdomain_id`, `id_store`, `subdomain`, `active`, `created_at`, `updated_at`
*   **Row Count:** 1
*   **Inferred Purpose:** Tracks the history of subdomains assigned to stores, possibly within a main domain managed by the platform.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 44. `colors`

*   **Columns:** `id_color`, `id_template`, `name`, `color_primary`, `color_logo`, `border_hover`, `color_text`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 44
*   **Inferred Purpose:** Defines color schemes or palettes, likely associated with store templates.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `templates` via `id_template`.

---

## 45. `commissions`

*   **Columns:** `commission_id`, `id_plan`, `name`, `value`, `active`, `created_at`, `updated_at`, `deleted_at`, `updated_by`, `created_by`, `deleted_by`
*   **Row Count:** 3
*   **Inferred Purpose:** Defines commission rates or values, likely associated with subscription or sales plans.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to a `plans` table via `id_plan`.

---

## 46. `configurations`

*   **Columns:** `id_configuration`, `name`, `value`, `key_id`, `input`, `required`, `icon`, `ordering`, `enabled`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 40
*   **Inferred Purpose:** Seems to be a *global* or platform-level configuration table (distinct from `store_configurations`), storing key-value settings for the application.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Unlikely to have direct FK relationships to main data tables like stores or products.

---

## 47. `customer_shipping_credentials`

*   **Columns:** `shipping_credentials` (likely PK), `id_store_order`, `id_customer`, `code_postal`, `country_code`, `province_code`, `city`, `street`, `active`, `created_at`, `updated_at`
*   **Row Count:** 11,610
*   **Inferred Purpose:** Stores the specific shipping address used for a particular order by a customer.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_orders` via `id_store_order`. Linked to `customers` via `id_customer`.

---

## 48. `customers`

*   **Columns:** `id_customer`, `name`, `surname`, `address`, `province`, `location`, `telephone`, `email`, `code_confirmation`, `type_user_id`, `status_code_confirmation`, `postal_code`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 8
*   **Inferred Purpose:** Appears to be a *global* customer table (distinct from `store_customers`). Customers might exist independently of placing an order in a specific store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `customer_shipping_credentials` via `id_customer`. May link to a `type_user` table. Could potentially link to `store_customers` if a global customer can interact with multiple stores.

---

## 49. `customizes`

*   **Columns:** `id_customize`, `color_primary`, `color_logo`, `border_hover`, `color_text`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 2,670
*   **Inferred Purpose:** Defines custom color schemes, potentially overriding template defaults (`colors` table) for specific instances (maybe stores, though no `id_store` visible).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** May link to `stores` or `templates` (though no FKs visible in sample).

---

## 50. `dollar`

*   **Columns:** `id_dollar`, `value`, `active`, `updated_at`, `created_at`, `deleted_at`, `update_by`, `create_by`, `delete_by`
*   **Row Count:** 4
*   **Inferred Purpose:** Stores the exchange rate for the US Dollar, likely against a base currency.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Used for currency conversion in pricing or reporting.

---

## 51. `domain_hosting`

*   **Columns:** `domain_hosting_id`, `id_store`, `name` (domain name), `sub_domain`, `certificate`, `send`, `active`, `created_at`, `updated_at`
*   **Row Count:** 20
*   **Inferred Purpose:** Manages custom domain and subdomain configurations associated with stores, possibly distinct from or complementary to the AWS tables (`aws_hosted_zone`, `aws_subdomain_history`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 52. `faqs`

*   **Columns:** `id_faq`, `question`, `answer`, `slug`, `active`, `created_at`, `updated_at`
*   **Row Count:** 63
*   **Inferred Purpose:** Stores Frequently Asked Questions and their answers for the platform.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Likely displayed on a general help/support page.

---

## 53. `groups`

*   **Columns:** `id_group`, `name`, `description`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 8
*   **Inferred Purpose:** Defines user groups or roles within the system (e.g., "admin").
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to users (likely via a `users_groups` junction table) to assign permissions.

---

## 54. `image_bank`

*   **Columns:** `id_image_bank`, `image` (filename), `active`, `created_at`, `updated_at`, `deleted_at`
*   **Row Count:** 165
*   **Inferred Purpose:** Appears to be a central repository or bank of images available for use across the platform, possibly for templates or generic content.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Unclear how these images are linked or used without more context.

---

## 55. `image_gallery`

*   **Columns:** `id_image`, `name`, `image` (filename), `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 46
*   **Inferred Purpose:** Stores images, possibly for a gallery feature or potentially product images (though `products` table also has an `image` column). `name` column suggests these might be manually uploaded/managed images.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Could be linked to `products`, `stores`, or `templates`.

---

## 56. `iva` (VAT/Sales Tax)

*   **Columns:** `id_iva`, `value` (percentage), `active`, `updated_at`, `created_at`, `deleted_at`, `update_by`, `create_by`, `delete_by`
*   **Row Count:** 5
*   **Inferred Purpose:** Stores the applicable Value Added Tax (or equivalent sales tax) rate.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Used in calculating final prices for orders or invoices.

---

## 57. `json_languages`

*   **Columns:** `json_language_id`, `name`, `json` (large JSON blob), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 94
*   **Inferred Purpose:** Stores localization strings for the platform's UI in JSON format, supporting multiple languages (e.g., 'es', 'en').
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Used by the frontend/backend application to display text in the user's selected language.

---

## 58. `login_attempts`

*   **Columns:** `id`, `id_user`, `ip_address` (Buffer type), `login` (username used), `time`, `active`, `create_at`, `update_at`, `delete_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 7,148
*   **Inferred Purpose:** Logs login attempts, likely for security monitoring and potentially rate-limiting.
*   **Used by Scripts:** Yes (`update_store_activity_metrics.py`)
*   **Potential Relationships:** Linked to a users table (likely `users`, based on `id_user`)

---

## 59. `login_attempts_errors`

*   **Columns:** Unknown (Query returned no rows)
*   **Row Count:** 0
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Inferred Purpose:** Unknown (Query returned no rows). Likely logs errors related to login attempts specifically.
*   **Potential Relationships:** Unknown.

---

## 60. `login_errors`

*   **Columns:** `id_login`, `user`, `password` (potentially stored plaintext?), `date`, `ip_address` (Buffer), `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 610
*   **Inferred Purpose:** Logs failed login attempts, capturing the username and password used.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Might link to a user table via `user` (username), although `login_attempts` used `id_user`.

---

## 61. `memberships`

*   **Columns:** `id_membership`, `id_plan`, `name`, `amount`, `amount_iva` (amount with tax), `active`, `created_at`, `updated_at`, `deleted_at`
*   **Row Count:** 19
*   **Inferred Purpose:** Defines pricing for different membership plans, possibly for different regions/currencies (e.g., "Uruguay").
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to a `plans` table via `id_plan`.

---

## 62. `memberships_description`

*   **Columns:** `membership_description` (likely PK), `id_plan`, `description`, `active`, `created_at`, `updated_at`
*   **Row Count:** 3
*   **Inferred Purpose:** Provides descriptive text or features associated with a specific membership plan.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `plans` via `id_plan`.

---

## 63. `menus`

*   **Columns:** `id_menu`, `description`, `link`, `status`, `parent`, `iconpath`, `active`, `dashboard`, `order`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 39
*   **Inferred Purpose:** Defines the structure and items for navigation menus within the application's admin/dashboard interface. Supports hierarchical menus (`parent` column).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `permissions` via `id_menu`.

---

## 64. `order_products`

*   **Columns:** `id_order_product`, `id_order`, `id_product`, `qty`, `price`, `active`, `created_at`, `updated_at`
*   **Row Count:** 4
*   **Inferred Purpose:** Line items for orders placed via the `orders` table (as opposed to `store_ordered_products` which links to `store_orders`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `orders` via `id_order`. Linked to `products` via `id_product`.

---

## 65. `pais` (Country)

*   **Columns:** `PaisCodigo` (3-letter code), `PaisNombre`, `PaisContinente`, `PaisRegion`, `PaisArea`, `PaisIndependencia`, `PaisPoblacion`, `PaisExpectativaDeVida`, `PaisProductoInternoBruto`, `PaisProductoInternoBrutoAntiguo`, `PaisNombreLocal`, `PaisGobierno`, `PaisJefeDeEstado`, `PaisCapital`, `PaisCodigo2` (2-letter code), `country_ml`, `url_ml`, `coin`, `status_ml`
*   **Row Count:** 239
*   **Inferred Purpose:** Stores detailed country information, including geographic, demographic, economic, and political details. Seems more comprehensive than the `countries` table queried earlier. It might be the primary country table.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Links to `ciudad` via `PaisCodigo`. Contains `PaisCodigo2` which likely corresponds to `code` in the `countries` table.

---

## 66. `permissions`

*   **Columns:** `id_permission`, `id_menu`, `id_group`, `read`, `insert`, `update`, `delete`, `export`, `print`, `invoice`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 78
*   **Inferred Purpose:** Junction table defining permissions for user groups (`id_group`) on specific menu items (`id_menu`), detailing allowed actions (read, insert, update, delete, etc.).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `menus` via `id_menu`. Linked to `groups` via `id_group`.

---

## 67. `plans`

*   **Columns:** `id_plan`, `name`, `active`, `description`, `created_at`, `updated_at`
*   **Row Count:** 5
*   **Inferred Purpose:** Defines the available subscription or service plans (e.g., "Emprendedor").
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `memberships`, `memberships_description`, `commissions`, `order_plans`, and likely `store_plans`.

---

## 68. `product_images`

*   **Columns:** `id_product_image`, `id_product`, `image` (filename), `active`, `create_at`, `update_at`, `deleted_at`, `update_by`, `delete_by`, `create_by`
*   **Row Count:** 117,097
*   **Inferred Purpose:** Stores multiple images associated with a single product, creating a gallery for each product.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `products` via `id_product`.

---

## 69. `products_categories_orders`

*   **Columns:** Unknown (Query returned no rows)
*   **Row Count:** 0
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Inferred Purpose:** Unknown (Query returned no rows). Might have been intended to track the display order of products within categories.
*   **Potential Relationships:** Unknown.

---

## 70. `products_providers`

*   **Columns:** `id_product_provider`, `id_product`, `code` (provider's code for the product), `active`, `created_at`, `updated_at`
*   **Row Count:** 5
*   **Inferred Purpose:** Links products to their providers/suppliers, storing the provider's specific code for that product.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `products` via `id_product`. Might link to a `providers` table (not yet seen).

---

## 71. `products_subategories_orders`

*   **Columns:** Unknown (Table name appears to have a typo in documentation)
*   **Row Count:** N/A
*   **Note:** Table does not exist in the database. This appears to be a typo in the original documentation - should be `products_subcategories_orders`.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** N/A

---

## 72. `products_subcategories_orders`

*   **Columns:** Unknown (Query returned no rows)
*   **Row Count:** 0
*   **Note:** Table exists but is empty.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Inferred Purpose:** Unknown (Query returned no rows). Might have been intended to track the display order of products within subcategories.
*   **Potential Relationships:** Unknown.

---

## 73. `products_subsidiaries`

*   **Columns:** `id_stock_subsidiary`, `id_product`, `id_subsidiary`, `stock`, `active`, `created_at`, `updated_at`
*   **Row Count:** 5
*   **Inferred Purpose:** Manages stock levels for specific products at different subsidiaries or locations.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `products` via `id_product`. Linked to `subsidiaries` via `id_subsidiary`.

---

## 74. `provinces`

*   **Columns:** `id_province`, `name`, `country` (2-letter code), `population`, `area`, `capital`, `capprovince` (?)
*   **Row Count:** 1,382
*   **Inferred Purpose:** Stores province/state information, linked to countries. Seems related to `pais` and `countries`.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to a country table (likely `pais` or `countries`) via the `country` column.

---

## 75. `remove_certificates`

*   **Columns:** `remove_certificates_id`, `id_store`, `certificate` (ARN), `active`, `created_at`, `updated_at`
*   **Row Count:** 12
*   **Inferred Purpose:** Seems to log or track SSL certificates that have been removed or are scheduled for removal for a store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. References certificate ARNs, possibly related to `aws_certificate_manager`.

---

## 76. `schedules_service`

*   **Columns:** `schedule_service_id`, `day`, `start_schedule`, `end_schedule`, `service_id`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 2,284
*   **Inferred Purpose:** Defines available time slots or schedules for a specific service.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to a `services` table (not yet seen) via `service_id`.

---

## 77. `sections`

*   **Columns:** `id_section`, `name`, `description` (HTML content), `active`, `enable`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 11
*   **Inferred Purpose:** Defines content sections for the platform's public website (e.g., "Quienes somos" - About Us).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Used by the frontend application to render content pages.

---

## 78. `services_taken`

*   **Columns:** `service_take_id`, `day`, `hour`, `service_id`, `date`, `id_store_order`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 155
*   **Inferred Purpose:** Records instances where a specific service time slot has been booked or taken, potentially linked to a store order.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to a `services` table via `service_id`. May link to `store_orders` via `id_store_order` (nullable).

---

## 79. `shopping_domains`

*   **Columns:** `shopping_domain_id`, `store_user_id`, `store_id`, `domain`, `price`, `response_price`, `godaddy_order_id`, `confirm_json` (GoDaddy contact info), `shpping_json` (GoDaddy purchase details), `response_shipping_json` (GoDaddy purchase response), `period`, `status`, `payment_method_id`, `payment_active`, `enable`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 31
*   **Inferred Purpose:** Tracks the purchase and configuration of custom domains through a registrar (GoDaddy, based on column names/JSON content) for stores.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `store_users` via `store_user_id`. Linked to `payment_methods` via `payment_method_id`.

---

## 80. `sliders_courses`

*   **Columns:** `id_slider_course`, `image` (filename), `active`, `created_at`, `updated_at`
*   **Row Count:** 11
*   **Inferred Purpose:** Stores images for sliders, specifically related to courses (perhaps tutorial courses?).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Might link to a `courses` or `tutorial_courses` table.

---

## 81. `store_affiliates`

*   **Columns:** `store_affiliate_id`, `store_id` (nullable), `user_id`, `ip_affiliate`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 674
*   **Inferred Purpose:** Tracks affiliate activity, linking an affiliate user (`user_id`) to a store they referred or are associated with (`store_id`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `users` via `user_id`.

---

## 82. `store_banners`

*   **Columns:** `store_banner_id`, `store_id`, `user_id` (nullable), `banner` (image filename), `link`, `cod`, `type`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 150
*   **Inferred Purpose:** Manages promotional banners displayed within a store, potentially linking to specific users or affiliate codes.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. May link to `users` via `user_id`.

---

## 83. `store_carshop` (Shopping Cart)

*   **Columns:** `id_carshop`, `id_customer`, `id_store`, `active`, `send`, `url`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 2,662
*   **Inferred Purpose:** Represents a customer's shopping cart for a specific store. `send` flag might indicate if a reminder was sent for an abandoned cart.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_customers` via `id_customer` (Note: sample shows linking to `id_customer=242`, which likely corresponds to `store_customers.id_store_customer`, not the global `customers.id_customer`). Linked to `stores` via `id_store`. Parent table for `store_carshop_detail`.

---

## 84. `store_carshop_detail`

*   **Columns:** `id_carshop_detail`, `id_carshop`, `id_product`, `id_product_variation`, `id_variaciones` (JSON, seems redundant with `id_product_variation`), `qty`, `price`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 9,047
*   **Inferred Purpose:** Details the items within a shopping cart, including the specific product, variation, quantity, and price.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_carshop` via `id_carshop`. Linked to `products` via `id_product`. Linked to `product_variations` via `id_product_variation`.

---

## 85. `store_commissions_orders`

*   **Columns:** `store_commission_order_id`, `store_id`, `store_order_id`, `id_plan`, `commission_percentage`, `is_paid`, `active`, `created_at`, `updated_at`, `deleted_at`, `updated_by`, `created_by`, `deleted_by`
*   **Row Count:** 13,464
*   **Inferred Purpose:** Records the commission calculated for a specific store order based on the store's plan at the time. Tracks payment status.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `store_orders` via `store_order_id`. Linked to `plans` via `id_plan`.

---

## 86. `store_convertion_pay` (Conversion Pay)

*   **Columns:** `convertion_id`, `store_id`, `payment_id`, `convertion` (rate?), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `update_by`, `deleted_by`
*   **Row Count:** 41
*   **Inferred Purpose:** Appears to store currency conversion rates applied to specific payments for a store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `payments` via `payment_id`.

---

## 87. `store_credentials_mercadolibre`

*   **Columns:** `store_credentials_mercadolibre_id`, `store_id`, `client_id`, `cliente_secret`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 23
*   **Inferred Purpose:** Stores API credentials for connecting a store to MercadoLibre.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`.

---

## 88. `store_favicons`

*   **Columns:** `id_favicon_store`, `id_store`, `favicon` (image filename), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 522
*   **Inferred Purpose:** Stores the favicon image file associated with a specific store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 89. `store_groups`

*   **Columns:** `id_group_store`, `id_store`, `super_admin`, `name`, `description`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1,322
*   **Inferred Purpose:** Defines user groups *specific* to a store, potentially distinct from the global `groups` table. Allows for store-level admin roles.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Likely linked to `store_users` via a junction table (`store_users_groups`?).

---

## 90. `store_logos`

*   **Columns:** `id_logo_store`, `id_store`, `logo` (image filename), `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 632
*   **Inferred Purpose:** Stores the logo image file associated with a specific store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 91. `store_menus`

*   **Columns:** `id_menu_store`, `name`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 11
*   **Inferred Purpose:** Defines menu items specific to the *store's* admin interface (as opposed to the global `menus` table).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `store_permissions` via `id_menu_store`.

---

## 92. `store_order_invoices`

*   **Columns:** `id_store_order_invoice`, `id_store_order`, `id_invoice`, `store_commission_order_id`, `date`, `amount`, `created_at`, `updated_at`
*   **Row Count:** 12,949
*   **Inferred Purpose:** Links a specific store order to a corresponding invoice, potentially including commission details.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_orders` via `id_store_order`. Linked to `invoices` via `id_invoice`. Linked to `store_commissions_orders` via `store_commission_order_id`.

---

## 93. `store_permissions`

*   **Columns:** `id_store_permission`, `id_menu_store`, `id_group_store`, `permission`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 13,484
*   **Inferred Purpose:** Junction table defining permissions for store-specific user groups (`id_group_store`) on store-specific menu items (`id_menu_store`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_menus` via `id_menu_store`. Linked to `store_groups` via `id_group_store`.

---

## 94. `store_plans`

*   **Columns:** `id_store_plan`, `id_store`, `id_plan`, `id_order` (nullable), `days`, `date_active_plan`, `active`, `was_processed`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1,688
*   **Inferred Purpose:** Tracks the subscription plan currently or previously active for a specific store, including duration and activation date.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. Linked to `plans` via `id_plan`. May link to `orders` via `id_order` (if plan purchased via an order).

---

## 95. `store_politics_changes` (Policies)

*   **Columns:** `id_store_politics_changes`, `id_store`, `title`, `text` (HTML content), `type`
*   **Row Count:** 1,059
*   **Inferred Purpose:** Stores custom policy text (like return policy, privacy policy) defined by a specific store. `type` likely indicates which policy it is.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 96. `store_shopping_visits`

*   **Columns:** `store_shopping_visit_id`, `store_order_id`, `user_id`, `store_id`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1
*   **Inferred Purpose:** Appears to log visits related to specific orders or users within a store context. Purpose isn't perfectly clear; could be tracking visits leading to a purchase or visits by store staff to an order page.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_orders` via `store_order_id`. Linked to `users` via `user_id`. Linked to `stores` via `store_id`.

---

## 97. `store_sliders`

*   **Columns:** `id_slider_store`, `id_store`, `slider` (image filename or ID?), `is_description`, `title`, `description`, `url`, `active`, `img_cust`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 3,807
*   **Inferred Purpose:** Manages image sliders specific to a store, including optional text overlays and links.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`. `slider` column might link to `image_gallery` or `image_bank`.

---

## 98. `store_templates`

*   **Columns:** `id_template_store`, `id_template`, `id_store`, `id_color`, `active`, `id_customize`, `id_typography`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 664
*   **Inferred Purpose:** Assigns a specific template, color scheme, custom theme, and typography to a store.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `templates` via `id_template`. Linked to `stores` via `id_store`. Linked to `colors` via `id_color`. Linked to `customizes` via `id_customize`. Linked to `typography` via `id_typography`.

---

## 99. `store_users_groups`

*   **Columns:** `id_user_group_store`, `id_user` (links to `store_users`), `id_group_store`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 1,323
*   **Inferred Purpose:** Junction table assigning store users (`id_user` from `store_users` table) to store-specific groups (`id_group_store` from `store_groups` table).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `store_users` via `id_user`. Linked to `store_groups` via `id_group_store`.

---

## 100. `subsidiaries`

*   **Columns:** `id_subsidiary`, `name`, `adress` (address), `telephone`, `email`, `active`, `created_at`, `updated_at`
*   **Row Count:** 2
*   **Inferred Purpose:** Stores information about different physical locations or branches (subsidiaries) of a business (likely related to stores, though no `id_store` is visible in the sample).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `products_subsidiaries` and `subsidiaries_postal_codes` via `id_subsidiary`.

---

## 101. `subsidiaries_postal_codes`

*   **Columns:** `id_postal_code`, `postal_code`, `shipping_amount`, `id_subsidiary`, `active`, `created_at`, `updated_at`
*   **Row Count:** 6
*   **Inferred Purpose:** Defines shipping costs based on postal code for specific subsidiaries.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `subsidiaries` via `id_subsidiary`.

---

## 102. `success_stories`

*   **Columns:** `id_success_story`, `author`, `description`, `link`, `image`, `is_published`, `active`, `created_at`, `updated_at`
*   **Row Count:** 3
*   **Inferred Purpose:** Stores customer success stories or case studies, likely for display on the platform's website.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Might link to `stores` or `customers` indirectly via `author` or `link`.

---

## 103. `templates`

*   **Columns:** `id_template`, `name`, `home` (image?), `listado` (image?), `ficha` (image?), `type`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 5
*   **Inferred Purpose:** Defines the available website/store design templates.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `store_templates` via `id_template`. Linked to `colors` and `templates_images`.

---

## 104. `templates_images`

*   **Columns:** `template_image_id`, `template_id`, `image`, `image_2`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 4
*   **Inferred Purpose:** Associates preview or component images with specific templates.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `templates` via `template_id`.

---

## 105. `testimoniales` (Testimonials)

*   **Columns:** `id_testimonial`, `id_store`, `title`, `description` (JSON with brief/detailed), `image`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 17
*   **Inferred Purpose:** Stores customer testimonials, potentially associated with a specific store. The description format suggests it might contain rich text or embedded content.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `id_store`.

---

## 106. `trafikerts_stores`

*   **Columns:** `trafikerts_store_id`, `store_id`, `heading_id`, `user_id`, `status`, `active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `update_by`, `deleted_by`
*   **Row Count:** 5
*   **Inferred Purpose:** The purpose is unclear from the name and columns. "Trafikert" might refer to an affiliate or traffic source. This table links stores, users, and headings.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `users` via `user_id`. Linked to `headings` via `heading_id`.

---

## 107. `tutorial_courses`

*   **Columns:** `tutorial_course_id`, `title`, `description_brief`, `description_detailed` (HTML content), `description`, `video`, `video_code`, `image`, `privacy`, `read_time`, `difficulty`, `type`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 198
*   **Inferred Purpose:** Stores content for tutorial courses offered on the platform, including text, images, and potentially embedded videos.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Might be linked to `sliders_courses`.

---

## 108. `typography`

*   **Columns:** `id_typography`, `name`, `font_family` (font filename), `active`, `created_at`, `updated_at`, `deleted_at`
*   **Row Count:** 10
*   **Inferred Purpose:** Defines available fonts or typography sets for use in store templates.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `store_templates` via `id_typography`.

---

## 109. `user_data_banks`

*   **Columns:** `user_data_bank_id`, `user_id`, `last_name`, `name`, `account_holder`, `my_bank` (bank details text), `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 4
*   **Inferred Purpose:** Stores bank account details associated with users, likely for payouts (e.g., for affiliates or store owners).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `users` via `user_id`.

---

## 110. `users`

*   **Columns:** `id_user`, `ip_address`, `username`, `password` (hashed), `salt`, `email`, `last_login`, `created_on`, `active`, `name`, `surname`, `telephone`, `mobile`, `image`, `idioma` (language), `template`, `cod_trafikerts`, `cod_afiliado`, `conversion_trafikerts`, `percentage`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`, `deleted`
*   **Row Count:** 32
*   **Inferred Purpose:** Represents *global* users of the platform (distinct from `store_users`), likely including administrators, affiliates, and potentially customers if they can log into a central platform account.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to by `login_attempts`, `login_errors`, `users_groups`, `store_affiliates`, `store_shopping_visits`, and `user_data_banks`.

---

## 111. `users_groups`

*   **Columns:** `id_user_group`, `id_user`, `id_group`, `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 32
*   **Inferred Purpose:** Junction table assigning global users (`users`) to global groups (`groups`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `users` via `id_user`. Linked to `groups` via `id_group`.

---

## 112. `visits`

*   **Columns:** `id_visit`, `ip_address`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`
*   **Row Count:** 40,548
*   **Inferred Purpose:** Logs visits to the main platform website (distinct from `store_visits`), identified by IP address.
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Likely used for general platform analytics.

---

## 113. `visits_trafikerts`

*   **Columns:** `visits_trafikert_id`, `store_id`, `user_id` (affiliate/trafikert?), `ip_visit`, `shopping` (boolean?), `store_order_id` (nullable), `active`, `created_at`, `updated_at`, `deleted_at`, `create_by`, `update_by`, `delete_by`
*   **Row Count:** 45
*   **Inferred Purpose:** Logs visits potentially driven by affiliates or specific traffic sources ("trafikerts") to stores. It seems to track if the visit resulted in a purchase (`shopping` flag and optional `store_order_id`).
*   **Used by Scripts:** No (Based on analyzed `update_*.py` scripts)
*   **Potential Relationships:** Linked to `stores` via `store_id`. Linked to `users` (likely affiliates/trafikerts) via `user_id`. May link to `store_orders` via `store_order_id`. Related to `trafikerts_stores`.