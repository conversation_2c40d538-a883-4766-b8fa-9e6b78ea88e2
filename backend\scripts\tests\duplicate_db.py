import os
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# --- Configuration ---
# Get MongoDB connection string from environment variable
CONNECTION_STRING = os.getenv("MONGODB_URI")
if not CONNECTION_STRING:
    raise ValueError("MONGODB_URI environment variable is not set")

# Database configuration (can be overridden by environment variables)
SOURCE_DB = os.getenv("SOURCE_DB", "D-Unit-AnalysisGPT")
DESTINATION_DB = os.getenv("DESTINATION_DB", "testingdb")
ARCHIVE_FILE = os.getenv("ARCHIVE_FILE", "db_dump.gz")

def run_command(command):
    """Executes a shell command and checks for errors."""
    print(f"🚀 Running command: {' '.join(command[:5])}...")
    try:
        process = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True
        )
        print("✅ Command successful.")
        return True
    except FileNotFoundError:
        print(f"❌ Error: Command '{command[0]}' not found.")
        print("Please ensure MongoDB Database Tools are installed and in your system's PATH.")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code {e.returncode}.")
        print(f"   Error output:\n{e.stderr}")
        return False

def duplicate_database():
    """Dumps a source database and restores it to a new destination database."""
    
    dump_command = [
        "mongodump",
        f"--uri={CONNECTION_STRING}",
        f"--db={SOURCE_DB}",
        f"--archive={ARCHIVE_FILE}",
        "--gzip"
    ]
    
    restore_command = [
        "mongorestore",
        f"--uri={CONNECTION_STRING}",
        f"--nsFrom={SOURCE_DB}.*",
        f"--nsTo={DESTINATION_DB}.*",
        f"--archive={ARCHIVE_FILE}",
        "--gzip"
    ]

    try:
        # Step 1: Dump the source database
        if not run_command(dump_command):
            print("Halting process due to dump failure.")
            return # Exit the function

        # Step 2: Restore to the destination database
        if not run_command(restore_command):
            print("Restore process failed.")
            
    finally:
        # Step 3: Clean up the archive file
        # This 'finally' block will always run, ensuring the file is removed
        # even if the 'try' block encounters an error or returns early.
        if os.path.exists(ARCHIVE_FILE):
            print(f"🧹 Cleaning up temporary file: {ARCHIVE_FILE}")
            os.remove(ARCHIVE_FILE)
    
    print("✨ Process finished.")

if __name__ == "__main__":
    duplicate_database()