import { fetchWithDeduplication } from './apiService';
import { StoreSalesData, TimeRange } from './types';
import axios from 'axios';
import logger from '../utils/logger';
import { ApiError } from './apiService';
import { authService } from './authService';
import { AuthUtils } from '../utils/authUtils';
import { API_URL } from '../config/api';

// Store data types
export interface Country {
  name: string;
  percentage: number;
  cities: Array<{
    name: string;
    percentage: number;
  }>;
}

// Missing type definitions
export interface ShippingAnalysisData {
  most_frequent_shipping_method: {
    name: string;
    count: number;
  } | null;
  shipping_method_distribution: Array<{
    name: string;
    count: number;
  }>;
  shipping_analysis?: {
    analysis_text?: string;
    recommendations?: string[];
  };
}

export interface TopPostsData {
  posts: Array<{
    id: string;
    message?: string;
    created_time: string;
    permalink_url: string;
    type?: string;
    platform: 'facebook' | 'instagram';
    metrics?: {
      likes: number;
      comments: number;
      shares?: number;
      saved?: number;
    };
  }>;
  total_posts: number;
}

// Define interface for individual customer order product
interface CustomerOrderProduct {
  product_id: number;
  product_name: string;
  quantity: number;
  price: number;
}

// Define interface for individual customer order
interface CustomerOrder {
  order_id: number;
  order_date: string; // Assuming ISODate string
  total: number;
  status: string;
  payment_method: string;
  shipping_method: string;
  shipping_cost: number;
  order_item_total: number; // Added based on user data
  products: CustomerOrderProduct[];
  coupon_code: string | null; // Added based on user data
  coupon_amount: number; // Added based on user data
}

// Define interface for individual customer from the nested array
export interface Customer {
  store_id: number;
  customer_id: number;
  customer_email: string;
  customer_ids: number[]; // Added based on user data
  customer_name: string;
  total_orders: number;
  total_spend: number;
  first_order_date: string; // Assuming ISODate string
  last_order_date: string; // Assuming ISODate string
  preferred_payment_method: string;
  preferred_shipping_method: string;
  orders: CustomerOrder[];
  payment_methods: Record<string, number>; // Added based on user data
  shipping_methods: Record<string, number>; // Added based on user data
  country: string;
  unique_products_count: number; // Added based on user data
}

// Define interface for the root customer metrics object
export interface CustomerMetricsData {
  abandoned_cart_count?: number;
  abandoned_cart_customer_count?: number;
  abandoned_cart_total_value?: number;
  average_spend_per_customer?: number;
  country_distribution?: Record<string, number>;
  coupon_code_distribution?: Record<string, number>;
  customers?: Customer[]; // The nested array of individual customers
  // last_customer_aggregation_update?: string; // Excluded per plan
  // last_updated?: string; // Excluded per plan
  most_frequent_coupon_code?: { code: string | null; count: number };
  most_frequent_payment_method?: { name: string; count: number };
  most_frequent_shipping_method?: { name: string; count: number };
  payment_method_distribution?: Record<string, number>;
  pending_cart_count?: number;
  pending_cart_total_value?: number;
  shipping_method_distribution?: Record<string, number>;
  status_distribution?: Record<string, number>;
  total_customers?: number;
  total_store_orders?: number;
}

export interface Analysis {
  insights?: string[];
  recommendations?: string[];
  top_performing_products?: {
    product_id: string;
    name: string;
    revenue: number;
  }[];
  market_position?: string;
  customer_segmentation?: {
    segment_name: string;
    characteristics: string[];
    size: number;
  }[];
  seo_analysis?: {
    keywords?: { term: string; volume: number; competitiveness: number }[];
    on_page_factors?: { score: number; issues: string[] };
    technical_issues?: string[];
    backlink_profile?: { total: number; quality_score: number };
    recommendations?: string[];
  };
  price_history?: { date: string; our_price: number; competitor_avg: number }[];

  // --- ADD THESE PROPERTIES ---
  summary?: string;
  product_performance?: string;
  // Update demographics to use the correct type
  demographics?: { countries?: Country[]; age?: DemographicsAgeData[]; gender?: DemographicsGenderData[]; };
  customer_analysis?: string;
  competitor_analysis?: string;
  shipping_analysis?: {
    analysis_text?: string;
    recommendations?: string[];
  };
  // --- END OF ADDED PROPERTIES ---
  category_summary?: CategorySummary; // Added category summary here
  [key: string]: unknown;
}

// --- Define KeyDates Interface ---
interface KeyDates {
  last_order_date?: string;
  last_visit_date?: string;
  last_customer_registration_date?: string;
  last_product_added_date?: string;
}
// --- End Define KeyDates Interface ---

// Add new interfaces for the enhanced data structure
export interface ActivityMetrics {
  visit_count_30d: number;
  visit_count_90d: number;
  revenue_30d: number;
  revenue_90d: number;
  order_count_30d: number;
  order_count_90d: number;
}

// Remove old CustomerMetrics, will use CustomerMetricsData
// export interface CustomerMetrics { ... }

export interface MetaMetrics {
  total_followers: number;
  total_engagement: number;
  pages: Array<{
    id: string;
    name: string;
    platform: string;
    followers?: number;
  }>;
}

export interface MetricChanges {
  revenue_change_pct: number;
  orders_change_pct: number;
  customers_change_pct: number;
  avg_order_value_change_pct: number;
}

export interface ProductMetrics {
  total: number;
  performance: string;
  top_selling: ProductDetail[];
}

export interface AnalysisInsights {
  performance_metrics_summary: string;
  product_insights: string;
  customer_insights: string;
  social_media_insights: string;
}

export interface StoreAnalysis {
  store?: {
    name?: string;
    shipping_methods?: Array<{
      name: string;
      times_used: number;
    }>;
    business_type?: string;
    country?: {
      code?: string;
      name?: string;
    };
    id_store?: string;
  };
  analysis: Analysis;
  metrics?: {
    total_revenue: number;
    order_count: number;
    avg_order_value: number;
    customer_count: number; // This might be redundant if present in CustomerMetricsData
    // --- ADDED/UPDATED METRIC FIELDS ---
    total_revenue_usd?: number;
    average_order_value_usd?: number;
    total_products?: number;
    total_visits?: number;
    active_customers?: number;
    customers_with_abandoned_carts?: number;
    store_ratings_count?: number;
    store_average_rating?: number;
    products_online?: number; // Added based on user query
    total_gross_revenue?: number; // Added for gross revenue card
    total_net_revenue?: number; // Added for net revenue card
    // --- END ADDED/UPDATED METRIC FIELDS ---
    revenue_30d?: number;
    revenue_90d?: number;
    revenue_365d?: number;
    order_count_30d?: number;
    order_count_90d?: number;
    order_count_365d?: number;
    visit_count_30d?: number;
    visit_count_90d?: number;
    visit_count_365d?: number;
  };
  // New enhanced fields
  activity?: ActivityMetrics;
  customers?: CustomerMetricsData; // Use the new detailed interface
  products?: ProductMetrics;
  meta?: MetaMetrics;
  changes?: MetricChanges;
  insights?: AnalysisInsights;
  // End of new fields
  // --- ADDED FIELDS ---
  key_dates?: KeyDates;
  keywords?: string;
  analysis_metrics_text?: string;
  social_media_strategy?: string;
  // --- END ADDED FIELDS ---
  currency?: {
    symbol: string;
  };
  social_media?: {
    // --- UPDATED SOCIAL MEDIA FIELDS ---
    facebook?: string;
    instagram?: string;
    tiktok?: string;
    x_twitter?: string; // Added
    youtube?: string;
    pixel_id?: string; // Added
    // --- END UPDATED SOCIAL MEDIA FIELDS ---
  };
  feedback?: string[];
  meta_summary?: {
    total_followers: number;
    ad_spend_30d: number;
  };
}

export interface FeedbackEntry {
  text: string;
  timestamp?: string; 
  likes?: number;
  dislikes?: number;
  source?: string;
  user_email?: string; 
  meta_user_id?: string;
}

interface FeedbackResponse {
  feedback: FeedbackEntry[];
}

// UPDATE: Define a more detailed Product interface for the cached data
// Define interfaces for nested structures first
interface ProductVariationAttribute {
  name: string;
  value: string;
}

interface ProductVariation {
  variation_id: string;
  sku?: string; // Made optional based on sample data
  variation_stock: number;
  variation_price: number;
  variation_offer: boolean;
  variation_offer_price?: number; // Made optional
  product_code?: string; // Made optional
  product_description?: string; // Made optional
  attributes?: ProductVariationAttribute[]; // Made optional
}

interface ProductCategory {
  id: string;
  name: string;
}

interface ProductCategories {
  is_featured: boolean;
  is_on_sale: boolean;
  store_categories: ProductCategory[];
  subcategories: ProductCategory[];
  primary_category: string | null;
}

export interface ProductSalesDetail {
  date: string;
  units_sold: number;
  revenue: number;
}

export interface ProductDetail {
  product_id: string; // Changed from id_product based on cache structure
  name: string;
  sales_units: number;
  revenue: number;
  current_price: number;
  currency_symbol: string;
  current_stock: number;
  favorite_count?: number; // Added optional
  related_product_count?: number; // Added optional
  last_updated?: string; // Added optional
  variations?: ProductVariation[]; // Added optional array of variations
  current_stock_variations?: number; // Added optional
  categories?: ProductCategories; // Added optional categories object
  sales_details?: ProductSalesDetail[]; // Added optional array of sales details
  is_online?: boolean; // Added based on user query
}

// Keep the old Product interface for potential other uses, or remove if completely unused
// export interface Product { ... } // Keep commented out or remove if unused elsewhere

// UPDATE: Update StoreProducts to use the new ProductDetail interface
export interface StoreProductDetails {
  total_products: number;
  products: ProductDetail[]; // Use the new detailed interface
  page?: number;
  page_size?: number;
  total_pages?: number;
  sales_by_date?: ProductSalesDetail[];
  category_summary?: CategorySummary;
  store_aggregations?: {
    store_average_rating?: number;
    store_ratings_count?: number;
  };
}

// Add DemographicsAgeData and DemographicsGenderData interfaces if not already present
// (These might be needed for CustomerInsightsChart)
export interface DemographicsAgeData {
  range: string;
  percentage: number;
}
export interface DemographicsGenderData {
  type: string;
  percentage: number;
}

export interface CompanyProfile {
  company_name: string;
  business_type: string;
  contact_name: string;
  tax_id: string;
  dni: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  alternate_emails?: string[];
  selected_meta_email?: string | null;
}

// Helper for exponential backoff retry
const fetchWithRetry = async <T>(
  url: string,
  options: RequestInit = {},
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: unknown;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Add attempt number to query string for cache-busting
      const separator = url.includes('?') ? '&' : '?';
      const urlWithAttempt = `${url}${separator}_attempt=${attempt}`;
      
      // Log retry attempts after first failure
      if (attempt > 0) {
        logger.debug(`Retry attempt ${attempt} for ${url}`);
      }
      
      return await fetchWithDeduplication<T>(urlWithAttempt, {
        ...options
      });
    } catch (error) {
      lastError = error;
      
      // Only retry on network errors or 5xx server errors
      const isNetworkError = axios.isAxiosError(error) && !error.response;
      const isServerError = axios.isAxiosError(error) && error.response?.status && error.response.status >= 500;
      
      if (!isNetworkError && !isServerError) {
        throw error; // Don't retry client errors or non-retryable errors
      }
      
      // Exponential backoff with jitter
      if (attempt < maxRetries - 1) {
        const delay = baseDelay * Math.pow(2, attempt) * (0.5 + Math.random() * 0.5);
        logger.debug(`Waiting ${Math.round(delay)}ms before next retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // If we get here, all retries failed
  throw lastError;
};

// Define types based on backend Pydantic models
export interface SeoRecommendation {
  title: string;
  description: string;
  priority?: string;
  category?: string;
}

export interface SeoInsightsResponse {
  recommendations: SeoRecommendation[];
  generated_at: string; // ISO date string
  model_used: string;
}

// Token response interface
// ... existing code ...

// Store data service
export const storeService = {
  // Get store analysis
  getStoreAnalysis: async (
    storeId: string, 
    options?: { since?: string; until?: string }
  ): Promise<StoreAnalysis> => {
    // Validate storeId
    if (!storeId || storeId === 'undefined' || storeId === 'null') {
      logger.error('Invalid store ID provided to getStoreAnalysis:', storeId);
      throw new Error('Invalid store ID provided');
    }
    
    // Build query parameters
    const params = new URLSearchParams();
    if (options?.since) params.append('since', options.since);
    if (options?.until) params.append('until', options.until);
    
    const queryString = params.toString() ? `?${params.toString()}` : '';
    
    logger.debug(`Fetching analysis for store ${storeId}`, { options });
    
    // Use the retry mechanism for better error resilience
    return fetchWithRetry<StoreAnalysis>(
      `/api/store/${storeId}/analysis${queryString}`,
      {
        headers: AuthUtils.getApiHeaders()
      },
      3,  // Max 3 retries
      2000 // Start with 2 second delay
    );
  },
  
  // Get store products
  getStoreProducts: async (
    storeId: string, 
    options?: { page?: number; page_size?: number; since?: string; until?: string }
  ): Promise<StoreProductDetails> => {
    // Build query parameters
    const params = new URLSearchParams();
    if (options?.page) params.append('page', options.page.toString());
    if (options?.page_size) params.append('page_size', options.page_size.toString());
    if (options?.since) params.append('since', options.since);
    if (options?.until) params.append('until', options.until);
    
    const queryString = params.toString() ? `?${params.toString()}` : '';
    
    logger.debug(`Fetching products for store ${storeId}`, { options });
    
    // Use the retry mechanism for better error resilience
    return fetchWithRetry<StoreProductDetails>(
      `/api/store/${storeId}/products${queryString}`,
      {
        headers: AuthUtils.getApiHeaders()
      },
      3,  // Max 3 retries
      2000 // Start with 2 second delay
    );
  },
  
  // Get company profile
  getCompanyProfile: async (storeId: string): Promise<CompanyProfile> => {
    return fetchWithDeduplication<CompanyProfile>(
      `/api/store/${storeId}/company-profile`,
      {
        headers: AuthUtils.getApiHeaders()
      }
    );
  },

  // Update company profile
  updateCompanyProfile: async (storeId: string, profile: CompanyProfile): Promise<{ message: string }> => {
    return fetchWithDeduplication<{ message: string }>(
      `/api/store/${storeId}/company-profile`,
      {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profile)
      }
    );
  },
  
  // Get store sales data by time range
  getStoreSalesByTimeRange: async (storeId: string, timeRange: TimeRange): Promise<StoreSalesData> => {
    return fetchWithDeduplication<StoreSalesData>(
      `/api/store/${storeId}/sales?since=${timeRange.since}&until=${timeRange.until}`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      }
    );
  },
  
  // Select Meta email
  selectMetaEmail: async (storeId: string, selectedEmail: string): Promise<{ message: string; selected_email: string }> => {
    logger.debug(`Sending selectMetaEmail request`, { storeId, selectedEmail });
    
    try {
      const response = await fetchWithDeduplication<{ message: string; selected_email: string }>(
        `/api/store/${storeId}/select-meta-email`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ selected_email: selectedEmail })
        }
      );
      
      logger.debug(`selectMetaEmail response received`, { response });
      return response;
    } catch (error) {
      logger.error('Error in selectMetaEmail service call:', error);
      throw error;
    }
  },
  
  // Add alternate email
  addAlternateEmail: async (storeId: string, email: string): Promise<{ message: string; selected_email: string }> => {
    return fetchWithDeduplication<{ message: string; selected_email: string }>(
      `/api/store/${storeId}/alternate-email`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      }
    );
  },
  
  // Remove alternate email
  removeAlternateEmail: async (storeId: string, email: string): Promise<{ message: string; selected_email: string }> => {
    return fetchWithDeduplication<{ message: string; selected_email: string }>(
      `/api/store/${storeId}/alternate-email`,
      {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      }
    );
  },

  async getSeoRecommendations(storeId: string, lang?: string): Promise<SeoInsightsResponse> {
    logger.debug(`Fetching SEO recommendations for store ${storeId}, lang: ${lang}`);
    try {
      const langQuery = lang ? `?lang=${lang}` : '';
      // Use fetchWithDeduplication for the GET request
      const response = await fetchWithDeduplication<SeoInsightsResponse>(
        `/api/store/${storeId}/seo-recommendations${langQuery}`, // URL
        { // Options object
          method: 'GET', // Specify the method
          headers: {
            'Authorization': `Bearer ${authService.getToken()}` // Assuming token is needed
          }
        }
      );
      return response;
    } catch (error) {
      logger.error('Error fetching SEO recommendations:', error);
      // Re-throw or handle as appropriate for your error handling strategy
      if (error instanceof ApiError) {
        // Use the message from the custom ApiError
        throw new Error(`${error.message}`);
      }
      // Fallback for unknown errors
      throw new Error('An unknown error occurred while fetching SEO recommendations.');
    }
  },

  // Add the new function to fetch feedback
  async getStoreFeedback(storeId: string): Promise<FeedbackResponse> {
    logger.debug(`Fetching feedback for store ${storeId}`);
    try {
      const data = await fetchWithDeduplication<FeedbackResponse>(`/api/store/${storeId}/feedback`);
      // Ensure the response structure matches FeedbackResponse or provide default
      if (data && Array.isArray(data.feedback)) {
        return data as FeedbackResponse;
      } else {
        logger.warn(`Received unexpected feedback data structure for store ${storeId}:`, data);
        return { feedback: [] }; // Return empty array if structure is wrong
      }
    } catch (error) {
      logger.error(`Error fetching feedback for store ${storeId}:`, error);
      throw error; // Re-throw the error to be handled by the caller
    }
  },

  async getStoreCredits(storeId: string): Promise<number> {
    try {
      const { credits } = await fetchWithDeduplication<{ credits: number }>(
        `${API_URL}/api/store/${storeId}/credits`
      );
      return credits ?? 0;
    } catch (error) {
      logger.error('Failed to fetch store credits', error);
      return 0;
    }
  },

  async getStoreProductList(storeId: string, options: { page?: number; page_size?: number; since?: string; until?: string } = {}): Promise<StoreProductDetails> {
    // Build query string from options
    const queryParams = new URLSearchParams();
    if (options.page) queryParams.append('page', options.page.toString());
    if (options.page_size) queryParams.append('page_size', options.page_size.toString());
    if (options.since) queryParams.append('since', options.since);
    if (options.until) queryParams.append('until', options.until);
    
    const queryString = queryParams.toString();
    const url = `/api/store/${storeId}/product-list${queryString ? `?${queryString}` : ''}`;
    
    return await fetchWithRetry<StoreProductDetails>(url, {
      method: 'GET',
    });
  },

  // Get store customers data
  getCustomersData: async (storeId: string): Promise<CustomerMetricsData | null> => {
    logger.debug(`Fetching customers data for store ${storeId}`);
    try {
      return await fetchWithRetry<CustomerMetricsData>(
        `/api/store/${storeId}/customers`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        },
        3,
        2000
      );
    } catch (error) {
      logger.error(`Error fetching customers data for store ${storeId}:`, error);
      return null;
    }
  },

  // Get product analysis
  getProductAnalysis: async (storeId: string): Promise<unknown> => {
    logger.debug(`Fetching product analysis for store ${storeId}`);
    return fetchWithRetry(
      `/api/store/${storeId}/products`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      },
      3,
      2000
    );
  },

  // Get product details data
  getProductDetailsData: async (storeId: string): Promise<ProductDetail[]> => {
    logger.debug(`Fetching product details for store ${storeId}`);
    return fetchWithRetry<ProductDetail[]>(
      `/api/store/${storeId}/product-details`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Accept': 'application/json',
        }
      },
      3,
      2000
    );
  },

  // Get SEO recommendations
  getSEORecommendations: async (storeId: string): Promise<SeoRecommendation[]> => {
    logger.debug(`Fetching SEO recommendations for store ${storeId}`);
    return fetchWithRetry<SeoRecommendation[]>(
      `/api/seo/recommendations/${storeId}`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      },
      3,
      2000
    );
  },

  // Get shipping analysis
  getShippingAnalysis: async (storeId: string, timeRange: string = '30d'): Promise<ShippingAnalysisData> => {
    logger.debug(`Fetching shipping analysis for store ${storeId}, timeRange: ${timeRange}`);
    const params = new URLSearchParams({ time_range: timeRange });
    
    return fetchWithRetry<ShippingAnalysisData>(
      `/api/store/${storeId}/shipping?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Accept': 'application/json',
        }
      },
      3,
      2000
    );
  },

  // Get top performing posts
  getTopPosts: async (storeId: string): Promise<TopPostsData> => {
    logger.debug(`Fetching top posts for store ${storeId}`);
    return fetchWithRetry<TopPostsData>(
      `/api/store/${storeId}/top-posts`,
      {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Accept': 'application/json',
        }
      },
      3,
      2000
    );
  },

  // Submit feedback
  submitFeedback: async (storeId: string, feedbackData: FeedbackSubmissionData): Promise<{ success: boolean; message: string }> => {
    logger.debug('Submitting feedback');
    return fetchWithRetry<{ success: boolean; message: string }>(
      '/api/feedback',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          feedback: feedbackData.feedback_text,
          likes: feedbackData.likes,
          dislikes: feedbackData.dislikes,
          source: feedbackData.source,
          store_id: storeId 
        })
      },
      3,
      2000
    );
  },

  // Add a new function to fetch product sales history
  async getProductSalesHistory(storeId: string, productId: string): Promise<ProductSalesDetail[]> {
    logger.debug(`Fetching sales history for product ${productId} in store ${storeId}`);
    try {
      return await fetchWithRetry<ProductSalesDetail[]>(
        `/api/store/${storeId}/product/${productId}/sales-history`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Accept': 'application/json',
          }
        },
        3,
        2000
      );
    } catch (error) {
      logger.error(`Error fetching product sales history for product ${productId} in store ${storeId}:`, error);
      // Return an empty array or re-throw, depending on desired error handling in UI
      return [];
    }
  }
};

// Feedback submission data interface
export interface FeedbackSubmissionData {
  feedback_text: string;
  likes: number;
  dislikes: number;
  source: string;
}

// Add CategorySummary interface (based on mongocollections.md)
export interface CategorySummary {
  total_products_with_categories?: number;
  unique_categories?: number;
  unique_subcategories?: number;
  top_categories?: Array<{ name: string; product_count: number }>;
  top_featured_categories?: Array<{ name: string; featured_count: number }>;
  top_sale_categories?: Array<{ name: string; sale_count: number }>;
  category_distribution?: Record<string, number>;
} 
