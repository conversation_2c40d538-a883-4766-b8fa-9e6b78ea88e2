/**
 * Service for storing and retrieving daily Meta/Instagram metrics
 */
import httpClient from '../config/axios';
import { logger } from '../utils/logger';

export interface DailyMetricData {
  date: Date;
  value: number;
  metadata?: Record<string, unknown>;
}

export interface MetricsTrendData {
  metric_type: string;
  period_days: number;
  first_value: number;
  last_value: number;
  growth_rate: number;
  average: number;
  avg_daily_change: number;
  total_change: number;
  data_points: number;
}

export class MetaDailyMetricsService {
  /**
   * Store daily metrics for a specific page
   */
  static async storeDailyMetrics(
    storeId: string,
    pageId: string,
    platform: 'facebook' | 'instagram',
    metricsData: Record<string, DailyMetricData[]>
  ): Promise<boolean> {
    try {
      const response = await httpClient.post(
        `/api/meta/store/${storeId}/daily-metrics/store`,
        {
          page_id: pageId,
          platform,
          metrics_data: metricsData
        }
      );
      
      logger.debug(`Stored daily metrics for ${platform} page ${pageId}:`, response.data);
      return response.data.success || false;
    } catch (error) {
      logger.error('Error storing daily metrics:', error);
      return false;
    }
  }

  /**
   * Retrieve daily metrics for a specific page and date range
   */
  static async getDailyMetrics(
    storeId: string,
    pageId: string,
    platform: 'facebook' | 'instagram',
    startDate: Date,
    endDate: Date
  ): Promise<Record<string, unknown[]>> {
    try {
      const response = await httpClient.get(
        `/api/meta/store/${storeId}/daily-metrics/${pageId}`,
        {
          params: {
            platform,
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString()
          }
        }
      );
      
      logger.debug(`Retrieved daily metrics for ${platform} page ${pageId}:`, response.data);
      return response.data.metrics || {};
    } catch (error) {
      logger.error('Error retrieving daily metrics:', error);
      return {};
    }
  }

  /**
   * Get trend analysis for specific metrics
   */
  static async getMetricsTrends(
    storeId: string,
    pageId: string,
    platform: 'facebook' | 'instagram',
    metricType: string,
    days: number = 30
  ): Promise<MetricsTrendData | null> {
    try {
      const response = await httpClient.get(
        `/api/meta/store/${storeId}/daily-metrics/${pageId}/trends`,
        {
          params: {
            platform,
            metric_type: metricType,
            days
          }
        }
      );
      
      logger.debug(`Retrieved trends for ${metricType}:`, response.data);
      return response.data.trends || null;
    } catch (error) {
      logger.error('Error retrieving metrics trends:', error);
      return null;
    }
  }

  /**
   * Store a single metric insight as daily data
   */
  static async storeMetricInsight(
    storeId: string,
    pageId: string,
    platform: 'facebook' | 'instagram',
    metricType: string,
    value: number,
    date: Date,
    metadata?: Record<string, unknown>
  ): Promise<boolean> {
    try {
      const metricsData = {
        [metricType]: [{
          date,
          value,
          metadata: metadata || {}
        }]
      };
      
      return await this.storeDailyMetrics(storeId, pageId, platform, metricsData);
    } catch (error) {
      logger.error('Error storing metric insight:', error);
      return false;
    }
  }

  /**
   * Convert time series insights to daily metrics format
   */
  static convertInsightsToDailyMetrics(
    insights: { end_time: string; value: number | Record<string, number> | (number | Record<string, number>)[] | Record<string, (number | Record<string, number>)[]>; name?: string }[],
    metricType: string
  ): Record<string, DailyMetricData[]> {
    try {
      const dailyData: DailyMetricData[] = insights.map(insight => ({
        date: new Date(insight.end_time),
        value: typeof insight.value === 'number' ? insight.value : 0,
        metadata: {
          source: 'api',
          metric_name: insight.name || metricType,
          original_end_time: insight.end_time
        }
      }));

      return { [metricType]: dailyData };
    } catch (error) {
      logger.error('Error converting insights to daily metrics:', error);
      return {};
    }
  }

  /**
   * Check if we have recent data for a metric (within last 24 hours)
   */
  static async hasRecentData(
    storeId: string,
    pageId: string,
    platform: 'facebook' | 'instagram',
    metricType: string
  ): Promise<boolean> {
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const today = new Date();
      
      const metrics = await this.getDailyMetrics(
        storeId,
        pageId,
        platform,
        yesterday,
        today
      );
      
      return metrics[metricType] && metrics[metricType].length > 0;
    } catch (error) {
      logger.error('Error checking for recent data:', error);
      return false;
    }
  }
}