# Suite de Pruebas de Autenticación

Este directorio contiene las pruebas unitarias y de integración que cubren los flujos de **login**, **2FA** y **registro/ verificación** para la API de autenticación (`routes.auth`). Las pruebas utilizan **FastAPI TestClient** junto con una base de datos MongoDB simulada mediante **mongomock** para evitar dependencias externas.

## Estructura

```
backend/tests/
 └── auth/
     ├── conftest.py          # Fixtures de BD, correo y cliente
     ├── utils.py             # Wrappers async para mongomock
     ├── test_login.py        # Casos de login y 2FA
     └── test_registration.py # Casos de registro y verificación
```

## Dependencias

Las dependencias necesarias están listadas en `backend/requirements-test.txt` e incluyen, entre otras:

- `pytest`
- `pytest-asyncio`
- `fastapi[test]`
- `httpx`
- `mongomock`

Instalación recomendada en un entorno virtual:

```bash
pip install -r backend/requirements-test.txt
```

## Ejecución

Para ejecutar únicamente esta suite:

```bash
pytest backend/tests/auth -q
```

Para ejecutar todas las pruebas del backend:

```bash
pytest backend/tests -q
```

## Cobertura

Los tests validan:

1. Login exitoso y generación de JWT.
2. Errores de autenticación (contraseña incorrecta, cuenta bloqueada).
3. Flujo completo de 2FA (envío de código y verificación).
4. Registro de nuevo usuario, validaciones de contraseña, usuario inexistente o ya registrado.
5. Verificación de registro con código correcto, erróneo y expirado.

Todos los flujos utilizan la lógica real de hashing y generación de tokens para garantizar coherencia con el entorno de producción. 