import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  Tabs, 
  Tab, 
  CircularProgress, 
  Alert,
  Button,
  Paper,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent
} from '@mui/material';
import { MetaPage, AdvancedCorrelationInsight, ForecastInsight } from '../../services/types';
import { CorrelationHeatmap } from '../charts/CorrelationHeatmap';
// [D-Unit] ForecastChart import is commented out for now—preserved for future analytics features.
// import ForecastChart from './ForecastChart';
import { InsightCard } from './InsightCard';
// [D-Unit] useTranslation import is commented out for now—preserved for future i18n support.
// import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';

interface MetaAdvancedAnalyticsPanelProps {
  page: MetaPage;
  useMockData?: boolean;
}

// Add interfaces for the metric data
interface HistoricalDataItem {
  date: string;
  value: number;
}

interface MetricResponse {
  date: string;
  value: number;
}

/**
 * Component for displaying advanced analytics for Meta data
 */
export const MetaAdvancedAnalyticsPanel: React.FC<MetaAdvancedAnalyticsPanelProps> = ({ 
  page,
  useMockData = false
}) => {
  const [tabValue, setTabValue] = useState<number>(0);
  const [correlationInsights, setCorrelationInsights] = useState<AdvancedCorrelationInsight[]>([]);
  const [forecastInsights, setForecastInsights] = useState<ForecastInsight[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form states for generating new insights
  const [metricType, setMetricType] = useState<string>('engagement');
  const [forecastPeriods, setForecastPeriods] = useState<number>(30);
  const [timeLag, setTimeLag] = useState<number>(0);
  const [segment, setSegment] = useState<string>('');
  
  // Fetch advanced correlation insights
  const fetchAdvancedCorrelationInsights = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (useMockData) {
        // Generate mock correlation insights
        const mockCorrelationInsights: AdvancedCorrelationInsight[] = [
          {
            id: `mock-correlation-1-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "advanced_correlation",
            title: "Engagement to Sales Correlation",
            text: "Strong positive correlation (0.78) between post engagement and sales within 2 days. Posts with product demonstrations show the highest correlation.",
            confidence: 0.92,
            created_at: new Date().toISOString(),
            recommendations: [
              "Focus on product demonstration content",
              "Schedule posts 2 days before promotional events",
              "Increase engagement by asking questions in posts"
            ],
            correlation_data: {
              dimensions: ["likes", "comments", "shares", "sales"],
              values: [
                [1.0, 0.65, 0.48, 0.78],
                [0.65, 1.0, 0.72, 0.63],
                [0.48, 0.72, 1.0, 0.59],
                [0.78, 0.63, 0.59, 1.0]
              ],
              time_lag: 2,
              segment: "all"
            },
            is_mock: true,
            // Required properties from AdvancedCorrelationInsight interface
            insight_type: "advanced_correlation",
            insight_text: "Strong positive correlation (0.78) between post engagement and sales within 2 days. Posts with product demonstrations show the highest correlation.",
            timestamp: new Date().toISOString(),
            source_data_type: "correlation",
            source_data_id: page.id,
            correlation_metrics: {
              "engagement_sales": {
                "correlation": 0.78,
                "p_value": 0.001
              }
            },
            time_lag: 2
          }
        ];
        
        setCorrelationInsights(mockCorrelationInsights);
      } else {
        // Fetch real correlation insights
        const response = await fetch(`/api/insights/advanced-correlation?page_id=${page.id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch advanced correlation insights');
        }
        
        const data = await response.json();
        setCorrelationInsights(data);
      }
    } catch (err) {
      logger.error('Error fetching advanced correlation insights:', err);
      setError('Failed to fetch correlation insights. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [page, useMockData]);
  
  // Fetch forecast insights
  const fetchForecastInsights = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (useMockData) {
        // Generate mock forecast insights
        const mockForecastInsights: ForecastInsight[] = [
          {
            id: `mock-forecast-1-${page.id}`,
            page_id: page.id,
            store_id: "mock-store",
            type: "forecast",
            title: "Engagement Forecast",
            text: "Engagement is predicted to increase by 15% over the next 30 days, with peak engagement expected around day 12-15.",
            confidence: 0.85,
            created_at: new Date().toISOString(),
            recommendations: [
              "Plan major announcements around days 12-15",
              "Increase posting frequency during predicted high-engagement periods",
              "Prepare content calendar to capitalize on the upward trend"
            ],
            forecast_data: {
              metric: "engagement",
              historical: Array.from({ length: 30 }, (_, i) => ({
                date: new Date(Date.now() - (30 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                value: 100 + Math.sin(i / 5) * 30 + i * 2
              })),
              forecast: Array.from({ length: 30 }, (_, i) => ({
                date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                value: 160 + Math.sin((i + 30) / 5) * 30 + i * 2,
                lower_bound: 160 + Math.sin((i + 30) / 5) * 30 + i * 2 - 20 - i,
                upper_bound: 160 + Math.sin((i + 30) / 5) * 30 + i * 2 + 20 + i
              })),
              accuracy: 0.85,
              trend: "increasing"
            },
            is_mock: true,
            // Required properties from ForecastInsight interface
            insight_type: "forecast",
            insight_text: "Engagement is predicted to increase by 15% over the next 30 days, with peak engagement expected around day 12-15.",
            timestamp: new Date().toISOString(),
            source_data_type: "metrics",
            source_data_id: page.id,
            metric_name: "engagement",
            forecast_values: Array.from({ length: 30 }, (_, i) => ({
              date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              value: 160 + Math.sin((i + 30) / 5) * 30 + i * 2
            })),
            forecast_period: "30d",
            forecast_accuracy: 0.85
          }
        ];
        
        setForecastInsights(mockForecastInsights);
      } else {
        // Fetch real forecast insights
        const response = await fetch(`/api/insights/forecast?page_id=${page.id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch forecast insights');
        }
        
        const data = await response.json();
        setForecastInsights(data);
      }
    } catch (err) {
      logger.error('Error fetching forecast insights:', err);
      setError('Failed to fetch forecast insights. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [page, useMockData]);
  
  // Generate advanced correlation insights
  const generateAdvancedCorrelation = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch Meta data
      const postsResponse = await fetch(`/api/meta/posts?page_id=${page.id}`);
      if (!postsResponse.ok) {
        throw new Error('Failed to fetch posts');
      }
      const posts = await postsResponse.json();
      
      // Fetch engagement metrics
      const engagementResponse = await fetch(`/api/meta/engagement?page_id=${page.id}`);
      if (!engagementResponse.ok) {
        throw new Error('Failed to fetch engagement metrics');
      }
      const engagement = await engagementResponse.json();
      
      // Fetch audience data
      const audienceResponse = await fetch(`/api/meta/followers?page_id=${page.id}`);
      if (!audienceResponse.ok) {
        throw new Error('Failed to fetch audience data');
      }
      const audience = await audienceResponse.json();
      
      // Fetch store data
      const storeResponse = await fetch(`/api/store/sales`);
      if (!storeResponse.ok) {
        throw new Error('Failed to fetch store data');
      }
      const sales = await storeResponse.json();
      
      // Fetch product data
      const productsResponse = await fetch(`/api/store/products`);
      if (!productsResponse.ok) {
        throw new Error('Failed to fetch product data');
      }
      const products = await productsResponse.json();
      
      // Prepare data for correlation analysis
      const metaData = {
        posts,
        engagement,
        audience
      };
      
      const storeData = {
        sales,
        products
      };
      
      // Generate advanced correlation insights
      const correlationResponse = await fetch('/api/insights/advanced-correlation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meta_data: metaData,
          store_data: storeData,
          time_lag: timeLag,
          segment: segment || undefined,
          dimensions: ['engagement', 'likes', 'comments', 'shares']
        }),
      });
      
      if (!correlationResponse.ok) {
        throw new Error('Failed to generate advanced correlation insights');
      }
      
      // Refresh insights
      await fetchAdvancedCorrelationInsights();
    } catch (err) {
      logger.error('Error generating advanced correlation insights:', err);
      setError('Failed to generate advanced correlation insights. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Generate forecast insights
  const generateForecast = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch historical data based on metric type
      let historicalData: HistoricalDataItem[] = [];
      
      if (metricType === 'engagement') {
        const response = await fetch(`/api/meta/engagement/history?page_id=${page.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch engagement history');
        }
        const data = await response.json();
        
        // Transform data to the required format
        historicalData = data.map((item: MetricResponse) => ({
          date: item.date,
          value: item.value
        }));
      } else if (metricType === 'followers') {
        const response = await fetch(`/api/meta/followers/history?page_id=${page.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch followers history');
        }
        const data = await response.json();
        
        // Transform data to the required format
        historicalData = data.map((item: MetricResponse) => ({
          date: item.date,
          value: item.value
        }));
      } else if (metricType === 'sales') {
        const response = await fetch(`/api/store/sales/history`);
        if (!response.ok) {
          throw new Error('Failed to fetch sales history');
        }
        const data = await response.json();
        
        // Transform data to the required format
        historicalData = data.map((item: MetricResponse) => ({
          date: item.date,
          value: item.value
        }));
      }
      
      // Generate forecast insights
      const forecastResponse = await fetch('/api/insights/forecast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric_type: metricType,
          historical_data: historicalData,
          forecast_periods: forecastPeriods,
          confidence_level: 0.95
        }),
      });
      
      if (!forecastResponse.ok) {
        throw new Error('Failed to generate forecast insights');
      }
      
      // Refresh insights
      await fetchForecastInsights();
    } catch (err) {
      logger.error('Error generating forecast insights:', err);
      setError('Failed to generate forecast insights. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle form changes
  const handleMetricTypeChange = (event: SelectChangeEvent) => {
    setMetricType(event.target.value);
  };
  
  const handleForecastPeriodsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setForecastPeriods(parseInt(event.target.value, 10));
  };
  
  const handleTimeLagChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTimeLag(parseInt(event.target.value, 10));
  };
  
  const handleSegmentChange = (event: SelectChangeEvent) => {
    setSegment(event.target.value);
  };
  
  // Fetch correlation insights when component mounts
  useEffect(() => {
    fetchAdvancedCorrelationInsights();
  }, [fetchAdvancedCorrelationInsights]);

  // Fetch forecast insights when component mounts
  useEffect(() => {
    fetchForecastInsights();
  }, [fetchForecastInsights]);
  
  return (
    <Box>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange} 
          aria-label="advanced analytics tabs"
        >
          <Tab label="Advanced Correlation" />
          <Tab label="Forecasting" />
        </Tabs>
      </Box>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Box>
          {/* Advanced Correlation Tab */}
          {tabValue === 0 && (
            <Box>
              <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant="h6" gutterBottom>Generate Advanced Correlation Analysis</Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={4}>
                    <TextField
                      label="Time Lag (days)"
                      type="number"
                      fullWidth
                      value={timeLag}
                      onChange={handleTimeLagChange}
                      inputProps={{ min: 0, max: 30 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Segment</InputLabel>
                      <Select
                        value={segment}
                        label="Segment"
                        onChange={handleSegmentChange}
                      >
                        <MenuItem value="">None</MenuItem>
                        <MenuItem value="product_category">Product Category</MenuItem>
                        <MenuItem value="customer_segment">Customer Segment</MenuItem>
                        <MenuItem value="post_type">Post Type</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth
                      onClick={generateAdvancedCorrelation}
                      disabled={loading}
                    >
                      Generate Correlation Analysis
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
              
              <Grid container spacing={2}>
                {correlationInsights.length > 0 ? (
                  correlationInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                      {/* Conditionally render the heatmap */}
                      {insight.insight_type === "advanced_correlation" && insight.correlation_metrics && (
                        <Box sx={{ mt: 2 }}>
                          <CorrelationHeatmap 
                            correlationData={insight.correlation_metrics} 
                            title={`Correlation Heatmap for ${insight.title}`}
                          />
                        </Box>
                      )}
                    </Grid>
                  ))
                ) : (
                  <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      No advanced correlation insights available yet. Click "Generate Correlation Analysis" to analyze the relationship between Meta metrics and sales data.
                    </Typography>
                  </Paper>
                )}
              </Grid>
            </Box>
          )}
          
          {/* Forecasting Tab */}
          {tabValue === 1 && (
            <Box>
              <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant="h6" gutterBottom>Generate Forecast</Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Metric Type</InputLabel>
                      <Select
                        value={metricType}
                        label="Metric Type"
                        onChange={handleMetricTypeChange}
                      >
                        <MenuItem value="engagement">Engagement</MenuItem>
                        <MenuItem value="followers">Followers</MenuItem>
                        <MenuItem value="sales">Sales</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      label="Forecast Periods (days)"
                      type="number"
                      fullWidth
                      value={forecastPeriods}
                      onChange={handleForecastPeriodsChange}
                      inputProps={{ min: 7, max: 90 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth
                      onClick={generateForecast}
                      disabled={loading}
                    >
                      Generate Forecast
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
              
              <Grid container spacing={2}>
                {forecastInsights.length > 0 ? (
                  forecastInsights.map((insight) => (
                    <Grid item xs={12} md={6} key={insight.id}>
                      <InsightCard insight={insight} />
                    </Grid>
                  ))
                ) : (
                  <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      No forecast insights available yet. Click "Generate Forecast" to predict future values for the selected metric.
                    </Typography>
                  </Paper>
                )}
              </Grid>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
}; 