{"greeting": "<PERSON><PERSON>", "dashboardTitle": "<PERSON><PERSON>", "loginButton": "<PERSON><PERSON><PERSON>", "language": "Idioma", "english": "Inglés", "spanish": "Español", "visualizadorDeTasas": "Visualizador de Tasas", "welcomeBack": "Bienvenido de Nuevo", "signInPrompt": "Inicia sesión en tu cuenta D-Unit", "registerNote": "(Regístrate primero con tu cuenta de La Nube)", "emailLabel": "Dirección de Correo Electrónico", "passwordLabel": "Contraseña", "forgotPasswordLink": "¿Olvidaste tu contraseña?", "orDivider": "O", "continueWithGoogle": "Continuar con <PERSON>", "continueWithMeta": "<PERSON><PERSON><PERSON><PERSON> con <PERSON>", "noAccountPrompt": "¿No tienes una cuenta?", "signUpLink": "Regístrate", "errorGoogleNoCredential": "No se recibió credencial de Google", "errorGoogleLoginFailedGeneral": "Error al iniciar sesión con Google. Inténtalo de nuevo.", "errorGoogleLoginFailedTryAgain": "Error de inicio de sesión de Google. Inténtalo de nuevo o usa email/contraseña.", "errorEnterEmailPassword": "Por favor, ingresa correo y contraseña", "errorInvalidEmailPassword": "Correo o contraseña inválidos", "errorLoginGeneral": "Ocurrió un error durante el inicio de sesión. Inténtalo de nuevo.", "errorFacebookNoToken": "No se pudo obtener el token de acceso de Facebook", "errorFacebookLoginFailed": "Error al iniciar sesión con Facebook. Inténtalo de nuevo.", "connectingStatus": "Conectando...", "twoFAInfo": "Por favor, ingresa el código de verificación enviado a tu correo.", "twoFACodeLabel": "Código de Verificación", "twoFAButton": "Verificar Código", "error2FANoCode": "Por favor, ingresa el código de verificación", "error2FANoToken": "No se recibió token de acceso de la verificación 2FA", "error2FAFailed": "Error al verificar el código. Inténtalo de nuevo.", "termsAndConditions": "Términos y Condiciones", "privacyPolicy": "Política de Privacidad", "poweredBy": "Impulsado por", "lightMode": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON>", "errorEnterEmail": "Por favor, ingresa tu dirección de correo electrónico", "errorInvalidEmail": "Por favor, ingresa una dirección de correo electrónico válida", "successPasswordSent": "Se ha enviado una nueva contraseña a tu correo electrónico. Por favor revisa tu bandeja de entrada.", "errorEmailNotFoundLaNube": "Correo electrónico no encontrado. Asegúrate de que estás usando tu correo electrónico de La Nube.", "errorRegisterFirst": "Primero necesitas registrarte en D-Unit. Por favor usa la página de <1>Registro</1> o Continúa con Google para crear tu cuenta D-Unit.", "errorInvalidEmailFormat": "Formato de correo electrónico inválido", "errorInvalidEmailFormatCheck": "Formato de correo electrónico inválido. Por favor revisa tu correo e inténtalo de nuevo.", "errorForgotPasswordGeneral": "Error al procesar la solicitud. Por favor, inténtalo de nuevo más tarde.", "forgotPasswordTitle": "¿Olvidaste tu contraseña?", "forgotPasswordSubtitle": "Ingresa tu dirección de correo electrónico de La Nube y te enviaremos una nueva contraseña.", "continueButton": "<PERSON><PERSON><PERSON><PERSON>", "backToLoginLink": "Volver al Inicio de Sesión", "errorPasswordLength": "La contraseña debe tener al menos 8 caracteres", "errorPasswordUppercase": "La contraseña debe contener al menos una letra mayúscula", "errorPasswordNumber": "La contraseña debe contener al menos un número", "errorPasswordSymbol": "La contraseña debe contener al menos un símbolo", "errorPasswordsDontMatch": "Las contraseñas no coinciden", "errorRegisterEmailNotFoundLaNube": "Correo electrónico no encontrado. Debes ser usuario de La Nube para registrarte en D-Unit.", "errorRegisterAlreadyRegistered": "Ya estás registrado en D-Unit. Por favor, inicia sesión.", "errorRegistrationFailed": "Registro fallido", "errorVerificationFailed": "Verificación fallida", "successRegistration": "¡Registro exitoso! Redirigiendo...", "registerTitle": "Registro de D-Unit", "registerSubtitle": "Solo para usuarios existentes de La Nube", "goToLoginLink": "<PERSON>r a Inicio de Sesión", "registerLabelLaNubeEmail": "Correo Electrónico de La Nube", "registerLabelName": "Tu Nombre", "registerLabelStoreName": "Nombre de la Tienda", "registerPasswordRequirements": "Requisitos de la contraseña:", "registerReqLength": "Mínimo 8 caracteres", "registerReqUppercase": "Al menos 1 letra mayúscula", "registerReqNumber": "Al menos 1 número", "registerReqSymbol": "Al menos 1 símbolo", "registerLabelPassword": "Contraseña de D-Unit", "registerLabelConfirmPassword": "Confirmar <PERSON>ña de D-Unit", "registerButton": "Registrarse", "registerAlreadyHaveAccount": "¿Ya tienes una cuenta?", "signInLink": "In<PERSON><PERSON>", "registerVerifyTitle": "Verifica Tu Correo Electrónico", "registerVerifySubtitle": "Por favor, ingresa el código de verificación enviado a tu correo electrónico.", "registerLabelVerificationCode": "Código de Verificación", "registerVerifyButton": "Verificar Cuenta", "adminLogin": {"title": "Inicio de Sesión del Portal de Administración", "subtitle": "Inicia sesión para acceder al Portal de Administración.", "usernameLabel": "Nombre de usuario", "passwordLabel": "Contraseña", "signInButton": "<PERSON><PERSON><PERSON>", "togglePasswordVisibility": "alternar visibilidad de contraseña", "errorRequired": "Se requieren correo electrónico y contraseña.", "errorUnexpected": "Ocurrió un error inesperado durante el inicio de sesión.", "errorNoAdminPrivileges": "Inicio de sesión exitoso, pero no tienes privilegios de administrador.", "errorInvalidCredentials": "Nombre de usuario o contraseña inválidos.", "errorLoginFailedFallback": "Error al iniciar sesión. Por favor, verifica tus credenciales."}, "metaDashboard": {"connectMetaTitle": "Conecta Tu Cuenta de Meta", "loginViaMain": "Para acceder a los análisis de Meta, primero conecta tu cuenta de Meta a través de nuestra página principal de inicio de sesión.", "goToLoginButton": "Ir a la Página de Inicio de Sesión", "pageTitle": "Panel de Meta", "title": "PANEL DE ANALÍTICAS DE META", "promptLogin": "Inicia sesión con tu cuenta de Meta para acceder a las analíticas de tus páginas de negocio y cuentas de Instagram.", "errorReloginFailed": "Error al re-autenticar con Meta. Por favor, intenta refrescar la página y asegúrate de que las cookies estén habilitadas.", "errorAuthFailedGeneral": "Autenticación fallida. Por favor, inicia sesión de nuevo.", "errorAuthFailedRetry": "Autenticación fallida. Por favor, inicia sesión de nuevo.", "loginAgainButton": "Iniciar <PERSON>", "disconnectButton": "Desconectar de Meta", "errorDisconnectFailed": "Error al desconectar de Meta.", "loading": {"initializing": "Inicializando Conexión Meta...", "fetchingAccounts": "Obteniendo Cuentas de Negocio...", "fetchingData": "Obteniendo Datos de Negocio...", "general": "Cargando Datos de Meta...", "description": "Verificando el estado de autenticación y cargando tus datos de Meta...", "title": "Cargando Panel de Meta", "main": "Cargando Panel de Meta"}, "errorRateLimit": "Límite de solicitudes API alcanzado. Por favor, inténtalo de nuevo en un minuto.", "errorPartialFetch": "No se pudieron obtener algunas cuentas, pero se muestran los datos disponibles.", "errorNoBusinessAccounts": "No se encontraron cuentas de negocio. Es posible que debas crear una cuenta de negocio de Meta primero. Inicia sesión nuevamente.", "errorNoPagesFound": "No se encontraron páginas. Por favor, conecta una página de Facebook o cuenta de Instagram para continuar.", "errorFetchPagesFallback": "Error al obtener las páginas. Por favor, intenta refrescar.", "errorRefreshPermissions": "Error al refrescar los permisos.", "selectPageLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noPagesAvailableOption": "No hay páginas disponibles", "refreshPagesButton": "<PERSON><PERSON><PERSON><PERSON>", "refreshPermissionsButton": "<PERSON><PERSON><PERSON><PERSON>", "successPermissionsRefreshed": "Permisos refrescados con éxito.", "warningMockData": "Usando datos de prueba debido a problemas de conexión.", "altEmailInfo": "¿Quieres usar un correo diferente para iniciar sesión en Meta? Puedes seleccionar tu correo preferido en la página de Configuración.", "pageHeader": "<PERSON><PERSON><PERSON><PERSON>", "categoryHeader": "Categoría", "idHeader": "ID", "noAccounts": {"title": "No hay Cuenta de Negocio de Meta Conectada", "description": "Para empezar, por favor conecta tu cuenta de Meta Business. Esto permitirá a D-Unit acceder a tus páginas y cuentas publicitarias.", "connectButton": "Conectar Cuenta de Negocio", "goToSettingsButton": "Ir a Configuración de Meta Business"}, "connect": {"title": "Conectar Cuenta de Negocio de Meta", "noBusinessAccounts": "No se encontraron cuentas de negocio. Es posible que debas crear una cuenta de negocio de Meta primero. Inicia sesión nuevamente.", "continueWithMeta": "<PERSON><PERSON><PERSON><PERSON> con <PERSON>", "info": "Esto permitirá a D-Unit acceder a tu página de Facebook y cuentas comerciales de Instagram para obtener información y datos analíticos. Te pediremos permisos específicos en el siguiente paso.", "createBusinessAccount": "<PERSON><PERSON><PERSON> de Negocio", "refreshAccounts": "<PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"overview": "Resumen", "posts": "Publicaciones", "audience": "Audiencia", "adMetricsTab": "Métricas de Anuncios", "campaigns": "Campañas", "permissions": "<PERSON><PERSON><PERSON>", "aiInsights": "Perspectivas IA"}, "overview": {"pageTitle": "Resumen de Página", "instagramTitle": "Resumen de Instagram", "pageSubtitle": "Métricas clave para tu página de Facebook en los últimos 28 días", "instagramSubtitle": "Métricas clave para tu perfil de Instagram (datos del período y actuales)", "impressionsTitle": "Impresiones", "impressionsDesc": "Número total de veces que se vio tu contenido de Instagram", "impressionsDescFB": "Número total de veces que se vio el contenido de tu página", "reachTitle": "Alcance", "reachDesc": "Número total de cuentas únicas que vieron tu contenido de Instagram", "reachDescFB": "Número total de personas únicas que vieron tu contenido de Facebook", "engagementTitle": "Interacción", "engagementDescIG": "Número total de interacciones con tu contenido de Instagram (me gusta, comentarios, guardados)", "engagementDescFB": "Número total de acciones realizadas en tu página (clics, me gusta, comentarios, compartidos)", "followersTitle": "<PERSON><PERSON><PERSON><PERSON>", "followersDesc": "Seguidores totales de tu perfil de Instagram", "profileViewsTitle": "Visitas al Perfil", "profileViewsDesc": "Número total de veces que se vio tu perfil de Instagram", "savedTitle": "Publicaciones Guardadas", "savedDesc": "Número total de veces que los usuarios guardaron tus publicaciones de Instagram", "mediaCountTitle": "Publicaciones Creadas", "mediaCountDesc": "Número total de publicaciones creadas en el período seleccionado", "pageLikesTitle": "Se<PERSON><PERSON><PERSON> de Página", "pageLikesDesc": "Número total de personas a quienes les gusta tu página", "pageViewsTitle": "Vistas de Página", "pageViewsDescFB": "Número total de veces que se vio tu página", "lowDataWarning": "Las métricas pueden ser cero si la Página tiene menos de 100 'Me gusta', o si no hay datos suficientes para el período seleccionado. Por favor, verifica el tamaño de la audiencia de tu Página e intenta con un rango de tiempo diferente si es aplicable.", "reconnectPrompt": "Por favor, reconecta tu cuenta de Facebook para ver las métricas"}, "posts": {"instagramTitle": "Analíticas de Publicaciones de Instagram", "facebookTitle": "Analítica de Publicaciones de Facebook", "analyticsTitle": "Analíticas de Publicaciones de Instagram", "totalPostsLabel": "Publicaciones Totales", "avgEngagementLabel": "Interacción Prom.", "periodLabel": "<PERSON><PERSON><PERSON>", "engagementDistributionTitle": "Distribución de Interacción", "postTypeDistributionTitle": "Distribución por Tipo de Publicación", "likesLabel": "<PERSON>", "commentsLabel": "Comentarios", "savedLabel": "Guardados", "sharesLabel": "Compartidos", "noEngagementData": "No hay datos de interacción disponibles", "noPostTypeData": "No hay datos de tipo de publicación disponibles", "topPerformingPostsTitle": "Publicaciones con Mejor Rendimiento", "tableHeaderPost": "Publicación", "tableHeaderDate": "<PERSON><PERSON>", "tableHeaderType": "Tipo", "tableHeaderLikes": "<PERSON>", "tableHeaderComments": "Comentarios", "tableHeaderSaved": "Guardados", "tableHeaderShares": "Compartidos", "tableHeaderTotal": "Total", "tableHeaderLink": "Enlace", "openPostTooltip": "Abrir publicación", "noPostsInRange": "No se encontraron publicaciones en el rango de fechas seleccionado ({{startDate}} - {{endDate}}). Intenta ajustar el período de tiempo o verifica que la cuenta haya publicado contenido durante este período.", "noTopPerformingPosts": "No hay publicaciones disponibles para el período seleccionado.", "errorLoad": "Error al cargar los datos de publicaciones de {{platform}}. Por favor, inténtalo de nuevo más tarde.", "permissionError": "Error de permisos: Por favor, actualiza tus permisos de Meta e inténtalo de nuevo.", "rateLimitError": "Límite de solicitudes de API de Facebook alcanzado. Por favor, espera e inténtalo de nuevo más tarde.", "type": {"photo": "Foto", "video": "Video", "status": "Estado", "link": "Enlace", "album": "<PERSON>l<PERSON><PERSON>", "event": "Evento", "note": "<PERSON>a", "offer": "<PERSON><PERSON><PERSON>", "image": "Imagen", "unknown": "Desconocido"}}, "audience": {"demographicsTitle": "Demografía de la Audiencia", "totalAudience": "Audiencia Total", "ageGenderTab": "<PERSON><PERSON> y Género", "countriesTab": "Países", "citiesTab": "Ciudades", "interestsTab": "Intereses", "ageDistributionTitle": "Distribución por Edad", "genderDistributionTitle": "Distribución por Género", "legendMale": "<PERSON><PERSON><PERSON><PERSON>", "legendFemale": "Femenino", "noDemographicsData": "No hay datos demográficos de audiencia disponibles para esta página.", "insufficientFollowers": "Los datos de audiencia no están disponibles. Esto generalmente requiere un número mínimo de seguidores (por ejemplo, 100 para Instagram).", "permissionDenied": "Permiso denegado para acceder a la demografía de la audiencia.", "authFailed": "Autenticación fallida. Por favor, vuelve a iniciar sesión en Meta.", "rateLimit": "Límite de solicitudes API alcanzado al obtener datos de audiencia.", "failedToLoad": "Error al cargar los datos demográficos de la audiencia.", "instagramDemographicsNote": "Los datos demográficos de audiencia de Instagram requieren una cuenta comercial con más de 100 seguidores y permisos específicos. Esta función puede no estar disponible para todas las cuentas.", "basicDataOnly": "Mostrando recuento básico de seguidores. Los datos demográficos detallados requieren permisos adicionales de Instagram.", "audienceTotal": "Audiencia Total", "period": "<PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "noAgeData": "No hay datos de edad disponibles", "noGenderData": "No hay datos de género disponibles", "tabAgeGender": "<PERSON><PERSON> y Género", "tabCountries": "Países", "tabCities": "Ciudades", "tabInterests": "Intereses"}, "adMetrics": {"title": "Métricas de Anuncios", "dataFromCache": "<PERSON><PERSON> de caché: {{date}}", "accountSummary": "Resumen de la Cuenta", "totalSpend": "Gasto Total", "impressions": "Impresiones", "clicks": "C<PERSON>s", "conversions": "Conversiones", "ctr": "CTR", "cpc": "CPC", "costPerConversion": "Costo por Conversión", "roas": "ROAS", "dateRange": "<PERSON><PERSON>", "showingMetrics": "Mostrando métricas para el rango de fechas seleccionado", "campaignsTab": "Campañas", "performanceTab": "Rendimiento", "roiAnalysisTab": "Análisis ROI", "campaign": "Campaña", "status": "Estado", "spend": "Gasto", "noCampaignData": "No hay datos de campañas disponibles", "noPerformanceData": "No hay datos de rendimiento diario disponibles", "noRoiData": "No hay datos de análisis ROI disponibles", "panelTitle": "Métricas de Anuncios", "includeOrganicData": "Incluir <PERSON>", "refreshNow": "<PERSON><PERSON><PERSON><PERSON>", "errorPageMissing": "Falta la información de la página.", "errorStoreIdMissing": "Falta el ID de la tienda.", "errorNoDataAfterRetries": "No hay datos de métricas disponibles después de reintentos.", "errorAdAccountAccessGeneric": "No se pudo acceder a la cuenta publicitaria. Por favor, verifica tu configuración.", "errorAdAccountAccessNoAccount": "No hay cuenta publicitaria vinculada a esta página/usuario.", "errorAdAccountAccessPermissions": "<PERSON><PERSON>tan permisos de cuenta publicitaria o son insuficientes.", "errorAdAccountAccessOther": "Error de Cuenta Publicitaria: {{message}}", "errorFetchFailed": "Error al obtener las métricas de anuncios. Por favor, inténtalo de nuevo más tarde.", "currencySymbolFallback": "$", "dateRangeLifetime": "<PERSON><PERSON> siempre", "dateRangeToday": "Hoy", "dateRangeYesterday": "Ayer", "dateRangeLastXDays": "Últimos {{count}} <PERSON><PERSON>", "dateRangeLastXWeeks": "Últimas {{count}} Semanas", "dateRangeLastXMonths": "<PERSON><PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON>", "dateRangeLastXYear": "<PERSON>lt<PERSON>", "dateRangeCustomPrefix": "<PERSON><PERSON>: ", "tableHeaderCampaign": "Campaña", "tableHeaderStatus": "Estado", "tableHeaderSpend": "Gasto", "tableHeaderImpressions": "Impresiones", "tableHeaderClicks": "C<PERSON>s", "tableHeaderConversions": "Conversiones", "tableHeaderCtr": "CTR", "tableHeaderCpc": "CPC", "tableHeaderCostPerConv": "Costo / Conv.", "tableHeaderRoas": "ROAS"}, "campaigns": {"title": "Comparación de Campañas", "errorLoading": "Error al cargar datos de la campaña: {{errorDetails}}", "errorNotFound": "Error al cargar datos de la campaña: No encontrado", "labelCampaignType": "Tipo de Campaña", "placeholderCampaignType": "Seleccione un Tipo de Campaña", "labelSelectCampaign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderSelectCampaign": "Seleccione una Campaña", "labelStatus": "Estado", "labelObjective": "Objetivo", "labelBudget": "Presupuesto", "labelBudgetPerDay": "{{amount}}/día", "labelConversionSalesCorrelation": "Correlación de Conversión y Ventas", "labelCorrelationCoefficient": "Coeficiente de Correlación", "labelImpactOnSales": "Impacto en Ventas", "labelRelationship": "Relación", "chartTitleConversionVsSales": "Tasa de Conversión vs. Ventas a lo Largo del Tiempo", "legendConversionRate": "Tasa de Conversión", "legendSales": "Ventas", "tableTitleComparisonData": "Datos de Comparación de Campañas", "tableHeaderCampaign": "Campaña", "tableHeaderConversionRate": "Tasa de Conversión", "tableHeaderLaNubeSales": "Ventas (La Nube)", "tableHeaderImpact": "Impacto", "statusActive": "ACTIVA", "statusCompleted": "COMPLETADA", "statusPaused": "PAUSADA", "statusArchived": "ARCHIVADA", "statusUnknown": "DESCONOCIDO", "objectiveConversions": "CONVERSIONES", "objectiveMessages": "MENSAJES", "objectiveLinkClicks": "CLICS EN ENLACE", "objectiveBrandAwareness": "RECONOCIMIENTO DE MARCA", "objectiveReach": "ALCANCE", "objectiveLeadGeneration": "GENERACIÓN DE CLIENTES POTENCIALES", "objectiveUnknown": "DESCONOCIDO", "relationshipWeakNegative": "Negativa Débil", "relationshipStrongNegative": "Negativa Fuerte", "relationshipWeakPositive": "Positiva Débil", "relationshipStrongPositive": "Positiva Fuerte", "relationshipNeutral": "Neutral", "mockSummerCampaign": "Campaña de Verano (Ejemplo)", "mockSpringSale": "Venta de Primavera (Ejemplo)", "loadingCampaigns": "Cargando campañas...", "loadingComparisonData": "Cargando datos de comparación...", "noCampaignData": "No hay datos de campaña disponibles para los criterios seleccionados.", "noSalesData": "No hay datos de ventas disponibles para el período seleccionado.", "defaultCampaignName": "Campaña {{id}}", "tooltipConversionRate": "Tasa de Conversión", "tooltipSales": "Ventas"}, "common": {"timeRange": "<PERSON><PERSON>", "last7days": "Últimos 7 días", "last28days": "Últimos 28 días", "last30days": "Últimos 30 días", "last90days": "Últimos 90 días", "last180days": "Últimos 180 días", "lastWeek": "Última semana", "lastMonth": "<PERSON><PERSON><PERSON>", "lastYear": "<PERSON>lt<PERSON>", "lifetime": "De por vida", "customRange": "<PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "apply": "Aplicar", "adjustTo30days": "Ajustar a 30 días", "instagramAPILimit": "La API de Instagram limita las consultas a un máximo de 30 días", "instagramAPIRequirement": "* La API de Instagram requiere rangos de fechas de 30 días o menos", "topProducts": "Productos Principales", "categoryTitle": "Categorías", "productsTitle": "Productos", "customersTitle": "Clientes", "shippingTitle": "Env<PERSON><PERSON>", "seoTitle": "SEO", "viewAll": "<PERSON><PERSON>", "backToHome": "Volver al Inicio", "loading": "Cargando...", "errorOccurred": "Se produjo un error", "tryAgain": "Inténtalo de nuevo", "loadingSettings": "Cargando configuración...", "view": "<PERSON>er", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "yes": "Sí", "no": "No", "allRightsReserved": "Todos los derechos reservados", "of": "de", "pageImpressionsChartDescIG": "Número total de cuentas únicas que han visto tu contenido", "period": "<PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "overviewChartTitle": "Resumen", "pageImpressionsChartTitle": "Impresiones de Página", "pageImpressionsChartDesc": "Número total de veces que se vio el contenido de tu página", "pageEngagementChartTitle": "Interacción de Página", "pageEngagementChartDescFB": "Número total de acciones realizadas en tu página (clics, me gusta, comentarios, compartidos)", "pageFollowersChartTitle": "Se<PERSON><PERSON><PERSON> de Página", "pageFollowersChartDesc": "Número total de personas a quienes les gusta tu página", "pageViewsChartTitle": "Vistas de Página", "pageViewsChartDesc": "Número total de veces que se vio tu página", "postsAnalyticsChartTitle": "Análisis de Publicaciones", "audienceDemographicsChartTitle": "Demografía de la Audiencia", "adMetricsChartTitle": "Métricas de Anuncios", "campaignsComparisonChartTitle": "Comparación de Campañas", "selectPlaceholder": "Seleccionar...", "actualLikesCommentsFootnote": "* Me gusta y comentarios reales de publicaciones recientes", "currentFollowerFootnote": "* Recuento actual de seguidores; tendencia histórica estimada", "derivedReachFootnote": "* Derivado de datos de alcance y actividad de seguidores", "instagramReachChartTitle": "Alcance de Instagram", "instagramEngagementChartTitle": "Interacción de Instagram", "instagramEngagementChartDesc": "Número total de interacciones con tu contenido de Instagram (me gusta, comentarios, guardados)", "instagramFollowersChartTitle": "Seguidores de Instagram", "instagramFollowersChartDesc": "Número total de cuentas que siguen tu perfil de Instagram", "instagramProfileViewsChartTitle": "Vistas del Perfil de Instagram", "instagramProfileViewsChartDesc": "Número total de veces que se vio tu perfil de Instagram"}, "permissions": {"title": "Verificación de Permisos de Meta", "alertTitle": "<PERSON><PERSON><PERSON>", "alertDescription": "Para realizar esta acción, D-Unit requiere los siguientes permisos de Meta:", "alertGuidance": "Por favor, otorga estos permisos en la configuración de tu negocio de Meta o vuelve a conectar tu cuenta.", "goToSettingsButton": "Ir a Configuración de Meta"}, "caption": {"actualLikesComments": "* Me gusta y comentarios reales de publicaciones recientes", "currentFollowerCount": "* Recuento actual de seguidores; tendencia histórica estimada", "derivedFromReachData": "* Derivado de datos de alcance y actividad de seguidores"}, "error": {"apiRateLimit": "Límite de solicitudes API alcanzado. Por favor, inténtalo de nuevo en un minuto.", "refreshPermissionsFailed": "Error al refrescar los permisos. Por favor, inténtalo de nuevo.", "tokenExpired": "Tu token de acceso de Meta ha expirado. Por favor, inicia sesión de nuevo para continuar.", "genericSdkMessage": "Ocurrió un error del SDK: {{message}}", "loginResponse": "Respuesta inválida del inicio de sesión de Meta SDK", "checkStatus": "Error al verificar el estado de inicio de sesión de Meta.", "initializationFailed": "Error al inicializar después del inicio de sesión.", "selectValidRange": "Por favor, selecciona un rango de tiempo válido."}, "connectToMetaTitle": "Conectar con Meta", "alert": {"missingTokenTitle": "Falta el Token de Acceso de la Página", "missingTokenBody": "Esta página no tiene un token de acceso válido, que es necesario para obtener datos de métricas. Intenta desconectarte y volver a conectarte con Meta, asegurándote de otorgar todos los permisos solicitados. Si estás utilizando una cuenta de Instagram, asegúrate de que esté vinculada a una página de Facebook que administres.", "sessionExpiredTitle": "Tu sesión de Meta ha expirado o es inválida", "sessionExpiredBody": "Esto generalmente sucede cuando tu token de acceso expira (normalmente después de 60 días) o los permisos han sido revocados. Haz clic en el botón para iniciar sesión nuevamente y asegúrate de otorgar todos los permisos solicitados."}, "selectPagePrompt": {"title": "Seleccionar una Página", "body": "Por favor, selecciona una página de Facebook o una cuenta de Instagram del menú desplegable de arriba para ver las analíticas."}, "info": {"altEmailLogin": "¿Usas un correo electrónico diferente para iniciar sesión en Meta? Puedes agregarlo en tu página de <0>Configuración</0>."}}, "metaConnect": {"error": {"fetchAccountsFailed": "Error al obtener las cuentas de negocio. Por favor, inténtalo de nuevo.", "authFailed": "Autenticación de Meta fallida. Por favor, inicia sesión de nuevo.", "missingPermissionsBusiness": "Faltan los permisos requeridos. Por favor, otorga acceso a la cuenta de negocio.", "apiCommsFailed": "Error al comunicarse con la API de Meta. Por favor, inténtalo de nuevo más tarde.", "networkError": "Ocurrió un error de red. Por favor, verifica tu conexión.", "connectAccountFailed": "Error al conectar la cuenta de negocio. Por favor, inténtalo de nuevo.", "missingPermissionsPage": "Faltan los permisos requeridos para el acceso a la página.", "fetchPageInfoFailed": "Error al obtener la información de la página. Por favor, inténtalo de nuevo más tarde."}}, "metaOverview": {"error": {"noMetricsData": "No hay datos de métricas disponibles. Por favor, inténtalo de nuevo más tarde.", "unexpected": "Ocurrió un error inesperado. Por favor, inténtalo de nuevo.", "metricLoadFailed": "Error al cargar datos de {{metricType}}. Por favor, verifica los permisos e inténtalo de nuevo.", "fetchSomeMetricsFailed": "Ocurrió un error al obtener algunas métricas."}}, "timeRangeFilter": {"error": {"exceeds30DaysInstagram": "El rango de fechas excede los 30 días ({{diffDays}} días seleccionados). La API de Instagram limita las consultas a 30 días."}}, "adminPortal": {"title": "Portal de Administración", "searchLabel": "Buscar Tiendas (ID o Nombre)", "storeSelectionTitle": "Selección de Tienda", "infoNoStoresFound": "No se encontraron tiendas que coincidan con tus criterios.", "unnamedStore": "Tienda sin nombre", "viewDashboardButton": "<PERSON><PERSON>", "errorNoToken": "Token de autenticación no encontrado. Por favor, inicia sesión de nuevo.", "errorUnknownFetch": "Ocurrió un error desconocido al buscar tiendas", "errorAuthRequired": "Autenticación requerida"}, "settings": {"pageTitle": "Configuración", "errorNoStoreId": "No hay ID de tienda disponible", "errorLoadProfileFailed": "Error al cargar el perfil", "adminStoreSpecificWarning": "La configuración del perfil de la empresa es específica de la tienda y no se puede gestionar aquí.", "adminPasswordStoreSpecificWarning": "La configuración de contraseña es específica de la tienda y no se puede gestionar aquí.", "admin2FAStoreSpecificWarning": "La configuración de Autenticación de Dos Factores es específica de la tienda y no se puede gestionar aquí.", "adminMetaEmailWarning": "La configuración de correo de Meta es específica de la tienda y no se puede gestionar aquí.", "companyProfile": {"title": "Perfil de la Compañía", "updateSuccess": "Perfil actualizado con éxito", "updateError": "Error al actualizar el perfil. Por favor, inténtalo de nuevo más tarde.", "validationError": "Por favor, corrija los errores de validación antes de enviar", "companyNameLabel": "Nombre de la Compañía *", "companyNamePlaceholder": "Nombre de Tienda Pendiente", "contactNameLabel": "Nombre de Contacto", "businessTypeLabel": "Tipo de Negocio *", "businessTypePlaceholder": "Tipo de Negocio Pendiente", "emailLabel": "Correo Electrónico", "taxIdLabel": "ID Fiscal (CUIT/CUIL)", "phoneLabel": "Teléfono", "dniLabel": "DNI", "websiteLabel": "Sitio Web", "addressLabel": "Dirección", "updateButton": "<PERSON><PERSON><PERSON><PERSON>"}, "metaEmail": {"title": "Configuración de Correo para Meta Dashboard", "description": "Selecciona qué dirección de correo electrónico usar para iniciar sesión en Meta Dashboard. Puede ser tu correo principal o uno de tus correos alternativos.", "selectLabel": "Elegir Correo de Inicio de Sesión de Meta", "primaryLabel": "(Principal)", "setButton": "Establecer como Correo de Meta", "updateSuccess": "Correo de inicio de sesión de Meta Dashboard actualizado con éxito.", "updateError": "Error al actualizar el correo de inicio de sesión de Meta Dashboard.", "currentSelectionPrefix": "Correo actualmente seleccionado para Meta Dashboard:", "primaryEmailUsage": "No hay correos alternativos disponibles. Se usará tu correo principal ({email}) para Meta Dashboard."}, "alternateEmail": {"addTitle": "Agregar Dirección de Correo Alternativa", "addDescription": "Agrega direcciones de correo adicionales que usas para tus cuentas de Meta. Estas pueden seleccionarse para el inicio de sesión de Meta Dashboard.", "newEmailInputLabel": "Nueva Dirección de Correo", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "yourAlternateEmails": "Tus Correos Alternativos:", "selectedForMeta": "Seleccionado para Meta", "noneAdded": "Aún no se han agregado correos alternativos.", "errorInvalidFormat": "Por favor, ingresa una dirección de correo electrónico válida", "addSuccess": "Correo electrónico agregado con éxito", "addErrorFallback": "Error al agregar correo electrónico", "addErrorConflict": "Esta dirección de correo electrónico ya está asociada a la cuenta.", "removeSuccess": "Correo electrónico eliminado con éxito", "removeError": "Error al eliminar correo electrónico"}, "appearance": {"title": "Configuración de Apariencia", "darkModeLabel": "<PERSON><PERSON>", "darkModeDescription": "Alternar entre temas claro y oscuro para la aplicación."}, "changePassword": {"title": "Cambiar Tu <PERSON>", "requirementsTitle": "Requisitos de la contraseña:", "reqLength": "Mínimo 8 caracteres", "reqUppercase": "Al menos 1 letra mayúscula", "reqNumber": "Al menos 1 número", "reqSymbol": "Al menos 1 símbolo", "reqMatch": "Las contraseñas coinciden", "currentPasswordLabel": "Contraseña Actual *", "newPasswordLabel": "Nueva Contraseña *", "confirmPasswordLabel": "Confirma<PERSON> *", "changeButton": "Cambiar <PERSON>", "errorMismatch": "Las contraseñas no coinciden", "errorSameAsOld": "La nueva contraseña no puede ser igual a la anterior", "errorRequirements": "La contraseña debe cumplir todos los requisitos", "updateSuccess": "Contraseña cambiada con éxito", "errorIncorrectOld": "Contraseña antigua incorrecta", "updateErrorFallback": "Error al cambiar la contraseña. Por favor, inténtalo de nuevo."}, "twoFactor": {"title": "Autenticación de Dos Factores", "enableSwitchLabel": "Habilitar Autenticación de Dos Factores", "description": "Mejora la seguridad de tu cuenta habilitando la autenticación de dos factores. Recibirás un código de verificación por correo electrónico al iniciar sesión.", "updateSuccess": "Estado de 2FA actualizado con éxito.", "enableSuccess": "2FA habilitado con éxito.", "disableSuccess": "2FA deshabilitado con éxito.", "updateError": "Error al actualizar el estado de 2FA.", "errorDisableStatus": "Error al deshabilitar 2FA. El estado todavía muestra habilitado.", "infoCodeSent": "Código de verificación enviado a tu correo. Por favor, ingrésalo a continuación.", "verificationTitle": "Verificar Autenticación de Dos Factores", "verificationInstructions": "Por favor, ingresa el código de 6 dígitos enviado a tu dirección de correo para completar la configuración.", "codeInputLabel": "Código de Verificación", "verifyButton": "Verificar Código", "warnEnterCode": "Por favor, ingresa el código de 6 dígitos.", "verifySuccess": "Verificación exitosa. 2FA ahora está habilitado.", "verifyErrorInvalidOrExpired": "Verificación fallida. Código inválido o solicitud expirada.", "verifyErrorFallback": "Error al verificar el código. Por favor, inténtalo de nuevo.", "resendButtonIdle": "Reenviar Código", "resendButtonSending": "Enviando...", "resendButtonSent": "<PERSON><PERSON><PERSON>", "refreshButton": "<PERSON><PERSON><PERSON><PERSON>"}, "language": {"title": "Configuración de Idioma", "description": "Elige tu idioma preferido para la aplicación."}, "validation": {"companyNameRequired": "El nombre de la empresa es requerido", "companyNameTooShort": "El nombre de la empresa debe tener al menos 2 caracteres", "companyNameTooLong": "El nombre de la empresa no debe exceder 100 caracteres", "companyNameInvalidChars": "El nombre de la empresa contiene caracteres inválidos", "contactNameRequired": "El nombre de contacto es requerido", "contactNameTooShort": "El nombre de contacto debe tener al menos 2 caracteres", "contactNameTooLong": "El nombre de contacto no debe exceder 50 caracteres", "contactNameInvalidChars": "El nombre de contacto solo puede contener letras, espacios, guiones y apostrofes", "businessTypeRequired": "El tipo de negocio es requerido", "businessTypeTooShort": "El tipo de negocio debe tener al menos 2 caracteres", "businessTypeTooLong": "El tipo de negocio no debe exceder 50 caracteres", "businessTypeInvalidChars": "El tipo de negocio solo puede contener letras, espacios y guiones", "emailRequired": "El correo electrónico es requerido", "emailTooLong": "El correo electrónico no debe exceder 254 caracteres", "emailInvalid": "Por favor, ingresa una dirección de correo electrónico válida", "taxIdRequired": "El ID fiscal es requerido", "taxIdTooShort": "El ID fiscal debe tener al menos 8 caracteres", "taxIdTooLong": "El ID fiscal no debe exceder 20 caracteres", "taxIdInvalidChars": "El ID fiscal solo puede contener letras, números y guiones", "phoneRequired": "El número de teléfono es requerido", "phoneTooShort": "El número de teléfono debe tener al menos 10 caracteres", "phoneTooLong": "El número de teléfono no debe exceder 18 caracteres", "phoneInvalid": "Por favor, ingresa un número de teléfono válido", "dniRequired": "El DNI es requerido", "dniTooShort": "El DNI debe tener al menos 7 caracteres", "dniTooLong": "El DNI no debe exceder 12 caracteres", "dniInvalidChars": "El DNI solo puede contener letras y números", "websiteTooLong": "La URL del sitio web no debe exceder 255 caracteres", "websiteInvalid": "Por favor, ingresa una URL válida del sitio web (incluyendo http:// o https://)", "addressRequired": "La dirección es requerida", "addressTooShort": "La dirección debe tener al menos 5 caracteres", "addressTooLong": "La dirección no debe exceder 200 caracteres", "addressInvalidChars": "La dirección contiene caracteres inválidos", "codeRequired": "El código de verificación es requerido", "codeInvalid": "El código de verificación debe tener exactamente 6 dígitos"}, "settings": {"categoryEnabled": "Cookies {{category}} habilitadas", "categoryDisabled": "Cookies {{category}} deshabilitadas", "allAccepted": "Todas las cookies aceptadas exitosamente", "optionalRejected": "Cookies opcionales rechazadas", "legalInfo": "Información Legal", "legalText": "Este sitio web cumple con GDPR, CCPA, LGPD, PIPEDA y otras regulaciones de privacidad aplicables. Usted tiene derecho a:", "right1": "Retirar el consentimiento en cualquier momento", "right2": "Solicitar la eliminación de sus datos", "right3": "Acceder a sus datos personales", "right4": "Portabilidad de datos", "supportedRegulations": "Regulaciones Soportadas", "noConsent": "Aún no se ha dado consentimiento para cookies", "details": "Detalles", "resetConsent": "Restablecer Todas las Configuraciones de Cookies", "save": "Guardar Preferencias", "privacyPolicyLink": "Ver nuestra Política de Privacidad"}}, "socialMediaCard.strategyTitle": "Estrategia de Redes Sociales", "socialMediaCard.strategyTooltip": "Resumen generado por IA del enfoque de redes sociales.", "dashboard": {"title": "Tablero de Tienda", "welcomeMessage": "¡Bienvenido, {{name}}! Aquí tienes un resumen de tu tienda.", "defaultUserName": "Gerente de Tienda", "errorFetchData": "Error al cargar los datos del tablero.", "errorSubmitFeedback": "Error al enviar comentarios.", "storeInfo": {"storeLabel": "Tienda", "businessTypeLabel": "Tipo de Negocio", "countryLabel": "<PERSON><PERSON>"}, "tabs": {"overview": "Resumen", "products": "Productos", "customers": "Clientes", "competition": "Competencia", "shipping": "Env<PERSON><PERSON>", "seo": "SEO", "feedback": "Comentarios"}, "overview": {"summaryTitle": "Resumen IA", "recommendationsTitle": "Recomendaciones IA", "noSummary": "No hay resumen disponible.", "noRecommendations": "No hay recomendaciones disponibles."}, "products": {"analysisTitle": "Análisis de Rendimiento de Productos", "totalProducts": "Productos Totales", "noAnalysis": "No hay análisis de productos disponible.", "performanceTitle": "Rendimiento de Productos"}, "customers": {"insightsTitle": "Información de Clientes", "totalCustomers": "Clientes Totales", "noAnalysis": "No hay análisis de clientes disponible."}, "competition": {"marketPositionTitle": "Análisis de Posición de Mercado", "competitorDetailsTitle": "Detalles de Competidores", "mainTitle": "Análisis de Competencia", "noData": "No hay datos de competencia disponibles para esta tienda todavía.", "tabTitle": "Competencia"}, "shipping": {"title": "<PERSON><PERSON><PERSON><PERSON>", "noAnalysis": "No hay análisis de envíos disponible.", "recommendationsTitle": "Recomendaciones:"}, "seo": {"title": "Recomendaciones SEO"}, "feedback": {"title": "<PERSON><PERSON>", "placeholder": "Comparte tus opiniones sobre el tablero...", "submit": "Enviar Comentarios", "submitting": "Enviando...", "success": "¡G<PERSON>ias por tus comentarios!", "previousFeedback": "Comentarios Anteriores", "noFeedback": "No se encontraron comentarios anteriores.", "errorFetchFeedback": "Error al cargar los comentarios."}, "metrics": {"revenue": "Ingresos", "orders": "Pedidos", "visits": "Visitas", "avgOrderValue": "Valor Promedio de Pedido", "totalRevenue": "Ingresos Totales", "grossRevenue": "Ingresos Brutos", "totalNetRevenue": "Ingresos Netos Totales", "totalRevenueUSD": "Ingresos Totales (USD)", "totalCustomers": "Total de Clientes", "totalProducts": "Total de Productos", "totalVisits": "Total de Visitas", "activeCustomers": "Clientes Activos", "abandonedCarts": "Carritos Abandonados", "metaFollowers": "Se<PERSON><PERSON><PERSON> de Meta", "metaAdSpend30d": "Gasto en Anuncios Meta (30d)"}, "titles": {"keyDates": "<PERSON><PERSON><PERSON>", "socialMedia": "Perfiles de Redes Sociales", "keywords": "Palabra<PERSON>", "detailedMetrics": "Resumen de Métricas Detalladas"}, "common": {"timeRange": "<PERSON><PERSON>", "last7days": "Últimos 7 días", "last28days": "Últimos 28 días", "last30days": "Últimos 30 días", "last90days": "Últimos 90 días", "last180days": "Últimos 180 días", "lastWeek": "Última semana", "lastMonth": "<PERSON><PERSON><PERSON>", "lastYear": "<PERSON>lt<PERSON>", "lifetime": "De por vida", "customRange": "<PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "apply": "Aplicar", "adjustTo30days": "Ajustar a 30 días", "instagramAPILimit": "La API de Instagram limita las consultas a un máximo de 30 días", "instagramAPIRequirement": "* La API de Instagram requiere rangos de fechas de 30 días o menos", "topProducts": "Productos Principales", "categoryTitle": "Categorías", "productsTitle": "Productos", "customersTitle": "Clientes", "shippingTitle": "Env<PERSON><PERSON>", "seoTitle": "SEO", "viewAll": "<PERSON><PERSON>", "backToHome": "Volver al Inicio", "loading": "Cargando...", "errorOccurred": "Se produjo un error", "tryAgain": "Inténtalo de nuevo", "loadingSettings": "Cargando configuración...", "view": "<PERSON>er", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "yes": "Sí", "no": "No", "allRightsReserved": "Todos los derechos reservados", "of": "de", "pageImpressionsChartDescIG": "Número total de cuentas únicas que han visto tu contenido", "period": "<PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "overviewChartTitle": "Resumen", "pageImpressionsChartTitle": "Impresiones de Página", "pageImpressionsChartDesc": "Número total de veces que se vio el contenido de tu página", "pageEngagementChartTitle": "Interacción de Página", "pageEngagementChartDescFB": "Número total de acciones realizadas en tu página (clics, me gusta, comentarios, compartidos)", "pageFollowersChartTitle": "Se<PERSON><PERSON><PERSON> de Página", "pageFollowersChartDesc": "Número total de personas a quienes les gusta tu página", "pageViewsChartTitle": "Vistas de Página", "pageViewsChartDesc": "Número total de veces que se vio tu página", "postsAnalyticsChartTitle": "Análisis de Publicaciones", "audienceDemographicsChartTitle": "Demografía de la Audiencia", "adMetricsChartTitle": "Métricas de Anuncios", "campaignsComparisonChartTitle": "Comparación de Campañas", "selectPlaceholder": "Seleccionar..."}}, "customerAnalysisCard": {"title": "Análisis de Clientes", "notAvailable": "No hay análisis de clientes disponible."}, "customerInsightsChart": {"title": "Información de Clientes", "notAvailable": "No hay datos de información disponibles.", "countryDistributionTitle": "Distribución por País", "demographicsTitle": "<PERSON><PERSON><PERSON>"}, "customerAggregatedMetricsCard": {"title": "Métricas Agregadas de Clientes", "notAvailable": "No hay métricas agregadas disponibles.", "totalCustomers": "Clientes Totales", "totalStoreOrders": "Pedidos Totales Tienda", "avgSpend": "Gasto Prom./Cliente", "abandonedCarts": "Carritos Abandonados", "abandonedCartCustomers": "Clientes (Abandonados)", "abandonedValue": "<PERSON><PERSON>", "pendingCarts": "Carritos Pendientes", "pendingValue": "Valor Pendiente", "mostFrequentPayment": "<PERSON><PERSON>", "mostFrequentShipping": "<PERSON><PERSON><PERSON>", "mostFrequentCoupon": "Cupón Más Frecuente", "countryDistribution": "Distribución por País", "couponDistribution": "Distribución de Cupones", "paymentDistribution": "Distribución Mét. Pago", "shippingDistribution": "Distribución Mét. Envío", "statusDistribution": "Distribución de Estados"}, "customerList": {"title": "Clientes", "searchPlaceholder": "Buscar por nombre o email...", "header": {"name": "Nombre", "email": "Email", "totalOrders": "Pedidos Totales", "totalSpend": "Gasto Total", "lastOrder": "Fecha Últ. Pedido"}, "noResults": "No se encontraron clientes"}, "customerDetailView": {"title": "Detalles del Cliente", "placeholder": "Selecciona un cliente de la lista para ver detalles.", "name": "Nombre", "email": "Email", "totalSpend": "Gasto Total", "firstOrderDate": "Primer Pedido", "lastOrderDate": "<PERSON>ltimo <PERSON>", "preferredPayment": "Pago Pref.", "preferredShipping": "Env<PERSON>.", "country": "<PERSON><PERSON>", "uniqueProducts": "Productos Únicos", "paymentMethods": "Métodos de Pago Usados", "shippingMethods": "Métodos de Envío Us<PERSON>s", "ordersTitle": "Pedidos", "orderId": "Pedido", "paymentMethod": "Pago", "shippingMethod": "Envío", "coupon": "Cupón", "productHeader": "Producto", "qtyHeader": "Cant.", "priceHeader": "Precio", "noProducts": "No hay detalles de productos disponibles para este pedido.", "noOrders": "No se encontraron pedidos para este cliente."}, "chat": {"pageTitle": "Cha<PERSON>", "newChatDefaultTitle": "Nuevo Chat", "newChatTooltip": "Nuevo Chat", "optionsTooltip": "Opciones de Chat", "openSidebarTooltip": "<PERSON><PERSON><PERSON>", "closeSidebarTooltip": "<PERSON><PERSON><PERSON>", "inputPlaceholder": "Escribe tu mensaje aquí...", "sendTooltip": "<PERSON><PERSON><PERSON> men<PERSON>", "feedbackButton": "Enviar Comentarios", "dateGroup": {"invalid": "<PERSON><PERSON>", "today": "Hoy", "yesterday": "Ayer", "previous7Days": "Últimos 7 Días", "previous30Days": "Últimos 30 Días", "older": "<PERSON>ás <PERSON>"}, "loadingSteps": {"thinking": "Pensando...", "analyzingContext": "<PERSON><PERSON><PERSON><PERSON>...", "generating": "Generando respuesta...", "processingQuery": "Procesando consulta...", "searchingBrave": "<PERSON><PERSON><PERSON> en la web (Brave)...", "analyzingResults": "<PERSON><PERSON><PERSON><PERSON>ús<PERSON>...", "searchingGoogle": "Buscando en la web (Google)...", "processing": "Procesando..."}, "errorLog": {"fetchHistory": "Error al obtener historial de chat:", "sendMessage": "Error al enviar mensaje:"}, "error": {"fetchHistory": "Error al obtener el historial de chat. Por favor, refresca la página.", "fetchMessages": "Error al obtener los mensajes para este chat.", "sendMessage": "Error al enviar el mensaje. Por favor, verifica tu conexión e inténtalo de nuevo.", "renameChat": "Error al renombrar el chat. Por favor, inténtalo de nuevo.", "deleteChat": "Error al eliminar el chat. Por favor, inténtalo de nuevo.", "unexpected": "Ocurrió un error inesperado. Por favor, inténtalo de nuevo."}, "export": {"exportToPDF": "Exportar chat a PDF", "exportedOn": "Exportado el", "chatCreated": "Chat creado el"}, "menu": {"rename": "Renombrar", "delete": "Eliminar"}, "rename": {"dialogTitle": "Reno<PERSON><PERSON>", "inputLabel": "Introduce nuevo título", "confirmButton": "Renombrar", "errorEnterTitle": "Por favor, introduce un nuevo título.", "errorCannotRenamePlaceholder": "No se puede renombrar un 'Nuevo Chat' vacío. Envía un mensaje primero."}, "delete": {"dialogTitle": "Eli<PERSON><PERSON>", "confirmationText": "¿Estás seguro de que quieres eliminar este chat?", "confirmButton": "Eliminar"}, "dialog": {"cancelButton": "<PERSON><PERSON><PERSON>"}, "metaPermissions": {"alertTitle": "<PERSON><PERSON><PERSON>", "alertDescription": "Para realizar esta acción, D-Unit requiere los siguientes permisos de Meta:", "alertGuidance": "Por favor, otorga estos permisos en la configuración de tu Meta Business o vuelve a conectar tu cuenta.", "goToSettingsButton": "Ir a Configuración de Meta"}, "initialAssistantMessage": "¿Qué quieres saber? Pregúntame lo que sea", "newChatTitle": "Nuevo Chat", "startNewChatButton": "Iniciar <PERSON>", "roleAgent": "<PERSON><PERSON>", "attachImageTooltip": "Adjuntar <PERSON>n", "modeButton": {"think": "Pensar", "deepSearch": "Búsqueda Profunda", "deeperSearch": "Búsqueda Más Profunda"}, "emptyStates": {"storeInfoNotAvailable": "Información de la tienda no disponible", "noChatHistory": "Aún no hay historial de chat", "startNewChat": "Inicia un nuevo chat para comenzar"}, "loadingConsole": {"notReady": "Cargando Consola de <PERSON>..."}, "contextDocuments": {"relatedInformation": "Información Relacionada:", "productInfo": "Información de Productos", "customerInfo": "Información de Clientes", "storeInfo": "Información de la Tienda", "topProducts": "Productos Principales", "customerBase": "Base de Clientes", "noCustomerData": "No hay datos detallados de clientes disponibles", "locatedIn": "Ubicado en", "unknownLocation": "Ubicación desconocida", "unknownType": "Tipo desconocido", "connectedSocial": "Redes sociales conectadas:"}, "processing": "Procesando...", "untitledChat": "Chat Sin Título", "customerData": {"orders": "pedidos", "spent": "<PERSON>tado", "avgCustomerSpend": "Gasto Promedio por Cliente:", "inStock": "en stock", "outOfStock": "Agotado"}, "similarity": "<PERSON><PERSON><PERSON><PERSON>", "loadingIndicator": {"mode": "Modo:", "time": "Tiempo:", "defaultProcessing": "Procesando..."}, "creditsRemaining": "Créditos Restantes", "creditsTooltip": "Costo de Tokens por Mensaje:\n• Chat normal: 10,000 tokens\n• Modo pensar: 20,000 tokens\n• Modo bús<PERSON>: 40,000 tokens\n• Modo búsqueda profunda: 40,000 tokens", "hideInput": "Ocultar entrada", "showInput": "Mostrar entrada"}, "navigation": {"chat": "Cha<PERSON>", "dashboard": "<PERSON><PERSON>", "meta": "Meta", "socialMedia": "Redes Sociales", "settings": "Configuración", "logout": "<PERSON><PERSON><PERSON>"}, "metaLogin": {"error": {"httpsRequired": "Meta Login requiere HTTPS. Por favor, utiliza HTTPS para conectarte con Meta.", "sdkFailed": "Error al cargar el SDK de Facebook. Por favor, intenta refrescar la página.", "authFailed": "Autenticación fallida. Por favor, asegúrate de que has iniciado sesión en Meta e inténtalo de nuevo.", "permissionDenied": "Permiso denegado. Por favor, otorga los permisos necesarios para usar esta función.", "securityError": "Meta login requiere HTTPS. Por favor, utiliza una conexión segura.", "networkError": "Ocurrió un error de red. Por favor, verifica tu conexión e inténtalo de nuevo.", "sdkNotLoaded": "Error al cargar el SDK de Meta. Por favor, asegúrate de que las cookies estén habilitadas e inténtalo de nuevo.", "unexpected": "Ocurrió un error inesperado. Por favor, inténtalo de nuevo.", "missingPermissions": "Faltan permisos críticos: {{permissions}}. La funcionalidad del panel estará limitada."}, "warning": {"optionalPermissions": "Algunos permisos opcionales no fueron otorgados: {{permissions}}. Algunas funciones pueden estar limitadas.", "noPagesFound": "Conectado a {{platform}}, pero no se encontraron {{accountType}}."}, "success": {"connectedPlatform": "¡Conectado a {{platform}} exitosamente!"}, "connecting": "Conectando...", "continueWithMeta": "<PERSON><PERSON><PERSON><PERSON> con <PERSON>", "continueWithInstagram": "Continuar con Instagram", "info": {"allowAccess": "Esto permitirá a D-Unit acceder a tu página de Facebook y cuentas comerciales de Instagram para obtener información y datos analíticos. Te pediremos permisos específicos en el siguiente paso."}, "choosePlatform": "Elige una plataforma para conectar:", "requiredPermissions": "Permisos requeridos:", "permissionsInfo": "Cuando se te solicite, por favor otorga todos los permisos solicitados para {{platformDetails}}.", "permissionsWarning": "Sin estos permisos, el panel podría no funcionar correctamente."}, "common": {"platform": "Plataforma", "facebook": "Facebook", "instagram": "Instagram", "metaLogo": "Logo de Meta", "pages": "p<PERSON><PERSON><PERSON>", "accounts": "cuentas", "pageInsights": "estadísticas de página, publicaciones y datos de audiencia", "instagramInsights": "acceso a Instagram y estadísticas", "notAvailable": "No Disponible", "showMore": "Mostrar <PERSON>", "showLess": "<PERSON><PERSON>", "uses": "usos", "countPrefix": "Conteo:", "loading": "Cargando...", "total": "Total", "average": "Promedio", "change": "Cambio", "netChange": "<PERSON><PERSON>", "noDataAvailable": "No hay datos disponibles.", "notAvailableShort": "N/D", "refreshing": "Refrescando...", "numberSuffix": {"million": "M", "thousand": "K"}}, "topProductsCard": {"cardTitle": "Productos Principales", "noData": "No hay datos de productos disponibles para el período seleccionado", "productName": "Nombre del Producto", "productSales": "Ventas", "productUnits": "Unidades", "revenue": "Ingresos", "orders": "Pedidos"}, "categorySummary": {"cardTitle": "Resumen de Categorías", "noData": "No hay datos de resumen de categorías disponibles.", "uniqueCats": "Categorías Únicas:", "uniqueSubCats": "Subcategorías Únicas:", "totalWithCats": "Productos con Categorías:", "distributionTitle": "Distribución de Categorías", "topCats": "Categorías <PERSON>", "featuredCats": "Categorías Destacadas Principales", "saleCats": "Categorías en Oferta Principales", "productsSuffix": "productos", "itemsSuffix": "elementos"}, "productPerformance": {"cardTitle": "Rendimiento de Productos", "revenueTitle": "Ingresos", "unitsTitle": "Unidades Vendidas", "noData": "No hay datos disponibles para el período seleccionado"}, "shippingAnalysis": {"cardTitle": "<PERSON><PERSON><PERSON><PERSON>", "deliveryTime": "Tiempo de Entrega", "shippingCost": "Costo de Envío", "shippingMethods": "Métodos de Envío", "days": "días", "hours": "horas", "noData": "No hay datos de envío disponibles para el período seleccionado", "errorNoStoreId": "Se requiere ID de tienda.", "errorFetchFailed": "Error al obtener datos de envío. Por favor, inténtalo de nuevo.", "loading": "Cargando datos de envío...", "noDistributionData": "No hay datos de distribución de métodos de envío disponibles", "usage": "<PERSON><PERSON>", "orders": "pedidos", "recommendationsTitle": "Recomendaciones:", "noRecommendations": "No hay recomendaciones de envío disponibles.", "usageTitle": "Uso de Métodos de Envío", "ordersSuffix": "pedidos", "tooltipUsage": "<PERSON><PERSON>", "centralLabel": {"methodText": "M<PERSON><PERSON><PERSON>"}}, "customerMetrics": {"cardTitle": "Métricas de Clientes", "newCustomers": "Nuevos Clientes", "returningCustomers": "<PERSON><PERSON><PERSON>", "averageOrderValue": "Valor Promedio de Pedido", "customerRetention": "Retención de Clientes", "conversionRate": "Tasa de Conversión"}, "couponPaymentShippingCard": {"title": "<PERSON><PERSON><PERSON>", "coupons": "Cupones", "payments": "Pagos", "shipping": "Env<PERSON><PERSON>", "mostFrequentTitle": "<PERSON><PERSON>", "mostFrequentCoupon": "Cupón:", "mostFrequentPayment": "Método de Pago:", "mostFrequentShipping": "Mé<PERSON><PERSON> de Envío:", "distributionTitle": "Distribuciones", "couponDistribution": "Códigos de Cupón", "paymentDistribution": "Métodos de Pago", "shippingDistribution": "Métodos de Envío"}, "seoMetrics": {"cardTitle": "Métricas SEO", "organicTraffic": "Tráfico Orgánico", "keywordRankings": "Posiciones de Palabras Clave", "backlinks": "Backlinks", "pagespeed": "Velocidad de Página", "mobileUsability": "Usabilidad <PERSON>"}, "seoRecommendations": {"cardTitle": "Recomendaciones SEO Potenciadas por IA", "priority": "Prioridad", "impact": "Impacto", "difficulty": "Dificultad", "high": "Alta", "medium": "Media", "low": "Baja", "implementBtn": "Implementar", "dismissBtn": "Descar<PERSON>", "noRecommendations": "No hay recomendaciones SEO disponibles en este momento", "errorNoStoreId": "Falta el ID de la tienda.", "errorGeneral": "Error al cargar las recomendaciones SEO.", "recommendations": {"improveDescriptions": {"title": "Mejorar Descripciones de Producto", "description": "Mejora las descripciones de producto con palabras clave relevantes y detalles convincentes."}, "targetKeywords": {"title": "Apuntar a Palabras Clave Long-Tail", "description": "Investiga e incorpora palabras clave long-tail para atraer tráfico más específico."}, "optimizeImages": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Asegúrate de que las imágenes de producto tengan texto alternativo descriptivo y estén optimizadas para la web."}, "improveSiteSpeed": {"title": "Mejorar Velocidad del Sitio", "description": "Optimiza la velocidad de carga de tu sitio para mejorar la experiencia del usuario y el ranking SEO."}, "enhanceMobileExperience": {"title": "Mejorar Experiencia Móvil", "description": "Asegúrate de que tu sitio sea totalmente responsivo y ofrezca una excelente experiencia en dispositivos móviles."}}, "categories": {"content": "Contenido", "keywords": "Palabra<PERSON>", "technical": "Técnico", "links": "Enlaces", "performance": "Rendimiento", "mobile": "Móvil", "structure": "Estructura"}}, "productDetails": {"showLess": "<PERSON><PERSON>", "showMore": "Mostrar <PERSON>", "productName": "Nombre del Producto", "productPerformance": "Rendimiento de Productos", "variations": "Variaciones", "salesHistory": "Historial de Ventas", "revenue": "Ingresos", "units": "Unidades", "page": "<PERSON><PERSON><PERSON><PERSON>", "detailsTitle": "Detalles", "salesDetailsTitle": "Detalles de Ventas", "salesDate": "<PERSON><PERSON>", "salesUnits": "Unidades", "salesNetRevenue": "Ingresos netos (Despues de Descuentos)", "noSalesDetails": "No hay detalles de ventas disponibles.", "loadMoreSales": "<PERSON><PERSON>", "showLessSales": "Mostrar Men<PERSON>", "noProducts": "No hay detalles de productos disponibles.", "header": {"product": "Producto", "salesUnits": "Unidades Vendidas", "grossRevenue": "Ingresos Brutos", "netRevenue": "Ingresos netos (Despues de Descuentos)", "price": "Precio", "stock": "Stock", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "primaryCategory": "Categoría Principal"}}, "variationDetail": {"cardTitle": "Variaciones de Producto", "noData": "Selecciona un producto para ver variaciones.", "stock": "Stock", "price": "Precio", "offer": "<PERSON><PERSON><PERSON>"}, "salesHistory": {"cardTitle": "Historial de Ventas", "noData": "Selecciona un producto para ver su historial de ventas.", "yAxis": {"units": "Unidades Vendidas", "revenue": "Ingresos"}, "legend": {"units": "Unidades Vendidas", "revenue": "Ingresos"}}, "shippingMethodsChart": {"title": "Uso de Métodos de Envío", "timesUsed": "<PERSON><PERSON><PERSON>"}, "productAnalysis": {"title": "Resumen de Productos", "totalProducts": "Productos Totales:", "storeRating": "Valoración de la Tienda", "ratings": "valoraciones", "noAnalysis": "No hay texto de análisis disponible."}, "customerDemographics": {"title": "Demografía de Clientes", "tooltipSuffixCustomers": "clientes", "notAvailable": "No hay datos demográficos de clientes disponibles."}, "meta": {"overview": {"title": "Resumen", "page_info": "Página: {{name}} ({{platform}})", "using_cached_data": "Usando datos en caché", "cached_age": "Datos en caché hace {{minutes}} minutos"}, "metrics": {"impressions": "Impresiones", "engagement": "Interacción", "followers": "<PERSON><PERSON><PERSON><PERSON>", "views": "Visualizaciones"}, "errors": {"insufficient_permissions": "Permisos insuficientes", "page_undefined": "Página no definida", "metrics_fetch_failed": "Error al obtener métricas", "token_expired": "Token de acceso expirado", "network_error": "Error de red", "permission_denied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "rate_limit_exceeded": "Límite de solicitudes excedido", "invalid_platform_for_insights": "Plataforma inválida para insights", "insights_fetch_failed": "Error al obtener insights", "authentication_failed": "Autenticación fallida", "server_error": "Error del servidor", "no_metrics_available": "No hay métricas disponibles"}, "insights": {"no_data_available": "No hay datos disponibles", "total_value": "Total", "current_period": "Período Actual", "previous_period": "<PERSON><PERSON><PERSON>", "using_cached_data": "Usando datos en caché", "cached_age": "Datos en caché hace {{minutes}} minutos"}, "posts": {"facebookTitle": "Analítica de Publicaciones de Facebook", "permissionError": "Error de permisos: Por favor, actualiza tus permisos de Meta e inténtalo de nuevo.", "noPostsInRange": "No se encontraron publicaciones en el rango de fechas seleccionado ({{startDate}} - {{endDate}}). Intenta ajustar el período de tiempo o verifica que la cuenta haya publicado contenido durante este período.", "errorLoad": "Error al cargar los datos de publicaciones de {{platform}}. Por favor, inténtalo de nuevo más tarde.", "rateLimitError": "Límite de solicitudes de API de Facebook alcanzado. Por favor, espera e inténtalo de nuevo más tarde.", "tableHeaderPost": "Publicación", "tableHeaderDate": "<PERSON><PERSON>", "tableHeaderType": "Tipo", "tableHeaderLikes": "Me gusta", "tableHeaderComments": "Comentarios", "tableHeaderShares": "Compartidos", "tableHeaderSaved": "Guardados", "tableHeaderTotalEngagement": "Interacción Total", "tableHeaderLink": "Enlace", "engagementLabelLikes": "Me gusta", "engagementLabelComments": "Comentarios", "engagementLabelShares": "Compartidos", "engagementLabelSaved": "Guardados", "postTypeLabelPhoto": "Foto", "postTypeLabelVideo": "Video", "postTypeLabelStatus": "Estado", "postTypeLabelLink": "Enlace", "postTypeLabelAlbum": "<PERSON>l<PERSON><PERSON>", "postTypeLabelEvent": "Evento", "postTypeLabelNote": "<PERSON>a", "postTypeLabelOffer": "<PERSON><PERSON><PERSON>", "postTypeLabelGeneric": "<PERSON><PERSON>", "type": {"photo": "Foto", "video": "Video", "status": "Estado", "link": "Enlace", "album": "<PERSON>l<PERSON><PERSON>", "event": "Evento", "note": "<PERSON>a", "offer": "<PERSON><PERSON><PERSON>", "image": "Imagen", "unknown": "Desconocido"}}, "restricted": {"unavailableTitle": "{{title}} No Disponible", "igInsightsRevokedTitle": "Permiso de Instagram Insights Revocado", "igInsightsRevokedBody": "Has revocado el acceso a los datos de Instagram Insights. Este widget requiere los permisos <0>instagram_basic</0> y <1>instagram_manage_insights</1>.", "igInsightsCurrentStatus": "Estado actual: <0>instagram_basic</0> (<PERSON><PERSON><PERSON>), <1>instagram_manage_insights</1> (Revocado)", "igBasicRevokedTitle": "Permiso Básico de Instagram Revocado", "igBasicRevokedBody": "Has revocado el acceso básico a tu cuenta de Instagram. Este widget requiere el permiso <0>instagram_basic</0>.", "igBasicCurrentStatus": "Estado actual: <0>instagram_basic</0> (Revocado)", "igMultipleRevokedTitle": "Múltiples Permisos de Instagram Revocados", "igMultipleRevokedBody": "Has revocado los permisos de Instagram Basic e Instagram Insights. Este widget requiere los permisos <0>instagram_basic</0> y <1>instagram_manage_insights</1>.", "igMultipleCurrentStatus": "Estado actual: <0>instagram_basic</0> (Revocado), <1>instagram_manage_insights</1> (Revocado)", "igGenericRevokedTitle": "Permisos de Instagram Revocados", "igGenericRevokedBody": "Has revocado los permisos de Instagram necesarios para esta función.", "requiredPermissionsList": "Permisos requeridos: {{permissions}}", "singlePermissionRevoked": "Has revocado el permiso <0>{{permissionName}}</0>.", "multiplePermissionsRevoked": "Has revocado los permisos necesarios para esta función.", "requiresFollowingPermissions_one": "Este widget requiere el siguiente permiso:", "requiresFollowingPermissions_other": "Este widget requiere los siguientes permisos:", "permissionStatus": "{{permissionName}}{{status}}", "grantedStatus": " (<PERSON><PERSON><PERSON>)", "revokedStatus": " (Revocado)", "reconnectButton": "Reconectar con Meta", "manageButton": "Gest<PERSON><PERSON>", "tooltipReconnect": "Necesitarás reconectarte a Meta para otorgar estos permisos", "tooltipManage": "Visita la pestaña de Gestión de Permisos para saber más"}}, "metaPermissions": {"title": "Gestión de Permisos de Meta", "description": "Administra los permisos que has otorgado a esta aplicación. Revocar permisos puede limitar la funcionalidad.", "refreshButton": "<PERSON><PERSON><PERSON><PERSON>", "refreshingButton": "Refrescando...", "searchPlaceholder": "Buscar permisos...", "tabs": {"all": "Todos los Permisos", "page": "<PERSON><PERSON><PERSON><PERSON>", "business": "Negocio", "content": "Contenido", "instagram": "Instagram", "advertising": "Publicidad", "other": "<PERSON><PERSON>"}, "status": {"optional": "Permiso opcional", "required": "<PERSON><PERSON><PERSON>", "revoked": "Este permiso ha sido revocado", "whyRequiredTooltip": "Este permiso es necesario para la funcionalidad principal y no se puede revocar aquí. Haz clic para más información.", "whyButton": "¿Por qué?"}, "warnings": {"genericImpact": "{{impactText}}"}, "revokeDialog": {"title": "¿Revocar <PERSON>?", "confirmText": "¿Estás seguro de que quieres revocar el permiso: {{permissionName}}? Revocar este permiso puede afectar la funcionalidad de la aplicación.", "cancelButton": "<PERSON><PERSON><PERSON>", "revokeButton": "Revocar", "impactPrefix": "Impacto: {{impactText}}", "unknownImpact": "Desconocido"}, "errors": {"refreshFailed": "Error al refrescar los permisos. Por favor, inténtalo de nuevo.", "fetchFailed": "Error al obtener los permisos. Por favor, inténtalo de nuevo.", "revokeFailed": "Error al revocar el permiso: {{permissionName}}. Por favor, inténtalo de nuevo.", "revokeFailedGeneric": "Error al revocar el permiso. Por favor, inténtalo de nuevo.", "alreadyRevoked": "Este permiso ya ha sido revocado. Para otorgarlo de nuevo, debes desconectarte y volver a conectarte a Meta."}, "noMatchFilters": "Ning<PERSON> permiso coincide con tus filtros actuales.", "requiredInfoDialogText": "Algunos permisos no se pueden revocar dentro de esta interfaz porque son esenciales para la funcionalidad principal. Sin estos permisos, el panel de Meta dejaría de funcionar por completo.", "requiredInfoDialog": {"whyRequired": {"title": "¿Por qué se requieren algunos permisos?", "accessPagesList": "Acceder a la Lista de Páginas", "businessManagement": "Administración del Negocio", "listItem1": "No podemos mostrar qué Páginas administras.", "listItem2": "No podemos acceder a ninguna métrica o contenido de la página.", "listItem3": "Otros permisos que dependen de estos también fallarán."}, "howToRevokeAll": {"title": "Cómo revocar todos los permisos (solución alternativa)", "text": "Si aún deseas revocar todos los permisos, incluidos los requeridos:", "step1": "Haz clic en \"Desconectar de Meta\" en la parte superior de este panel.", "step2": "Visita la Configuración de Aplicaciones de Facebook.", "step3": "Encuentra esta aplicación y haz clic en \"Eliminar\".", "note": "Nota: Deberás volver a conectarte y otorgar permisos nuevamente para usar las funciones del panel de Meta."}, "warning": "Revocar los permisos requeridos desactivará por completo la funcionalidad de integración de Meta."}, "common": {"okButton": "Aceptar"}, "details": {"pages_show_list": {"name": "Acceder a Lista de Páginas", "description": "Permite que tu aplicación acceda a la lista de Páginas que una persona administra.", "impact": "Sin este permiso, no puedes seleccionar páginas de Facebook para administrar."}, "pages_read_engagement": {"name": "Datos de Interacción de Página", "description": "Permite que tu aplicación lea contenido (publicaciones, fotos, videos, eventos) publicado por la Página, lea datos de seguidores y lea metadatos e insights sobre la Página.", "impact": "Sin este permiso, las métricas de interacción no estarán disponibles."}, "pages_manage_metadata": {"name": "Administrar <PERSON>gin<PERSON>", "description": "Permite que tu aplicación se suscriba y reciba webhooks sobre la actividad en la Página, y actualice la configuración de la Página.", "impact": "Sin este permiso, no puedes actualizar los metadatos de la Página a través de esta aplicación."}, "pages_read_user_content": {"name": "<PERSON><PERSON>", "description": "Permite que tu aplicación lea contenido generado por usuarios en la Página, como publicaciones, comentarios y calificaciones de usuarios u otras Páginas.", "impact": "Sin este permiso, las funciones de análisis de contenido no estarán disponibles."}, "public_profile": {"name": "<PERSON><PERSON><PERSON>", "description": "Permite que una aplicación lea los Campos de Perfil Público Predeterminados en el perfil del Usuario.", "impact": "Sin este permiso, no se puede acceder a la información básica de la cuenta."}, "email": {"name": "Dirección de Correo Electrónico", "description": "Permite que la aplicación acceda a tu dirección de correo electrónico.", "impact": "Sin este permiso, las notificaciones de la cuenta pueden no funcionar correctamente."}, "business_management": {"name": "Administración del Negocio", "description": "Permite que la aplicación acceda y administre tus activos comerciales.", "impact": "Sin este permiso, las funciones comerciales no estarán disponibles."}, "ads_management": {"name": "Administración de Anuncios", "description": "Permite que tu aplicación administre anuncios asociados con la Página.", "impact": "Sin este permiso, las funciones de administración de anuncios no estarán disponibles."}, "ads_read": {"name": "<PERSON><PERSON>", "description": "Permite que tu aplicación lea los datos de tu campaña publicitaria.", "impact": "Sin este permiso, las analíticas de anuncios no estarán disponibles."}, "instagram_basic": {"name": "Básico de Instagram", "description": "Permite que tu aplicación lea la información y medios de la cuenta de Instagram de un negocio.", "impact": "Sin este permiso, las funciones de Instagram no estarán disponibles."}, "instagram_manage_insights": {"name": "Insights de Instagram", "description": "Permite que tu aplicación lea insights de la cuenta de Instagram de un negocio.", "impact": "Sin este permiso, las analíticas de Instagram no estarán disponibles."}, "instagram_content_publish": {"name": "Publicación de Contenido de Instagram", "description": "Permite que tu aplicación publique contenido en cuentas de Instagram conectadas a una Página.", "impact": "Sin este permiso, no puedes publicar en Instagram a través de esta aplicación."}, "read_insights": {"name": "Leer Insights", "description": "Proporciona acceso a datos de insights de Página y dominio para Páginas y dominios que posees o a los que tienes acceso.", "impact": "Sin este permiso, los datos de insights no estarán disponibles."}, "catalog_management": {"name": "Administración de Catálogo", "description": "Permite que tu aplicación cree, lea, actualice y elimine catálogos de productos propiedad del negocio de los que el usuario es administrador.", "impact": "Sin este permiso, las funciones de administración de catálogo no estarán disponibles."}, "pages_manage_cta": {"name": "Administrar CTA de Página", "description": "Permite que tu aplicación administre botones de llamada a la acción en una Página de Facebook.", "impact": "Sin este permiso, no puedes administrar botones de llamada a la acción en las Páginas."}, "pages_manage_instant_articles": {"name": "Administrar Instant Articles", "description": "Permite que tu aplicación administre Instant Articles en nombre de las Páginas de Facebook administradas por personas que usan tu aplicación.", "impact": "Sin este permiso, no puedes administrar Instant Articles."}, "read_page_mailboxes": {"name": "<PERSON><PERSON> Página", "description": "Permite que tu aplicación lea mensajes en el buzón de una Página.", "impact": "Sin este permiso, no puedes acceder a los mensajes de conversación de la Página."}, "pages_messaging": {"name": "Mensajería de Páginas", "description": "Permite que tu aplicación administre y acceda a las conversaciones de la Página en Messenger.", "impact": "Sin este permiso, las funciones de mensajería para Páginas no estarán disponibles."}, "pages_messaging_phone_number": {"name": "Número de Teléfono de Mensajería de Páginas", "description": "Permite el acceso al número de teléfono utilizado en el flujo de mensajería de una Página.", "impact": "Sin este permiso, no puedes acceder a los números de teléfono de mensajería de la página."}, "pages_messaging_subscriptions": {"name": "Suscripciones de Mensajería de Páginas", "description": "Permite que tu aplicación administre las suscripciones de mensajería para Páginas.", "impact": "Sin este permiso, no puedes administrar las suscripciones de mensajería."}, "instagram_manage_comments": {"name": "Administrar Comentarios de Instagram", "description": "Permite que tu aplicación cree y elimine comentarios en nombre de una cuenta de Instagram Business.", "impact": "Sin este permiso, no puedes administrar los comentarios de Instagram."}, "leads_retrieval": {"name": "Recuperación de Leads", "description": "Permite que tu aplicación recupere información recopilada a través de los formularios de generación de leads de un negocio.", "impact": "Sin este permiso, no puedes acceder a los datos de generación de leads."}, "whatsapp_business_management": {"name": "Administración de WhatsApp Business", "description": "Permite que tu aplicación administre cuentas de WhatsApp Business.", "impact": "Sin este permiso, las funciones de WhatsApp Business no estarán disponibles."}, "instagram_manage_messages": {"name": "Admini<PERSON><PERSON>", "description": "Permite que tu aplicación administre mensajes para una cuenta de Instagram Business.", "impact": "Sin este permiso, no puedes administrar los mensajes de Instagram."}, "page_events": {"name": "Eventos de Página", "description": "Permite que tu aplicación registre eventos en nombre de las Páginas de Facebook administradas por personas que usan tu aplicación.", "impact": "Sin este permiso, no puedes registrar eventos de Página para segmentación y optimización."}, "pages_manage_ads": {"name": "Administra<PERSON>", "description": "Permite que tu aplicación administre anuncios asociados con la Página.", "impact": "Sin este permiso, no puedes administrar anuncios para Páginas."}, "pages_manage_posts": {"name": "Administrar Publicaciones de Páginas", "description": "Permite que tu aplicación cree, edite y elimine las publicaciones de tu Página.", "impact": "Sin este permiso, no puedes administrar las publicaciones de la Página."}, "instagram_shopping_tag_products": {"name": "Etiquetar Productos de Instagram Shopping", "description": "Permite que tu aplicación etiquete productos en publicaciones e historias de Instagram.", "impact": "Sin este permiso, no puedes etiquetar productos en contenido de Instagram."}, "instagram_branded_content_creator": {"name": "Creador de Contenido de Marca de Instagram", "description": "Permite que tu aplicación administre permisos de contenido de marca como creador en Instagram.", "impact": "Sin este permiso, las funciones de contenido de creador en Instagram serán limitadas."}, "instagram_branded_content_brand": {"name": "Marca de Contenido de Marca de Instagram", "description": "Permite que tu aplicación administre permisos de contenido de marca como marca en Instagram.", "impact": "Sin este permiso, las funciones de contenido de marca en Instagram serán limitadas."}, "instagram_branded_content_ads_brand": {"name": "Anuncios de Contenido de Marca de Instagram (Marca)", "description": "Permite que tu aplicación cree anuncios a partir de contenido de marca como marca en Instagram.", "impact": "Sin este permiso, no puedes crear anuncios de contenido de marca en Instagram."}, "instagram_manage_events": {"name": "Administrar Eventos de Instagram", "description": "Permite que tu aplicación administre eventos para una cuenta de Instagram Business.", "impact": "Sin este permiso, no puedes administrar eventos de Instagram."}, "instagram_manage_upcoming_events": {"name": "Administrar Próximos Eventos de Instagram", "description": "Permite que tu aplicación administre próximos eventos para una cuenta de Instagram Business.", "impact": "Sin este permiso, no puedes administrar próximos eventos de Instagram."}, "manage_fundraisers": {"name": "Administrar Recaudaciones de Fondos", "description": "Permite que tu aplicación administre recaudaciones de fondos en Facebook.", "impact": "Sin este permiso, las funciones de administración de recaudaciones de fondos no estarán disponibles."}, "publish_video": {"name": "Publicar Video", "description": "Permite que tu aplicación publique videos en Facebook.", "impact": "Sin este permiso, no puedes publicar videos en Facebook."}, "private_computation_access": {"name": "Acceso a Computación Privada", "description": "Permite que una aplicación acceda a los productos de Computación Privada de Meta.", "impact": "Sin este permiso, las funciones de computación privada no estarán disponibles."}, "attribution_read": {"name": "Leer Atribu<PERSON>", "description": "Permite que tu aplicación lea datos de atribución para campañas publicitarias.", "impact": "Sin este permiso, el análisis de atribución de anuncios no estará disponible."}, "commerce_account_read_settings": {"name": "Leer Configuración de Cuenta de Commerce", "description": "Permite que tu aplicación lea la configuración de la cuenta de commerce.", "impact": "Sin este permiso, no puedes leer la información de la cuenta de commerce."}, "commerce_account_manage_orders": {"name": "Administrar Pedidos de Cuenta de Commerce", "description": "Permite que tu aplicación lea y actualice los pedidos de la cuenta de commerce.", "impact": "Sin este permiso, no puedes administrar los pedidos de la cuenta de commerce."}, "commerce_account_read_orders": {"name": "Leer Pedidos de Cuenta de Commerce", "description": "Permite que tu aplicación lea los pedidos de la cuenta de commerce.", "impact": "Sin este permiso, no puedes leer los pedidos de la cuenta de commerce."}, "commerce_account_read_reports": {"name": "Leer Reportes de Cuenta de Commerce", "description": "Permite que tu aplicación lea datos de informes financieros para crear informes personalizados de impuestos, conciliación de efectivo y reembolsos para una cuenta de commerce.", "impact": "Sin este permiso, los informes financieros de commerce no estarán disponibles."}, "whatsapp_business_messaging": {"name": "Mensajería de WhatsApp Business", "description": "Permite que tu aplicación envíe y reciba mensajes a través de la API de WhatsApp Business.", "impact": "Sin este permiso, la mensajería de WhatsApp Business no estará disponible."}, "whatsapp_business_manage_events": {"name": "Administrar Eventos de WhatsApp Business", "description": "Permite que tu aplicación administre eventos para cuentas de WhatsApp Business.", "impact": "Sin este permiso, no puedes administrar eventos de WhatsApp Business."}, "pages_manage_engagement": {"name": "Administrar Interacción de Páginas", "description": "Permite que tu aplicación cree, edite y elimine comentarios publicados en la Página.", "impact": "Sin este permiso, no puedes administrar comentarios e interacción en las Páginas."}, "unavailableDescription": "Descripción no disponible.", "unavailableImpact": "Información de impacto no disponible.", "unknownPermission": {"name": "Per<PERSON>o Desconocido ({{permissionId}})", "description": "Este permiso no es reconocido por la aplicación. Sus detalles no están disponibles.", "impact": "No se puede determinar el impacto de revocar este permiso desconocido."}}}, "chat.agent.greeting": "¡Hola! ¿En qué puedo ayudarte hoy con la tienda Bandida?", "chat.input.placeholder": "Escribe tu mensaje aquí...", "chat.actions.think": "Pensar", "chat.actions.thinkTooltip": "Obtén respuestas reflexivas y detalladas para preguntas que requieren investigación.", "chat.actions.deepSearch": "Búsqueda Profunda", "chat.actions.deepSearchTooltip": "Explora la web a fondo para obtener información detallada sobre competidores, noticias recientes del mercado u otros conocimientos del sector que te interesen.", "chat.actions.deeperSearch": "Búsqueda Más Profunda", "chat.actions.deeperSearchTooltip": "Busca rápidamente en la web noticias recientes del mercado o actualizaciones de la competencia relevantes para tu tienda.", "feedbackModal": {"title": "Danos tu opinión", "textFieldLabel": "<PERSON>s comentarios", "errorEmptyText": "El texto de los comentarios no puede estar vacío.", "errorNoStore": "No se pudo identificar la tienda. Por favor, inicia sesión de nuevo.", "errorSubmitFailed": "Error al enviar comentarios. Por favor, inténtalo de nuevo.", "cancelButton": "<PERSON><PERSON><PERSON>", "submitButton": "Enviar"}, "socialMediaCard.visitProfile": "Visitar Perfil", "socialMediaCard.officialPage": "<PERSON><PERSON><PERSON><PERSON> Oficial", "socialMediaCard.notAvailable": "No Disponible", "cookies": {"firstTime": {"title": "🍪 ¡Bienvenido a D-Unit!", "message": "Usamos cookies para mejorar tu experiencia. Puedes gestionar tus preferencias en cualquier momento.", "accept": "<PERSON><PERSON><PERSON>", "manage": "Gestionar", "close": "<PERSON><PERSON><PERSON>"}, "banner": {"title": "Consentimiento de Cookies", "description": "Utilizamos cookies para mejorar tu experiencia, analizar el uso del sitio y ayudar con el marketing. Las cookies esenciales son necesarias para que el sitio funcione correctamente.", "legalNotice": "Este sitio web cumple con GDPR, CCPA, LGPD y otras regulaciones de privacidad. Puedes retirar el consentimiento en cualquier momento desde la configuración de cookies.", "acceptAll": "<PERSON><PERSON><PERSON>", "rejectAll": "<PERSON><PERSON><PERSON>", "rejectOptional": "Rechazar <PERSON>", "customize": "Personalizar", "close": "<PERSON><PERSON><PERSON>", "moreInfo": "Más información en nuestra", "privacyPolicy": "Política de Privacidad", "cookiePolicy": "Política de Cookies", "managePreferences": "Gestionar Preferencias", "settingsLink": "Configuración de Cookies"}, "categories": {"essential": {"name": "Cookies Esenciales", "description": "Estas cookies son necesarias para que el sitio web funcione y no se pueden desactivar. Generalmente se configuran solo en respuesta a acciones realizadas por ti, como iniciar sesión o completar formularios."}, "functional": {"name": "Cookies Funcionales", "description": "Estas cookies permiten que el sitio web proporcione una funcionalidad y personalización mejoradas, como las preferencias del usuario y la configuración de idioma."}, "analytics": {"name": "<PERSON><PERSON> de Análisis", "description": "Estas cookies nos permiten contar las visitas y las fuentes de tráfico para que podamos medir y mejorar el rendimiento de nuestro sitio."}, "performance": {"name": "Cookies de Rendimiento", "description": "Estas cookies nos ayudan a comprender cómo los visitantes interactúan con nuestro sitio web mediante la recopilación y el informe de información de forma anónima."}, "marketing": {"name": "Cookies de Marketing", "description": "Estas cookies pueden ser establecidas a través de nuestro sitio por nuestros socios publicitarios para crear un perfil de tus intereses y mostrarte anuncios relevantes en otros sitios."}}, "inventory": {"dunit_auth_token": {"name": "Token de Autenticación (dunit_auth_token)", "description": "Token de autenticación JWT para la gestión de la sesión del usuario."}, "dunit_refresh_token": {"name": "Token de Actualización (dunit_refresh_token)", "description": "Mecanismo de actualización de token para autenticación continua."}, "dunit_csrf_token": {"name": "Token CSRF (dunit_csrf_token)", "description": "Token de protección CSRF para envíos de formularios."}, "dunit_store_context": {"name": "Contexto de Tienda (dunit_store_context)", "description": "Contexto de la tienda actual para usuarios con múltiples tiendas."}, "CloudFront-*": {"name": "<PERSON><PERSON> (CloudFront-* )", "description": "Cookies de distribución y equilibrio de carga de AWS CloudFront."}, "dunit_user_preferences": {"name": "Preferencias de Usuario (dunit_user_preferences)", "description": "Preferencias y configuraciones de la interfaz de usuario."}, "dunit_language": {"name": "Idioma (dunit_language)", "description": "Preferencia de idioma seleccionada por el usuario."}, "dunit_theme": {"name": "<PERSON><PERSON> (dunit_theme)", "description": "Preferencia de tema de la interfaz de usuario (modo claro/oscuro)."}, "dunit_analytics_session": {"name": "Sesión de Análisis (dunit_analytics_session)", "description": "Identificador de sesión de análisis para el seguimiento del uso."}, "dunit_page_views": {"name": "Vistas de Página (dunit_page_views)", "description": "Seguimiento de vistas de página para el análisis de la navegación."}, "dunit_performance_timing": {"name": "Temporización de Rendimiento (dunit_performance_timing)", "description": "Métricas de rendimiento del sitio web y tiempos de carga."}, "dunit_error_tracking": {"name": "Seguimiento de Errores (dunit_error_tracking)", "description": "Información de monitoreo de errores y depuración."}, "fbsr_*": {"name": "Cookies de Sesión de Facebook SDK (fbsr_*)", "description": "Cookies de sesión del SDK de Facebook para la integración con Meta."}, "fb_access_token": {"name": "Token de Acceso de Facebook (fb_access_token)", "description": "Token de acceso de Facebook para la integración de API."}}, "cookieManagement": {"title": "Gestión de Cookies", "description": "Gestiona tus preferencias de cookies. Puedes habilitar o deshabilitar categorías de cookies no esenciales. Las cookies esenciales son necesarias para la funcionalidad del sitio.", "lastUpdated": "Última actualización de consentimiento: {{date}}", "consentNotGiven": "Aún no has dado tu consentimiento para las cookies.", "acceptAllButton": "<PERSON><PERSON><PERSON>", "rejectOptionalButton": "Rechazar <PERSON>", "savePreferencesButton": "Guardar Preferencias", "resetSettingsButton": "Restablecer Todas las Configuraciones de Cookies", "preferencesSaved": "Preferencias de cookies guardadas con éxito.", "preferencesReset": "Preferencias de cookies restablecidas a los valores predeterminados.", "errorSaving": "Error al guardar las preferencias de cookies.", "consentStatusTitle": "Estado del Consentimiento", "consentGiven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consentPartiallyGiven": "Consentimiento Parcialm<PERSON>", "consentNone": "Sin Consentimiento Previo", "cookieCategoriesTitle": "Categorías de Cookies", "categoryEnabled": "Cookies de {{category}} habilitadas", "categoryDisabled": "Cookies de {{category}} deshabilitadas", "allAccepted": "Todas las cookies aceptadas con éxito", "optionalRejected": "Cookies opcionales rechazadas", "cookieDetailsTitle": "Detalles de Cookies Utilizadas", "noCookiesInCategory": "No hay cookies específicas listadas para esta categoría en este momento.", "cookieName": "Nombre", "cookieCategory": "Categoría", "cookieDescription": "Descripción", "cookieDuration": "Duración", "cookieProvider": "<PERSON><PERSON><PERSON><PERSON>", "cookieType": "Tipo", "essentialMarker": "(Esencial)", "firstParty": "Propia", "thirdParty": "De Terceros", "session": "Sesión", "learnMoreLink": "Más información sobre cookies", "legalInformationTitle": "Información Legal", "legalComplianceText": "Este sitio web cumple con GDPR, CCPA, LGPD, PIPEDA y otras regulaciones de privacidad aplicables. Tienes derecho a:", "legalRightWithdraw": "Retirar el consentimiento en cualquier momento.", "legalRightDelete": "Solicitar la eliminación de tus datos.", "legalRightAccess": "Acceder a tus datos personales.", "legalRightPortability": "Portabilidad de datos.", "supportedRegulationsTitle": "Regulaciones Soportadas", "consentVersion": "Versión de Consentimiento: {{version}}", "compactTitle": "Cookies", "manageLabel": "Gestionar", "badgeCount": "{{count}} cookies", "expandDetails": "<PERSON><PERSON>", "collapseDetails": "<PERSON><PERSON><PERSON><PERSON>", "alertTitle": "Actualización de Consentimiento Requerida", "alertMessage": "Nuestra política de cookies ha sido actualizada. Por favor, revisa y actualiza tus preferencias.", "alertButton": "<PERSON><PERSON><PERSON>"}, "details": {"title": "Preferencias de Cookies", "description": "Gestiona tus preferencias de cookies a continuación. Puedes habilitar o deshabilitar categorías de cookies, y ver información detallada sobre cada cookie que utilizamos.", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "rejectAll": "Re<PERSON>zar <PERSON>das las Opcionales", "save": "Guardar Preferencias", "version": "Versión de Consentimiento", "lastUpdated": "Última Actualización"}, "settings": {"title": "Configuración de Cookies", "description": "Gestiona tus preferencias de cookies y ve información detallada sobre las cookies que utilizamos. Los cambios tendrán efecto inmediatamente.", "consentGiven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consentVersion": "Versión", "consentId": "ID de Consentimiento", "save": "Guardar Preferencias", "resetConsent": "Restable<PERSON>", "saved": "Preferencias de cookies guardadas con éxito", "reset": "El consentimiento de cookies ha sido restablecido", "legalInfo": "Información Legal", "legalText": "Este sitio web cumple con GDPR, CCPA, LGPD, PIPEDA y otras regulaciones de privacidad aplicables. Tienes derecho a:", "right1": "Retirar el consentimiento en cualquier momento", "right2": "Solicitar la eliminación de tus datos", "right3": "Acceder a tus datos personales", "right4": "Portabilidad de datos", "version": "Versión de Configuración", "supportedRegulations": "Regulaciones Soportadas", "details": "Detalles", "noConsent": "Aún no se ha dado consentimiento para cookies", "allAccepted": "Todas las cookies aceptadas", "optionalRejected": "Cookies opcionales rechazadas", "privacyPolicyLink": "Ver nuestra Política de Privacidad"}, "category": {"essential": "Esencial", "cookies": "cookies", "cookiesUsed": "Cookies en esta categoría:"}, "table": {"name": "Nombre de Cookie", "purpose": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Duración", "type": "Tipo", "provider": "<PERSON><PERSON><PERSON><PERSON>"}, "types": {"first-party": "Propia", "third-party": "De Terceros"}, "durations": {"session": "Sesión", "persistent": "Persistente", "timeUnits": {"hour": "hora", "hours": "horas", "day": "día", "days": "días", "year": "año", "years": "años", "session": "Sesión"}}, "purposes": {"authSession": "Autenticación y gestión de sesiones de usuario", "sessionRenewal": "Renovación de sesión y seguridad", "csrfProtection": "Seguridad - Protección CSRF", "storeIsolation": "Aislamiento de datos de tienda y control de acceso", "contentDelivery": "Entrega de contenido y balanceamiento de carga", "userPreferences": "Almacenamiento de preferencias de usuario y personalización", "languageLocalization": "Localización de idioma", "themeCustomization": "Personalización de tema de interfaz", "usageAnalytics": "Análisis de uso del sitio web y mejoramiento", "navigationPatterns": "Patrones de navegación del usuario y rendimiento de página", "performanceMonitoring": "Monitoreo de rendimiento y optimización", "errorTracking": "Detección de errores y estabilidad de la aplicación", "metaIntegration": "Integración con plataforma Meta/Facebook", "facebookTools": "Integración con herramientas comerciales de Facebook"}}, "storeView": {"errorNoPermission": "No tienes permisos para ver esta tienda o no existe.", "invalidStoreId": "ID de tienda inválido o permisos insuficientes.", "incompleteUserData": "Los datos del usuario están incompletos. No se puede mostrar el tablero.", "adminViewLabel": "Vista de Administrador: Tienda #{{storeId}}"}, "cookiePolicy": {"title": "Política de Cookies", "subtitle": "Información completa sobre las cookies utilizadas en D-Unit", "lastUpdated": "Última Actualización: {{date}}", "effectiveDate": "Fecha de Entrada en Vigor: 1 de mayo, 2025", "introduction": {"title": "Introducción", "content": "Esta Política de Cookies explica cómo D-Unit utiliza cookies y tecnologías similares para reconocerte cuando visitas nuestro sitio web. Explica qué son estas tecnologías y por qué las usamos, así como tus derechos para controlar nuestro uso de las mismas."}, "whatAreCookies": {"title": "¿Qué son las Cookies?", "content": "Las cookies son pequeños archivos de datos que se colocan en tu computadora o dispositivo móvil cuando visitas un sitio web. Las cookies son ampliamente utilizadas por los propietarios de sitios web para hacer que sus sitios funcionen, o funcionen de manera más eficiente, así como para proporcionar información de informes."}, "whyWeUseCookies": {"title": "¿Por qué usamos Cookies?", "content": "Utilizamos cookies por varias razones. Algunas cookies son necesarias por razones técnicas para que nuestro sitio web funcione, y nos referimos a estas como cookies 'esenciales' o 'estrictamente necesarias'. Otras cookies nos permiten rastrear y dirigir los intereses de nuestros usuarios para mejorar la experiencia en nuestro sitio web."}, "categories": {"title": "Categorías de Cookies que Utilizamos", "essential": {"title": "Cookies Esenciales", "description": "Estas cookies son estrictamente necesarias para proporcionarte servicios disponibles a través de nuestro sitio web y para usar algunas de sus características, como el acceso a áreas seguras. Debido a que estas cookies son estrictamente necesarias para entregar el sitio web, no puedes rechazarlas sin afectar cómo funciona nuestro sitio."}, "functional": {"title": "Cookies Funcionales", "description": "Estas cookies permiten que el sitio web proporcione funcionalidad mejorada y personalización. Pueden ser establecidas por nosotros o por proveedores de terceros cuyos servicios hemos agregado a nuestras páginas."}, "analytics": {"title": "<PERSON><PERSON> de Análisis", "description": "Estas cookies nos permiten contar visitas y fuentes de tráfico para que podamos medir y mejorar el rendimiento de nuestro sitio. Nos ayudan a saber qué páginas son las más y menos populares y ver cómo los visitantes se mueven por el sitio."}, "performance": {"title": "Cookies de Rendimiento", "description": "Estas cookies se utilizan para mejorar el rendimiento y la funcionalidad de nuestro sitio web, pero no son esenciales para su uso. Sin embargo, sin estas cookies, cierta funcionalidad puede no estar disponible."}, "marketing": {"title": "Cookies de Marketing", "description": "Estas cookies pueden ser establecidas a través de nuestro sitio por nuestros socios publicitarios. Pueden ser utilizadas por esas empresas para construir un perfil de tus intereses y mostrarte anuncios relevantes en otros sitios."}}, "cookieInventory": {"title": "Inventario Detallado de Cookies", "description": "A continuación se encuentra una lista completa de todas las cookies utilizadas en nuestro sitio web:", "tableHeaders": {"name": "Nombre de Cookie", "purpose": "<PERSON><PERSON><PERSON><PERSON>", "category": "Categoría", "duration": "Duración", "type": "Tipo", "provider": "<PERSON><PERSON><PERSON><PERSON>"}}, "management": {"title": "Cómo Gestionar tus Preferencias de Cookies", "content": "Tienes varias opciones para gestionar las cookies:", "options": {"browserSettings": "Configuración del Navegador: La mayoría de los navegadores web te permiten controlar las cookies a través de sus preferencias de configuración.", "ourSettings": "Nuestra Configuración de Cookies: Puedes gestionar tus preferencias de cookies en cualquier momento a través de nuestro panel de configuración de cookies.", "optOut": "Enlaces de Exclusión: Para ciertas cookies, proporcionamos mecanismos de exclusión directos."}}, "yourRights": {"title": "Tus Derechos Bajo las Leyes de Privacidad", "content": "Bajo varias regulaciones de privacidad (GDPR, CCPA, LGPD, PIPEDA), tienes los siguientes derechos:", "rights": {"access": "Acceso: Tienes el derecho de saber qué datos personales recopilamos y cómo los utilizamos.", "rectification": "Rectificación: Tienes el derecho de corregir datos personales inexactos.", "erasure": "Eliminación: Tienes el derecho de solicitar la eliminación de tus datos personales.", "portability": "Portabilidad de Datos: Tienes el derecho de recibir tus datos personales en un formato estructurado.", "withdraw": "Retirar Consentimiento: <PERSON><PERSON><PERSON> retirar tu consentimiento para cookies no esenciales en cualquier momento.", "object": "Objetar: Tienes el derecho de objetar ciertos tipos de procesamiento."}}, "legalBasis": {"title": "Base Legal para el Procesamiento", "content": "Procesamos cookies basándose en las siguientes bases legales:", "grounds": {"consent": "Consentimiento: Para cookies no esenciales, nos basamos en tu consentimiento explícito.", "legitimate": "Interés Legítimo: Para cookies de análisis y rendimiento que nos ayudan a mejorar nuestros servicios.", "necessary": "Necesidad Contractual: Para cookies esenciales requeridas para proporcionar nuestros servicios."}}, "thirdParty": {"title": "Cookies de Terceros", "content": "Algunas cookies son colocadas por servicios de terceros que aparecen en nuestras páginas. No controlamos estas cookies, y debes verificar el sitio web del tercero relevante para más información.", "services": {"meta": "Meta/Facebook: Para integración de redes sociales y publicidad", "google": "Google Analytics: Para análisis de uso del sitio web", "aws": "Amazon Web Services: Para entrega de contenido y rendimiento"}}, "updates": {"title": "Actualizaciones a esta Política", "content": "Podemos actualizar esta Política de Cookies de vez en cuando para reflejar cambios en nuestras prácticas o por razones legales, operacionales o regulatorias. Te notificaremos de cualquier cambio material publicando la nueva política en esta página con una nueva fecha de 'Última Actualización'."}, "contact": {"title": "Información de Contacto", "content": "Si tienes alguna pregunta sobre nuestro uso de cookies o esta Política de Cookies, por favor contáctanos:", "email": "Co<PERSON>o electrónico: <EMAIL>", "address": "Oficial de Protección de Datos, D-Unit"}, "compliance": {"title": "Cumplimiento Regulatorio", "frameworks": {"gdpr": "GDPR (Reglamento General de Protección de Datos) - Unión Europea", "ccpa": "CCPA (Ley de Privacidad del Consumidor de California) - California, EE.UU.", "lgpd": "LGPD (Lei Geral de Proteção de Dados) - Brasil", "pipeda": "PIPEDA (Ley de Protección de Información Personal y Documentos Electrónicos) - Canadá"}}, "navigation": {"backToTop": "Volver al Inicio", "manageCookies": "Gestionar Preferencias de Cookies", "viewPrivacyPolicy": "Ver Política de Privacidad"}}, "verificationCodeLabel": "Código de verificación", "newPasswordLabel": "Nueva contraseña", "confirmPasswordLabel": "Confirmar con<PERSON>", "successPasswordCodeSent": "Te enviamos un código de verificación por correo.", "successPasswordReset": "Contraseña restablecida. Redirigiendo…", "resetPasswordButton": "Restablecer contraseña", "creditsRemaining": "Créditos Restantes", "errorUnknownAdd": "<PERSON><PERSON>r al añadir crédit<PERSON>", "errors": {"insufficientCredits": "Te quedaste sin créditos para usar el chat, por favor contacta al administrador"}}