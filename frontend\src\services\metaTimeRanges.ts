/**
 * Standardized time range configurations for Meta (Facebook/Instagram) APIs
 * Based on API limitations and best practices
 */

import { TimeRangePreset } from './types';

/**
 * Standard time range presets optimized for Meta APIs
 * - Facebook API: Generally supports longer periods
 * - Instagram API: Limited to 30 days for most insights
 */
export const META_TIME_RANGE_PRESETS = {
  // For components that work with both platforms
  STANDARD: ['7d', '30d', 'custom'] as TimeRangePreset[],
  
  // For Facebook-specific components (simplified to 30d and lifetime only)
  FACEBOOK: ['30d', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For Instagram-specific components (30-day limit)
  INSTAGRAM: ['7d', '30d', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For ad-related components (supports longer periods for analysis)
  ADVERTISING: ['7d', '30d', '90d', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For posts and content (recent content is most relevant)
  CONTENT: ['7d', '30d', '90d', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For Instagram posts specifically (supports longer periods)
  INSTAGRAM_POSTS: ['7d', '30d', '1y', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For demographics (lifetime data is often more meaningful)
  DEMOGRAPHICS: ['30d', '90d', 'lifetime', 'custom'] as TimeRangePreset[],
  
  // For campaigns (need longer periods for trend analysis)
  CAMPAIGNS: ['30d', '90d', 'lifetime', 'custom'] as TimeRangePreset[]
};

/**
 * Get appropriate time range presets based on platform and component type
 */
export function getMetaTimeRangePresets(
  platform: 'facebook' | 'instagram' | 'mixed',
  componentType: 'standard' | 'advertising' | 'content' | 'demographics' | 'campaigns' = 'standard'
): TimeRangePreset[] {
  // Instagram has stricter limitations
  if (platform === 'instagram') {
    switch (componentType) {
      case 'advertising':
      case 'campaigns':
        return META_TIME_RANGE_PRESETS.INSTAGRAM; // Still limited to 30 days
      case 'content':
        return META_TIME_RANGE_PRESETS.INSTAGRAM_POSTS; // Use longer periods for Instagram posts
      case 'demographics':
        return META_TIME_RANGE_PRESETS.INSTAGRAM;
      default:
        return META_TIME_RANGE_PRESETS.INSTAGRAM;
    }
  }
  
  // Facebook supports longer periods
  if (platform === 'facebook') {
    switch (componentType) {
      case 'advertising':
        return META_TIME_RANGE_PRESETS.ADVERTISING;
      case 'content':
        return META_TIME_RANGE_PRESETS.CONTENT;
      case 'demographics':
        return META_TIME_RANGE_PRESETS.DEMOGRAPHICS;
      case 'campaigns':
        return META_TIME_RANGE_PRESETS.CAMPAIGNS;
      default:
        return META_TIME_RANGE_PRESETS.FACEBOOK;
    }
  }
  
  // Mixed platform or unknown - use conservative presets
  return META_TIME_RANGE_PRESETS.STANDARD;
}

/**
 * Get default time range (30 days) for Meta components
 */
export function getDefaultMetaTimeRange(): { since: string; until: string; preset: TimeRangePreset } {
  const until = new Date();
  const since = new Date();
  since.setDate(since.getDate() - 30);
  
  return {
    since: since.toISOString().split('T')[0],
    until: until.toISOString().split('T')[0],
    preset: '30d'
  };
}

/**
 * Get default time range for Instagram posts (1 year)
 */
export function getDefaultInstagramPostsTimeRange(): { since: string; until: string; preset: TimeRangePreset } {
  const until = new Date();
  const since = new Date();
  since.setFullYear(since.getFullYear() - 1);
  
  return {
    since: since.toISOString().split('T')[0],
    until: until.toISOString().split('T')[0],
    preset: '1y'
  };
}

/**
 * Validate and adjust time range for Meta API limitations
 */
export function validateMetaTimeRange(
  timeRange: { since: string; until: string },
  platform: 'facebook' | 'instagram' | 'mixed',
  maxDays: number = 90
): { since: string; until: string; adjusted: boolean } {
  const sinceDate = new Date(timeRange.since);
  const untilDate = new Date(timeRange.until);
  const daysDiff = Math.floor((untilDate.getTime() - sinceDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Apply platform-specific limits
  const platformMaxDays = platform === 'instagram' ? Math.min(maxDays, 30) : maxDays;
  
  if (daysDiff <= platformMaxDays) {
    return { ...timeRange, adjusted: false };
  }
  
  // Adjust to maximum allowed period
  const adjustedSince = new Date(untilDate);
  adjustedSince.setDate(adjustedSince.getDate() - platformMaxDays);
  
  return {
    since: adjustedSince.toISOString().split('T')[0],
    until: timeRange.until,
    adjusted: true
  };
}