#!/usr/bin/env python3
"""
Test Credits Endpoint

This script tests the credits endpoint directly to see what's happening.

Usage:
    python test_credits_endpoint.py
"""

import asyncio
import logging
import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from config.database import db_main, db_analysis

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)

async def test_credits_endpoint_direct():
    """Test the credits endpoint by directly checking the database."""
    
    print("🔍 Direct Credits Test")
    print("=" * 50)
    
    # Test stores
    test_stores = ["566", "4", "10"]  # From the debug output
    
    for store_id in test_stores:
        print(f"\nTesting store {store_id}:")
        
        # Check if store exists in active_stores_cache
        try:
            doc = await db_analysis["active_stores_cache"].find_one({"_id": store_id}, {"credits": 1, "name": 1})
            if doc:
                credits = int(doc.get("credits", 0))
                name = doc.get("name", "Unknown")
                print(f"  ✅ Store found: {name} - {credits} credits")
            else:
                print(f"  ❌ Store NOT found in active_stores_cache")
        except Exception as e:
            print(f"  ❌ Error: {e}")

async def test_user_authentication():
    """Test user authentication for the problematic users."""
    
    print(f"\n🔍 User Authentication Test")
    print("=" * 50)
    
    # Test users
    test_users = [
        {"email": "<EMAIL>", "store": "566"},
        {"email": "<EMAIL>", "store": "4"},
        {"email": "<EMAIL>", "store": "3"}
    ]
    
    for user_info in test_users:
        email = user_info["email"]
        expected_store = user_info["store"]
        
        print(f"\nTesting user: {email}")
        
        # Check if user exists
        try:
            user = await db_main["store_users"].find_one({"email": email})
            if user:
                user_store = str(user.get("id_store"))
                role = user.get("role", "user")
                print(f"  ✅ User found: store={user_store}, role={role}")
                
                # Check store access
                if role == "admin":
                    print(f"  ✅ Admin access - can access any store")
                elif user_store == expected_store:
                    print(f"  ✅ Store access - matches expected store {expected_store}")
                else:
                    print(f"  ❌ Store mismatch - user store: {user_store}, expected: {expected_store}")
            else:
                print(f"  ❌ User NOT found")
        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_api_endpoint():
    """Test the actual API endpoint with a real HTTP request."""
    
    print(f"\n🔍 API Endpoint Test")
    print("=" * 50)
    
    # This would require a valid JWT token, which we don't have
    # But we can test if the endpoint is reachable
    
    base_url = "https://d1w62wlepuin1r.cloudfront.net"
    endpoint = f"{base_url}/api/store/566/credits"
    
    print(f"Testing endpoint: {endpoint}")
    
    try:
        # Test without authentication (should get 401/403, not 404)
        response = requests.get(endpoint, timeout=10)
        print(f"  Status Code: {response.status_code}")
        print(f"  Response: {response.text[:200]}...")
        
        if response.status_code == 404:
            print("  ❌ 404 suggests the endpoint doesn't exist or routing issue")
        elif response.status_code in [401, 403]:
            print("  ✅ Authentication error (expected without token)")
        else:
            print(f"  ❓ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Request failed: {e}")

async def check_backend_logs():
    """Check if we can find any backend logs or errors."""
    
    print(f"\n🔍 Backend Status Check")
    print("=" * 50)
    
    # Check if backend files exist and are accessible
    backend_files = [
        "backend/routes/store.py",
        "backend/main.py",
        "backend/config/database.py"
    ]
    
    for file_path in backend_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path} exists")
        else:
            print(f"  ❌ {file_path} missing")
    
    # Check if we can import the route
    try:
        from routes.store import router
        print(f"  ✅ Store router imported successfully")
        
        # Check if credits route exists
        routes = [route.path for route in router.routes]
        credits_route = "/{store_id}/credits"
        if credits_route in routes:
            print(f"  ✅ Credits route found: {credits_route}")
        else:
            print(f"  ❌ Credits route not found in: {routes}")
            
    except Exception as e:
        print(f"  ❌ Error importing store router: {e}")

async def main():
    """Main function."""
    print("Credits Endpoint Test Script")
    print("=" * 50)
    
    try:
        await test_credits_endpoint_direct()
        await test_user_authentication()
        test_api_endpoint()
        await check_backend_logs()
        
        print(f"\n🎯 DIAGNOSIS")
        print("=" * 50)
        print("If the database checks pass but the API returns 404:")
        print("1. Backend server might not be running")
        print("2. Route might not be registered correctly")
        print("3. CloudFront/proxy configuration issue")
        print("4. Authentication middleware failing before reaching the endpoint")
        
    except Exception as e:
        logger.error(f"❌ Script failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
