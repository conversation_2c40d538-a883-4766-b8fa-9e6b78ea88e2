import { 
  MetaAuthResponse, 
  MetaLoginResponse, 
  MetaBusinessAccount,
  MetaPage,
  MetaError,
  MetaErrorType,
  META_PERMISSIONS
} from './types';
import { API_URL } from '../config/api';
import { isFacebookSDKReady } from './init';
import { META_AUTH_STATE_CHANGE } from './authChecker';
import { logger } from '../utils/logger';

// Define Platform Configuration
const FACEBOOK_CONFIG = {
  permissions: META_PERMISSIONS.facebook,
  criticalPermissions: ['pages_show_list', 'pages_read_engagement', 'public_profile', 'email'],
  loginScope: 'public_profile,email,pages_show_list,pages_read_engagement,business_management,instagram_basic,instagram_manage_insights' // Request both Facebook and Instagram permissions
};

const INSTAGRAM_CONFIG = {
  permissions: META_PERMISSIONS.instagram,
  criticalPermissions: ['instagram_basic', 'instagram_manage_insights', 'public_profile', 'email'],
  loginScope: 'public_profile,email,pages_show_list,pages_read_engagement,business_management,instagram_basic,instagram_manage_insights' // Request both Facebook and Instagram permissions
};

// List of deprecated permissions that should be filtered out in Meta API v23.0
const DEPRECATED_PERMISSIONS = new Set([
  "threads_business_basic",
  "manage_app_solution",
  "manage_app_solution_settings",
  "pages_utility_messaging",
  "pages_utility_messages"
]);

// Constants for localStorage keys
const META_TOKEN_KEY = 'meta_access_token';
const META_TOKEN_EXPIRES_KEY = 'meta_token_expires';
const META_AUTH_STATE_KEY = 'meta_auth_state';

// For token validation
let lastValidationTime = 0;
const MIN_VALIDATION_INTERVAL = 10000; // 10 seconds between validations

// Helper function to silence unused param linter errors
const silenceUnusedError = <T>(error: T): void => { 
  /* This function intentionally does nothing with the error parameter */
  void error; // Use void operator to silence the linter warning
};

// Instead of using namespace, define interface for Facebook login options
export interface FacebookLoginOptions {
  scope: string;
  return_scopes?: boolean;
  auth_type?: string;
  [key: string]: unknown;
}

// Interface for general Facebook SDK options 
interface FacebookSDKOptions {
  include_headers?: boolean;
  timestamp?: number;
  [key: string]: unknown;
}

// Interface for Meta login status response
interface MetaLoginStatusResponse {
  status: 'connected' | 'not_authorized' | 'unknown';
  authResponse?: {
    accessToken: string;
    userID: string;
    expiresIn: number;
    grantedScopes?: string;
  };
}

// Helper function to temporarily suppress FB access token override warnings
const suppressFBAccessTokenWarning = (fn: () => void, durationMs: number = 10000) => {
  const originalWarn = console.warn;
  const originalError = console.error;
  const originalLog = console.log;
  
  // Suppress warnings
  console.warn = (...args: unknown[]) => {
    const message = args.join(' ');
    // Check if this is the specific FB access token warning
    if (message.includes('overriding current access token') || 
        message.includes('some other app is expecting different access token') ||
        message.includes('You are overriding current access token') ||
        message.includes('access token') && message.includes('override')) {
      // Suppress this specific warning
      return;
    }
    // Allow all other warnings through
    originalWarn.apply(console, args);
  };
  
  // Suppress errors that might be related
  console.error = (...args: unknown[]) => {
    const message = args.join(' ');
    if (message.includes('overriding current access token') || 
        message.includes('some other app is expecting different access token') ||
        message.includes('You are overriding current access token')) {
      // Suppress this specific error
      return;
    }
    // Allow all other errors through
    originalError.apply(console, args);
  };
  
  // Suppress logs that might be related
  console.log = (...args: unknown[]) => {
    const message = args.join(' ');
    if (message.includes('overriding current access token') || 
        message.includes('some other app is expecting different access token') ||
        message.includes('You are overriding current access token')) {
      // Suppress this specific log
      return;
    }
    // Allow all other logs through
    originalLog.apply(console, args);
  };
  
  // Execute the function immediately
  fn();
  
  // Keep suppression active for the specified duration to handle async SDK operations
  setTimeout(() => {
    console.warn = originalWarn;
    console.error = originalError;
    console.log = originalLog;
  }, durationMs);
};

export class MetaAuthService {
  // Private static property to store the access token in memory
  private static accessToken: string | null = null;

  /**
   * Get the current access token, checking both memory and localStorage
   * @returns string | null - The access token or null
   */
  static getAccessToken(): string | null {
    // First check memory
    if (this.accessToken) {
      return this.accessToken;
    }
    
    // Then check localStorage
    try {
      const storedToken = localStorage.getItem(META_TOKEN_KEY);
      const expiresStr = localStorage.getItem(META_TOKEN_EXPIRES_KEY);
      
      if (storedToken && expiresStr) {
        const expiresAt = parseInt(expiresStr, 10);
        const now = Date.now();
        
        // Check if token is expired
        if (expiresAt > now) {
          // Save token to memory for faster access
          this.accessToken = storedToken;
          return storedToken;
        } else {
          logger.warn('Stored Meta token has expired, clearing');
          this.clearToken();
        }
      }
    } catch (e) {
      logger.error('Error reading Meta token from localStorage:', e);
    }
    
    return null;
  }
  
  /**
   * Save the Meta access token to both memory and localStorage
   * @param token The access token to save
   * @param expiresIn Optional expiration time in seconds
   */
  static saveToken(token: string, expiresIn?: number): void {
    if (!token) return;
    
    this.accessToken = token;
    localStorage.setItem(META_TOKEN_KEY, token);
    
    // Set token expiry (default to 60 days if not provided)
    const expirySeconds = expiresIn || 60 * 24 * 60 * 60; // 60 days in seconds
    const expiry = Date.now() + (expirySeconds * 1000);
    localStorage.setItem(META_TOKEN_EXPIRES_KEY, expiry.toString());
    
    // Dispatch event for other components to know auth state changed
    const event = new CustomEvent(META_AUTH_STATE_CHANGE, { 
      detail: { isAuthenticated: true }
    });
    document.dispatchEvent(event);
    
    logger.info(`Meta token saved and will expire in ${expirySeconds} seconds`);
    
    // Note: We do NOT call FB.setAccessToken() here to avoid the "overriding access token" warning
    // Instead, all FB.api calls should pass the token as a parameter
  }
  
  /**
   * Clear the Meta access token from both memory and localStorage
   */
  static clearToken(): void {
    this.accessToken = null;
    localStorage.removeItem(META_TOKEN_KEY);
    localStorage.removeItem(META_TOKEN_EXPIRES_KEY);
    localStorage.removeItem(META_AUTH_STATE_KEY);

    // Also clear any legacy token storage keys
    localStorage.removeItem('meta_access_token');
    localStorage.removeItem('meta_user_id');
    localStorage.removeItem('meta_expires_in');
    localStorage.removeItem('meta_granted_scopes');

    // Clear Facebook SDK cached auth state if available
    if (typeof window.FB !== 'undefined') {
      try {
        // Force logout from Facebook SDK to clear cached auth state
        window.FB.logout(() => {
          logger.debug('Facebook SDK logout completed during token clear');
        });
      } catch (fbError) {
        logger.debug('Facebook SDK logout not available or failed during token clear:', fbError);
      }
    }

    // Dispatch event for other components to know auth state changed
    const event = new CustomEvent(META_AUTH_STATE_CHANGE, {
      detail: { isAuthenticated: false }
    });
    document.dispatchEvent(event);

    logger.info('Meta token cleared');
  }

  /**
   * Detect browser compatibility issues that might affect Meta login
   */
  private static detectBrowserCompatibility() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isFirefox = userAgent.includes('firefox');
    const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
    const isChrome = userAgent.includes('chrome');
    const isEdge = userAgent.includes('edge');

    // Check for known privacy-focused browsers
    const isPrivacyBrowser = userAgent.includes('brave') ||
                            userAgent.includes('duckduckgo') ||
                            userAgent.includes('tor');

    // Detect if third-party cookies are likely blocked
    const supportsThirdPartyCookies = !isPrivacyBrowser &&
                                     !(isSafari && !document.cookie.includes('SameSite=None'));

    // Safari and Firefox have stricter default privacy settings
    const hasStrictPrivacySettings = isSafari || isFirefox || isPrivacyBrowser;

    const browserName = isFirefox ? 'Firefox' :
                       isSafari ? 'Safari' :
                       isChrome ? 'Chrome' :
                       isEdge ? 'Edge' :
                       isPrivacyBrowser ? 'Privacy Browser' : 'Unknown';

    return {
      supportsThirdPartyCookies,
      hasStrictPrivacySettings,
      requiresSpecialHandling: hasStrictPrivacySettings || !supportsThirdPartyCookies,
      browserName
    };
  }

  /**
   * Check if the user has a valid Meta token
   * @returns True if a valid token exists, false otherwise
   */
  static hasValidToken(): boolean {
    const token = this.getAccessToken();
    if (!token) return false;
    
    try {
      const expiresStr = localStorage.getItem(META_TOKEN_EXPIRES_KEY);
      if (expiresStr) {
        const expiresAt = parseInt(expiresStr, 10);
        const now = Date.now();
        return expiresAt > now;
      }
    } catch (e) {
      logger.error('Error checking token expiry:', e);
    }
    
    return false;
  }

  /**
   * Verify if the user is currently logged in to Facebook
   * @returns Promise<boolean> - true if logged in, false otherwise
   */
  private static async isLoggedIn(): Promise<boolean> {
    try {
      // First check if we have a valid token in localStorage
      if (this.hasValidToken()) {
        logger.debug('Found valid Meta token in storage');
        return true;
      }
      
      // Check if we have auth state in localStorage
      const authStateStr = localStorage.getItem(META_AUTH_STATE_KEY);
      if (authStateStr) {
        try {
          const authState = JSON.parse(authStateStr);
          // If auth state is recent (within last 24 hours), consider it valid
          if (authState.isLoggedIn && (Date.now() - authState.timestamp < 24 * 60 * 60 * 1000)) {
            logger.debug('Found recent auth state, checking with FB SDK');
          } else {
            logger.debug('Auth state is stale, checking with FB SDK');
          }
        } catch (error) {
          silenceUnusedError(error);
          logger.warn('Failed to parse stored auth state');
        }
      }
      
      // If no valid token found, check with FB SDK
      // Check if FB SDK is loaded
      if (typeof window.FB === 'undefined') {
        logger.warn('Facebook SDK not loaded');
        return false;
      }
      
      // Use Promise to handle the async nature of FB.getLoginStatus
      const response = await new Promise<MetaLoginStatusResponse>((resolve) => {
        // Some FB SDK versions don't support the second parameter
        try {
          window.FB.getLoginStatus((response) => {
            resolve(response);
          });
        } catch (error) {
          silenceUnusedError(error);
          logger.warn('Error with getLoginStatus, falling back to basic check');
          resolve({ status: 'unknown' });
        }
      });
      
      const isConnected = response.status === 'connected';
      
      // If connected, save the token
      if (isConnected && response.authResponse && response.authResponse.accessToken) {
        this.saveToken(
          response.authResponse.accessToken, 
          response.authResponse.expiresIn
        );
        
        // Update auth state
        localStorage.setItem(META_AUTH_STATE_KEY, JSON.stringify({
          isLoggedIn: true,
          platform: 'facebook', // Default to facebook
          timestamp: Date.now()
        }));
      } else if (!isConnected) {
        // Clear any invalid tokens if not connected
        this.clearToken();
      }
      
      logger.debug('FB.login full response:', response);
      if (response && response.authResponse) {
        if (response.authResponse.grantedScopes) {
          logger.debug('Granted scopes:', response.authResponse.grantedScopes);
          if (!response.authResponse.grantedScopes.includes('public_profile')) {
            logger.warn('public_profile NOT granted in grantedScopes!');
          } else {
            logger.debug('public_profile IS granted in grantedScopes.');
          }
        } else {
          logger.warn('No grantedScopes found in FB.login response.');
        }
      } else {
        logger.warn('No authResponse in FB.login response.');
      }
      
      return isConnected;
    } catch (error) {
      logger.error('Error checking Facebook login status:', error);
      return false;
    }
  }

  /**
   * Ensures the user is logged in before proceeding with API calls
   * @returns Promise<void> - throws error if not logged in
   */
  private static async ensureLoggedIn(): Promise<void> {
    const isLoggedIn = await this.isLoggedIn();
    if (!isLoggedIn) {
      logger.error('User is not logged in to Facebook');
      throw {
        message: 'You need to log in to Meta first',
        type: MetaErrorType.AUTH_FAILED
      };
    }
  }

  /**
   * Check if the current Meta token is valid
   * @returns Promise<boolean> True if token is valid, false otherwise
   */
  static async checkTokenStatus(): Promise<boolean> {
    try {
      // Debounce validation checks to prevent excessive API calls
      const now = Date.now();
      if (now - lastValidationTime < MIN_VALIDATION_INTERVAL) {
        logger.debug('Skipping token validation, checked recently');
        // Return cached result if we checked recently
        return this.hasValidToken();
      }
      
      // Update last validation time
      lastValidationTime = now;
      
      // Check if we have a valid token
      const token = this.getAccessToken();
      if (!token) {
        logger.warn('No Meta token available to check');
        return false;
      }
      
      logger.debug('Checking Meta token status...');
      
      // First try to check with FB SDK (faster)
      try {
        const isLoggedIn = await this.isLoggedIn();
        if (isLoggedIn) {
          logger.debug('Token confirmed valid with FB SDK');
          return true;
        }
      } catch (error) {
        logger.warn('Error checking login status with FB SDK:', error);
        // Continue with API check
      }
      
      // API check - try to get permissions which requires a valid token
      // Only make actual API call if the basic token check wasn't sufficient
      const permissions = await this.getCurrentPermissions();
      if (permissions && permissions.length > 0) {
        logger.debug('Token validated through permissions API check');
        return true;
      }
      
      logger.warn('Token is invalid, clearing...');
      this.clearToken();
      return false;
    } catch (error) {
      logger.error('Error checking token status:', error);
      this.clearToken();
      return false;
    }
  }
  
  /**
   * Attempt to refresh the Meta token
   * @returns Promise<boolean> True if token was refreshed, false otherwise
   */
  static async refreshToken(): Promise<boolean> {
    try {
      // Ensure user is logged in
      await this.ensureLoggedIn();
      
      logger.debug('Attempting to refresh Meta token...');
      
      // For Meta Graph API, we need to re-login to get a new token
      const response = await this.login('facebook');
      return !!response;
    } catch (error) {
      logger.error('Failed to refresh Meta token:', error);
      return false;
    }
  }

  static async login(platform: 'facebook' | 'instagram' = 'facebook'): Promise<MetaAuthResponse> {
    // Start suppressing access token warnings immediately
    const originalWarn = console.warn;
    const originalError = console.error;
    const originalLog = console.log;
    
    const suppressWarning = (...args: unknown[]) => {
      const message = args.join(' ');
      if (message.includes('overriding current access token') || 
          message.includes('some other app is expecting different access token') ||
          message.includes('You are overriding current access token')) {
        return; // Suppress
      }
      originalWarn.apply(console, args);
    };
    
    const suppressError = (...args: unknown[]) => {
      const message = args.join(' ');
      if (message.includes('overriding current access token') || 
          message.includes('some other app is expecting different access token') ||
          message.includes('You are overriding current access token')) {
        return; // Suppress
      }
      originalError.apply(console, args);
    };
    
    const suppressLog = (...args: unknown[]) => {
      const message = args.join(' ');
      if (message.includes('overriding current access token') || 
          message.includes('some other app is expecting different access token') ||
          message.includes('You are overriding current access token')) {
        return; // Suppress
      }
      originalLog.apply(console, args);
    };
    
    console.warn = suppressWarning;
    console.error = suppressError;
    console.log = suppressLog;
    
    // Set timeout to restore console functions
    const restoreConsole = () => {
      console.warn = originalWarn;
      console.error = originalError;
      console.log = originalLog;
    };
    
    // Restore after 15 seconds to ensure we cover the entire login flow
    const timeoutId = setTimeout(restoreConsole, 15000);
    
    async function performLogin(): Promise<MetaAuthResponse> {
      const platformConfig = platform === 'facebook' ? FACEBOOK_CONFIG : INSTAGRAM_CONFIG;
      const permissions = platformConfig.permissions || [];
      const criticalPermissions = platformConfig.criticalPermissions || [];
      const loginScope = platformConfig.loginScope || '';
      
      // Ensure public_profile is always requested, as it's often fundamental
      const requiredBaseScopes = ['public_profile'];
      if (platform === 'facebook') {
          requiredBaseScopes.push('email'); // Add email for Facebook
      }

      // Combine all requested permissions, ensuring no duplicates and including base scopes
      const allRequestedPermissions = [
        ...new Set([...requiredBaseScopes, ...loginScope.split(',').filter(s => s), ...permissions])
      ].filter(s => s); // Filter out any empty strings
      
      const fullScope = allRequestedPermissions.join(',');
      
      logger.debug(`Full login scope being requested: ${fullScope}`);

      // Check browser compatibility for Meta login
      const browserCompat = MetaAuthService.detectBrowserCompatibility();
      if (browserCompat.requiresSpecialHandling) {
        logger.warn(`Browser compatibility issue detected: ${browserCompat.browserName}. May require special handling.`);

        // Clear any existing auth state that might interfere
        MetaAuthService.clearToken();

        // For Safari and privacy browsers, provide user guidance
        if (browserCompat.hasStrictPrivacySettings) {
          logger.info('Browser has strict privacy settings. If login fails, try incognito/private mode or adjust privacy settings.');
        }
      }

      // Check if the SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw new Error('Facebook SDK not loaded. Please refresh the page and try again.');
      }
      
      logger.debug('Facebook SDK is available, calling FB.login...');
      
      // Auth options
      const authOptions: FacebookLoginOptions = {
        scope: fullScope,
        return_scopes: true,
        auth_type: 'rerequest' // Force re-prompt for permissions
      };
      
      return new Promise<MetaAuthResponse>((resolve, reject) => {
        suppressFBAccessTokenWarning(() => {
          window.FB.login((response) => {
            logger.debug('FB.login response received:', JSON.stringify(response));
            
            if (response && response.authResponse) {
              logger.info(`${platform} login successful, auth response received`);
              
              // Check granted permissions
              if (response.authResponse.grantedScopes) {
                const grantedScopes = response.authResponse.grantedScopes.split(',');
                logger.debug('Granted permissions:', grantedScopes);
                
                // Check for missing critical permissions
                const missingCriticalPermissions = criticalPermissions.filter(
                  perm => !grantedScopes.includes(perm)
                );
                
                if (missingCriticalPermissions.length > 0) {
                  logger.warn('Missing critical permissions:', missingCriticalPermissions);
                  
                  // We'll still continue but log this as a warning
                  logger.warn(`User did not grant critical permissions: ${missingCriticalPermissions.join(', ')}. Some functionality will be limited.`);
                }
                
                // Check for all missing permissions
                const missingPermissions = permissions.filter(
                  perm => !grantedScopes.includes(perm)
                );
                
                if (missingPermissions.length > 0 && missingPermissions.length !== missingCriticalPermissions.length) {
                  logger.warn('Missing non-critical permissions:', 
                    missingPermissions.filter(p => !missingCriticalPermissions.includes(p))
                  );
                }
              } else {
                logger.warn('No granted scopes information in the response');
              }
              
              // Store the access token using our new method
              // Get expiration time from authResponse if available
              const expiresIn = response.authResponse.expiresIn || undefined;
              MetaAuthService.saveToken(response.authResponse.accessToken, expiresIn);
              
              resolve(response);
            } else {
              logger.warn(`${platform} login failed or was cancelled by the user`);
              reject({
                type: MetaErrorType.AUTH_FAILED,
                message: 'Meta login failed or was cancelled by the user'
              });
            }
          }, authOptions);
        }, 10000); // Extended to 10 seconds to cover the entire async login flow
      });
    }
    
    // Call the async function and ensure console is restored regardless of outcome
    try {
      const result = await performLogin();
      clearTimeout(timeoutId);
      restoreConsole();
      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      restoreConsole();
      throw error;
    }
  }

  static async handleMetaAuth(metaResponse: MetaAuthResponse, platform: 'facebook' | 'instagram' = 'facebook'): Promise<MetaLoginResponse> {
    try {
      logger.debug('Handling Meta auth with backend...', {
        platform,
        hasToken: !!metaResponse?.authResponse?.accessToken,
        grantedScopes: metaResponse?.authResponse?.grantedScopes
      });
      
      if (!metaResponse || !metaResponse.authResponse || !metaResponse.authResponse.accessToken) {
        throw {
          type: MetaErrorType.AUTH_FAILED,
          message: 'Invalid Meta authentication response. Missing token.'
        } as MetaError;
      }
      
      // Check if we have critical permissions granted
      if (metaResponse.authResponse.grantedScopes) {
        const grantedScopes = metaResponse.authResponse.grantedScopes.split(',');
        const criticalPermissions = platform === 'facebook'
          ? ['pages_show_list', 'pages_read_engagement', 'public_profile', 'email']
          : ['instagram_basic', 'instagram_manage_insights', 'public_profile', 'email'];
          
        const missingCriticalPermissions = criticalPermissions.filter(
          perm => !grantedScopes.includes(perm)
        );
        
        if (missingCriticalPermissions.length > 0) {
          logger.warn(`Missing critical ${platform} permissions:`, missingCriticalPermissions);
          // We'll continue but log this for debugging
        }
      }
      
      const response = await fetch(`${API_URL}/api/auth/meta`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credential: metaResponse.authResponse.accessToken,
          platform: platform,
          granted_scopes: metaResponse.authResponse.grantedScopes
        }),
        credentials: 'include' // Include cookies for session management
      });

      if (!response.ok) {
        const errorData = await response.json();
        logger.error('Server error in handleMetaAuth:', errorData);
        
        // Check for permission-related errors
        if (errorData.detail && typeof errorData.detail === 'string' && 
            (errorData.detail.includes('permission') || errorData.detail.includes('access'))) {
          throw {
            type: MetaErrorType.PERMISSION_DENIED,
            message: errorData.detail || 'Missing required permissions for Meta integration',
            originalError: errorData
          } as MetaError;
        }
        
        throw {
          type: MetaErrorType.API_ERROR,
          message: errorData.detail || 'Failed to authenticate with server',
          originalError: errorData
        } as MetaError;
      }

      const responseData = await response.json();
      logger.debug('Auth response from server:', {
        success: !!responseData,
        hasPagesOrAccounts: responseData?.pages?.length > 0 || responseData?.accounts?.length > 0
      });
      
      // Store the access token using our new method with proper expiry
      const expiresIn = metaResponse.authResponse.expiresIn || 60 * 24 * 60 * 60; // Default to 60 days
      MetaAuthService.saveToken(metaResponse.authResponse.accessToken, expiresIn);
      
      // Set auth state in localStorage to indicate user is logged in with Meta
      localStorage.setItem(META_AUTH_STATE_KEY, JSON.stringify({
        isLoggedIn: true,
        platform,
        timestamp: Date.now()
      }));
      
      // Dispatch an event to notify the app about successful login
      // This will be used to trigger permissions refresh
      const loginEvent = new CustomEvent('meta-login-success', { 
        detail: { platform, accessToken: metaResponse.authResponse.accessToken }
      });
      document.dispatchEvent(loginEvent);
      logger.debug('Dispatched meta-login-success event, permissions can now be refreshed');
      
      return responseData;
    } catch (error) {
      logger.error('Error in handleMetaAuth:', error);
      
      if ((error as MetaError).type) {
        throw error;
      }
      
      throw {
        type: MetaErrorType.NETWORK_ERROR,
        message: 'Failed to communicate with server',
        originalError: error
      } as MetaError;
    }
  }

  /**
   * Get business accounts for the current user
   */
  public static async getBusinessAccounts(): Promise<MetaBusinessAccount[]> {
    try {
      // Ensure user is logged in before making API calls
      await this.ensureLoggedIn();
      
      logger.debug('Fetching Meta business accounts...');
      
      return new Promise<MetaBusinessAccount[]>((resolve, reject) => {
        // Check login status before making API call
        window.FB.getLoginStatus((statusResponse) => {
          if (statusResponse.status !== 'connected') {
            logger.error('User is not connected to Facebook. Status:', statusResponse.status);
            reject({
              type: MetaErrorType.AUTH_FAILED,
              message: 'User is not connected to Facebook',
              originalError: statusResponse
            });
            return;
          }
          
          const accessToken = MetaAuthService.getAccessToken();
          logger.debug('Fetching business accounts with token:', accessToken?.substring(0, 15) + '...');
          
          // Check granted permissions
          window.FB.api(
            '/me/permissions',
            'GET',
            { access_token: accessToken },
            (permissionsResponse: { error?: unknown; data?: Array<{ permission: string; status: string }> }) => {
              if (permissionsResponse.error) {
                logger.warn('Error fetching permissions:', permissionsResponse.error);
              } else {
                logger.debug('Granted permissions:', permissionsResponse.data);
                
                // Check if business_management permission is granted
                const hasBusinessPermission = permissionsResponse.data?.some(
                  (p: { permission: string; status: string }) => 
                    p.permission === 'business_management' && p.status === 'granted'
                );
                
                if (!hasBusinessPermission) {
                  logger.warn('business_management permission not granted');
                }
              }
              
              // Continue with business accounts API call
              window.FB.api(
                '/me/businesses',
                'GET',
                { 
                  fields: 'id,name,role',
                  access_token: accessToken
                },
                (response: { error?: unknown; data?: MetaBusinessAccount[] }) => {
                  if (response.error) {
                    logger.error('Error fetching business accounts:', response.error);
                    
                    // If error is due to permissions or no businesses, return an empty array instead of rejecting
                    if (response.error && typeof response.error === 'object' && 
                        'code' in response.error && 
                        ((response.error as { code: number }).code === 200 || 
                         (response.error as { code: number }).code === 10 || 
                         'type' in response.error && (response.error as { type: string }).type === 'OAuthException')) {
                      logger.warn('Permission issue or no businesses found, returning empty array');
                      resolve([]);
                      return;
                    }
                    
                    reject({
                      type: MetaErrorType.API_ERROR,
                      message: 'Failed to fetch business accounts',
                      originalError: response.error
                    });
                  } else {
                    logger.debug('Business accounts fetched successfully:', response.data ? response.data.length : 0);
                    resolve(response.data || []);
                  }
                }
              );
            }
          );
        });
      });
    } catch (error) {
      logger.error('Error in getBusinessAccounts:', error);
      
      if ((error as MetaError).type) {
        throw error;
      }
      
      throw {
        type: MetaErrorType.NETWORK_ERROR,
        message: 'Failed to communicate with server',
        originalError: error
      } as MetaError;
    }
  }

  /**
   * Get pages for a business account
   */
  public static async getPages(businessId: string): Promise<MetaPage[]> {
    try {
      // Ensure user is logged in
      await this.ensureLoggedIn();
      
      logger.debug(`Fetching pages for business ${businessId}...`);
      
      // Check if the SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw new Error('Facebook SDK not loaded. Please refresh the page and try again.');
      }

      logger.debug('Fetching pages for business ID:', businessId);
      let allPages: MetaPage[] = [];

      // First get Facebook pages
      try {
        const fbResponse = await new Promise<{ data?: Record<string, unknown>[] }>((resolve, reject) => {
          window.FB.api(
            '/me/accounts',
            'GET',
            { fields: 'id,name,access_token,category,category_list,followers_count,fan_count' },
            (response: { error?: unknown; data?: Record<string, unknown>[] }) => {
              if (response.error) {
                reject(response.error);
              } else {
                resolve(response);
              }
            }
          );
        });

        // Transform Facebook pages
        if (fbResponse && fbResponse.data) {
          const fbPages = fbResponse.data.map((page: Record<string, unknown>) => ({
            ...page,
            platform: 'facebook' as const,
            followers_count: (page.fan_count as number) || 0,
            tasks: []
          }));
          allPages = [...allPages, ...fbPages] as MetaPage[];
        }
      } catch (fbError) {
        logger.warn('Error fetching Facebook pages:', fbError);
      }

      // Then get Instagram accounts
      try {
        // First try the business-specific endpoint
        const igResponse = await new Promise<{ data?: Record<string, unknown>[] }>((resolve, reject) => {
          window.FB.api(
            `/${businessId}/instagram_accounts`,
            'GET',
            { fields: 'id,username,name,profile_picture_url,followers_count,media_count' },
            (response: { error?: unknown; data?: Record<string, unknown>[] }) => {
              if (response.error) {
                // Check if this is a permission error and handle gracefully
                const errorObj = response.error as { code?: number; message?: string; type?: string };
                if (errorObj.code === 200 && errorObj.message?.includes('business_management')) {
                  logger.warn('Business management permission not available, skipping business Instagram accounts');
                  resolve({ data: [] }); // Return empty array instead of rejecting
                } else {
                  reject(response.error);
                }
              } else {
                resolve(response);
              }
            }
          );
        });

        if (igResponse && igResponse.data) {
          // Transform Instagram accounts
          const igPages = igResponse.data.map((account: Record<string, unknown>) => ({
            id: account.id as string,
            name: (account.username as string) || (account.name as string),
            access_token: '', // Will be set from the Facebook page
            category: 'INSTAGRAM_ACCOUNT',
            category_list: [{ id: '1', name: 'Instagram Account' }],
            platform: 'instagram' as const,
            followers_count: (account.followers_count as number) || 0,
            profile_picture_url: account.profile_picture_url as string,
            media_count: (account.media_count as number) || 0,
            tasks: []
          }));

          // Get Facebook pages again to match Instagram accounts with their access tokens
          const fbPagesResponse = await new Promise<{ data?: Record<string, unknown>[] }>((resolve, reject) => {
            window.FB.api(
              '/me/accounts',
              'GET',
              { fields: 'id,name,access_token,instagram_business_account{id}' },
              (response: { error?: unknown; data?: Record<string, unknown>[] }) => {
                if (response.error) {
                  reject(response.error);
                } else {
                  resolve(response);
                }
              }
            );
          });

          if (fbPagesResponse && fbPagesResponse.data) {
            // Create a map of Instagram account IDs to Facebook page access tokens
            const tokenMap = new Map<string, string>();
            fbPagesResponse.data.forEach((page: Record<string, unknown>) => {
              if (page.instagram_business_account && typeof page.instagram_business_account === 'object') {
                const igAccount = page.instagram_business_account as { id: string };
                tokenMap.set(igAccount.id, page.access_token as string);
                logger.debug('Mapping token for FB Page:', page.id, '-> IG Account:', igAccount?.id, 'Token:', page.access_token ? 'Exists' : 'Missing');
              }
            });

            // Before the igPages.forEach loop, add logging for the tokenMap
            logger.debug('Token Map:', JSON.stringify(Array.from(tokenMap.entries())));
            
            // Inside the igPages.forEach loop, add logging
            igPages.forEach((page) => {
              logger.debug('Attempting to assign token to IG Page:', page.id, 'Found in map:', tokenMap.has(page.id));
              if (tokenMap.has(page.id)) {
                page.access_token = tokenMap.get(page.id)!;
              }
            });

            allPages = [...allPages, ...igPages];
          }
        }
      } catch (igError) {
        logger.warn('Error fetching Instagram accounts:', igError);
      }

      logger.debug('Total pages fetched:', allPages.length);
      return allPages;
    } catch (error) {
      logger.error('Error in getPages:', error);
      
      if ((error as MetaError).type) {
        throw error;
      }
      
      throw {
        type: MetaErrorType.NETWORK_ERROR,
        message: 'Failed to communicate with server',
        originalError: error
      } as MetaError;
    }
  }

  static async getInstagramAccounts(businessId: string): Promise<MetaPage[]> {
    async function fetchInstagramAccounts(): Promise<MetaPage[]> {
      // Check if the SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw new Error('Facebook SDK not loaded. Please refresh the page and try again.');
      }
      
      logger.debug(`Fetching Instagram accounts for business ID: ${businessId}`);
      
      return new Promise<MetaPage[]>((resolve, reject) => {
        // First check if we have the instagram_basic permission
        window.FB.api(
          '/me/permissions',
          'GET',
          {},
          (permissionsResponse: { error?: unknown; data?: Array<{ permission: string; status: string }> }) => {
            if (permissionsResponse.error) {
              logger.warn('Error fetching permissions for Instagram:', permissionsResponse.error);
            } else {
              // Check if instagram_basic permission is granted
              const hasInstagramPermission = permissionsResponse.data?.some(
                (p: { permission: string; status: string }) => 
                  p.permission === 'instagram_basic' && p.status === 'granted'
              );
              
              if (!hasInstagramPermission) {
                logger.warn('instagram_basic permission not granted, returning empty array');
                resolve([]);
                return;
              }
            }
            
            // First try the business-specific endpoint
            window.FB.api(
              `/${businessId}/instagram_accounts`,
              'GET',
              { fields: 'id,username,name,profile_picture_url,followers_count,media_count' },
              (response: { error?: unknown; data?: Record<string, unknown>[] }) => {
                if (response.error) {
                  // Check if this is a permission error and handle gracefully
                  const errorObj = response.error as { code?: number; message?: string; type?: string };
                  if (errorObj.code === 200 && errorObj.message?.includes('business_management')) {
                    logger.warn('Business management permission not available, trying alternative approach');
                  } else {
                    logger.warn('Error fetching Instagram accounts from business endpoint:', response.error);
                  }
                  
                  // Try an alternative approach for Instagram accounts
                  window.FB.api(
                    '/me/accounts',
                    'GET',
                    { fields: 'id,name,access_token,instagram_business_account{id,name,username,profile_picture_url,followers_count,media_count}' },
                    (accountsResponse: { error?: unknown; data?: Record<string, unknown>[] }) => {
                      if (accountsResponse.error) {
                        logger.error('Error fetching connected Instagram accounts:', accountsResponse.error);
                        
                        // Check if it's a permissions issue
                        if (accountsResponse.error && typeof accountsResponse.error === 'object' && 
                            'code' in accountsResponse.error && 
                            ((accountsResponse.error as { code: number }).code === 200 || 
                             (accountsResponse.error as { code: number }).code === 10 || 
                             'type' in accountsResponse.error && 
                             (accountsResponse.error as { type: string }).type === 'OAuthException')) {
                          logger.warn('Permission issue for Instagram accounts, returning empty array');
                          resolve([]);
                          return;
                        }
                        
                        reject({
                          type: MetaErrorType.API_ERROR,
                          message: 'Failed to fetch Instagram accounts',
                          originalError: accountsResponse.error
                        });
                      } else {
                        logger.debug('Checking Facebook pages for connected Instagram accounts');
                        
                        // Extract Instagram accounts from Facebook pages
                        const instagramAccounts: MetaPage[] = [];
                        
                        if (accountsResponse.data) {
                          accountsResponse.data.forEach((page: Record<string, unknown>) => {
                            if (page.instagram_business_account && typeof page.instagram_business_account === 'object') {
                              // Get the Instagram business account data
                              const igAccount = page.instagram_business_account as Record<string, unknown>;
                              instagramAccounts.push({
                                id: igAccount.id as string,
                                name: (igAccount.username as string) || (igAccount.name as string),
                                access_token: page.access_token as string, // Use the page access token
                                category: 'INSTAGRAM_ACCOUNT',
                                category_list: [{ id: '1', name: 'Instagram Account' }],
                                tasks: [],
                                platform: 'instagram' as const,
                                followers_count: parseInt(igAccount.followers_count as string) || 0,
                                profile_picture_url: igAccount.profile_picture_url as string,
                                media_count: parseInt(igAccount.media_count as string) || 0
                              });
                              logger.debug('Added Instagram account with token:', {
                                id: igAccount.id,
                                hasToken: !!(page.access_token),
                                tokenStart: page.access_token ? (page.access_token as string).substring(0, 10) + '...' : 'none'
                              });
                            }
                          });
                        }
                        
                        logger.debug('Instagram accounts found through pages:', instagramAccounts.length);
                        resolve(instagramAccounts);
                      }
                    }
                  );
                } else {
                  logger.debug('Instagram accounts fetched successfully:', response.data ? response.data.length : 0);
                  
                  // Transform Instagram account data to match MetaPage interface
                  const instagramPages = (response.data || []).map((account: Record<string, unknown>) => ({
                    id: account.id as string,
                    name: (account.username as string) || (account.name as string),
                    access_token: '', // Instagram accounts don't have individual tokens
                    category: 'INSTAGRAM_ACCOUNT',
                    category_list: [{ id: '1', name: 'Instagram Account' }],
                    tasks: [],
                    platform: 'instagram' as const,
                    followers_count: account.followers_count as number || 0,
                    profile_picture_url: account.profile_picture_url as string,
                    media_count: account.media_count as number || 0
                  }));
                  
                  logger.debug('Created Instagram pages with empty tokens. These need to be mapped from FB pages:', instagramPages.length);
                  
                  resolve(instagramPages);
                }
              }
            );
          }
        );
      });
    }
    
    return fetchInstagramAccounts();
  }

  static async getPageInsights(pageId: string, metrics: string[]): Promise<unknown> {
    // Create async function outside the Promise executor
    async function fetchPageInsights(): Promise<unknown> {
      // Check if the SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw {
          type: MetaErrorType.API_ERROR,
          message: 'Facebook SDK not loaded. Please refresh the page and try again.'
        };
      }
      
      return new Promise((resolve, reject) => {
        const accessToken = MetaAuthService.getAccessToken();
        window.FB.api(
          `/${pageId}/insights`,
          'GET',
          { 
            metric: metrics.join(','),
            period: 'day',
            access_token: accessToken
          },
          (response: { error?: unknown; data?: unknown[] }) => {
            if (response.error) {
              reject({
                type: MetaErrorType.API_ERROR,
                message: 'Failed to fetch page insights',
                originalError: response.error
              });
            } else {
              resolve(response.data);
            }
          }
        );
      });
    }
    
    return fetchPageInsights();
  }

  static async getUserProfile(): Promise<unknown> {
    // Create async function outside the Promise executor
    async function fetchUserProfile(): Promise<unknown> {
      // Check if the SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw {
          type: MetaErrorType.API_ERROR,
          message: 'Facebook SDK not loaded. Please refresh the page and try again.'
        };
      }
      
      return new Promise((resolve, reject) => {
        window.FB.api(
          '/me',
          'GET',
          { fields: 'id,name,email,picture' },
          (response: { error?: unknown; data?: unknown }) => {
            if (response.error) {
              reject({
                type: MetaErrorType.API_ERROR,
                message: 'Failed to fetch user profile',
                originalError: response.error
              });
            } else {
              resolve(response);
            }
          }
        );
      });
    }
    
    return fetchUserProfile();
  }

  /**
   * Get current permissions granted to the app
   * @param forceRefresh Whether to force a fresh API call by clearing any caches
   * @param prevPermissions Optional previous permissions to compare with for detecting external changes
   * @returns Promise<Array<{permission: string, status: string}>>
   */
  static async getCurrentPermissions(
    forceRefresh: boolean = false,
    prevPermissions?: Array<{permission: string, status: string}>
  ): Promise<Array<{permission: string, status: string}>> {
    try {
      // Skip API call if not logged in
      const isLoggedIn = await this.isLoggedIn();
      if (!isLoggedIn) {
        logger.debug('User not logged in, returning empty permissions');
        return [];
      }
      
      logger.debug('Fetching current Meta permissions...');
      
      // Check if SDK is ready
      const isReady = await isFacebookSDKReady();
      if (!isReady) {
        throw new Error('Facebook SDK not loaded. Please refresh the page and try again.');
      }

      return new Promise((resolve, reject) => {
        const options: FacebookSDKOptions = {};
        
        // Add cache-busting parameter if forcing refresh
        if (forceRefresh) {
          options.include_headers = false; // Avoid cached response
          options.timestamp = new Date().getTime(); // Add timestamp to bust cache
        }
        
        // Define a type for the error object from FB API
        interface FBError {
          code?: number;
          message?: string;
          type?: string;
          fbtrace_id?: string;
        }

        window.FB.api('/me/permissions', 'GET', options, (response: { error?: FBError; data?: Array<{ permission: string; status: string }> }) => {
          if (response.error) {
            logger.error('Error fetching permissions:', response.error);

            // Check for Rate Limit error (code 4)
            if (response.error.code === 4) {
              reject({
                type: MetaErrorType.RATE_LIMIT, // Use existing RATE_LIMIT type
                message: response.error.message || 'Meta API request limit reached while fetching permissions.',
                originalError: response.error
              } as MetaError);
            } else {
              // Handle other API errors
              reject({
                type: MetaErrorType.API_ERROR,
                message: response.error.message || 'Failed to fetch permissions',
                originalError: response.error
              } as MetaError);
            }
          } else {
            logger.debug('Permissions retrieved:', response.data);

            // Filter out deprecated permissions
            const filteredPermissions = (response.data || []).filter(perm => {
              if (DEPRECATED_PERMISSIONS.has(perm.permission)) {
                logger.debug(`Filtering out deprecated permission: ${perm.permission}`);
                return false;
              }
              return true;
            });

            // Check for permissions that changed externally
            if (prevPermissions && filteredPermissions) {
              const changedPermissions = filteredPermissions.filter(newPerm => {
                const oldPerm = prevPermissions.find(p => p.permission === newPerm.permission);
                return oldPerm && oldPerm.status !== newPerm.status;
              });

              if (changedPermissions.length > 0) {
                logger.debug('Detected permissions changed externally:', changedPermissions);
              }
            }

            resolve(filteredPermissions);
          }
        });
      });
    } catch (error) {
      logger.error('Error in getCurrentPermissions:', error);
      
      if ((error as MetaError).type) {
        throw error;
      }
      
      throw {
        type: MetaErrorType.NETWORK_ERROR,
        message: 'Failed to communicate with server',
        originalError: error
      } as MetaError;
    }
  }

  /**
   * Revoke a specific permission
   * @param permission The permission string to revoke
   * @returns Promise<boolean> Success status
   */
  static async revokePermission(permission: string): Promise<boolean> {
    // Check if SDK is ready
    const isReady = await isFacebookSDKReady();
    if (!isReady) {
      throw new Error('Facebook SDK not loaded');
    }
    
    return new Promise((resolve, reject) => {
      window.FB.api(
        `/me/permissions/${permission}`,
        'DELETE',
        {},
        (response: { error?: unknown; success?: boolean }) => {
          if (response.error) {
            logger.error(`Error revoking permission ${permission}:`, response.error);
            reject(response.error);
          } else {
            logger.info(`Permission ${permission} revoked successfully`);
            resolve(response.success || false);
          }
        }
      );
    });
  }

  /**
   * Logs out the user from Meta and clears all stored tokens and auth state
   * @returns Promise that resolves when logout is complete
   */
  static async logout(): Promise<void> {
    return new Promise<void>((resolve) => {
      // Clear our stored token and auth state first
      this.clearToken();
      localStorage.removeItem(META_AUTH_STATE_KEY);
      
      // Check if FB SDK is loaded
      if (typeof window.FB === 'undefined') {
        logger.warn('Facebook SDK not loaded, could only clear local token');
        resolve();
        return;
      }
      
      // Call FB.logout to invalidate the session on Meta's side
      window.FB.logout(() => {
        logger.info('Successfully logged out from Meta');
        
        // Dispatch logout event
        const logoutEvent = new CustomEvent('meta-logout', { detail: { timestamp: Date.now() } });
        document.dispatchEvent(logoutEvent);
        
        resolve();
      });
    });
  }

  // Add a new method to check if the user has a valid session
  /**
   * Check if the user has a valid Meta session
   * @returns Promise<boolean> True if the user has a valid session, false otherwise
   */
  static async hasValidSession(): Promise<boolean> {
    // First check if we have a valid token
    if (this.hasValidToken()) {
      return true;
    }
    
    // If no token or expired token, check login status with Facebook SDK
    return this.isLoggedIn();
  }

  /**
   * Request additional permissions from the user
   * @param permissions Array of permission strings to request
   * @returns Promise<boolean> True if permissions were granted, false otherwise
   */
  static async requestAdditionalPermissions(permissions: string[]): Promise<boolean> {
    logger.info('Requesting additional permissions:', permissions);
    
    if (!isFacebookSDKReady()) {
      logger.error('Facebook SDK not ready when requesting additional permissions');
      throw { type: MetaErrorType.SDK_NOT_READY, message: 'Facebook SDK not ready' };
    }

    return new Promise((resolve, reject) => {
      window.FB.login((response: MetaAuthResponse) => {
        if (response.status === 'connected') {
          logger.info('Additional permissions granted successfully');
          
          // Update the stored token with new permissions
          if (response.authResponse) {
            const expiresIn = response.authResponse.expiresIn * 1000; // Convert to milliseconds
            this.saveToken(response.authResponse.accessToken, expiresIn);
          }
          
          // Dispatch event to notify components about permission change
          const permissionEvent = new CustomEvent('meta-permissions-updated', { 
            detail: { permissions, timestamp: Date.now() } 
          });
          document.dispatchEvent(permissionEvent);
          
          resolve(true);
        } else if (response.status === 'not_authorized') {
          logger.warn('User cancelled permission request or denied permissions');
          resolve(false);
        } else {
          logger.error('Error requesting additional permissions:', response);
          reject({ 
            type: MetaErrorType.API_ERROR, 
            message: 'Failed to request additional permissions', 
            originalError: response 
          });
        }
      }, { 
        scope: permissions.join(','),
        return_scopes: true
      } as { scope: string; return_scopes: boolean; auth_type?: string });
    });
  }
} 