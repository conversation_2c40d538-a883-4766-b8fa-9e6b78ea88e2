export interface CookieCategory {
  id: string;
  name: string;
  description: string;
  essential: boolean;
  enabled: boolean;
}

export interface CookieDetails {
  name: string;
  category: string;
  description: string;
  duration: string;
  domain?: string; // Optional domain to allow automatic domain detection
  type: 'first-party' | 'third-party';
  essential: boolean;
  purpose: string;
  provider?: string;
}

export interface CookiePreferences {
  essential: boolean;
  analytics: boolean;
  performance: boolean;
  functional: boolean;
  marketing: boolean;
  preferences: boolean;
}

export interface CookieConsent {
  timestamp: Date;
  version: string;
  preferences: CookiePreferences;
  ipAddress?: string;
  userAgent?: string;
  consentId: string;
}

export interface CookieConfiguration {
  categories: CookieCategory[];
  cookies: CookieDetails[];
  consentVersion: string;
  requiredNoticeVersion: string;
  legalBasis: {
    gdpr: boolean;
    ccpa: boolean;
    lgpd: boolean;
    pipeda: boolean;
  };
}

export type CookieUpdateEvent = CustomEvent<{
  category: string;
  enabled: boolean;
  preferences: CookiePreferences;
}>; 