import React from 'react';
import { Card, CardContent, Typography, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useTranslation } from 'react-i18next';

interface RecommendationCardProps {
  title: string;
  content: string | null;
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({ title, content }) => {
  const { t } = useTranslation();

  return (
    <Card elevation={0} sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ color: '#42a5f5' }}>
          {title}
        </Typography>
        {(() => {
          if (!content) {
            return (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', mt: 2 }}>
                {t('dashboard.recommendations.none', 'No recommendations available at this time.')}
              </Typography>
            );
          }

          // Helper function to process line and check for bolding
          const processAndStyleLine = (line: string): { text: string; isBold: boolean } => {
            let displayLine = line.trim();
            let isBold = false;

            // Remove list markers
            if (displayLine.startsWith('- ')) {
              displayLine = displayLine.substring(2).trim();
            } else if (displayLine.startsWith('* ')) {
              displayLine = displayLine.substring(2).trim();
            }

            // Check for bolding conditions (e.g., ends with colon or specific pattern)
            if (displayLine.endsWith(':') || /^\w+-term \(\d+-\d+ months\):?$/i.test(displayLine)) {
              isBold = true;
            }
            
            return { text: displayLine, isBold };
          };

          const lines = content.split('\\n').filter(line => line.trim() !== '');

          if (lines.length > 1) {
            // Render list for multiple lines
            return (
              <List disablePadding sx={{ p: 0, mt: 2 }}>
                {lines.map((line, index) => {
                  const { text, isBold } = processAndStyleLine(line);
                  return (
                    <ListItem key={index} sx={{ py: 1.5, px: 0, alignItems: 'flex-start' }}>
                      <ListItemIcon sx={{ minWidth: 'auto', mr: 1, mt: '0.4em', color: 'text.secondary' }}>
                        <FiberManualRecordIcon sx={{ fontSize: '0.6em' }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={
                          <Typography 
                            variant="body2" 
                            color="text.primary"
                            sx={{ fontSize: '1rem', fontWeight: isBold ? 'bold' : 'regular' }}
                          >
                            {text}
                          </Typography>
                        } 
                        sx={{ my: 0 }}
                      />
                    </ListItem>
                  );
                })}
              </List>
            );
          } else if (lines.length === 1) {
            // Render single Typography for single line
            const { text, isBold } = processAndStyleLine(lines[0]);
            return (
              <Typography 
                variant="body2" 
                color="text.primary"
                sx={{ fontSize: '1rem', mt: 2, whiteSpace: 'pre-wrap', fontWeight: isBold ? 'bold' : 'regular' }}
              >
                {text}
              </Typography>
            );
          } else {
            // Fallback if content exists but has no valid lines after split/filter
            return (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', mt: 2 }}>
                {t('dashboard.recommendations.none', 'No recommendations available at this time.')}
              </Typography>
            );
          }
        })()}
      </CardContent>
    </Card>
  );
};

export default RecommendationCard; 
