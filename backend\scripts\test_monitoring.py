#!/usr/bin/env python3
"""
Test Monitoring Script for Security Gateway
Monitors test performance, generates reports, and tracks regression.
"""

import json
import time
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import sys

class TestMonitor:
    def __init__(self, results_dir: str = "test-results"):
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def run_tests_with_monitoring(self, test_category: str = "all") -> Dict[str, Any]:
        """Run tests and collect performance metrics."""
        print(f"🚀 Running {test_category} tests with monitoring...")
        
        start_time = time.time()
        
        # Define test commands
        test_commands = {
            "unit": ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short"],
            "integration": ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short"],
            "load": ["python", "-m", "pytest", "tests/load/", "-v", "--tb=short"],
            "security": ["python", "-m", "pytest", "tests/security/", "-v", "--tb=short"],
            "cost": ["python", "-m", "pytest", "tests/cost/", "-v", "--tb=short"],
            "all": ["python", "-m", "pytest", "tests/", "-v", "--tb=short"]
        }
        
        if test_category not in test_commands:
            raise ValueError(f"Unknown test category: {test_category}")
        
        # Run tests
        cmd = test_commands[test_category]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Parse test results
        test_results = self._parse_pytest_output(result.stdout, result.stderr)
        test_results.update({
            "category": test_category,
            "execution_time": execution_time,
            "timestamp": self.timestamp,
            "success": result.returncode == 0,
            "command": " ".join(cmd)
        })
        
        # Save results
        self._save_results(test_results, test_category)
        
        return test_results
    
    def _parse_pytest_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """Parse pytest output to extract metrics."""
        results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "warnings": 0,
            "errors": [],
            "stdout": stdout,
            "stderr": stderr
        }
        
        # Parse test summary line
        for line in stdout.split('\n'):
            if 'passed' in line and 'warnings' in line:
                # Example: "20 passed, 15 warnings in 0.18s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed':
                        results["passed"] = int(parts[i-1])
                        results["total_tests"] += results["passed"]
                    elif part == 'failed':
                        results["failed"] = int(parts[i-1])
                        results["total_tests"] += results["failed"]
                    elif part == 'skipped':
                        results["skipped"] = int(parts[i-1])
                        results["total_tests"] += results["skipped"]
                    elif part == 'warnings':
                        results["warnings"] = int(parts[i-1])
        
        return results
    
    def _save_results(self, results: Dict[str, Any], category: str):
        """Save test results to file."""
        filename = f"{category}_results_{self.timestamp}.json"
        filepath = self.results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📊 Results saved to {filepath}")
    
    def generate_performance_report(self) -> str:
        """Generate performance report from recent test runs."""
        print("📈 Generating performance report...")
        
        # Load recent results
        result_files = sorted(self.results_dir.glob("*_results_*.json"))
        recent_results = []
        
        for file in result_files[-10:]:  # Last 10 results
            with open(file) as f:
                recent_results.append(json.load(f))
        
        if not recent_results:
            return "No test results found for performance analysis."
        
        # Generate report
        report = self._create_performance_report(recent_results)
        
        # Save report
        report_file = self.results_dir / f"performance_report_{self.timestamp}.md"
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"📋 Performance report saved to {report_file}")
        return report
    
    def _create_performance_report(self, results: List[Dict[str, Any]]) -> str:
        """Create formatted performance report."""
        report = [
            "# Security Gateway Performance Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Summary",
            ""
        ]
        
        # Calculate averages
        categories = {}
        for result in results:
            cat = result.get("category", "unknown")
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result)
        
        for category, cat_results in categories.items():
            if not cat_results:
                continue
            
            avg_time = sum(r.get("execution_time", 0) for r in cat_results) / len(cat_results)
            success_rate = sum(1 for r in cat_results if r.get("success", False)) / len(cat_results) * 100
            avg_tests = sum(r.get("total_tests", 0) for r in cat_results) / len(cat_results)
            
            report.extend([
                f"### {category.title()} Tests",
                f"- **Average Execution Time**: {avg_time:.2f} seconds",
                f"- **Success Rate**: {success_rate:.1f}%",
                f"- **Average Test Count**: {avg_tests:.0f}",
                f"- **Last 10 Runs**: {len(cat_results)}",
                ""
            ])
        
        # Recent trends
        report.extend([
            "## Recent Performance Trends",
            ""
        ])
        
        if len(results) >= 2:
            latest = results[-1]
            previous = results[-2]
            
            time_diff = latest.get("execution_time", 0) - previous.get("execution_time", 0)
            trend = "⬆️ Slower" if time_diff > 0 else "⬇️ Faster" if time_diff < 0 else "➡️ Same"
            
            report.extend([
                f"- **Latest Run**: {latest.get('execution_time', 0):.2f}s",
                f"- **Previous Run**: {previous.get('execution_time', 0):.2f}s",
                f"- **Trend**: {trend} ({time_diff:+.2f}s)",
                ""
            ])
        
        # Recommendations
        report.extend([
            "## Recommendations",
            ""
        ])
        
        # Analyze performance issues
        for category, cat_results in categories.items():
            if not cat_results:
                continue
            
            avg_time = sum(r.get("execution_time", 0) for r in cat_results) / len(cat_results)
            
            if category == "unit" and avg_time > 5:
                report.append(f"- ⚠️ Unit tests taking too long ({avg_time:.1f}s). Consider optimizing mocks.")
            elif category == "load" and avg_time > 120:
                report.append(f"- ⚠️ Load tests taking too long ({avg_time:.1f}s). Review test scenarios.")
            elif any(not r.get("success", False) for r in cat_results):
                report.append(f"- ❌ Recent failures in {category} tests. Review failing tests.")
        
        if not any("⚠️" in line or "❌" in line for line in report[-10:]):
            report.append("- ✅ All test categories performing within expected ranges.")
        
        return "\n".join(report)
    
    def run_security_scan(self) -> Dict[str, Any]:
        """Run security scan using bandit."""
        print("🔒 Running security scan...")
        
        # Run bandit security scanner
        cmd = ["bandit", "-r", "middleware/", "services/", "models/", "utils/", "-f", "json"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        scan_results = {
            "timestamp": self.timestamp,
            "success": result.returncode in [0, 1],  # bandit returns 1 for issues found
            "command": " ".join(cmd),
            "raw_output": result.stdout,
            "errors": result.stderr
        }
        
        try:
            if result.stdout:
                bandit_data = json.loads(result.stdout)
                scan_results["issues"] = bandit_data.get("results", [])
                scan_results["metrics"] = bandit_data.get("metrics", {})
        except json.JSONDecodeError:
            scan_results["parse_error"] = "Failed to parse bandit output"
        
        # Save security scan results
        self._save_results(scan_results, "security_scan")
        
        return scan_results
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """Generate data for test result dashboard."""
        print("📊 Generating dashboard data...")
        
        # Load all recent results
        result_files = sorted(self.results_dir.glob("*_results_*.json"))
        all_results = []
        
        for file in result_files[-50:]:  # Last 50 results
            try:
                with open(file) as f:
                    all_results.append(json.load(f))
            except json.JSONDecodeError:
                continue
        
        dashboard_data = {
            "last_updated": datetime.now().isoformat(),
            "total_test_runs": len(all_results),
            "categories": {},
            "trends": {},
            "health_status": "healthy"
        }
        
        # Process by category
        for result in all_results:
            category = result.get("category", "unknown")
            if category not in dashboard_data["categories"]:
                dashboard_data["categories"][category] = {
                    "runs": 0,
                    "success_rate": 0,
                    "avg_execution_time": 0,
                    "last_run": None
                }
            
            cat_data = dashboard_data["categories"][category]
            cat_data["runs"] += 1
            
            if result.get("timestamp", "") > (cat_data["last_run"] or ""):
                cat_data["last_run"] = result.get("timestamp")
        
        # Calculate success rates and averages
        for category in dashboard_data["categories"]:
            cat_results = [r for r in all_results if r.get("category") == category]
            if cat_results:
                success_count = sum(1 for r in cat_results if r.get("success", False))
                dashboard_data["categories"][category]["success_rate"] = success_count / len(cat_results) * 100
                
                total_time = sum(r.get("execution_time", 0) for r in cat_results)
                dashboard_data["categories"][category]["avg_execution_time"] = total_time / len(cat_results)
        
        # Determine overall health
        success_rates = [cat["success_rate"] for cat in dashboard_data["categories"].values()]
        if success_rates:
            avg_success_rate = sum(success_rates) / len(success_rates)
            if avg_success_rate < 80:
                dashboard_data["health_status"] = "critical"
            elif avg_success_rate < 95:
                dashboard_data["health_status"] = "warning"
        
        # Save dashboard data
        dashboard_file = self.results_dir / f"dashboard_data_{self.timestamp}.json"
        with open(dashboard_file, 'w') as f:
            json.dump(dashboard_data, f, indent=2, default=str)
        
        return dashboard_data

def main():
    parser = argparse.ArgumentParser(description="Security Gateway Test Monitoring")
    parser.add_argument("--category", choices=["unit", "integration", "load", "security", "cost", "all"], 
                       default="all", help="Test category to run")
    parser.add_argument("--report", action="store_true", help="Generate performance report")
    parser.add_argument("--security-scan", action="store_true", help="Run security scan")
    parser.add_argument("--dashboard", action="store_true", help="Generate dashboard data")
    parser.add_argument("--results-dir", default="test-results", help="Results directory")
    
    args = parser.parse_args()
    
    monitor = TestMonitor(args.results_dir)
    
    try:
        if args.security_scan:
            scan_results = monitor.run_security_scan()
            print(f"🔒 Security scan completed. Issues found: {len(scan_results.get('issues', []))}")
        
        if args.report:
            report = monitor.generate_performance_report()
            print("📈 Performance report generated.")
        
        if args.dashboard:
            dashboard_data = monitor.generate_dashboard_data()
            print(f"📊 Dashboard data generated. Health status: {dashboard_data['health_status']}")
        
        if not any([args.report, args.security_scan, args.dashboard]):
            # Run tests
            results = monitor.run_tests_with_monitoring(args.category)
            
            print(f"\n🎯 Test Results Summary:")
            print(f"Category: {results['category']}")
            print(f"Total Tests: {results['total_tests']}")
            print(f"Passed: {results['passed']}")
            print(f"Failed: {results['failed']}")
            print(f"Execution Time: {results['execution_time']:.2f}s")
            print(f"Status: {'✅ SUCCESS' if results['success'] else '❌ FAILED'}")
            
            if not results['success']:
                sys.exit(1)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 