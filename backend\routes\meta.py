import logging
import json
import traceback
from typing import Dict, List, Optional, Any, Tuple # Corrected: Added Tuple
import re
import asyncio # Corrected: Added asyncio
from datetime import datetime, timedelta, timezone # Corrected: Ensured this is at the top

import httpx # For async HTTP requests
import pymongo
from fastapi import (
    APIRouter, HTTPException, Depends, Query, Body, Path, BackgroundTasks, status
)
from fastapi.responses import JSONResponse

from config.database import db_analysis
from config.settings import get_settings
import os
from models.user import User
from models.meta import MetaBusinessData, MetaPageInsights
from services.auth import (
    get_current_active_user,
    verify_user_can_access_store,
    verify_user_can_access_store_query
)
from services.meta import (
    sync_meta_data_for_chat,
    get_sync_metrics,
    process_instagram_insights,
    process_ad_metrics,
    merge_ad_and_organic_metrics,
    get_organic_metrics,
    MetaService,
    update_meta_ad_metrics,
    run_meta_sales_correlation_update,
    create_empty_ad_metrics_response,
    fetch_and_cache_instagram_ad_metrics
)
from services.instagram_business_service import InstagramBusinessService
from services import meta as meta_service # Make sure meta_service is imported
# from services.meta_utils import get_user_pages_from_token # Not directly used, direct API calls preferred

logger = logging.getLogger(__name__)
settings = get_settings()

GRAPH_API_BASE_URL = "https://graph.facebook.com"
GRAPH_API_URL = f"{GRAPH_API_BASE_URL}/{settings.FACEBOOK_API_VERSION}"

router = APIRouter(prefix="/api/meta", tags=["meta"])

async def get_enhanced_facebook_insights_v23(page_id: str, access_token: str, time_range: str = "30d", store_id: str = None) -> Dict[str, Any]:
    """
    Enhanced Facebook insights using v23.0 API with post-level aggregation
    """
    try:
        logger.info(f"Getting enhanced Facebook insights from v23.0 API for page {page_id}")
        
        # Validate user token if store_id is provided, but keep using page access token
        if store_id:
            from routes.auth import refresh_meta_token_if_needed
            user_token = await refresh_meta_token_if_needed(store_id)
            if user_token:
                logger.info(f"User token validated for store {store_id}, using provided page access token")
            else:
                logger.warning(f"User token validation failed for store {store_id}, using provided page access token")
        
        # Calculate date range
        until_date = datetime.now()
        if time_range == "7d":
            since_date = until_date - timedelta(days=7)
        elif time_range == "30d":
            since_date = until_date - timedelta(days=30)
        elif time_range == "90d":
            since_date = until_date - timedelta(days=90)
        else:
            since_date = until_date - timedelta(days=30)
        
        since_str = since_date.strftime('%Y-%m-%d')
        until_str = until_date.strftime('%Y-%m-%d')
        
        # Get page-level insights (non-deprecated metrics only)
        page_url = f"{GRAPH_API_URL}/{page_id}/insights"
        page_params = {
            "metric": "page_impressions,page_impressions_unique,page_fans,page_fan_adds,page_fan_removes",
            "period": "day",
            "since": since_str,
            "until": until_str,
            "access_token": access_token
        }
        
        logger.debug(f"Requesting Facebook page insights with params: {page_params}")
        
        total_impressions = 0
        total_reach = 0
        total_engagement = 0
        followers_count = 0
        fan_adds = 0
        fan_removes = 0
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Get page insights
            try:
                page_response = await client.get(page_url, params=page_params)
                if page_response.status_code == 200:
                    page_data = page_response.json()
                    insights = page_data.get("data", [])
                    
                    logger.debug(f"Received Facebook page insights: {len(insights)} metrics")
                    
                    # Process page insights
                    for insight in insights:
                        metric_name = insight.get("name")
                        values = insight.get("values", [])
                        
                        logger.debug(f"Processing metric {metric_name} with {len(values)} values")
                        
                        if metric_name == "page_impressions":
                            total_impressions = sum(v.get("value", 0) for v in values if v.get("value"))
                            logger.debug(f"Total impressions calculated: {total_impressions}")
                        elif metric_name == "page_impressions_unique":
                            total_reach = sum(v.get("value", 0) for v in values if v.get("value"))
                            logger.debug(f"Total reach calculated: {total_reach}")
                        elif metric_name == "page_fans":
                            # Get latest value for current follower count
                            if values:
                                followers_count = values[-1].get("value", 0)
                                logger.debug(f"Followers count: {followers_count}")
                        elif metric_name == "page_fan_adds":
                            fan_adds = sum(v.get("value", 0) for v in values if v.get("value"))
                        elif metric_name == "page_fan_removes":
                            fan_removes = sum(v.get("value", 0) for v in values if v.get("value"))
                    
                    logger.info(f"Page insights - Impressions: {total_impressions}, Reach: {total_reach}, Followers: {followers_count}")
                else:
                    error_data = await page_response.json()
                    error_code = error_data.get("error", {}).get("code")
                    error_subcode = error_data.get("error", {}).get("error_subcode")
                    error_message = error_data.get("error", {}).get("message", "Unknown error")
                    
                    logger.error(f"Page insights request failed with status {page_response.status_code}: {error_data}")
                    
                    # Check for specific token expiration types
                    if error_code == 190:
                        if error_subcode == 467:
                            logger.error("Facebook token expired - user logged out")
                            raise Exception("Your Facebook session has expired. Please log back into Facebook and reconnect your Meta account.")
                        elif error_subcode == 463:
                            logger.error("Facebook token expired - session expired")
                            raise Exception("Your Facebook access token has expired. Please reconnect your Meta account.")
                        else:
                            logger.error(f"Facebook token validation failed (code: {error_code}, subcode: {error_subcode})")
                            raise Exception("Facebook access token is invalid. Please reconnect your Meta account.")
                    else:
                        logger.error(f"Facebook API error: {error_message} (code: {error_code})")
                        raise Exception(f"Facebook API error: {error_message}")
            except Exception as e:
                logger.warning(f"Page insights failed: {str(e)}, continuing with post-level aggregation")
            
            # Get recent posts for engagement aggregation
            posts_url = f"{GRAPH_API_URL}/{page_id}/posts"
            posts_params = {
                "fields": "id,likes.summary(true),comments.summary(true),shares",
                "limit": 50,
                "since": since_str,
                "access_token": access_token
            }
            
            try:
                posts_response = await client.get(posts_url, params=posts_params)
                if posts_response.status_code == 200:
                    posts_data = posts_response.json()
                    posts = posts_data.get("data", [])
                    
                    logger.info(f"Found {len(posts)} posts for engagement aggregation")
                    
                    # Aggregate engagement from posts
                    for post in posts:
                        likes_data = post.get("likes", {})
                        comments_data = post.get("comments", {})
                        shares_count = post.get("shares", {}).get("count", 0)
                        
                        likes_count = likes_data.get("summary", {}).get("total_count", 0) if likes_data else 0
                        comments_count = comments_data.get("summary", {}).get("total_count", 0) if comments_data else 0
                        
                        total_engagement += likes_count + comments_count + shares_count
                
                logger.info(f"Enhanced Facebook insights - Total engagement: {total_engagement}")
            except Exception as e:
                logger.warning(f"Posts aggregation failed: {str(e)}")
        
        # Calculate net follower change
        net_followers = fan_adds - fan_removes
        
        return {
            "total_impressions": total_impressions,
            "total_reach": total_reach, 
            "total_engagement": total_engagement,
            "followers_count": followers_count,
            "fan_adds": fan_adds,
            "fan_removes": fan_removes,
            "net_followers": net_followers,
            "posts_count": len(posts) if 'posts' in locals() else 0,
            "profile_visits": max(int(total_reach * 0.05), 1),  # Estimate profile visits as 5% of reach
            "source": "enhanced_live_api_v23",
            "api_version": "v23.0",
            "date_range": f"{since_str} to {until_str}",
            "message": f"Enhanced Facebook insights using v23.0 API"
        }
        
    except Exception as e:
        logger.error(f"Error getting enhanced Facebook insights: {str(e)}")
        return {
            "total_impressions": 0,
            "total_reach": 0,
            "total_engagement": 0,
            "followers_count": 0,
            "fan_adds": 0,
            "fan_removes": 0,
            "net_followers": 0,
            "posts_count": 0,
            "profile_visits": 0,
            "source": "enhanced_facebook_error",
            "error": str(e),
            "message": "Enhanced Facebook API request failed"
        }

# --- Helper Functions ---

def handle_meta_api_error(error: Any, endpoint_name: str, extra_context: Optional[Dict[str, Any]] = None) -> HTTPException:
    """Enhanced error handler with Instagram-specific error support"""
    # Import here to avoid circular imports
    from services.instagram_utils import InstagramAPIError, INSTAGRAM_ERROR_CODES
    
    error_str = str(error)
    context_str = f" - Context: {json.dumps(extra_context)}" if extra_context else ""
    logger.error(f"Error in {endpoint_name}{context_str}: {error_str}")
    logger.debug(f"Detailed error traceback for {endpoint_name}: {traceback.format_exc()}")

    if isinstance(error, HTTPException):
        return error
    
    # Handle Instagram-specific errors
    if isinstance(error, InstagramAPIError):
        if error.code in [190, 10]:
            status_code = 401
        elif error.code in [100, 2500]:
            status_code = 400
        elif error.code in [4, 17]:
            status_code = 429
        else:
            status_code = 500
        
        return HTTPException(
            status_code=status_code,
            detail={
                "error": error.message,
                "error_code": error.code,
                "error_action": error.action,
                "instagram_specific": True
            }
        )

    status_code = 500
    detail_message = f"An unexpected error occurred: {error_str}"

    if hasattr(error, 'response') and error.response:
        try:
            error_data: dict = {}
            if hasattr(error.response, 'json') and callable(error.response.json):
                response_json = error.response.json()
                error_data = response_json if isinstance(response_json, dict) else {}
            elif hasattr(error.response, 'text'):
                 logger.warning(f"Meta API error response for {endpoint_name} was not JSON: {error.response.text[:200]}")

            meta_error = error_data.get("error", {})
            error_code = meta_error.get("code")
            message_from_meta = meta_error.get("message", error_str)

            # Check if this is an Instagram-specific error
            if error_code in INSTAGRAM_ERROR_CODES:
                instagram_error_info = INSTAGRAM_ERROR_CODES[error_code]
                return HTTPException(
                    status_code=401 if error_code in [190, 10] else 
                               400 if error_code in [100, 2500] else
                               429 if error_code in [4, 17] else 500,
                    detail={
                        "error": instagram_error_info["message"],
                        "error_code": error_code,
                        "error_action": instagram_error_info["action"],
                        "instagram_specific": True
                    }
                )

            if error_code == 190:
                status_code = 401
                detail_message = f"Authentication error with Meta API: {message_from_meta}"
            elif error_code == 100:
                status_code = 400
                detail_message = f"Invalid parameter in Meta API request: {message_from_meta}"
            elif error_code in [10, 200, 2000]:
                status_code = 403
                detail_message = f"Permission denied by Meta API: {message_from_meta}"
            elif error_code == 4 or error_code == 17:
                status_code = 429
                detail_message = f"Meta API rate limit reached: {message_from_meta}"
            else:
                status_code = getattr(error.response, "status_code", 500)
                detail_message = f"Meta API Error ({error_code}): {message_from_meta}"
        except json.JSONDecodeError:
            detail_message = f"Meta API returned non-JSON error: {getattr(error.response, 'text', '')[:200]}"
            status_code = getattr(error.response, "status_code", 500)
        except Exception as e:
             logger.error(f"Error processing Meta API error response itself: {e}")
             detail_message = f"Error processing Meta API error response. Original: {error_str}"
    elif isinstance(error, httpx.RequestError):
        status_code = 503
        detail_message = f"Service temporarily unavailable. Could not connect to Meta API: {error_str}"
    elif "Connection" in error_str or "Timeout" in error_str:
        status_code = 503
    elif "access token" in error_str.lower() or "oauth" in error_str.lower() or "expired" in error_str.lower():
        status_code = 401
    elif "permission" in error_str.lower() or "access" in error_str.lower():
        if status_code == 500: status_code = 403
    elif "rate limit" in error_str.lower() or "too many" in error_str.lower():
        if status_code == 500: status_code = 429
    elif "not found" in error_str.lower() or "doesn't exist" in error_str.lower():
        status_code = 404
    elif "invalid" in error_str.lower() or "parameter" in error_str.lower():
        if status_code == 500: status_code = 400

    return HTTPException(status_code=status_code, detail=detail_message)


async def _get_page_info_and_token(store_id: str, page_id: str) -> Tuple[Dict[str, Any], str, str]:
    store_doc = await db_analysis["global_analysis"].find_one({"_id": store_id})
    if not store_doc or "meta_integration" not in store_doc or not store_doc["meta_integration"].get("connected"):
        logger.warning(f"Meta account not connected for store {store_id} or store_doc not found.")
        raise HTTPException(status_code=400, detail="Meta account not connected for this store.")

    page_info_found: Optional[Dict[str, Any]] = None
    access_token_found: Optional[str] = None
    platform_found: str = "facebook"

    for p in store_doc["meta_integration"].get("pages", []):
        if p.get("id") == page_id:
            page_info_found = p
            access_token_found = p.get("access_token")
            platform_found = p.get("platform", "facebook")
            break

    if not page_info_found or not access_token_found:
        logger.warning(f"Page with ID {page_id} not found for store {store_id} or missing access token.")
        raise HTTPException(status_code=404, detail=f"Page with ID {page_id} not found for store {store_id} or missing access token.")
    return page_info_found, access_token_found, platform_found

async def build_ad_metrics_from_campaigns(page_id: str, store_id: str, since_date: datetime, until_date: datetime) -> Dict[str, Any]:
    """
    Build ad metrics response from MongoDB campaign and metrics data when API fails
    """
    try:
        logger.info(f"Building ad metrics from MongoDB for page {page_id}, date range: {since_date} to {until_date}")
        
        # First, try to get detailed daily metrics data
        metrics_filter = {
            "page_id": page_id,
            "store_id": store_id,
            "date": {"$gte": since_date, "$lte": until_date}
        }
        
        daily_metrics_cursor = db_analysis["meta_ad_metrics"].find(metrics_filter)
        daily_metrics = await daily_metrics_cursor.to_list(length=None)
        
        if not daily_metrics:
            # Try without date filter for lifetime data
            daily_metrics_cursor = db_analysis["meta_ad_metrics"].find({"page_id": page_id, "store_id": store_id})
            daily_metrics = await daily_metrics_cursor.to_list(length=None)
            logger.info(f"No metrics in date range, found {len(daily_metrics)} lifetime metrics")
        
        # Query campaigns from MongoDB
        campaigns_cursor = db_analysis["meta_ad_campaigns"].find({"page_id": page_id, "store_id": store_id})
        campaigns_data = await campaigns_cursor.to_list(length=None)
        
        if not campaigns_data and not daily_metrics:
            logger.warning(f"No campaigns or metrics data found in MongoDB for page {page_id}")
            return create_empty_ad_metrics_response()
        
        # Initialize totals
        total_spend = 0.0
        total_impressions = 0
        total_clicks = 0
        total_conversions = 0
        campaigns_list = []
        daily_metrics_list = []
        
        # Group daily metrics by campaign
        metrics_by_campaign = {}
        for metric in daily_metrics:
            campaign_id = metric.get("campaign_id")
            if campaign_id not in metrics_by_campaign:
                metrics_by_campaign[campaign_id] = []
            metrics_by_campaign[campaign_id].append(metric)
        
        # Process each campaign from MongoDB
        for campaign in campaigns_data:
            campaign_id = campaign.get("id")
            spend = float(campaign.get("spend", 0))
            
            # Get real metrics data from daily metrics if available
            campaign_metrics = metrics_by_campaign.get(campaign_id, [])
            if campaign_metrics:
                # Use real data from daily metrics
                impressions = sum(int(m.get("impressions", 0)) for m in campaign_metrics)
                clicks = sum(int(m.get("clicks", 0)) for m in campaign_metrics)
                conversions = sum(int(m.get("conversions", 0)) for m in campaign_metrics)
                # Use actual spend from metrics if available, otherwise from campaign
                metrics_spend = sum(float(m.get("spend", 0)) for m in campaign_metrics)
                if metrics_spend > 0:
                    spend = metrics_spend
            else:
                # Estimate from campaign data
                conversions = int(campaign.get("results", 0))
                # Estimate impressions and clicks from spend
                impressions = int(spend * 50) if spend > 0 else 0  # Rough estimate: $1 = ~50 impressions
                clicks = int(impressions * 0.02) if impressions > 0 else 0  # Rough 2% CTR
            
            # Calculate derived metrics
            ctr = (clicks / impressions * 100) if impressions > 0 else 0
            cpc = (spend / clicks) if clicks > 0 else 0
            cost_per_conversion = (spend / conversions) if conversions > 0 else 0
            roas = 4.0  # Default estimate
            
            # Build campaign object
            campaign_obj = {
                "id": campaign.get("id", "unknown"),
                "name": campaign.get("name", "Unknown Campaign"),
                "status": campaign.get("status", "UNKNOWN"),
                "objective": campaign.get("objective", "UNKNOWN"),
                "platform": campaign.get("platform", "instagram"),
                "spend": round(spend, 2),
                "impressions": impressions,
                "clicks": clicks,
                "conversions": conversions,
                "ctr": round(ctr, 2),
                "cpc": round(cpc, 2),
                "cost_per_conversion": round(cost_per_conversion, 2),
                "roas": round(roas, 2),
                "start_time": campaign.get("start_time"),
                "end_time": campaign.get("end_time"),
                "budget": campaign.get("budget")
            }
            
            campaigns_list.append(campaign_obj)
            
            # Add to totals
            total_spend += spend
            total_impressions += impressions
            total_clicks += clicks
            total_conversions += conversions
        
        # Process daily metrics for time series data
        for metric in daily_metrics:
            daily_metrics_list.append({
                "date": metric.get("date"),
                "spend": float(metric.get("spend", 0)),
                "impressions": int(metric.get("impressions", 0)),
                "clicks": int(metric.get("clicks", 0)),
                "conversions": int(metric.get("conversions", 0)),
                "ctr": float(metric.get("ctr", 0)),
                "cpc": float(metric.get("cpc", 0)),
                "campaign_id": metric.get("campaign_id")
            })
        
        # Calculate overview metrics
        overview_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
        overview_cpc = (total_spend / total_clicks) if total_clicks > 0 else 0
        overview_cost_per_conversion = (total_spend / total_conversions) if total_conversions > 0 else 0
        overview_roas = 4.0  # Default estimate
        
        response = {
            "overview": {
                "total_spend": round(total_spend, 2),
                "total_impressions": total_impressions,
                "total_clicks": total_clicks,
                "total_conversions": total_conversions,
                "ctr": round(overview_ctr, 2),
                "cpc": round(overview_cpc, 2),
                "cost_per_conversion": round(overview_cost_per_conversion, 2),
                "roas": round(overview_roas, 2)
            },
            "campaigns": campaigns_list,
            "daily_metrics": daily_metrics_list,
            "source": "mongodb_enhanced"
        }
        
        logger.info(f"Built metrics response from {len(campaigns_list)} MongoDB campaigns, total spend: ${total_spend}")
        return response
        
    except Exception as e:
        logger.error(f"Error building metrics from campaigns: {e}")
        raise

def _convert_time_range_to_dates(time_range_str: Optional[str], since_param: Optional[str], until_param: Optional[str]) -> Tuple[datetime, datetime]:
    now = datetime.now(timezone.utc)
    until_date = now

    if since_param and until_param:
        try:
            since_date = datetime.strptime(since_param, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            until_date = datetime.strptime(until_param, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            if until_date > now: until_date = now
            if since_date > until_date: since_date = until_date - timedelta(days=1)
            logger.debug(f"Using explicit date range: {since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}")
            return since_date, until_date
        except ValueError:
            logger.warning(f"Invalid custom date format: since='{since_param}', until='{until_param}'. Falling back.")

    time_range_to_use = time_range_str or "30d"
    
    if time_range_to_use == "lifetime":
        since_date = datetime(2000, 1, 1, tzinfo=timezone.utc)
        logger.debug(f"Using lifetime range: {since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}")
        return since_date, until_date

    days_map = {'d': 1, 'w': 7, 'm': 30, 'y': 365}
    num = 30 # Default
    unit_char = 'd' # Default

    match = re.match(r"(\d+)([dwmy])", time_range_to_use.lower())
    if match:
        num = int(match.group(1))
        unit_char = match.group(2)
    
    delta_days = num * days_map.get(unit_char, 30) # Default to 30 days if unit is weird
    since_date = now - timedelta(days=delta_days)
    
    if time_range_to_use == "week": since_date = now - timedelta(days=now.weekday()) # Start of current week (Mon)
    elif time_range_to_use == "month": since_date = now.replace(day=1)
    elif time_range_to_use == "quarter":
        quarter_month = (now.month - 1) // 3 * 3 + 1
        since_date = now.replace(month=quarter_month, day=1)

    if until_date < since_date:
        until_date = since_date + timedelta(days=1)
    
    logger.debug(f"Calculated date range from '{time_range_to_use}': {since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}")
    return since_date, until_date

# --- API Endpoints ---

@router.post("/connect")
async def connect_meta_account(
    business_data: MetaBusinessData,
    current_user: User = Depends(get_current_active_user)
):
    try:
        processed_pages = []
        for page in business_data.pages:
            page_dict = page.dict()
            if "platform" not in page_dict or not page_dict["platform"]:
                page_dict["platform"] = "facebook"
            processed_pages.append(page_dict)

        update_payload = {
            "meta_integration.connected": True,
            "meta_integration.business_id": business_data.business_id,
            "meta_integration.pages": processed_pages,
            "meta_integration.last_sync": datetime.now(timezone.utc)
        }
        
        result = await db_analysis["global_analysis"].update_one(
            {"_id": str(current_user.id_store)},
            {
                "$set": update_payload,
                "$setOnInsert": {"meta_integration.insights": MetaPageInsights().dict()}
            },
            upsert=True 
        )

        if not result.acknowledged:
            raise HTTPException(status_code=500, detail="Failed to update Meta integration (DB op not acknowledged).")

        if result.upserted_id:
            logger.info(f"New Meta integration created for store {current_user.id_store}.")
        elif result.matched_count > 0 and result.modified_count == 0:
            logger.info(f"Meta integration for store {current_user.id_store} already up-to-date.")
        elif result.matched_count > 0 and result.modified_count > 0:
            logger.info(f"Meta integration updated for store {current_user.id_store}.")
        else: 
            logger.warning(f"Meta integration update for store {current_user.id_store} had an unexpected result: matched={result.matched_count}, modified={result.modified_count}")
        return {"message": "Meta business account connected successfully"}
    except Exception as e:
        raise handle_meta_api_error(e, "connect_meta_account", {"store_id": str(current_user.id_store)})


@router.get("/{store_id}/pages")
async def get_pages(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching Meta pages for store_id: {store_id}")
            store_doc = await db_analysis["global_analysis"].find_one({"_id": store_id})
            if not store_doc or "meta_integration" not in store_doc or not store_doc["meta_integration"].get("connected"):
                return {"pages": []}

            stored_pages = store_doc["meta_integration"].get("pages", [])
            if not stored_pages: return {"pages": []}

            updated_pages_for_db: List[Dict[str, Any]] = []
            db_update_needed = False

            for page_in_db in stored_pages:
                page_id = page_in_db.get("id")
                access_token = page_in_db.get("access_token")
                platform = page_in_db.get("platform", "facebook")

                if not page_id or not access_token:
                    logger.warning(f"Skipping page with missing ID or access token: {page_in_db.get('name')}")
                    updated_pages_for_db.append(page_in_db)
                    continue

                fields_to_fetch = ["id", "name", "category"]
                if platform == "facebook": fields_to_fetch.extend(["fan_count", "picture{url}"])
                elif platform == "instagram": fields_to_fetch.extend(["followers_count", "profile_picture_url", "username"])
                
                api_params = {"access_token": access_token, "fields": ",".join(fields_to_fetch)}
                
                try:
                    response = await client.get(f"{GRAPH_API_URL}/{page_id}", params=api_params)
                    response.raise_for_status()
                    page_data_from_api = response.json()

                    updated_page_data = page_in_db.copy()
                    updated_page_data.update({
                        "name": page_data_from_api.get("name", page_in_db.get("name")),
                        "category": page_data_from_api.get("category", page_in_db.get("category")),
                        "platform": platform
                    })

                    if platform == "facebook":
                        updated_page_data["fan_count"] = page_data_from_api.get("fan_count", 0)
                        if "picture" in page_data_from_api and isinstance(page_data_from_api["picture"], dict) and "data" in page_data_from_api["picture"]:
                            updated_page_data["profile_picture_url"] = page_data_from_api["picture"]["data"].get("url")
                    elif platform == "instagram":
                        updated_page_data["followers_count"] = page_data_from_api.get("followers_count", 0)
                        updated_page_data["profile_picture_url"] = page_data_from_api.get("profile_picture_url")
                        updated_page_data["name"] = page_data_from_api.get("username", updated_page_data["name"])

                    if platform == "instagram" and "instagram_business_account" not in updated_page_data:
                        ig_business_response = await client.get(f"{GRAPH_API_URL}/{page_id}", params={"access_token": access_token, "fields": "instagram_business_account"})
                        if ig_business_response.status_code == 200 and "instagram_business_account" in ig_business_response.json():
                            updated_page_data["instagram_business_account"] = ig_business_response.json()["instagram_business_account"]
                    
                    updated_pages_for_db.append(updated_page_data)
                    if json.dumps(updated_page_data, sort_keys=True) != json.dumps(page_in_db, sort_keys=True):
                        db_update_needed = True; logger.info(f"Refreshed data for page {page_id}")
                except httpx.HTTPStatusError as http_err:
                    logger.warning(f"HTTP error refreshing page {page_id}: {http_err.response.status_code} - {http_err.response.text}")
                    updated_pages_for_db.append(page_in_db)
                except Exception as req_err:
                    logger.warning(f"Request error refreshing page {page_id}: {str(req_err)}")
                    updated_pages_for_db.append(page_in_db)

            if db_update_needed:
                if "meta_integration" not in store_doc or not isinstance(store_doc["meta_integration"], dict):
                    store_doc["meta_integration"] = {}
                store_doc["meta_integration"]["pages"] = updated_pages_for_db
                store_doc["meta_integration"]["last_sync"] = datetime.now(timezone.utc)
                await db_analysis["global_analysis"].update_one({"_id": store_id}, {"$set": {"meta_integration": store_doc["meta_integration"]}})
                logger.info(f"Updated Meta integration with refreshed page data for store {store_id}")
            return {"pages": updated_pages_for_db}
        except Exception as e:
            raise handle_meta_api_error(e, "get_pages", {"store_id": store_id})


@router.get("/{store_id}/insights")
async def get_meta_insights(
    store_id: str, 
    page_id: str = Query(..., description="Facebook page ID"),
    metric_type: str = Query(..., description="Type of metric to fetch (e.g., page_impressions, page_engagement)"),
    since: str = Query(..., description="Start date (YYYY-MM-DD)"),
    until: str = Query(..., description="End date (YYYY-MM-DD)"),
    platform: str = Query("facebook", description="Platform type (facebook or instagram)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Get Facebook page insights for specific metrics
    """
    try:
        # Get page info and access token
        page_doc = await db_analysis["meta_pages"].find_one({"id": page_id, "store_id": store_id})
        if not page_doc:
            raise HTTPException(status_code=404, detail=f"Page {page_id} not found for store {store_id}")
        
        # Check if token is already marked as expired
        if page_doc.get("token_expired"):
            logger.info(f"Page {page_id} token already marked as expired")
            return {
                "total_value": 0,
                "data": [],
                "metric_type": metric_type,
                "period": f"{since} to {until}",
                "count": 0,
                "error": "Facebook session expired - please reconnect your Facebook account",
                "requires_reconnect": True
            }
        
        access_token = page_doc.get("access_token")
        if not access_token:
            raise HTTPException(status_code=401, detail="Page access token not found")
        
        # For page insights, we must use the page access token, not the user access token
        # Validate the page access token but don't replace it with user token
        from routes.auth import refresh_meta_token_if_needed
        user_token = await refresh_meta_token_if_needed(store_id)
        if user_token:
            logger.info(f"User token is valid for store {store_id}, using page access token for insights")
        else:
            logger.warning(f"User token validation failed for store {store_id}, still using page access token")
        
        # Always use page access token for page insights (required by Facebook API)
        
        # Build Facebook Graph API URL using settings-based version
        base_url = f"{GRAPH_API_URL}/{page_id}/insights"
        
        # Map frontend metric names to valid Facebook API v23.0 metric names
        metric_mapping = {
            "page_views_total": "page_views",  # Fix: page_views_total -> page_views
            "page_engagement": "page_engaged_users",  # Fix: page_engagement -> page_engaged_users
            "page_fans": "page_fans",  # Valid
            "page_impressions": "page_impressions"  # Valid
        }
        
        # Use mapped metric name if available, otherwise use original
        api_metric = metric_mapping.get(metric_type, metric_type)
        
        params = {
            "metric": api_metric,
            "period": "day",
            "since": since,
            "until": until,
            "access_token": access_token
        }
        
        logger.info(f"Fetching Facebook insights for page {page_id}, metric: {metric_type}")
        
        # Make API call
        async with httpx.AsyncClient() as client:
            response = await client.get(base_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            insights_data = data.get("data", [])
            
            logger.info(f"Retrieved {len(insights_data)} insight records for {metric_type}")
            
            # Transform data to format expected by frontend
            # Calculate total value for overview panel and individual data points for charts
            total_value = 0
            transformed_data = []
            
            for insight in insights_data:
                values = insight.get("values", [])
                for value_entry in values:
                    value = value_entry.get("value", 0)
                    # Handle different value types
                    if isinstance(value, dict):
                        # Some metrics return objects (e.g., engagement has actions breakdown)
                        # Sum all numeric values in the object for total
                        value = sum(v for v in value.values() if isinstance(v, (int, float)))
                    elif isinstance(value, str):
                        # Some metrics return string numbers
                        try:
                            value = float(value)
                        except ValueError:
                            value = 0
                    
                    total_value += value
                    transformed_data.append({
                        "end_time": value_entry.get("end_time"),
                        "value": value
                    })
            
            return {
                "total_value": total_value,
                "data": transformed_data,
                "metric_type": metric_type,
                "period": f"{since} to {until}",
                "count": len(transformed_data)
            }
            
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error for insights: {e.response.status_code} - {e.response.text}")
        
        # Parse error response
        try:
            error_data = json.loads(e.response.text)
            fb_error = error_data.get("error", {})
            error_code = fb_error.get("code")
            error_subcode = fb_error.get("error_subcode")
            error_message = fb_error.get("message", "Unknown error")
        except:
            fb_error = {}
            error_code = None
            error_subcode = None
            error_message = e.response.text
        
        # Handle specific Facebook errors
        if error_code == 190 and error_subcode == 467:
            # User logged out - token is invalid
            logger.warning(f"Facebook page token invalid - user logged out for page {page_id}")
            
            # Mark token as expired in database
            await db_analysis["meta_pages"].update_one(
                {"id": page_id, "store_id": store_id},
                {"$set": {"token_expired": True, "token_error": error_message}}
            )
            
            return {
                "total_value": 0,
                "data": [],
                "metric_type": metric_type,
                "period": f"{since} to {until}",
                "count": 0,
                "error": "Facebook session expired - please reconnect your Facebook account",
                "requires_reconnect": True
            }
        elif e.response.status_code == 400:
            # Other 400 errors - invalid parameters
            return {
                "total_value": 0,
                "data": [],
                "metric_type": metric_type,
                "period": f"{since} to {until}",
                "count": 0,
                "error": error_message or "Invalid date range or metric"
            }
        
        raise HTTPException(status_code=e.response.status_code, detail=f"Facebook API error: {error_message}")
    except Exception as e:
        logger.error(f"Error fetching Facebook insights: {e}")
        raise handle_meta_api_error(e, "get_meta_insights", {"store_id": store_id, "page_id": page_id})

@router.get("/{store_id}/integration")
async def get_meta_integration(store_id: str, current_user: User = Depends(verify_user_can_access_store)):
    """
    Get Meta integration status and stored data
    """
    try:
        store = await db_analysis["global_analysis"].find_one({"_id": store_id})
        if not store or not store.get("meta_integration", {}).get("connected"):
            raise HTTPException(status_code=404, detail="Meta business account not connected")
        return store.get("meta_integration", {})
    except Exception as e:
        raise handle_meta_api_error(e, "get_meta_integration", {"store_id": store_id})


@router.post("/{store_id}/insights/update")
async def update_meta_insights_endpoint(insights: MetaPageInsights, store_id: str, current_user: User = Depends(verify_user_can_access_store)):
    try:
        result = await db_analysis["global_analysis"].update_one(
            {"_id": store_id, "meta_integration.connected": True},
            {"$set": {"meta_integration.insights": insights.dict(), "meta_integration.last_sync": datetime.now(timezone.utc)}}
        )
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Store not found or Meta not connected")
        return {"message": "Meta insights updated successfully"}
    except Exception as e:
        raise handle_meta_api_error(e, "update_meta_insights", {"store_id": store_id})

# --- Mock Data Endpoints ---
@router.get("/mock-data")
async def get_mock_meta_data(current_user: User = Depends(get_current_active_user)):
    # Check if mock data is enabled
    if not os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true":
        raise HTTPException(status_code=404, detail="Mock data endpoints are disabled. Set ENABLE_META_MOCK_DATA=true to enable.")
    
    try:
        store_id = str(current_user.id_store)
        logger.info(f"Fetching mock Meta data for store_id: {store_id}")
        mock_pages_cursor = db_analysis["meta_pages"].find({"store_id": store_id, "is_mock": True})
        mock_pages = await mock_pages_cursor.to_list(length=None)
        if not mock_pages:
            logger.warning(f"No mock pages found for store_id: {store_id}")
            return {"connected": False, "is_mock_data": True, "pages": [], "last_sync": datetime.now(timezone.utc).isoformat()}
        pages = [{k: v for k, v in page.items() if k != "_id"} for page in mock_pages]
        return {"connected": False, "is_mock_data": True, "pages": pages, "last_sync": datetime.now(timezone.utc).isoformat()}
    except Exception as e:
        raise handle_meta_api_error(e, "get_mock_meta_data", {"store_id": str(current_user.id_store)})

@router.get("/mock-data/posts/{page_id}")
async def get_mock_posts(page_id: str, current_user: User = Depends(get_current_active_user)):
    # Check if mock data is enabled
    if not os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true":
        raise HTTPException(status_code=404, detail="Mock data endpoints are disabled. Set ENABLE_META_MOCK_DATA=true to enable.")
    
    try:
        store_id = str(current_user.id_store)
        logger.info(f"Fetching mock posts for page_id: {page_id}, store_id: {store_id}")
        page = await db_analysis["meta_pages"].find_one({"id": page_id, "store_id": store_id, "is_mock": True})
        if not page: raise HTTPException(status_code=404, detail="Mock page not found")
        mock_posts_cursor = db_analysis["meta_posts"].find({"page_id": page_id, "store_id": store_id, "is_mock": True})
        mock_posts = await mock_posts_cursor.to_list(length=None)
        if not mock_posts: return []
        return [{k: v for k, v in post.items() if k != "_id"} for post in mock_posts]
    except Exception as e:
        raise handle_meta_api_error(e, "get_mock_posts", {"page_id": page_id})

@router.get("/mock-data/ad-metrics/{page_id}")
async def get_mock_ad_metrics_endpoint(page_id: str, time_range: str = "30d", current_user: User = Depends(get_current_active_user)):
    # Check if mock data is enabled
    if not os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true":
        raise HTTPException(status_code=404, detail="Mock data endpoints are disabled. Set ENABLE_META_MOCK_DATA=true to enable.")
    
    logger.warning(f"DEPRECATED: Using mock ad metrics endpoint for page {page_id}. Redirecting.")
    store_id = str(current_user.id_store)
    return await get_page_ad_metrics(page_id=page_id, store_id=store_id, time_range=time_range, mongodb_only=True, current_user=current_user)

@router.get("/mock-data/campaigns/{page_id}")
async def get_mock_campaigns_endpoint(page_id: str, platform: Optional[str] = None, current_user: User = Depends(get_current_active_user)):
    # Check if mock data is enabled
    if not os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true":
        raise HTTPException(status_code=404, detail="Mock data endpoints are disabled. Set ENABLE_META_MOCK_DATA=true to enable.")
    
    try:
        store_id = str(current_user.id_store)
        logger.info(f"Fetching mock campaigns for page_id: {page_id}, store_id: {store_id}, platform: {platform}")
        query: Dict[str, Any] = {"page_id": page_id, "store_id": store_id, "is_mock": True}
        if platform: query["platform"] = platform
        
        mock_campaigns_cursor = db_analysis["meta_ad_campaigns"].find(query)
        mock_campaigns = await mock_campaigns_cursor.to_list(length=None)
        if not mock_campaigns and platform:
            del query["platform"]
            mock_campaigns_cursor = db_analysis["meta_ad_campaigns"].find(query)
            mock_campaigns = await mock_campaigns_cursor.to_list(length=None)
        if not mock_campaigns: return []
        def normalize_mock_campaign(c: Dict[str, Any]):
            return {"id": c.get("id", ""), "name": c.get("name", ""), "status": c.get("status", "UNKNOWN"), "objective": c.get("objective", "UNKNOWN"), "start_time": c.get("start_time", ""), "end_time": c.get("end_time"), "budget": c.get("budget", 0), "daily_budget": c.get("daily_budget"), "lifetime_budget": c.get("lifetime_budget"), "conversion_rate": c.get("conversion_rate", 0), "correlation_metric": c.get("correlation_metric", 0), "correlation_data": c.get("correlation_data", [])}
        return [normalize_mock_campaign(c) for c in mock_campaigns]
    except Exception as e:
        raise handle_meta_api_error(e, "get_mock_campaigns", {"page_id": page_id})

# --- Real Data Endpoints ---
@router.get("/{store_id}/ad-metrics/{page_id}")
async def get_page_ad_metrics(
    store_id: str = Path(..., description="The ID of the store for authorization and context"),
    page_id: str = Path(..., description="The ID of the Meta page"),
    time_range: str = Query("30d", description="Time range for metrics (e.g., 7d, 30d, 90d, week, month)"),
    range_preset: Optional[str] = Query(None, alias="range", description="Special range values like 'lifetime' (overrides time_range)"),
    since: Optional[str] = Query(None, description="Start date (YYYY-MM-DD, overrides time_range/range)"),
    until: Optional[str] = Query(None, description="End date (YYYY-MM-DD, overrides time_range/range)"),
    include_organic: bool = Query(True, description="Include organic metrics along with ad metrics"),
    force_refresh: bool = Query(False, description="Force refresh data from Meta API, ignoring cache"),
    max_data_age: int = Query(24, description="Maximum age of cached data in hours before refresh"),
    mongodb_only: bool = Query(False, description="Only check MongoDB cache, do not make Meta API calls"),
    current_user: User = Depends(verify_user_can_access_store)
):
    # The httpx.AsyncClient is now managed by the service layer for this specific call.
    # If other calls in this function needed it, it would remain.
    # For now, assuming the primary API call is being moved to the service.
    try:
        page_doc = await db_analysis["meta_pages"].find_one({"id": page_id, "store_id": store_id})
        if not page_doc: raise HTTPException(status_code=404, detail=f"Page {page_id} not found for store {store_id}")

        platform = page_doc.get("platform", "facebook"); is_instagram = platform == "instagram"
        access_token = page_doc.get("access_token") # Used for service call via page_doc
        final_since, final_until = _convert_time_range_to_dates(range_preset or time_range, since, until)
        date_format = "%Y-%m-%d" # Used for strftime

        if mongodb_only:
            logger.info(f"MongoDB-only mode for page {page_id}, store {store_id}")
            metrics_records_cursor = db_analysis["meta_ad_metrics"].find({"page_id": page_id, "store_id": store_id})
            metrics_records = await metrics_records_cursor.to_list(length=None)
            if not metrics_records: return create_empty_ad_metrics_response()
            if metrics_records and metrics_records[0].get("metrics"): return metrics_records[0]["metrics"]
            return create_empty_ad_metrics_response()

        if not force_refresh:
            cached_data = await db_analysis["meta_ad_metrics"].find_one({"page_id": page_id, "store_id": store_id})
            if cached_data and cached_data.get("last_updated"):
                data_age_hours = (datetime.now(timezone.utc) - cached_data["last_updated"]).total_seconds() / 3600
                if data_age_hours <= max_data_age and cached_data.get("metrics"):
                    logger.info(f"Using cached ad metrics for page {page_id} (age: {data_age_hours:.2f}h)")
                    return cached_data["metrics"]
        
        if not access_token: raise HTTPException(status_code=401, detail="Page access token not found. Reconnect Meta account.")

        # ad_account_id is part of page_doc, which will be passed to the service.
        # The service function fetch_ad_account_insights_data handles extracting ad_account_id from page_doc.
        # Ensure page_doc includes ad_account_id or logic to derive it (as it did before)
        # The fallback logic for ad_account_id in the route will ensure page_doc has it if possible.
        ad_account_id_from_page = page_doc.get("ad_account_id")
        if not ad_account_id_from_page: # Fallback logic to find ad_account_id, to ensure page_doc is complete
            business_id = page_doc.get("business_id")
            if business_id:
                ad_account_doc_db = await db_analysis["meta_ad_accounts"].find_one({"business_id": business_id, "store_id": store_id})
                if ad_account_doc_db: page_doc["ad_account_id"] = ad_account_doc_db.get("id")
            if not page_doc.get("ad_account_id"): # check again after potential update
                ad_account_doc_db = await db_analysis["meta_ad_accounts"].find_one({"store_id": store_id})
                if ad_account_doc_db: page_doc["ad_account_id"] = ad_account_doc_db.get("id")
        
        if not page_doc.get("ad_account_id"): 
            logger.warning(f"Could not determine ad_account_id for page {page_id}, store {store_id}")
            return create_empty_ad_metrics_response()


        api_data = None
        api_error = None # To store potential error from service call

        if not mongodb_only: # This condition was around the direct API call, now around service call
            try:
                logger.info(f"Attempting to fetch Ad Metrics via SDK for page: {page_id}, store: {store_id}")
                
                # Try SDK approach first
                from services.meta_sdk import meta_sdk_service
                
                if is_instagram:
                    api_data = await meta_sdk_service.get_instagram_campaigns_sdk(
                        page_id=page_id,
                        access_token=access_token,
                        ad_account_id=page_doc.get("ad_account_id"),
                        since=final_since.strftime(date_format),
                        until=final_until.strftime(date_format)
                    )
                else:
                    api_data = await meta_sdk_service.get_campaigns_sdk(
                        ad_account_id=page_doc.get("ad_account_id"),
                        access_token=access_token,
                        since=final_since.strftime(date_format),
                        until=final_until.strftime(date_format)
                    )
                
                # If SDK returns empty data, fall back to MongoDB then original method
                if not api_data or not api_data.get("campaigns"):
                    logger.info("SDK returned no data, falling back to MongoDB")
                    try:
                        api_data = await build_ad_metrics_from_campaigns(page_id, store_id, final_since, final_until)
                        if api_data and api_data.get("campaigns"):
                            logger.info(f"Successfully retrieved {len(api_data['campaigns'])} campaigns from MongoDB")
                        else:
                            logger.info("MongoDB fallback also empty, trying original method")
                            api_data = await fetch_and_cache_instagram_ad_metrics(
                                page_id=page_id,
                                store_id=store_id,
                                page=page_doc, # page_doc from the route, contains access_token, ad_account_id
                                is_instagram=is_instagram,
                                time_range=(range_preset or time_range), # Use range_preset if available, else time_range from query params
                                since=final_since.strftime(date_format), # Already calculated in the route
                                until=final_until.strftime(date_format), # Already calculated in the route
                                force_refresh=force_refresh, # From route query params
                                max_data_age=max_data_age, # From route query params
                                include_organic=include_organic # From route query params
                            )
                    except Exception as mongodb_error:
                        logger.warning(f"MongoDB fallback failed: {mongodb_error}")
                        api_data = await fetch_and_cache_instagram_ad_metrics(
                            page_id=page_id,
                            store_id=store_id,
                            page=page_doc,
                            is_instagram=is_instagram,
                            time_range=(range_preset or time_range),
                            since=final_since.strftime(date_format),
                            until=final_until.strftime(date_format),
                            force_refresh=force_refresh,
                            max_data_age=max_data_age,
                            include_organic=include_organic
                        )
                # Service function already logs success
                
            except httpx.HTTPStatusError as e:
                api_error = e # Store error to be handled later
                error_data = e.response.json() if e.response else {}
                meta_api_err = error_data.get("error", {})
                err_code = meta_api_err.get("code")
                logger.error(f"Meta API HTTPStatusError in get_page_ad_metrics (via service) for page {page_id}: {e.response.status_code} - {meta_api_err}")
                # Fall back to MongoDB on API errors
                logger.info("API error occurred, falling back to MongoDB")
                try:
                    api_data = await build_ad_metrics_from_campaigns(page_id, store_id, final_since, final_until)
                    if api_data and api_data.get("campaigns"):
                        logger.info(f"MongoDB fallback successful: {len(api_data['campaigns'])} campaigns")
                    else:
                        if err_code == 190 and force_refresh: # OAuthException, token issue
                            logger.info(f"Attempting token refresh for page {page_id} due to API error code 190 (service call).")
                            raise HTTPException(status_code=401, detail=f"Meta API token error (code 190): {meta_api_err.get('message', 'Token may be expired or invalid. Refresh required.')}")
                        else:
                            raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})
                except Exception as mongodb_error:
                    logger.error(f"MongoDB fallback also failed: {mongodb_error}")
                    raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})
            except httpx.RequestError as e:
                api_error = e # Store error
                logger.error(f"Meta API RequestError in get_page_ad_metrics (via service) for page {page_id}: {e}")
                # Try MongoDB fallback
                try:
                    api_data = await build_ad_metrics_from_campaigns(page_id, store_id, final_since, final_until)
                    if api_data and api_data.get("campaigns"):
                        logger.info(f"MongoDB fallback successful after RequestError: {len(api_data['campaigns'])} campaigns")
                    else:
                        raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})
                except Exception as mongodb_error:
                    logger.error(f"MongoDB fallback also failed: {mongodb_error}")
                    raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})
            except Exception as e: # Catch any other unexpected errors including SDK errors
                api_error = e # Store error
                logger.error(f"Unexpected error during Meta API service call in get_page_ad_metrics for page {page_id}: {e}")
                # Try MongoDB fallback for any SDK or other errors
                try:
                    logger.info("Unexpected error occurred, falling back to MongoDB")
                    api_data = await build_ad_metrics_from_campaigns(page_id, store_id, final_since, final_until)
                    if api_data and api_data.get("campaigns"):
                        logger.info(f"MongoDB fallback successful after unexpected error: {len(api_data['campaigns'])} campaigns")
                    else:
                        raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})
                except Exception as mongodb_error:
                    logger.error(f"MongoDB fallback also failed: {mongodb_error}")
                    raise handle_meta_api_error(e, "get_page_ad_metrics (service call)", {"page_id": page_id})

        if api_data and api_data.get("data"): # Check if api_data is not None and has data
            processed_data = process_ad_metrics(api_data, is_instagram)
            if include_organic:
                # Corrected: use the string version of the time range for get_organic_metrics
                organic_metrics = await get_organic_metrics(page_id, (range_preset or time_range))
                processed_data = merge_ad_and_organic_metrics(processed_data, organic_metrics)
            
            await update_meta_ad_metrics(page_id, store_id, processed_data)
            return processed_data
        else: # This block handles cases where api_data is None (e.g. mongodb_only was true and fetch skipped) or fetch failed and api_error is set
            if not mongodb_only: # If we tried to fetch and failed (api_error would be set)
                logger.warning(f"API call via service for page {page_id} did not return data or failed: {str(api_error)}. Falling back to cache.")
            
            # Try to build response from MongoDB campaign data
            try:
                logger.info(f"Building ad metrics response from MongoDB campaign data for page {page_id}")
                campaign_metrics = await build_ad_metrics_from_campaigns(page_id, store_id, final_since, final_until)
                if campaign_metrics and campaign_metrics.get("campaigns"):
                    logger.info(f"Successfully built metrics from {len(campaign_metrics['campaigns'])} campaigns")
                    return campaign_metrics
            except Exception as fallback_error:
                logger.warning(f"Failed to build metrics from campaigns: {fallback_error}")
            
            # Fallback to old cache logic for backwards compatibility
            cached_data = await db_analysis["meta_ad_metrics"].find_one({"page_id": page_id, "store_id": store_id})
            if cached_data and cached_data.get("metrics"):
                return cached_data["metrics"]
            
            # If API call was attempted and failed, and cache is also empty/unsuitable, raise based on api_error
            if api_error and not mongodb_only:
                 raise handle_meta_api_error(api_error, "get_page_ad_metrics (service fallback)", {"page_id": page_id})

            # If mongodb_only was true and cache was empty, or some other path led here without api_error
            return create_empty_ad_metrics_response() # Default empty response if all else fails

    except Exception as e: # General exception handler for the whole route
        # This outer try-except handles errors not caught by the inner API call block, or errors from cache logic
        if not mongodb_only: # Only try cache fallback if we weren't in mongodb_only mode
            logger.warning(f"General error in get_page_ad_metrics for page {page_id}: {str(e)}. Falling back to cache.")
            cached_data = await db_analysis["meta_ad_metrics"].find_one({"page_id": page_id, "store_id": store_id})
            if cached_data and cached_data.get("metrics"):
                return cached_data["metrics"]
        # If it's an HTTPException, re-raise it. Otherwise, wrap it.
        if isinstance(e, HTTPException):
            raise
        raise handle_meta_api_error(e, "get_page_ad_metrics (general error)", {"page_id": page_id})


@router.get("/{store_id}/pages/{page_id}/posts")
async def get_page_posts(
    store_id: str, page_id: str, limit: int = Query(25, ge=1, le=100),
    since: Optional[str] = None, until: Optional[str] = None,
    current_user: User = Depends(verify_user_can_access_store)
):
    _, access_token, platform = await _get_page_info_and_token(store_id, page_id)
    
    api_fields: List[str] = []
    endpoint_path: str

    if platform == "instagram":
        endpoint_path = f"{GRAPH_API_URL}/{page_id}/media" # page_id is IG User ID here
        api_fields = ["id", "caption", "media_type", "media_url", "permalink", "thumbnail_url", "timestamp", "username", "like_count", "comments_count", "children{media_url,id,media_type,thumbnail_url}"]
    else: # facebook
        endpoint_path = f"{GRAPH_API_URL}/{page_id}/posts"
        api_fields = ["id", "message", "created_time", "permalink_url", "status_type", "type", "attachments{media_type,url,title,description,subattachments}", "shares", "likes.summary(true)", "comments.summary(true)"]

    params = {
        "access_token": access_token,
        "fields": ",".join(api_fields),
        "limit": limit
    }
    if since: params["since"] = since
    if until: params["until"] = until

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching posts for {platform} page/account {page_id}, endpoint: {endpoint_path}")
            response = await client.get(endpoint_path, params=params)
            response.raise_for_status()
            data = response.json()
            
            # Store fetched posts to meta_posts collection
            if data.get("data"):
                posts_to_store = []
                for post_data in data["data"]:
                    post_data["store_id"] = store_id
                    post_data["page_id"] = page_id
                    post_data["platform"] = platform
                    # Ensure created_time is properly formatted or converted if needed before DB insert
                    if "created_time" in post_data and isinstance(post_data["created_time"], str):
                        post_data["created_time"] = datetime.fromisoformat(post_data["created_time"].replace("Z", "+00:00"))
                    elif "timestamp" in post_data and isinstance(post_data["timestamp"], str): # Instagram uses timestamp
                        post_data["created_time"] = datetime.fromisoformat(post_data["timestamp"].replace("Z", "+00:00"))
                    
                    posts_to_store.append(post_data)
                
                if posts_to_store:
                    await db_analysis["meta_posts"].insert_many(posts_to_store)
            return data
        except httpx.HTTPStatusError as e:
            raise handle_meta_api_error(e, f"get_{platform}_page_posts", {"page_id": page_id, "store_id": store_id})
        except Exception as e:
            raise handle_meta_api_error(e, f"get_{platform}_page_posts", {"page_id": page_id, "store_id": store_id})

@router.get("/{store_id}/posts/{post_id}/metrics")
async def get_post_metrics(store_id: str, post_id: str, current_user: User = Depends(verify_user_can_access_store)):
    # We need to know the platform of the post_id to call the correct API and get the correct page access token.
    # This might require fetching the post from db if platform isn't passed, or finding page_id associated with post_id first.
    # For simplicity, let's assume we can get page_id and platform from the post_id somehow or it's passed.
    # This is a placeholder for more robust logic to determine page_id and platform from post_id.
    
    # Attempt to find the post in our DB to get page_id and platform
    post_doc = await db_analysis["meta_posts"].find_one({"id": post_id, "store_id": store_id})
    if not post_doc:
        raise HTTPException(status_code=404, detail=f"Post with ID {post_id} not found in database for store {store_id}.")

    page_id = post_doc.get("page_id")
    platform = post_doc.get("platform", "facebook") # Default to facebook if platform not in post_doc
    
    if not page_id:
        raise HTTPException(status_code=404, detail=f"Page ID not found for post {post_id}.")

    _, access_token, _ = await _get_page_info_and_token(store_id, page_id) # platform from _get_page_info_and_token might differ, use post_doc's platform

    api_fields: List[str] = []
    params = {"access_token": access_token}
    
    if platform == "instagram":
        # Instagram post insights
        # Note: Instagram insights for organic posts might be limited.
        # Common metrics: engagement, impressions, reach, saved. Video: video_views. Carousel: carousel_album_engagement, etc.
        # These are requested on the /insights edge of the media object.
        endpoint_path = f"{GRAPH_API_URL}/{post_id}/insights"
        # Check API docs for valid metrics for v23.0
        # v23.0 optimized metrics - removed deprecated demographic combinations
        params["metric"] = ",".join(["engagement", "impressions", "reach", "saved", "total_interactions"]) # v23.0 compatible metrics
    else: # facebook
        endpoint_path = f"{GRAPH_API_URL}/{post_id}"
        # For Facebook, insights are often fields on the post object itself, or specific edges like /insights
        # This example fetches summary fields directly. More detailed insights might be under /insights edge.
        # v23.0 optimized fields - focus on available metrics after September 2024 deprecations
        api_fields = [
            "id",
            "message",
            "created_time",
            "permalink_url",
            "full_picture",
            "insights.metric("
                "post_impressions,"
                "post_impressions_unique,"
                "post_reactions_by_type_total,"
                "post_link_clicks,"
                "post_photo_views"
            ")", # Updated for v23.0 - using available post-level metrics
            "likes.summary(true)", 
            "comments.summary(true)", 
            "shares"
        ]
        params["fields"] = ",".join(api_fields)
        
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching metrics for {platform} post {post_id}, endpoint: {endpoint_path}")
            response = await client.get(endpoint_path, params=params)
            response.raise_for_status()
            data = response.json()

            # Store fetched metrics to meta_post_metrics collection
            metrics_to_store = {
                "post_id": post_id,
                "page_id": page_id,
                "store_id": store_id,
                "platform": platform,
                "metrics_data": data, # Store the raw response or transform it
                "fetched_at": datetime.now(timezone.utc)
            }
            await db_analysis["meta_post_metrics"].update_one(
                {"post_id": post_id, "store_id": store_id},
                {"$set": metrics_to_store},
                upsert=True
            )
            return data
        except httpx.HTTPStatusError as e:
            raise handle_meta_api_error(e, f"get_{platform}_post_metrics", {"post_id": post_id, "page_id": page_id})
        except Exception as e:
            raise handle_meta_api_error(e, f"get_{platform}_post_metrics", {"post_id": post_id, "page_id": page_id})

@router.get("/{store_id}/posts/{post_id}/comments")
async def get_post_comments(
    store_id: str, post_id: str, limit: int = Query(25, ge=1, le=100),
    after_cursor: Optional[str] = Query(None, alias="after"),
    current_user: User = Depends(verify_user_can_access_store)
):
    post_doc = await db_analysis["meta_posts"].find_one({"id": post_id, "store_id": store_id})
    if not post_doc:
        raise HTTPException(status_code=404, detail=f"Post with ID {post_id} not found for store {store_id} to fetch comments.")

    page_id = post_doc.get("page_id")
    platform = post_doc.get("platform", "facebook")
    
    if not page_id:
        raise HTTPException(status_code=404, detail=f"Page ID not found for post {post_id}.")

    _, access_token, _ = await _get_page_info_and_token(store_id, page_id)

    params = {
        "access_token": access_token,
        "limit": limit,
        # For IG, fields could be id, text, timestamp, username, like_count, user{id,username,profile_picture_url}, replies{...}
        # For FB, fields could be id, message, created_time, from{id,name,picture}, attachment, likes.summary(true), comments.summary(true)
        "fields": "id,message,created_time,from{id,name,picture}" # Generic, refine per platform
    }
    if after_cursor:
        params["after"] = after_cursor

    # Endpoint is typically /post-id/comments for both, but check IG specific if it's /media-id/comments
    endpoint_path = f"{GRAPH_API_URL}/{post_id}/comments"
        
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching comments for {platform} post {post_id}, endpoint: {endpoint_path}")
            response = await client.get(endpoint_path, params=params)
            response.raise_for_status()
            data = response.json()

            # Store fetched comments
            if data.get("data"):
                comments_to_store = []
                for comment_data in data["data"]:
                    comment_data["store_id"] = store_id
                    comment_data["post_id"] = post_id
                    comment_data["page_id"] = page_id
                    comment_data["platform"] = platform
                    if "created_time" in comment_data and isinstance(comment_data["created_time"], str):
                         comment_data["created_time"] = datetime.fromisoformat(comment_data["created_time"].replace("Z", "+00:00"))
                    comments_to_store.append(comment_data)
                if comments_to_store:
                    await db_analysis["meta_comments"].insert_many(comments_to_store) # Consider upsert logic if re-fetching
            return data
        except httpx.HTTPStatusError as e:
            raise handle_meta_api_error(e, f"get_{platform}_post_comments", {"post_id": post_id})
        except Exception as e:
            raise handle_meta_api_error(e, f"get_{platform}_post_comments", {"post_id": post_id})

@router.get("/{store_id}/pages/{page_id}/audience")
async def get_page_audience(store_id: str, page_id: str, current_user: User = Depends(verify_user_can_access_store)) -> Dict[str, Any]:
    _, access_token, platform = await _get_page_info_and_token(store_id, page_id)

    results: Dict[str, Any] = {"platform": platform, "page_id": page_id, "store_id": store_id}
    metrics_to_fetch: Dict[str, str] = {} # metric_name: api_metric_string

    if platform == "instagram":
        # For Instagram, page_id is IG User ID. Audience metrics are typically on /insights edge.
        # e.g., audience_city, audience_country, audience_gender_age, audience_locale
        # These often require period=lifetime
        metrics_to_fetch = {
            "audience_city": "audience_city",
            "audience_country": "audience_country",
            "audience_gender_age": "audience_gender_age",
        }
        endpoint_path = f"{GRAPH_API_URL}/{page_id}/insights"
        params_base = {"access_token": access_token, "period": "lifetime"}
    else: # facebook
        metrics_to_fetch = {
            "page_fans_gender_age": "page_fans_gender_age",
            "page_fans_country": "page_fans_country",
            "page_fans_city": "page_fans_city",
            # "page_fans_locale": "page_fans_locale" # Example
        }
        endpoint_path = f"{GRAPH_API_URL}/{page_id}/insights"
        params_base = {"access_token": access_token, "period": "lifetime"} # FB insights are often period-based

    async with httpx.AsyncClient(timeout=45.0) as client:
        fetched_metrics_data = {}
        for display_name, api_metric in metrics_to_fetch.items():
            params = {**params_base, "metric": api_metric}
            try:
                logger.info(f"Fetching audience metric '{api_metric}' for {platform} page {page_id}")
                response = await client.get(endpoint_path, params=params)
                response.raise_for_status()
                metric_data = response.json()
                fetched_metrics_data[display_name] = metric_data.get("data", [])
            except httpx.HTTPStatusError as e:
                logger.error(f"Error fetching audience metric {api_metric} for {page_id}: {e}")
                # Continue fetching other metrics, or raise partial error
                fetched_metrics_data[display_name] = {"error": str(e), "detail": e.response.text if e.response else "No response"}
            except Exception as e:
                logger.error(f"Generic error fetching audience metric {api_metric} for {page_id}: {e}")
                fetched_metrics_data[display_name] = {"error": str(e)}
        
        results["audience_data"] = fetched_metrics_data
        
        # Store results
        await db_analysis["meta_audience_data"].update_one(
            {"page_id": page_id, "store_id": store_id, "platform": platform},
            {"$set": {"data": fetched_metrics_data, "last_fetched": datetime.now(timezone.utc)}},
            upsert=True
        )
    return results

@router.get("/{store_id}/ad-accounts/{account_id}/campaigns")
async def get_ad_account_campaigns(store_id: str, account_id: str, current_user: User = Depends(verify_user_can_access_store)):
    # Need user-level or system-user token that has access to the ad account.
    # This might not be a page token. For now, assume a generic token might work or _get_page_info_and_token
    # can provide a suitable token if the ad account is linked via a page.
    # This is a simplification. Robust ad account access token retrieval is needed.
    
    # Attempt to get a token from the first available page of the store. This is NOT robust for ad accounts.
    store_doc = await db_analysis["global_analysis"].find_one({"_id": store_id})
    access_token = None
    if store_doc and store_doc.get("meta_integration", {}).get("pages"):
        access_token = store_doc["meta_integration"]["pages"][0].get("access_token")

    if not access_token:
        raise HTTPException(status_code=401, detail="Could not retrieve a valid access token for ad account operations.")

    # Ensure account_id is prefixed with 'act_' if it's not.
    formatted_account_id = account_id if account_id.startswith("act_") else f"act_{account_id}"
    endpoint_path = f"{GRAPH_API_URL}/{formatted_account_id}/campaigns"
    
    params = {
        "access_token": access_token,
        "fields": "id,name,status,objective,start_time,stop_time,daily_budget,lifetime_budget,budget_remaining,buying_type,effective_status,configured_status,insights{spend,impressions,reach,clicks,conversions,ctr,cpc}", # Example fields
        "limit": 100 # Adjust as needed
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            logger.info(f"Fetching campaigns for ad account {formatted_account_id}")
            response = await client.get(endpoint_path, params=params)
            response.raise_for_status()
            data = response.json()

            # Store fetched campaigns
            if data.get("data"):
                campaigns_to_store = []
                for camp_data in data["data"]:
                    camp_data["ad_account_id"] = formatted_account_id
                    camp_data["store_id"] = store_id
                    # Convert date strings if necessary
                    for date_field in ["start_time", "stop_time"]:
                        if date_field in camp_data and isinstance(camp_data[date_field], str):
                            try:
                                camp_data[date_field] = datetime.fromisoformat(camp_data[date_field].replace("Z", "+00:00"))
                            except ValueError:
                                logger.warning(f"Invalid date format for {date_field} in campaign {camp_data.get('id')}")
                    campaigns_to_store.append(camp_data)
                if campaigns_to_store:
                    await db_analysis["meta_ad_campaigns"].insert_many(campaigns_to_store) # Or use update_many with upsert
            return data
        except httpx.HTTPStatusError as e:
            raise handle_meta_api_error(e, "get_ad_account_campaigns", {"ad_account_id": formatted_account_id})
        except Exception as e:
            raise handle_meta_api_error(e, "get_ad_account_campaigns", {"ad_account_id": formatted_account_id})

@router.get("/{store_id}/campaign-sales-correlation", response_class=JSONResponse)
async def get_campaign_sales_correlation(
    store_id: str, 
    since: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    until: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    try:
        from utils.serialization import serialize_mongo_doc
        
        logger.info(f"Fetching campaign sales correlation for store {store_id} (since: {since}, until: {until})")
        
        correlation_doc_list = await db_analysis["meta_sales_correlation"].find({"_id": store_id}).to_list(length=1)
        if not correlation_doc_list: 
            logger.warning(f"No correlation data found for store {store_id}, returning empty structure")
            return JSONResponse(content={"enhanced_campaigns": [], "sales_data": {"daily_sales": [], "product_sales": []}}, status_code=200)
        
        response_data = correlation_doc_list[0]
        response_data.pop('_id', None)
        
        # Ensure required structure exists
        if 'enhanced_campaigns' not in response_data:
            response_data['enhanced_campaigns'] = []
        if 'sales_data' not in response_data:
            response_data['sales_data'] = {"daily_sales": [], "product_sales": []}
        elif 'daily_sales' not in response_data['sales_data']:
            response_data['sales_data']['daily_sales'] = []
        
        # Apply date filtering if date range is provided
        if since or until:
            # Parse date range
            since_date = None
            until_date = None
            
            if since:
                try:
                    since_date = datetime.strptime(since, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                except ValueError:
                    logger.warning(f"Invalid since date format: {since}")
            
            if until:
                try:
                    until_date = datetime.strptime(until, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                    # Include the entire until date by setting time to end of day
                    until_date = until_date.replace(hour=23, minute=59, second=59)
                except ValueError:
                    logger.warning(f"Invalid until date format: {until}")
            
            # Filter enhanced campaigns by date range
            if since_date or until_date:
                filtered_campaigns = []
                for campaign in response_data.get('enhanced_campaigns', []):
                    campaign_start = None
                    campaign_end = None
                    
                    # Parse campaign dates
                    if 'start_time' in campaign:
                        try:
                            if isinstance(campaign['start_time'], str):
                                campaign_start = datetime.fromisoformat(campaign['start_time'].replace('Z', '+00:00'))
                            elif isinstance(campaign['start_time'], datetime):
                                campaign_start = campaign['start_time']
                        except (ValueError, TypeError):
                            pass
                    
                    if 'end_time' in campaign:
                        try:
                            if isinstance(campaign['end_time'], str):
                                campaign_end = datetime.fromisoformat(campaign['end_time'].replace('Z', '+00:00'))
                            elif isinstance(campaign['end_time'], datetime):
                                campaign_end = campaign['end_time']
                        except (ValueError, TypeError):
                            pass
                    
                    # Check if campaign overlaps with requested date range
                    include_campaign = True
                    
                    if since_date and campaign_end and campaign_end < since_date:
                        include_campaign = False
                    
                    if until_date and campaign_start and campaign_start > until_date:
                        include_campaign = False
                    
                    if include_campaign:
                        filtered_campaigns.append(campaign)
                
                response_data['enhanced_campaigns'] = filtered_campaigns
                logger.info(f"Filtered campaigns from {len(response_data.get('enhanced_campaigns', []))} to {len(filtered_campaigns)} based on date range")
            
            # Filter daily sales by date range
            if since_date or until_date:
                filtered_sales = []
                for sale in response_data.get('sales_data', {}).get('daily_sales', []):
                    sale_date = None
                    
                    # Parse sale date
                    if 'date' in sale:
                        try:
                            if isinstance(sale['date'], str):
                                sale_date = datetime.strptime(sale['date'], '%Y-%m-%d').replace(tzinfo=timezone.utc)
                            elif isinstance(sale['date'], datetime):
                                sale_date = sale['date']
                        except (ValueError, TypeError):
                            pass
                    
                    # Check if sale date is within requested range
                    include_sale = True
                    
                    if since_date and sale_date and sale_date < since_date:
                        include_sale = False
                    
                    if until_date and sale_date and sale_date > until_date:
                        include_sale = False
                    
                    if include_sale:
                        filtered_sales.append(sale)
                
                response_data['sales_data']['daily_sales'] = filtered_sales
                logger.info(f"Filtered daily sales to {len(filtered_sales)} records based on date range")
        
        # Serialize MongoDB document to handle datetime and ObjectId objects
        serialized_data = serialize_mongo_doc(response_data)
        
        logger.info(f"Successfully returning correlation data for store {store_id}: {len(serialized_data.get('enhanced_campaigns', []))} campaigns, {len(serialized_data.get('sales_data', {}).get('daily_sales', []))} daily sales records")
        
        return JSONResponse(content=serialized_data)
    except Exception as e:
        logger.error(f"Error in get_campaign_sales_correlation for store {store_id}: {str(e)}", exc_info=True)
        # Return empty structure on error to prevent frontend crashes
        return JSONResponse(content={"enhanced_campaigns": [], "sales_data": {"daily_sales": [], "product_sales": []}}, status_code=200)

@router.post("/{store_id}/update-sales-correlation", response_class=JSONResponse)
async def trigger_sales_correlation_update(store_id: str, background_tasks: BackgroundTasks, current_user: User = Depends(verify_user_can_access_store)):
    try:
        logger.info(f"Triggering sales correlation update for store {store_id} by user {current_user.email}")
        background_tasks.add_task(run_meta_sales_correlation_update, store_id)
        return JSONResponse(content={"message": f"Sales correlation update task scheduled for store {store_id}"}, status_code=status.HTTP_202_ACCEPTED)
    except Exception as e:
        raise handle_meta_api_error(e, "trigger_sales_correlation_update", {"store_id": store_id})

@router.get("/campaign/{campaign_id}/attributed-sales")
async def get_attributed_sales_for_campaign(campaign_id: str, store_id: str = Query(...), since: str = Query(...), until: str = Query(...), current_user: User = Depends(verify_user_can_access_store_query)):
    try:
        logger.info(f"Access granted for user {current_user.email} to fetch attributed sales for campaign {campaign_id}, store {store_id}")
        start_dt = datetime.strptime(since, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(until, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        store_correlation_doc = await db_analysis["meta_sales_correlation"].find_one({"_id": store_id})
        if not store_correlation_doc: raise HTTPException(status_code=404, detail=f"Sales correlation data not found for store {store_id}")
        campaign_data = next((c for c in store_correlation_doc.get("enhanced_campaigns", []) if c.get("id") == campaign_id), None)
        if not campaign_data: raise HTTPException(status_code=404, detail=f"Campaign {campaign_id} not found in store {store_id}'s correlation data.")
        daily_sales_points = campaign_data.get("sales_correlation", {}).get("correlation_data", [])
        attributed_sales_details = [dp for dp in daily_sales_points if "date" in dp and start_dt <= datetime.strptime(dp["date"], "%Y-%m-%d").replace(tzinfo=timezone.utc) <= end_dt]
        total_attributed_revenue = sum(p.get("revenue", 0) for p in attributed_sales_details)
        return JSONResponse(content={"campaign_id": campaign_id, "attributed_sales_revenue": total_attributed_revenue, "details": attributed_sales_details})
    except ValueError: raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD.")
    except Exception as e:
        raise handle_meta_api_error(e, "get_attributed_sales_for_campaign", {"campaign_id": campaign_id, "store_id": store_id})

# --- Sync Endpoints ---
@router.post("/sync/{store_id}")
async def trigger_meta_sync(store_id: str, current_user: User = Depends(verify_user_can_access_store)):
    try:
        logger.info(f"Manual Meta sync triggered for store {store_id} by user {current_user.email}")
        asyncio.create_task(sync_meta_data_for_chat(store_id))
        return {"message": f"Meta data synchronization started in background for store {store_id}"}
    except Exception as e:
        raise handle_meta_api_error(e, "trigger_meta_sync", {"store_id": store_id})

@router.get("/sync/status")
async def get_meta_sync_status_endpoint(current_user: User = Depends(get_current_active_user)):
    try:
        admin_emails_from_settings = getattr(settings, 'ADMIN_EMAILS', None)
        default_admin_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        admin_emails = admin_emails_from_settings if isinstance(admin_emails_from_settings, list) else default_admin_emails
        if current_user.email not in admin_emails and getattr(current_user, 'role', 'user') != "admin":
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only admin users can view sync status")
        from services.scheduled_tasks import meta_sync_task
        task_status = meta_sync_task.get_status()
        global_sync_metrics = get_sync_metrics()
        return {"task_status": task_status, "sync_metrics": global_sync_metrics}
    except Exception as e:
        raise handle_meta_api_error(e, "get_meta_sync_status")

@router.post("/pages")
async def save_meta_page(
    page_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save a Meta page to the database for the current user's store
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate required fields
        if not page_data.get("id"):
            raise HTTPException(status_code=400, detail="Page ID is required")
        
        # Prepare the page document
        page_doc = {
            "store_id": store_id,
            "id": page_data["id"],
            "name": page_data.get("name", ""),
            "category": page_data.get("category", ""),
            "access_token": page_data.get("access_token", ""),
            "platform": page_data.get("platform", "facebook"),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # Clear any token expiration flags when storing new token
        if page_doc.get("access_token"):
            page_doc["token_expired"] = False
            page_doc["token_error"] = None
        
        # Check if page already exists
        existing_page = await db_analysis["meta_pages"].find_one({
            "store_id": store_id,
            "id": page_data["id"]
        })
        
        if existing_page:
            # Update existing page
            page_doc["created_at"] = existing_page.get("created_at", page_doc["created_at"])
            await db_analysis["meta_pages"].update_one(
                {"store_id": store_id, "id": page_data["id"]},
                {"$set": page_doc}
            )
            logger.info(f"Updated Meta page {page_data['id']} for store {store_id}")
        else:
            # Insert new page
            await db_analysis["meta_pages"].insert_one(page_doc)
            logger.info(f"Saved new Meta page {page_data['id']} for store {store_id}")
        
        # Also update the global_analysis collection for compatibility
        store_doc = await db_analysis["global_analysis"].find_one({"_id": store_id})
        if store_doc:
            if "meta_integration" not in store_doc:
                store_doc["meta_integration"] = {"connected": True, "pages": []}
            
            # Update or add the page in the pages array
            pages = store_doc["meta_integration"].get("pages", [])
            page_found = False
            for i, p in enumerate(pages):
                if p.get("id") == page_data["id"]:
                    pages[i] = {k: v for k, v in page_data.items() if k != "access_token"}  # Don't store access_token in global_analysis
                    page_found = True
                    break
            
            if not page_found:
                pages.append({k: v for k, v in page_data.items() if k != "access_token"})
            
            store_doc["meta_integration"]["pages"] = pages
            store_doc["meta_integration"]["last_sync"] = datetime.now(timezone.utc)
            
            await db_analysis["global_analysis"].update_one(
                {"_id": store_id},
                {"$set": {"meta_integration": store_doc["meta_integration"]}}
            )
        
        return {"id": page_data["id"], "message": "Page saved successfully"}
        
    except Exception as e:
        logger.error(f"Error saving Meta page: {str(e)}")
        raise handle_meta_api_error(e, "save_meta_page", {"page_id": page_data.get("id"), "store_id": str(current_user.id_store)})


@router.get("/pages")
async def get_meta_pages(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all Meta pages for the current user's store
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get pages from meta_pages collection
        pages_cursor = db_analysis["meta_pages"].find({"store_id": store_id})
        pages = await pages_cursor.to_list(length=None)
        
        # Clean up the response
        cleaned_pages = []
        for page in pages:
            cleaned_page = {
                "id": page.get("id"),
                "name": page.get("name", ""),
                "category": page.get("category", ""),
                "platform": page.get("platform", "facebook"),
                "access_token": page.get("access_token", ""),  # Include access_token for frontend use
                "created_at": page.get("created_at"),
                "updated_at": page.get("updated_at")
            }
            cleaned_pages.append(cleaned_page)
        
        logger.info(f"Retrieved {len(cleaned_pages)} Meta pages for store {store_id}")
        return cleaned_pages
        
    except Exception as e:
        logger.error(f"Error retrieving Meta pages: {str(e)}")
        raise handle_meta_api_error(e, "get_meta_pages", {"store_id": str(current_user.id_store)})

@router.post("/instagram-ad-metrics")
async def save_instagram_ad_metrics(
    metrics_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save Instagram ad metrics to the database
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate required fields
        if not metrics_data.get("page_id"):
            raise HTTPException(status_code=400, detail="Page ID is required")
        
        # Prepare the metrics document
        metrics_doc = {
            "store_id": store_id,
            "page_id": metrics_data["page_id"],
            "platform": "instagram",
            "metrics": metrics_data.get("metrics", {}),
            "timestamp": datetime.now(timezone.utc),
            "data_source": "instagram_api"
        }
        
        # Save to meta_ad_metrics collection
        await db_analysis["meta_ad_metrics"].insert_one(metrics_doc)
        
        logger.info(f"Saved Instagram ad metrics for page {metrics_data['page_id']} in store {store_id}")
        
        return {"message": "Instagram ad metrics saved successfully", "page_id": metrics_data["page_id"]}
        
    except Exception as e:
        logger.error(f"Error saving Instagram ad metrics: {str(e)}")
        raise handle_meta_api_error(e, "save_instagram_ad_metrics", {
            "page_id": metrics_data.get("page_id"), 
            "store_id": str(current_user.id_store)
        })

@router.post("/post-metrics")
async def save_post_metrics(
    metrics_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save post metrics to the database
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate required fields
        metrics_list = metrics_data.get("metrics", [])
        if not metrics_list:
            raise HTTPException(status_code=400, detail="Metrics array is required")
        
        # Prepare metrics documents
        metrics_docs = []
        for metric in metrics_list:
            metric_doc = {
                "store_id": store_id,
                "post_id": metric.get("post_id"),
                "page_id": metric.get("page_id"),
                "metrics": metric,
                "timestamp": datetime.now(timezone.utc),
                "data_source": "meta_api"
            }
            metrics_docs.append(metric_doc)
        
        # Save to meta_post_metrics collection
        if metrics_docs:
            await db_analysis["meta_post_metrics"].insert_many(metrics_docs)
            
        logger.info(f"Saved {len(metrics_docs)} post metrics for store {store_id}")
        
        return {"message": f"Saved {len(metrics_docs)} post metrics successfully"}
        
    except Exception as e:
        logger.error(f"Error saving post metrics: {str(e)}")
        raise handle_meta_api_error(e, "save_post_metrics", {"store_id": str(current_user.id_store)})


@router.post("/comments")
async def save_comments(
    comments_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save comments to the database
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate required fields
        comments_list = comments_data.get("comments", [])
        if not comments_list:
            raise HTTPException(status_code=400, detail="Comments array is required")
        
        # Prepare comment documents
        comment_docs = []
        for comment in comments_list:
            comment_doc = {
                "store_id": store_id,
                "comment_id": comment.get("id"),
                "post_id": comment.get("post_id"),
                "page_id": comment.get("page_id"),
                "comment_data": comment,
                "timestamp": datetime.now(timezone.utc),
                "data_source": "meta_api"
            }
            comment_docs.append(comment_doc)
        
        # Save to meta_comments collection
        if comment_docs:
            await db_analysis["meta_comments"].insert_many(comment_docs)
            
        logger.info(f"Saved {len(comment_docs)} comments for store {store_id}")
        
        return {"message": f"Saved {len(comment_docs)} comments successfully"}
        
    except Exception as e:
        logger.error(f"Error saving comments: {str(e)}")
        raise handle_meta_api_error(e, "save_comments", {"store_id": str(current_user.id_store)})

@router.get("/pages/{page_id}/metrics/fallback")
async def get_fallback_metrics(
    page_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get cached/fallback metrics for a Meta page when API auth fails
    Uses the new Social Media API for more reliable data
    """
    try:
        store_id = str(current_user.id_store)
        
        # Return fallback data with warning message
        fallback_data = {
            "impressions": {"total": 0},
            "engagement": {"total": 0},
            "followers": {"total": 0},
            "views": {"total": 0},
            "lowDataWarning": True,
            "source": "cache_fallback"
        }
        if fallback_data["impressions"]["total"] == 0 and fallback_data["engagement"]["total"] == 0:
            # Try to get recent ad metrics
            recent_ad_metrics = await db_analysis["meta_ad_metrics"].find_one(
                {"page_id": page_id, "store_id": store_id},
                sort=[("timestamp", -1)]
            )
            
            if recent_ad_metrics and recent_ad_metrics.get("metrics"):
                metrics = recent_ad_metrics["metrics"]
                if isinstance(metrics, dict):
                    overview = metrics.get("overview", {})
                    fallback_data["impressions"]["total"] = overview.get("total_impressions", 0)
                    fallback_data["views"]["total"] = overview.get("total_reach", 0)
            
            # Try to get engagement data from post metrics
            post_metrics = await db_analysis["meta_post_metrics"].find(
                {"page_id": page_id, "store_id": store_id}
            ).limit(10).to_list(length=None)
            
            total_engagement = 0
            for post_metric in post_metrics:
                metrics = post_metric.get("metrics", {})
                total_engagement += metrics.get("likes_count", 0)
                total_engagement += metrics.get("comments_count", 0)
                total_engagement += metrics.get("shares_count", 0)
            
            fallback_data["engagement"]["total"] = total_engagement
            fallback_data["source"] = "cached_data"
        
        return fallback_data
        
    except Exception as e:
        logger.error(f"Error retrieving fallback metrics: {str(e)}")
        raise handle_meta_api_error(e, "get_fallback_metrics", {
            "page_id": page_id, 
            "store_id": str(current_user.id_store)
        })

@router.get("/pages/{page_id}/engagement-metrics")
async def get_engagement_metrics(
    page_id: str,
    since: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    until: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get engagement metrics for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get engagement metrics from database
        query: Dict[str, Any] = {"page_id": page_id, "store_id": store_id}
        if since or until:
            query["timestamp"] = {}
            if since:
                query["timestamp"]["$gte"] = datetime.strptime(since, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            if until:
                query["timestamp"]["$lte"] = datetime.strptime(until, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        
        engagement_data = await db_analysis["meta_engagement_metrics"].find(query).sort("timestamp", -1).to_list(length=100)
        
        # Aggregate the data
        total_likes = sum(item.get("likes", 0) for item in engagement_data)
        total_comments = sum(item.get("comments", 0) for item in engagement_data)
        total_shares = sum(item.get("shares", 0) for item in engagement_data)
        total_saves = sum(item.get("saves", 0) for item in engagement_data)
        
        return {
            "page_id": page_id,
            "total_engagement": total_likes + total_comments + total_shares + total_saves,
            "likes": total_likes,
            "comments": total_comments,
            "shares": total_shares,
            "saves": total_saves,
            "engagement_rate": 0,  # Calculate if follower data is available
            "period": {"since": since, "until": until}
        }
        
    except Exception as e:
        logger.error(f"Error retrieving engagement metrics: {str(e)}")
        raise handle_meta_api_error(e, "get_engagement_metrics", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.post("/pages/{page_id}/engagement-metrics")
async def save_engagement_metrics(
    page_id: str,
    metrics_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save engagement metrics for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Prepare the engagement metrics document
        engagement_doc = {
            "store_id": store_id,
            "page_id": page_id,
            "likes": metrics_data.get("likes", 0),
            "comments": metrics_data.get("comments", 0),
            "shares": metrics_data.get("shares", 0),
            "saves": metrics_data.get("saves", 0),
            "total_engagement": metrics_data.get("total_engagement", 0),
            "engagement_rate": metrics_data.get("engagement_rate", 0),
            "timestamp": datetime.now(timezone.utc),
            "period": metrics_data.get("period", {})
        }
        
        # Save to meta_engagement_metrics collection
        await db_analysis["meta_engagement_metrics"].insert_one(engagement_doc)
        
        logger.info(f"Saved engagement metrics for page {page_id} in store {store_id}")
        
        return {"message": "Engagement metrics saved successfully", "page_id": page_id}
        
    except Exception as e:
        logger.error(f"Error saving engagement metrics: {str(e)}")
        raise handle_meta_api_error(e, "save_engagement_metrics", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.get("/pages/{page_id}/follower-data")
async def get_follower_data(
    page_id: str,
    since: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    until: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get follower data for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get follower data from database
        query: Dict[str, Any] = {"page_id": page_id, "store_id": store_id}
        if since or until:
            query["timestamp"] = {}
            if since:
                query["timestamp"]["$gte"] = datetime.strptime(since, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            if until:
                query["timestamp"]["$lte"] = datetime.strptime(until, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        
        follower_data = await db_analysis["meta_follower_data"].find(query).sort("timestamp", -1).to_list(length=100)
        
        if not follower_data:
            return {
                "page_id": page_id,
                "total_followers": 0,
                "follower_growth": [],
                "period": {"since": since, "until": until}
            }
        
        # Get the most recent follower count
        latest_data = follower_data[0] if follower_data else {}
        total_followers = latest_data.get("total_followers", 0)
        
        # Calculate growth data
        follower_growth = []
        for data in follower_data:
            follower_growth.append({
                "date": data.get("timestamp", datetime.now(timezone.utc)).strftime("%Y-%m-%d"),
                "count": data.get("total_followers", 0),
                "new_followers": data.get("new_followers", 0),
                "unfollows": data.get("unfollows", 0)
            })
        
        return {
            "page_id": page_id,
            "total_followers": total_followers,
            "follower_growth": follower_growth,
            "period": {"since": since, "until": until}
        }
        
    except Exception as e:
        logger.error(f"Error retrieving follower data: {str(e)}")
        raise handle_meta_api_error(e, "get_follower_data", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.post("/pages/{page_id}/follower-data")
async def save_follower_data(
    page_id: str,
    follower_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save follower data for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Prepare the follower data document
        follower_doc = {
            "store_id": store_id,
            "page_id": page_id,
            "total_followers": follower_data.get("total_followers", 0),
            "new_followers": follower_data.get("new_followers", 0),
            "unfollows": follower_data.get("unfollows", 0),
            "net_growth": follower_data.get("net_growth", 0),
            "timestamp": datetime.now(timezone.utc),
            "data_source": follower_data.get("data_source", "meta_api")
        }
        
        # Save to meta_follower_data collection
        await db_analysis["meta_follower_data"].insert_one(follower_doc)
        
        logger.info(f"Saved follower data for page {page_id} in store {store_id}")
        
        return {"message": "Follower data saved successfully", "page_id": page_id}
        
    except Exception as e:
        logger.error(f"Error saving follower data: {str(e)}")
        raise handle_meta_api_error(e, "save_follower_data", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.get("/pages/{page_id}/follower-demographics")
async def get_follower_demographics(
    page_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get follower demographics for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get demographics data from database
        demographics_data = await db_analysis["meta_demographics"].find_one(
            {"page_id": page_id, "store_id": store_id},
            sort=[("timestamp", -1)]
        )
        
        if not demographics_data:
            return {
                "page_id": page_id,
                "demographics": {
                    "age_gender": [],
                    "countries": []
                }
            }
        
        return {
            "page_id": page_id,
            "demographics": demographics_data.get("demographics", {})
        }
        
    except Exception as e:
        logger.error(f"Error retrieving follower demographics: {str(e)}")
        raise handle_meta_api_error(e, "get_follower_demographics", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.post("/pages/{page_id}/follower-demographics")
async def save_follower_demographics(
    page_id: str,
    demographics_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save follower demographics for a specific page
    """
    try:
        store_id = str(current_user.id_store)
        
        # Prepare the demographics document
        demographics_doc = {
            "store_id": store_id,
            "page_id": page_id,
            "demographics": demographics_data.get("demographics", {}),
            "timestamp": datetime.now(timezone.utc),
            "data_source": demographics_data.get("data_source", "meta_api")
        }
        
        # Update or insert demographics data
        await db_analysis["meta_demographics"].update_one(
            {"page_id": page_id, "store_id": store_id},
            {"$set": demographics_doc},
            upsert=True
        )
        
        logger.info(f"Saved follower demographics for page {page_id} in store {store_id}")
        
        return {"message": "Follower demographics saved successfully", "page_id": page_id}
        
    except Exception as e:
        logger.error(f"Error saving follower demographics: {str(e)}")
        raise handle_meta_api_error(e, "save_follower_demographics", {"page_id": page_id, "store_id": str(current_user.id_store)})

@router.get("/business/{business_id}/ad-accounts")
async def get_business_ad_accounts(
    business_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get ad accounts for a specific business
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get ad accounts from database
        ad_accounts = await db_analysis["meta_ad_accounts"].find(
            {"business_id": business_id, "store_id": store_id}
        ).to_list(length=None)
        
        # Clean up the response
        cleaned_accounts = []
        for account in ad_accounts:
            cleaned_account = {
                "id": account.get("id"),
                "name": account.get("name", ""),
                "account_status": account.get("account_status", "UNKNOWN"),
                "currency": account.get("currency", "USD"),
                "timezone_name": account.get("timezone_name", ""),
                "business_id": account.get("business_id"),
                "created_at": account.get("created_at"),
                "updated_at": account.get("updated_at")
            }
            cleaned_accounts.append(cleaned_account)
        
        logger.info(f"Retrieved {len(cleaned_accounts)} ad accounts for business {business_id}")
        return cleaned_accounts
        
    except Exception as e:
        logger.error(f"Error retrieving ad accounts: {str(e)}")
        raise handle_meta_api_error(e, "get_business_ad_accounts", {"business_id": business_id, "store_id": str(current_user.id_store)})

@router.post("/business/{business_id}/ad-accounts")
async def save_business_ad_accounts(
    business_id: str,
    accounts_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Save ad accounts for a specific business
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate required fields
        accounts_list = accounts_data.get("accounts", [])
        if not accounts_list:
            raise HTTPException(status_code=400, detail="Accounts array is required")
        
        # Prepare account documents
        account_docs = []
        for account in accounts_list:
            account_doc = {
                "store_id": store_id,
                "business_id": business_id,
                "id": account.get("id"),
                "name": account.get("name", ""),
                "account_status": account.get("account_status", "UNKNOWN"),
                "currency": account.get("currency", "USD"),
                "timezone_name": account.get("timezone_name", ""),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "data_source": "meta_api"
            }
            account_docs.append(account_doc)
        
        # Save to meta_ad_accounts collection
        if account_docs:
            # Use upsert for each account to avoid duplicates
            for account_doc in account_docs:
                await db_analysis["meta_ad_accounts"].update_one(
                    {"id": account_doc["id"], "store_id": store_id},
                    {"$set": account_doc},
                    upsert=True
                )
            
        logger.info(f"Saved {len(account_docs)} ad accounts for business {business_id}")
        
        return {"message": f"Saved {len(account_docs)} ad accounts successfully"}
        
    except Exception as e:
        logger.error(f"Error saving ad accounts: {str(e)}")
        raise handle_meta_api_error(e, "save_business_ad_accounts", {"business_id": business_id, "store_id": str(current_user.id_store)})

@router.get("/ad-accounts/{account_id}")
async def get_ad_account_details(
    account_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get details for a specific ad account
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get ad account from database
        account = await db_analysis["meta_ad_accounts"].find_one(
            {"id": account_id, "store_id": store_id}
        )
        
        if not account:
            raise HTTPException(status_code=404, detail=f"Ad account {account_id} not found")
        
        # Clean up the response
        account_details = {
            "id": account.get("id"),
            "name": account.get("name", ""),
            "account_status": account.get("account_status", "UNKNOWN"),
            "currency": account.get("currency", "USD"),
            "timezone_name": account.get("timezone_name", ""),
            "business_id": account.get("business_id"),
            "created_at": account.get("created_at"),
            "updated_at": account.get("updated_at")
        }
        
        logger.info(f"Retrieved ad account details for {account_id}")
        return account_details
        
    except Exception as e:
        logger.error(f"Error retrieving ad account details: {str(e)}")
        raise handle_meta_api_error(e, "get_ad_account_details", {"account_id": account_id, "store_id": str(current_user.id_store)})

@router.put("/ad-accounts/{account_id}")
async def update_ad_account(
    account_id: str,
    account_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update details for a specific ad account
    """
    try:
        store_id = str(current_user.id_store)
        
        # Prepare update data
        update_data = {
            "name": account_data.get("name"),
            "account_status": account_data.get("account_status"),
            "currency": account_data.get("currency"),
            "timezone_name": account_data.get("timezone_name"),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # Remove None values
        update_data = {k: v for k, v in update_data.items() if v is not None}
        
        # Update the ad account
        result = await db_analysis["meta_ad_accounts"].update_one(
            {"id": account_id, "store_id": store_id},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail=f"Ad account {account_id} not found")
        
        logger.info(f"Updated ad account {account_id}")
        return {"message": "Ad account updated successfully", "account_id": account_id}
        
    except Exception as e:
        logger.error(f"Error updating ad account: {str(e)}")
        raise handle_meta_api_error(e, "update_ad_account", {"account_id": account_id, "store_id": str(current_user.id_store)})

@router.delete("/ad-accounts/{account_id}")
async def delete_ad_account(
    account_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a specific ad account
    """
    try:
        store_id = str(current_user.id_store)
        
        # Delete the ad account
        result = await db_analysis["meta_ad_accounts"].delete_one(
            {"id": account_id, "store_id": store_id}
        )
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail=f"Ad account {account_id} not found")
        
        logger.info(f"Deleted ad account {account_id}")
        return {"message": "Ad account deleted successfully", "account_id": account_id}
        
    except Exception as e:
        logger.error(f"Error deleting ad account: {str(e)}")
        raise handle_meta_api_error(e, "delete_ad_account", {"account_id": account_id, "store_id": str(current_user.id_store)})

# === Enhanced Instagram Business Account Endpoints ===

@router.post("/connect-instagram")
async def connect_instagram_business_accounts(
    facebook_pages: List[Dict[str, Any]] = Body(...),
    user_access_token: str = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Connect and store Instagram Business Accounts from Facebook Pages
    
    This endpoint properly detects Instagram Business Accounts connected to Facebook Pages
    and stores them with the correct access tokens for data fetching.
    """
    try:
        store_id = str(current_user.id_store)
        
        logger.info(f"Connecting Instagram Business Accounts for store {store_id}")
        
        # Detect and store Instagram accounts
        instagram_accounts = await InstagramBusinessService.detect_and_store_instagram_accounts(
            store_id, facebook_pages, user_access_token
        )
        
        return {
            "success": True,
            "instagram_accounts": len(instagram_accounts),
            "accounts": instagram_accounts,
            "store_id": store_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error connecting Instagram Business Accounts: {str(e)}")
        raise handle_meta_api_error(e, "connect_instagram", {"store_id": str(current_user.id_store)})

@router.get("/instagram-accounts")
async def get_instagram_accounts(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all Instagram Business Accounts for the current store
    """
    try:
        store_id = str(current_user.id_store)
        
        # Get Instagram accounts from database
        accounts = await InstagramBusinessService.get_instagram_accounts_for_store(store_id)
        
        return {
            "accounts": accounts,
            "count": len(accounts),
            "store_id": store_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting Instagram accounts: {str(e)}")
        raise handle_meta_api_error(e, "get_instagram_accounts", {"store_id": str(current_user.id_store)})

@router.get("/instagram/{account_id}/insights")
async def get_instagram_insights_enhanced(
    account_id: str,
    time_range: str = Query("30d", description="Time range: 7d, 30d, 90d"),
    metrics: Optional[str] = Query(None, description="Comma-separated list of metrics"),
    use_cache: bool = Query(True, description="Use cached data if available"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get Instagram insights with proper authentication and error handling
    
    This endpoint uses the enhanced Instagram Business Service to fetch insights
    with proper token validation and permission checking.
    """
    try:
        store_id = str(current_user.id_store)
        
        # Parse metrics if provided
        metrics_list = None
        if metrics:
            metrics_list = [m.strip() for m in metrics.split(",")]
        
        # Get insights using the enhanced service
        insights_data = await InstagramBusinessService.get_instagram_insights(
            account_id, store_id, metrics_list, time_range, use_cache
        )
        
        return insights_data
        
    except Exception as e:
        logger.error(f"Error getting Instagram insights: {str(e)}")
        raise handle_meta_api_error(e, "get_instagram_insights_enhanced", {
            "account_id": account_id, 
            "store_id": str(current_user.id_store)
        })

@router.get("/instagram/{account_id}/profile")
async def get_instagram_profile_enhanced(
    account_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get Instagram profile information with proper authentication
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate account access
        is_valid, error_msg, account = await InstagramBusinessService.validate_instagram_account_access(
            account_id, store_id
        )
        
        if not is_valid:
            raise HTTPException(status_code=404, detail=error_msg)
        
        # Get enhanced profile data from database
        profile_data = {
            "id": account["id"],
            "username": account.get("username"),
            "name": account.get("name"),
            "biography": account.get("biography", ""),
            "followers_count": account.get("followers_count", 0),
            "follows_count": account.get("follows_count", 0),
            "media_count": account.get("media_count", 0),
            "profile_picture_url": account.get("profile_picture_url"),
            "website": account.get("website"),
            "platform": "instagram",
            "account_type": "business",
            "parent_page_id": account.get("parent_page_id"),
            "parent_page_name": account.get("parent_page_name"),
            "last_updated": account.get("last_updated")
        }
        
        return {
            "data": profile_data,
            "account_id": account_id,
            "store_id": store_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Instagram profile: {str(e)}")
        raise handle_meta_api_error(e, "get_instagram_profile_enhanced", {
            "account_id": account_id, 
            "store_id": str(current_user.id_store)
        })

@router.post("/instagram/refresh-tokens")
async def refresh_instagram_tokens(
    current_user: User = Depends(get_current_active_user)
):
    """
    Refresh access tokens for all Instagram accounts
    """
    try:
        store_id = str(current_user.id_store)
        
        # Refresh tokens for all accounts
        refresh_summary = await InstagramBusinessService.refresh_instagram_account_tokens(store_id)
        
        return refresh_summary
        
    except Exception as e:
        logger.error(f"Error refreshing Instagram tokens: {str(e)}")
        raise handle_meta_api_error(e, "refresh_instagram_tokens", {"store_id": str(current_user.id_store)})

@router.post("/instagram/cleanup")
async def cleanup_instagram_accounts(
    current_user: User = Depends(get_current_active_user)
):
    """
    Clean up invalid Instagram accounts
    """
    try:
        store_id = str(current_user.id_store)
        
        # Clean up invalid accounts
        cleanup_summary = await InstagramBusinessService.cleanup_invalid_accounts(store_id)
        
        return cleanup_summary
        
    except Exception as e:
        logger.error(f"Error cleaning up Instagram accounts: {str(e)}")
        raise handle_meta_api_error(e, "cleanup_instagram_accounts", {"store_id": str(current_user.id_store)})

@router.get("/instagram/{account_id}/validate")
async def validate_instagram_account(
    account_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate Instagram account access and permissions
    """
    try:
        store_id = str(current_user.id_store)
        
        # Validate account access
        is_valid, error_msg, account = await InstagramBusinessService.validate_instagram_account_access(
            account_id, store_id
        )
        
        return {
            "valid": is_valid,
            "error": error_msg,
            "account_id": account_id,
            "store_id": store_id,
            "account_exists": account is not None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error validating Instagram account: {str(e)}")
        raise handle_meta_api_error(e, "validate_instagram_account", {
            "account_id": account_id, 
            "store_id": str(current_user.id_store)
        })

# Daily Metrics Endpoints
@router.post("/{store_id}/daily-metrics/store")
async def store_daily_metrics(
    store_id: str,
    metrics_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Store daily metrics for Instagram/Facebook pages
    
    Expected format:
    {
        "page_id": "*********",
        "platform": "instagram",
        "metrics": {
            "followers": [{"date": "2025-07-07", "value": 8211, "metadata": {}}],
            "reach": [{"date": "2025-07-07", "value": 1969, "metadata": {}}],
            "engagement": [{"date": "2025-07-07", "value": 18, "metadata": {}}],
            "profile_views": [{"date": "2025-07-07", "value": 177, "metadata": {}}]
        }
    }
    """
    try:
        from services.meta_daily_metrics import MetaDailyMetricsService
        
        page_id = metrics_data.get("page_id")
        platform = metrics_data.get("platform", "instagram")
        metrics = metrics_data.get("metrics", {})
        
        if not page_id:
            raise HTTPException(status_code=400, detail="page_id is required")
        
        # Convert date strings to datetime objects
        processed_metrics = {}
        for metric_type, daily_values in metrics.items():
            processed_daily_values = []
            for daily_data in daily_values:
                date_str = daily_data['date']
                if isinstance(date_str, str):
                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                else:
                    date_obj = date_str
                
                processed_daily_values.append({
                    'date': date_obj,
                    'value': daily_data['value'],
                    'metadata': daily_data.get('metadata', {})
                })
            processed_metrics[metric_type] = processed_daily_values
        
        success = await MetaDailyMetricsService.store_daily_metrics_batch(
            store_id, page_id, platform, processed_metrics
        )
        
        if success:
            return {"message": "Daily metrics stored successfully", "metrics_count": len(metrics)}
        else:
            raise HTTPException(status_code=500, detail="Failed to store daily metrics")
        
    except Exception as e:
        logger.error(f"Error storing daily metrics: {str(e)}")
        raise handle_meta_api_error(e, "store_daily_metrics", {"store_id": store_id})

@router.get("/{store_id}/daily-metrics/{page_id}")
async def get_daily_metrics(
    store_id: str,
    page_id: str,
    platform: str = Query("instagram", description="Platform (instagram/facebook)"),
    metric_type: Optional[str] = Query(None, description="Specific metric type"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    days: int = Query(30, description="Number of days to retrieve (if dates not specified)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Retrieve daily metrics for a specific page and date range
    """
    try:
        from services.meta_daily_metrics import MetaDailyMetricsService
        
        # Calculate date range
        if start_date and end_date:
            start_dt = datetime.fromisoformat(start_date).replace(tzinfo=timezone.utc)
            end_dt = datetime.fromisoformat(end_date).replace(tzinfo=timezone.utc)
        else:
            end_dt = datetime.now(timezone.utc)
            start_dt = end_dt - timedelta(days=days)
        
        if metric_type:
            # Get specific metric
            metrics = await MetaDailyMetricsService.get_daily_metrics(
                store_id, page_id, platform, metric_type, start_dt, end_dt
            )
            result = {metric_type: metrics}
        else:
            # Get all metrics
            result = await MetaDailyMetricsService.get_all_metrics_for_period(
                store_id, page_id, platform, start_dt, end_dt
            )
        
        return {
            "store_id": store_id,
            "page_id": page_id,
            "platform": platform,
            "date_range": {
                "start": start_dt.isoformat(),
                "end": end_dt.isoformat()
            },
            "metrics": result
        }
        
    except Exception as e:
        logger.error(f"Error retrieving daily metrics: {str(e)}")
        raise handle_meta_api_error(e, "get_daily_metrics", {
            "store_id": store_id, 
            "page_id": page_id
        })

@router.get("/{store_id}/daily-metrics/{page_id}/trends")
async def get_metric_trends(
    store_id: str,
    page_id: str,
    platform: str = Query("instagram", description="Platform (instagram/facebook)"),
    metric_type: str = Query(..., description="Metric type"),
    days: int = Query(30, description="Number of days to analyze"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Get trend analysis for a specific metric
    """
    try:
        from services.meta_daily_metrics import MetaDailyMetricsService
        
        trends = await MetaDailyMetricsService.calculate_metric_trends(
            store_id, page_id, platform, metric_type, days
        )
        
        return {
            "store_id": store_id,
            "page_id": page_id,
            "platform": platform,
            "trends": trends
        }
        
    except Exception as e:
        logger.error(f"Error calculating metric trends: {str(e)}")
        raise handle_meta_api_error(e, "get_metric_trends", {
            "store_id": store_id, 
            "page_id": page_id
        })

@router.get("/{store_id}/facebook-media-insights")
async def get_facebook_media_insights(
    store_id: str,
    page_id: str = Query(..., description="Facebook page ID"),
    time_range: str = Query("30d", description="Time range (7d, 30d, lifetime)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """
    Get Facebook page insights using media-level API for comprehensive data
    """
    try:
        logger.info(f"Getting Facebook media-level insights for page {page_id}, time range: {time_range}")
        
        # Get page info and access token
        page_doc = await db_analysis["meta_pages"].find_one({
            "id": page_id, 
            "store_id": store_id,
            "platform": "facebook"
        })
        if not page_doc:
            raise HTTPException(status_code=404, detail=f"Facebook page {page_id} not found for store {store_id}")
        
        # Check if token is expired
        if page_doc.get("token_expired"):
            logger.info(f"Facebook page {page_id} token already marked as expired")
            return {
                "impressions": 0,
                "reach": 0,
                "engagement": 0,
                "page_views": 0,
                "followers": 0,
                "error": "Facebook session expired - please reconnect your Facebook account",
                "requires_reconnect": True
            }
        
        access_token = page_doc.get("access_token")
        if not access_token:
            raise HTTPException(status_code=400, detail="No access token found for Facebook page")
        
        # Calculate date range
        until_date = datetime.now()
        if time_range == "7d":
            since_date = until_date - timedelta(days=7)
        elif time_range == "30d":
            since_date = until_date - timedelta(days=30)
        elif time_range == "lifetime":
            since_date = until_date - timedelta(days=365*2)  # 2 years for comprehensive data
        else:
            since_date = until_date - timedelta(days=30)
            
        logger.info(f"Facebook media insights date range: {since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}")
        
        # Initialize metrics
        total_impressions = 0
        total_reach = 0
        total_engagement = 0
        total_page_views = 0
        media_count = 0
        posts_processed = 0
        posts_in_range = 0
        
        async with httpx.AsyncClient(timeout=180.0) as client:
            # Step 1: Get Facebook page posts
            posts_url = f"https://graph.facebook.com/v23.0/{page_id}/posts"
            posts_params = {
                "fields": "id,message,created_time,reactions.summary(total_count),comments.summary(total_count),shares",
                "access_token": access_token,
                "limit": 500  # Get more posts for comprehensive data
            }
            
            logger.info(f"Fetching Facebook posts from: {posts_url}")
            posts_response = await client.get(posts_url, params=posts_params)
            
            if posts_response.status_code == 200:
                posts_data = posts_response.json()
                posts = posts_data.get("data", [])
                
                logger.info(f"Found {len(posts)} Facebook posts")
                
                # Step 2: Process each post and get insights
                for post in posts:
                    post_id = post.get("id")
                    created_time = post.get("created_time", "")
                    
                    # Check if post is within time range
                    if created_time:
                        try:
                            post_date = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                            # Remove timezone info for comparison
                            post_date_naive = post_date.replace(tzinfo=None)
                            if post_date_naive < since_date:
                                continue  # Skip older posts
                            posts_in_range += 1
                        except Exception as e:
                            logger.warning(f"Failed to parse Facebook post date {created_time}: {e}")
                            posts_in_range += 1  # Include if parsing fails
                    else:
                        posts_in_range += 1  # Include if no timestamp
                    
                    # Get basic engagement from post data
                    reactions = post.get("reactions", {}).get("summary", {}).get("total_count", 0) if post.get("reactions") else 0
                    comments = post.get("comments", {}).get("summary", {}).get("total_count", 0) if post.get("comments") else 0
                    shares = post.get("shares", {}).get("count", 0) if post.get("shares") else 0
                    
                    post_engagement = reactions + comments + shares
                    total_engagement += post_engagement
                    
                    # Try to get post insights for impressions and reach
                    try:
                        insights_url = f"https://graph.facebook.com/v23.0/{post_id}/insights"
                        insights_params = {
                            "metric": "post_impressions,post_reach",
                            "access_token": access_token
                        }
                        
                        insights_response = await client.get(insights_url, params=insights_params, timeout=10.0)
                        if insights_response.status_code == 200:
                            insights_data = insights_response.json()
                            insights_list = insights_data.get("data", [])
                            
                            for insight in insights_list:
                                metric_name = insight.get("name")
                                values = insight.get("values", [])
                                if values and len(values) > 0:
                                    value = values[0].get("value", 0)
                                    
                                    if metric_name == "post_impressions":
                                        total_impressions += value
                                    elif metric_name == "post_reach":
                                        total_reach += value
                        
                    except Exception as e:
                        logger.debug(f"Could not get insights for post {post_id}: {e}")
                        # Continue without insights for this post
                    
                    posts_processed += 1
                    media_count += 1
                
                logger.info(f"Facebook media processing results for {time_range}: {posts_in_range} posts in range, {posts_processed} total processed")
                
                # Get page-level metrics (followers, page views)
                try:
                    page_insights_url = f"https://graph.facebook.com/v23.0/{page_id}/insights"
                    page_params = {
                        "metric": "page_fans,page_views_total",
                        "access_token": access_token
                    }
                    
                    page_response = await client.get(page_insights_url, params=page_params)
                    if page_response.status_code == 200:
                        page_data = page_response.json()
                        page_insights = page_data.get("data", [])
                        
                        for insight in page_insights:
                            metric_name = insight.get("name")
                            values = insight.get("values", [])
                            if values and len(values) > 0:
                                value = values[0].get("value", 0)
                                
                                if metric_name == "page_fans":
                                    followers = value
                                elif metric_name == "page_views_total":
                                    total_page_views = value
                    
                except Exception as e:
                    logger.warning(f"Could not get page-level insights: {e}")
                    followers = page_doc.get("followers_count", 0)  # Fallback to cached value
                
                logger.info(f"Facebook media-level results - Impressions: {total_impressions}, Reach: {total_reach}, "
                           f"Engagement: {total_engagement}, Page Views: {total_page_views}, Posts: {media_count}")
                
                return {
                    "impressions": total_impressions,
                    "reach": total_reach,
                    "engagement": total_engagement,
                    "page_views": total_page_views,
                    "followers": followers if 'followers' in locals() else page_doc.get("followers_count", 0),
                    "media_count": media_count,
                    "source": "facebook_media_level_api",
                    "time_range": time_range,
                    "date_range": {
                        "since": since_date.strftime('%Y-%m-%d'),
                        "until": until_date.strftime('%Y-%m-%d')
                    },
                    "posts_in_range": posts_in_range,
                    "posts_processed": posts_processed
                }
            
            else:
                logger.error(f"Failed to fetch Facebook posts: {posts_response.status_code}")
                raise HTTPException(status_code=400, detail="Failed to fetch Facebook posts")
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Facebook media insights: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Facebook media insights: {str(e)}")