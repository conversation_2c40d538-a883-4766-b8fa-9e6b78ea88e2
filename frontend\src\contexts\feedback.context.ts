import { createContext, useContext } from 'react';

export interface FeedbackContextProps {
  isFeedbackModalOpen: boolean;
  openFeedbackModal: () => void;
  closeFeedbackModal: () => void;
  openFeedbackModalForLogout: () => Promise<void>;
  isLogoutIntent: boolean;
}

export const FeedbackContext = createContext<FeedbackContextProps | undefined>(undefined);

// Custom hook to use the FeedbackContext
export const useFeedback = (): FeedbackContextProps => {
  const context = useContext(FeedbackContext);
  if (context === undefined) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
}; 