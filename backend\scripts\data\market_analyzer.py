import os
import time
import json
import logging
import argparse
import numpy as np
import requests
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Set, Union
from dotenv import load_dotenv
from pymongo import MongoClient
from openai import OpenAI
from bson import ObjectId
from bs4 import BeautifulSoup
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.tag import pos_tag
from nltk.chunk import ne_chunk
from tenacity import retry, stop_after_attempt, wait_exponential
from cachetools import TTLCache, cached
import re
from bson.binary import Binary
import base64
import multiprocessing
from multiprocessing import Pool
from functools import partial
from config.settings import get_settings
settings = get_settings()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('market_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize NLTK components
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('taggers/averaged_perceptron_tagger')
    nltk.data.find('chunkers/maxent_ne_chunker')
    nltk.data.find('corpora/words')
except LookupError:
    logger.info("Downloading required NLTK data...")
    nltk.download('punkt')
    nltk.download('averaged_perceptron_tagger')
    nltk.download('maxent_ne_chunker')
    nltk.download('words')

# Initialize caches
CACHE_TTL = 3600  # Cache time to live in seconds
product_cache = TTLCache(maxsize=1000, ttl=CACHE_TTL)
price_cache = TTLCache(maxsize=1000, ttl=CACHE_TTL)
feature_cache = TTLCache(maxsize=1000, ttl=CACHE_TTL)
analysis_cache = TTLCache(maxsize=1000, ttl=CACHE_TTL)

class MongoJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for MongoDB documents"""
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(obj, bytes):
            return base64.b64encode(obj).decode('utf-8')
        if isinstance(obj, Binary):
            return base64.b64encode(bytes(obj)).decode('utf-8')
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super().default(obj)

class MarketAnalyzer:
    def __init__(self):
        """Initialize the market analyzer with necessary connections"""
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        
        # Initialize analysis collection name
        self.analysis_collection = 'global_analysis'
        
        # Then load config and setup connections
        self.load_config()
        self.setup_connections()
        self.SIMILARITY_THRESHOLD = 0.75
        self.MAX_SIMILAR_DOCS = 3
        
        # Initialize caches as instance variables
        self.product_cache = TTLCache(maxsize=1000, ttl=3600)  # 1 hour TTL
        self.price_cache = TTLCache(maxsize=1000, ttl=3600)
        self.feature_cache = TTLCache(maxsize=1000, ttl=3600)
        self.analysis_cache = TTLCache(maxsize=1000, ttl=3600)
        
        # Map generic types to specific analysis collection names in D-Unit-AnalysisGPT
        self.analysis_collection_map = {
            'products': 'product_details_cache',
            'customers': 'store_customers_cache',
            'stores': 'active_stores_cache',
            'activity': 'store_activity_metrics'
        }
        
        # Other instance variables
        self.batch_size = 10
        self.analysis_queue = []
        self.analysis_results = {}
        
        # Store IDs to analyze (empty means analyze all)
        self.target_store_ids = set()
        
        # Load platform reference data (shipping methods, active FAQs)
        self.platform_reference_data = None
        try:
            ref_collection = self.analysis_db['platform_reference_data']
            ref_doc = ref_collection.find_one({'_id': 'reference'})
            if ref_doc:
                self.platform_reference_data = ref_doc
                self.logger.info('Loaded platform reference data (shipping methods, active FAQs)')
            else:
                self.logger.warning('No platform reference data found in MongoDB. Run the sync script.')
        except Exception as e:
            self.logger.error(f'Error loading platform reference data: {str(e)}')

    def debug_store_data_structure(self, store_id: str):
        """Debug method to inspect actual data structure for a store"""
        try:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"DEBUGGING DATA STRUCTURE FOR STORE {store_id}")
            self.logger.info(f"{'='*60}")
            
            # Check product cache structure
            product_cache = self.analysis_db['product_details_cache'].find_one({'_id': store_id})
            if product_cache and 'products' in product_cache:
                products = product_cache['products']
                self.logger.info(f"\nPRODUCT CACHE: Found {len(products)} products")
                
                if products:
                    # Sample first 3 products
                    for i, product in enumerate(products[:3]):
                        self.logger.info(f"\nProduct {i+1} structure:")
                        self.logger.info(f"  Keys: {list(product.keys())}")
                        
                        # Check for current_price (MongoDB field name)
                        if 'current_price' in product:
                            self.logger.info(f"  current_price: {product['current_price']}")
                        else:
                            self.logger.warning(f"  current_price field NOT FOUND")
                        
                        # Check variations
                        if 'variations' in product and product['variations']:
                            self.logger.info(f"  Variations: {len(product['variations'])} found")
                            if product['variations'][0].get('variation_price'):
                                self.logger.info(f"  First variation_price: {product['variations'][0]['variation_price']}")
            
            # Check active store social media
            active_store = self.analysis_db['active_stores_cache'].find_one({'_id': store_id})
            if active_store:
                self.logger.info(f"\nACTIVE STORE CACHE:")
                if 'social_media' in active_store:
                    self.logger.info(f"  Social media data: {active_store['social_media']}")
                else:
                    self.logger.info(f"  No social_media field found")
            
            # Check Meta integration
            self.logger.info(f"\nMETA INTEGRATION DATA:")
            meta_collections = {
                'meta_pages': 'Facebook/Instagram Pages',
                'meta_posts': 'Social Media Posts',
                'meta_ad_campaigns': 'Ad Campaigns',
                'meta_ad_metrics': 'Ad Performance Metrics'
            }
            
            for collection, description in meta_collections.items():
                count = self.analysis_db[collection].count_documents({'store_id': store_id})
                self.logger.info(f"  {description}: {count} documents")
                
                if count > 0 and collection == 'meta_ad_campaigns':
                    active = self.analysis_db[collection].count_documents({
                        'store_id': store_id,
                        'status': 'ACTIVE'
                    })
                    self.logger.info(f"    - Active campaigns: {active}")
            
            self.logger.info(f"\n{'='*60}\n")
            
        except Exception as e:
            self.logger.error(f"Error in debug_store_data_structure: {str(e)}")

    def get_all_shipping_methods(self):
        if self.platform_reference_data and 'shipping_methods' in self.platform_reference_data:
            return self.platform_reference_data['shipping_methods']
        return []

    def get_active_faqs(self):
        if self.platform_reference_data and 'faqs_active' in self.platform_reference_data:
            return self.platform_reference_data['faqs_active']
        return []

    def load_config(self):
        """Load configuration from environment variables"""
        load_dotenv()
        self.mongodb_uri = os.getenv('MONGODB_CONNECTION')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if not self.mongodb_uri or not self.openai_api_key:
            raise ValueError("Missing required environment variables")
            
        self.logger.info(f"Using MongoDB URI: {self.mongodb_uri}")
        self.logger.info(f"OpenAI API Key provided: {'Yes' if self.openai_api_key else 'No'}")

    def setup_connections(self):
        """Setup MongoDB and OpenAI connections"""
        try:
            self.client = MongoClient(self.mongodb_uri)
            # self.embeddings_db = self.client['D-Unit-Embeddings']  # Removed: all embeddings now in analysis_db
            self.analysis_db = self.client['D-Unit-AnalysisGPT']
            self.openai_client = OpenAI(api_key=self.openai_api_key)
            
            # Initialize collections if they don't exist
            self._initialize_collections()
            
        except Exception as e:
            self.logger.error(f"Error setting up connections: {str(e)}")
            raise

    def _initialize_collections(self):
        """Initialize all required collections if they don't exist"""
        try:
            # Analysis/cache embedding collections in D-Unit-AnalysisGPT
            embeddings_collections = [
                'active_stores_cache_embeddings',
                'product_details_cache_embeddings',
                'store_customers_cache_embeddings',
                'store_activity_metrics_embeddings',
                'meta_pages_embeddings',
                'meta_posts_embeddings',
                'meta_post_metrics_embeddings',
                'meta_comments_embeddings',
                'meta_followers_embeddings',
                'meta_demographics_embeddings',
                'meta_ad_campaigns_embeddings',
                'meta_ad_metrics_embeddings',
                'meta_insights_embeddings',
                'meta_sales_correlation_embeddings',
                'meta_chat_context_embeddings'
            ]
            for collection_name in embeddings_collections:
                if collection_name not in self.analysis_db.list_collection_names():
                    self.logger.info(f"Creating collection: {collection_name} in D-Unit-AnalysisGPT")
                    self.analysis_db.create_collection(collection_name)
                    self.analysis_db[collection_name].create_index([("e", 1)])
                    self.analysis_db[collection_name].create_index([("_id", 1)])
                    self.analysis_db[collection_name].create_index([("m.type", 1)])
                    self.analysis_db[collection_name].create_index([("m.active", 1)])
                    self.analysis_db[collection_name].create_index([("m.created_at", -1)])
            self.logger.info("All required collections and indexes have been initialized")
        except Exception as e:
            self.logger.error(f"Error initializing collections: {str(e)}")
            raise

    def _validate_database_structure(self) -> bool:
        """Validate that all required collections exist in D-Unit-AnalysisGPT database."""
        try:
            required_collections = {
                # Core analysis collections
                'active_stores_cache',
                'store_activity_metrics',
                'product_details_cache',
                'store_customers_cache',
                'store_chats',
                
                # Meta integration collections
                'meta_pages',
                'meta_posts',
                'meta_comments',
                'meta_followers',
                'meta_demographics',
                'meta_insights',
                'meta_ad_accounts',
                'meta_ad_campaigns',
                'meta_ad_metrics',
                'meta_post_metrics',
                'meta_sales_correlation',
                'meta_chat_context',
                
                # Embedding collections
                'active_stores_cache_embeddings',
                'store_activity_metrics_embeddings',
                'product_details_cache_embeddings',
                'store_customers_cache_embeddings',
                'store_chats_embeddings',
                'meta_pages_embeddings',
                'meta_posts_embeddings',
                'meta_comments_embeddings',
                'meta_followers_embeddings',
                'meta_demographics_embeddings',
                'meta_insights_embeddings',
                'meta_ad_accounts_embeddings',
                'meta_ad_campaigns_embeddings',
                'meta_ad_metrics_embeddings',
                'meta_post_metrics_embeddings',
                'meta_sales_correlation_embeddings',
                'meta_chat_context_embeddings'
            }
            
            existing_collections = set(self.analysis_db.list_collection_names())
            missing_collections = required_collections - existing_collections
            
            if missing_collections:
                logger.error(f"Missing required collections in D-Unit-AnalysisGPT: {missing_collections}")
                return False
                
            logger.info("All required collections present in D-Unit-AnalysisGPT database")
            return True
            
        except Exception as e:
            logger.error(f"Error validating database structure: {str(e)}")
            return False

    def cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

    def _binary_to_array(self, binary_embedding: Union[Binary, bytes, str]) -> np.ndarray:
        """Convert binary embedding back to numpy array"""
        try:
            binary_data: bytes
            if isinstance(binary_embedding, str):
                binary_data = base64.b64decode(binary_embedding)
            else:
                binary_data = binary_embedding

            # Convert bytes to numpy array
            array_data = np.frombuffer(binary_data, dtype=np.float16)
            return array_data.reshape(-1)  # Ensure 1D array
        except Exception as e:
            self.logger.error(f"Error converting binary to array: {str(e)}")
            return np.zeros(1536, dtype=np.float16)  # Return zero vector of correct size

    def find_similar_documents(self, embedding: np.ndarray, collection_type: str, limit: int = 5) -> List[Dict]:
        """Find similar documents using vector similarity search in new *_embeddings collections"""
        try:
            # If embedding is binary, convert it to array
            if isinstance(embedding, (Binary, bytes, str)):
                embedding = self._binary_to_array(embedding)
            if not isinstance(embedding, np.ndarray):
                embedding = np.array(embedding, dtype=np.float16)
            # Map collection_type to new *_embeddings collection
            collection_map = {
                'stores': 'active_stores_cache_embeddings',
                'products': 'product_details_cache_embeddings',
                'customers': 'store_customers_cache_embeddings',
                'store_activity': 'store_activity_metrics_embeddings',
                'meta_pages': 'meta_pages_embeddings',
                'meta_posts': 'meta_posts_embeddings',
                'meta_post_metrics': 'meta_post_metrics_embeddings',
                'meta_comments': 'meta_comments_embeddings',
                'meta_followers': 'meta_followers_embeddings',
                'meta_demographics': 'meta_demographics_embeddings',
                'meta_ad_campaigns': 'meta_ad_campaigns_embeddings',
                'meta_ad_metrics': 'meta_ad_metrics_embeddings',
                'meta_insights': 'meta_insights_embeddings',
                'meta_sales_correlation': 'meta_sales_correlation_embeddings',
                'meta_chat_context': 'meta_chat_context_embeddings',
            }
            embeddings_collection_name = collection_map.get(collection_type)
            if not embeddings_collection_name:
                self.logger.error(f"Unknown collection_type for embeddings: {collection_type}")
                return []
            embeddings_collection = self.analysis_db[embeddings_collection_name]
            cursor = embeddings_collection.find({}, {'_id': 1, 'e': 1, 'm': 1})
            similar_docs = []
            for doc in cursor:
                try:
                    doc_embedding = doc.get('e')
                    if isinstance(doc_embedding, (Binary, bytes, str)):
                        doc_embedding = self._binary_to_array(doc_embedding)
                    if not isinstance(doc_embedding, np.ndarray):
                        continue
                    similarity = self.cosine_similarity(embedding, doc_embedding)
                    doc['similarity'] = float(similarity)
                    similar_docs.append(doc)
                except Exception as e:
                    self.logger.error(f"Error processing document {doc.get('_id')}: {str(e)}")
                    continue
            similar_docs.sort(key=lambda x: x.get('similarity', 0), reverse=True)
            similar_docs = similar_docs[:limit]
            for doc in similar_docs:
                if isinstance(doc.get('e'), np.ndarray):
                    doc['e'] = doc['e'].tolist()
            return similar_docs
        except Exception as e:
            self.logger.error(f"Error finding similar documents: {str(e)}")
            return []

    def get_gpt_analysis(self, context: str, analysis_type: str) -> str:
        """Get gpt-4.1-mini analysis based on context and type"""
        try:
            prompts = {
                'competition': """Analyze the competitive landscape for this product/store based on the following data. 
                                Focus on: pricing strategy, market positioning, competitive advantages, and recommendations:""",
                'regional': """Analyze the regional performance and trends based on the following order data. 
                             Focus on: geographic patterns, seasonal trends, and regional opportunities:""",
                'customer': """Analyze the customer behavior and preferences based on the following data. 
                             Focus on: customer segments, buying patterns, and engagement opportunities:""",
                'store': """Analyze the store performance and characteristics based on the following data. 
                          Focus on: business model effectiveness, growth opportunities, and operational recommendations:"""
            }

            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,  # Changed model identifier as requested
                messages=[
                    # Enhance system prompt for expert estimation
                    {"role": "system", "content": "You are a seasoned Principal Market Strategist with 30 years of experience in competitive analysis for consumer goods. Based *only* on the provided product details (name, category, price), provide concise, expert estimations for the requested market factors. Focus on likely positioning and common competitive patterns relevant to this product archetype."},
                    {"role": "user", "content": f"{prompts[analysis_type]}\n\nContext:\n{context}"}
                ],
                # temperature=0.7, # Removed
                # Use max_completion_tokens as required by the API error message
                max_completion_tokens=1200 
                # max_tokens=1200 # Removed as it caused errors
            )
            
            analysis_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            return analysis_content if analysis_content is not None else ""
            
        except Exception as e:
            self.logger.error(f"Error getting GPT analysis: {str(e)}")
            return ""

    def analyze_and_store(self, doc: Dict, collection_type: str):
        """Analyze a document and store results"""
        try:
            # Get document embedding
            embeddings_collection = self.analysis_db[f"{collection_type}_embeddings"]
            doc_id = str(doc.get('_id')) if isinstance(doc.get('_id'), ObjectId) else doc.get('_id')
            doc_embedding = embeddings_collection.find_one({"_id": doc_id})
            
            if not doc_embedding or not doc_embedding.get('embedding'):
                self.logger.error(f"No embedding found for document {doc_id}")
                return
            
            # Convert binary embedding to array if needed
            embedding = doc_embedding['embedding']
            if isinstance(embedding, (Binary, bytes)):
                embedding = self._binary_to_array(embedding)
            
            # Find similar documents
            similar_docs = self.find_similar_documents(embedding, collection_type)
            
            # Prepare context for analysis
            context = self._prepare_analysis_context(doc, similar_docs, collection_type)
            
            # Get GPT analysis
            analysis_type_map = {
                'products': 'competition',
                'orders': 'regional',
                'customers': 'customer',
                'stores': 'store'
            }
            analysis = self.get_gpt_analysis(context, analysis_type_map[collection_type])
            
            # Prepare document for storage
            try:
                # First try to serialize with our custom encoder
                store_doc = json.loads(json.dumps(doc, cls=MongoJSONEncoder))
                store_similar_docs = json.loads(json.dumps(similar_docs, cls=MongoJSONEncoder))
            except Exception as e:
                self.logger.error(f"Error in JSON serialization: {str(e)}")
                # Fallback: manually convert problematic fields
                store_doc = self._sanitize_for_json(doc)
                store_similar_docs = [self._sanitize_for_json(d) for d in similar_docs]
            
            self.analysis_db[self.analysis_collection_map[collection_type]].update_one(
                {"_id": doc_id},
                {
                    "$set": {
                        "document": store_doc,
                        "similar_documents": store_similar_docs,
                        "analysis": analysis,
                        "last_updated": datetime.now()
                    }
                },
                upsert=True
            )
            
            self.logger.info(f"Analysis completed and stored for {collection_type} {doc_id}")
            
        except Exception as e:
            self.logger.error(f"Error in analyze_and_store: {str(e)}")
            self.logger.error(f"Error details: {str(e.__class__.__name__)}: {str(e)}")

    def _sanitize_for_json(self, doc: Dict) -> Dict:
        """Sanitize document for JSON serialization"""
        if not isinstance(doc, dict):
            return doc
        
        sanitized = {}
        for key, value in doc.items():
            if isinstance(value, bytes):
                sanitized[key] = base64.b64encode(value).decode('utf-8')
            elif isinstance(value, Binary):
                sanitized[key] = base64.b64encode(bytes(value)).decode('utf-8')
            elif isinstance(value, ObjectId):
                sanitized[key] = str(value)
            elif isinstance(value, datetime):
                sanitized[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, np.ndarray):
                sanitized[key] = value.tolist()
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_for_json(value)
            elif isinstance(value, list):
                sanitized[key] = [self._sanitize_for_json(item) if isinstance(item, dict) else item for item in value]
            else:
                sanitized[key] = value
        
        return sanitized

    def join_enriched_data(self, embeddings_docs: List[Dict], collection_type: str) -> List[Dict]:
        """Join embeddings data with cache data from D-Unit-AnalysisGPT using new *_embeddings collections"""
        try:
            doc_ids = [doc.get('_id') for doc in embeddings_docs if doc.get('_id')]
            # Map collection_type to cache collection
            cache_map = {
                'products': 'product_details_cache',
                'customers': 'store_customers_cache',
                'stores': 'active_stores_cache',
                'store_activity': 'store_activity_metrics',
                'meta_pages': 'meta_pages',
                'meta_posts': 'meta_posts',
                'meta_post_metrics': 'meta_post_metrics',
                'meta_comments': 'meta_comments',
                'meta_followers': 'meta_followers',
                'meta_demographics': 'meta_demographics',
                'meta_ad_campaigns': 'meta_ad_campaigns',
                'meta_ad_metrics': 'meta_ad_metrics',
                'meta_insights': 'meta_insights',
                'meta_sales_correlation': 'meta_sales_correlation',
                'meta_chat_context': 'meta_chat_context',
            }
            cache_collection = cache_map.get(collection_type)
            if not cache_collection:
                self.logger.error(f"Unknown collection_type for cache join: {collection_type}")
                return []
            cache_docs = list(self.analysis_db[cache_collection].find({'_id': {'$in': doc_ids}}))
            cache_lookup = {str(doc['_id']): doc for doc in cache_docs}
            combined_docs = []
            for emb_doc in embeddings_docs:
                doc_id = str(emb_doc.get('_id'))
                if doc_id in cache_lookup:
                    combined_doc = {
                        **cache_lookup[doc_id],
                        'embedding_metadata': emb_doc.get('m', {}),
                        'similarity_score': emb_doc.get('similarity', 0)
                    }
                    combined_docs.append(combined_doc)
            return combined_docs
        except Exception as e:
            self.logger.error(f"Error joining cache data: {str(e)}")
            return []

    def _prepare_analysis_context(self, doc: Dict, similar_docs: List[Dict], collection_type: str) -> str:
        """Prepare context for analysis with optimized data joining"""
        try:
            # Join with enriched data
            main_doc = self.join_enriched_data([{'_id': doc.get('_id')}], collection_type)[0]
            enriched_similar_docs = self.join_enriched_data(similar_docs, collection_type)
            
            context_parts = []
            
            # Add main document context
            if collection_type == 'products':
                context_parts.append(f"""
                Main Product Analysis:
                Name: {main_doc.get('name')}
                Price: {main_doc.get('price')}
                Category: {main_doc.get('categories', {}).get('name')}
                Store: {main_doc.get('store', {}).get('name')}
                Stock: {main_doc.get('stock')}
                """)
                
                # Add similar products context
                context_parts.append("\nSimilar Products:")
                for similar in enriched_similar_docs:
                    context_parts.append(f"""
                    Name: {similar.get('name')}
                    Price: {similar.get('price')}
                    Store: {similar.get('store', {}).get('name')}
                    Similarity: {similar.get('similarity_score', 0):.2f}
                    """)
                    
            elif collection_type == 'stores':
                # Get related data from cache collections
                store_id = str(main_doc.get('_id'))
                # Get products from product_details_cache
                product_cache_doc = self.analysis_db['product_details_cache'].find_one({'_id': store_id}) or {}
                products = product_cache_doc.get('products', [])
                # Get orders from orders_analysis (if available)
                orders_doc = self.analysis_db['orders_analysis'].find_one({'_id': store_id}) or {}
                orders = orders_doc.get('orders', [])
                # Get customers from store_customers_cache
                customer_cache_doc = self.analysis_db['store_customers_cache'].find_one({'_id': store_id}) or {}
                customers = customer_cache_doc.get('customers', [])
                # Calculate additional metrics
                total_revenue = sum(float(order.get('total', 0)) for order in orders)
                active_products = len([p for p in products if p.get('active')])
                product_categories = set(p.get('categories', {}).get('name') for p in products if p.get('categories'))
                # Log the metrics for debugging
                self.logger.info(f"""Store {store_id} metrics:\n- Total Revenue: {total_revenue}\n- Active Products: {active_products}\n- Product Categories: {', '.join(product_categories) if product_categories else 'None'}\n- Unique Customers: {len(customers)}\n""")
                context_parts.append(f"""
                Main Store Analysis:
                Name: {main_doc.get('name')}
                Country: {main_doc.get('country', {}).get('name')}
                Total Revenue: {total_revenue}
                Total Orders: {len(orders)}
                Active Products: {active_products}
                Product Categories: {', '.join(product_categories) if product_categories else 'None'}
                Unique Customers: {len(customers)}

                Product Portfolio:
                {self._format_product_summary(products)}

                Customer Base:
                {self._format_customer_summary(customers, orders)}

                Order History:
                {self._format_order_summary(orders)}
                """)
                # Add similar stores context
                context_parts.append("\nSimilar Stores:")
                for similar in enriched_similar_docs:
                    context_parts.append(f"""
                    Name: {similar.get('name')}
                    Country: {similar.get('country', {}).get('name')}
                    Products: {similar.get('product_count')}
                    Similarity: {similar.get('similarity_score', 0):.2f}
                    """)
                    
            elif collection_type == 'customers':
                context_parts.append(f"""
                Main Customer Analysis:
                Name: {main_doc.get('name')} {main_doc.get('last_name', '')}
                Location: {main_doc.get('location_info', {}).get('pais')}, {main_doc.get('location_info', {}).get('ciudad')}
                Orders: {main_doc.get('order_count')}
                Total Spent: {main_doc.get('total_spent')}
                """)
                
                # Add similar customers context
                context_parts.append("\nSimilar Customers:")
                for similar in enriched_similar_docs:
                    context_parts.append(f"""
                    Name: {similar.get('name')} {similar.get('last_name', '')}
                    Location: {similar.get('location_info', {}).get('pais')}
                    Orders: {similar.get('order_count')}
                    Similarity: {similar.get('similarity_score', 0):.2f}
                    """)
                    
            elif collection_type == 'orders':
                context_parts.append(f"""
                Main Order Analysis:
                Order ID: {main_doc.get('id_order')}
                Store: {main_doc.get('store', {}).get('name')}
                Customer: {main_doc.get('customer', {}).get('name')}
                Total: {main_doc.get('total')}
                Status: {main_doc.get('status', {}).get('name')}
                """)
                
                # Add similar orders context
                context_parts.append("\nSimilar Orders:")
                for similar in enriched_similar_docs:
                    context_parts.append(f"""
                    Order ID: {similar.get('id_order')}
                    Store: {similar.get('store', {}).get('name')}
                    Total: {similar.get('total')}
                    Similarity: {similar.get('similarity_score', 0):.2f}
                    """)
            
            return "\n".join(context_parts)
            
        except Exception as e:
            self.logger.error(f"Error preparing analysis context: {str(e)}")
            return ""

    def _format_product_summary(self, products: List[Dict]) -> str:
        """Format product summary for store analysis"""
        if not products:
            return "No products found"
        
        total_products = len(products)
        total_value = sum(float(p.get('price', 0)) for p in products)
        avg_price = total_value / total_products if total_products > 0 else 0
        
        categories = {}
        for product in products:
            category = product.get('categories', {}).get('name', 'Uncategorized')
            categories[category] = categories.get(category, 0) + 1
        
        summary = [
            f"Total Products: {total_products}",
            f"Average Price: {avg_price:.2f}",
            "\nProduct Categories Distribution:"
        ]
        
        for category, count in categories.items():
            percentage = (count / total_products) * 100
            summary.append(f"- {category}: {count} products ({percentage:.1f}%)")
        
        return "\n".join(summary)

    def _format_customer_summary(self, customers: List[Dict], orders: List[Dict]) -> str:
        """Format customer summary for store analysis"""
        if not customers:
            return "No customer data available"
        
        # Create customer order mapping
        customer_orders = {}
        for order in orders:
            customer_id = str(order.get('customer', {}).get('id'))
            if customer_id:
                if customer_id not in customer_orders:
                    customer_orders[customer_id] = {
                        'order_count': 0,
                        'total_spent': 0
                    }
                customer_orders[customer_id]['order_count'] += 1
                customer_orders[customer_id]['total_spent'] += float(order.get('total', 0))
        
        # Calculate metrics
        total_customers = len(customers)
        repeat_customers = len([c for c in customer_orders.values() if c['order_count'] > 1])
        avg_order_value = sum(c['total_spent'] for c in customer_orders.values()) / sum(c['order_count'] for c in customer_orders.values()) if customer_orders else 0
        
        summary = [
            f"Total Customers: {total_customers}",
            f"Repeat Customers: {repeat_customers}",
            f"Average Order Value: {avg_order_value:.2f}",
            "\nCustomer Locations:"
        ]
        
        # Analyze customer locations
        locations = {}
        for customer in customers:
            location = customer.get('location_info', {}).get('pais', 'Unknown')
            locations[location] = locations.get(location, 0) + 1
        
        for location, count in locations.items():
            percentage = (count / total_customers) * 100
            summary.append(f"- {location}: {count} customers ({percentage:.1f}%)")
        
        return "\n".join(summary)

    def _format_order_summary(self, orders: List[Dict]) -> str:
        """Format order summary for store analysis"""
        if not orders:
            return "No order history available"
        
        # Calculate metrics
        total_orders = len(orders)
        total_revenue = sum(float(order.get('total', 0)) for order in orders)
        avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
        
        # Analyze order status
        status_counts = {}
        for order in orders:
            status = order.get('status', {}).get('name', 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        summary = [
            f"Total Orders: {total_orders}",
            f"Total Revenue: {total_revenue:.2f}",
            f"Average Order Value: {avg_order_value:.2f}",
            "\nOrder Status Distribution:"
        ]
        
        for status, count in status_counts.items():
            percentage = (count / total_orders) * 100
            summary.append(f"- {status}: {count} orders ({percentage:.1f}%)")
        
        return "\n".join(summary)

    def process_collection(self, collection_type: str, limit: Optional[int] = None):
        """Process documents in a collection with optimized batch processing"""
        try:
            if not self._validate_database_structure():
                self.logger.error("Database structure validation failed. Please check the collections.")
                return
            # Map collection_type to cache collection
            cache_map = {
                'products': 'product_details_cache',
                'customers': 'store_customers_cache',
                'stores': 'active_stores_cache',
                'orders': 'orders_analysis'
            }
            cache_collection = cache_map.get(collection_type)
            if not cache_collection:
                self.logger.error(f"Unknown collection_type for cache: {collection_type}")
                return
            collection = self.analysis_db[cache_collection]
            total_docs = collection.count_documents({})
            if limit:
                total_docs = min(total_docs, limit)
                self.logger.info(f"Limited processing to {limit} documents")
            processed = 0
            start_time = datetime.now()
            self.logger.info(f"Starting analysis of {total_docs} documents in {collection_type}")
            # Check for already analyzed documents
            analysis_collection = self.analysis_db[self.analysis_collection_map[collection_type]]
            existing_ids = set(doc['_id'] for doc in analysis_collection.find({}, {'_id': 1}))
            # Process in batches
            batch = []
            for doc in collection.find().limit(total_docs):
                doc_id = str(doc.get('_id'))
                # Skip if already analyzed and not modified
                if doc_id in existing_ids:
                    current_doc = {k: v for k, v in doc.items() if k not in ['updated_at', 'last_modified', '_id']}
                    stored_doc = analysis_collection.find_one({"_id": doc_id})
                    if stored_doc:
                        stored_content = {k: v for k, v in stored_doc.get('document', {}).items() if k not in ['updated_at', 'last_modified', '_id']}
                        if current_doc == stored_content:
                            processed += 1
                            continue
                batch.append(doc)
                # Process batch when it reaches batch_size
                if len(batch) >= self.batch_size:
                    self._process_batch(batch, collection_type)
                    processed += len(batch)
                    batch = []
                    # Log progress
                    if processed % 10 == 0:
                        elapsed_time = (datetime.now() - start_time).total_seconds()
                        docs_per_second = processed / elapsed_time if elapsed_time > 0 else 0
                        estimated_remaining = (total_docs - processed) / docs_per_second if docs_per_second > 0 else 0
                        self.logger.info(
                            f"Progress for {collection_type}: {processed}/{total_docs} documents "
                            f"({(processed/total_docs*100):.1f}%) - "
                            f"Speed: {docs_per_second:.2f} docs/sec - "
                            f"Est. remaining time: {estimated_remaining/60:.1f} minutes"
                        )
            # Process remaining documents
            if batch:
                self._process_batch(batch, collection_type)
                processed += len(batch)
            total_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(
                f"Completed processing {collection_type} - "
                f"Total time: {total_time/60:.1f} minutes - "
                f"Average speed: {processed/total_time:.2f} docs/sec"
            )
        except Exception as e:
            self.logger.error(f"Error processing collection {collection_type}: {str(e)}")
            self.logger.error(f"Error details: {str(e.__class__.__name__)}: {str(e)}")

    def _process_batch(self, batch: List[Dict], collection_type: str):
        """Process a batch of documents in parallel"""
        try:
            # Prepare contexts in parallel
            with Pool(processes=min(len(batch), multiprocessing.cpu_count() - 1)) as pool:
                contexts = pool.starmap(
                    self._prepare_analysis_context,
                    [(doc, self.find_similar_documents(doc.get('embedding', []), collection_type), collection_type) for doc in batch]
                )
            
            # Get GPT analysis for the batch
            analysis_type_map = {
                'products': 'competition',
                'orders': 'regional',
                'customers': 'customer',
                'stores': 'store'
            }
            
            # Process each document with its context
            for doc, context in zip(batch, contexts):
                try:
                    # Check cache first
                    cache_key = f"{collection_type}_{doc.get('_id')}"
                    if cache_key in self.analysis_cache:
                        analysis = self.analysis_cache[cache_key]
                    else:
                        analysis = self.get_gpt_analysis(context, analysis_type_map[collection_type])
                        self.analysis_cache[cache_key] = analysis
                    
                    # Store analysis
                    store_doc = json.loads(json.dumps(doc, cls=MongoJSONEncoder))
                    self.analysis_db[self.analysis_collection_map[collection_type]].update_one(
                        {"_id": str(doc.get('_id'))},
                        {
                            "$set": {
                                "document": store_doc,
                                "analysis": analysis,
                                "last_updated": datetime.now()
                            }
                        },
                        upsert=True
                    )
                except Exception as e:
                    self.logger.error(f"Error processing document {doc.get('_id')}: {str(e)}")
                    continue
                
        except Exception as e:
            self.logger.error(f"Error processing batch: {str(e)}")

    def get_marketplace_data(self, product_name: str) -> Dict:
        """Get market data using gpt-4.1-mini instead of web scraping"""
        try:
            # Check cache first
            cache_key = f"marketplace_data_{product_name}"
            if cache_key in self.product_cache:
                self.logger.info(f"Using cached market data for product: {product_name}")
                return self.product_cache[cache_key]

            self.logger.info(f"Fetching new market data for product: {product_name}")
            
            # Single comprehensive API call instead of multiple ones
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": """Analyze the given product and provide a comprehensive analysis including:

1. Price Analysis:
   - Price ranges in different markets (USD)
   - Price comparison across regions (Uruguay: 40 UYU = 1 USD, Argentina: 1170 ARS = 1 USD, Peru: 3.7 PEN = 1 USD)
   - Price trends and seasonality
   - Price positioning relative to competitors

2. Promotional Analysis:
   - Common promotional strategies and their effectiveness
   - Typical discount ranges (%)
   - Seasonal promotion patterns
   - Shipping options and costs
   - Bundle deals and their impact
   - Warranty terms and coverage

3. Market Position:
   - Market segment (premium, mid-range, budget)
   - Competitive advantages
   - Regional market differences
   - Growth opportunities

Format response in clear sections with specific values and metrics."""},
                    {"role": "user", "content": f"Analyze market data for: {product_name}"}
                ],
                max_tokens=1000
            )
            
            analysis_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if analysis_content is None:
                self.logger.error(f"GPT analysis returned None for product: {product_name}")
                return {} # Return empty dict if analysis is None

            # Now it's safe to call extractors
            marketplace_data = {
                'price_range': {
                    'min': self._extract_price_range(analysis_content, 'min'),
                    'max': self._extract_price_range(analysis_content, 'max'),
                    'avg': self._extract_price_range(analysis_content, 'avg')
                },
                'normalized_prices': {
                    'usd_analysis': self._extract_normalized_prices(analysis_content),
                    'regional_comparison': self._extract_regional_prices(analysis_content)
                },
                'features': self._extract_features_from_analysis(analysis_content),
                'promotional_analysis': {
                    'strategies': self._extract_promotional_strategies(analysis_content),
                    'shipping_options': self._extract_shipping_from_analysis(analysis_content),
                    'warranty_info': self._extract_warranty_from_analysis(analysis_content),
                    'effectiveness': self._extract_promotion_effectiveness(analysis_content)
                },
                'competitive_position': self._extract_position_from_analysis(analysis_content),
                'last_updated': datetime.now().isoformat()
            }
            
            # Store in cache before returning
            self.product_cache[cache_key] = marketplace_data
            self.logger.info(f"Cached market data for product: {product_name}")
            
            return marketplace_data
            
        except Exception as e:
            self.logger.error(f"Error getting marketplace data: {str(e)}")
            return {}

    def _extract_normalized_prices(self, analysis: str) -> Dict:
        """Extract normalized USD prices from analysis"""
        try:
            min_match = re.search(r'minimum.*?(\d+\.?\d*)\s*USD', analysis, re.I)
            max_match = re.search(r'maximum.*?(\d+\.?\d*)\s*USD', analysis, re.I)
            avg_match = re.search(r'average.*?(\d+\.?\d*)\s*USD', analysis, re.I)

            return {
                'min_usd': float(min_match.group(1)) if min_match else 0.0,
                'max_usd': float(max_match.group(1)) if max_match else 0.0,
                'avg_usd': float(avg_match.group(1)) if avg_match else 0.0
            }
        except Exception as e:
            self.logger.error(f"Error extracting normalized prices: {str(e)}")
            return {'min_usd': 0.0, 'max_usd': 0.0, 'avg_usd': 0.0}

    def _extract_regional_prices(self, analysis: str) -> Dict:
        """Extract regional price comparisons"""
        try:
            return {
                'uruguay': self._extract_country_price(analysis, 'Uruguay', 'UYU'),
                'argentina': self._extract_country_price(analysis, 'Argentina', 'ARS'),
                'peru': self._extract_country_price(analysis, 'Peru', 'PEN')
            }
        except Exception as e:
            self.logger.error(f"Error extracting regional prices: {str(e)}")
            return {}

    def _extract_country_price(self, analysis: str, country: str, currency: str) -> Dict:
        """Extract price information for a specific country"""
        try:
            price_match = re.search(rf'{country}.*?(\d+\.?\d*)\s*{currency}', analysis, re.I)
            usd_match = re.search(rf'{country}.*?(\d+\.?\d*)\s*USD', analysis, re.I)
            
            return {
                'local_currency': float(price_match.group(1)) if price_match else 0.0,
                'usd_equivalent': float(usd_match.group(1)) if usd_match else 0.0
            }
        except Exception as e:
            self.logger.error(f"Error extracting {country} price: {str(e)}")
            return {'local_currency': 0.0, 'usd_equivalent': 0.0}

    def _extract_promotional_strategies(self, analysis: str) -> Dict:
        """Extract promotional strategy information"""
        try:
            return {
                'common_types': [line.strip('- ').strip() for line in analysis.split('\n') if 'promotion' in line.lower() or 'discount' in line.lower()],
                'discount_ranges': self._extract_discount_ranges(analysis),
                'seasonal_patterns': self._extract_seasonal_patterns(analysis),
                'bundle_deals': self._extract_bundle_deals(analysis)
            }
        except Exception as e:
            self.logger.error(f"Error extracting promotional strategies: {str(e)}")
            return {}

    def _extract_discount_ranges(self, analysis: str) -> List[Dict]:
        """Extract discount range information"""
        try:
            ranges = []
            for match in re.finditer(r'(\d+(?:\.\d+)?)\s*%\s*(?:to|-)\s*(\d+(?:\.\d+)?)\s*%', analysis):
                ranges.append({
                    'min': float(match.group(1)),
                    'max': float(match.group(2))
                })
            return ranges
        except Exception as e:
            self.logger.error(f"Error extracting discount ranges: {str(e)}")
            return []

    def _extract_seasonal_patterns(self, analysis: str) -> List[str]:
        """Extract seasonal promotion patterns"""
        try:
            seasons = ['summer', 'winter', 'spring', 'fall', 'holiday', 'christmas', 'new year']
            patterns = []
            for season in seasons:
                if season in analysis.lower():
                    pattern = re.search(rf'{season}.*?(\d+%|\w+\s+promotion)', analysis, re.I)
                    if pattern:
                        patterns.append(f"{season.title()}: {pattern.group(1)}")
            return patterns
        except Exception as e:
            self.logger.error(f"Error extracting seasonal patterns: {str(e)}")
            return []

    def _extract_bundle_deals(self, analysis: str) -> List[str]:
        """Extract bundle deal information"""
        try:
            return [line.strip('- ').strip() for line in analysis.split('\n') if 'bundle' in line.lower()]
        except Exception as e:
            self.logger.error(f"Error extracting bundle deals: {str(e)}")
            return []

    def _extract_promotion_effectiveness(self, analysis: str) -> Dict:
        """Extract promotion effectiveness metrics"""
        try:
            return {
                'most_effective': [line.strip('- ').strip() for line in analysis.split('\n') if 'effective' in line.lower()],
                'impact_metrics': self._extract_impact_metrics(analysis),
                'recommendations': [line.strip('- ').strip() for line in analysis.split('\n') if 'recommend' in line.lower()]
            }
        except Exception as e:
            self.logger.error(f"Error extracting promotion effectiveness: {str(e)}")
            return {}

    def _extract_price_range(self, analysis: str, range_type: str) -> float:
        """Extract price range from GPT analysis"""
        try:
            # Use regex to find price ranges in the analysis
            if range_type == 'min':
                match = re.search(r'minimum.*?(\d+\.?\d*)', analysis, re.I)
            elif range_type == 'max':
                match = re.search(r'maximum.*?(\d+\.?\d*)', analysis, re.I)
            else:  # avg
                match = re.search(r'average.*?(\d+\.?\d*)', analysis, re.I)
            
            return float(match.group(1)) if match else 0.0
        except Exception as e:
            self.logger.error(f"Error extracting price range: {str(e)}")
            return 0.0

    def _extract_features_from_analysis(self, analysis: str) -> List[str]:
        """Extract features from GPT analysis"""
        try:
            # Use GPT to extract and structure features
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "Extract key product features from the analysis. Return as a list."},
                    {"role": "user", "content": analysis}
                ],
                max_tokens=500
            )
            
            features_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if features_content is None:
                self.logger.warning("No feature content received from GPT.")
                return []
            features = features_content.split('\\n')
            return [f.strip('- ') for f in features if f.strip()]
        except Exception as e:
            self.logger.error(f"Error extracting features: {str(e)}")
            return []

    def _extract_shipping_from_analysis(self, analysis: str) -> Dict:
        """Extract shipping information from GPT analysis"""
        try:
            # Use GPT to extract shipping details
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": """Extract shipping information. Include:
                    - Common delivery times
                    - Shipping options
                    - Free shipping availability"""},
                    {"role": "user", "content": analysis}
                ],
                max_tokens=500
            )
            
            shipping_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if shipping_content is None:
                self.logger.warning("No shipping content received from GPT.")
                return {'options': [], 'free_shipping_available': False}
            return {
                'options': shipping_content.split('\n'),
                'free_shipping_available': 'free' in shipping_content.lower()
            }
        except Exception as e:
            self.logger.error(f"Error extracting shipping info: {str(e)}")
            return {}

    def _extract_warranty_from_analysis(self, analysis: str) -> Dict:
        """Extract warranty information from GPT analysis"""
        try:
            # Use GPT to extract warranty details
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": """Extract warranty information. Include:
                    - Common warranty duration
                    - Warranty types
                    - Additional protection options"""},
                    {"role": "user", "content": analysis}
                ],
                max_tokens=500
            )
            
            warranty_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if warranty_content is None:
                 self.logger.warning("No warranty content received from GPT.")
                 return {'details': [], 'has_warranty': False}
            return {
                'details': warranty_content.split('\n'),
                'has_warranty': 'warranty' in warranty_content.lower()
            }
        except Exception as e:
            self.logger.error(f"Error extracting warranty info: {str(e)}")
            return {}

    def _extract_position_from_analysis(self, analysis: str) -> Dict:
        """Extract competitive positioning from GPT analysis"""
        try:
            # Use GPT to analyze competitive position
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": """Analyze competitive position. Include:
                    - Price positioning
                    - Feature comparison
                    - Market advantages
                    - Improvement opportunities"""},
                    {"role": "user", "content": analysis}
                ],
                max_tokens=500
            )
            
            position_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if position_content is None:
                self.logger.warning("No position content received from GPT.")
                return {'analysis': [], 'position': 'unknown'}
            return {
                'analysis': position_content.split('\n'),
                'position': self._determine_market_position(position_content) # Pass the checked string
            }
        except Exception as e:
            self.logger.error(f"Error extracting competitive position: {str(e)}")
            return {}

    def _determine_market_position(self, analysis: str) -> str:
        """Determine market position from analysis"""
        analysis_lower = analysis.lower() if analysis else "" # Handle potential empty/None string
        if 'premium' in analysis_lower or 'high-end' in analysis_lower:
            return 'premium'
        elif 'budget' in analysis_lower or 'low-end' in analysis_lower:
            return 'budget'
        else:
            return 'mid-range'

    def _extract_competitor_analysis_from_analysis(self, analysis_sections: Dict[str, str]) -> str:
        """Extract competitor analysis from the AI response.
        
        Args:
            analysis_sections: Dictionary of section name -> content pairs
            
        Returns:
            String containing the competitor analysis text
        """
        try:
            # Check for Competitive & Market Positioning section first
            if "Competitive & Market Positioning" in analysis_sections:
                return analysis_sections["Competitive & Market Positioning"]
            
            # Check for other sections that might contain competitor information
            for section_name, content in analysis_sections.items():
                if "competitive" in section_name.lower() or "market" in section_name.lower() or "competitor" in section_name.lower():
                    return content
            
            # If no specific section found, try to extract from the entire analysis
            competitor_mentions = []
            for section_name, content in analysis_sections.items():
                content_lower = content.lower()
                if "competitor" in content_lower or "competition" in content_lower or "market position" in content_lower:
                    # Get paragraphs mentioning competitors
                    paragraphs = content.split('\n\n')
                    for paragraph in paragraphs:
                        paragraph_lower = paragraph.lower()
                        if "competitor" in paragraph_lower or "competition" in paragraph_lower or "market position" in paragraph_lower:
                            competitor_mentions.append(paragraph)
            
            # Join competitor mentions if any found
            if competitor_mentions:
                return "\n\n".join(competitor_mentions)
            
            return "No specific competitor analysis available."
            
        except Exception as e:
            self.logger.error(f"Error extracting competitor analysis: {str(e)}")
            return "Error extracting competitor analysis."

    def analyze_market_position(self, product: Dict) -> Dict:
        """Analyze product's market position across different marketplaces"""
        try:
            # Get marketplace data
            marketplace_data = self.get_marketplace_data(product.get('name', ''))
            
            # Calculate price positioning
            product_price_usd = self._normalize_price_to_usd(
                amount=float(product.get('price', 0)),
                currency='UYU',  # Assuming base currency is UYU
                marketplace='internal'
            )
            
            # Analyze competitive position
            price_analysis = self._analyze_price_position(product_price_usd, marketplace_data)
            feature_analysis = self._analyze_feature_differences(product, marketplace_data)
            promotion_analysis = self._analyze_promotions(marketplace_data)
            
            return {
                'price_positioning': price_analysis,
                'feature_comparison': feature_analysis,
                'promotion_insights': promotion_analysis,
                'marketplace_data': marketplace_data
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing market position: {str(e)}")
            return {}

    def _analyze_price_position(self, product_price: float, marketplace_data: Dict) -> Dict:
        """Analyze price positioning across marketplaces"""
        try:
            # Extract prices from marketplace data
            normalized_prices = marketplace_data.get('normalized_prices', {})
            usd_analysis = normalized_prices.get('usd_analysis', {})
            
            min_price = usd_analysis.get('min_usd', 0)
            max_price = usd_analysis.get('max_usd', 0)
            avg_price = usd_analysis.get('avg_usd', 0)
            
            if not any([min_price, max_price, avg_price]):
                return {
                    'position': 'unknown',
                    'difference_percentage': 0,
                    'recommendation': 'Insufficient market data'
                }
            
            # Calculate price position
            if avg_price > 0:
                price_difference = ((product_price - avg_price) / avg_price) * 100
            else:
                price_difference = 0
            
            # Determine position
            if price_difference > 15:
                position = 'premium'
            elif price_difference < -15:
                position = 'budget'
            else:
                position = 'competitive'
            
            return {
                'position': position,
                'difference_percentage': price_difference,
                'min_market_price': min_price,
                'max_market_price': max_price,
                'avg_market_price': avg_price,
                'recommendation': self._get_price_recommendation(price_difference)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing price position: {str(e)}")
            return {
                'position': 'unknown',
                'difference_percentage': 0,
                'recommendation': f'Error in price analysis: {str(e)}'
            }

    def _analyze_feature_differences(self, product: Dict, marketplace_data: Dict) -> Dict:
        """Analyze feature differences across marketplaces"""
        try:
            # Get features from marketplace data
            market_features = marketplace_data.get('features', [])
            
            # Get product features
            product_features = self._extract_product_features(product)
            
            # Convert to sets for comparison
            market_features_set = set(market_features)
            product_features_set = set(product_features)
            
            # Find unique and missing features
            unique_features = product_features_set - market_features_set
            missing_features = market_features_set - product_features_set
            
            return {
                'unique_features': list(unique_features),
                'missing_features': list(missing_features),
                'common_features': list(product_features_set & market_features_set),
                'recommendation': self._get_feature_recommendations(unique_features, missing_features)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing feature differences: {str(e)}")
            return {
                'unique_features': [],
                'missing_features': [],
                'common_features': [],
                'recommendation': f'Error in feature analysis: {str(e)}'
            }

    def _analyze_promotions(self, marketplace_data: Dict) -> Dict:
        """Analyze promotions across marketplaces"""
        try:
            all_promotions = {}
            effective_promotions = set()
            
            for marketplace, data in marketplace_data.items():
                promotions = data.get('promotions', [])
                all_promotions[marketplace] = promotions
                
                # Identify promotions that seem effective (based on sales data if available)
                if data.get('sales_rank') and data.get('price_usd', 0) > 0:
                    effective_promotions.update(promotions)
            
            return {
                'marketplace_promotions': all_promotions,
                'effective_promotions': list(effective_promotions),
                'recommendation': self._get_promotion_recommendations(effective_promotions)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing promotions: {str(e)}")
            return {}

    def _extract_product_features(self, product: Dict) -> List[str]:
        """Extract features from product data"""
        features = []
        
        # Add basic features
        if product.get('name'):
            features.append(f"name:{product['name']}")
        if product.get('description'):
            features.extend(self._extract_features_from_text(product['description']))
        
        # Add category features
        if product.get('categories', {}).get('name'):
            features.append(f"category:{product['categories']['name']}")
        
        # Add variations if available
        if product.get('variations'):
            for variation in product['variations']:
                if variation.get('name'):
                    features.append(f"variation:{variation['name']}")
        
        return features

    def _extract_features_from_text(self, text: str) -> List[str]:
        """Extract features from text description using GPT with enhanced error handling"""
        if not text or not isinstance(text, str):
            return []

        try:
            # Check cache first
            cache_key = f"features_{hash(text)}"
            if cache_key in self.feature_cache:
                self.logger.info("Using cached feature extraction")
                return self.feature_cache[cache_key]

            self.logger.info("Performing new feature extraction")
            
            # Truncate text if too long
            max_text_length = 1000
            if len(text) > max_text_length:
                text = text[:max_text_length] + "..."

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": """Extract key product features from the given text.
                    Return as a structured list with one feature per line.
                    Each feature should be clear and specific.
                    Format: feature: value"""},
                    {"role": "user", "content": text}
                ],
                max_tokens=800
            )
            
            content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if content is None:
                 self.logger.warning("No text feature content received from GPT.")
                 return []
            # Process features
            features = []
            for line in content.split('\n'): # Use checked content
                line = line.strip('- ').strip()
                if line and ':' in line:
                    feature_parts = line.split(':', 1)
                    if len(feature_parts) == 2 and all(part.strip() for part in feature_parts):
                        features.append(line)
            
            # Store in cache before returning
            self.feature_cache[cache_key] = features
            self.logger.info("Cached feature extraction results")
            
            return features

        except Exception as e:
            self.logger.error(f"Error extracting features from text: {str(e)}")
            return []

    def _get_price_recommendation(self, price_difference: float) -> str:
        """Get price adjustment recommendations"""
        if price_difference > 15:
            return "Consider lowering price to be more competitive"
        elif price_difference < -15:
            return "Potential opportunity to increase price"
        else:
            return "Price is competitive in the market"

    def _get_feature_recommendations(self, unique_features: set, missing_features: set) -> List[str]:
        """Get feature-based recommendations"""
        recommendations = []
        
        if unique_features:
            recommendations.append("Highlight these unique features in marketing: " + ", ".join(unique_features))
        
        if missing_features:
            recommendations.append("Consider adding these features to stay competitive: " + ", ".join(missing_features))
        
        if not recommendations:
            recommendations.append("Product features are well-aligned with market")
        
        return recommendations

    def _get_promotion_recommendations(self, effective_promotions: set) -> List[str]:
        """Get promotion-based recommendations"""
        recommendations = []
        
        if effective_promotions:
            recommendations.append("Consider implementing these successful promotion types: " + ", ".join(effective_promotions))
        else:
            recommendations.append("No clear promotion patterns identified in the market")
        
        return recommendations

    def process_global_analysis(self, limit: Optional[int] = None):
        """Process all collections together for global analysis"""
        try:
            if not self._validate_database_structure():
                self.logger.error("Database structure validation failed")
                return

            self.logger.info("Starting global analysis across all collections")
            start_time = datetime.now()

            # Get stores to process
            # Fetch store IDs from active_stores_cache in analysis_db
            store_ids = [doc['_id'] for doc in self.analysis_db['active_stores_cache'].find({}, {'_id': 1}).limit(limit if limit else 0)]
            total_stores = len(store_ids)
            
            self.logger.info(f"Found {total_stores} store IDs from active_stores_cache to process")
            processed = 0

            for i in range(0, total_stores, self.batch_size):
                # Create batch of store IDs
                batch_store_ids = store_ids[i:i + self.batch_size]
                self.logger.info(f"Processing batch of {len(batch_store_ids)} store IDs (IDs {i+1} to {min(i+self.batch_size, total_stores)})")
                
                # Pass batch of store IDs to _process_global_batch
                self._process_global_batch(batch_store_ids)
                processed += len(batch_store_ids)

                # Log progress
                elapsed_time = (datetime.now() - start_time).total_seconds()
                stores_per_second = processed / elapsed_time if elapsed_time > 0 else 0
                estimated_remaining = (total_stores - processed) / stores_per_second if stores_per_second > 0 else 0

                self.logger.info(
                    f"Progress: {processed}/{total_stores} stores "
                    f"({(processed/total_stores*100):.1f}%) - "
                    f"Speed: {stores_per_second:.2f} stores/sec - "
                    f"Est. remaining time: {estimated_remaining/60:.1f} minutes"
                )

            total_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(
                f"Completed global analysis - "
                f"Total time: {total_time/60:.1f} minutes - "
                f"Average speed: {processed/total_time:.2f} stores/sec"
            )

        except Exception as e:
            self.logger.error(f"Error in global analysis: {str(e)}")
            self.logger.error(f"Error details: {str(e.__class__.__name__)}: {str(e)}")

    # Change parameter to accept store IDs
    def _process_global_batch(self, store_ids: List[str]):
        """Process a batch of store IDs with their related data for global analysis"""
        try:
            # Loop through store IDs
            for store_id in store_ids:
                # Log using store_id
                self.logger.info(f"Processing store ID: {store_id}")
                
                # Call _get_store_complete_data with store_id
                store_data = self._get_store_complete_data(store_id)
                if not store_data:
                    # Update warning log message
                    self.logger.warning(f"Could not get complete data for store ID {store_id}")
                    continue

                # Log data retrieval success using store_id if needed (adjust log content based on new store_data structure later)
                # self.logger.info(f"Retrieved data for store {store_id}:") # Placeholder, content depends on step 3

                # Prepare global context (will be adapted in step 4)
                context = self._prepare_global_context(store_data)

                # Calculate metrics FIRST before using them
                calculated_metrics = self._calculate_store_metrics(store_data)
                
                # Get global analysis
                analysis = self.get_global_analysis(context)
                analysis = self._verify_metrics_consistency(analysis, calculated_metrics)
                analysis = self._verify_product_statements(analysis, store_data)
                
                # Extract shipping analysis for shipping recommendations
                shipping_analysis_text = ""
                # Try to get product performance section which might mention shipping
                if "product_performance" in analysis:
                    shipping_analysis_text = analysis.get("product_performance", "")
                
                # Get shipping recommendations
                shipping_recommendations = []
                if shipping_analysis_text:
                    shipping_recommendations = self._get_shipping_recommendations(store_id, shipping_analysis_text)
                
                # Extract competitor analysis
                analysis_sections = self._parse_analysis_sections(analysis.get("market_position", ""))
                competitor_analysis = self._extract_competitor_analysis_from_analysis(analysis_sections)
                
                # Store analysis results using store_id
                self.analysis_db[self.analysis_collection].update_one(
                    # Use store_id for matching
                    {"_id": store_id},
                    {
                        "$set": {
                            # Remove 'store' field, as data will come from cache via store_data
                            # "store": self._sanitize_for_json(store), # Removed
                            "analysis": analysis,
                            # Add shipping_analysis field with both analysis text and recommendations
                            "analysis.shipping_analysis": {
                                "analysis_text": shipping_analysis_text,
                                "recommendations": shipping_recommendations
                            },
                            # Add competitor_analysis field
                            "analysis.competitor_analysis": competitor_analysis,
                            # Use calculated metrics for consistency
                            "metrics": calculated_metrics,
                            "order_statistics": {  # Add explicit order statistics
                                "total_orders": calculated_metrics.get('total_orders', 0),
                                "total_sales": calculated_metrics.get('total_revenue', 0),
                                "average_order_value": calculated_metrics.get('average_order_value', 0)
                            },
                            "metadata": {
                                "customer_count": calculated_metrics.get('customer_count', 0),
                                "order_count": calculated_metrics.get('total_orders', 0),
                                "product_count": calculated_metrics.get('product_count_online', 0), # Usar el nuevo campo de productos en línea
                                "last_updated": datetime.now(timezone.utc), # Use UTC timestamp
                                "shipping_analysis_updated": datetime.now(timezone.utc),
                                "competitor_analysis_updated": datetime.now(timezone.utc)
                            }
                        }
                    },
                    upsert=True
                )
                
                # Log completion using store_id
                self.logger.info(f"Completed analysis for store ID {store_id}")

        except Exception as e:
            self.logger.error(f"Error processing global batch: {str(e)}")

    def _get_store_complete_data(self, store_id: str) -> Optional[Dict]:
        """Get complete store data using D-Unit-AnalysisGPT cache collections."""
        try:
            self.logger.debug(f"Getting complete data for store_id: {store_id}")
            
            # Query core cache collections with projections
            active_store_projection = {
                'name': 1, 'country.name': 1, 'currency.symbol': 1, 'business_type': 1,
                'keywords': 1, 'metrics.total_revenue': 1, 'metrics.total_orders': 1,
                'key_dates.last_sale_date': 1, 
                'social_media': 1,  # ADD THIS: Ensures social media data is retrieved
                '_id': 0 # Exclude _id if store_id is already known
            }
            active_store_doc = self.analysis_db['active_stores_cache'].find_one(
                {"_id": store_id},
                active_store_projection
            )

            # Explicit projection for product_details_cache including nested fields
            product_details_projection = {
                '_id': 0,
                'product_count': 1,
                'store_average_rating': 1,
                'store_ratings_count': 1,
                'category_summary': 1,
                'sales_by_date': 1, # Check existence
                'products.name': 1,
                'products.current_price': 1,
                'products.variations': 1,
                'products.category_summary.name': 1,
                'products.revenue': 1,
                'products.units_sold': 1,
                'products.stock': 1,
                'store_aggregations': 1, # NEW: Include the entire store_aggregations object
                # Add other necessary fields from products array here
            }
            product_details_doc = self.analysis_db['product_details_cache'].find_one(
                {"_id": store_id},
                product_details_projection
            )

            customer_details_projection = {
                # 'customers': 0, # Exclude the large array (REMOVE THIS LINE)
                '_id': 0        # Exclude _id
                # By default, all other fields will now be included
            }
            customer_details_doc = self.analysis_db['store_customers_cache'].find_one(
                {"_id": store_id},
                customer_details_projection
            )
            
            # Query Meta collections (will add projections next)
            # meta_pages: Only need name
            meta_pages_docs = list(self.analysis_db['meta_pages'].find(
                {"store_id": store_id},
                {'name': 1, '_id': 0} # Project only name
            ))
            # meta_insights: Sort, limit, project title/timestamp
            meta_insights_docs = list(self.analysis_db['meta_insights'].find(
                {"store_id": store_id},
                {'title': 1, 'timestamp': 1, '_id': 0} # Project title/timestamp
            ).sort("timestamp", -1).limit(3)) # Sort and limit
             # meta_sales_correlation: Project for existence checks
            meta_sales_corr_projection = {
                'sales_data.daily_sales': 1,
                'sales_data.product_sales': 1,
                 '_id': 0
            }
            meta_sales_corr_doc = self.analysis_db['meta_sales_correlation'].find_one(
                {"_id": store_id},
                meta_sales_corr_projection
            )
            
            # Fetch additional data for deeper context (will add projections next)
            # meta_posts: Limit to 3, project message/timestamp
            meta_posts_docs = list(self.analysis_db['meta_posts'].find(
                {"store_id": store_id},
                {'message': 1, 'created_time': 1, '_id': 0} # Project message/timestamp
            ).sort("created_time", -1).limit(3)) # Limit to 3
            
            # meta_demographics: Project age/location/date
            meta_demographics_projection = {
                'age_ranges': 1, 'top_locations': 1, 'date': 1, '_id': 0
            }
            meta_demographics_doc = self.analysis_db['meta_demographics'].find_one(
                {"store_id": store_id},
                meta_demographics_projection,
                sort=[("date", -1)]
            )
            
            # Log the fetched active_store_doc for currency debugging
            self.logger.debug(f"Store {store_id}: Fetched active_store_doc: {active_store_doc}")
            
            # meta_ad_campaigns: Filter active, project objective
            meta_ad_campaigns_docs = list(self.analysis_db['meta_ad_campaigns'].find(
                {"store_id": store_id, "status": "ACTIVE"},
                {'objective': 1, '_id': 0} # Project objective
            ))
            
            # meta_ad_metrics: Limit 30, project spend/date
            meta_ad_metrics_docs = list(self.analysis_db['meta_ad_metrics'].find(
                {"store_id": store_id},
                {'spend': 1, 'date': 1, '_id': 0} # Project spend/date
            ).sort("date", -1).limit(30))
            # meta_comments, meta_followers etc. could be added here if needed
            
            # --- NEWLY ADDED COLLECTIONS ---
            # meta_post_metrics: Limit 5 recent, project key metrics
            meta_post_metrics_docs = list(self.analysis_db['meta_post_metrics'].find(
                {"store_id": store_id},
                {'post_id': 1, 'likes': 1, 'comments': 1, 'shares': 1, 'timestamp': 1, '_id': 0}
            ).sort("timestamp", -1).limit(5))

            # meta_comments: Limit 5 recent, project message/time
            meta_comments_docs = list(self.analysis_db['meta_comments'].find(
                {"store_id": store_id},
                {'message': 1, 'created_time': 1, '_id': 0}
            ).sort("created_time", -1).limit(5))

            # meta_followers: Get latest count
            meta_followers_doc = self.analysis_db['meta_followers'].find_one(
                {"store_id": store_id},
                {'count': 1, 'date': 1, '_id': 0},
                sort=[("date", -1)]
            )

            # meta_ad_accounts: Project name/ID
            meta_ad_accounts_docs = list(self.analysis_db['meta_ad_accounts'].find(
                {"store_id": store_id},
                {'name': 1, 'account_id': 1, '_id': 0}
            ))

            # meta_chat_context: Get latest summary
            meta_chat_context_doc = self.analysis_db['meta_chat_context'].find_one(
                {"store_id": store_id},
                {'summary': 1, 'last_updated': 1, '_id': 0},
                sort=[("last_updated", -1)]
            )

            # store_chats: Get 5 most recent messages using aggregation
            store_chats_pipeline = [
                {'$match': {'store_id': store_id}},
                {'$unwind': '$messages'},
                {'$sort': {'messages.created_at': -1}},
                {'$limit': 5},
                {'$project': {
                    '_id': 0,
                    'sender': '$messages.role',
                    'message': '$messages.content',
                    'timestamp': '$messages.created_at'
                }}
            ]
            store_chats_docs = list(self.analysis_db['store_chats'].aggregate(store_chats_pipeline))
            if not store_chats_docs:
                self.logger.info(f"Store {store_id}: No messages found in store_chats after aggregation.")
            elif len(store_chats_docs) < 5:
                self.logger.info(f"Store {store_id}: Found only {len(store_chats_docs)} messages in store_chats (less than 5 requested).")

            # Validate essential data
            if not active_store_doc:
                self.logger.warning(f"Essential cache 'active_stores_cache' missing for store ID {store_id}")
                return None
            if not product_details_doc:
                 self.logger.warning(f"Cache 'product_details_cache' missing for store ID {store_id}, analysis might be limited.")
            if not customer_details_doc:
                 self.logger.warning(f"Cache 'store_customers_cache' missing for store ID {store_id}, analysis might be limited.")

            # Construct the result dictionary using fetched cache data
            return {
                'store_id': store_id,
                'active_store_cache': active_store_doc or {},
                'product_details_cache': product_details_doc or {},
                'customer_details_cache': customer_details_doc or {},
                'meta_pages': meta_pages_docs,
                'meta_insights': meta_insights_docs,
                'meta_sales_correlation': meta_sales_corr_doc or {},
                # Add newly fetched data to the return dictionary
                'meta_posts': meta_posts_docs,
                'meta_demographics': meta_demographics_doc or {},
                'meta_ad_campaigns': meta_ad_campaigns_docs,
                'meta_ad_metrics': meta_ad_metrics_docs,
                'meta_post_metrics': meta_post_metrics_docs,
                'meta_comments': meta_comments_docs,
                'meta_followers': meta_followers_doc or {},
                'meta_ad_accounts': meta_ad_accounts_docs,
                'meta_chat_context': meta_chat_context_doc or {},
                'store_chats': store_chats_docs,
            }

        except Exception as e:
            self.logger.error(f"Error getting complete store data from caches for ID {store_id}: {str(e)}")
            self.logger.exception(f"Traceback for getting store data error:") # Log traceback for details
            return None

    def _process_order(self, order: Dict, customer_lookup: Dict, product_lookup: Dict) -> Dict:
        """Process a single order with efficient lookups"""
        try:
            customer_id = str(order.get('customer', {}).get('id'))
            if customer_id in customer_lookup:
                order['customer_complete'] = customer_lookup[customer_id]
            
            enriched_products = []
            for product in order.get('products', []):
                product_id = str(product['id'])
                if product_id in product_lookup:
                    enriched_products.append({
                        **product,
                        'complete_info': product_lookup[product_id]
                    })
            order['products_complete'] = enriched_products
            
            return order
        except Exception as e:
            self.logger.error(f"Error processing order: {str(e)}")
            return order

    def _calculate_store_metrics(self, store_data: Dict) -> Dict:
        """Calculate consistent metrics from store data"""
        metrics = {}
        
        # Get data from various caches
        active_store = store_data.get('active_store_cache', {})
        customer_cache = store_data.get('customer_details_cache', {})
        product_cache = store_data.get('product_details_cache', {})
        
        # REVENUE CALCULATION: Handle both gross and net product revenue
        gross_product_revenue = 0.0
        net_product_revenue = 0.0
        total_revenue = 0.0
        shipping_revenue = 0.0
        total_discounts = 0.0
        revenue_source = 'product_details_cache'
        
        # Get store metrics from active_stores_cache (SOURCE OF TRUTH for order totals)
        store_metrics = active_store.get('metrics', {})
        
        # Get revenue breakdown from product cache store_aggregations
        store_aggregations = product_cache.get('store_aggregations', {})
        
        # Use the clearly labeled revenue fields if available
        if 'gross_product_revenue' in store_aggregations and 'net_revenue_after_discounts' in store_aggregations:
            gross_product_revenue = store_aggregations.get('gross_product_revenue', 0.0)
            net_product_revenue = store_aggregations.get('net_revenue_after_discounts', 0.0)
            total_discounts = store_aggregations.get('total_allocated_discounts', 0.0)
            
            # Get total revenue with shipping from store metrics
            total_revenue = store_metrics.get('total_revenue', net_product_revenue)
            shipping_revenue = total_revenue - net_product_revenue
            revenue_source = 'product_details_cache (v4.0 with clear labeling)'
            
            self.logger.info(f"Store {store_data.get('store_id')}: Using v4.0 revenue breakdown - "
                            f"Gross: {gross_product_revenue}, Net: {net_product_revenue}, "
                            f"Discounts: {total_discounts}, Shipping: {shipping_revenue}")
        
        # FALLBACK: Use legacy field names
        elif 'total_gross_revenue' in store_aggregations:
            gross_product_revenue = store_aggregations.get('total_gross_revenue', 0.0)
            net_product_revenue = store_aggregations.get('total_revenue', 0.0)
            total_discounts = store_aggregations.get('total_allocated_discounts', 0.0)
            
            total_revenue = store_metrics.get('total_revenue', net_product_revenue)
            shipping_revenue = total_revenue - net_product_revenue
            revenue_source = 'product_details_cache (legacy field names)'
            
            self.logger.info(f"Store {store_data.get('store_id')}: Using legacy revenue fields - "
                            f"Gross: {gross_product_revenue}, Net: {net_product_revenue}")
        
        # FALLBACK: Use enhanced revenue breakdown from active_stores_cache
        elif store_metrics.get('product_revenue') is not None:
            net_product_revenue = store_metrics.get('product_revenue', 0.0)
            total_revenue = store_metrics.get('total_revenue', 0.0)
            shipping_revenue = store_metrics.get('shipping_revenue', 0.0)
            
            # Try to estimate gross from net (this is approximate)
            if store_metrics.get('average_discount_rate'):
                discount_rate = store_metrics.get('average_discount_rate', 0.0)
                gross_product_revenue = net_product_revenue / (1 - discount_rate)
                total_discounts = gross_product_revenue - net_product_revenue
            else:
                gross_product_revenue = net_product_revenue  # No discount info available
                total_discounts = 0.0
                
            revenue_source = 'active_stores_cache (enhanced)'
            self.logger.info(f"Store {store_data.get('store_id')}: Using enhanced active_stores_cache - "
                            f"Net: {net_product_revenue}, Total: {total_revenue}")
        
        # Calculate from individual products (last resort)
        elif 'products' in product_cache:
            for product in product_cache['products']:
                gross_revenue = float(product.get('gross_revenue', 0.0) or 0.0)
                net_revenue = float(product.get('revenue', 0.0) or 0.0)
                discount = float(product.get('allocated_discount', 0.0) or 0.0)
                
                gross_product_revenue += gross_revenue if gross_revenue > 0 else net_revenue
                net_product_revenue += net_revenue
                total_discounts += discount
                
            total_revenue = store_metrics.get('total_revenue', net_product_revenue)
            shipping_revenue = total_revenue - net_product_revenue
            revenue_source = 'calculated from individual products'
            
            self.logger.info(f"Store {store_data.get('store_id')}: Calculated from products - "
                            f"Gross: {gross_product_revenue}, Net: {net_product_revenue}")
        
        # FINAL FALLBACK: Use total revenue only
        else:
            total_revenue = store_metrics.get('total_revenue', 0.0)
            net_product_revenue = total_revenue  # Assume all revenue is product revenue
            gross_product_revenue = total_revenue  # No discount info available
            shipping_revenue = 0.0
            total_discounts = 0.0
            revenue_source = 'active_stores_cache (total only)'
            
            self.logger.warning(f"Store {store_data.get('store_id')}: No detailed revenue data, "
                            f"using total only: {total_revenue}")
        
        # Get order count - prefer customer cache as it's more accurate for order tracking
        total_orders = customer_cache.get('total_store_orders', 0)
        
        # If no order count in customer cache, try active store metrics
        if total_orders == 0:
            store_metrics = active_store.get('metrics', {})
            total_orders = store_metrics.get('total_orders', 0)
        
        # Calculate AOVs from different revenue types
        average_order_value = 0
        average_product_value = 0
        average_gross_value = 0
        
        if total_orders > 0:
            if total_revenue > 0:
                average_order_value = total_revenue / total_orders
            if net_product_revenue > 0:
                average_product_value = net_product_revenue / total_orders
            if gross_product_revenue > 0:
                average_gross_value = gross_product_revenue / total_orders
        
        # Calculate discount rate
        discount_rate = 0.0
        if gross_product_revenue > 0:
            discount_rate = (total_discounts / gross_product_revenue) * 100
        
        metrics.update({
            'total_orders': total_orders,
            
            # Clear revenue labeling
            'gross_product_revenue': round(gross_product_revenue, 2),
            'net_product_revenue': round(net_product_revenue, 2),
            'total_discounts': round(total_discounts, 2),
            'discount_rate': round(discount_rate, 2),
            
            # Total revenue components
            'total_revenue': round(total_revenue, 2),
            'shipping_revenue': round(shipping_revenue, 2),
            
            # Legacy field for compatibility
            'product_revenue': round(net_product_revenue, 2),
            
            # Average values
            'average_order_value': round(average_order_value, 2),
            'average_product_value': round(average_product_value, 2),
            'average_gross_value': round(average_gross_value, 2),
            
            # Other metrics
            'customer_count': customer_cache.get('total_customers', 0),
            'product_count': product_cache.get('product_count', 0),
            'product_count_online': store_aggregations.get('products_online', 0),
            'revenue_source': revenue_source
        })
        
        return metrics

    def _prepare_global_context(self, store_data: Dict) -> str:
        """Prepare comprehensive context using data from D-Unit-AnalysisGPT cache collections."""
        try:
            store_id = store_data.get('store_id', 'Unknown')
            if store_id == 'Unknown':
                self.logger.error("Store ID is missing in store_data")
                return "Error: Store ID missing."
            
            # After calculating metrics
            calculated_metrics = self._calculate_store_metrics(store_data)

            # Use the calculated metrics in the context
            gross_product_revenue = calculated_metrics['gross_product_revenue']
            net_product_revenue = calculated_metrics['net_product_revenue']
            total_discounts = calculated_metrics['total_discounts']
            discount_rate = calculated_metrics['discount_rate']
            total_revenue = calculated_metrics['total_revenue']
            total_orders = calculated_metrics['total_orders']
            average_order_value = calculated_metrics['average_order_value']
            shipping_revenue = calculated_metrics['shipping_revenue']
            products_online_count = calculated_metrics['product_count_online'] # Obtener el nuevo campo
            # Helper function to get data with defaults and logging
            def get_data(data_dict, key, default_value, warning_msg):
                if not isinstance(data_dict, dict):
                    self.logger.warning(f"Store {store_id}: Expected dict for {warning_msg}, got {type(data_dict)}. Using default: {default_value}")
                    return default_value
                value = data_dict.get(key)
                if value is None: # Check explicitly for None
                    self.logger.warning(f"Store {store_id}: {warning_msg} - Key '{key}' missing or None, using default: {default_value}")
                    return default_value
                return value
            # Unpack data with defaults
            active_store = store_data.get('active_store_cache', {})
            # TEMP: Log full active_store for store 1018
            if store_id == "1018":
                self.logger.warning(f"Store 1018 full active_store document: {active_store}")
            product_cache = store_data.get('product_details_cache', {})
            customer_cache = store_data.get('customer_details_cache', {})
            meta_pages = store_data.get('meta_pages', [])
            meta_insights = store_data.get('meta_insights', [])
            meta_sales_corr = store_data.get('meta_sales_correlation', {})
            meta_posts = store_data.get('meta_posts', [])
            meta_demographics = store_data.get('meta_demographics', {})
            meta_ad_campaigns = store_data.get('meta_ad_campaigns', [])
            meta_ad_metrics = store_data.get('meta_ad_metrics', [])
            # Unpack newly added data
            meta_post_metrics = store_data.get('meta_post_metrics', [])
            meta_comments = store_data.get('meta_comments', [])
            meta_followers = store_data.get('meta_followers', {})
            meta_ad_accounts = store_data.get('meta_ad_accounts', [])
            meta_chat_context = store_data.get('meta_chat_context', {})
            store_chats = store_data.get('store_chats', [])

            # Extract basic store info with defaults
            store_name = get_data(active_store, 'name', 'Unknown Store', "Store name missing")
            country_dict = active_store.get('country', {})
            store_location = get_data(country_dict, 'name', 'Unknown Location', "Store location missing")
            
            # Simplified currency code extraction - access nested dictionary directly
            currency_dict = active_store.get('currency', {})
            native_dict = currency_dict.get('native', {})
            # Log the currency structure for debugging
            self.logger.debug(f"Store {store_id}: Currency structure: {currency_dict}")
            
            # Direct access with fallback
            currency_code = native_dict.get('code') if isinstance(native_dict, dict) else None
            store_currency = currency_code if currency_code else 'UYU'  # Default currency if missing
            
            store_metrics = active_store.get('metrics', {})
            business_type = get_data(active_store, 'business_type', 'N/A', "Business type missing")
            keywords = active_store.get('keywords', []) # Already defaults to [] if missing
            # Use calculated metrics instead of raw store metrics
            total_revenue_metric = total_revenue
            total_orders_metric = total_orders
            
            # Get the last sale date from customer_details_cache instead of active_store
            last_sale_date = 'N/A'
            customers_list = customer_cache.get('customers', [])
            if customers_list:
                try:
                    # Find the most recent last_order_date across all customers
                    latest_date = None
                    for customer in customers_list:
                        order_date_str = customer.get('last_order_date')
                        if not order_date_str:
                            continue
                        
                        # Try to parse the date string - handle both string and datetime formats
                        if isinstance(order_date_str, str):
                            try:
                                # Try ISO format with timezone
                                if 'T' in order_date_str and '+' in order_date_str:
                                    order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00'))
                                else:
                                    # Try other common formats
                                    order_date = datetime.strptime(order_date_str, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                # Skip invalid dates
                                continue
                        elif isinstance(order_date_str, datetime):
                            order_date = order_date_str
                        else:
                            # Skip non-date values
                            continue
                        
                        # Update latest_date if this is more recent
                        if latest_date is None or order_date > latest_date:
                            latest_date = order_date
                    
                    # Format the result if we found a valid latest date
                    if latest_date:
                        last_sale_date = latest_date.strftime('%Y-%m-%d %H:%M:%S')
                        self.logger.info(f"Store {store_id}: Found latest sale date: {last_sale_date}")
                except Exception as e:
                    self.logger.error(f"Store {store_id}: Error finding latest sale date: {str(e)}")
                    last_sale_date = 'N/A (Error processing dates)'
            else:
                self.logger.warning(f"Store {store_id}: No customers found for extracting last sale date")
                last_sale_date = 'N/A (No customers)'
            
            # Extract product info with defaults
            cached_products = product_cache.get('products', []) # Already defaults to []
            product_count = get_data(product_cache, 'product_count', 0, "Product count missing")
            # NEW: Use products_online from store_aggregations for online product count
            products_online_count = get_data(product_cache.get('store_aggregations', {}), 'products_online', 0, "Online product count missing")

            store_avg_rating = get_data(product_cache, 'store_average_rating', 'N/A', "Store average rating missing")
            store_ratings_count = get_data(product_cache, 'store_ratings_count', 0, "Store ratings count missing")
            category_summary = product_cache.get('category_summary', {}) # Already defaults to {}
            sales_by_date_exists = bool(product_cache.get('sales_by_date')) # Checks existence
            # Enhanced sorting: prioritize products with meaningful sales (≥ 1) over products with $0 revenue
            try:
                def prioritize_products_with_sales(product):
                    revenue = float(product.get('revenue', 0))
                    # Products with meaningful revenue (≥ 1) get priority, then sort by revenue
                    if revenue >= 1:
                        return (0, -revenue)  # Higher priority (0), then by revenue descending
                    else:
                        return (1, -revenue)  # Lower priority (1), then by revenue descending
                
                # First, try to get products with meaningful sales only
                products_with_sales = [p for p in cached_products if float(p.get('revenue', 0)) >= 1]
                
                if len(products_with_sales) >= 3:
                    # If we have at least 3 products with meaningful sales, prioritize them
                    top_products_data = sorted(products_with_sales, key=lambda p: float(p.get('revenue', 0)), reverse=True)[:5]
                    self.logger.info(f"Store {store_id}: Showing top {len(top_products_data)} products with meaningful sales (filtered out {len(cached_products) - len(products_with_sales)} products with no sales)")
                else:
                    # Otherwise, use the mixed approach but still prioritize products with sales
                    top_products_data = sorted(cached_products, key=prioritize_products_with_sales)[:5]
                    products_with_meaningful_sales = sum(1 for p in top_products_data if float(p.get('revenue', 0)) >= 1)
                    self.logger.info(f"Store {store_id}: Mixed selection - {products_with_meaningful_sales} with meaningful sales, {len(top_products_data) - products_with_meaningful_sales} without significant sales")
                    
            except (TypeError, ValueError):
                top_products_data = cached_products[:5]
                self.logger.warning(f"Store {store_id}: Could not sort products by revenue, using first 5.")
            
            # Calculate product statistics for enhanced overview
            products_with_sales = sum(1 for p in cached_products if float(p.get('revenue', 0)) > 0)
            products_without_sales = len(cached_products) - products_with_sales
            total_product_revenue = sum(float(p.get('revenue', 0)) for p in cached_products)
            
            # Extract customer info with defaults
            customer_count = get_data(customer_cache, 'total_customers', 0, "Customer count missing")
            avg_spend = get_data(customer_cache, 'average_spend_per_customer', 0.0, "Average customer spend missing")
            country_dist = customer_cache.get('country_distribution', {}) # Already defaults
            status_dist = customer_cache.get('status_distribution', {}) # Already defaults
            payment_dist = customer_cache.get('payment_method_distribution', {}) # Already defaults
            shipping_dist = customer_cache.get('shipping_method_distribution', {}) # Already defaults
            
            # Get store country for filtering shipping methods (AEX is Paraguay-specific)
            store_country = None
            if active_store and 'country' in active_store:
                store_country = active_store['country'].get('name', '').lower() if isinstance(active_store['country'], dict) else str(active_store['country']).lower()
            
            # Filter out AEX from shipping distribution for non-Paraguay stores
            if store_country != 'paraguay' and shipping_dist:
                original_aex_present = 'AEX' in shipping_dist
                shipping_dist = {k: v for k, v in shipping_dist.items() if k.upper() != 'AEX'}
                if original_aex_present:
                    self.logger.info(f"Store {store_id} is in {store_country}, filtered out AEX from shipping distribution display")
            coupon_dist = customer_cache.get('coupon_code_distribution', {}) # Already defaults
            abandoned_carts = get_data(customer_cache, 'abandoned_cart_count', 0, "Abandoned cart count missing")
            abandoned_value = get_data(customer_cache, 'abandoned_cart_total_value', 0.0, "Abandoned cart value missing")
            pending_carts = get_data(customer_cache, 'pending_cart_count', 0, "Pending cart count missing")
            pending_value = get_data(customer_cache, 'pending_cart_total_value', 0.0, "Pending cart value missing")
            
            # Marketplace Insights
            marketplace_insights = []
            for product in top_products_data:
                self.logger.debug(f"Store {store_id}: Processing product for marketplace insights: {product}")
                
                product_name = get_data(product, 'name', 'Unknown Product', f"Product name missing in top products data")
                
                # FIXED: Updated to use current_price based on MongoDB structure
                product_price = 0.0
                price_found = False
                try:
                    # MongoDB uses 'current_price' not 'price'
                    if 'current_price' in product and product['current_price'] is not None:
                        try:
                            temp_price = float(product['current_price'])
                            if temp_price > 0:
                                product_price = temp_price
                                price_found = True
                                self.logger.debug(f"Store {store_id}: Found price in current_price field: {product_price}")
                        except (ValueError, TypeError):
                            pass
                    
                    # Fallback to variations if no main price
                    if not price_found and 'variations' in product and product['variations']:
                        for variation in product['variations']:
                            if 'variation_price' in variation and variation['variation_price'] is not None:
                                try:
                                    temp_price = float(variation['variation_price'])
                                    if temp_price > 0:
                                        product_price = temp_price
                                        price_found = True
                                        self.logger.debug(f"Store {store_id}: Found price in variation_price: {product_price}")
                                        break
                                except (ValueError, TypeError):
                                    continue
                    
                    if not price_found:
                        self.logger.warning(f"Store {store_id}: No valid price found for product '{product_name}'")
                        
                except Exception as e:
                    self.logger.error(f"Store {store_id}: Error extracting price for product '{product_name}': {str(e)}")
                    product_price = 0.0

                # Robust extraction for category name
                product_category_dict = product.get('category_summary') if isinstance(product.get('category_summary'), dict) else {}
                product_category = product_category_dict.get('name', 'Unknown Category')
                if product_category == 'Unknown Category':
#                    self.logger.warning(f"Store {store_id}: Product category missing for '{product_name}'")
                    pass

                product_market_prompt = f"Analyze the likely market position for a product named '{product_name}' in category '{product_category}' with a price of {product_price} {store_currency}. Based *only* on this information, estimate:\n1. Price Position: (e.g., Low-end, Mid-range, High-end, Premium) compared to similar items.\n2. Key Features: List 2-3 likely key features for a product like this.\n3. Potential Competitors: Mention 1-2 types of likely competitors.\nProvide a brief analysis."
                estimated_market_analysis = self.get_gpt_analysis(product_market_prompt, 'competition')
                parsed_insights = {'position': 'unknown', 'features': 'N/A', 'competitors': 'N/A'}
                if estimated_market_analysis:
                    for line in estimated_market_analysis.split('\n'):
                        line_lower = line.lower()
                        if 'price position:' in line_lower:
                            parsed_insights['position'] = line.split(':')[-1].strip() or 'unknown'
                        elif 'key features:' in line_lower:
                            parsed_insights['features'] = line.split(':')[-1].strip() or 'N/A'
                        elif 'potential competitors:' in line_lower:
                            parsed_insights['competitors'] = line.split(':')[-1].strip() or 'N/A'
                else:
                    self.logger.warning(f"Store {store_id}: Could not get estimated market analysis for product {product_name}")
                marketplace_insights.append(f"""
Product: {product_name} (AI Market Estimation)
- Est. Price Position: {parsed_insights.get('position', 'unknown')}
- Est. Key Features: {parsed_insights.get('features', 'N/A')}
- Est. Competitors: {parsed_insights.get('competitors', 'N/A')}
""")
            # Prepare Meta Integration Summary
            meta_summary = "Meta Integration: Not Connected."
            if meta_pages:
                page_names = [get_data(p, 'name', 'Unknown Page', "Meta page name missing") for p in meta_pages]
                insight_count = len(meta_insights)
                active_campaign_count = len(meta_ad_campaigns)
                joined_page_names = ", ".join(page_names)
                meta_summary = f"Meta Integration: Connected ({len(page_names)} pages: {joined_page_names}). {insight_count} insights available. {active_campaign_count} active ad campaigns found."
                if meta_demographics:
                    demo_highlights = []
                    age_ranges = meta_demographics.get('age_ranges')
                    if age_ranges:
                        try:
                            dominant_age = max(age_ranges, key=lambda x: list(x.values())[0])
                            demo_highlights.append(f"Dominant Audience Age: {list(dominant_age.keys())[0]}")
                        except Exception as e:
                             self.logger.warning(f"Store {store_id}: Could not determine dominant age from meta_demographics: {e}")
                    top_locations = meta_demographics.get('top_locations')
                    if top_locations:
                        try:
                            top_loc = top_locations[0]
                            demo_highlights.append(f"Top Audience Location: {list(top_loc.keys())[0]}")
                        except Exception as e:
                             self.logger.warning(f"Store {store_id}: Could not determine top location from meta_demographics: {e}")
                    if demo_highlights:
                        joined_highlights = ", ".join(demo_highlights)
                        meta_summary += f"\n- Audience Highlights: {joined_highlights}."
                if meta_posts:
                    # Provide warning message for post message
                    post_themes = [get_data(p, 'message', '', "Meta post message missing")[:50] + '...' for p in meta_posts[:3]]
                    meta_summary += f"\n- Recent Post Snippets: {post_themes}"
                if meta_ad_campaigns:
                    objectives = list(set([get_data(c, 'objective', 'N/A', "Ad campaign objective missing") for c in meta_ad_campaigns]))
                    joined_objectives = ", ".join(objectives)
                    meta_summary += f"\n- Active Ad Objectives: {joined_objectives}"
                if meta_ad_metrics:
                    try:
                        recent_spend = sum(float(get_data(m, 'spend', 0.0, "Ad metric spend missing")) for m in meta_ad_metrics)
                        meta_summary += f"\n- Recent Ad Spend (last 30d approx): {recent_spend:.2f}"
                    except Exception as e:
                         self.logger.warning(f"Store {store_id}: Could not calculate recent ad spend: {e}")
                if meta_insights:
                     latest_insights = sorted(meta_insights, key=lambda x: x.get('timestamp'), reverse=True)[:3]
                     # Provide warning message for insight title
                     insight_titles = [get_data(i, "title", "Insight", "Insight title missing") for i in latest_insights]
                     joined_insight_titles = ", ".join([f'{title}' for title in insight_titles]) # Corrected f-string usage
                     meta_summary += "\n- Latest Insights: " + joined_insight_titles

            # --- Add summaries for NEWLY ADDED collections ---
            # Ad Accounts Summary
            ad_accounts_summary = "No Ad Accounts Found."
            if meta_ad_accounts:
                account_names = [get_data(a, 'name', 'Unnamed Account', "Ad account name missing") for a in meta_ad_accounts]
                ad_accounts_summary = f"Ad Accounts: {', '.join(account_names)}"

            # Followers Summary
            followers_summary = "Follower Count: N/A"
            if meta_followers and 'count' in meta_followers:
                followers_count = get_data(meta_followers, 'count', 'N/A', "Follower count missing")
                followers_date = get_data(meta_followers, 'date', 'N/A', "Follower date missing")
                followers_summary = f"Follower Count: {followers_count} (as of {followers_date})"

            # Post Metrics Summary
            post_metrics_summary = "No Recent Post Metrics Found."
            if meta_post_metrics:
                engagement_summary = []
                for metric in meta_post_metrics:
                    likes = get_data(metric, 'likes', 0, "Post likes missing")
                    comments_count = get_data(metric, 'comments', 0, "Post comments count missing")
                    shares = get_data(metric, 'shares', 0, "Post shares missing")
                    engagement_summary.append(f"(Likes: {likes}, Comments: {comments_count}, Shares: {shares})")
                post_metrics_summary = f"Recent Post Engagement (Top 5): {', '.join(engagement_summary)}"

            # Comments Summary
            comments_summary = "No Recent Comments Found."
            if meta_comments:
                comment_snippets = [get_data(c, 'message', '', "Comment message missing")[:75] + '...' for c in meta_comments]
                comments_summary = f"Recent Comment Snippets: \n  - " + "\n  - ".join(comment_snippets)

            # Chat Context Summary
            chat_context_summary = "No Chat Context Summary Available."
            if meta_chat_context and 'summary' in meta_chat_context:
                chat_summary = get_data(meta_chat_context, 'summary', 'N/A', "Chat context summary missing")
                chat_last_updated = get_data(meta_chat_context, 'last_updated', 'N/A', "Chat context last updated missing")
                chat_context_summary = f"Chat Context Summary: {chat_summary} (Updated: {chat_last_updated})"

            # Store Chats Summary
            store_chats_summary = "No Recent Chat Messages Found."
            if store_chats:
                chat_snippets = []
                for chat in store_chats:
                    sender = get_data(chat, 'sender', 'Unknown', "Chat sender missing")
                    message = get_data(chat, 'message', '', "Chat message missing")[:75] + '...'
                    timestamp = get_data(chat, 'timestamp', 'N/A', "Chat timestamp missing")
                    chat_snippets.append(f"[{timestamp}] {sender}: {message}")
                store_chats_summary = f"Recent Chat Snippets: \n  - " + "\n  - ".join(chat_snippets)

            # Sales Correlation Summary
            sales_data_dict = meta_sales_corr.get('sales_data', {}) # Use .get() for nested dict
            daily_sales_available = 'Yes' if sales_data_dict.get('daily_sales') else 'No'
            product_sales_available = 'Yes' if sales_data_dict.get('product_sales') else 'No'

            # --- Enhanced Social Media Detection ---
            social_media_data = active_store.get('social_media', {})
            has_social_media = False
            has_active_marketing = False
            social_media_accounts = []
            marketing_activities = []
            missing_integrations = []
            
            # Check traditional social media fields (URLs in active_stores_cache)
            if social_media_data:
                for platform, value in social_media_data.items():
                    if value and str(value).strip() and "Suggest to Add" not in str(value):
                        has_social_media = True
                        if platform == 'pixel_id':
                            social_media_accounts.append(f"Facebook Pixel ID: {value}")
                            has_active_marketing = True  # Pixel indicates active marketing
                        elif platform == 'facebook':
                            social_media_accounts.append(f"Facebook: {value}")
                        elif platform == 'instagram':
                            social_media_accounts.append(f"Instagram: {value}")
                        else:
                            social_media_accounts.append(f"{platform.replace('_', ' ').title()}: {value}")
            
            # Check Meta API integration data
            has_meta_api_integration = False
            
            if meta_pages and len(meta_pages) > 0:
                has_meta_api_integration = True
                has_active_marketing = True
                page_names = [p.get('name', 'Unknown') for p in meta_pages]
                marketing_activities.append(f"Meta API integrated with {len(meta_pages)} pages: {', '.join(page_names)}")
            
            if meta_posts and len(meta_posts) > 0:
                has_meta_api_integration = True
                has_active_marketing = True
                marketing_activities.append(f"Content posting tracked: {len(meta_posts)} recent posts")
            
            if meta_ad_campaigns:
                active_campaigns = [c for c in meta_ad_campaigns if c.get('status') == 'ACTIVE']
                if active_campaigns:
                    has_meta_api_integration = True
                    has_active_marketing = True
                    marketing_activities.append(f"Running {len(active_campaigns)} active ad campaigns")
                elif meta_ad_campaigns:  # Has campaigns but not active
                    has_meta_api_integration = True
                    marketing_activities.append(f"Ad account connected ({len(meta_ad_campaigns)} historical campaigns)")
            
            if meta_ad_metrics and len(meta_ad_metrics) > 0:
                has_meta_api_integration = True
                has_active_marketing = True
                try:
                    recent_spend = sum(float(m.get('spend', 0)) for m in meta_ad_metrics[:30])
                    if recent_spend > 0:
                        marketing_activities.append(f"Recent ad spend tracked: ${recent_spend:.2f}")
                except:
                    pass
            
            # Check for missing integrations
            if has_social_media and not has_meta_api_integration:
                if 'facebook' in str(social_media_data).lower() or 'instagram' in str(social_media_data).lower():
                    missing_integrations.append("Meta API integration not connected (Facebook/Instagram data not syncing)")
            
            # Build comprehensive social media summary
            social_media_summary = "Social Media & Digital Marketing Presence:\n"
            
            if has_social_media:
                social_media_summary += "\n✓ SOCIAL MEDIA ACCOUNTS DETECTED:\n"
                for account in social_media_accounts:
                    social_media_summary += f"  • {account}\n"
                
                if has_active_marketing and marketing_activities:
                    social_media_summary += "\n✓ ACTIVE MARKETING DETECTED:\n"
                    for activity in marketing_activities:
                        social_media_summary += f"  • {activity}\n"
                    
                    # Add engagement metrics if available
                    if meta_followers and meta_followers.get('count'):
                        social_media_summary += f"  • Follower count: {meta_followers['count']}\n"
                    
                    if meta_post_metrics:
                        total_engagement = sum(
                            m.get('likes', 0) + m.get('comments', 0) + m.get('shares', 0) 
                            for m in meta_post_metrics
                        )
                        social_media_summary += f"  • Recent engagement: {total_engagement} total interactions\n"
                elif missing_integrations:
                    social_media_summary += "\n⚠ INTEGRATION OPPORTUNITIES:\n"
                    for missing in missing_integrations:
                        social_media_summary += f"  • {missing}\n"
                    social_media_summary += "\nNote: Social media accounts exist but may not be fully integrated with D-Unit analytics.\n"
            else:
                social_media_summary += "⚠ No social media accounts found in store profile.\n"
                social_media_summary += "  Consider adding social media presence for better customer engagement.\n"

            # --- CONTEXT STRING CONSTRUCTION ---
            context = f"""
Store Analysis for {store_name} (ID: {store_id}):
Location: {store_location}
Currency: {store_currency}
Business Type: {business_type}
Keywords: {keywords}

Overall Metrics (from active_stores_cache):
- Gross Product Revenue: ${gross_product_revenue:,.2f} (before discounts)
- Total Discounts: ${total_discounts:,.2f} ({discount_rate:.1f}% of gross)
- Net Product Revenue: ${net_product_revenue:,.2f} (after discounts)
- Shipping Revenue: ${shipping_revenue:,.2f}
- Total Revenue: ${total_revenue:,.2f} (net products + shipping)
- Total Orders: {total_orders}
- Average Order Value: ${average_order_value:.2f}
- Key Date (e.g., Last Sale): {last_sale_date}

Customer Overview ({customer_count} total - from store_customers_cache):
- Avg Spend per Customer: {avg_spend:.2f} {store_currency}
- Geographic Distribution:
{self._format_distribution(country_dist)}
- Abandoned Carts: {abandoned_carts} (Total Value: {abandoned_value:.2f} {store_currency})
- Pending Carts: {pending_carts} (Total Value: {pending_value:.2f} {store_currency})
- Coupon Usage:
{self._format_distribution(coupon_dist)}

Product Overview ({products_online_count} online - from product_details_cache):
- Products with Sales: {products_with_sales} products (${total_product_revenue:,.2f} revenue)
- Products without Sales: {products_without_sales} products
- All products have valid pricing and are properly configured
- Store Average Rating: {store_avg_rating} ({store_ratings_count} ratings)
- Category Summary: {category_summary}
- Top 5 Products (by approx. revenue):
{self._format_top_products_from_cache(top_products_data)}
- Sales Trend Data Available: {sales_by_date_exists}

Operational Overview (from store_customers_cache):
- Order Status Distribution:
{self._format_distribution(status_dist)}
- Payment Method Distribution:
{self._format_distribution(payment_dist)}
- Shipping Method Distribution:
{self._format_distribution(shipping_dist)}

Market Comparison for Top Products:
{chr(10).join(marketplace_insights) if marketplace_insights else "No marketplace comparison data available for top products."}

{social_media_summary}

Meta Platform Integration:
{meta_summary}
{ad_accounts_summary}
{followers_summary}
{post_metrics_summary}
{comments_summary}

{chat_context_summary}
{store_chats_summary}

Sales Correlation Summary (from meta_sales_correlation):
- Daily Sales Data Available: {daily_sales_available}
- Product Sales Data Available: {product_sales_available}

Business Analysis Areas (Consider these based on the data):
- Identify high-value customer segments based on country or spending.
- Analyze reasons for abandoned/pending carts and suggest recovery strategies.
- Evaluate top product performance against market data and sales trends.
- Assess operational efficiency based on order status and payment/shipping methods.
- Leverage Meta insights, audience data, and ad performance for marketing or product strategies.
- Analyze potential correlations between Meta activity/ads and sales patterns.

{self._format_distribution(shipping_dist)}
- Payment Methods:
{self._format_distribution(payment_dist)}

Market & Competitor Context:
- Based *only* on the information provided above (without performing external searches):
  1. Identify the likely market position for {store_name} in the {business_type} industry
  2. Outline potential direct competitor types based on the product categories
  3. Suggest areas where the store may have competitive advantages or disadvantages
  4. Identify key differentiating factors compared to typical competitors
  5. Leverage insights from customer interactions (chats, comments) and social engagement (followers, post metrics)

{meta_summary}
""" 
            return context
        except Exception as e:
            self.logger.error(f"Error preparing global context for store {store_data.get('store_id', 'Unknown')}: {str(e)}")
            self.logger.exception("Traceback for preparing global context error:")
            return "Error preparing analysis context. Please check logs."

    def _format_distribution(self, data: Dict) -> str:
        """Format distribution data into a readable string with percentages. Handles potential non-numeric values gracefully."""
        try:
            if not data:
                return "  No data available"
            
            # Filter out non-numeric values before summing
            numeric_values = [v for v in data.values() if isinstance(v, (int, float))]
            total = sum(numeric_values)
            
            # Sort items by numeric value in descending order, put non-numeric last
            def sort_key(item):
                key, value = item
                if isinstance(value, (int, float)):
                    return (-value, key) # Sort numerics descending by value, then key
                return (float('inf'), key) # Put non-numerics at the end, sorted by key

            sorted_items = sorted(data.items(), key=sort_key)
            
            # Format each item, avoiding meaningless percentages
            formatted_items = []
            for key, value in sorted_items:
                if isinstance(value, (int, float)):
                    if total > 0:
                        percentage = (value / total * 100)
                        if percentage >= 0.1:  # Only show percentages >= 0.1%
                            formatted_items.append(f"  - {key}: {value} ({percentage:.1f}%)")
                        else:
                            formatted_items.append(f"  - {key}: {value} (minimal share)")
                    else:
                        formatted_items.append(f"  - {key}: {value}")
                else:
                    formatted_items.append(f"  - {key}: {value}") # Handle non-numeric display without meaningless %
            
            return "\n".join(formatted_items)
            
        except Exception as e:
            self.logger.error(f"Error formatting distribution: {str(e)}")
            return "  Error formatting data"

    # Updated helper to use product data directly from the cache structure
    def _format_top_products_from_cache(self, top_products_data: List[Dict]) -> str:
        """Format top products information from cached product data with clear status indicators."""
        try:
            if not top_products_data:
                return "  No top product data available from cache."
            
            result = []
            for i, product in enumerate(top_products_data, 1):
                name = product.get('name', 'Unknown Product')
                revenue = float(product.get('revenue', 0) or 0)
                units_sold = int(product.get('sales_units', 0) or 0)
                current_price = float(product.get('current_price', 0) or 0)
                catalog_price = float(product.get('catalog_price', 0) or 0)
                stock = product.get('current_stock')
                
                # Clear status indicators
                if revenue > 0:
                    sales_status = f"✓ ACTIVE SELLER - Revenue: ${revenue:,.2f} from {units_sold} units"
                    price_status = f"Selling at: ${current_price:,.2f}"
                else:
                    sales_status = "→ NO SALES YET"
                    price_status = f"Listed at: ${current_price:,.2f}" if current_price > 0 else "No price set"
                
                # Stock status
                if stock is not None and isinstance(stock, (int, float)):
                    stock_status = f"In Stock: {int(stock)} units" if stock > 0 else "Out of Stock"
                else:
                    stock_status = "Stock not tracked"
                
                # Build product entry
                result.append(
                    f"{i}. {name}\n"
                    f"   {sales_status}\n"
                    f"   {price_status}\n"
                    f"   Catalog Price: ${catalog_price:,.2f}\n"
                    f"   {stock_status}"
                )
            
            return "\n".join(result)
        except Exception as e:
            self.logger.error(f"Error formatting top products from cache: {str(e)}")
            return "  Error formatting product data"

    def get_global_analysis(self, context: str) -> Dict:
        """Get comprehensive analysis using gpt-4.1-mini with enhanced processing"""
        default_error_message = "Analysis not available for this section."
        if not context or context == "Error preparing analysis context. Please check logs.":
            self.logger.warning("Empty or error context provided for global analysis, returning defaults.")
            return {
                "summary": default_error_message,
                "metrics": default_error_message,
                "customer_analysis": default_error_message,
                "product_performance": default_error_message,
                "market_position": default_error_message,
                "recommendations": default_error_message,
                "generated_at": datetime.now().isoformat()
            }
        try:
            # Stricter Prompt Engineering:
            system_prompt = """You are a Chief Strategy Officer level consultant with 30 years of experience analyzing e-commerce businesses holistically. 

CRITICAL DATA INTERPRETATION RULES:
1. USE PROVIDED METRICS EXACTLY: When you see "Average Order Value: X.XX CURRENCY" in the data, use that EXACT value in your analysis. Do NOT recalculate or change these metrics.
2. REVENUE TERMINOLOGY (CRITICAL):
   - "Gross Product Revenue": Revenue from product sales BEFORE any discounts are applied
   - "Net Product Revenue": Revenue from product sales AFTER discounts are applied  
   - "Total Discounts": The amount of discounts given (Gross - Net)
   - "Discount Rate": Percentage of gross revenue given as discounts
   - "Shipping Revenue": Revenue from shipping charges
   - "Total Revenue": Net Product Revenue + Shipping Revenue
   - Always specify which type of revenue you're discussing
   - When analyzing profitability or margins, use NET revenue figures
   - When analyzing pricing strategy, reference BOTH gross and net to show discount impact
3. METRIC CONSISTENCY: If a metric appears multiple times in the context (e.g., Average Order Value), it should have the SAME value throughout your entire response.
4. PRODUCT PRICING: 
   - If a product shows meaningful price values, it HAS a valid price and IS properly priced
   - If a product shows meaningful revenue, it HAS been selling at a valid price
   - NEVER claim products lack pricing if they show:
     * Meaningful current prices
     * Meaningful sale prices  
     * Meaningful revenue figures
     * Meaningful catalog prices
   - The presence of sales/revenue is PROOF that pricing exists and works
   - Some products may show catalog_price different from selling price due to promotions, but this is NORMAL
   - AVOID using phrases like "greater than 0", "> 0", "more than zero" - instead use descriptive language like "meaningful sales", "active pricing", "established revenue"
5. SOCIAL MEDIA PRESENCE: 
   - If social_media field contains URLs (Facebook, Instagram, etc.) or any kind of data like users or numbers, the store HAS social media accounts
   - If pixel_id exists, the store IS using Facebook advertising
   - Absence of Meta API data (posts, campaigns) means integration is missing, NOT that they don't use social media
   - Always distinguish between "has social media accounts" vs "has Meta API integration"
6. BASE ALL CONCLUSIONS ON PROVIDED DATA: Do not make assumptions about missing data being indicative of absence.

PRODUCT ANALYSIS RULES:
1. NEVER claim products have "placeholder prices" or "lack proper pricing" if they show:
   - Any price value > 0 (current_price, catalog_price, or variation_price)
   - Any revenue > 0 (which proves they had valid prices when sold)
   - Price history data (showing historical sales)

2. Distinguish between:
   - "Products with sales": Have revenue > 0, units_sold > 0
   - "Products without sales": Have revenue = 0, but may still have valid prices
   - BOTH categories can have proper pricing

3. When you see "NO SALES YET" - this means the product hasn't sold, NOT that it lacks pricing
4. When you see "Listed at: $X" - this IS a valid price, just without sales yet

5. AVOID phrases like:
   - "placeholder prices"
   - "prices need to be set"
   - "promotional prices instead of real commercial prices"
   
   UNLESS the product literally shows $0 or "No price set"

IMPORTANT PRICING INTERPRETATION:
- If you see products with substantial revenues like "336,732.00" for "Fabulash PRO Serum 3ml", this product CLEARLY has proper pricing
- Products generating significant revenue are OBVIOUSLY priced correctly and actively selling
- Do NOT confuse catalog_price with actual selling price - stores often sell at different prices than catalog
- If top products show meaningful revenue figures, the store's pricing strategy is WORKING
- Focus on describing pricing as "established", "active", "meaningful" rather than using numerical comparisons

Synthesize ALL provided data points (store metrics, customer behavior, product performance, market comparisons, social media presence, Meta platform activity, sales correlations) into a cohesive strategic assessment.

YOU MUST GENERATE ALL 8 SECTIONS LISTED BELOW. DO NOT OMIT ANY SECTION. Structure your response strictly as follows:
1. Executive Summary: High-level strategic overview and most critical findings.
2. Key Performance Indicators (KPIs): Use the EXACT metrics provided in the context data. Format numbers with thousands separators but DO NOT change the values.
3. Customer Deep Dive: Analyze segmentation, behavior patterns (including cart abandonment), and geographic factors.
4. Product Portfolio Analysis: 
   - Assess top performers and category health, prioritizing products with actual sales performance
   - VERIFY pricing effectiveness by looking at revenue data
   - If products show meaningful revenue, they ARE priced correctly and actively selling
   - Focus on actual performance and established sales patterns, not theoretical pricing issues
   - When analyzing product performance, prioritize discussing products that show meaningful sales over products with no revenue
   - Use descriptive language about product performance rather than numerical comparisons
5. Competitive & Market Positioning: Evaluate standing based on product comparisons and market context.
6. Social Media Strategy: Accurately reflect ACTUAL presence shown in data. If URLs exist in social_media field, acknowledge them. If pixel_id exists, they ARE advertising. Distinguish between having accounts vs API integration.
7. Meta Platform & Correlation Analysis: If Meta API data is missing but social accounts exist, recommend integration, don't claim absence of social media.
8. Strategic Action Plan: Detail specific, prioritized, data-driven recommendations for growth, efficiency, and risk mitigation."""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini", 
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Analyze the following store data and provide insights:\n\n{context}"}
                ],
                max_completion_tokens=3000,
                n=1,
                presence_penalty=0.0,
                frequency_penalty=0.0
            )
            if not response.choices or not response.choices[0].message.content:
                self.logger.error("Invalid or empty response from GPT")
                analysis_text = ""
            else:
                analysis_text = response.choices[0].message.content
            # Process and structure the response
            sections = self._parse_analysis_sections(analysis_text)
            # Define expected sections and their corresponding output keys
            expected_sections = {
                "Executive Summary": "summary",
                "Key Performance Indicators (KPIs)": "metrics", # Match prompt
                "Customer Deep Dive": "customer_analysis",      # Match prompt
                "Product Portfolio Analysis": "product_performance", # Match prompt
                "Competitive & Market Positioning": "market_position", # Match prompt
                "Social Media Strategy": "social_media_strategy", # NEW SECTION
                "Strategic Action Plan": "recommendations"        # Match prompt
                # Note: Meta analysis section is not directly mapped to a single field
            }
            final_analysis = {}
            for section_name, output_key in expected_sections.items():
                section_content = sections.get(section_name, "").strip()
                if not section_content:
                    self.logger.warning(f"Missing or empty section '{section_name}' in GPT response for context starting with: {context[:100]}...")
                    final_analysis[output_key] = default_error_message
                else:
                    final_analysis[output_key] = section_content
            final_analysis["generated_at"] = datetime.now().isoformat()
            # After parsing the GPT/AI response for market position, replace or prepend the 'Market Data' line with general advice and a DeepSearch recommendation.
            if 'market_position' in final_analysis and isinstance(final_analysis['market_position'], str):
                advice_lines = [
                    "General Market Position Advice:",
                    "- Focus on your unique value proposition and customer experience.",
                    "- Regularly review your product portfolio for gaps and opportunities.",
                    "- Monitor pricing and promotions of similar products in your region.",
                    "- Use the Chat Console in D-Unit with DeepSearch mode to get more insights and ask questions about your competitors.",
                    "- Consider customer feedback and reviews as a source of competitive advantage.",
                    ""
                ]
                # Remove any existing 'Market Data:' line from the section
                lines = [line for line in final_analysis['market_position'].split('\n') if not line.strip().startswith('Market Data:')]
                final_analysis['market_position'] = '\n'.join(advice_lines + lines)
            return final_analysis
        except Exception as e:
            self.logger.error(f"Error getting global analysis: {str(e)}")
            # Return dict with default error messages if exception occurs
            return {
                "summary": default_error_message,
                "metrics": default_error_message,
                "customer_analysis": default_error_message,
                "product_performance": default_error_message,
                "market_position": default_error_message,
                "social_media_strategy": default_error_message, # Added default for new section
                "recommendations": default_error_message,
                "generated_at": datetime.now().isoformat()
            }
    
    def _verify_metrics_consistency(self, analysis: Dict, calculated_metrics: Dict) -> Dict:
        """Verify and fix any metric inconsistencies in the AI analysis"""
        
        # Extract numbers from the metrics section of the analysis
        metrics_text = analysis.get('metrics', '')
        
        # Check for AOV mentions
        import re
        aov_pattern = r'Average Order Value[:\s]+([0-9,]+(?:\.\d+)?)'
        matches = re.finditer(aov_pattern, metrics_text, re.IGNORECASE)
        
        for match in matches:
            found_value_str = match.group(1).replace(',', '')
            try:
                found_value = float(found_value_str)
                correct_value = calculated_metrics['average_order_value']
                
                # If the value is different, log it
                if abs(found_value - correct_value) > 0.01:
                    self.logger.warning(
                        f"GPT reported AOV {found_value}, "
                        f"but calculated AOV is {correct_value}"
                    )
            except ValueError:
                continue
        
        return analysis
    
    def _verify_product_statements(self, analysis: Dict, store_data: Dict) -> Dict:
        """Verify product-related statements are accurate"""
        
        product_cache = store_data.get('product_details_cache', {})
        products = product_cache.get('products', [])
        
        # Check if analysis mentions pricing issues
        product_analysis = analysis.get('product_performance', '')
        
        # Flag incorrect statements
        problematic_phrases = [
            'placeholder price',
            'promotional prices instead of real commercial prices',
            'prices need to be set',
            'lack proper pricing'
        ]
        
        if any(phrase in product_analysis.lower() for phrase in problematic_phrases):
            # Check if ANY products have valid prices
            products_with_prices = sum(1 for p in products if float(p.get('current_price', 0)) > 0)
            products_with_revenue = sum(1 for p in products if float(p.get('revenue', 0)) > 0)
            
            if products_with_prices > 0 or products_with_revenue > 0:
                self.logger.warning(
                    f"AI incorrectly claimed pricing issues when {products_with_prices} "
                    f"products have valid prices and {products_with_revenue} have sales history"
                )
                
                # Auto-correct the analysis
                for phrase in problematic_phrases:
                    if phrase in product_analysis.lower():
                        product_analysis = product_analysis.replace(
                            phrase,
                            'properly configured prices'
                        )
                        # Also replace common variations
                        product_analysis = product_analysis.replace(
                            'promotional prices or placeholder prices',
                            'properly configured prices'
                        )
                
                analysis['product_performance'] = product_analysis
        
        return analysis
    
    def _parse_analysis_sections(self, text: str) -> Dict[str, str]:
        """Parse analysis text into structured sections based on numbered list"""
        sections = {}
        current_section = "Header" # Start with a placeholder
        current_content = []
        expected_headers = [
            "Executive Summary",
            "Key Performance Indicators (KPIs)",
            "Customer Deep Dive",
            "Product Portfolio Analysis",
            "Competitive & Market Positioning",
            "Social Media Strategy", # Added new section header
            "Meta Platform & Correlation Analysis",
            "Strategic Action Plan"
        ]
        header_pattern = re.compile(r"^\s*\d+\.\s+(.+?):?\s*$") # Matches lines like "1. Executive Summary:"
        try:
            lines = text.split('\n')
            for line in lines:
                match = header_pattern.match(line)
                if match:
                    potential_header = match.group(1).strip()
                    # Check if it matches one of the expected headers
                    found_header = None
                    for expected in expected_headers:
                        if expected.lower() in potential_header.lower():
                            found_header = expected # Use the canonical name
                            break
                    if found_header:
                        if current_content:
                            sections[current_section] = '\n'.join(current_content).strip()
                        current_section = found_header
                        current_content = []
                        # Optional: Include the header line itself in the content?
                        # current_content.append(line.strip()) 
                        continue # Skip adding header line to content itself
                # Append non-header lines to current section
                if line.strip():
                    current_content.append(line.strip())
            # Add the last section
            if current_content and current_section != "Header":
                sections[current_section] = '\n'.join(current_content).strip()
            # Add default message for any expected section not found
            # for expected in expected_headers:
            #     if expected not in sections:
            #         self.logger.warning(f"Expected section '{expected}' not found in LLM output.")
            #         sections[expected] = "Analysis not available for this section."
            return sections
        except Exception as e:
            self.logger.error(f"Error parsing analysis sections: {str(e)}")
            return {"Error": "Failed to parse analysis sections"}

    def _summarize_context(self, context: str) -> str:
        """Summarize context if it's too long"""
        try:
            # Use GPT to create a shorter summary
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini", 
                messages=[
                    # Enhance system prompt for strategic summarization
                    {"role": "system", "content": "You are an executive briefing analyst. Condense the following detailed business report into a concise summary suitable for strategic review. Ruthlessly prioritize; retain only the most critical metrics, trend indicators, key findings, and core data points essential for understanding the overall business situation and making informed decisions. Eliminate redundancy and low-impact details."},
                    {"role": "user", "content": context}
                ],
                # temperature=0.5, # Removed by previous edit
                # Change max_tokens to max_completion_tokens
                max_completion_tokens=2000
            )
            
            summary_content = response.choices[0].message.content if response.choices and response.choices[0].message else None
            return summary_content if summary_content is not None else "" # Return empty string if None
        except Exception as e:
            self.logger.error(f"Error summarizing context: {str(e)}")
            # If summarization fails, truncate manually
            words = context.split()
            return ' '.join(words[:2500]) + "..."

    def _extract_impact_metrics(self, analysis: str) -> Dict:
        """Extract impact metrics from promotion analysis"""
        try:
            metrics = {
                'sales_increase': [],
                'revenue_impact': [],
                'customer_response': []
            }
            
            for line in analysis.split('\n'):
                line = line.lower()
                if 'sales' in line and any(x in line for x in ['increase', 'growth', 'higher']):
                    metrics['sales_increase'].append(line.strip())
                elif 'revenue' in line or 'profit' in line:
                    metrics['revenue_impact'].append(line.strip())
                elif 'customer' in line and ('response' in line or 'engagement' in line):
                    metrics['customer_response'].append(line.strip())
            
            return metrics
        except Exception as e:
            self.logger.error(f"Error extracting impact metrics: {str(e)}")
            return {}

    def _normalize_price_to_usd(self, amount: float, currency: str, marketplace: str = 'internal') -> float:
        """Convert price to USD using current exchange rates"""
        try:
            # Exchange rates (you might want to update these or fetch them dynamically)
            rates = {
                'UYU': 40.0,    # Uruguayan Peso
                'ARS': 1170.0,  # Argentine Peso
                'PEN': 3.7,     # Peruvian Sol
                'USD': 1.0
            }
            
            if currency not in rates:
                self.logger.warning(f"Unknown currency {currency}, returning original amount")
                return amount
                
            # Convert to USD
            usd_amount = amount / rates[currency]
            
            # Round to 2 decimal places
            return round(usd_amount, 2)
            
        except Exception as e:
            self.logger.error(f"Error normalizing price to USD: {str(e)}")
            return amount

    def set_target_stores(self, store_ids: List[str]):
        """Set specific store IDs to analyze"""
        self.target_store_ids = set(str(store_id) for store_id in store_ids)
        self.logger.info(f"Set target stores for analysis: {', '.join(self.target_store_ids)}")

    def analyze_stores(self, store_ids: Optional[List[str]] = None):
        """Analyze stores using cache data, optionally filtering by specific IDs."""
        try:
            if store_ids:
                self.logger.info(f"Starting analysis for specific stores: {', '.join(store_ids)}")
                # Store IDs are strings, no integer conversion needed for active_stores_cache
                # int_store_ids = [] # Removed
                # for sid in store_ids: # Removed
                #     try: # Removed
                #         int_store_ids.append(int(sid)) # Removed
                #     except (ValueError, TypeError): # Removed
                #         continue # Removed
                
                # Query using string IDs against _id field
                stores_query = {"_id": {'$in': store_ids}}
            else:
                self.logger.info("Starting analysis for all stores")
                # If analyzing all, we should fetch IDs from the cache
                # This mode might be better handled by process_global_analysis now.
                # For now, keeping the empty query means it might fetch all from cache if store_ids is None.
                stores_query = {}

            # Get stores from active_stores_cache in the analysis database
            stores_collection = self.analysis_db['active_stores_cache']
            
            # Get stores to analyze from the cache collection
            store_batch = list(stores_collection.find(stores_query))
            total_stores = len(store_batch)
            
            if total_stores == 0:
                self.logger.warning("No stores found matching the criteria")
                return
            
            self.logger.info(f"Found {total_stores} stores to analyze")
            processed_count = 0
            skipped_count = 0
            
            # Add data quality check
            if len(store_batch) > 0 and self.logger.level <= logging.DEBUG:
                # Debug first store to check data structure
                first_store_id = store_batch[0].get('_id')
                self.debug_store_data_structure(first_store_id)
            
            # Process each store document fetched from the cache
            for store_cache_doc in store_batch:
                # Get ID and Name from the cache document
                store_id = store_cache_doc.get('_id')
                store_name = store_cache_doc.get('name', 'Unknown')
                # Update logging
                self.logger.info(f"\n{'='*50}\nProcessing store: {store_name} (ID: {store_id})\n{'='*50}")
                
                try:
                    # Check if we have a recent complete analysis
                    cached_analysis = self.analysis_db[self.analysis_collection].find_one({"_id": str(store_id)})
                    
                    if cached_analysis:
                        # Check if analysis is complete and recent
                        required_fields = ['summary', 'metrics', 'customer_analysis', 'product_performance', 'market_position', 'recommendations']
                        has_all_fields = all(field in cached_analysis.get('analysis', {}) for field in required_fields)
                        
                        last_updated = cached_analysis.get('metadata', {}).get('last_updated')
                        if isinstance(last_updated, str):
                            last_updated = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                        elif isinstance(last_updated, datetime) and last_updated.tzinfo is None:
                            last_updated = last_updated.replace(tzinfo=timezone.utc)
                        
                        current_time = datetime.now(timezone.utc)
                        
                        if has_all_fields and last_updated and (current_time - last_updated) < timedelta(days=7):
                            self.logger.info(f"Skipping store {store_id} - Complete analysis exists from {last_updated}")
                            skipped_count += 1
                            continue
                        else:
                            if not has_all_fields:
                                self.logger.info(f"Reanalyzing store {store_id} - Incomplete analysis")
                            else:
                                self.logger.info(f"Reanalyzing store {store_id} - Analysis older than 7 days")
                    
                    # Get complete store data using store_id
                    store_data = self._get_store_complete_data(store_id)
                    if not store_data:
                        self.logger.warning(f"Could not get complete data for store {store_id}")
                        continue

                    # Remove old logging based on previous store_data structure
                    # self.logger.info(f"Retrieved data for store {store_id}:") 
                    # self.logger.info(f"- Customers: {len(store_data['customers'])}") 
                    # self.logger.info(f"- Orders: {len(store_data['orders'])}") 
                    # self.logger.info(f"- Products: {len(store_data['products'])}") 

                    # Prepare global context (uses the new store_data structure)
                    context = self._prepare_global_context(store_data)

                    # Get global analysis
                    analysis = self.get_global_analysis(context)

                    # Extract shipping analysis for shipping recommendations
                    shipping_analysis_text = ""
                    if "product_performance" in analysis:
                        shipping_analysis_text = analysis.get("product_performance", "")
                    
                    # Get shipping recommendations
                    shipping_recommendations = []
                    if shipping_analysis_text:
                        self.logger.info(f"Generating shipping recommendations for store {store_id}")
                        shipping_recommendations = self._get_shipping_recommendations(store_id, shipping_analysis_text)

                    # Extract competitor analysis
                    analysis_sections = self._parse_analysis_sections(analysis.get("market_position", ""))
                    competitor_analysis = self._extract_competitor_analysis_from_analysis(analysis_sections)
                    
                    # Prepare complete analysis dictionary with nested fields
                    # This ensures we have a properly structured object before updating MongoDB
                    complete_analysis = dict(analysis)  # Create a copy to avoid modifying the original
                    
                    # Add shipping_analysis as a nested dictionary
                    complete_analysis["shipping_analysis"] = {
                        "analysis_text": shipping_analysis_text,
                        "recommendations": shipping_recommendations
                    }
                    
                    # Add competitor_analysis as a string field
                    complete_analysis["competitor_analysis"] = competitor_analysis
                    
                    # Prepare the complete $set object for the update
                    update_document = {
                        "analysis": complete_analysis,
                        "metrics": store_data.get('active_store_cache', {}).get('metrics', {}),
                        "metadata": {
                            "customer_count": store_data.get('customer_details_cache', {}).get('total_customers', 0),
                            "order_count": store_data.get('active_store_cache', {}).get('metrics', {}).get('total_orders', 0),
                            "product_count": store_data.get('product_details_cache', {}).get('product_count', 0),
                            "last_updated": datetime.now(timezone.utc),
                            "shipping_analysis_updated": datetime.now(timezone.utc),
                            "competitor_analysis_updated": datetime.now(timezone.utc)
                        }
                    }
                    
                    # Store analysis results with the properly constructed document
                    self.analysis_db[self.analysis_collection].update_one(
                        {"_id": store_id}, # Filter by string store_id
                        {"$set": update_document},
                        upsert=True
                    )
                    
                    processed_count += 1
                    self.logger.info(f"✓ Completed analysis for store {store_id}")
                except Exception as e:
                    self.logger.error(f"✗ Error analyzing store {store_id}: {str(e)}")
                    continue
            
            self.logger.info(f"\nAnalysis Complete!")
            self.logger.info(f"Final Results:")
            self.logger.info(f"- Total Stores Found: {total_stores}")
            self.logger.info(f"- Successfully Processed: {processed_count}")
            self.logger.info(f"- Skipped (Recent Analysis): {skipped_count}")
            
        except Exception as e:
            self.logger.error(f"Error in analyze_stores: {str(e)}")
            raise

    def _get_shipping_recommendations(self, store_id: str, shipping_analysis_text: str) -> List[str]:
        """Generate shipping recommendations based on the shipping analysis text.
        
        Args:
            store_id: Store ID (for logging/context)
            shipping_analysis_text: Text from shipping analysis section
            
        Returns:
            List of recommendation strings
        """
        try:
            self.logger.info(f"Generating shipping recommendations for store {store_id}")

            # --- NEW LOGIC: Always recommend 3 best-fit unused shipping methods ---
            all_methods = self.get_all_shipping_methods()
            all_method_names = set(m['name'] for m in all_methods if m.get('name'))
            
            # Get store country to filter shipping methods appropriately
            store_data = self.analysis_db['active_stores_cache'].find_one({'_id': store_id})
            store_country = None
            if store_data and 'country' in store_data:
                store_country = store_data['country'].get('name', '').lower() if isinstance(store_data['country'], dict) else str(store_data['country']).lower()
            
            # Filter out AEX for non-Paraguay stores (AEX is Paraguay-specific)
            if store_country != 'paraguay':
                all_method_names = {name for name in all_method_names if name.upper() != 'AEX'}
                self.logger.info(f"Store {store_id} is in {store_country}, filtering out AEX shipping method")
            
            store_doc = self.analysis_db['store_customers_cache'].find_one({'_id': store_id})
            used_methods = set()
            if store_doc and 'shipping_method_distribution' in store_doc:
                used_methods = set(store_doc['shipping_method_distribution'].keys())
            unused_methods = list(all_method_names - used_methods)
            best_fit_unused = sorted(unused_methods)[:3]
            expand_recommendations = [
                f"Consider enabling the shipping method: '{method}' to expand delivery options." for method in best_fit_unused
            ] if best_fit_unused else [
                "All available shipping methods are already enabled for this store."
            ]

            # --- NEW LOGIC: Inject relevant active FAQ context ---
            faqs = self.get_active_faqs()
            relevant_faqs = []
            store_data = self.analysis_db['active_stores_cache'].find_one({'_id': store_id})
            has_categories = False
            if store_data:
                product_cache = self.analysis_db['product_details_cache'].find_one({'_id': store_id})
                if product_cache and product_cache.get('category_summary'):
                    has_categories = True
            for faq in faqs:
                q = faq.get('question', '').lower()
                if 'categor' in q and not has_categories:
                    relevant_faqs.append(faq)
            faq_context = ""
            if relevant_faqs:
                faq_context = "\n\nRelevant FAQ(s) for this store (consider these in your recommendations):\n" + "\n".join([
                    f"Q: {faq['question']}\nA: {faq['answer']}" for faq in relevant_faqs
                ])

            # --- CLARIFIED PROMPT CONSTRUCTION ---
            prompt_user_content = (
                "Store's current shipping methods: " + (", ".join(sorted(used_methods)) if used_methods else "None") +
                "\nAvailable shipping methods not yet enabled: " + (", ".join(best_fit_unused) if best_fit_unused else "None") +
                "\n\nShipping analysis:\n" + shipping_analysis_text +
                (faq_context if faq_context else "") +
                "\n\nBased on the above, provide 3-5 specific, actionable recommendations to improve shipping efficiency, reduce costs, or enhance customer experience."
            )
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": """You are a shipping and logistics consultant. 
                    Based on the provided context, generate 3-5 specific, actionable recommendations 
                    to improve shipping efficiency, reduce costs, or enhance customer experience.
                    
                    IMPORTANT: NEVER recommend AEX shipping method for stores outside of Paraguay, as AEX is a Paraguay-specific shipping service.
                    
                    FORMAT YOUR RESPONSE AS A SIMPLE PYTHON LIST OF STRINGS, one recommendation per line.
                    Example:
                    [
                        \"Consider adding a second shipping carrier to reduce dependency on a single provider.\",
                        \"Implement free shipping for orders over $50 to reduce cart abandonment.\",
                        \"Add expedited shipping options for premium customers.\"
                    ]
                    """},
                    {"role": "user", "content": prompt_user_content}
                ],
                max_tokens=600
            )
            recommendations_text = response.choices[0].message.content if response.choices and response.choices[0].message else None
            if not recommendations_text:
                self.logger.warning(f"Empty shipping recommendations response for store {store_id}")
                ai_recommendations = [
                    "Consider diversifying shipping carriers.", 
                    "Analyze shipping costs for potential savings.", 
                    "Offer free shipping thresholds based on order value."
                ]
            else:
                try:
                    cleaned_text = recommendations_text.strip()
                    if cleaned_text.startswith("[") and cleaned_text.endswith("]"):
                        import ast
                        ai_recommendations = ast.literal_eval(cleaned_text)
                        if not isinstance(ai_recommendations, list):
                            ai_recommendations = []
                    else:
                        lines = [line.strip() for line in recommendations_text.split('\n') if line.strip()]
                        ai_recommendations = [line for line in lines 
                            if not line.startswith("```") and 
                               not line.endswith("```") and
                               not line.startswith("[") and
                               not line.endswith("]") and
                               not "recommendations" in line.lower()]
                except Exception as e:
                    self.logger.warning(f"Failed to parse shipping recommendations as a list: {str(e)}")
                    ai_recommendations = []
            if not ai_recommendations:
                ai_recommendations = [
                    "Consider diversifying shipping carriers.", 
                    "Analyze shipping costs for potential savings.", 
                    "Offer free shipping thresholds based on order value."
                ]
            return expand_recommendations + ai_recommendations
        except Exception as e:
            self.logger.error(f"Error getting shipping recommendations: {str(e)}")
            return [
                "Consider diversifying shipping carriers.", 
                "Analyze shipping costs for potential savings.", 
                "Offer free shipping thresholds based on order value."
            ]

def main():
    # Initialize logger for main
    logger = logging.getLogger(__name__)
    
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Run market analysis for stores')
    parser.add_argument('--stores', 
                       nargs='+', 
                       help='Specific store IDs to analyze (e.g., --stores 1236 961 895)',
                       default=None)
    
    args = parser.parse_args()
    
    try:
        analyzer = MarketAnalyzer()
        
        if args.stores:
            # Analyze specific stores
            logger.info(f"Starting analysis for specific stores: {', '.join(args.stores)}")
            analyzer.analyze_stores(store_ids=args.stores)
        else:
            # Default behavior - analyze all stores
            logger.info("Starting analysis for all stores.")
            analyzer.analyze_stores(store_ids=None)
        
        logger.info("Analysis completed!")
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise

if __name__ == "__main__":
    main()