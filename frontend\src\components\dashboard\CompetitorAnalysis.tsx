import React from 'react';
import { Card, CardContent, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { getLocalizedDbText } from '../../utils/localizationUtils';
import type { Analysis } from '../../services/storeService';
import type { ExtendedAnalysis } from '../Dashboard';

// Define props for the revamped component
interface CompetitorAnalysisProps {
  // Pass the whole analysis object
  analysisData: Analysis | ExtendedAnalysis | Record<string, unknown> | null | undefined;
}

// Basic styled card to match other dashboard elements
const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper, // White background
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1], // Subtle shadow
  border: `1px solid ${theme.palette.divider}`,
  height: '100%', // Ensure cards take full height if in a grid
}));

const CompetitorAnalysis: React.FC<CompetitorAnalysisProps> = ({ analysisData }) => {
  const { i18n, t } = useTranslation();
  const currentLang = i18n.language;

  // Get localized text using the helper
  const marketPositionText = getLocalizedDbText(analysisData, 'market_position', currentLang);
  const competitorAnalysisText = getLocalizedDbText(analysisData, 'competitor_analysis', currentLang);

  const hasMarketPosition = marketPositionText && marketPositionText.trim() !== '';
  const hasCompetitorAnalysis = competitorAnalysisText && competitorAnalysisText.trim() !== '';

  // If no data at all, show a placeholder
  if (!hasMarketPosition && !hasCompetitorAnalysis) {
    return (
      <StyledCard>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('dashboard.competition.mainTitle', 'Competition Analysis')}
          </Typography>
          <Typography color="text.secondary">
            {t('dashboard.competition.noData', 'No competition data available for this store yet.')}
          </Typography>
        </CardContent>
      </StyledCard>
    );
  }

  return (
    <div className="space-y-6"> {/* Use space-y for vertical spacing */}
      {/* Market Position Analysis Card */}
      {hasMarketPosition && (
        <StyledCard>
          <CardContent>
            <Typography variant="h6" component="div" gutterBottom>
              {t('dashboard.competition.marketPositionTitle', 'Market Position Analysis')}
            </Typography>
            {/* Render the analysis text. Handle potential multiline text */}
            <Typography variant="body2" color="text.secondary" style={{ whiteSpace: 'pre-wrap' }}>
              {marketPositionText}
            </Typography>
          </CardContent>
        </StyledCard>
      )}

      {/* Competitor Details Card */}
      {hasCompetitorAnalysis && (
        <StyledCard>
          <CardContent>
            <Typography variant="h6" component="div" gutterBottom>
              {t('dashboard.competition.competitorDetailsTitle', 'Competitor Details')}
            </Typography>
            {/* Render the analysis text. Handle potential multiline text */}
            <Typography variant="body2" color="text.secondary" style={{ whiteSpace: 'pre-wrap' }}>
              {competitorAnalysisText}
            </Typography>
          </CardContent>
        </StyledCard>
      )}
    </div>
  );
};

export default CompetitorAnalysis; 