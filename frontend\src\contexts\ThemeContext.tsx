import React, { createContext, useState, useMemo, useContext, useEffect, ReactNode } from 'react';
import { ThemeProvider as MuiThemeProvider, CssBaseline, PaletteMode, createTheme } from '@mui/material';
import { useMediaQuery } from '@mui/material';
import { getAppTheme } from '../theme'; // Import the theme factory
import { logger } from '../utils/logger';

interface ThemeContextType {
  toggleColorMode: () => void;
  mode: PaletteMode;
}

const ThemeContext = createContext<ThemeContextType>({
  toggleColorMode: () => {},
  mode: 'light',
});

interface AppThemeProviderProps {
  children: ReactNode;
}

export const AppThemeProvider: React.FC<AppThemeProviderProps> = ({ children }) => {
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  const [mode, setMode] = useState<PaletteMode>(() => {
    try {
      const storedMode = window.localStorage.getItem('colorMode') as PaletteMode | null;
      if (storedMode) {
        return storedMode;
      }
      return prefersDarkMode ? 'dark' : 'light';
    } catch {
      // Fallback if localStorage is unavailable
      return prefersDarkMode ? 'dark' : 'light';
    }
  });

  useEffect(() => {
    try {
      window.localStorage.setItem('colorMode', mode);
    } catch {
      logger.error("LocalStorage is not available for saving theme preference.");
    }
    // Apply class to HTML element for Tailwind
    const root = window.document.documentElement;
    root.classList.remove(mode === 'dark' ? 'light' : 'dark');
    root.classList.add(mode);
  }, [mode]);

  const colorMode = useMemo(
    () => ({
      toggleColorMode: () => {
        setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
      },
      mode,
    }),
    [mode],
  );

  // Create the theme dynamically based on the mode, merging CssBaseline overrides
  const theme = useMemo(() => {
    // Get the base theme first
    const baseTheme = getAppTheme(mode);
    // Merge base theme with CssBaseline overrides using createTheme
    return createTheme(baseTheme, { 
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              backgroundColor: baseTheme.palette.background.default,
            },
          },
        },
      },
    });
  }, [mode]);

  return (
    <ThemeContext.Provider value={colorMode}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline /> {/* Ensures background color is applied, etc. */}
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useThemeContext = () => useContext(ThemeContext); 