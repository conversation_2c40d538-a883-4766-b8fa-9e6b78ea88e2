"""
Instagram-specific caching strategies with appropriate TTL and invalidation
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
from config.database import db_analysis

logger = logging.getLogger(__name__)

# Instagram-specific cache TTL settings (in seconds)
INSTAGRAM_CACHE_TTL = {
    "profile_insights": 1800,        # 30 minutes - profile metrics change slowly
    "media_insights": 3600,          # 1 hour - individual post metrics
    "story_insights": 600,           # 10 minutes - stories are short-lived
    "account_insights": 1800,        # 30 minutes - account-level metrics
    "business_discovery": 7200,      # 2 hours - discovery data is less frequent
    "media_list": 900,               # 15 minutes - new posts need quick updates
    "hashtag_insights": 3600,        # 1 hour - hashtag data
    "user_insights": 1800,           # 30 minutes - user activity data
    "advertising_insights": 900      # 15 minutes - ad metrics need frequent updates
}

class InstagramCacheService:
    """Service for managing Instagram-specific caching"""
    
    @staticmethod
    async def get_cached_data(
        cache_key: str, 
        data_type: str,
        max_age_seconds: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached Instagram data if still valid
        
        Args:
            cache_key: Unique identifier for the cached data
            data_type: Type of data (profile_insights, media_insights, etc.)
            max_age_seconds: Override default TTL for this request
        
        Returns:
            Cached data if valid, None if expired or not found
        """
        try:
            collection_name = f"instagram_{data_type}_cache"
            cached_item = await db_analysis[collection_name].find_one({"cache_key": cache_key})
            
            if not cached_item:
                logger.debug(f"No cached data found for {cache_key} in {collection_name}")
                return None
            
            # Check if data is still valid
            ttl_seconds = max_age_seconds or INSTAGRAM_CACHE_TTL.get(data_type, 1800)
            cache_time = cached_item.get("cached_at")
            
            if not cache_time:
                logger.warning(f"Cached item {cache_key} has no cached_at timestamp")
                return None
            
            # Handle both datetime objects and timestamps
            if isinstance(cache_time, datetime):
                age_seconds = (datetime.utcnow() - cache_time).total_seconds()
            else:
                age_seconds = (datetime.utcnow().timestamp() - cache_time)
            
            if age_seconds > ttl_seconds:
                logger.debug(f"Cached data for {cache_key} is stale ({age_seconds}s > {ttl_seconds}s)")
                # Don't delete here, let cleanup task handle it
                return None
            
            logger.debug(f"Retrieved valid cached data for {cache_key} (age: {age_seconds}s)")
            return cached_item.get("data")
            
        except Exception as e:
            logger.error(f"Error retrieving cached Instagram data for {cache_key}: {str(e)}")
            return None
    
    @staticmethod
    async def set_cached_data(
        cache_key: str,
        data_type: str,
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Cache Instagram data with appropriate TTL
        
        Args:
            cache_key: Unique identifier for the data
            data_type: Type of data being cached
            data: The data to cache
            metadata: Additional metadata (page_id, store_id, etc.)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            collection_name = f"instagram_{data_type}_cache"
            cache_document = {
                "cache_key": cache_key,
                "data": data,
                "cached_at": datetime.utcnow(),
                "data_type": data_type,
                "ttl_seconds": INSTAGRAM_CACHE_TTL.get(data_type, 1800),
                **(metadata or {})
            }
            
            # Upsert the document
            await db_analysis[collection_name].replace_one(
                {"cache_key": cache_key},
                cache_document,
                upsert=True
            )
            
            logger.debug(f"Cached Instagram {data_type} data for {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching Instagram data for {cache_key}: {str(e)}")
            return False
    
    @staticmethod
    async def invalidate_cache(
        cache_key: Optional[str] = None,
        data_type: Optional[str] = None,
        page_id: Optional[str] = None,
        store_id: Optional[str] = None
    ) -> int:
        """
        Invalidate Instagram cache entries based on criteria
        
        Args:
            cache_key: Specific cache key to invalidate
            data_type: Invalidate all entries of this type
            page_id: Invalidate all entries for this page
            store_id: Invalidate all entries for this store
        
        Returns:
            Number of entries invalidated
        """
        try:
            total_deleted = 0
            
            if cache_key and data_type:
                # Invalidate specific entry
                collection_name = f"instagram_{data_type}_cache"
                result = await db_analysis[collection_name].delete_one({"cache_key": cache_key})
                total_deleted += result.deleted_count
                
            elif data_type:
                # Invalidate all entries of a specific type
                collection_name = f"instagram_{data_type}_cache"
                filter_query = {}
                
                if page_id:
                    filter_query["page_id"] = page_id
                if store_id:
                    filter_query["store_id"] = store_id
                
                result = await db_analysis[collection_name].delete_many(filter_query)
                total_deleted += result.deleted_count
                
            else:
                # Invalidate across all Instagram cache collections
                for cache_type in INSTAGRAM_CACHE_TTL.keys():
                    collection_name = f"instagram_{cache_type}_cache"
                    filter_query = {}
                    
                    if page_id:
                        filter_query["page_id"] = page_id
                    if store_id:
                        filter_query["store_id"] = store_id
                    
                    if filter_query:
                        result = await db_analysis[collection_name].delete_many(filter_query)
                        total_deleted += result.deleted_count
            
            logger.info(f"Invalidated {total_deleted} Instagram cache entries")
            return total_deleted
            
        except Exception as e:
            logger.error(f"Error invalidating Instagram cache: {str(e)}")
            return 0
    
    @staticmethod
    async def cleanup_expired_cache() -> int:
        """
        Remove expired cache entries across all Instagram collections
        
        Returns:
            Number of entries cleaned up
        """
        try:
            total_cleaned = 0
            current_time = datetime.utcnow()
            
            for data_type, ttl_seconds in INSTAGRAM_CACHE_TTL.items():
                collection_name = f"instagram_{data_type}_cache"
                
                # Calculate cutoff time
                cutoff_time = current_time - timedelta(seconds=ttl_seconds)
                
                # Delete expired entries
                result = await db_analysis[collection_name].delete_many({
                    "cached_at": {"$lt": cutoff_time}
                })
                
                total_cleaned += result.deleted_count
                
                if result.deleted_count > 0:
                    logger.debug(f"Cleaned up {result.deleted_count} expired {data_type} cache entries")
            
            if total_cleaned > 0:
                logger.info(f"Cleaned up {total_cleaned} expired Instagram cache entries")
            
            return total_cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning up Instagram cache: {str(e)}")
            return 0
    
    @staticmethod
    async def get_cache_stats() -> Dict[str, Any]:
        """
        Get statistics about Instagram cache usage
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            stats = {
                "collections": {},
                "total_entries": 0,
                "total_size_mb": 0,
                "oldest_entry": None,
                "newest_entry": None
            }
            
            for data_type in INSTAGRAM_CACHE_TTL.keys():
                collection_name = f"instagram_{data_type}_cache"
                
                # Count documents
                count = await db_analysis[collection_name].count_documents({})
                
                # Get collection stats
                collection_stats = await db_analysis.command("collStats", collection_name)
                size_mb = collection_stats.get("size", 0) / (1024 * 1024)
                
                # Get oldest and newest entries
                oldest = await db_analysis[collection_name].find_one(
                    {}, 
                    sort=[("cached_at", 1)]
                )
                newest = await db_analysis[collection_name].find_one(
                    {}, 
                    sort=[("cached_at", -1)]
                )
                
                stats["collections"][data_type] = {
                    "count": count,
                    "size_mb": round(size_mb, 2),
                    "oldest": oldest.get("cached_at") if oldest else None,
                    "newest": newest.get("cached_at") if newest else None
                }
                
                stats["total_entries"] += count
                stats["total_size_mb"] += size_mb
                
                # Track global oldest and newest
                if oldest and (not stats["oldest_entry"] or oldest["cached_at"] < stats["oldest_entry"]):
                    stats["oldest_entry"] = oldest["cached_at"]
                
                if newest and (not stats["newest_entry"] or newest["cached_at"] > stats["newest_entry"]):
                    stats["newest_entry"] = newest["cached_at"]
            
            stats["total_size_mb"] = round(stats["total_size_mb"], 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting Instagram cache stats: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def generate_cache_key(
        page_id: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        time_range: Optional[str] = None
    ) -> str:
        """
        Generate a consistent cache key for Instagram API calls
        
        Args:
            page_id: Instagram page/account ID
            endpoint: API endpoint name
            params: Additional parameters
            time_range: Time range for the data
        
        Returns:
            Consistent cache key string
        """
        key_parts = [page_id, endpoint]
        
        if time_range:
            key_parts.append(time_range)
        
        if params:
            # Sort params for consistency
            sorted_params = sorted(params.items())
            param_string = "_".join(f"{k}={v}" for k, v in sorted_params)
            key_parts.append(param_string)
        
        return "_".join(key_parts)