import { useState, useEffect } from 'react';
import { Box, Button, IconButton, Drawer, List, ListItem, ListItemIcon, ListItemText, Tooltip, useMediaQuery, useTheme, Divider, BottomNavigation, BottomNavigationAction, Paper as NavPaper } from '@mui/material';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';
import ChatIcon from '@mui/icons-material/Chat';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
// import ShareIcon from '@mui/icons-material/Share';
import logo from '../../assets/logo.png';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';

// Meta logo as an SVG icon - infinity symbol
const MetaIcon = () => (
  <Box sx={{ 
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
    backgroundColor: 'white',
    borderRadius: '50%',
    boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
  }}>
    <Box
      component="img"
      src="/meta.png"
      alt="Meta Logo"
      sx={{
        width: '18px',
        height: '18px',
        objectFit: 'contain',
      }}
    />
  </Box>
);

const Navigation = () => {
  const { logout, user } = useAuth();
  const { refreshPermissions } = useMetaPermissions();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const [mobileOpen, setMobileOpen] = useState(false);
  const isXs = useMediaQuery(theme.breakpoints.down('sm'));
  const [navValue, setNavValue] = useState(0);
  const { t } = useTranslation();
  
  // Path-specific breakpoints
  // Use 1700px for chat page, 800px for all other pages
  const isChatPage = location.pathname === '/chat' || location.pathname === '/' || location.pathname.includes('/chat');
  const customBreakpoint = isChatPage ? '(max-width:1700px)' : theme.breakpoints.down('md');
  
  // Define custom breakpoint for switching between horizontal and vertical navigation
  const isSmallScreen = useMediaQuery(customBreakpoint);

  // Determine which navigation variant is displayed
  const showVerticalSidebar = !isXs && isSmallScreen; // only on small screens but not extra-small
  const showBottomNav = isSmallScreen; // bottom nav for all small sizes (xs + sm)
  
  const buttonBaseStyle = {
    textTransform: 'none',
    px: 2,
    py: 1,
    fontWeight: 500,
    fontSize: '0.875rem',
    minWidth: '70px',
    borderRadius: '8px',
  };

  const blueButtonStyle = {
    ...buttonBaseStyle,
    bgcolor: '#00A3FF',
    animation: 'bluePulse 2s infinite',
    '&:hover': { 
      bgcolor: '#0082CC',
      animation: 'none'
    },
    '@keyframes bluePulse': {
      '0%': {
        boxShadow: '0 0 0 0 rgba(0, 163, 255, 0.7)'
      },
      '70%': {
        boxShadow: '0 0 0 10px rgba(0, 163, 255, 0)'
      },
      '100%': {
        boxShadow: '0 0 0 0 rgba(0, 163, 255, 0)'
      }
    }
  };

  const redButtonStyle = {
    ...buttonBaseStyle,
    bgcolor: '#DC3545',
    animation: 'redPulse 2s infinite',
    '&:hover': { 
      bgcolor: '#C82333',
      animation: 'none'
    },
    '@keyframes redPulse': {
      '0%': {
        boxShadow: '0 0 0 0 rgba(220, 53, 69, 0.7)'
      },
      '70%': {
        boxShadow: '0 0 0 10px rgba(220, 53, 69, 0)'
      },
      '100%': {
        boxShadow: '0 0 0 0 rgba(220, 53, 69, 0)'
      }
    }
  };

  // Determine context-aware links for admin in store context
  let storeId: string | null = null;
  const storeMatch = location.pathname.match(/^\/store\/(\w+)(?:\/|$)/);
  if (user && user.role === 'admin' && storeMatch && storeMatch[1]) {
    storeId = storeMatch[1];
  }
  const dashboardLink = storeId ? `/store/${storeId}/overview` : '/dashboard';
  const settingsLink = storeId ? `/store/${storeId}/settings` : '/settings';
  const chatLink = storeId ? `/store/${storeId}/chat` : '/chat';

  const isAdmin = user?.role === 'admin';

  // Handle click on Meta button, refresh permissions and navigate
  const handleMetaClick = () => {
    // Force refresh permissions
    logger.debug('Meta button clicked, refreshing permissions...');
    refreshPermissions(true).catch(err => {
      logger.error('Error refreshing permissions from Nav button:', err);
    });
    
    // Navigate to Meta dashboard
    navigate('/meta');
    setMobileOpen(false);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    setMobileOpen(false);
  };

  // Export current navigation mode to let other components adjust layout
  useEffect(() => {
    // Make the navigation mode available globally
    window.isVerticalNav = showVerticalSidebar;
    window.isChatPage = isChatPage;
    
    // Dispatch custom event when navigation mode changes
    const event = new CustomEvent('navigationModeChanged', { 
      detail: { 
        isVerticalNav: showVerticalSidebar,
        isChatPage: isChatPage
      } 
    });
    window.dispatchEvent(event);
  }, [showVerticalSidebar, isChatPage]);

  // Sidebar content for mobile drawer
  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <List>
        <Link to={chatLink} style={{ textDecoration: 'none', color: 'inherit' }}>
          <ListItem button onClick={() => setMobileOpen(false)}>
            <ListItemIcon>
              <ChatIcon sx={{ color: '#00A3FF' }} />
            </ListItemIcon>
            <ListItemText primary={t('navigation.chat')} />
          </ListItem>
        </Link>
        
        <Link to={dashboardLink} style={{ textDecoration: 'none', color: 'inherit' }}>
          <ListItem button onClick={() => setMobileOpen(false)}>
            <ListItemIcon>
              <DashboardIcon sx={{ color: '#00A3FF' }} />
            </ListItemIcon>
            <ListItemText primary={t('navigation.dashboard')} />
          </ListItem>
        </Link>
        
        <ListItem button onClick={handleMetaClick}>
          <ListItemIcon>
            <MetaIcon />
          </ListItemIcon>
          <ListItemText primary={t('navigation.meta')} />
        </ListItem>
        
        {/* Social Media navigation hidden */}
        {/*
        <Link to={socialMediaLink} style={{ textDecoration: 'none', color: 'inherit' }}>
          <ListItem button onClick={() => setMobileOpen(false)}>
            <ListItemIcon>
              <ShareIcon sx={{ color: '#00A3FF' }} />
            </ListItemIcon>
            <ListItemText primary={t('navigation.socialMedia')} />
          </ListItem>
        </Link>
        */}
        
        <Link to={settingsLink} style={{ textDecoration: 'none', color: 'inherit' }}>
          <ListItem button onClick={() => setMobileOpen(false)}>
            <ListItemIcon>
              <SettingsIcon sx={{ color: '#00A3FF' }} />
            </ListItemIcon>
            <ListItemText primary={t('navigation.settings')} />
          </ListItem>
        </Link>
      </List>
      
      <Box sx={{ mt: 'auto', mb: 2 }}>
        <ListItem button onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon sx={{ color: '#DC3545' }} />
          </ListItemIcon>
          <ListItemText primary={t('navigation.logout')} />
        </ListItem>
      </Box>
    </Box>
  );

  // Bottom navigation for extra-small screens
  const bottomNav = (
    <NavPaper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 2000,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        overflow: 'hidden',
      }}
      elevation={6}
    >
      <BottomNavigation
        value={navValue}
        onChange={(_, newValue) => {
          setNavValue(newValue);
          switch (newValue) {
            case 0:
              navigate(chatLink);
              break;
            case 1:
              navigate(dashboardLink);
              break;
            case 2:
              handleMetaClick();
              break;
            case 3:
              navigate(settingsLink);
              break;
            case 4:
              handleLogout();
              break;
            default:
              break;
          }
        }}
        showLabels
        sx={{ bgcolor: theme.palette.mode === 'dark' ? '#1f2937' : '#ffffff' }}
      >
        <BottomNavigationAction label={t('navigation.chat')} icon={<ChatIcon />} />
        <BottomNavigationAction label={t('navigation.dashboard')} icon={<DashboardIcon />} />
        <BottomNavigationAction label={t('navigation.meta')} icon={<MetaIcon />} />
        <BottomNavigationAction label={t('navigation.settings')} icon={<SettingsIcon />} />
        <BottomNavigationAction label={t('navigation.logout')} icon={<LogoutIcon />} />
      </BottomNavigation>
    </NavPaper>
  );

  // Vertical sidebar with icons only (for small screens)
  const verticalSidebar = (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        right: 0,
        height: '100%',
        width: '48px',
        bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#f5f5f5',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        pt: 2,
        pb: 2,
        boxShadow: '-1px 0px 5px rgba(0,0,0,0.1)',
        zIndex: 1400,
      }}
    >
      {/* Logo at the top */}
      <Box sx={{ width: '100%', textAlign: 'center', pt: 2, pb: 1 }}> {/* Added padding top/bottom */}
        <img 
          src={logo} 
          alt="D-Unit Logo" 
          style={{ // Reverted to style prop for simplicity here
            height: '32px', // Slightly smaller logo
            cursor: 'pointer',
          }} 
          onClick={() => navigate('/')} 
        />
      </Box>

      {/* Divider */} 
      <Divider sx={{ width: '80%', my: 1 }} />

      {/* Icons container */}
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 2, 
        alignItems: 'center', 
        width: '100%', // Ensure icons container takes width
        // Removed mt: 2 from here, spacing handled by Divider and logo padding
      }}>
        <Tooltip title={t('navigation.chat')} placement="left">
          <IconButton 
            component={Link} 
            to={chatLink}
            sx={{ color: '#00A3FF' }}
          >
            <ChatIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title={t('navigation.dashboard')} placement="left">
          <IconButton 
            component={Link} 
            to={dashboardLink}
            sx={{ color: '#00A3FF' }}
          >
            <DashboardIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title={t('navigation.meta')} placement="left">
          <IconButton 
            onClick={handleMetaClick}
            sx={{ color: '#00A3FF' }}
          >
            <MetaIcon />
          </IconButton>
        </Tooltip>
        
        {/* Social Media navigation hidden */}
        {/*
        <Tooltip title={t('navigation.socialMedia')} placement="left">
          <IconButton 
            component={Link} 
            to={socialMediaLink}
            sx={{ color: '#00A3FF' }}
          >
            <ShareIcon />
          </IconButton>
        </Tooltip>
        */}
        
        <Tooltip title={t('navigation.settings')} placement="left">
          <IconButton 
            component={Link} 
            to={settingsLink}
            sx={{ color: '#00A3FF' }}
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Box sx={{ mt: 'auto' }}>
        <Tooltip title={t('navigation.logout')} placement="left">
          <IconButton 
            onClick={logout}
            sx={{ color: '#DC3545' }}
          >
            <LogoutIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );

  // Horizontal navigation bar (for larger screens)
  const horizontalNavbar = (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        zIndex: 1200,
      }}
    >
      {/* Left: Logo */}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <img 
          src={logo} 
          alt="D-Unit Logo" 
          style={{ 
            height: '40px',
            cursor: 'pointer',
          }} 
          onClick={() => navigate('/')} 
        />
      </Box>
      {/* Right: Navigation buttons */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
        <Link to={chatLink} style={{ textDecoration: 'none' }}>
          <Button
            variant="contained"
            sx={blueButtonStyle}
          >
            {t('navigation.chat')}
          </Button>
        </Link>
        <Link to={dashboardLink} style={{ textDecoration: 'none' }}>
          <Button
            variant="contained"
            sx={blueButtonStyle}
          >
            {t('navigation.dashboard')}
          </Button>
        </Link>
        <Button
          variant="contained"
          sx={{
            ...blueButtonStyle,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            px: 1.5,
          }}
          onClick={handleMetaClick}
          disabled={isAdmin}
        >
          <MetaIcon />
          <span>{t('navigation.meta')}</span>
        </Button>
        {/* Social Media navigation hidden */}
        {/*
        <Link to={socialMediaLink} style={{ textDecoration: 'none' }}>
          <Button
            variant="contained"
            sx={blueButtonStyle}
          >
            {t('navigation.socialMedia')}
          </Button>
        </Link>
        */}
        <Link to={settingsLink} style={{ textDecoration: 'none' }}>
          <Button
            variant="contained"
            sx={blueButtonStyle}
          >
            {t('navigation.settings')}
          </Button>
        </Link>
        <Button
          variant="contained"
          onClick={logout}
          sx={redButtonStyle}
        >
          {t('navigation.logout')}
        </Button>
      </Box>
    </Box>
  );

  return (
    <>
      {/* Render bottom nav on xs, vertical sidebar on small, horizontal nav on large */}
      {showBottomNav ? bottomNav : (showVerticalSidebar ? verticalSidebar : horizontalNavbar)}
      
      {/* Mobile drawer that shows full menu (only on small screens) */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'none', sm: 'block', md: 'none' },
          '& .MuiDrawer-paper': { width: 240 },
        }}
      >
        {sidebarContent}
      </Drawer>
    </>
  );
};

export default Navigation; 

