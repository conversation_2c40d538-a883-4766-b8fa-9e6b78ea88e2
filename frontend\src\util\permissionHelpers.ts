import { useMetaPermissions } from '../contexts/MetaPermissionsContext';

/**
 * Detect if a message is related to Instagram content
 * @param message The user message to check
 * @returns Whether the message appears to be requesting Instagram-related data
 */
export const isInstagramQuery = (message: string): boolean => {
  const lowerMessage = message.toLowerCase();
  
  // Keywords that suggest Instagram-related queries
  const instagramKeywords = [
    'instagram',
    'insta ',
    'ig ',
    'instagram insights',
    'instagram followers',
    'instagram posts',
    'instagram metrics',
    'instagram analytics',
    'instagram performance',
    'instagram engagement',
    'instagram reach',
    'instagram impressions',
    'instagram stories',
    'instagram reels',
    'instagram grid',
    'instagram content'
  ];
  
  return instagramKeywords.some(keyword => lowerMessage.includes(keyword));
};

/**
 * Hook to check if Instagram features are available
 * @returns Object with methods to check Instagram permissions
 */
export const useInstagramPermissions = () => {
  const { canAccessFeature, getRevokedPermissions } = useMetaPermissions();
  
  const instagramBasicStatus = canAccessFeature('instagramBasic');
  const instagramInsightsStatus = canAccessFeature('instagramInsights');
  
  const allRevokedPermissions = getRevokedPermissions();
  const revokedInstagramPermissions = allRevokedPermissions.filter(p => 
    p.toLowerCase().includes('instagram')
  );
  
  return {
    canAccessBasic: instagramBasicStatus.canAccess,
    canAccessInsights: instagramInsightsStatus.canAccess,
    instagramBasicRevoked: instagramBasicStatus.revokedPermissions.length > 0,
    instagramInsightsRevoked: instagramInsightsStatus.revokedPermissions.length > 0,
    hasRevokedInstagramPermissions: revokedInstagramPermissions.length > 0,
    revokedInstagramPermissions
  };
}; 