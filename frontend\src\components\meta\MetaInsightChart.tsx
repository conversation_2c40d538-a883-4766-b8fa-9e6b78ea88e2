import React, { useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent,
  useTheme
} from '@mui/material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  <PERSON><PERSON>hart,
  Bar
} from 'recharts';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';

interface MetricDataPoint {
  date: string;
  value: number;
  label?: string;
}

interface MetaInsightChartProps {
  title: string;
  data: MetricDataPoint[];
  height?: number;
  chartType?: 'line' | 'bar';
  color?: string;
  showTrend?: boolean;
  formatValue?: (value: number) => string;
}

/**
 * A reusable chart component for displaying Meta insights data
 */
export const MetaInsightChart: React.FC<MetaInsightChartProps> = ({
  title,
  data,
  height = 300,
  chartType = 'line',
  color,
  showTrend = false,
  formatValue = (value) => value.toLocaleString()
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  // Memoize formatted data to prevent unnecessary re-renders
  const formattedData = useMemo(() => {
    return data.map(point => ({
      ...point,
      // Format date for display
      displayDate: new Date(point.date).toLocaleDateString(),
      formattedValue: formatValue(point.value)
    }));
  }, [data, formatValue]);

  // Calculate trend if needed (simplified - no percentage)
  const trend = useMemo(() => {
    if (!showTrend || data.length < 2) return null;
    
    const first = data[0]?.value || 0;
    const last = data[data.length - 1]?.value || 0;
    
    return {
      direction: last > first ? 'up' : last < first ? 'down' : 'neutral'
    };
  }, [data, showTrend]);

  // Dynamic color based on theme
  const chartColor = color || (theme.palette.mode === 'dark' ? '#90caf9' : '#1976d2');

  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <Card elevation={1} sx={{ mt: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box sx={{ 
            height: height, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            color: 'text.secondary'
          }}>
            <Typography variant="body2">
              {t('common.noDataAvailable', 'No data available')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const renderChart = () => {
    if (chartType === 'bar') {
      return (
        <BarChart
          data={formattedData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="displayDate"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickFormatter={formatValue}
            domain={[0, 'dataMax + 1']}
          />
          <Tooltip 
            formatter={(value, name) => [formatValue(Number(value)), name]}
            labelFormatter={(label) => `${t('common.date', 'Date')}: ${label}`}
            contentStyle={theme.palette.mode === 'dark' ? { 
              background: '#fff', 
              color: '#111', 
              borderRadius: 8 
            } : {}}
            itemStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
            labelStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
          />
          <Bar 
            dataKey="value" 
            name={title}
            fill={chartColor}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      );
    }

    return (
      <LineChart
        data={formattedData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="displayDate"
          tick={{ fontSize: 12 }}
          angle={-45}
          textAnchor="end"
          height={60}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickFormatter={formatValue}
          domain={[0, 'dataMax + 1']}
        />
        <Tooltip 
          formatter={(value, name) => [formatValue(Number(value)), name]}
          labelFormatter={(label) => `${t('common.date', 'Date')}: ${label}`}
          contentStyle={theme.palette.mode === 'dark' ? { 
            background: '#fff', 
            color: '#111', 
            borderRadius: 8 
          } : {}}
          itemStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
          labelStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
        />
        <Line 
          type="monotone" 
          dataKey="value" 
          name={title}
          stroke={chartColor}
          strokeWidth={2}
          dot={{ fill: chartColor, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, fill: chartColor }}
        />
      </LineChart>
    );
  };

  return (
    <Card elevation={1} sx={{ mt: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {title}
          </Typography>
          {trend && (
            <Typography 
              variant="body2" 
              sx={{ 
                color: trend.direction === 'up' ? 'success.main' : 
                       trend.direction === 'down' ? 'error.main' : 'text.secondary'
              }}
            >
              {trend.direction === 'up' ? '↗' : trend.direction === 'down' ? '↘' : '→'}
            </Typography>
          )}
        </Box>
        
        <Box sx={{ height: height, width: '100%' }}>
          <ChartContainer width="100%" height="100%">
            {renderChart()}
          </ChartContainer>
        </Box>
      </CardContent>
    </Card>
  );
};