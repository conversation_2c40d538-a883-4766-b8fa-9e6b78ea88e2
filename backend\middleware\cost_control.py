import time
import json
import yaml
import logging
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any, List
from pathlib import Path
from collections import defaultdict
from threading import RLock
import os
import traceback

from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware

from models.security import CostCheckResult, SecurityEvent, SecurityEventType, ThreatLevel, BudgetAlert
from utils.security_utils import extract_client_ip, generate_trace_id
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class CostTracker:
    """Thread-safe cost tracking with time-based aggregation"""
    
    def __init__(self):
        self._lock = RLock()
        self._daily_costs: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self._monthly_costs: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self._hourly_costs: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self._request_counts: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
    def _get_time_keys(self) -> tuple:
        """Get time-based keys for tracking"""
        now = datetime.now(timezone.utc)
        daily_key = now.strftime("%Y-%m-%d")
        monthly_key = now.strftime("%Y-%m")
        hourly_key = now.strftime("%Y-%m-%d-%H")
        return daily_key, monthly_key, hourly_key
    
    def add_cost(self, store_id: str, service: str, cost: float, request_count: int = 1):
        """Add cost to tracking"""
        with self._lock:
            daily_key, monthly_key, hourly_key = self._get_time_keys()
            
            # Track by store and service
            self._daily_costs[daily_key][f"{store_id}:{service}"] += cost
            self._monthly_costs[monthly_key][f"{store_id}:{service}"] += cost
            self._hourly_costs[hourly_key][f"{store_id}:{service}"] += cost
            
            # Track request counts
            self._request_counts[daily_key][f"{store_id}:{service}"] += request_count
    
    def get_daily_cost(self, store_id: str, service: Optional[str] = None) -> float:
        """Get daily cost for store and optional service"""
        with self._lock:
            daily_key, _, _ = self._get_time_keys()
            daily_data = self._daily_costs.get(daily_key, {})
            
            if service:
                return daily_data.get(f"{store_id}:{service}", 0.0)
            else:
                # Sum all services for the store
                total = 0.0
                for key, cost in daily_data.items():
                    if key.startswith(f"{store_id}:"):
                        total += cost
                return total
    
    def get_monthly_cost(self, store_id: str, service: Optional[str] = None) -> float:
        """Get monthly cost for store and optional service"""
        with self._lock:
            _, monthly_key, _ = self._get_time_keys()
            monthly_data = self._monthly_costs.get(monthly_key, {})
            
            if service:
                return monthly_data.get(f"{store_id}:{service}", 0.0)
            else:
                # Sum all services for the store
                total = 0.0
                for key, cost in monthly_data.items():
                    if key.startswith(f"{store_id}:"):
                        total += cost
                return total
    
    def get_system_daily_cost(self) -> float:
        """Get total system daily cost"""
        with self._lock:
            daily_key, _, _ = self._get_time_keys()
            return sum(self._daily_costs.get(daily_key, {}).values())
    
    def get_system_monthly_cost(self) -> float:
        """Get total system monthly cost"""
        with self._lock:
            _, monthly_key, _ = self._get_time_keys()
            return sum(self._monthly_costs.get(monthly_key, {}).values())
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old cost data to prevent memory leaks"""
        with self._lock:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            # Clean daily data
            keys_to_remove = []
            for date_key in self._daily_costs.keys():
                try:
                    date_obj = datetime.strptime(date_key, "%Y-%m-%d")
                    if date_obj.replace(tzinfo=timezone.utc) < cutoff_date:
                        keys_to_remove.append(date_key)
                except ValueError:
                    continue
            
            for key in keys_to_remove:
                del self._daily_costs[key]
                if key in self._request_counts:
                    del self._request_counts[key]
            
            # Clean hourly data (keep only last 7 days)
            hourly_cutoff = datetime.now(timezone.utc) - timedelta(days=7)
            keys_to_remove = []
            for hour_key in self._hourly_costs.keys():
                try:
                    date_obj = datetime.strptime(hour_key, "%Y-%m-%d-%H")
                    if date_obj.replace(tzinfo=timezone.utc) < hourly_cutoff:
                        keys_to_remove.append(hour_key)
                except ValueError:
                    continue
            
            for key in keys_to_remove:
                del self._hourly_costs[key]

class CostControlMiddleware(BaseHTTPMiddleware):
    """
    Cost control middleware for monitoring and limiting API costs
    """
    
    def __init__(self, app, config_path: str = "config/cost_limits.yaml"):
        super().__init__(app)
        self.config_path = config_path
        self.config = self._load_config()
        self.enabled = True
        self.cost_tracker = CostTracker()
        self.last_cleanup = time.time()
        
        # Cost estimation models for different endpoints
        self.endpoint_cost_models = self._load_endpoint_costs()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load cost control configuration from YAML file"""
        try:
            # Use absolute path resolution
            if not os.path.isabs(self.config_path):
                # Get the project root directory (assuming backend is in project root)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = Path(project_root) / self.config_path
            else:
                config_file = Path(self.config_path)
                
            logger.info(f"Loading cost control config from: {config_file.absolute()}")
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"Successfully loaded cost control configuration from {config_file}")
                return config or {}
            else:
                logger.warning(f"Cost config file not found at: {config_file.absolute()}")
                logger.warning(f"Current working directory: {os.getcwd()}")
                logger.warning(f"Config directory contents: {list(config_file.parent.iterdir()) if config_file.parent.exists() else 'Directory does not exist'}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load cost config from {self.config_path}: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default cost control configuration"""
        return {
            "global_cost_limits": {
                "system_daily_limit": 200.0,
                "system_monthly_limit": 5000.0,
                "emergency_stop_daily": 0.95,
                "emergency_stop_monthly": 0.95,
                "warning_threshold": 0.70,
                "critical_threshold": 0.85
            },
            "store_tier_limits": {
                "free": {
                    "daily_limit": 5.0,
                    "monthly_limit": 50.0,
                    "openai_daily_limit": 2.0,
                    "meta_api_daily_limit": 1.0
                }
            },
            "endpoint_costs": {
                "/api/chat/**": {
                    "estimated_cost_per_request": 0.015
                },
                "/api/meta/**": {
                    "estimated_cost_per_request": 0.0015
                }
            }
        }
    
    def _load_endpoint_costs(self) -> Dict[str, Dict[str, float]]:
        """Load endpoint cost models"""
        return self.config.get("endpoint_costs", {})
    
    def _get_store_tier(self, store_id: str) -> str:
        """Get store tier - in real implementation, this would query database"""
        # For now, return 'free' as default
        # In production, you would look up the store's subscription tier
        return "free"
    
    def _get_store_limits(self, store_id: str) -> Dict[str, float]:
        """Get cost limits for a specific store"""
        tier = self._get_store_tier(store_id)
        store_limits = self.config.get("store_tier_limits", {})
        return store_limits.get(tier, store_limits.get("free", {}))
    
    def _estimate_request_cost(self, request: Request) -> float:
        """Estimate cost for a specific request"""
        path = request.url.path
        
        # Find matching endpoint pattern
        for pattern, cost_info in self.endpoint_cost_models.items():
            if self._match_endpoint_pattern(path, pattern):
                base_cost = cost_info.get("estimated_cost_per_request", 0.001)
                
                # Apply complexity multipliers
                multiplier = 1.0
                
                # Increase cost for AI endpoints
                if "/chat" in path.lower() or "/ai" in path.lower():
                    multiplier *= 2.0
                
                # Increase cost for complex queries
                query_params = len(request.query_params)
                if query_params > 5:
                    multiplier *= 1.2
                
                # Increase cost for POST/PUT requests (usually more expensive)
                if request.method in ["POST", "PUT", "PATCH"]:
                    multiplier *= 1.1
                
                return base_cost * multiplier
        
        # Default cost for unmatched endpoints
        return 0.001
    
    def _match_endpoint_pattern(self, path: str, pattern: str) -> bool:
        """Match request path against endpoint pattern"""
        if pattern.endswith("/**"):
            base_pattern = pattern[:-3]
            return path.startswith(base_pattern)
        elif pattern.endswith("/*"):
            base_pattern = pattern[:-2]
            path_parts = path.split("/")
            pattern_parts = base_pattern.split("/")
            return len(path_parts) == len(pattern_parts) + 1 and path.startswith(base_pattern)
        else:
            return path == pattern
    
    def _check_cost_limits(self, store_id: str, estimated_cost: float) -> CostCheckResult:
        """Check if request would exceed cost limits"""
        store_limits = self._get_store_limits(store_id)
        
        # Get current costs
        current_daily_cost = self.cost_tracker.get_daily_cost(store_id)
        current_monthly_cost = self.cost_tracker.get_monthly_cost(store_id)
        
        # Get limits
        daily_limit = store_limits.get("daily_limit", 5.0)
        monthly_limit = store_limits.get("monthly_limit", 50.0)
        
        # Check if adding this request would exceed limits
        new_daily_cost = current_daily_cost + estimated_cost
        new_monthly_cost = current_monthly_cost + estimated_cost
        
        # Calculate remaining budgets
        remaining_daily = max(0, daily_limit - current_daily_cost)
        remaining_monthly = max(0, monthly_limit - current_monthly_cost)
        
        # Determine if request is allowed
        allowed = (new_daily_cost <= daily_limit and new_monthly_cost <= monthly_limit)
        
        return CostCheckResult(
            allowed=allowed,
            current_daily_cost=current_daily_cost,
            current_monthly_cost=current_monthly_cost,
            daily_limit=daily_limit,
            monthly_limit=monthly_limit,
            estimated_request_cost=estimated_cost,
            remaining_daily_budget=remaining_daily,
            remaining_monthly_budget=remaining_monthly
        )
    
    def _check_system_limits(self) -> bool:
        """Check if system-wide cost limits are exceeded"""
        global_limits = self.config.get("global_cost_limits", {})
        
        system_daily_cost = self.cost_tracker.get_system_daily_cost()
        system_monthly_cost = self.cost_tracker.get_system_monthly_cost()
        
        daily_limit = global_limits.get("system_daily_limit", 200.0)
        monthly_limit = global_limits.get("system_monthly_limit", 5000.0)
        
        emergency_daily_threshold = global_limits.get("emergency_stop_daily", 0.95)
        emergency_monthly_threshold = global_limits.get("emergency_stop_monthly", 0.95)
        
        # Check emergency thresholds
        daily_usage_ratio = system_daily_cost / daily_limit
        monthly_usage_ratio = system_monthly_cost / monthly_limit
        
        if (daily_usage_ratio >= emergency_daily_threshold or 
            monthly_usage_ratio >= emergency_monthly_threshold):
            return False
        
        return True
    
    def _should_generate_alert(self, cost_result: CostCheckResult) -> Optional[BudgetAlert]:
        """Check if we should generate a budget alert"""
        warning_threshold = self.config.get("global_cost_limits", {}).get("warning_threshold", 0.70)
        critical_threshold = self.config.get("global_cost_limits", {}).get("critical_threshold", 0.85)
        
        # Check daily usage
        daily_usage_ratio = cost_result.current_daily_cost / cost_result.daily_limit
        monthly_usage_ratio = cost_result.current_monthly_cost / cost_result.monthly_limit
        
        max_usage_ratio = max(daily_usage_ratio, monthly_usage_ratio)
        
        if max_usage_ratio >= critical_threshold:
            alert_type = "critical_threshold"
        elif max_usage_ratio >= warning_threshold:
            alert_type = "warning_threshold"
        else:
            return None
        
        # Generate alert (in production, you'd check if alert was already sent)
        return BudgetAlert(
            alert_id=generate_trace_id(),
            store_id="system",  # You'd use actual store_id in production
            alert_type=alert_type,
            current_amount=max(cost_result.current_daily_cost, cost_result.current_monthly_cost),
            threshold=max(cost_result.daily_limit * warning_threshold, 
                         cost_result.monthly_limit * warning_threshold),
            percentage_used=max_usage_ratio,
            escalated=max_usage_ratio >= critical_threshold
        )
    
    def _log_cost_limit_exceeded(self, request: Request, cost_result: CostCheckResult):
        """Log cost limit violation for monitoring"""
        client_ip = extract_client_ip(request)
        
        security_event = SecurityEvent(
            event_type=SecurityEventType.COST_LIMIT_EXCEEDED,
            user_id=getattr(request.state, 'user_id', None),
            store_id=getattr(request.state, 'store_id', None),
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            endpoint=request.url.path,
            method=request.method,
            threat_level=ThreatLevel.HIGH,
            details={
                "current_daily_cost": cost_result.current_daily_cost,
                "daily_limit": cost_result.daily_limit,
                "current_monthly_cost": cost_result.current_monthly_cost,
                "monthly_limit": cost_result.monthly_limit,
                "estimated_request_cost": cost_result.estimated_request_cost
            },
            trace_id=generate_trace_id()
        )
        
        # Log to SecurityService if available
        security_service = getattr(request.app.state, 'security_service', None)
        if security_service:
            try:
                # Use asyncio to run the async method
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're in an async context, schedule the task
                    asyncio.create_task(security_service.log_security_event(security_event))
                else:
                    # If not in async context, run directly
                    loop.run_until_complete(security_service.log_security_event(security_event))
            except Exception as e:
                logger.error(f"Failed to log cost limit exceeded event: {e}")
        
        logger.error(f"Cost limit exceeded for store on {request.method} {request.url.path}")
    
    def _apply_cost_optimization(self, request: Request) -> Dict[str, Any]:
        """Apply cost optimization strategies"""
        optimizations = {}
        
        # Check if we should enable aggressive caching
        optimization_config = self.config.get("optimization", {})
        if optimization_config.get("enable_intelligent_caching", True):
            # Check if this is a cacheable request
            if request.method == "GET" and not any(
                sensitive_path in request.url.path 
                for sensitive_path in ["/auth", "/admin", "/private"]
            ):
                optimizations["enable_aggressive_caching"] = True
                optimizations["cache_duration"] = optimization_config.get("cache_duration_minutes", 15)
        
        # Model selection optimization for AI endpoints
        if optimization_config.get("auto_model_selection", True):
            if "/chat" in request.url.path.lower():
                # Determine complexity and suggest model
                query_params = len(request.query_params)
                if query_params <= 2:
                    optimizations["suggested_model"] = settings.OPENAI_DEFAULT_MODEL
                else:
                    optimizations["suggested_model"] = settings.OPENAI_DEFAULT_MODEL
        
        return optimizations
    
    async def dispatch(self, request: Request, call_next):
        """Main middleware dispatch method"""
        if not self.enabled:
            return await call_next(request)
        
        # Periodic cleanup
        current_time = time.time()
        if current_time - self.last_cleanup > 3600:  # Every hour
            self.cost_tracker.cleanup_old_data()
            self.last_cleanup = current_time
        
        # Get store ID from request (you'd implement this based on your auth system)
        store_id = getattr(request.state, 'store_id', 'default')
        
        # Estimate request cost
        estimated_cost = self._estimate_request_cost(request)
        
        # Check system-wide limits first
        if not self._check_system_limits():
            logger.critical("System-wide cost limits exceeded - emergency stop activated")
            return JSONResponse(
                status_code=503,
                content={
                    "error": "Service temporarily unavailable",
                    "message": "System cost limits exceeded. Please try again later.",
                    "retry_after": 3600
                }
            )
        
        # Check store-specific limits
        cost_result = self._check_cost_limits(store_id, estimated_cost)
        
        if not cost_result.allowed:
            # Log cost limit violation
            self._log_cost_limit_exceeded(request, cost_result)
            
            return JSONResponse(
                status_code=402,  # Payment Required
                content={
                    "error": "Cost limit exceeded",
                    "message": "Your usage has exceeded the cost limits for your plan.",
                    "current_daily_cost": cost_result.current_daily_cost,
                    "daily_limit": cost_result.daily_limit,
                    "current_monthly_cost": cost_result.current_monthly_cost,
                    "monthly_limit": cost_result.monthly_limit,
                    "estimated_request_cost": cost_result.estimated_request_cost
                }
            )
        
        # Apply cost optimizations
        optimizations = self._apply_cost_optimization(request)
        request.state.cost_optimizations = optimizations
        
        # Check for alerts
        alert = self._should_generate_alert(cost_result)
        if alert:
            logger.warning(f"Budget alert: {alert.alert_type} for usage {alert.percentage_used:.2%}")
        
        # Store cost info in request state
        request.state.cost_result = cost_result
        
        # Process the request
        response = await call_next(request)
        
        # Track actual cost (in production, you might get actual cost from response)
        actual_cost = estimated_cost  # For now, use estimate
        service_name = self._extract_service_name(request)
        self.cost_tracker.add_cost(store_id, service_name, actual_cost)
        
        # Track cost with SecurityService if available
        security_service = getattr(request.app.state, 'security_service', None)
        if security_service and store_id:
            try:
                await security_service.track_cost(
                    store_id=store_id,
                    service=service_name,
                    cost=actual_cost,
                    request_count=1
                )
            except Exception as e:
                logger.error(f"Failed to track cost via SecurityService: {e}")
        
        # Add cost headers to response
        response.headers["X-Cost-Estimate"] = f"{estimated_cost:.6f}"
        response.headers["X-Daily-Cost"] = f"{cost_result.current_daily_cost:.4f}"
        response.headers["X-Daily-Limit"] = f"{cost_result.daily_limit:.2f}"
        response.headers["X-Daily-Remaining"] = f"{cost_result.remaining_daily_budget:.4f}"
        
        return response
    
    def _extract_service_name(self, request: Request) -> str:
        """Extract service name from request path"""
        path = request.url.path.lower()
        
        if "/chat" in path or "/ai" in path:
            return "openai"
        elif "/meta" in path:
            return "meta_api"
        elif "/insights" in path:
            return "analytics"
        else:
            return "general" 