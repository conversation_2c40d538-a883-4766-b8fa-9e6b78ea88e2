/**
 * Location utilities for normalizing and aggregating location data
 */

/**
 * Normalizes location names to handle variations and misspellings
 * @param name The location name to normalize
 * @returns The normalized location name
 */
export const normalizeLocationName = (name: string): string => {
  if (!name) return 'Unknown Location';
  
  // Convert to title case and trim
  const normalized = name.trim().toLowerCase().replace(/\b\w/g, c => c.toUpperCase());
  
  // Common city name mappings
  const cityMappings: { [key: string]: string } = {
    // Argentina
    'BUENOS AIRES': 'Buenos Aires',
    'Bs As': 'Buenos Aires',
    'Bs. As.': 'Buenos Aires',
    'BsAs': 'Buenos Aires',
    'Capital Federal': 'Buenos Aires',
    'CABA': 'Buenos Aires',
    'Ciudad De Buenos Aires': 'Buenos Aires',
    'Buenos Aires, Moreno': 'Buenos Aires',
    'Buenos Aires Caba': 'Buenos Aires',
    'Argentina Buenos Aires': 'Buenos Aires',
    'Baires': 'Buenos Aires',
    
    // Uruguay cities
    'MONTEVIDEO': 'Montevideo',
    'Montevidio': 'Montevideo',
    'MVD': 'Montevideo',
    'Mvd': 'Montevideo',
    'Mtv': 'Montevideo',
    'MTV': 'Montevideo',
    'Montevideano': 'Montevideo',
    'Atahualpa, Montevideo': 'Montevideo',
    
    'Ciudad De La Costa': 'Ciudad de la Costa',
    'CIUDAD DE LA COSTA': 'Ciudad de la Costa',
    'Ciudad de la Costa': 'Ciudad de la Costa',
    'Ciudad De La Costa, Montes De Solymar': 'Ciudad de la Costa',
    'Cuidad De La Costa': 'Ciudad de la Costa',
    
    'San Jose': 'San José',
    'San José': 'San José',
    'San Jose De Mayo': 'San José de Mayo',
    'San José De Mayo': 'San José de Mayo',
    
    'Canelones': 'Canelones',
    'CANELONES': 'Canelones',
    'Canelones Ciudad': 'Canelones',
    'Canelones, Paso Carrasco': 'Canelones',
    
    // Uruguay
    'Maldonado': 'Maldonado',
    'MALDONADO': 'Maldonado',
    'Punta Del Este': 'Punta del Este',
    'PUNTA DEL ESTE': 'Punta del Este',
    'Pde': 'Punta del Este',
    'PDE': 'Punta del Este',
    'Salto': 'Salto',
    'SALTO': 'Salto',
    'Paysandu': 'Paysandú',
    'PAYSANDU': 'Paysandú',
    
    // Chile
    'Santiago': 'Santiago',
    'SANTIAGO': 'Santiago',
    'Santiago De Chile': 'Santiago',
    'Stgo': 'Santiago',
    'STGO': 'Santiago',
    'Valparaiso': 'Valparaíso',
    'VALPARAISO': 'Valparaíso',
    'Valparaíso': 'Valparaíso',
    'Viña Del Mar': 'Viña del Mar',
    'VIÑA DEL MAR': 'Viña del Mar',
    
    // Brazil
    'Sao Paulo': 'São Paulo',
    'SAO PAULO': 'São Paulo',
    'São Paulo': 'São Paulo',
    'SP': 'São Paulo',
    'Rio': 'Rio de Janeiro',
    'RIO': 'Rio de Janeiro',
    'Rio De Janeiro': 'Rio de Janeiro',
    'RJ': 'Rio de Janeiro',
    
    // Paraguay
    'Asuncion': 'Asunción',
    'ASUNCION': 'Asunción',
    'Asunción': 'Asunción',
    'ASU': 'Asunción',
    'Ciudad Del Este': 'Ciudad del Este',
    'CIUDAD DEL ESTE': 'Ciudad del Este',
    
    // Bolivia
    'La Paz': 'La Paz',
    'LA PAZ': 'La Paz',
    'Santa Cruz': 'Santa Cruz',
    'SANTA CRUZ': 'Santa Cruz',
    'Santa Cruz De La Sierra': 'Santa Cruz',
    
    // Peru
    'Lima': 'Lima',
    'LIMA': 'Lima',
    'Cusco': 'Cusco',
    'CUSCO': 'Cusco',
    'Cuzco': 'Cusco',
    
    // Colombia
    'Bogota': 'Bogotá',
    'BOGOTA': 'Bogotá',
    'Bogotá': 'Bogotá',
    'BOG': 'Bogotá',
    'Medellin': 'Medellín',
    'MEDELLIN': 'Medellín',
    'Medellín': 'Medellín',
    'MDE': 'Medellín',
    
    // Venezuela
    'Caracas': 'Caracas',
    'CARACAS': 'Caracas',
    'CCS': 'Caracas',
    
    // Ecuador
    'Quito': 'Quito',
    'QUITO': 'Quito',
    'UIO': 'Quito',
    'Guayaquil': 'Guayaquil',
    'GUAYAQUIL': 'Guayaquil',
    'GYE': 'Guayaquil'
  };

  // First try exact match
  if (cityMappings[name]) {
    return cityMappings[name];
  }
  
  // Then try normalized version
  if (cityMappings[normalized]) {
    return cityMappings[normalized];
  }
  
  // If no mapping found, return the normalized version
  return normalized;
};

/**
 * Interface for city data with name and percentage
 */
export interface CityData {
  name: string;
  percentage: number;
}

/**
 * Interface for country data with name, percentage and cities
 */
export interface Country {
  name: string;
  percentage: number;
  cities: CityData[];
}

/**
 * Interface for product data
 */
export interface ProductData {
  id: string;
  sales: number;
  revenue: number;
  name?: string;
}

/**
 * Aggregates city data by normalizing names and combining percentages
 * @param cities Array of city data objects with name and percentage
 * @returns Object containing mainCities (>=2%) and smallCities (<2%)
 */
export const aggregateCityData = (cities: CityData[]) => {
  const aggregatedCities: { [key: string]: number } = {};
  
  cities.forEach(city => {
    const normalizedName = normalizeLocationName(city.name);
    aggregatedCities[normalizedName] = (aggregatedCities[normalizedName] || 0) + city.percentage;
  });
  
  // Convert to array and sort by percentage
  const sortedCities = Object.entries(aggregatedCities)
    .map(([name, percentage]) => ({
      name,
      percentage: Number(percentage.toFixed(1)) // Round to 1 decimal place
    }))
    .sort((a, b) => b.percentage - a.percentage);

  // Separate cities into main and small based on percentage
  const mainCities = sortedCities.filter(city => city.percentage >= 2);
  const smallCities = sortedCities.filter(city => city.percentage < 2);

  return { mainCities, smallCities };
};

/**
 * Formats a number as currency in UYU (Uruguayan Peso)
 * @param amount The amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-UY', {
    style: 'currency',
    currency: 'UYU'
  }).format(amount);
}; 