import os
import sys
import logging
import mysql.connector
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
import json
import argparse
import time
from pymongo import MongoClient
from pymongo.errors import NetworkTimeout, ConnectionFailure, OperationFailure

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection, db_analysis
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    META_CAMPAIGN_COLLECTION = 'meta_ad_campaigns'
    META_SALES_CORRELATION_COLLECTION = 'meta_sales_correlation'
    PRODUCT_DETAILS_CACHE_COLLECTION = 'product_details_cache'
    
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    
    # Add constants for store filtering to match update_active_stores.py
    INACTIVE_STORE_EXCEPTIONS = getattr(settings, 'INACTIVE_STORE_EXCEPTIONS', [])
    NEW_STORE_CUTOFF_YEAR = getattr(settings, 'NEW_STORE_CUTOFF_YEAR', 2025)
    
    # Add configurable order statuses with documentation
    # Common order statuses:
    # 1 = Pending/Created
    # 2 = Paid/Confirmed
    # 3 = Processing
    # 4 = Shipped
    # 5 = Delivered
    # 6 = Cancelled
    # 7 = Completed
    VALID_ORDER_STATUSES = getattr(settings, 'VALID_ORDER_STATUSES', [2, 5, 7])
    
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# --- Helper Functions ---

def get_stores_to_process(mysql_conn) -> List[Dict[str, Any]]:
    """
    Fetches active stores from the MySQL database, using the same criteria as
    update_active_stores.py to ensure consistency.
    """
    stores: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available for get_stores_to_process.")
        return stores
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            exception_clause = f"OR s.id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
            
            query = f"""
            SELECT 
                s.id_store, s.symbol, s.name
            FROM stores s
            WHERE 
                (s.active = 1
                {exception_clause}
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
            ORDER BY s.id_store
            """
            cursor.execute(query)
            stores = cursor.fetchall()
            logger.info(f"Found {len(stores)} stores to process using consistent criteria.")
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching stores: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching stores from MySQL: {e}", exc_info=True)
    return stores

def get_meta_campaigns_for_store(store_id: str, mongo_db) -> List[Dict[str, Any]]:
    """Fetch Meta campaign data for a specific store from MongoDB."""
    try:
        campaign_collection = mongo_db[META_CAMPAIGN_COLLECTION]
        campaigns = list(campaign_collection.find({"store_id": str(store_id)}))
        
        # Process campaigns for use
        for campaign in campaigns:
            # Convert ObjectId to string
            if "_id" in campaign:
                campaign["_id"] = str(campaign["_id"])
                
            # Ensure date fields are datetime objects
            if "start_time" in campaign and isinstance(campaign["start_time"], str):
                try:
                    campaign["start_time"] = datetime.fromisoformat(campaign["start_time"].replace("Z", "+00:00"))
                except ValueError:
                    # Handle invalid date formats
                    pass
                
            if "end_time" in campaign and isinstance(campaign["end_time"], str):
                try:
                    campaign["end_time"] = datetime.fromisoformat(campaign["end_time"].replace("Z", "+00:00"))
                except ValueError:
                    # Handle invalid date formats
                    pass
        
        logger.info(f"Found {len(campaigns)} Meta campaigns for store {store_id}")
        return campaigns
    except Exception as e:
        logger.error(f"Error fetching Meta campaigns for store {store_id}: {e}", exc_info=True)
        return []

def get_daily_sales_data(store_id: int, mysql_conn, start_date=None, end_date=None) -> Dict[str, Any]:
    """
    Get daily sales data for a store within a date range.
    Returns a dictionary with daily sales totals and product-specific sales.
    """
    sales_data = {
        "daily_totals": [],
        "product_sales": []
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_daily_sales_data (store: {store_id}).")
        return sales_data
        
    try:
        # Set default date range if not provided (last 120 days)
        if not end_date:
            end_date = datetime.now(timezone.utc)
        if not start_date:
            start_date = end_date - timedelta(days=120)
            
        # Format dates for SQL
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Convert order statuses list to string for SQL
        order_statuses_str = ','.join(str(status) for status in VALID_ORDER_STATUSES)
        
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Query 1: Get daily sales totals with proper filtering
            daily_totals_query = f"""
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       created_at,
                       id_order_status,
                       deleted_at,
                       active
                FROM store_orders
                WHERE id_store = %s
                UNION ALL
                SELECT id_order            AS id_store_order,
                       id_store,
                       date                AS created_at,
                       id_status           AS id_order_status,
                       NULL                AS deleted_at,
                       1                   AS active
                FROM orders
                WHERE id_store = %s
            )
            SELECT
                DATE(ao.created_at) AS sale_date,
                COUNT(DISTINCT ao.id_store_order) AS orders,
                SUM(sop.qty) AS units,
                SUM(CAST(sop.price AS DECIMAL(10,2)) * sop.qty) AS daily_revenue
            FROM all_orders ao
            JOIN store_ordered_products sop ON ao.id_store_order = sop.id_store_order
            WHERE ao.deleted_at IS NULL
              AND sop.deleted_at IS NULL
              AND ao.active = 1
              AND sop.active = 1
              AND ao.id_order_status IN ({order_statuses_str})
              AND ao.created_at BETWEEN %s AND %s
            GROUP BY DATE(ao.created_at)
            ORDER BY DATE(ao.created_at);
            """
            
            cursor.execute(daily_totals_query, (store_id, store_id, start_date_str, end_date_str))
            daily_totals = cursor.fetchall()
            
            # Format the results with proper decimal handling
            sales_data["daily_totals"] = [
                {
                    "date": row["sale_date"].strftime('%Y-%m-%d') if row["sale_date"] else None,
                    "orders": int(row["orders"]) if row["orders"] else 0,
                    "units": int(row["units"]) if row["units"] else 0,
                    "revenue": float(Decimal(str(row["daily_revenue"])).quantize(Decimal('0.01'))) if row["daily_revenue"] else 0.0
                }
                for row in daily_totals
            ]
            
            # Query 2: Get product-specific sales by date with LEFT JOIN to handle deleted products
            product_sales_query = f"""
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       created_at,
                       id_order_status,
                       deleted_at,
                       active
                FROM store_orders
                WHERE id_store = %s
                UNION ALL
                SELECT id_order              AS id_store_order,
                       id_store,
                       date                  AS created_at,
                       id_status             AS id_order_status,
                       NULL                  AS deleted_at,
                       1                     AS active
                FROM orders
                WHERE id_store = %s
            )
            SELECT
                DATE(ao.created_at) AS sale_date,
                sop.id_product AS product_id,
                COALESCE(p.name, CONCAT('Product #', sop.id_product)) AS product_name,
                SUM(sop.qty) AS units_sold,
                SUM(CAST(sop.price AS DECIMAL(10,2)) * sop.qty) AS revenue
            FROM all_orders ao
            JOIN store_ordered_products sop ON ao.id_store_order = sop.id_store_order
            LEFT JOIN products p ON sop.id_product = p.id_product
            WHERE ao.deleted_at IS NULL
              AND sop.deleted_at IS NULL
              AND ao.active = 1
              AND sop.active = 1
              AND (p.id_product IS NULL OR (p.status = 1 AND p.active = 1))
              AND ao.id_order_status IN ({order_statuses_str})
              AND ao.created_at BETWEEN %s AND %s
            GROUP BY DATE(ao.created_at), sop.id_product, p.name
            ORDER BY DATE(ao.created_at), sop.id_product;
            """
            
            cursor.execute(product_sales_query, (store_id, store_id, start_date_str, end_date_str))
            product_sales = cursor.fetchall()
            
            # Format the results with proper null handling
            sales_data["product_sales"] = [
                {
                    "date": row["sale_date"].strftime('%Y-%m-%d') if row["sale_date"] else None,
                    "product_id": str(row["product_id"]),
                    "product_name": row["product_name"] or f"Product #{row['product_id']}",
                    "units_sold": int(row["units_sold"]) if row["units_sold"] else 0,
                    "revenue": float(Decimal(str(row["revenue"])).quantize(Decimal('0.01'))) if row["revenue"] else 0.0
                }
                for row in product_sales
            ]
            
            logger.info(f"Fetched {len(sales_data['daily_totals'])} days of sales data and {len(sales_data['product_sales'])} product sales records for store {store_id}")
            
            return sales_data
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching sales data for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching sales data for store {store_id}: {e}", exc_info=True)
        
    return sales_data

def calculate_campaign_correlation(campaigns, sales_data):
    """
    Calculate correlation between Meta campaigns and sales data.
    Returns enhanced campaign data with correlation metrics.
    """
    enhanced_campaigns = []
    
    # Create a date-indexed dictionary of daily sales for faster lookups
    daily_sales_by_date = {}
    for day in sales_data["daily_totals"]:
        if "date" in day and day["date"]:
            daily_sales_by_date[day["date"]] = day
    
    for campaign in campaigns:
        try:
            # Skip if missing required date fields
            if "start_time" not in campaign or "end_time" not in campaign:
                logger.warning(f"Campaign {campaign.get('id', 'unknown')} missing start/end time, skipping correlation")
                enhanced_campaigns.append(campaign)
                continue
                
            # Convert dates to proper format for comparison
            start_date = campaign["start_time"]
            end_date = campaign["end_time"]
            
            if not isinstance(start_date, datetime):
                logger.warning(f"Campaign {campaign.get('id', 'unknown')} has invalid start_time format")
                enhanced_campaigns.append(campaign)
                continue
                
            if not isinstance(end_date, datetime):
                logger.warning(f"Campaign {campaign.get('id', 'unknown')} has invalid end_time format")
                enhanced_campaigns.append(campaign)
                continue
            
            # Initialize correlation data
            correlation_data = []
            
            # Calculate days to analyze (7 days before campaign to 7 days after)
            analysis_start = start_date - timedelta(days=7)
            analysis_end = end_date + timedelta(days=7)
            
            # Create a list of dates for analysis window
            current_date = analysis_start
            while current_date <= analysis_end:
                date_str = current_date.strftime('%Y-%m-%d')
                
                # Get sales data for this date
                sales_for_date = daily_sales_by_date.get(date_str, {
                    "orders": 0,
                    "units": 0,
                    "revenue": 0.0
                })
                
                # Determine if this date is during campaign
                is_during_campaign = start_date <= current_date <= end_date
                
                # Calculate days from campaign start (-ve for before, +ve for after)
                days_from_start = (current_date - start_date).days
                
                # Add to correlation data
                correlation_data.append({
                    "date": date_str,
                    "during_campaign": is_during_campaign,
                    "days_from_campaign_start": days_from_start,
                    "orders": sales_for_date.get("orders", 0),
                    "units": sales_for_date.get("units", 0),
                    "revenue": sales_for_date.get("revenue", 0.0)
                })
                
                current_date += timedelta(days=1)
            
            # Calculate average sales before, during, and after campaign
            pre_campaign_data = [day for day in correlation_data if day["days_from_campaign_start"] < 0]
            during_campaign_data = [day for day in correlation_data if day["during_campaign"]]
            post_campaign_data = [day for day in correlation_data if day["days_from_campaign_start"] > 0 and not day["during_campaign"]]
            
            # Calculate averages
            avg_daily_sales_pre = sum(day["revenue"] for day in pre_campaign_data) / max(len(pre_campaign_data), 1)
            avg_daily_sales_during = sum(day["revenue"] for day in during_campaign_data) / max(len(during_campaign_data), 1)
            avg_daily_sales_post = sum(day["revenue"] for day in post_campaign_data) / max(len(post_campaign_data), 1)
            
            # Calculate sales lift percentages
            sales_lift_during = ((avg_daily_sales_during - avg_daily_sales_pre) / max(avg_daily_sales_pre, 1)) * 100 if avg_daily_sales_pre > 0 else 0
            sales_lift_post = ((avg_daily_sales_post - avg_daily_sales_pre) / max(avg_daily_sales_pre, 1)) * 100 if avg_daily_sales_pre > 0 else 0
            
            # Calculate total sales during campaign period
            total_sales_during = sum(day["revenue"] for day in during_campaign_data)
            total_orders_during = sum(day["orders"] for day in during_campaign_data)
            total_units_during = sum(day["units"] for day in during_campaign_data)
            
            # Add enhanced metrics to campaign
            enhanced_campaign = campaign.copy()
            enhanced_campaign["sales_correlation"] = {
                "correlation_data": correlation_data,
                "metrics": {
                    "avg_daily_sales_pre": avg_daily_sales_pre,
                    "avg_daily_sales_during": avg_daily_sales_during,
                    "avg_daily_sales_post": avg_daily_sales_post,
                    "sales_lift_during_percent": sales_lift_during,
                    "sales_lift_post_percent": sales_lift_post,
                    "total_sales_during": total_sales_during,
                    "total_orders_during": total_orders_during,
                    "total_units_during": total_units_during
                }
            }
            
            enhanced_campaigns.append(enhanced_campaign)
            
        except Exception as e:
            logger.error(f"Error calculating correlation for campaign {campaign.get('id', 'unknown')}: {e}", exc_info=True)
            enhanced_campaigns.append(campaign)
    
    return enhanced_campaigns

def update_meta_sales_correlation(store_id: str, enhanced_campaigns: List[Dict[str, Any]], sales_data: Dict[str, Any], mongo_db):
    """Update the Meta sales correlation data in MongoDB."""
    try:
        # Get the collection
        collection = mongo_db[META_SALES_CORRELATION_COLLECTION]
        
        # Fetch existing document to check if update is needed, using _id as the store_id
        existing_doc = collection.find_one({"_id": str(store_id)})
        
        # Check if update is needed by comparing existing data with new data
        update_needed = True
        update_reason = "Initial document creation"
        
        if existing_doc:
            # 1. Check if new sales data exists
            existing_last_date = None
            new_last_date = None
            
            if 'sales_data' in existing_doc and 'daily_sales' in existing_doc['sales_data'] and existing_doc['sales_data']['daily_sales']:
                # Find latest date in existing data
                existing_dates = [day.get('date') for day in existing_doc['sales_data']['daily_sales'] if day.get('date')]
                if existing_dates:
                    existing_last_date = max(existing_dates)
            
            # Find latest date in new data
            new_dates = [day.get('date') for day in sales_data['daily_totals'] if day.get('date')]
            if new_dates:
                new_last_date = max(new_dates)
            
            # 2. Check if there are new campaigns
            existing_campaign_ids = set()
            if 'enhanced_campaigns' in existing_doc:
                existing_campaign_ids = {c.get('id') for c in existing_doc['enhanced_campaigns'] if 'id' in c}
            
            new_campaign_ids = {c.get('id') for c in enhanced_campaigns if 'id' in c}
            
            has_new_campaigns = len(new_campaign_ids - existing_campaign_ids) > 0
            has_newer_sales = new_last_date and (not existing_last_date or new_last_date > existing_last_date)
            
            # Only update if there are new campaigns or newer sales data
            if not has_new_campaigns and not has_newer_sales:
                update_needed = False
                logger.info(f"No new data for store {store_id}, skipping Meta sales correlation update")
                return True
            elif has_new_campaigns:
                update_reason = "New Meta campaigns detected"
            elif has_newer_sales:
                update_reason = f"New sales data detected (latest: {new_last_date}, previous: {existing_last_date})"

        # Convert MongoDB ObjectIDs to strings for JSON serialization
        for campaign in enhanced_campaigns:
            if "_id" in campaign and not isinstance(campaign["_id"], str):
                campaign["_id"] = str(campaign["_id"])

        # Create document with summary data, using store_id as _id
        correlation_doc = {
            "_id": str(store_id), # Use store_id as the primary key
            "enhanced_campaigns": enhanced_campaigns,
            "sales_data": {
                "summary": {
                    "total_days": len(sales_data["daily_totals"]),
                    "total_orders": sum(day["orders"] for day in sales_data["daily_totals"]),
                    "total_units": sum(day["units"] for day in sales_data["daily_totals"]),
                    "total_revenue": sum(day["revenue"] for day in sales_data["daily_totals"])
                },
                "daily_sales": sales_data["daily_totals"],
                # Only store the first 1000 product sales to avoid excessive document size
                "product_sales": sales_data["product_sales"][:1000] if len(sales_data["product_sales"]) > 1000 else sales_data["product_sales"]
            },
            "last_updated": datetime.now(timezone.utc)
        }

        # Update MongoDB using _id as the filter
        result = collection.update_one(
            {"_id": str(store_id)},
            {"$set": correlation_doc},
            upsert=True
        )

        if result:
            if result.upserted_id:
                logger.info(f"Inserted new Meta sales correlation data for store {store_id}")
            elif result.modified_count > 0:
                logger.info(f"Updated Meta sales correlation data for store {store_id}: {update_reason}")
            else:
                logger.info(f"No changes needed for Meta sales correlation data for store {store_id} (data identical)")
        
        return True
    except Exception as e:
        logger.error(f"Error updating Meta sales correlation data for store {store_id}: {e}", exc_info=True)
        return False

def update_product_details_with_sales_data(store_id: str, sales_data: Dict[str, Any], mongo_db, currency_symbol: str = "$", has_new_meta_campaigns: bool = False):
    """
    Update the product_details_cache collection with daily sales data.
    
    Parameters:
    - store_id: The store ID
    - sales_data: The sales data dictionary from get_daily_sales_data
    - mongo_db: MongoDB database connection
    - currency_symbol: The currency symbol for the store
    - has_new_meta_campaigns: Whether there are new Meta campaigns that should trigger an update
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Define a retry helper function for MongoDB operations
        def retry_mongo_operation(operation, *args, **kwargs):
            max_retries = 3
            retry_delay = 5  # seconds between retries
            last_error = None
            
            # Define timeout escalation sequence (in seconds)
            timeouts = [120, 300, 600]  # 2 min, 5 min, 10 min
            
            for attempt in range(max_retries):
                try:
                    # Use current client for first attempt
                    if attempt == 0:
                        return operation(*args, **kwargs)
                    else:
                        # For retries, create a temporary client with longer timeout
                        logger.info(f"Creating temporary MongoDB client with {timeouts[attempt]} second timeout for retry")
                        
                        # Get connection string from settings
                        from config.settings import get_settings
                        settings_instance = get_settings()
                        
                        if hasattr(settings_instance, 'MONGODB_CONNECTION'):
                            # Create temp client with longer timeout
                            temp_client = MongoClient(
                                settings_instance.MONGODB_CONNECTION,
                                socketTimeoutMS=timeouts[attempt] * 1000,
                                connectTimeoutMS=timeouts[attempt] * 1000,
                                serverSelectionTimeoutMS=timeouts[attempt] * 1000
                            )
                            
                            # Get temp collection and retry operation
                            temp_db = temp_client[MONGO_DB_ANALYSIS_NAME]
                            temp_collection = temp_db[PRODUCT_DETAILS_CACHE_COLLECTION]
                            
                            # Replace collection in the operation with temp_collection
                            if operation.__self__ == collection:
                                # Replace the first argument (self) with temp_collection
                                bound_method = getattr(temp_collection, operation.__name__)
                                result = bound_method(*args, **kwargs)
                                
                                # Close temp client after operation
                                temp_client.close()
                                return result
                            else:
                                # Can't rebind the operation, use original
                                return operation(*args, **kwargs)
                        else:
                            # Fall back to original operation
                            return operation(*args, **kwargs)
                        
                except (NetworkTimeout, ConnectionFailure) as err:
                    last_error = err
                    if attempt < max_retries - 1:
                        logger.warning(f"MongoDB timeout error, retrying in {retry_delay} seconds with longer timeout (attempt {attempt+1}/{max_retries}): {err}")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff for delay between retries
                    else:
                        # Last attempt failed
                        logger.error(f"MongoDB operation failed after {max_retries} attempts with increasing timeouts: {err}")
                        raise
            return None  # Should never reach here due to raise above
            
        # First, check if the store exists in product_details_cache
        existing_doc = retry_mongo_operation(collection.find_one, {"_id": str(store_id)})
        
        # Check if update is needed by comparing existing data with new data
        update_needed = True
        update_reason = "Initial document creation"
        
        if existing_doc and "sales_by_date" in existing_doc:
            # Find latest date in existing data
            existing_last_date = None
            new_last_date = None
            
            if existing_doc["sales_by_date"]:
                existing_dates = [day.get('date') for day in existing_doc["sales_by_date"] if day.get('date')]
                if existing_dates:
                    existing_last_date = max(existing_dates)
            
            # Find latest date in new data
            new_dates = [day.get('date') for day in sales_data["daily_totals"] if day.get('date')]
            if new_dates:
                new_last_date = max(new_dates)
            
            # Check if there's newer sales data
            has_newer_sales = new_last_date and (not existing_last_date or new_last_date > existing_last_date)
            
            # Only update if there is newer sales data OR new Meta campaigns
            if not has_newer_sales and not has_new_meta_campaigns:
                # No new data to update
                logger.info(f"No new sales data or Meta campaigns for store {store_id}, skipping product details update")
                return True
            elif has_new_meta_campaigns:
                update_reason = "New Meta campaigns detected"
            elif has_newer_sales:
                update_reason = f"New sales data detected (latest: {new_last_date}, previous: {existing_last_date or 'none'})"
        
        # If we get here, we need to update
        
        # Prepare the sales_by_date field
        sales_by_date = []
        
        # Group products by date for summary
        product_counts_by_date = {}
        
        # Process daily sales
        for day in sales_data["daily_totals"]:
            date_str = day["date"]
            
            # Count unique products for this date
            if date_str not in product_counts_by_date:
                # Find products sold on this date
                products_on_date = [p for p in sales_data["product_sales"] if p["date"] == date_str]
                unique_products = set(p["product_id"] for p in products_on_date)
                product_counts_by_date[date_str] = len(unique_products)
            
            # Create summary entry for this date
            summary_entry = {
                "date": date_str,
                "total_revenue": day["revenue"],
                "total_units": day["units"],
                "order_count": day["orders"],
                "product_count": product_counts_by_date[date_str]
            }
            sales_by_date.append(summary_entry)
        
        # Prepare product details from sales_data
        # Group by product_id instead of listing each sale separately
        products_with_sales = []
        
        # Group products by product_id
        product_sales_by_id = {}
        for product_sale in sales_data["product_sales"]:
            product_id = product_sale["product_id"]
            if product_id not in product_sales_by_id:
                product_sales_by_id[product_id] = {
                    "product_id": product_id,
                    "name": product_sale["product_name"],
                    "sales_units": 0,
                    "revenue": 0.0,
                    "currency_symbol": currency_symbol,
                    "sales_details": []
                }
            
            # Add to totals
            product_sales_by_id[product_id]["sales_units"] += product_sale["units_sold"]
            product_sales_by_id[product_id]["revenue"] += product_sale["revenue"]
            
            # Add date details
            product_sales_by_id[product_id]["sales_details"].append({
                "date": product_sale["date"],
                "units_sold": product_sale["units_sold"],
                "revenue": product_sale["revenue"]
            })
        
        # Convert to list for storage
        products_with_sales = list(product_sales_by_id.values())
        
        # Prepare the update document
        update_doc = {
            "sales_by_date": sales_by_date,
            "overall_last_updated": datetime.now(timezone.utc)
        }
        
        # If existing document has products, update them
        if existing_doc and "products" in existing_doc:
            # Keep existing products, just update the sales_by_date
            logger.info(f"Updating existing product_details_cache entry for store {store_id}: {update_reason}")
        else:
            # No existing products, initialize with sales data
            update_doc["products"] = products_with_sales
            logger.info(f"Creating new product_details_cache entry for store {store_id}")
        
        # Update MongoDB
        result = retry_mongo_operation(
            collection.update_one,
            {"_id": str(store_id)},
            {"$set": update_doc},
            upsert=True
        )
        
        if result:
            if result.upserted_id:
                logger.info(f"Inserted new product details with sales data for store {store_id}")
            elif result.modified_count > 0:
                logger.info(f"Updated product details with sales data for store {store_id}")
            else:
                logger.info(f"No changes needed for product details with sales data for store {store_id}")
        else:
            logger.warning(f"MongoDB update operation returned no result for store {store_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error updating product details with sales data for store {store_id}: {e}", exc_info=True)
        return False

def update_product_details_with_sales_data_batched(store_id: str, sales_data: Dict[str, Any], mongo_db, currency_symbol: str = "$", has_new_meta_campaigns: bool = False):
    """
    Update the product_details_cache collection with daily sales data in batches to avoid timeouts.
    Used for stores with very large amounts of sales data (like store 961).
    
    Parameters:
    - store_id: The store ID
    - sales_data: The sales data dictionary from get_daily_sales_data
    - mongo_db: MongoDB database connection
    - currency_symbol: The currency symbol for the store
    - has_new_meta_campaigns: Whether there are new Meta campaigns that should trigger an update
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Check if mongo_db is None first
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Define a retry helper function for MongoDB operations
        def retry_mongo_operation(operation, *args, **kwargs):
            max_retries = 3
            retry_delay = 5  # seconds between retries
            last_error = None
            
            # Define timeout escalation sequence (in seconds)
            timeouts = [120, 300, 600]  # 2 min, 5 min, 10 min
            
            for attempt in range(max_retries):
                try:
                    # Use current client for first attempt
                    if attempt == 0:
                        return operation(*args, **kwargs)
                    else:
                        # For retries, create a temporary client with longer timeout
                        logger.info(f"Creating temporary MongoDB client with {timeouts[attempt]} second timeout for retry")
                        
                        # Get connection string from settings
                        from config.settings import get_settings
                        settings_instance = get_settings()
                        
                        if hasattr(settings_instance, 'MONGODB_CONNECTION'):
                            # Create temp client with longer timeout
                            temp_client = MongoClient(
                                settings_instance.MONGODB_CONNECTION,
                                socketTimeoutMS=timeouts[attempt] * 1000,
                                connectTimeoutMS=timeouts[attempt] * 1000,
                                serverSelectionTimeoutMS=timeouts[attempt] * 1000
                            )
                            
                            # Get temp collection and retry operation
                            temp_db = temp_client[MONGO_DB_ANALYSIS_NAME]
                            temp_collection = temp_db[PRODUCT_DETAILS_CACHE_COLLECTION]
                            
                            # Replace collection in the operation with temp_collection
                            if operation.__self__ == collection:
                                # Replace the first argument (self) with temp_collection
                                bound_method = getattr(temp_collection, operation.__name__)
                                result = bound_method(*args, **kwargs)
                                
                                # Close temp client after operation
                                temp_client.close()
                                return result
                            else:
                                # Can't rebind the operation, use original
                                return operation(*args, **kwargs)
                        else:
                            # Fall back to original operation
                            return operation(*args, **kwargs)
                        
                except (NetworkTimeout, ConnectionFailure) as err:
                    last_error = err
                    if attempt < max_retries - 1:
                        logger.warning(f"MongoDB timeout error, retrying in {retry_delay} seconds with longer timeout (attempt {attempt+1}/{max_retries}): {err}")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff for delay between retries
                    else:
                        # Last attempt failed
                        logger.error(f"MongoDB operation failed after {max_retries} attempts with increasing timeouts: {err}")
                        raise
            return None  # Should never reach here due to raise above
        
        # Step 1: First check if the store exists in product_details_cache
        existing_doc = retry_mongo_operation(collection.find_one, {"_id": str(store_id)})
        
        # Check if update is needed by comparing existing data with new data
        update_needed = True
        update_reason = "Initial document creation"
        
        if existing_doc and "sales_by_date" in existing_doc:
            # Find latest date in existing data
            existing_last_date = None
            new_last_date = None
            
            if existing_doc["sales_by_date"]:
                existing_dates = [day.get('date') for day in existing_doc["sales_by_date"] if day.get('date')]
                if existing_dates:
                    existing_last_date = max(existing_dates)
            
            # Find latest date in new data
            new_dates = [day.get('date') for day in sales_data["daily_totals"] if day.get('date')]
            if new_dates:
                new_last_date = max(new_dates)
            
            # Check if there's newer sales data
            has_newer_sales = new_last_date and (not existing_last_date or new_last_date > existing_last_date)
            
            # Only update if there is newer sales data OR new Meta campaigns
            if not has_newer_sales and not has_new_meta_campaigns:
                # No new data to update
                logger.info(f"No new sales data or Meta campaigns for store {store_id}, skipping batched product details update")
                return True
            elif has_new_meta_campaigns:
                update_reason = "New Meta campaigns detected"
                logger.info(f"Updating batched product details for store {store_id}: {update_reason}")
            elif has_newer_sales:
                update_reason = f"New sales data detected (latest: {new_last_date}, previous: {existing_last_date or 'none'})"
                logger.info(f"Updating batched product details for store {store_id}: {update_reason}")
        
        # If we get here, we need to update
        
        if existing_doc is None:
            logger.warning(f"Cache document for store {store_id} not found. Creating new document.")
            existing_doc = {"_id": str(store_id), "products": []}
        
        if 'products' not in existing_doc or not isinstance(existing_doc['products'], list):
            logger.error(f"Cache document for store {store_id} does not have a valid 'products' array.")
            return False
        
        # Step 2: Prepare the sales_by_date field (this is not batched as it's usually small)
        sales_by_date = []
        
        # Group products by date for summary
        product_counts_by_date = {}
        
        # Process daily sales
        for day in sales_data["daily_totals"]:
            date_str = day["date"]
            
            # Count unique products for this date
            if date_str not in product_counts_by_date:
                # Find products sold on this date
                products_on_date = [p for p in sales_data["product_sales"] if p["date"] == date_str]
                unique_products = set(p["product_id"] for p in products_on_date)
                product_counts_by_date[date_str] = len(unique_products)
            
            # Create summary entry for this date
            summary_entry = {
                "date": date_str,
                "total_revenue": day["revenue"],
                "total_units": day["units"],
                "order_count": day["orders"],
                "product_count": product_counts_by_date[date_str]
            }
            sales_by_date.append(summary_entry)
        
        # Step 3: Update sales_by_date immediately as it's small and not product-related
        update_result = retry_mongo_operation(
            collection.update_one,
            {"_id": str(store_id)},
            {
                "$set": {
                    "sales_by_date": sales_by_date,
                    "overall_last_updated": datetime.now(timezone.utc)
                }
            },
            upsert=True
        )
        
        logger.info(f"Updated sales_by_date summary for store {store_id} with {len(sales_by_date)} daily entries")
        
        # Step 4: Prepare product details from sales_data
        # Group by product_id
        product_sales_by_id = {}
        for product_sale in sales_data["product_sales"]:
            product_id = product_sale["product_id"]
            if product_id not in product_sales_by_id:
                product_sales_by_id[product_id] = {
                    "product_id": product_id,
                    "name": product_sale["product_name"],
                    "sales_units": 0,
                    "revenue": 0.0,
                    "currency_symbol": currency_symbol,
                    "sales_details": []
                }
            
            # Add to totals
            product_sales_by_id[product_id]["sales_units"] += product_sale["units_sold"]
            product_sales_by_id[product_id]["revenue"] += product_sale["revenue"]
            
            # Add date details
            product_sales_by_id[product_id]["sales_details"].append({
                "date": product_sale["date"],
                "units_sold": product_sale["units_sold"],
                "revenue": product_sale["revenue"]
            })
        
        # Convert to list for storage
        products_with_sales = list(product_sales_by_id.values())
        product_count = len(products_with_sales)
        
        # Step 5: Process products in batches of 50
        batch_size = 50
        total_batches = (product_count + batch_size - 1) // batch_size  # Ceiling division
        modified_count = 0
        success = True
        
        logger.info(f"Processing {product_count} products with sales data in {total_batches} batches for store {store_id}")
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, product_count)
            batch_products = products_with_sales[start_idx:end_idx]
            
            logger.info(f"Processing batch {batch_num + 1}/{total_batches} (products {start_idx}-{end_idx-1}) for store {store_id}")
            
            # Store 961 special handling with more detailed logging
            if int(store_id) == 961:
                logger.info(f"Store 961 special handling: Processing batch {batch_num + 1}/{total_batches} - progress: {int((batch_num + 1) / total_batches * 100)}%")
            
            # Create a batch update operation
            update_operations = {}
            
            # If this is the first batch and there are no existing products, initialize the products array
            if batch_num == 0 and (not existing_doc or "products" not in existing_doc or len(existing_doc["products"]) == 0):
                update_operations["products"] = batch_products
            else:
                # Otherwise append to the existing products array
                for i, product in enumerate(batch_products):
                    product_id = product["product_id"]
                    
                    # Check if this product already exists in the document
                    existing_product_index = None
                    for j, existing_product in enumerate(existing_doc.get("products", [])):
                        if existing_product.get("product_id") == product_id:
                            existing_product_index = j
                            break
                    
                    if existing_product_index is not None:
                        # Update existing product's sales data
                        update_operations[f"products.{existing_product_index}.sales_units"] = product["sales_units"]
                        update_operations[f"products.{existing_product_index}.revenue"] = product["revenue"]
                        update_operations[f"products.{existing_product_index}.sales_details"] = product["sales_details"]
                    else:
                        # Add new product to the end of the array
                        update_operations[f"products.{len(existing_doc.get('products', []))}"] = product
                        # Update our tracking of the document for subsequent batches
                        if "products" not in existing_doc:
                            existing_doc["products"] = []
                        existing_doc["products"].append(product)
            
            # Update MongoDB with this batch using retry logic
            batch_result = retry_mongo_operation(
                collection.update_one,
                {"_id": str(store_id)},
                {"$set": update_operations}
            )
            
            if batch_result and batch_result.modified_count > 0:
                logger.info(f"Updated batch {batch_num + 1} with {len(batch_products)} products for store {store_id}")
                modified_count += len(batch_products)
            else:
                logger.info(f"No changes needed for batch {batch_num + 1} for store {store_id}")
                
            # Small delay between batches to prevent overloading the database
            time.sleep(1)
        
        logger.info(f"Completed updating {modified_count} products with sales data for store {store_id} in {total_batches} batches")
        return True
        
    except Exception as e:
        logger.error(f"Error updating product details with sales data in batches for store {store_id}: {e}", exc_info=True)
        return False

# --- Main Execution ---

def main():
    # Process command-line arguments
    parser = argparse.ArgumentParser(description='Update Meta sales correlation data')
    parser.add_argument('--store_id', type=str, help='Process only a specific store ID')
    args = parser.parse_args()
    
    specific_store_id = args.store_id
    
    logger.info("Starting Meta sales correlation update script")
    logger.info(f"Using order statuses: {VALID_ORDER_STATUSES}")
    if specific_store_id:
        logger.info(f"Processing only store ID: {specific_store_id}")
    else:
        logger.info("Processing all stores")
        
    mysql_conn = None
    mongo_client = None
    processed_count = 0
    failed_count = 0

    try:
        # 1. Establish Connections
        logger.info(f"Connecting to MySQL host: {MYSQL_HOST}, database: {MYSQL_DB_LANUBE}")
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=10
        )
        logger.info("MySQL connection successful")

        logger.info("Connecting to MongoDB...")
        mongo_client = get_mongodb_connection()
        if mongo_client is None:
            logger.error("Failed to establish MongoDB connection. Exiting.")
            raise ConnectionError("MongoDB connection failed.")

        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"MongoDB connection successful (DB: {MONGO_DB_ANALYSIS_NAME})")

        # 2. Get stores to process
        if specific_store_id:
            # If specific store_id is provided, just process that one
            store = None
            with mysql_conn.cursor(dictionary=True) as cursor:
                cursor.execute("""
                    SELECT s.id_store, s.symbol, s.name  
                    FROM stores s
                    WHERE s.id_store = %s AND s.active = 1
                """, (specific_store_id,))
                store = cursor.fetchone()
            
            if store:
                stores = [store]
                # For MySQL rows, access the values directly but add type ignore comments for Pylance
                store_name = store.get('name', 'Unknown') if isinstance(store, dict) else 'Unknown'
                store_id_str = str(store.get('id_store', 'Unknown')) if isinstance(store, dict) else 'Unknown'
                logger.info(f"Found store: {store_name} (ID: {store_id_str})")
            else:
                logger.error(f"Store with ID {specific_store_id} not found or not active")
                return
        else:
            # Otherwise get all active stores
            stores = get_stores_to_process(mysql_conn)
            
        if not stores:
            logger.warning("No active stores found to process. Exiting.")
            return

        # 3. Process each store
        logger.info(f"Processing {len(stores)} stores...")
        for store in stores:
            store_id = None
            try:
                # Get store ID using direct access with type ignore
                if not isinstance(store, dict):
                    logger.warning(f"Store data is not a dictionary: {store}. Skipping.")
                    failed_count += 1
                    continue

                if 'id_store' not in store:
                    logger.warning(f"Skipping store entry due to missing 'id_store': {store}")
                    failed_count += 1
                    continue
                
                store_id_val = store.get('id_store') if isinstance(store, dict) else None
                
                # Add proper error handling for integer conversion
                try:
                    # Convert to string first to handle various numeric types safely
                    store_id = int(str(store_id_val)) if store_id_val is not None else None
                except (ValueError, TypeError):
                    logger.warning(f"Cannot convert store ID '{store_id_val}' to integer. Skipping store.")
                    failed_count += 1
                    continue
                
                store_name = store.get('name', 'Unknown Store') if isinstance(store, dict) else 'Unknown Store'
                logger.info(f"--- Processing store ID: {store_id} ({store_name}) ---")

                # 4. Get Meta campaigns for this store
                meta_campaigns = get_meta_campaigns_for_store(str(store_id), mongo_db)
                if not meta_campaigns:
                    logger.info(f"No Meta campaigns found for store {store_id}, but still processing sales data.")

                # Find date range to analyze based on campaigns
                campaign_start_dates = [c["start_time"] for c in meta_campaigns if "start_time" in c and isinstance(c["start_time"], datetime)]
                campaign_end_dates = [c["end_time"] for c in meta_campaigns if "end_time" in c and isinstance(c["end_time"], datetime)]
                
                # Log campaign date range info if campaigns exist
                if campaign_start_dates and campaign_end_dates:
                    campaign_min = min(campaign_start_dates).strftime('%Y-%m-%d')
                    campaign_max = max(campaign_end_dates).strftime('%Y-%m-%d')
                    logger.info(f"Found Meta campaigns for store {store_id} with date range: {campaign_min} to {campaign_max}")
                
                # Always use full historical range for all stores
                min_date = datetime(2020, 1, 1, tzinfo=timezone.utc)  # Start from 2020 or adjust as needed
                max_date = datetime.now(timezone.utc) + timedelta(days=1)  # Include today
                logger.info(f"Using full historical range for all sales data: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")

                # 5. Get sales data for this store
                sales_data = get_daily_sales_data(store_id, mysql_conn, min_date, max_date)

                # 6. Calculate correlation between campaigns and sales
                enhanced_campaigns = calculate_campaign_correlation(meta_campaigns, sales_data)

                # 7. Update MongoDB with enhanced data
                success = update_meta_sales_correlation(str(store_id), enhanced_campaigns, sales_data, mongo_db)
                
                # Check if there are new Meta campaigns that should trigger a product update
                has_new_meta_campaigns = False
                
                # Check if there are new campaigns by comparing with what's in MongoDB
                if meta_campaigns:
                    # Fetch existing campaigns from MongoDB using _id
                    existing_campaigns = []
                    existing_doc = mongo_db[META_SALES_CORRELATION_COLLECTION].find_one({"_id": str(store_id)})
                    if existing_doc and 'enhanced_campaigns' in existing_doc:
                        existing_campaigns = existing_doc['enhanced_campaigns']
                    
                    # Get campaign IDs
                    existing_campaign_ids = {c.get('id') for c in existing_campaigns if 'id' in c}
                    new_campaign_ids = {c.get('id') for c in meta_campaigns if 'id' in c}
                    
                    # Check if there are any new campaigns
                    has_new_meta_campaigns = len(new_campaign_ids - existing_campaign_ids) > 0
                    
                    if has_new_meta_campaigns:
                        logger.info(f"Detected new Meta campaigns for store {store_id}, will update product details")
                
                # 8. Even if we don't have Meta campaigns, we should update the product_details_cache
                # Get currency symbol from store with fallback
                currency_symbol = str(store.get('symbol', '$')) if isinstance(store, dict) else '$'
                
                # Check if this is store 961 or has large amounts of sales data
                is_large_store = int(store_id) == 961
                has_large_dataset = len(sales_data.get("product_sales", [])) > 1000 or len(sales_data.get("daily_totals", [])) > 300
                
                # Use batched version for store 961 or stores with large datasets
                if is_large_store or has_large_dataset:
                    logger.info(f"Using batched processing for store {store_id} due to large dataset ({len(sales_data.get('product_sales', []))} sales records, {len(sales_data.get('daily_totals', []))} daily records)")
                    success_product_details = update_product_details_with_sales_data_batched(
                        str(store_id), sales_data, mongo_db, currency_symbol, has_new_meta_campaigns
                    )
                else:
                    success_product_details = update_product_details_with_sales_data(
                        str(store_id), sales_data, mongo_db, currency_symbol, has_new_meta_campaigns
                    )
                    
                if success_product_details:
                    logger.info(f"Successfully updated product_details_cache for store {store_id}")
                else:
                    logger.warning(f"Failed to update product_details_cache for store {store_id}")
                
                if success or success_product_details:
                    processed_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"Error processing store {store_id}: {e}", exc_info=True)
                failed_count += 1

        logger.info("--- Store processing finished ---")

    except mysql.connector.Error as db_err:
        logger.error(f"Database connection error: {db_err}")
    except ConnectionError as conn_err:
        logger.error(f"Connection error: {conn_err}")
    except Exception as e:
        logger.error(f"Unexpected error during script execution: {e}", exc_info=True)
    finally:
        # Close connections
        logger.info("Closing database connections...")
        if mysql_conn and mysql_conn.is_connected():
            try:
                mysql_conn.close()
                logger.info("MySQL connection closed.")
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
        if mongo_client:
            try:
                mongo_client.close()
                logger.info("MongoDB connection closed.")
            except Exception as e:
                logger.error(f"Error closing MongoDB connection: {e}")

        logger.info("--- Script Summary ---")
        logger.info(f"Successfully processed/updated correlation data for: {processed_count} stores")
        logger.info(f"Failed to process/update correlation data for: {failed_count} stores")
        logger.info("Script finished.")

if __name__ == "__main__":
    main()