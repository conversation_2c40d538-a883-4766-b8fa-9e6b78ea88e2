import { logger } from './logger';

export interface ErrorDetails {
  type: 'csrf' | 'validation' | 'network' | 'server' | 'unknown';
  message: string;
  originalError?: unknown;
  statusCode?: number;
  retryable: boolean;
  userMessage: string;
}

export interface CSRFErrorInfo {
  isCSRFError: boolean;
  needsTokenRefresh: boolean;
  shouldRetry: boolean;
  errorMessage: string;
}

/**
 * Centralized error handling utilities for consistent error management
 */
export class ErrorUtils {
  /**
   * Analyze an error to determine if it's CSRF-related
   */
  static analyzeCSRFError(error: unknown): CSRFErrorInfo {
    const defaultResult: CSRFErrorInfo = {
      isCSRFError: false,
      needsTokenRefresh: false,
      shouldRetry: false,
      errorMessage: 'An error occurred'
    };

    if (!error) return defaultResult;

    const status = this.getErrorStatus(error);
    const data = this.getErrorData(error);
    const message = this.getErrorMessage(error);

    // Check for CSRF-specific patterns
    const isStatusMatch = status === 401 || status === 403;
    const hasCSRFMessage = this.containsCSRFKeywords(this.getDetailFromData(data)) || 
                          this.containsCSRFKeywords(this.getMessageFromData(data)) ||
                          this.containsCSRFKeywords(message);

    const isCSRFError = isStatusMatch && hasCSRFMessage;

    return {
      isCSRFError,
      needsTokenRefresh: isCSRFError,
      shouldRetry: isCSRFError && status !== 403, // Don't retry 403 (forbidden)
      errorMessage: this.extractErrorMessage(error)
    };
  }

  /**
   * Categorize and analyze any error for appropriate handling
   */
  static analyzeError(error: unknown): ErrorDetails {
    if (!error) {
      return {
        type: 'unknown',
        message: 'Unknown error occurred',
        retryable: false,
        userMessage: 'An unexpected error occurred. Please try again.'
      };
    }

    const status = this.getErrorStatus(error);
    const message = this.getErrorMessage(error);

    // CSRF errors
    const csrfInfo = this.analyzeCSRFError(error);
    if (csrfInfo.isCSRFError) {
      return {
        type: 'csrf',
        message: csrfInfo.errorMessage,
        originalError: error,
        statusCode: status,
        retryable: csrfInfo.shouldRetry,
        userMessage: 'Security token expired. Retrying with fresh token...'
      };
    }

    // Network errors
    if (!status || this.isNetworkError(error) || (typeof message === 'string' && message.includes('network'))) {
      return {
        type: 'network',
        message: 'Network connection error',
        originalError: error,
        retryable: true,
        userMessage: 'Network connection issue. Please check your internet connection and try again.'
      };
    }

    // Validation errors (400)
    if (status === 400) {
      return {
        type: 'validation',
        message: this.extractErrorMessage(error),
        originalError: error,
        statusCode: status,
        retryable: false,
        userMessage: this.extractErrorMessage(error)
      };
    }

    // Server errors (500+)
    if (status >= 500) {
      return {
        type: 'server',
        message: 'Server error occurred',
        originalError: error,
        statusCode: status,
        retryable: true,
        userMessage: 'Server error. Please try again in a moment.'
      };
    }

    // Other client errors (401, 403, 404, etc.)
    return {
      type: 'server',
      message: this.extractErrorMessage(error),
      originalError: error,
      statusCode: status,
      retryable: false,
      userMessage: this.extractErrorMessage(error)
    };
  }

  /**
   * Get user-friendly error message from error details
   */
  static getUserMessage(error: unknown, fallback: string = 'An error occurred'): string {
    const details = this.analyzeError(error);
    return details.userMessage || fallback;
  }

  /**
   * Check if an error should trigger a retry
   */
  static shouldRetry(error: unknown): boolean {
    const details = this.analyzeError(error);
    return details.retryable;
  }

  /**
   * Log error with appropriate level and context
   */
  static logError(error: unknown, context: string = 'Unknown'): void {
    const details = this.analyzeError(error);
    
    const logData = {
      context,
      type: details.type,
      message: details.message,
      statusCode: details.statusCode,
      retryable: details.retryable,
      originalError: details.originalError
    };

    switch (details.type) {
      case 'csrf':
        logger.warn(`CSRF Error in ${context}:`, logData);
        break;
      case 'network':
        logger.warn(`Network Error in ${context}:`, logData);
        break;
      case 'validation':
        logger.info(`Validation Error in ${context}:`, logData);
        break;
      case 'server':
        logger.error(`Server Error in ${context}:`, logData);
        break;
      default:
        logger.error(`Unknown Error in ${context}:`, logData);
    }
  }

  /**
   * Create standardized error response for UI components
   */
  static createErrorResponse(
    error: unknown,
    defaultMessage: string = 'Operation failed'
  ): { success: false; error: string; type: string; retryable: boolean } {
    const details = this.analyzeError(error);
    
    return {
      success: false,
      error: details.userMessage || defaultMessage,
      type: details.type,
      retryable: details.retryable
    };
  }

  /**
   * Extract error message from various error formats
   */
  private static extractErrorMessage(error: unknown): string {
    if (typeof error === 'string') {
      return error;
    }

    // Try to get error message from response data
    const data = this.getErrorData(error);
    if (data && typeof data === 'object') {
      const detail = this.getDetailFromData(data);
      if (detail) return detail;
      
      const message = this.getMessageFromData(data);
      if (message) return message;
      
      if ('error' in data) {
        const errorField = (data as { error: unknown }).error;
        if (typeof errorField === 'string') return errorField;
      }
      
      if ('errors' in data) {
        const errors = (data as { errors: unknown }).errors;
        if (Array.isArray(errors) && errors.length > 0) {
          return errors.join(', ');
        }
      }
    }

    // Try to get direct message from error
    const directMessage = this.getErrorMessage(error);
    if (directMessage) return directMessage;

    // Try to get statusText
    if (typeof error === 'object' && error !== null && 'statusText' in error) {
      const statusText = (error as { statusText: unknown }).statusText;
      if (typeof statusText === 'string') return statusText;
    }

    return 'An unexpected error occurred';
  }

  /**
   * Check if text contains CSRF-related keywords
   */
  private static containsCSRFKeywords(text: string | undefined): boolean {
    if (!text || typeof text !== 'string') return false;
    
    const lowerText = text.toLowerCase();
    const csrfKeywords = [
      'csrf',
      'token',
      'expired',
      'invalid token',
      'token required',
      'security token',
      'cross-site request forgery'
    ];

    return csrfKeywords.some(keyword => lowerText.includes(keyword));
  }

  private static getErrorStatus(error: unknown): number | undefined {
    if (typeof error === 'object' && error !== null && 'response' in error) {
      const errorWithResponse = error as { response: unknown };
      if (typeof errorWithResponse.response === 'object' && errorWithResponse.response !== null && 'status' in errorWithResponse.response) {
        const status = (errorWithResponse.response as { status: unknown }).status;
        return typeof status === 'number' ? status : undefined;
      }
    }
    if (typeof error === 'object' && error !== null && 'status' in error) {
      const status = (error as { status: unknown }).status;
      return typeof status === 'number' ? status : undefined;
    }
    return undefined;
  }

  private static getErrorData(error: unknown): unknown {
    if (typeof error === 'object' && error !== null && 'response' in error) {
      const errorWithResponse = error as { response: unknown };
      if (typeof errorWithResponse.response === 'object' && errorWithResponse.response !== null && 'data' in errorWithResponse.response) {
        return (errorWithResponse.response as { data: unknown }).data;
      }
    }
    if (typeof error === 'object' && error !== null && 'data' in error) {
      return (error as { data: unknown }).data;
    }
    return {};
  }

  private static getErrorMessage(error: unknown): string {
    if (typeof error === 'object' && error !== null && 'message' in error) {
      const message = (error as { message: unknown }).message;
      return typeof message === 'string' ? message : '';
    }
    return '';
  }

  private static getDetailFromData(data: unknown): string | undefined {
    if (typeof data === 'object' && data !== null && 'detail' in data) {
      const detail = (data as { detail: unknown }).detail;
      return typeof detail === 'string' ? detail : undefined;
    }
    return undefined;
  }

  private static getMessageFromData(data: unknown): string | undefined {
    if (typeof data === 'object' && data !== null && 'message' in data) {
      const message = (data as { message: unknown }).message;
      return typeof message === 'string' ? message : undefined;
    }
    return undefined;
  }

  private static isNetworkError(error: unknown): boolean {
    if (typeof error === 'object' && error !== null && 'code' in error) {
      const code = (error as { code: unknown }).code;
      return code === 'NETWORK_ERROR';
    }
    return false;
  }
}

/**
 * Specific error classes for different error types
 */
export class CSRFError extends Error {
  constructor(message: string, public readonly needsRefresh: boolean = true) {
    super(message);
    this.name = 'CSRFError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public readonly field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network connection error') {
    super(message);
    this.name = 'NetworkError';
  }
}

// Utility functions for common error handling patterns
export const handleFormError = (error: unknown, context: string = 'Form submission') => {
  ErrorUtils.logError(error, context);
  return ErrorUtils.getUserMessage(error, 'Form submission failed. Please try again.');
};

export const handleCSRFError = (error: unknown) => {
  const csrfInfo = ErrorUtils.analyzeCSRFError(error);
  if (csrfInfo.isCSRFError) {
    return {
      isCSRF: true,
      shouldRetry: csrfInfo.shouldRetry,
      message: 'Security token expired. Please try again.'
    };
  }
  return { isCSRF: false, shouldRetry: false, message: ErrorUtils.getUserMessage(error) };
};

export const isRetryableError = (error: unknown): boolean => ErrorUtils.shouldRetry(error);

export const getErrorType = (error: unknown): string => ErrorUtils.analyzeError(error).type; 