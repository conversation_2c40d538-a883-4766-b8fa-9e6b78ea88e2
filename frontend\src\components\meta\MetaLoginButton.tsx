import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, CircularProgress, Alert, Box, FormControl, InputLabel, Select, MenuItem, Typography } from '@mui/material';
import InstagramIcon from '@mui/icons-material/Instagram';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { MetaAuthService } from '../../services/auth';
import { useAuth } from '../../contexts/AuthContext';
import { MetaErrorType, META_PERMISSIONS, MetaLoginResponse } from '../../services/types';
import { onSdkError, onSdkReady } from '../../services/init';
import { useThemeContext } from '../../contexts/ThemeContext';
import { logger } from '../../utils/logger';

// Extended MetaLoginResponse to include pages property
interface MetaLoginResponseExtended extends MetaLoginResponse {
  pages?: Array<{
    id: string;
    name: string;
    access_token?: string;
    [key: string]: string | undefined;
  }>;
}

interface MetaLoginButtonProps {
  onError?: (error: string) => void;
  onSuccess?: (platform: 'facebook' | 'instagram') => void;
  className?: string;
  style?: React.CSSProperties;
  loginMode?: boolean; // If true, will use a simpler UI focused on login only
}

export const MetaLoginButton: React.FC<MetaLoginButtonProps> = ({
  onError,
  onSuccess,
  className,
  style,
  loginMode = false
}) => {
  // Using useAuth for potential context needs but not extracting metaLogin
  useAuth();
  const { t } = useTranslation();
  const { mode } = useThemeContext();
  const [loading, setLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [statusType, setStatusType] = useState<'info' | 'warning' | 'error' | 'success'>('info');
  const [platform, setPlatform] = useState<'facebook' | 'instagram'>('facebook');
  const [sdkReady, setSdkReady] = useState<boolean>(false);

  // Check HTTPS and SDK status on mount
  useEffect(() => {
    // Check if we're using HTTPS
    if (window.location.protocol !== 'https:' && !window.location.hostname.includes('localhost')) {
      setStatusMessage(t('metaLogin.error.httpsRequired', 'Meta Login requires HTTPS. Please use HTTPS to connect with Meta.'));
      setStatusType('error');
    }

    // Listen for SDK ready/error events
    const cleanupReady = onSdkReady(() => {
      logger.debug('MetaLoginButton: SDK Ready event received');
      setSdkReady(true);
      setStatusMessage(null);
    });

    const cleanupError = onSdkError((event) => {
      logger.error('MetaLoginButton: SDK Error event received', event.detail);
      setSdkReady(false);
      setStatusMessage(t('metaLogin.error.sdkFailed', 'Facebook SDK failed to load. Please try refreshing the page.'));
      setStatusType('error');
    });

    // Check if SDK is already available
    if (typeof window.FB !== 'undefined') {
      setSdkReady(true);
    }

    // Cleanup event listeners
    return () => {
      cleanupReady();
      cleanupError();
    };
  }, [t]);

  const handleMetaLogin = async () => {
    setLoading(true);
    setStatusMessage('');
    
    try {
      logger.debug(`Attempting to login with ${platform}...`);
      
      // Check if we're using HTTPS
      if (window.location.protocol !== 'https:' && process.env.NODE_ENV === 'production') {
        throw {
          type: MetaErrorType.SECURITY_ERROR,
          message: t('metaLogin.error.httpsRequired', 'Meta login requires HTTPS. Please use a secure connection.')
        };
      }
      
      // Attempt login
      const authResponse = await MetaAuthService.login(platform);
      logger.debug('MetaLoginButton: Auth response:', authResponse);
      
      // If in login mode, we don't check for specific permissions
      if (!loginMode) {
        // Check for declined_permissions
        if (authResponse && authResponse.authResponse && authResponse.authResponse.grantedScopes) {
          const grantedScopes = authResponse.authResponse.grantedScopes.split(',');
          const requiredPermissions = META_PERMISSIONS[platform];
          
          // Find missing permissions
          const missingPermissions = requiredPermissions.filter(perm => !grantedScopes.includes(perm));
          
          if (missingPermissions.length > 0) {
            logger.warn('Missing permissions:', missingPermissions);
            
            // If critical permissions are missing, show a warning
            const criticalPermissions = ['pages_read_engagement', 'pages_show_list', 'instagram_basic', 'instagram_manage_insights'];
            const missingCritical = missingPermissions.filter(perm => criticalPermissions.includes(perm));
            
            if (missingCritical.length > 0) {
              throw {
                type: MetaErrorType.PERMISSION_DENIED,
                message: t('metaLogin.error.missingPermissions', 'Missing critical permissions: {{permissions}}. Dashboard functionality will be limited.', {
                  permissions: missingCritical.join(', ')
                })
              };
            } else {
              // Non-critical permissions, just warn
              setStatusType('warning');
              setStatusMessage(t('metaLogin.warning.optionalPermissions', 'Some optional permissions were not granted: {{permissions}}. Some features may be limited.', {
                permissions: missingPermissions.join(', ')
              }));
            }
          }
        }
      }
      
      // Process the login
      const response = await MetaAuthService.handleMetaAuth(authResponse, platform) as MetaLoginResponseExtended;
      logger.debug('MetaLoginButton: Meta auth completed:', response);
      
      if (loginMode) {
        // In login mode, we just need the auth to succeed
        setStatusType('success');
        setStatusMessage(t('metaLogin.success.connectedPlatform', 'Connected to {{platform}} successfully!', { platform }));
        onSuccess?.(platform);
      } else {
        // Normal mode - check if we have pages
        if (response && response.pages && response.pages.length > 0) {
          setStatusType('success');
          setStatusMessage(t('metaLogin.success.connectedPlatform', 'Connected to {{platform}} successfully!', { platform }));
          onSuccess?.(platform);
        } else {
          setStatusType('warning');
          setStatusMessage(t('metaLogin.warning.noPagesFound', 'Connected to {{platform}}, but no {{accountType}} were found.', {
            platform,
            accountType: platform === 'facebook' ? t('common.pages', 'pages') : t('common.accounts', 'accounts')
          }));
        }
      }
    } catch (error: unknown) {
      logger.error('MetaLoginButton: Login error:', error);
      
      let errorMessage = t('metaLogin.error.unexpected', 'An unexpected error occurred. Please try again.');
      
      if (error && typeof error === 'object') {
        switch ((error as { type?: MetaErrorType }).type) {
          case MetaErrorType.AUTH_FAILED:
            errorMessage = t('metaLogin.error.authFailed', 'Authentication failed. Please make sure you are logged in to Meta and try again.');
            break;
          case MetaErrorType.PERMISSION_DENIED:
            errorMessage = (error as { message?: string }).message || t('metaLogin.error.permissionDenied', 'Permission denied. Please grant the required permissions to use this feature.');
            break;
          case MetaErrorType.SECURITY_ERROR:
            errorMessage = t('metaLogin.error.securityError', 'Meta login requires HTTPS. Please use a secure connection.');
            break;
          case MetaErrorType.NETWORK_ERROR:
            errorMessage = t('metaLogin.error.networkError', 'Network error occurred. Please check your connection and try again.');
            break;
          case MetaErrorType.SDK_NOT_LOADED:
            errorMessage = t('metaLogin.error.sdkNotLoaded', 'Meta SDK failed to load. Please ensure cookies are enabled and try again.');
            break;
        }
      }
      
      logger.error('MetaLoginButton: Error message:', errorMessage);
      setStatusMessage(errorMessage);
      setStatusType('error');
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Helper to get icon based on status type
  const getStatusIcon = () => {
    switch (statusType) {
      case 'error':
        return <ErrorOutlineIcon sx={{ mr: 1 }} />;
      case 'warning':
        return <HelpOutlineIcon sx={{ mr: 1 }} />;
      default:
        return null;
    }
  };

  // In login mode, render a simpler interface
  if (loginMode) {
    return (
      <>
        {statusMessage && (
          <Alert 
            severity={statusType}
            sx={{ mb: 2 }}
            icon={statusType !== 'info' ? getStatusIcon() : undefined}
          >
            {statusMessage}
          </Alert>
        )}
        
        <Button
          fullWidth
          variant="contained"
          onClick={() => {
            setPlatform('facebook');  // Always use Facebook for the main login
            handleMetaLogin();
          }}
          disabled={loading || !sdkReady}
          className={className}
          style={style}
          sx={{
            color: 'white !important',
            backgroundColor: mode === 'dark' ? '#42a5f5' : '#0668E1',
            borderColor: mode === 'dark' ? '#42a5f5' : '#0668E1',
            '&:hover': {
              backgroundColor: mode === 'dark' ? '#1e88e5' : '#0557BC',
              borderColor: mode === 'dark' ? '#1e88e5' : '#0557BC',
            },
            '&:disabled': {
              opacity: 0.7,
              color: 'white',
              backgroundColor: mode === 'dark' ? '#42a5f5' : '#0668E1',
            },
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 500,
            position: 'relative'
          }}
        >
          <Box sx={{ 
            position: 'absolute', 
            left: 16, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            backgroundColor: 'white',
            borderRadius: '50%',
            width: 24,
            height: 24,
            padding: '3px',
            overflow: 'hidden'
          }}>
            <Box
              component="img"
              src="/meta.png"
              alt={t('common.metaLogo', 'Meta Logo')}
              sx={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                display: 'block'
              }}
            />
          </Box>
          <Typography sx={{ fontWeight: 500, width: '100%', textAlign: 'center' }}>
            {loading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                {t('metaLogin.connecting', 'Connecting...')}
              </>
            ) : (
              t('metaLogin.continueWithMeta', 'Continue with Meta')
            )}
          </Typography>
        </Button>
        
        <Box sx={{ mt: 2, textAlign: 'left' }}>
          <Typography variant="caption" color="text.secondary">
            {t('metaLogin.info.allowAccess', 'This will allow D-Unit to access your Facebook Page and Instagram Business accounts to retrieve insights and analytics data. We\'ll ask for specific permissions in the next step.')}
          </Typography>
        </Box>
      </>
    );
  }

  // Standard full UI for Meta platform connection
  return (
    <>
      {statusMessage && (
        <Alert 
          severity={statusType}
          sx={{ mb: 2 }}
          icon={statusType !== 'info' ? getStatusIcon() : undefined}
        >
          {statusMessage}
        </Alert>
      )}
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {t('metaLogin.choosePlatform', 'Choose a platform to connect with:')}
        </Typography>
        <FormControl fullWidth>
          <InputLabel id="platform-select-label">{t('common.platform', 'Platform')}</InputLabel>
          <Select
            labelId="platform-select-label"
            value={platform}
            label={t('common.platform', 'Platform')}
            onChange={(e) => setPlatform(e.target.value as 'facebook' | 'instagram')}
            disabled={loading}
          >
            <MenuItem value="facebook">{t('common.facebook', 'Facebook')}</MenuItem>
            <MenuItem value="instagram">{t('common.instagram', 'Instagram')}</MenuItem>
          </Select>
        </FormControl>
      </Box>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>{t('metaLogin.requiredPermissions', 'Required permissions:')}</strong> {t('metaLogin.permissionsInfo', 'When prompted, please grant all requested permissions for {{platformDetails}}.', {
            platformDetails: platform === 'facebook' 
              ? t('common.pageInsights', 'page insights, posts, and audience data') 
              : t('common.instagramInsights', 'Instagram account access and insights')
          })}
          {' '}
          {t('metaLogin.permissionsWarning', 'Without these permissions, the dashboard may not function properly.')}
        </Typography>
      </Alert>
      
      <Button
        fullWidth
        variant="contained"
        onClick={handleMetaLogin}
        disabled={loading || !sdkReady || (statusType === 'error' && statusMessage?.includes('HTTPS'))}
        className={className}
        style={style}
        sx={{
          mt: 2,
          color: 'white',
          backgroundColor: platform === 'facebook' ? '#0668E1' : '#C13584',
          borderColor: platform === 'facebook' ? '#0668E1' : '#C13584',
          '&:hover': {
            borderColor: platform === 'facebook' ? '#0668E1' : '#C13584',
            backgroundColor: platform === 'facebook' ? '#0557BC' : '#A1286A'
          },
          '&:disabled': {
            opacity: 0.7,
            color: 'white'
          },
          position: 'relative',
          py: 1.2
        }}
      >
        <Box sx={{ 
          position: 'absolute', 
          left: 16, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: '50%',
          width: 24,
          height: 24,
          border: `1px solid ${platform === 'facebook' ? '#0668E1' : '#C13584'}`,
          padding: '3px',
          overflow: 'hidden'
        }}>
          {platform === 'facebook' ? (
            <Box
              component="img"
              src="/meta.png"
              alt={t('common.metaLogo', 'Meta Logo')}
              sx={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                display: 'block'
              }}
            />
          ) : (
            <InstagramIcon sx={{ width: '18px', height: '18px', color: '#C13584' }} />
          )}
        </Box>
        <Typography sx={{ fontWeight: 500, width: '100%', textAlign: 'center', color: 'white' }}>
          {loading ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
              {t('metaLogin.connecting', 'Connecting...')}
            </>
          ) : (
            platform === 'facebook' 
              ? t('metaLogin.continueWithMeta', 'Continue with Meta')
              : t('metaLogin.continueWithInstagram', 'Continue with Instagram')
          )}
        </Typography>
      </Button>
    </>
  );
}; 
