#!/usr/bin/env python3
"""
Test script to verify database connection and competitor analysis query
"""

import os
from dotenv import load_dotenv
from pymongo import MongoClient
from datetime import datetime, timezone

# Load environment variables
load_dotenv()

# Get MongoDB connection
mongodb_uri = os.getenv('MONGODB_CONNECTION')
if not mongodb_uri:
    print("ERROR: MONGODB_CONNECTION not found in .env file")
    exit(1)

print(f"Connecting to MongoDB...")

try:
    # Connect to MongoDB
    client = MongoClient(mongodb_uri)
    db = client['D-Unit-AnalysisGPT']
    collection = db['global_analysis']
    
    print(f"✓ Connected to database")
    
    # Count total documents
    total = collection.count_documents({})
    print(f"\nTotal documents: {total}")
    
    # Test different queries to find stores without competitor analysis
    
    # Query 1: Check for the exact text
    query1 = {"analysis.competitor_analysis": "No specific competitor analysis available."}
    count1 = collection.count_documents(query1)
    print(f"\nStores with 'No specific competitor analysis available.': {count1}")
    
    # Query 2: Check for missing field
    query2 = {"analysis.competitor_analysis": {"$exists": False}}
    count2 = collection.count_documents(query2)
    print(f"Stores missing competitor_analysis field: {count2}")
    
    # Query 3: Check for empty string
    query3 = {"analysis.competitor_analysis": ""}
    count3 = collection.count_documents(query3)
    print(f"Stores with empty competitor_analysis: {count3}")
    
    # Query 4: Check for None/null
    query4 = {"analysis.competitor_analysis": None}
    count4 = collection.count_documents(query4)
    print(f"Stores with null competitor_analysis: {count4}")
    
    # Combined query (what the script uses)
    combined_query = {
        "$or": [
            {"analysis.competitor_analysis": {"$exists": False}},
            {"analysis.competitor_analysis": ""},
            {"analysis.competitor_analysis": None},
            {"analysis.competitor_analysis": "No specific competitor analysis available."}
        ]
    }
    combined_count = collection.count_documents(combined_query)
    print(f"\nTotal stores needing competitor analysis (combined query): {combined_count}")
    
    # Get sample documents
    print("\n=== SAMPLE DOCUMENTS ===")
    samples = collection.find(combined_query).limit(5)
    for i, doc in enumerate(samples, 1):
        print(f"\nDocument {i}:")
        print(f"  ID: {doc['_id']}")
        if 'analysis' in doc and 'competitor_analysis' in doc['analysis']:
            comp_analysis = doc['analysis']['competitor_analysis']
            print(f"  Competitor Analysis: {repr(comp_analysis[:50])}..." if comp_analysis else repr(comp_analysis))
        else:
            print(f"  Competitor Analysis: MISSING")
            
    # Test specific store 566
    print("\n=== CHECKING STORE 566 ===")
    store_566 = collection.find_one({"_id": "566"})
    if store_566:
        print("Found store 566")
        if 'analysis' in store_566:
            comp = store_566['analysis'].get('competitor_analysis', 'MISSING')
            print(f"Competitor analysis value: {repr(comp)}")
        else:
            print("No 'analysis' field found")
    else:
        print("Store 566 not found")
        
except Exception as e:
    print(f"ERROR: {str(e)}")
    print(f"Error type: {type(e).__name__}")
    import traceback
    traceback.print_exc()