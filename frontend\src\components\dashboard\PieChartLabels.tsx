import { useState } from "react";
import { pie, arc, PieArcDatum } from "d3";
import { Box, Typography, Tooltip, Paper } from '@mui/material';

export type PieChartDataItem = {
  name: string;
  value: number;
  percentage?: number;
  colorFrom: string;
  colorTo: string;
};

interface PieChartLabelsProps {
  data: PieChartDataItem[];
  title?: string;
  height?: number;
}

export function PieChartLabels({ data, title }: PieChartLabelsProps) {
  const [hoveredSlice, setHoveredSlice] = useState<number | null>(null);
  
  // Chart dimensions
  const radius = Math.PI * 100;
  const gap = 0.025; 

  // Pie layout and arc generator
  const pieLayout = pie<PieChartDataItem>()
    .sort(null)
    .value((d) => d.value)
    .padAngle(gap); // Creates a gap between slices

  const arcGenerator = arc<PieArcDatum<PieChartDataItem>>()
    .innerRadius(20)
    .outerRadius((_d, i) => (hoveredSlice === i ? radius * 1.05 : radius))
    .cornerRadius(8);

  const labelRadius = radius * 0.8;
  const arcLabel = arc<PieArcDatum<PieChartDataItem>>().innerRadius(labelRadius).outerRadius(labelRadius);

  const arcs = pieLayout(data);
  // Calculate the angle for each slice
  const computeAngle = (d: PieArcDatum<PieChartDataItem>) => {
    return ((d.endAngle - d.startAngle) * 180) / Math.PI;
  };

  // Minimum angle to display text
  const MIN_ANGLE = 20;

  // Colors for slices
  const colorMapping: Record<string, string> = {
    "text-pink-400": "#ec4899",
    "text-purple-400": "#c084fc", 
    "text-indigo-400": "#818cf8",
    "text-sky-400": "#38bdf8",
    "text-lime-400": "#a3e635",
    "text-amber-400": "#fbbf24"
  };

  // Custom tooltip content with very soft border
  const CustomTooltip = ({ item }: { item: PieChartDataItem }) => (
    <Paper 
      elevation={0}
      sx={{ 
        p: 1.5, 
        maxWidth: 200, 
        backgroundColor: 'rgba(255, 255, 255, 0.98)',
        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
        border: '1px solid rgba(230,230,230,0.7)',
        borderRadius: 1
      }}
    >
      <Typography 
        variant="subtitle2" 
        sx={{ 
          fontWeight: '500', 
          mb: 0.5,
          fontSize: '0.95rem',
          color: 'rgba(0,0,0,0.85)',
          letterSpacing: '-0.01em'
        }}
      >
        {item.name}
      </Typography>
      <Typography 
        variant="body2" 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          color: 'rgba(0,0,0,0.75)',
          fontSize: '0.85rem',
          letterSpacing: '-0.01em'
        }}
      >
        <span>Percentage:</span> <span>{item.percentage}%</span>
      </Typography>
    </Paper>
  );

  return (
    <Box sx={{ 
      width: '100%', 
      border: 'none', 
      backgroundColor: 'transparent', 
      boxShadow: 'none'
    }}>
      {title && (
        <Typography 
          variant="h6" 
          sx={{ 
            mb: 2, 
            textAlign: 'center',
            fontWeight: '500',
            color: 'rgba(0,0,0,0.85)',
            letterSpacing: '-0.02em'
          }}
        >
          {title}
        </Typography>
      )}
      <Box sx={{ 
        position: 'relative', 
        maxWidth: '20rem', 
        mx: 'auto', 
        my: 4, 
        border: 'none', 
        backgroundColor: 'transparent', 
        boxShadow: 'none' // Ensure no shadow
      }}>
        <svg
          viewBox={`-${radius} -${radius} ${radius * 2} ${radius * 2}`}
          style={{ 
            overflow: 'visible', 
            border: 'none', 
            backgroundColor: 'transparent' // Ensure no background
          }}
        >
          {/* Slices */}
          {arcs.map((d, i) => {
            const midAngle = (d.startAngle + d.endAngle) / 2;
            const colorFromKey = d.data.colorFrom;
            const colorToKey = d.data.colorTo;
            const colorFrom = colorMapping[colorFromKey] || "#ec4899";
            const colorTo = colorMapping[colorToKey] || "#ec4899";
            const isHovered = hoveredSlice === i;

            return (
              <Tooltip
                key={i}
                title={<CustomTooltip item={d.data} />}
                placement={(() => {
                  const midAngle = (d.startAngle + d.endAngle) / 2;
                  const degrees = (midAngle * 180) / Math.PI;
                  if (degrees >= 315 || degrees < 45) return "right";
                  if (degrees >= 45 && degrees < 135) return "bottom";
                  if (degrees >= 135 && degrees < 225) return "left";
                  return "top";
                })()}
                arrow
                followCursor
                enterTouchDelay={0}
                leaveTouchDelay={3000}
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: 'transparent',
                      padding: 0,
                      '& .MuiTooltip-arrow': {
                        color: 'rgba(255, 255, 255, 0.98)',
                        '&::before': {
                          border: '1px solid rgba(230,230,230,0.7)',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.02)'
                        }
                      }
                    }
                  }
                }}
              >
                <g 
                  onMouseEnter={() => setHoveredSlice(i)}
                  onMouseLeave={() => setHoveredSlice(null)}
                >
                  <path 
                    fill={`url(#pieColors-${i})`} 
                    d={arcGenerator(d, i) as string} 
                    style={{ 
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      filter: isHovered ? 'drop-shadow(0px 0px 4px rgba(0,0,0,0.2))' : 'none',
                      transform: isHovered ? 'scale(1.02)' : 'scale(1)',
                    }}
                  />
                  <defs>
                    <linearGradient
                      id={`pieColors-${i}`}
                      x1="0"
                      y1="0"
                      x2="1"
                      y2="0"
                      gradientTransform={`rotate(${(midAngle * 180) / Math.PI - 90}, 0.5, 0.5)`}
                    >
                      <stop offset="0%" stopColor={colorFrom} />
                      <stop offset="100%" stopColor={colorTo} />
                    </linearGradient>
                  </defs>
                </g>
              </Tooltip>
            );
          })}
        </svg>

        {/* Labels as absolutely positioned divs */}
        <Box sx={{ position: 'absolute', inset: 0, pointerEvents: 'none' }}>
          {arcs.map((d: PieArcDatum<PieChartDataItem>, i) => {
            const angle = computeAngle(d);
            if (angle <= MIN_ANGLE) return null;

            // Get pie center position
            const [x, y] = arcLabel.centroid(d);
            const CENTER_PCT = 50;

            // Convert to percentage positions. Adjust magic numbers to move the labels around
            const nameLeft = `${CENTER_PCT + (x / radius) * 40}%`;
            const nameTop = `${CENTER_PCT + (y / radius) * 40}%`;

            const valueLeft = `${CENTER_PCT + (x / radius) * 72}%`;
            const valueTop = `${CENTER_PCT + (y / radius) * 70}%`;

            return (
              <Box key={i}>
                <Box
                  sx={{
                    position: 'absolute',
                    transform: 'translate(-50%, -50%)',
                    fontWeight: 'medium',
                    left: valueLeft,
                    top: valueTop,
                    transition: 'all 0.3s ease',
                    zIndex: hoveredSlice === i ? 2 : 1,
                    fontSize: '0.875rem',
                    letterSpacing: '-0.01em',
                  }}
                >
                  {d.data.value}
                </Box>
                <Box
                  sx={{
                    position: 'absolute',
                    maxWidth: '80px',
                    color: 'white',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    textAlign: 'center',
                    fontSize: '0.875rem',
                    fontWeight: 'medium',
                    transform: 'translate(-50%, -50%)',
                    left: nameLeft,
                    top: nameTop,
                    ml: x > 0 ? '2px' : '-2px',
                    mt: y > 0 ? '2px' : '-2px',
                    transition: 'all 0.3s ease',
                    zIndex: hoveredSlice === i ? 2 : 1,
                    letterSpacing: '-0.01em',
                  }}
                >
                  {d.data.name}
                </Box>
              </Box>
            );
          })}
        </Box>
      </Box>
    </Box>
  );
}

export default PieChartLabels;