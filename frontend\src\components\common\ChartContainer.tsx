import React, { useRef, useEffect, useState } from 'react';
import SilentResponsiveContainer from './SilentResponsiveContainer';
import { Box, CircularProgress } from '@mui/material';

interface ChartContainerProps {
  width?: string | number;
  height?: string | number;
  minHeight?: number;
  minWidth?: number;
  aspect?: number;
  debounce?: number;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onResize?: (width: number, height: number) => void;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
}

/**
 * Enhanced ResponsiveContainer wrapper that prevents logUtils warnings
 * and ensures proper chart dimensions during initial render
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({
  width = '100%',
  height = '100%',
  minHeight = 300,
  minWidth = 200,
  aspect,
  debounce = 0,
  children,
  className,
  style,
  onResize,
  loading = false,
  loadingComponent
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Check if container has valid dimensions
  const hasDimensions = dimensions && dimensions.width > 0 && dimensions.height > 0;

  useEffect(() => {
    // Store original console methods for this effect
    const originalWarn = console.warn;
    const originalError = console.error;
    
    // Create ultra-aggressive Recharts warning suppression
    const suppressWarn = (...args: unknown[]) => {
      const message = args.join(' ').toLowerCase();
      if (
        message.includes('width') && message.includes('height') ||
        message.includes('logutils') ||
        message.includes('chart') ||
        message.includes('responsivecontainer') ||
        message.includes('greater than 0') ||
        message.includes('minwidth') || 
        message.includes('minheight') ||
        message.includes('aspect')
      ) {
        return; // Completely suppress
      }
      originalWarn.apply(console, args);
    };

    const suppressError = (...args: unknown[]) => {
      const message = args.join(' ').toLowerCase();
      if (
        message.includes('width') && message.includes('height') ||
        message.includes('logutils') ||
        message.includes('chart')
      ) {
        return; // Completely suppress
      }
      originalError.apply(console, args);
    };

    // Override console methods during this component's lifecycle
    console.warn = suppressWarn;
    console.error = suppressError;

    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const newWidth = Math.max(rect.width, minWidth);
        const newHeight = Math.max(rect.height, minHeight);
        
        setDimensions({ width: newWidth, height: newHeight });
        setIsReady(true);
        
        if (onResize) {
          onResize(newWidth, newHeight);
        }
      }
    };

    // Initial measurement
    updateDimensions();

    // Set up ResizeObserver for dynamic updates
    const resizeObserver = new ResizeObserver(() => {
      updateDimensions();
    });

    const currentContainer = containerRef.current;
    if (currentContainer) {
      resizeObserver.observe(currentContainer);
    }

    // Fallback for older browsers
    const handleResize = () => updateDimensions();
    window.addEventListener('resize', handleResize);

    return () => {
      if (currentContainer) {
        resizeObserver.unobserve(currentContainer);
      }
      resizeObserver.disconnect();
      window.removeEventListener('resize', handleResize);
      
      // Restore original console methods
      console.warn = originalWarn;
      console.error = originalError;
    };
  }, [minWidth, minHeight, onResize]);

  // Default loading component
  const defaultLoadingComponent = (
    <Box 
      sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: minHeight,
        width: '100%'
      }}
    >
      <CircularProgress size={40} />
    </Box>
  );

  // Container styles
  const containerStyle: React.CSSProperties = {
    width: typeof width === 'string' ? width : `${width}px`,
    height: typeof height === 'string' ? height : `${height}px`,
    minHeight: minHeight,
    minWidth: minWidth,
    position: 'relative',
    ...style
  };

  return (
    <div
      ref={containerRef}
      className={className}
      style={containerStyle}
    >
      {(loading || !isReady || !hasDimensions) ? (
        loadingComponent || defaultLoadingComponent
      ) : (
        <SilentResponsiveContainer
          width={width}
          height={height}
          minHeight={minHeight}
          minWidth={minWidth}
          aspect={aspect}
          debounce={debounce}
        >
          {children}
        </SilentResponsiveContainer>
      )}
    </div>
  );
};

export default ChartContainer; 