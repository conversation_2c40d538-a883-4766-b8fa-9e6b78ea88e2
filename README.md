# D-Unit: AI-Driven E-Commerce Analytics Platform

<div align="center">

**Enterprise-grade analytics platform empowering e-commerce stores with AI-powered insights, competitive analysis, and seamless Meta integration.**

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![License](https://img.shields.io/badge/license-proprietary-blue)
![Python](https://img.shields.io/badge/python-3.11.9-blue)
![Node.js](https://img.shields.io/badge/node.js-18+-green)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-blue)
![React](https://img.shields.io/badge/React-18.3+-61DAFB)

</div>

## 🚀 Overview

D-Unit is a comprehensive analytics platform designed for La Nube e-commerce stores, providing AI-powered business intelligence, real-time performance metrics, and actionable insights. The platform combines advanced data processing with modern web technologies to deliver enterprise-grade analytics capabilities.

## ✨ Key Features

### 🧠 AI-Powered Intelligence
- **Advanced Analytics**: Machine learning-driven insights and recommendations
- **Natural Language Chat**: Multi-modal AI interface with web search integration
- **Predictive Analytics**: Market trend analysis and performance forecasting
- **RAG-Enhanced Analysis**: Context-aware responses using vector embeddings

### 📊 Business Analytics
- **Real-time Dashboards**: Interactive visualizations with time-range filtering
- **Product Performance**: Sales tracking, pricing analysis, and inventory insights
- **Customer Analytics**: Demographics, behavior patterns, and segmentation
- **Competitive Analysis**: Market positioning and competitor insights

### 🌐 Meta Integration
- **Social Media Analytics**: Facebook & Instagram business account integration
- **Ad Performance**: Campaign tracking, ROI analysis, and audience insights
- **Granular Permissions**: Fine-tuned access control for social media data
- **Real-time Sync**: Automated data synchronization every 30 minutes

### 🔒 Enterprise Security
- **Multi-layer Authentication**: JWT with 2FA and role-based access control
- **CSRF Protection**: Advanced token rotation and attack monitoring
- **Security Gateway**: 4-layer security architecture with threat detection
- **Data Encryption**: End-to-end security for sensitive business data

## 🛠 Technology Stack

### Frontend
- **Framework**: React 18.3+ with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Components**: Material-UI + Tailwind CSS for responsive design
- **State Management**: Context API with custom hooks
- **Internationalization**: i18next for multi-language support

### Backend
- **API Framework**: FastAPI with async/await support
- **Runtime**: Python 3.11.9 with Uvicorn ASGI server
- **Databases**: MongoDB Atlas (analytics) + MySQL (operational data)
- **AI Integration**: OpenAI GPT-4 models with vector embeddings
- **External APIs**: Meta Graph API, Brave Search, Google Custom Search

### Infrastructure
- **Backend Hosting**: AWS Elastic Beanstalk with auto-scaling
- **Frontend CDN**: S3 + CloudFront for global distribution
- **Database**: MongoDB Atlas with optimized indexes
- **Security**: 4-layer middleware with CORS and CSRF protection

## 🏗 System Architecture

D-Unit employs a sophisticated multi-layered architecture designed for scalability, performance, and data integrity:

### Data Processing Pipeline
```
MySQL (La Nube) → ETL Scripts → MongoDB Atlas → Vector Embeddings → AI Chat
```

**11-Script Data Pipeline:**
1. **Data Acquisition**: Extract from MySQL operational database
2. **Data Transformation**: Process into analytics-ready format
3. **Cache Generation**: Create optimized data structures
4. **AI Enhancement**: Generate vector embeddings for semantic search
5. **Real-time Updates**: Continuous synchronization and analysis

### Database Architecture
- **MySQL (lanube)**: Operational data source with transactional records
- **MongoDB D-Unit**: User management and application state
- **MongoDB D-Unit-AnalysisGPT**: Analytics cache, AI embeddings, and insights

### Security Layers
1. **SecurityHeadersMiddleware**: HSTS, CSP, X-Frame-Options
2. **CSRFMiddleware**: Token validation with rotation
3. **SecurityGatewayMiddleware**: Threat detection and rate limiting
4. **RequestValidationMiddleware**: Input sanitization and protection

## 🚀 Quick Start

### Prerequisites
- Python 3.11.9+
- Node.js 18+
- MongoDB Atlas account
- OpenAI API access
- Meta Developer Account
- AWS CLI (for deployment)

### Local Development

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Configure environment variables
python main.py
```

#### Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env
# Configure environment variables
npm run dev
```

### Environment Variables

#### Backend (.env)
```bash
MONGODB_CONNECTION=mongodb+srv://your-cluster
JWT_SECRET_KEY=your-secret-key
OPENAI_API_KEY=sk-your-openai-key
FACEBOOK_APP_ID=your-app-id
FACEBOOK_APP_SECRET=your-app-secret
BRAVE_SEARCH_API_KEY=your-brave-key
CORS_ORIGINS=["https://localhost:5173"]
```

#### Frontend (.env)
```bash
VITE_API_URL=https://localhost:8000
VITE_APP_ENV=development
VITE_FACEBOOK_APP_ID=your-app-id
```

## 📁 Project Structure

```
D-Unit/
├── 📂 backend/                 # FastAPI backend application
│   ├── 📂 config/             # Configuration management
│   ├── 📂 middleware/         # Security and request middleware  
│   ├── 📂 models/             # Data models and schemas
│   ├── 📂 routes/             # API endpoints
│   ├── 📂 services/           # Business logic layer
│   ├── 📂 utils/              # Utility functions
│   ├── 📂 tests/              # Test suites
│   └── 📄 main.py             # Application entry point
├── 📂 frontend/               # React frontend application
│   ├── 📂 src/
│   │   ├── 📂 components/     # React components
│   │   ├── 📂 services/       # API clients and utilities
│   │   ├── 📂 contexts/       # React context providers
│   │   ├── 📂 hooks/          # Custom React hooks
│   │   └── 📂 utils/          # Frontend utilities
│   ├── 📂 public/             # Static assets
│   └── 📄 package.json        # Dependencies and scripts
├── 📂 readme/                 # Documentation
└── 📄 CLAUDE.md               # Claude Code guidance
```

## 🔌 API Documentation

### Core Endpoints

#### Authentication
```http
POST /api/auth/token              # User login
POST /api/auth/register           # User registration  
POST /api/auth/2fa/verify-login   # Two-factor verification
GET  /api/auth/user/me            # Current user profile
```

#### Store Analytics
```http
GET /api/store/{store_id}/analysis
# Query Parameters:
# - timeRange: 'week'|'month'|'quarter'|'year'|'custom'
# - startDate: YYYY-MM-DD (for custom range)
# - endDate: YYYY-MM-DD (for custom range)

GET /api/store/{store_id}/product-list
# Query Parameters:
# - page: number (pagination)
# - pageSize: number (items per page)
```

#### Meta Integration
```http
POST /api/meta/connect            # Connect Meta business account
GET  /api/meta/{store_id}/pages   # List connected pages
GET  /api/meta/pages/{page_id}/ad-metrics    # Ad performance
GET  /api/meta/{store_id}/pages/{page_id}/audience  # Demographics
```

#### AI Chat System
```http
POST /api/chat/{store_id}         # Send message (multipart/form-data)
GET  /api/chat/history/{store_id} # Conversation history
PUT  /api/chat/{conversation_id}/rename  # Rename conversation
```

## 🛡 Security Features

### Multi-Layer Security Architecture
- **Layer 1**: Security Headers (HSTS, CSP, X-Frame-Options)
- **Layer 2**: CSRF Protection with token rotation
- **Layer 3**: Rate limiting and threat detection
- **Layer 4**: Input validation and sanitization

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Two-factor authentication (2FA) support
- Role-based access control (RBAC)
- Session management with configurable timeouts

### Data Protection
- End-to-end encryption for sensitive data
- Secure password hashing with bcrypt
- API key management and rotation
- Comprehensive audit logging

## 🚀 Production Deployment

### AWS Infrastructure
- **Backend**: Elastic Beanstalk with auto-scaling
- **Frontend**: S3 + CloudFront global CDN
- **Database**: MongoDB Atlas with automated backups
- **Security**: WAF + SSL/TLS encryption

### CI/CD Pipeline
```bash
# Backend deployment
aws configure
cd backend && eb deploy

# Frontend deployment  
cd frontend && npm run build
aws s3 sync dist/ s3://your-bucket --delete
aws cloudfront create-invalidation --distribution-id YOUR_ID --paths "/*"
```

## 🔄 Data Pipeline

### Automated Processing
- **Raw Data Sync**: Every 12 hours from MySQL source
- **Analytics Cache**: Updated every 6 hours  
- **Meta Integration**: Real-time sync every 30 minutes
- **AI Embeddings**: Generated after each data update

### Script Execution Order
1. **Foundation**: Store users and active store identification
2. **Product Pipeline**: Details → Variations → Categories → Sales correlation
3. **Customer Analytics**: Relationships and activity metrics  
4. **AI Analysis**: Market analysis → Competitor insights → Vector embeddings

## 📊 Monitoring & Analytics

### Performance Monitoring
- Real-time API response times
- Database query optimization
- CDN cache hit rates
- Security event tracking

### Business Intelligence
- Store performance dashboards
- Customer behavior analytics
- Revenue trend analysis
- Competitive market insights

## 🌐 Internationalization

D-Unit supports multi-language functionality with comprehensive translations:

### Supported Languages
- **English** (Primary)
- **Spanish** (Comprehensive support)

### Translated Components
- Authentication system (Login, Registration, 2FA)
- Dashboard interface and analytics
- Meta integration panels
- Admin portal and settings
- AI chat interface
- Error messages and notifications

Translation files are managed using `i18next` and located in `frontend/public/locales/`.

## 📚 Documentation

### Developer Resources
- **[Complete Documentation](readme/READMEMain.md)** - Comprehensive project documentation
- **[Backend API Guide](backend/readme/BackendReadme.md)** - Detailed backend documentation
- **[Script Documentation](backend/readme/README_SCRIPTS.md)** - Data pipeline scripts guide
- **[Frontend Guide](frontend/readme/UIREADME.md)** - Frontend development guide
- **[Claude Code Guide](CLAUDE.md)** - AI assistant integration guide

### Quick References
- **[General Fixes](readme/general-fixes.md)** - Troubleshooting and fixes
- **[API Endpoints](backend/readme/endpoints.md)** - Complete API reference
- **[Meta Integration](backend/readme/meta-overview.md)** - Social media integration guide

## 🤝 Contributing

This is a proprietary project. For development guidelines and coding standards, please refer to the documentation in the `readme/` directory.

### Development Workflow
1. Follow the setup instructions in the Quick Start section
2. Review the architecture documentation before making changes
3. Ensure all tests pass before submitting changes
4. Maintain security best practices as outlined in the documentation

## 📞 Support

For technical support and troubleshooting:
- Check the **[General Fixes Guide](readme/general-fixes.md)**
- Review system logs in the `logs/` directory
- Monitor database performance through MongoDB Atlas
- Verify API integrations (Meta, OpenAI) are functioning

## 📄 License

**Proprietary** – All rights reserved