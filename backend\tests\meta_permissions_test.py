import pytest
import asyncio
from unittest.mock import patch, MagicMock
import sys
import os
from datetime import datetime, timezone

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.meta_permissions import (
    is_meta_related_query,
    extract_meta_data_points,
    get_available_meta_data_points,
    fetch_meta_data_for_chat,
    generate_permission_message,
    get_data_point_permission_status,
    track_permission_change,
    get_permission_history
)

class TestMetaPermissions:
    """Test the Meta permissions functionality"""
    
    def test_is_meta_related_query(self):
        """Test the is_meta_related_query function"""
        # Test positive cases
        assert is_meta_related_query("How many Instagram followers do I have?") == True
        assert is_meta_related_query("Show me my Facebook engagement") == True
        assert is_meta_related_query("What's my social media performance?") == True
        
        # Test negative cases
        assert is_meta_related_query("How many orders did I get yesterday?") == False
        assert is_meta_related_query("What's my revenue this month?") == False
        assert is_meta_related_query("Show me my top products") == False
    
    def test_extract_meta_data_points(self):
        """Test the extract_meta_data_points function"""
        # Test extracting followers
        points = extract_meta_data_points("How many followers do I have on Instagram?")
        assert "followers" in points or "followers_count" in points
        
        # Test extracting multiple data points
        points = extract_meta_data_points("What's my engagement rate and post performance?")
        assert "engagement_rate" in points
        assert "post_performance" in points
        
        # Test extracting no data points
        points = extract_meta_data_points("Hello, how are you?")
        assert len(points) == 0
    
    @pytest.mark.asyncio
    async def test_get_available_meta_data_points(self):
        """Test the get_available_meta_data_points function"""
        # Mock store permissions
        mock_permissions = {
            "instagram_basic": "granted",
            "instagram_manage_insights": "revoked",
            "pages_read_engagement": "granted"
        }
        
        with patch('services.meta_permissions.get_store_permissions', return_value=mock_permissions):
            # Test with all permissions granted
            available = await get_available_meta_data_points("store123", ["followers"])
            assert available["followers"] == True
            
            # Test with some permissions revoked
            available = await get_available_meta_data_points("store123", ["reach"])
            assert available["reach"] == False
            
            # Test with mixed permissions
            available = await get_available_meta_data_points("store123", ["followers", "reach", "likes"])
            assert available["followers"] == True
            assert available["reach"] == False
            assert available["likes"] == True
    
    @pytest.mark.asyncio
    async def test_get_data_point_permission_status(self):
        """Test the get_data_point_permission_status function"""
        # Mock store permissions
        mock_permissions = {
            "instagram_basic": "granted",
            "instagram_manage_insights": "revoked",
            "pages_read_engagement": "granted"
        }
        
        with patch('services.meta_permissions.get_store_permissions', return_value=mock_permissions):
            # Test with all permissions granted
            status = await get_data_point_permission_status("store123", "followers")
            assert status["available"] == True
            assert len(status["missing_permissions"]) == 0
            
            # Test with some permissions revoked
            status = await get_data_point_permission_status("store123", "reach")
            assert status["available"] == False
            assert "instagram_manage_insights" in status["missing_permissions"]
            
            # Test with no required permissions
            status = await get_data_point_permission_status("store123", "unknown_data_point")
            assert status["available"] == True
            assert len(status["required_permissions"]) == 0
    
    @pytest.mark.asyncio
    async def test_generate_permission_message(self):
        """Test the generate_permission_message function"""
        # Test with all data available
        data = {
            "available": {"followers": {"total_followers": 1000}},
            "unavailable": []
        }
        
        message = generate_permission_message("How many followers do I have?", data)
        assert message == ""
        
        # Test with some data unavailable
        data = {
            "available": {"followers": {"total_followers": 1000}},
            "unavailable": ["reach", "impressions"]
        }
        
        message = generate_permission_message("What's my reach and impressions?", data)
        assert "can provide information about" in message
        assert "can't access" in message
        assert "permission" in message.lower()
        
        # Test with all data unavailable
        data = {
            "available": {},
            "unavailable": ["followers", "reach"]
        }
        
        message = generate_permission_message("How many followers and what's my reach?", data)
        assert "can't access" in message
        assert "permissions have been revoked" in message
    
    @pytest.mark.asyncio
    async def test_track_permission_change(self):
        """Test the track_permission_change function"""
        # Mock the database
        mock_update = MagicMock()
        
        with patch('services.meta_permissions.db_analysis', {"meta_permissions": MagicMock(update_one=mock_update)}):
            await track_permission_change("store123", "instagram_basic", "revoked")
            
            # Verify the update was called
            mock_update.assert_called_once()
            
            # Check that the update contains the correct fields
            update_query = mock_update.call_args[0][1]
            assert "$push" in update_query
            assert "revocation_history" in update_query["$push"]
            assert "$set" in update_query
            assert "last_updated" in update_query["$set"]
            assert "permissions.instagram_basic" in update_query["$set"]
            assert update_query["$set"]["permissions.instagram_basic"] == "revoked"
    
    @pytest.mark.asyncio
    async def test_get_permission_history(self):
        """Test the get_permission_history function"""
        # Create test history
        history = [
            {
                "permission": "instagram_basic",
                "status": "granted",
                "timestamp": datetime(2023, 1, 1, tzinfo=timezone.utc)
            },
            {
                "permission": "instagram_basic",
                "status": "revoked",
                "timestamp": datetime(2023, 1, 2, tzinfo=timezone.utc)
            },
            {
                "permission": "instagram_manage_insights",
                "status": "revoked",
                "timestamp": datetime(2023, 1, 3, tzinfo=timezone.utc)
            }
        ]
        
        # Mock the database
        mock_find_one = MagicMock(return_value={"revocation_history": history})
        
        with patch('services.meta_permissions.db_analysis', {"meta_permissions": MagicMock(find_one=mock_find_one)}):
            # Test getting all history
            result = await get_permission_history("store123")
            assert len(result) == 3
            
            # Test getting history for specific permission
            result = await get_permission_history("store123", "instagram_basic")
            assert len(result) == 2
            
            # Test sorting (most recent first)
            assert result[0]["timestamp"] > result[1]["timestamp"]


if __name__ == "__main__":
    pytest.main() 