#!/usr/bin/env python3
"""Test script to validate competitor analysis accuracy"""

import sys
from scripts.data.update_competitor_analysis import CompetitorAnalysisUpdater, BusinessTypeMapper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_business_type_mapping():
    """Test that business types are correctly mapped"""
    test_cases = [
        ("Venta de Accesorios", "optical"),  # Should map to optical
        ("Tiendas de indumentaria", "clothing"),  # Should map to clothing
        ("Gimnasio/centro de fitness", "fitness"),  # Should map to fitness
        ("Unknown Business", "default"),  # Should map to default
    ]
    
    print("\n=== Testing Business Type Mapping ===")
    for business_type, expected_category in test_cases:
        keywords = BusinessTypeMapper.get_keywords(business_type)
        contains_expected = any(expected in str(keywords).lower() for expected in [expected_category])
        status = "✓" if contains_expected else "✗"
        print(f"{status} {business_type} -> {expected_category}")

def test_competitor_extraction():
    """Test competitor extraction doesn't hallucinate"""
    updater = CompetitorAnalysisUpdater(force_refresh=True, max_queries_per_store=2)
    
    # Test with a sample search result
    sample_search = """
Title: Optica Florida - Tienda online
Domain: opticaflorida.com.uy
Description: Venta de lentes y gafas en Uruguay
URL: https://opticaflorida.com.uy

Title: Netflix presenta Bandida
Domain: netflix.com
Description: Nueva pelicula en Netflix
URL: https://netflix.com/bandida

Title: Grandvision Uruguay - Lentes y mas
Domain: grandvision.com.uy
Description: Cadena de opticas en Uruguay
URL: https://grandvision.com.uy
"""
    
    print("\n=== Testing Competitor Extraction ===")
    competitors = updater.extract_competitor_names(sample_search, "Uruguay", "optical store")
    
    print(f"Found {len(competitors)} competitors:")
    for comp in competitors:
        print(f"  - {comp}")
    
    # Verify no Netflix in results
    assert "Netflix" not in competitors, "Netflix should not be identified as competitor"
    print("✓ Successfully filtered out irrelevant results")

def test_search_query_generation():
    """Test search query generation for different business types"""
    updater = CompetitorAnalysisUpdater()
    
    test_contexts = [
        {
            'store_name': 'Test Optical',
            'business_type': 'Venta de Accesorios',
            'country': 'Uruguay',
            'categories': ['LENTES DE SOL', 'LENTES PREMIUM']
        },
        {
            'store_name': 'Test Clothing',
            'business_type': 'Tiendas de indumentaria',
            'country': 'Argentina',
            'categories': ['Vestidos', 'Ropa Casual']
        }
    ]
    
    print("\n=== Testing Search Query Generation ===")
    for context in test_contexts:
        queries = updater.get_enhanced_search_queries(context)
        print(f"\n{context['business_type']} in {context['country']}:")
        for query in queries:
            # Verify store name is NOT in query
            assert context['store_name'] not in query, f"Store name should not be in query: {query}"
            print(f"  ✓ {query}")

def test_full_process():
    """Test full process with a real store"""
    print("\n=== Testing Full Process ===")
    print("Testing with store ID 566 (Bandida)...")
    
    updater = CompetitorAnalysisUpdater(force_refresh=True, max_queries_per_store=3)
    success = updater.process_store("566")
    
    if success:
        # Verify results in database
        result = updater.analysis_collection.find_one({"_id": "566"})
        if result and 'analysis' in result:
            analysis = result['analysis'].get('competitor_analysis', '')
            competitors = result['analysis'].get('identified_competitors', [])
            
            print(f"✓ Analysis generated with {len(competitors)} competitors")
            print(f"✓ Competitors found: {', '.join(competitors[:5])}...")
            
            # Check for common hallucination patterns
            hallucination_patterns = ['Netflix', 'Amazon', 'MercadoLibre', 'Example Store']
            for pattern in hallucination_patterns:
                assert pattern not in analysis, f"Hallucination detected: {pattern}"
            
            print("✓ No hallucinations detected in analysis")
        else:
            print("✗ No analysis found in database")
    else:
        print("✗ Process failed")

if __name__ == "__main__":
    print("Running Competitor Analysis Tests...")
    
    try:
        test_business_type_mapping()
        test_search_query_generation()
        test_competitor_extraction()
        
        # Only run full test if requested
        if "--full" in sys.argv:
            test_full_process()
        else:
            print("\n[Skipping full process test. Run with --full to include]")
        
        print("\n✓ All tests passed!")
        
    except AssertionError as e:
        print(f"\n✗ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        sys.exit(1)