import * as React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Select,
  MenuItem,
  Button,
  Alert,
  CircularProgress,
  SelectChangeEvent,
  Paper
} from '@mui/material';
import { MetaAuthService } from '../../services/auth';
import { MetaBusinessAccount, MetaPage, MetaError, MetaAuthResponse } from '../../services/types';
import { MetaLoginButton } from './MetaLoginButton';
import { MetaErrorType } from '../../services/types';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';

interface MetaBusinessConnectProps {
  onConnect?: (businessId: string, pages: MetaPage[]) => void;
  onError?: (error: string) => void;
  className?: string;
  style?: React.CSSProperties;
  isUserLoggedIn?: boolean;
}

export const MetaBusinessConnect: React.FC<MetaBusinessConnectProps> = ({
  onConnect,
  onError,
  className,
  style,
  isUserLoggedIn
}) => {
  const [businesses, setBusinesses] = useState<MetaBusinessAccount[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchingBusinesses, setFetchingBusinesses] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(isUserLoggedIn || false);
  
  // Use useRef to store the fetchBusinesses function reference
  const fetchBusinessesRef = useRef<() => Promise<void>>();
  
  // Update fetchBusinessesRef when dependencies change
  useEffect(() => {
    fetchBusinessesRef.current = async () => {
      // Don't attempt to fetch if not logged in
      if (!isLoggedIn) {
        logger.debug('Not logged in to Meta, skipping business fetch');
        return;
      }
      
      try {
        setFetchingBusinesses(true);
        setError(null);
        logger.debug('Fetching Meta business accounts...');
        const accounts = await MetaAuthService.getBusinessAccounts();
        setBusinesses(accounts);
      } catch (err: Error | MetaError | unknown) {
        logger.error('Failed to fetch business accounts:', err);
        let errorMessage = 'Failed to fetch business accounts. Please try again.';
        
        if (typeof err === 'object' && err !== null && 'type' in err) {
          const typedError = err as MetaError;
          switch (typedError.type) {
            case MetaErrorType.AUTH_FAILED:
              errorMessage = 'Meta authentication failed. Please log in again.';
              setIsLoggedIn(false);
              break;
            case MetaErrorType.PERMISSION_DENIED:
              errorMessage = 'Missing required permissions. Please grant business account access.';
              break;
            case MetaErrorType.API_ERROR:
              errorMessage = 'Failed to communicate with Meta API. Please try again later.';
              break;
            case MetaErrorType.NETWORK_ERROR:
              errorMessage = 'Network error occurred. Please check your connection.';
              break;
          }
        }
        
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setFetchingBusinesses(false);
      }
    };
  }, [isLoggedIn, onError]);
  
  // Wrapper function to call the current ref function
  const fetchBusinesses = useCallback(() => {
    fetchBusinessesRef.current?.();
  }, []);
  
  // Check if user is already logged in with Meta
  const checkLoginStatus = useCallback(async () => {
    try {
      // If we already have a login state from props, use that instead of checking
      if (isUserLoggedIn !== undefined) {
        return;
      }
      
      // Only check if FB SDK is loaded
      if (typeof window.FB === 'undefined') {
        logger.debug('Facebook SDK not loaded yet, skipping login check');
        setIsLoggedIn(false); // Make sure we default to not logged in
        return;
      }
      
      logger.debug('MetaBusinessConnect: Checking Facebook login status...');
      
      // Check login status without triggering any API calls to Meta endpoints
      const loginStatus = await new Promise<MetaAuthResponse>((resolve) => {
        try {
          window.FB.getLoginStatus((response: MetaAuthResponse) => {
            resolve(response);
          });
        } catch (err) {
          logger.error('Error with getLoginStatus call:', err);
          resolve({ status: 'unknown', authResponse: { accessToken: '', userID: '', expiresIn: 0 } });
        }
      });
      
      logger.debug('MetaBusinessConnect: Login status response:', loginStatus);
      const loggedIn = loginStatus.status === 'connected';
      logger.debug('MetaBusinessConnect: User is', loggedIn ? 'logged in' : 'not logged in', 'to Meta');
      setIsLoggedIn(loggedIn);
      
      // Only fetch businesses if logged in AND we're not using a parent-provided login state
      // This prevents duplicate API calls when used as a child component
      if (loggedIn && isUserLoggedIn === undefined) {
        fetchBusinesses();
      } else {
        logger.debug('MetaBusinessConnect: Not logged in, will show login button');
      }
    } catch (err) {
      logger.error('Error checking login status:', err);
      setIsLoggedIn(false);
    }
  }, [isUserLoggedIn, fetchBusinesses]);
  
  // Update internal state when prop changes
  useEffect(() => {
    if (isUserLoggedIn !== undefined) {
      logger.debug('MetaBusinessConnect: isUserLoggedIn prop changed to', isUserLoggedIn);
      setIsLoggedIn(isUserLoggedIn);
      
      // Important: Do NOT fetch businesses automatically when prop changes
      // This prevents redundant API calls that can trigger rate limits
    }
  }, [isUserLoggedIn]);
  
  // Initial check when component mounts
  useEffect(() => {
    checkLoginStatus();
  }, [checkLoginStatus]);

  const handleBusinessChange = (event: SelectChangeEvent<string>) => {
    setSelectedBusiness(event.target.value);
    setError(null);
  };

  const handleConnect = async () => {
    if (!selectedBusiness) return;
    
    try {
      setLoading(true);
      setError(null);
      const pages = await MetaAuthService.getPages(selectedBusiness);
      onConnect?.(selectedBusiness, pages);
    } catch (err: Error | MetaError | unknown) {
      logger.error('Failed to connect business account:', err);
      let errorMessage = 'Failed to connect business account. Please try again.';
      
      if (typeof err === 'object' && err !== null && 'type' in err) {
        const typedError = err as MetaError;
        switch (typedError.type) {
          case MetaErrorType.AUTH_FAILED:
            errorMessage = 'Meta authentication failed. Please log in again.';
            setIsLoggedIn(false);
            break;
          case MetaErrorType.PERMISSION_DENIED:
            errorMessage = 'Missing required permissions for page access.';
            break;
          case MetaErrorType.API_ERROR:
            errorMessage = 'Failed to fetch page information. Please try again later.';
            break;
          case MetaErrorType.NETWORK_ERROR:
            errorMessage = 'Network error occurred. Please check your connection.';
            break;
        }
      }
      
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSuccess = (platform?: 'facebook' | 'instagram') => {
    logger.info(`Successfully logged in with ${platform || 'Meta'}`);
    setIsLoggedIn(true);
    // Fetch businesses after successful login
    fetchBusinesses();
  };

  const { t } = useTranslation();

  return (
    <Paper 
      elevation={2} 
      className={className}
      style={style}
      sx={{ p: 3, borderRadius: 2 }}
    >
      <Typography variant="h6" gutterBottom>
        {t('metaDashboard.connect.title')}
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!isLoggedIn ? (
        <Box>
          <Alert severity="info" sx={{ mb: 3 }}>
            {t('metaDashboard.connect.noBusinessAccounts')}
          </Alert>
          
          <MetaLoginButton 
            loginMode={true}
            onSuccess={handleLoginSuccess}
            onError={(errorMsg: string) => {
              setError(errorMsg);
              onError?.(errorMsg);
            }}
          />
          
          <Box sx={{ mt: 2, textAlign: 'left' }}>
            <Typography variant="caption" color="text.secondary">
              {t('metaDashboard.connect.info')}
            </Typography>
          </Box>
        </Box>
      ) : fetchingBusinesses ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : businesses.length === 0 ? (
        <Box>
          <Alert severity="info" sx={{ mb: 3 }}>
            {t('metaDashboard.connect.noBusinessAccounts')}
          </Alert>
          
          <Box sx={{ mb: 2 }}>
            <MetaLoginButton 
              loginMode={true}
              onSuccess={handleLoginSuccess}
              onError={(errorMsg: string) => {
                setError(errorMsg);
                onError?.(errorMsg);
              }}
            />
          </Box>
          
          <Button
            variant="outlined"
            fullWidth
            onClick={() => window.open('https://business.facebook.com/create', '_blank')}
            sx={{ mb: 2 }}
          >
            {t('metaDashboard.connect.createBusinessAccount')}
          </Button>
          
          <Button
            variant="outlined"
            fullWidth
            onClick={fetchBusinesses}
          >
            {t('metaDashboard.connect.refreshAccounts')}
          </Button>
        </Box>
      ) : (
        <>
          <Select
            value={selectedBusiness}
            onChange={handleBusinessChange}
            fullWidth
            disabled={loading}
            sx={{ mb: 2 }}
          >
            {businesses.map((business) => (
              <MenuItem key={business.id} value={business.id}>
                {business.name}
              </MenuItem>
            ))}
          </Select>

          <Button
            variant="contained"
            onClick={handleConnect}
            disabled={!selectedBusiness || loading}
            fullWidth
            sx={{
              bgcolor: '#1877F2',
              '&:hover': {
                bgcolor: '#1664d9'
              }
            }}
          >
            {loading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Connecting...
              </>
            ) : (
              'Connect Business'
            )}
          </Button>
        </>
      )}
    </Paper>
  );
}; 