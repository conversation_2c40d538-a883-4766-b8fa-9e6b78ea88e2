import os, sys
import pytest
from httpx import AsyncClient
from fastapi import status

# Ensure backend module can be imported when running from repository root
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
if ROOT_DIR not in sys.path:
    sys.path.append(ROOT_DIR)
# Ensure backend dir in path
BACKEND_DIR = os.path.join(ROOT_DIR, "backend")
if BACKEND_DIR not in sys.path:
    sys.path.append(BACKEND_DIR)

# -------------------------------------------------------------------
# Mock database layer BEFORE importing backend routes to avoid
# real MongoDB connection attempts during unit tests.
# -------------------------------------------------------------------
import types
import mongomock

# Create mock config.database module
mock_db_module = types.ModuleType("config.database")
mock_client = mongomock.MongoClient()
mock_db_module.db_analysis = mock_client["test_db"]  # type: ignore[attr-defined]
mock_db_module.db_main = mock_client["test_db"]  # type: ignore[attr-defined]

# Provide simplistic async wrappers around mongomock collection methods used in route
class AsyncMockCollection:
    """Wrap a mongomock collection with awaitable methods used in tests."""

    def __init__(self, collection):
        self._coll = collection

    async def insert_one(self, *args, **kwargs):
        return self._coll.insert_one(*args, **kwargs)

    async def delete_one(self, *args, **kwargs):
        return self._coll.delete_one(*args, **kwargs)

    async def find_one_and_update(self, *args, **kwargs):
        return self._coll.find_one_and_update(*args, **kwargs)

    async def count_documents(self, *args, **kwargs):
        return self._coll.count_documents(*args, **kwargs)

    # Support [] access (e.g., db_analysis["collection"])
    def __getattr__(self, item):
        return getattr(self._coll, item)


# Replace the active_stores_cache with async wrapper
mock_db_module.active_stores_cache = AsyncMockCollection(mock_db_module.db_analysis["active_stores_cache"])  # type: ignore[attr-defined]

# db_analysis should mimic Motor database behaviour for [] access
class AsyncMockDatabase:
    def __init__(self, db):
        self._db = db

    def __getitem__(self, name):
        return AsyncMockCollection(self._db[name])

mock_db_module.db_analysis = AsyncMockDatabase(mock_db_module.db_analysis)  # type: ignore[attr-defined]

# Insert mock into sys.modules
sys.modules["config.database"] = mock_db_module

# Build minimal FastAPI app for testing
from fastapi import FastAPI
from backend.routes.admin import router as admin_router  # type: ignore

app = FastAPI()
app.include_router(admin_router)
from config.database import db_analysis


# ---------------------- Fixtures & Overrides ----------------------


@pytest.fixture(scope="module")
def admin_auth_token():
    """Dummy token for authorization header."""
    return "dummytoken"


@pytest.fixture(autouse=True, scope="module")
def override_admin_dependency():
    """Automatically override admin auth dependency in routes for tests."""
    from backend.routes.admin import get_current_active_admin_user  # local import to avoid circular
    from models.user import User

    def _get_user():
        return User(email="<EMAIL>", role="admin")  # type: ignore[arg-type]

    app.dependency_overrides[get_current_active_admin_user] = _get_user  # type: ignore[index]
    yield
    app.dependency_overrides.pop(get_current_active_admin_user, None)


@pytest.mark.asyncio
async def test_add_credits_success(admin_auth_token):  # admin_auth_token fixture assumed
    # Insert dummy store
    store_id = "test_store_credits"
    await db_analysis["active_stores_cache"].insert_one({"_id": store_id, "credits": 1000})

    async with AsyncClient(app=app, base_url="http://test") as client:  # type: ignore[arg-type]
        response = await client.post(
            f"/api/admin/store/{store_id}/add-credits",
            json={"amount": 500},
            headers={"Authorization": f"Bearer {admin_auth_token}"},
        )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["credits"] == 1500

    # Cleanup
    await db_analysis["active_stores_cache"].delete_one({"_id": store_id})


@pytest.mark.asyncio
async def test_add_credits_invalid_amount(admin_auth_token):
    store_id = "test_store_invalid_amount"
    await db_analysis["active_stores_cache"].insert_one({"_id": store_id, "credits": 0})

    async with AsyncClient(app=app, base_url="http://test") as client:  # type: ignore[arg-type]
        response = await client.post(
            f"/api/admin/store/{store_id}/add-credits",
            json={"amount": -10},
            headers={"Authorization": f"Bearer {admin_auth_token}"},
        )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    await db_analysis["active_stores_cache"].delete_one({"_id": store_id}) 