# D-Unit Backend Scripts Documentation

This document provides a comprehensive overview of the data processing scripts that form the backbone of the D-Unit analytics platform.

## Overview

The D-Unit backend includes organized scripts for various backend operations, with 11 core data processing scripts that transform raw MySQL data into enriched MongoDB collections for analytics and AI processing. These scripts follow a specific execution order to maintain data dependencies and consistency.

## Directory Structure

### `/scripts/data/` - Data Processing Scripts
Scripts that process and transform data between databases:
- `update_customers_relationships.py` - Updates customer relationship data
- `update_active_stores.py` - Updates active stores cache
- `update_competitor_analysis.py` - Updates competitor analysis data
- `update_meta_context_from_collections.py` - Updates meta context from collections
- `update_meta_contexts_scheduled.py` - Scheduled meta context updates
- `update_meta_sales_correlation.py` - Updates meta sales correlation data
- `update_product_categories.py` - Updates product categories
- `update_product_details.py` - Updates product details cache
- `update_product_variations.py` - Updates product variations
- `update_store_activity_metrics.py` - Updates store activity metrics
- `update_store_users.py` - Updates store user data
- `market_analyzer.py` - Analyzes market data
- `embeddings_generator_analysis.py` - Generates embeddings for analysis

### `/scripts/database/` - Database Management Scripts
Scripts that manage database structure and migrations:
- `meta_db_init.py` - Initializes meta database
- `migrate_meta_format.py` - Migrates meta data format
- `sync_shipping_faqs_to_mongo.py` - Syncs shipping FAQs to MongoDB
- `translate_mongo_collections_async.py` - Translates MongoDB collections

### `/scripts/admin/` - Administration Scripts
Scripts for administrative tasks:
- `reset_admin_password.py` - Resets admin password
- `setup_meta_permissions.py` - Sets up meta permissions
- `diagnose_collections.py` - Diagnoses collection issues

### `/scripts/tests/` - Test Scripts
Scripts for testing various components:
- `test_brave_search.py` - Tests Brave search functionality
- `test_brave_simple.py` - Simple Brave search tests
- `test_business_mapper.py` - Tests business mapping
- `test_competitor_analysis.py` - Tests competitor analysis
- `test_competitor_extraction.py` - Tests competitor extraction
- `test_competitor_query.py` - Tests competitor queries
- `test_csrf_production.py` - Tests CSRF protection in production
- `test_logging.py` - Tests logging functionality

### `/scripts/dev/` - Development Tools
Tools for development and debugging:
- `CSRF_COOKIE_FIX.py` - CSRF cookie fix tool

## Script Categories

### 1. Data Migration Scripts
- `update_store_users.py` - MySQL → MongoDB user migration
- `update_active_stores.py` - Active store identification and caching

### 2. Product Enhancement Pipeline
- `update_product_details.py` - Base product performance cache
- `update_product_variations.py` - SKU variations and stock data
- `update_product_categories.py` - Category information and summaries

### 3. Analytics Scripts
- `update_customers_relationships.py` - Customer analytics and order history
- `update_store_activity_metrics.py` - Store engagement tracking
- `update_meta_sales_correlation.py` - Meta ad campaign correlation

### 4. AI & Analysis Scripts
- `market_analyzer.py` - AI business analysis generation
- `update_competitor_analysis.py` - Web-sourced competitive insights
- `embeddings_generator_analysis.py` - Vector embeddings for search

## Execution Order

Scripts must be run in this specific order to maintain dependencies:

```bash
# 1. Foundation (Independent)
python scripts/data/update_store_users.py        # User data migration
python scripts/data/update_active_stores.py      # Active store identification

# 2. Product Pipeline (Sequential)
python scripts/data/update_product_details.py     # Base product cache
python scripts/data/update_product_variations.py  # → Add variations
python scripts/data/update_product_categories.py  # → Add categories

# 3. Analytics (Independent)
python scripts/data/update_customers_relationships.py  # Customer analytics
python scripts/data/update_store_activity_metrics.py   # Activity tracking
python scripts/data/update_meta_sales_correlation.py   # Meta-sales correlation

# 4. AI Analysis (Sequential)
python scripts/data/market_analyzer.py                 # AI business analysis
python scripts/data/update_competitor_analysis.py      # → Add competitor insights
python scripts/data/embeddings_generator_analysis.py   # → Generate embeddings
```

## Usage Examples

All scripts can be executed from anywhere in the backend directory or from their specific subdirectories. The scripts automatically handle path resolution to find the config modules.

```bash
# From backend root directory:
python scripts/data/update_customers_relationships.py

# From the scripts/data directory:
cd scripts/data
python update_customers_relationships.py

# With arguments:
python scripts/data/update_customers_relationships.py --store_id 123

# From any subdirectory:
python ../../scripts/admin/reset_admin_password.py
```

## Path Resolution

All scripts use automatic path resolution to find the backend config modules:

```python
# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)
```

This means the scripts will work correctly regardless of where they are executed from, as long as they can navigate to the backend root directory.

## Script Details

### Data Migration Scripts

#### `update_store_users.py`
**Purpose**: Migrates user data from MySQL to MongoDB
- **Input**: MySQL `lanube.store_users` table
- **Output**: MongoDB `D-Unit.store_users` collection
- **Features**:
  - Intelligent duplicate resolution (active > recent > original)
  - Adds `alternate_emails` field for Meta integration
  - Incremental updates supported
  - Migration metadata tracking

#### `update_active_stores.py`
**Purpose**: Identifies and caches active stores
- **Input**: MySQL store data + configuration filters
- **Output**: MongoDB `D-Unit.active_stores_cache` collection
- **Criteria**:
  - Recent orders/visits
  - Product count > threshold
  - Created ≥ 2025 (NEW_STORE_CUTOFF_YEAR)
  - Exception list (94 manually included stores)
- **Revenue**: Lifetime totals with USD conversion

### Product Enhancement Pipeline

#### `update_product_details.py`
**Purpose**: Creates base product performance cache
- **Input**: MySQL product, orders, favorites, ratings tables
- **Output**: MongoDB `D-Unit.product_details_cache` collection
- **Data**:
  - Sales units, revenue, price, stock
  - Favorite counts, related products
  - Store-level rating aggregates
- **Filtering**: Only "real sales" orders (status 2, 5, 7)

#### `update_product_variations.py`
**Purpose**: Enriches products with SKU variations
- **Input**: Product details cache + MySQL variations
- **Output**: Enhanced `product_details_cache` documents
- **Features**:
  - Batch processing (50 products for stores >10K variations)
  - Stock totals: `current_stock_variations`
  - Timeout handling (120s)
  - Progress tracking

#### `update_product_categories.py`
**Purpose**: Adds categorization data to products
- **Input**: Product details cache + MySQL categories
- **Output**: Enhanced `product_details_cache` documents
- **Features**:
  - Product categories: featured, on_sale, store categories
  - Store-level category summaries
  - Batch processing with retry logic
  - Failed store logging

### Analytics Scripts

#### `update_customers_relationships.py`
**Purpose**: Builds comprehensive customer analytics
- **Input**: MySQL customer, order, payment, shipping data
- **Output**: MongoDB `D-Unit.store_customers_cache` collection
- **Metrics**:
  - Customer profiles with complete order history
  - Store aggregations: revenue, AOV, payment methods
  - Country distribution, coupon usage
- **Performance**: Batch processing, index hints, incremental updates

#### `update_store_activity_metrics.py`
**Purpose**: Tracks store engagement over time
- **Input**: MySQL activity data + MongoDB user data
- **Output**: MongoDB `D-Unit.store_activity_metrics` collection
- **Time Periods**: 30, 90, 365-day windows
- **Metrics**:
  - Logins, visits, orders, revenue
  - Login-revenue correlation analysis
  - Traffic light status (red/yellow/green)

#### `update_meta_sales_correlation.py`
**Purpose**: Correlates Meta campaigns with sales
- **Input**: MongoDB Meta campaigns + MySQL sales data
- **Output**: MongoDB `D-Unit-AnalysisGPT.meta_sales_correlation`
- **Features**:
  - Campaign performance attribution
  - Daily sales correlation
  - ROI analysis
  - Also updates `product_details_cache` with sales summaries

### AI & Analysis Scripts

#### `market_analyzer.py`
**Purpose**: Generates AI-powered business analysis
- **Input**: All cache collections + Meta data
- **Output**: MongoDB `D-Unit-AnalysisGPT.global_analysis`
- **AI Analysis**:
  - Executive summary, KPIs
  - Customer and product insights
  - Competitive positioning
  - Strategic recommendations
- **API**: OpenAI GPT-4 models

#### `update_competitor_analysis.py`
**Purpose**: Adds competitive intelligence
- **Input**: Global analysis + web search results
- **Output**: Enhanced `global_analysis.competitor_analysis`
- **Features**:
  - Brave Search API integration
  - Market positioning analysis
  - Competitor strengths/weaknesses
  - Strategic recommendations

#### `embeddings_generator_analysis.py`
**Purpose**: Creates vector embeddings for AI search
- **Input**: All analysis collections
- **Output**: Multiple `*_embeddings` collections
- **Features**:
  - OpenAI embedding API
  - Content hashing for efficiency
  - Compressed float16 storage
  - Semantic search enablement

## Database Schema

### Primary Databases
- **D-Unit**: Operational data (users, caches, metrics)
- **D-Unit-AnalysisGPT**: Analytics and AI data

### Collection Dependencies

```
Foundation Collections (Independent):
├── store_users              ← update_store_users.py
├── active_stores_cache      ← update_active_stores.py
├── store_customers_cache    ← update_customers_relationships.py
└── store_activity_metrics   ← update_store_activity_metrics.py

Product Pipeline (Sequential):
product_details_cache ← update_product_details.py
                     ← update_product_variations.py (enhances)
                     ← update_product_categories.py (enhances)
                     ← update_meta_sales_correlation.py (enhances)

Analysis Pipeline (Hierarchical):
├── meta_sales_correlation   ← update_meta_sales_correlation.py
├── global_analysis          ← market_analyzer.py
│                           ← update_competitor_analysis.py (enhances)
└── *_embeddings            ← embeddings_generator_analysis.py
```

## Revenue Calculation Standard

All scripts use consistent revenue calculation for data integrity:

**Formula**: `sum(item_prices) + shipping_cost - order_discount`
**Valid Orders**: Status 2 (Pago), 5 (Pedido Entregado), 7 (Completo)

### Revenue by Collection
- **active_stores_cache**: Lifetime revenue totals
- **store_activity_metrics**: Time-windowed revenue (30/90/365 days)
- **meta_sales_correlation**: Campaign-specific revenue attribution

## Script Features

### Performance Optimizations
- **Batch Processing**: Large datasets processed in chunks
- **Index Hints**: SQL query optimization
- **Timeout Handling**: MongoDB operation timeouts
- **Progress Tracking**: Detailed completion percentages
- **Retry Logic**: Exponential backoff for failures

### Error Handling
- **Failed Store Logging**: JSON files for retry processing
- **Incremental Updates**: Resume from last successful update
- **Data Validation**: Input validation and cleanup
- **Comprehensive Logging**: Detailed execution logs

### Configuration Options
Many scripts support command-line options:
- `--store_id`: Process single store
- `--batch_threshold`: Customize batch sizes
- `--retry_failed`: Retry previously failed stores
- `--socket_timeout`: MongoDB timeout configuration

## Utility Scripts

### Meta Integration
- `update_meta_context_from_collections.py` - Builds chat context from Meta data
- `update_meta_contexts_scheduled.py` - Scheduled Meta context updates

### Translation Support
- `translate_mongo_collections_async.py` - Translates analysis content

## Monitoring & Maintenance

### Regular Schedules
- **Daily**: Active stores, Meta context
- **Every 6 hours**: Store activity metrics
- **Campaign changes**: Meta sales correlation
- **Data changes**: Embeddings (automatic)

### Best Practices
1. Run scripts in the specified order
2. Monitor execution logs for errors
3. Verify data consistency across collections
4. Schedule based on data freshness requirements
5. Backup databases before major updates

## Troubleshooting

### Common Issues
- **Timeout Errors**: Increase batch sizes or timeouts
- **Memory Issues**: Process smaller batches
- **Dependency Errors**: Ensure prerequisite scripts completed
- **Data Inconsistency**: Check order status filtering

### Recovery Procedures
1. Check logs for specific error details
2. Use `--retry_failed` options where available
3. Process individual stores with `--store_id`
4. Verify MySQL and MongoDB connectivity