import os
import sys
import logging
import mysql.connector
from datetime import datetime, timezone
from decimal import Decimal
from typing import List, Dict, Any, Optional
import json

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    CACHE_COLLECTION_NAME = 'product_details_cache'
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    
    # Add constants for store filtering to match update_active_stores.py
    INACTIVE_STORE_EXCEPTIONS = getattr(settings, 'INACTIVE_STORE_EXCEPTIONS', [])
    NEW_STORE_CUTOFF_YEAR = getattr(settings, 'NEW_STORE_CUTOFF_YEAR', 2025)
    
    # Price discrepancy threshold (when to use actual sale price instead of catalog price)
    PRICE_DISCREPANCY_THRESHOLD = 1000  # If difference is > $1000, use actual sale price
    PRICE_RATIO_THRESHOLD = 10  # If actual price is 10x different, use actual sale price
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# --- Helper Functions ---

def get_stores_to_process(mysql_conn) -> List[Dict[str, Any]]:
    """
    Fetches active stores from the MySQL database, using the same criteria as
    update_active_stores.py to ensure consistency.
    """
    stores: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available for get_stores_to_process.")
        return stores
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            exception_clause = f"OR s.id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
            
            query = f"""
            SELECT 
                s.id_store, s.symbol, s.name
            FROM stores s
            WHERE 
                (s.active = 1
                {exception_clause}
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
            ORDER BY s.id_store
            """
            cursor.execute(query)
            stores = cursor.fetchall()
            logger.info(f"Found {len(stores)} stores to process using consistent criteria.")
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching stores: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching stores from MySQL: {e}", exc_info=True)
    return stores

def query_product_performance_mysql(store_id: int, mysql_conn) -> List[Dict[str, Any]]:
    """
    Queries MySQL for product performance data for a given store.
    FIXED VERSION: Properly distributes order-level discounts to products proportionally.
    
    Includes both active and inactive products that have sales.
    Calculates sales units, NET revenue (after proportional discounts), current price, and stock.
    
    IMPORTANT: Only counts orders with status 2 (Pago), 5 (Pedido Entregado), or 7 (Completo) as real sales.
    
    Returns a list of product details dictionaries, sorted by net revenue (products with sales first).
    """
    product_details: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for query_product_performance_mysql (store: {store_id}).")
        return product_details
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # First, get price history for all products in a separate query
            price_history_query = """
WITH all_orders AS (
    SELECT id_store_order,
           id_store,
           created_at,
           id_order_status
    FROM store_orders
    WHERE id_store = %s
    UNION ALL
    SELECT id_order            AS id_store_order,
           id_store,
           date                AS created_at,
           id_status           AS id_order_status
    FROM orders
    WHERE id_store = %s
)
SELECT
    sop.id_product,
    sop.price,
    MAX(ao.created_at)                        AS last_used_date,
    COUNT(DISTINCT ao.id_store_order)         AS times_used,
    SUM(sop.qty)                              AS units_sold_at_price
FROM store_ordered_products sop
JOIN all_orders ao ON ao.id_store_order = sop.id_store_order
WHERE ao.id_order_status IN (2,5,7)
GROUP BY sop.id_product, sop.price
ORDER BY sop.id_product, last_used_date DESC
"""
            cursor.execute(price_history_query, (store_id, store_id))
            price_history_results = cursor.fetchall()
            
            # Build price history dictionary
            price_history_by_product = {}
            latest_prices = {}
            
            for row in price_history_results:
                product_id = row['id_product']
                
                # Initialize price history list for this product if needed
                if product_id not in price_history_by_product:
                    price_history_by_product[product_id] = []
                
                # Add to price history
                price_history_by_product[product_id].append({
                    'price': float(row['price']),
                    'last_used': row['last_used_date'].strftime('%Y-%m-%d') if row['last_used_date'] else None,
                    'times_used': int(row['times_used']),
                    'units_sold': int(row['units_sold_at_price'])
                })
                
                # Track latest price (first one for each product due to ORDER BY)
                if product_id not in latest_prices:
                    latest_prices[product_id] = {
                        'price': float(row['price']),
                        'last_used_date': row['last_used_date'],
                        'times_used': int(row['times_used'])
                    }
            
            # FIXED: Main query with proper discount distribution
            main_query = """
            WITH all_orders AS (
                SELECT id_store_order,
                       id_store,
                       subtotal,
                       COALESCE(discount,0)   AS discount,
                       created_at,
                       id_order_status
                FROM store_orders
                WHERE id_store = %s
                UNION ALL
                SELECT id_order              AS id_store_order,
                       id_store,
                       amount                AS subtotal,
                       0                     AS discount,
                       date                  AS created_at,
                       id_status             AS id_order_status
                FROM orders
                WHERE id_store = %s
            ),
            order_totals AS (
                SELECT id_store_order, subtotal, discount, created_at, id_order_status
                FROM all_orders
                WHERE id_order_status IN (2,5,7)
            ),
            product_sales AS (
                -- Calculate sales metrics with proper discount distribution
                SELECT 
                    p.id_product,
                    p.name,
                    p.active,
                    p.status,
                    p.deleted_at,
                    p.price AS catalog_price,
                    p.stock AS current_stock,
                    -- Sales units
                    COALESCE(SUM(sop.qty), 0) AS sales_units,
                    -- Gross revenue (before discounts)
                    COALESCE(SUM(sop.price * sop.qty), 0) AS gross_revenue,
                    -- Calculate proportional discount for each product
                    COALESCE(SUM(
                        CASE 
                            WHEN ot.subtotal > 0 THEN 
                                (sop.price * sop.qty) * (ot.discount / ot.subtotal)
                            ELSE 0 
                        END
                    ), 0) AS allocated_discount,
                    -- Net revenue (after proportional discount)
                    COALESCE(SUM(
                        (sop.price * sop.qty) - 
                        CASE 
                            WHEN ot.subtotal > 0 THEN 
                                (sop.price * sop.qty) * (ot.discount / ot.subtotal)
                            ELSE 0 
                        END
                    ), 0) AS net_revenue,
                    -- Average sale price (weighted by quantity)
                    CASE 
                        WHEN SUM(sop.qty) > 0 THEN 
                            SUM(sop.price * sop.qty) / SUM(sop.qty)
                        ELSE NULL
                    END AS avg_sale_price,
                    -- Recent sales flag
                    MAX(CASE 
                        WHEN ot.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY) 
                        THEN 1 ELSE 0 
                    END) AS has_recent_sales
                FROM products p
                LEFT JOIN store_ordered_products sop ON p.id_product = sop.id_product
                LEFT JOIN order_totals ot ON sop.id_store_order = ot.id_store_order
                WHERE p.id_store = %s
                    AND (sop.id_product IS NULL OR ot.id_store_order IS NOT NULL)
                GROUP BY p.id_product, p.name, p.active, p.status, p.deleted_at, p.price, p.stock
            ),
            favorite_counts AS (
                -- Get favorite counts separately to avoid multiplication
                SELECT 
                    id_product,
                    COUNT(DISTINCT id_store_customer) AS favorite_count
                FROM favorites_products
                WHERE active = 1
                GROUP BY id_product
            ),
            related_counts AS (
                -- Get related product counts separately to avoid multiplication
                SELECT 
                    id_product,
                    COUNT(DISTINCT id_related_product) AS related_product_count
                FROM related_products
                WHERE active = 1
                GROUP BY id_product
            )
            SELECT
                ps.id_product AS product_id,
                ps.name,
                ps.active AS is_active,
                ps.status AS is_online,
                CASE WHEN ps.deleted_at IS NOT NULL THEN 1 ELSE 0 END AS is_deleted,
                ps.sales_units,
                ps.gross_revenue,
                ps.allocated_discount,
                ps.net_revenue,
                ps.catalog_price,
                ps.avg_sale_price,
                ps.current_stock,
                COALESCE(fc.favorite_count, 0) AS favorite_count,
                COALESCE(rc.related_product_count, 0) AS related_product_count,
                ps.has_recent_sales,
                -- Determine if product is currently sellable
                CASE 
                    WHEN ps.active = 1 AND ps.status = 1 AND ps.deleted_at IS NULL 
                    THEN 1 ELSE 0 
                END AS is_currently_sellable,
                -- Calculate average discount percentage for this product
                CASE 
                    WHEN ps.gross_revenue > 0 THEN 
                        ROUND((ps.allocated_discount / ps.gross_revenue) * 100, 2)
                    ELSE 0 
                END AS avg_discount_percentage
            FROM product_sales ps
            LEFT JOIN favorite_counts fc ON ps.id_product = fc.id_product
            LEFT JOIN related_counts rc ON ps.id_product = rc.id_product
            WHERE 
                -- Include products with sales OR active products
                ps.net_revenue > 0 
                OR (ps.active = 1 AND ps.status = 1 AND ps.deleted_at IS NULL)
            ORDER BY ps.net_revenue DESC, ps.name;
            """
            cursor.execute(main_query, (store_id, store_id, store_id))
            results = cursor.fetchall()

            # Track products with and without sales
            products_with_sales = 0
            products_without_sales = 0
            products_with_dynamic_pricing = 0
            active_products_count = 0
            inactive_products_with_sales = 0
            inactive_product_revenue = 0.0
            total_discounts = 0.0

            # Also get total product count for comparison
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(CASE WHEN active = 1 AND deleted_at IS NULL THEN 1 END) as active_products,
                    COUNT(CASE WHEN active = 0 THEN 1 END) as inactive_products,
                    COUNT(CASE WHEN deleted_at IS NOT NULL THEN 1 END) as deleted_products
                FROM products 
                WHERE id_store = %s
            """, (store_id,))
            
            product_stats = cursor.fetchone()
            total_products = product_stats.get('total_products', 0)
            inactive_products = product_stats.get('inactive_products', 0)
            deleted_products = product_stats.get('deleted_products', 0)

            for row in results:
                # Use .get() for safer dictionary access and provide defaults
                product_id = int(row.get('product_id', 0))
                sales_units = int(row.get('sales_units', 0) or 0)
                gross_revenue = float(row.get('gross_revenue', 0.0) or 0.0)
                allocated_discount = float(row.get('allocated_discount', 0.0) or 0.0)
                net_revenue = float(row.get('net_revenue', 0.0) or 0.0)
                catalog_price = float(row.get('catalog_price', 0.0) or 0.0)
                avg_sale_price = row.get('avg_sale_price')
                has_recent_sales = bool(row.get('has_recent_sales', 0))
                avg_discount_percentage = float(row.get('avg_discount_percentage', 0.0) or 0.0)
                
                # Product status
                is_active = bool(row.get('is_active', 0))
                is_online = bool(row.get('is_online', 0))
                is_deleted = bool(row.get('is_deleted', 0))
                is_currently_sellable = bool(row.get('is_currently_sellable', 0))
                
                if is_currently_sellable:
                    active_products_count += 1
                
                # Get latest price info
                latest_price_info = latest_prices.get(product_id, {})
                current_price = latest_price_info.get('price', catalog_price)
                current_price_date = latest_price_info.get('last_used_date')
                current_price_usage = latest_price_info.get('times_used', 0)
                
                # Get price history
                price_history = price_history_by_product.get(product_id, [])
                
                if sales_units > 0:
                    products_with_sales += 1
                    total_discounts += allocated_discount
                    
                    if not is_currently_sellable:
                        inactive_products_with_sales += 1
                        inactive_product_revenue += net_revenue
                    
                    # Check if using dynamic pricing (current price differs from catalog)
                    if abs(current_price - catalog_price) > 0.01:
                        products_with_dynamic_pricing += 1
                        logger.debug(f"Dynamic pricing for '{row.get('name', 'Unknown')}' (ID: {product_id}): "
                                   f"Catalog: ${catalog_price:.2f} → Current: ${current_price:.2f} "
                                   f"(Last used: {current_price_date})")
                else:
                    products_without_sales += 1
                
                product_details.append({
                    "product_id": str(product_id),
                    "name": str(row.get('name', 'Unknown Product')),
                    "sales_units": sales_units,
                    "gross_revenue": gross_revenue,  # Revenue before discounts
                    "allocated_discount": allocated_discount,  # This product's share of discounts
                    "revenue": net_revenue,  # NET revenue after discounts (renamed for compatibility)
                    "net_revenue": net_revenue,  # Also include as net_revenue for clarity
                    "avg_discount_percentage": avg_discount_percentage,
                    "current_price": current_price,  # This now uses the LATEST sale price
                    "catalog_price": catalog_price,  # Original catalog price for reference
                    "avg_sale_price": float(avg_sale_price) if avg_sale_price else None,
                    "current_price_date": current_price_date,  # When current price was last used
                    "current_price_usage_count": current_price_usage,
                    "price_history": price_history,  # Full price history
                    "current_stock": int(row.get('current_stock', 0) or 0),
                    "favorite_count": int(row.get('favorite_count', 0) or 0),
                    "related_product_count": int(row.get('related_product_count', 0) or 0),
                    "has_recent_sales": has_recent_sales,  # Flag for recent activity
                    "uses_dynamic_pricing": abs(current_price - catalog_price) > 0.01,  # Flag for dynamic pricing
                    # New status fields
                    "is_active": is_active,
                    "is_online": is_online,
                    "is_deleted": is_deleted,
                    "is_currently_sellable": is_currently_sellable
                })
            
            # Enhanced logging
            logger.info(f"Store {store_id} product stats: {len(results)} total products "
                       f"({active_products_count} currently sellable). "
                       f"Sales breakdown: {products_with_sales} with sales "
                       f"({inactive_products_with_sales} inactive products contributed ${inactive_product_revenue:.2f}), "
                       f"{products_without_sales} without sales. "
                       f"Total discounts allocated: ${total_discounts:.2f}. "
                       f"Dynamic pricing: {products_with_dynamic_pricing} products.")

    except mysql.connector.Error as err:
         logger.error(f"MySQL Error querying performance for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error querying performance for store {store_id}: {e}", exc_info=True)

    return product_details

def query_store_ratings_mysql(store_id: int, mysql_conn) -> Dict[str, Any]:
    """
    Queries MySQL for store-level rating aggregates (count and average).
    """
    store_ratings = {"store_ratings_count": 0, "store_average_rating": 0.0}
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for query_store_ratings_mysql (store: {store_id}).")
        return store_ratings # Return default values

    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query = """
            SELECT
                COUNT(id_ratings_products) AS store_ratings_count,
                COALESCE(AVG(ratings), 0.0) AS store_average_rating
            FROM ratings_products
            WHERE store_id = %s AND active = 1;
            """
            cursor.execute(query, (store_id,))
            result = cursor.fetchone()
            if result:
                # Ensure types are correct (int and float)
                store_ratings["store_ratings_count"] = int(result.get('store_ratings_count', 0))
                # AVG returns Decimal, cast to float for JSON/Mongo compatibility
                avg_rating = result.get('store_average_rating', 0.0)
                store_ratings["store_average_rating"] = float(avg_rating) if avg_rating is not None else 0.0
                logger.info(f"Fetched store-level ratings for store {store_id}: Count={store_ratings['store_ratings_count']}, Average={store_ratings['store_average_rating']:.2f}")
            else:
                # Log if no results found (e.g., store has no ratings yet)
                logger.info(f"No active ratings found for store {store_id}.")

    except mysql.connector.Error as err:
        logger.error(f"MySQL Error querying ratings for store {store_id}: {err}")
        # Return default values on error
    except Exception as e:
        logger.error(f"Unexpected error querying ratings for store {store_id}: {e}", exc_info=True)
        # Return default values on error

    return store_ratings

def get_shipping_revenue_info(store_id: int, mysql_conn) -> Dict[str, Any]:
    """
    Queries MySQL to get shipping revenue information for a store.
    This helps users understand the difference between product revenue and total revenue.
    """
    shipping_info = {
        "shipping_revenue": 0.0,
        "total_revenue_with_shipping": 0.0,
        "shipping_revenue_percentage": 0.0,
        "average_shipping_per_order": 0.0,
        "has_shipping_data": False
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_shipping_revenue_info (store: {store_id}).")
        return shipping_info
        
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Query to get total revenue including shipping
            query = """
            SELECT
                COUNT(DISTINCT so.id_store_order) AS total_orders,
                -- Product revenue (subtotal - discount)
                COALESCE(SUM(so.subtotal - COALESCE(so.discount, 0)), 0) AS product_revenue,
                -- Total revenue (subtotal + shipping - discount)
                COALESCE(SUM(so.subtotal + COALESCE(so.cost_shipping, 0) - COALESCE(so.discount, 0)), 0) AS total_revenue,
                -- Just shipping revenue
                COALESCE(SUM(COALESCE(so.cost_shipping, 0)), 0) AS shipping_revenue
            FROM store_orders so
            WHERE so.id_store = %s 
                AND so.id_order_status IN (2, 5, 7)  -- Only completed orders
            """
            cursor.execute(query, (store_id,))
            result = cursor.fetchone()
            
            if result and result.get('total_orders', 0) > 0:
                total_orders = int(result.get('total_orders', 0))
                product_revenue = float(result.get('product_revenue', 0.0) or 0.0)
                total_revenue = float(result.get('total_revenue', 0.0) or 0.0)
                shipping_revenue = float(result.get('shipping_revenue', 0.0) or 0.0)
                
                # Calculate percentages and averages
                shipping_percentage = (shipping_revenue / total_revenue * 100) if total_revenue > 0 else 0
                avg_shipping = shipping_revenue / total_orders if total_orders > 0 else 0
                
                shipping_info = {
                    "shipping_revenue": round(shipping_revenue, 2),
                    "total_revenue_with_shipping": round(total_revenue, 2),
                    "product_revenue_net": round(product_revenue, 2),  # Net product revenue after discounts
                    "shipping_revenue_percentage": round(shipping_percentage, 2),
                    "average_shipping_per_order": round(avg_shipping, 2),
                    "has_shipping_data": True,
                    "total_orders_count": total_orders
                }
                
                logger.info(f"Store {store_id} shipping info: ${shipping_revenue:.2f} shipping revenue "
                           f"({shipping_percentage:.1f}% of total), avg ${avg_shipping:.2f}/order")
            else:
                logger.info(f"No shipping data found for store {store_id}")
                
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error querying shipping info for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error querying shipping info for store {store_id}: {e}", exc_info=True)
        
    return shipping_info

def get_store_discount_info(store_id: int, mysql_conn) -> Dict[str, Any]:
    """
    Queries MySQL to get discount information for a store.
    This helps users understand the discount patterns.
    """
    discount_info = {
        "total_gross_revenue": 0.0,
        "total_discounts": 0.0,
        "average_discount_percentage": 0.0,
        "orders_with_discounts": 0,
        "total_orders": 0,
        "has_discount_data": False
    }
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_store_discount_info (store: {store_id}).")
        return discount_info
        
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            query = """
            SELECT
                COUNT(DISTINCT so.id_store_order) AS total_orders,
                COUNT(DISTINCT CASE WHEN so.discount > 0 THEN so.id_store_order END) AS orders_with_discounts,
                COALESCE(SUM(so.subtotal), 0) AS total_gross_revenue,
                COALESCE(SUM(COALESCE(so.discount, 0)), 0) AS total_discounts,
                CASE 
                    WHEN SUM(so.subtotal) > 0 THEN 
                        (SUM(COALESCE(so.discount, 0)) / SUM(so.subtotal)) * 100
                    ELSE 0 
                END AS average_discount_percentage
            FROM store_orders so
            WHERE so.id_store = %s 
                AND so.id_order_status IN (2, 5, 7)  -- Only completed orders
            """
            cursor.execute(query, (store_id,))
            result = cursor.fetchone()
            
            if result and result.get('total_orders', 0) > 0:
                discount_info = {
                    "total_gross_revenue": round(float(result.get('total_gross_revenue', 0.0) or 0.0), 2),
                    "total_discounts": round(float(result.get('total_discounts', 0.0) or 0.0), 2),
                    "average_discount_percentage": round(float(result.get('average_discount_percentage', 0.0) or 0.0), 2),
                    "orders_with_discounts": int(result.get('orders_with_discounts', 0)),
                    "total_orders": int(result.get('total_orders', 0)),
                    "has_discount_data": True
                }
                
                logger.info(f"Store {store_id} discount info: ${discount_info['total_discounts']:.2f} total discounts "
                           f"({discount_info['average_discount_percentage']:.1f}% average), "
                           f"{discount_info['orders_with_discounts']}/{discount_info['total_orders']} orders with discounts")
                
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error querying discount info for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error querying discount info for store {store_id}: {e}", exc_info=True)
        
    return discount_info

def update_product_details_cache(store_id: int, currency_symbol: str, performance_data: list, 
                               store_ratings: Dict[str, Any], shipping_info: Dict[str, Any],
                               discount_info: Dict[str, Any], mongo_db: Optional[Any]) -> bool:
    """Updates or inserts the product details into the MongoDB cache collection with proper revenue calculations."""
    if mongo_db is None:
        logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
        return False
    try:
        collection = mongo_db[CACHE_COLLECTION_NAME]
        now_utc = datetime.now(timezone.utc)

        # Calculate store-level aggregations from products (using NET revenue)
        total_gross_revenue = discount_info.get('total_gross_revenue', 0.0) # Updated to use total_gross_revenue from discount_info
        total_allocated_discounts = discount_info.get('total_discounts', 0.0) # Updated to use total_discounts from discount_info
        total_store_revenue = total_gross_revenue - total_allocated_discounts # Recalculated for consistency
        total_units_sold = sum(product.get('sales_units', 0) for product in performance_data)
        products_with_sales = sum(1 for product in performance_data if product.get('sales_units', 0) > 0)
        products_with_dynamic_pricing = sum(1 for product in performance_data if product.get('uses_dynamic_pricing', False))
        products_with_recent_sales = sum(1 for product in performance_data if product.get('has_recent_sales', False))
        products_online = sum(1 for product in performance_data if product.get('is_active', False) and product.get('is_online', False))

        # Revenue calculations now match between product-level and store-level queries
        # No scaling factor needed since the WHERE clause fix ensures only valid orders are included

        # Add currency symbol and timestamp to each product record
        products_to_cache = [
            {
                **product,
                "currency_symbol": currency_symbol or ' $ ',
                "last_updated": now_utc
            } for product in performance_data
        ]

        # --- Store Aggregations --- #
        store_aggregations = {
            "gross_product_revenue": total_gross_revenue,
            "net_revenue_after_discounts": total_store_revenue,
            "total_allocated_discounts": total_allocated_discounts,
            "total_revenue": total_store_revenue, # Legacy field name, equals net_revenue_after_discounts
            "total_gross_revenue": total_gross_revenue, # Legacy field name, equals gross_product_revenue
            "total_units_sold": total_units_sold,
            "products_with_sales": products_with_sales,
            "products_without_sales": len(products_to_cache) - products_with_sales,
            "products_with_dynamic_pricing": products_with_dynamic_pricing,
            "products_with_recent_sales": products_with_recent_sales,
            "products_online": products_online,
            "currency_symbol": currency_symbol,
            "store_average_rating": store_ratings.get("store_average_rating", 0.0),
            "store_ratings_count": store_ratings.get("store_ratings_count", 0),
            "average_discount_percentage": discount_info.get('average_discount_percentage', 0.0),
            "orders_with_discounts": discount_info.get('orders_with_discounts', 0),
            "discount_frequency": discount_info.get('discount_frequency', 0.0),
            "shipping_revenue": shipping_info.get('shipping_revenue', 0.0),
            "shipping_revenue_percentage": shipping_info.get('shipping_revenue_percentage', 0.0),
            "average_shipping_per_order": shipping_info.get('average_shipping_per_order', 0.0),
        }

        # Construct the final document to save in MongoDB
        store_details = {
            "active_products_only": False,
            "product_count": len(products_to_cache),
            "products": products_to_cache,
            "store_aggregations": store_aggregations,
            "price_detection_version": "4.0",
            "last_calculated": now_utc,
            "overall_last_updated": now_utc,
        }

        # Update or insert into MongoDB
        result = collection.update_one(
            {"_id": str(store_id)},
            {"$set": store_details},
            upsert=True
        )

        if result.upserted_id:
            if len(products_to_cache) > 0:
                logger.info(f"Inserted new product details cache for store {store_id} with {len(products_to_cache)} products.")
            else:
                logger.info(f"Inserted new product details cache for store {store_id} (store exists but has no active products in inventory).")
        elif result.modified_count > 0:
            if len(products_to_cache) > 0:
                logger.info(f"Updated product details cache for store {store_id} with {len(products_to_cache)} products.")
            else:
                logger.info(f"Updated product details cache for store {store_id} (store exists but has no active products in inventory).")
        else:
            logger.info(f"No changes needed for product details cache for store {store_id}.")
        return True

    except Exception as e:
        logger.error(f"Error updating MongoDB cache for store {store_id}: {e}", exc_info=True)
        return False

# --- Main Execution ---

def main():
    logger.info("Starting product details cache update script (v4.0 - Proper Discount Distribution).")
    logger.info("NOTE: This version properly distributes order-level discounts to products for accurate NET revenue.")
    mysql_conn = None
    mongo_client = None
    processed_count = 0
    failed_count = 0
    total_active_products = 0
    total_dynamic_pricing_products = 0
    stores_with_shipping_info = 0
    stores_with_discount_info = 0

    try:
        # 1. Establish Connections
        logger.info(f"Connecting to MySQL host: {MYSQL_HOST}, database: {MYSQL_DB_LANUBE}")
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=10 # Add a timeout
        )
        logger.info("MySQL connection successful.")

        logger.info("Connecting to MongoDB...")
        mongo_client = get_mongodb_connection() # Use the imported function
        if mongo_client is None:
             # get_mongodb_connection already logs errors/raises HTTPException usually,
             # but add a safeguard here.
             logger.error("Failed to establish MongoDB connection (get_mongodb_connection returned None). Exiting.")
             raise ConnectionError("MongoDB connection failed.") # Raise error to trigger finally block

        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"MongoDB connection successful (DB: {MONGO_DB_ANALYSIS_NAME}).")

        # 2. Get list of stores to process
        stores = get_stores_to_process(mysql_conn)
        if not stores:
            logger.warning("No active stores found to process. Exiting.")
            return # Exit gracefully if no stores

        # 3. Process each store
        logger.info(f"Processing {len(stores)} stores...")
        for store in stores:
            store_id = None # Initialize for safety
            try:
                # Safely get store ID and currency symbol
                if not isinstance(store, dict):
                    logger.warning(f"Store data is not a dictionary: {store}. Skipping.")
                    failed_count += 1
                    continue

                # Use correct column name: id_store
                store_id_val = store.get('id_store')
                if store_id_val is None:
                    logger.warning(f"Skipping store entry due to missing 'id_store': {store}")
                    failed_count += 1
                    continue

                store_id = int(store_id_val) # Convert fetched ID to int

                # Use correct column name: symbol
                currency_symbol = str(store.get('symbol', '$')) # Default to '$'

                logger.info(f"--- Processing store ID: {store_id} ---")

                # 4. Query MySQL for product performance (with proper discount distribution)
                performance_data = query_product_performance_mysql(store_id, mysql_conn)

                # 5. Query MySQL for store ratings
                store_ratings = query_store_ratings_mysql(store_id, mysql_conn)
                
                # 5b. Query MySQL for shipping revenue info
                shipping_info = get_shipping_revenue_info(store_id, mysql_conn)
                if shipping_info.get('has_shipping_data', False):
                    stores_with_shipping_info += 1
                    
                # 5c. Query MySQL for discount info
                discount_info = get_store_discount_info(store_id, mysql_conn)
                if discount_info.get('has_discount_data', False):
                    stores_with_discount_info += 1

                # 6. Update MongoDB cache
                # Only update if performance data query was successful (returned a list, possibly empty)
                if isinstance(performance_data, list):
                    # Track total products processed and dynamic pricing usage
                    total_active_products += len(performance_data)
                    # Count products with dynamic pricing
                    products_with_dynamic = sum(1 for p in performance_data if p.get('uses_dynamic_pricing', False))
                    total_dynamic_pricing_products += products_with_dynamic
                    
                    # Pass all info dictionaries to the cache function
                    success = update_product_details_cache(store_id, currency_symbol, performance_data, 
                                                         store_ratings, shipping_info, discount_info, mongo_db)
                    if success:
                        processed_count += 1
                    else:
                        logger.error(f"Failed to update cache for store {store_id}.")
                        failed_count += 1
                else:
                     # This case indicates an error during the MySQL query itself
                     logger.error(f"Skipping cache update for store {store_id} due to error in performance data query.")
                     failed_count += 1

            except (ValueError, TypeError) as type_err:
                 # Use correct column name: id_store
                logger.warning(f"Skipping store entry due to invalid data format (ID: {store.get('id_store', 'N/A')}): {type_err}")
                failed_count += 1
            except Exception as inner_e:
                # Catch unexpected errors during loop processing for a single store
                logger.error(f"Unexpected error processing store ID {store_id or 'Unknown'}: {inner_e}", exc_info=True)
                failed_count += 1
                # Continue to the next store

        logger.info("--- Store processing finished ---")

    except mysql.connector.Error as db_err:
        logger.error(f"Database connection error: {db_err}")
    except ConnectionError as conn_err:
        logger.error(f"Connection error: {conn_err}")
    except Exception as e:
        # Catch broader errors during setup or overall processing
        logger.error(f"An unexpected error occurred during script execution: {e}", exc_info=True)
    finally:
        # Ensure connections are closed
        logger.info("Closing database connections...")
        if mysql_conn and mysql_conn.is_connected():
            try:
                mysql_conn.close()
                logger.info("MySQL connection closed.")
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
        if mongo_client:
            try:
                mongo_client.close()
                logger.info("MongoDB connection closed.")
            except Exception as e:
                logger.error(f"Error closing MongoDB connection: {e}")

        logger.info("--- Script Summary ---")
        logger.info(f"Successfully processed/updated cache for: {processed_count} stores")
        logger.info(f"Failed to process/update cache for: {failed_count} stores")
        logger.info(f"Total products cached across all stores: {total_active_products}")
        logger.info(f"Total products using dynamic pricing: {total_dynamic_pricing_products}")
        logger.info(f"Stores with shipping revenue data: {stores_with_shipping_info}")
        logger.info(f"Stores with discount data: {stores_with_discount_info}")
        logger.info("Script finished.")

# --- Execution Guard ---
if __name__ == "__main__":
    main()