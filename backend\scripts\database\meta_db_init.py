from pymongo import MongoClient, ASCENDING, DESCENDING
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
import logging
import random
import uuid
from typing import List, Dict, Any

# Configure logging (REMOVED basicConfig)
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get MongoDB connection string from environment variables
MONGODB_CONNECTION = os.getenv("MONGODB_CONNECTION")
MONGODB_ANALYSIS_DB = os.getenv("MONGODB_ANALYSIS_DB", "D-Unit-AnalysisGPT")

# Control mock data creation via environment variable
ENABLE_MOCK_DATA = os.getenv("ENABLE_META_MOCK_DATA", "false").lower() == "true"

# Hardcoded store IDs to create mock data for (only used if ENABLE_MOCK_DATA=true)
STORE_IDS = ["961", "280", "1018", "895"]

def create_meta_collections(db):
    """Create MongoDB collections for Meta integration"""
    try:
        logger.info(f"Connected to MongoDB database: {MONGODB_ANALYSIS_DB}")
        
        # Define collections and their indexes
        collections_config = {
            "meta_pages": [
                [("id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("platform", ASCENDING)],
                [("created_at", DESCENDING)]
            ],
            "meta_posts": [
                [("id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("created_time", DESCENDING)],
                [("type", ASCENDING)],
                [("platform", ASCENDING)]
            ],
            "meta_post_metrics": [
                [("post_id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("date", DESCENDING)]
            ],
            "meta_comments": [
                [("id", ASCENDING)],
                [("post_id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("created_time", DESCENDING)]
            ],
            "meta_followers": [
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("date", DESCENDING)]
            ],
            "meta_demographics": [
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("date", DESCENDING)]
            ],
            "meta_ad_campaigns": [
                [("id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("status", ASCENDING)],
                [("start_time", DESCENDING)]
            ],
            "meta_ad_metrics": [
                [("campaign_id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("date", DESCENDING)]
            ],
            "meta_insights": [
                [("id", ASCENDING)],
                [("store_id", ASCENDING)],
                [("page_id", ASCENDING)],
                [("insight_type", ASCENDING)],
                [("source_data_type", ASCENDING)],
                [("source_data_id", ASCENDING)],
                [("timestamp", DESCENDING)],
                [("is_mock", ASCENDING)]
            ]
        }
        
        # Create collections and indexes
        for collection_name, indexes in collections_config.items():
            # Check if collection exists
            if collection_name not in db.list_collection_names():
                logger.info(f"Creating collection: {collection_name}")
                db.create_collection(collection_name)
            else:
                logger.info(f"Collection {collection_name} already exists")
            
            # Create indexes
            collection = db[collection_name]
            existing_indexes = collection.list_indexes()
            existing_index_keys = [
                tuple((key, value) for key, value in idx['key'].items())
                for idx in existing_indexes
            ]

            for index in indexes:
                index_tuple = tuple(index)
                if index_tuple not in existing_index_keys:
                    index_name = "_".join([f"{field}_{direction}" for field, direction in index])
                    logger.info(f"Creating index {index_name} on {collection_name}")
                    try:
                        collection.create_index(index, background=True)
                    except Exception as idx_error:
                        logger.warning(f"Could not create index {index_name} on {collection_name}: {str(idx_error)}")
                else:
                    logger.info(f"Index {index} already exists on {collection_name}")
        
        # Update global_analysis collection to support meta_integration field
        if "global_analysis" in db.list_collection_names():
            collection = db["global_analysis"]
            existing_indexes = collection.list_indexes()
            existing_index_keys = [
                tuple((key, value) for key, value in idx['key'].items())
                for idx in existing_indexes
            ]

            meta_integration_indexes = [
                [("meta_integration.connected", ASCENDING)],
                [("meta_integration.business_id", ASCENDING)],
                [("meta_integration.last_sync", DESCENDING)]
            ]

            for index in meta_integration_indexes:
                index_tuple = tuple(index)
                if index_tuple not in existing_index_keys:
                    index_name = "_".join([f"{field.replace('.', '_')}_{direction}" for field, direction in index])
                    logger.info(f"Creating index {index_name} on global_analysis")
                    try:
                        collection.create_index(index, background=True)
                    except Exception as idx_error:
                        logger.warning(f"Could not create index {index_name} on global_analysis: {str(idx_error)}")
                else:
                    logger.info(f"Index {index} already exists on global_analysis")
        
        logger.info("Meta collections and indexes setup completed successfully!")
        
    except Exception as e:
        logger.error(f"Error creating meta collections: {str(e)}")

def initialize_mock_data_for_store(store_id):
    """Initialize all Meta collections with mock data for a specific store"""
    try:
        # Connect to MongoDB
        client = MongoClient(MONGODB_CONNECTION)
        db = client[MONGODB_ANALYSIS_DB]
        
        # Current timestamp
        now = datetime.utcnow()
        
        logger.info(f"Initializing mock data for store ID: {store_id}")
        
        # Create mock Facebook and Instagram pages
        facebook_page = {
            "id": f"fb_page_{store_id}",
            "name": f"Store {store_id} Facebook Page",
            "access_token": f"mock_token_{store_id}_fb",
            "category": "Shopping & Retail",
            "platform": "facebook",
            "store_id": str(store_id),
            "followers_count": random.randint(500, 5000),
            "profile_picture_url": f"https://graph.facebook.com/fb_page_{store_id}/picture?type=square&width=150",
            "created_at": now,
            "last_sync": now,
            "is_mock": True
        }
        
        instagram_page = {
            "id": f"ig_page_{store_id}",
            "name": f"Store {store_id} Instagram",
            "access_token": f"mock_token_{store_id}_ig",
            "category": "Business",
            "platform": "instagram",
            "store_id": str(store_id),
            "followers_count": random.randint(300, 3000),
            "profile_picture_url": f"https://graph.facebook.com/ig_page_{store_id}/picture?type=square&width=150",
            "created_at": now,
            "last_sync": now,
            "is_mock": True
        }
        
        # Insert pages
        db.meta_pages.insert_many([facebook_page, instagram_page])
        logger.info(f"Created mock Facebook and Instagram pages for store {store_id}")
        
        # Create mock posts for both platforms
        posts = []
        post_ids = []
        
        # Facebook posts
        for i in range(5):
            post_id = f"fb_post_{i}_{store_id}"
            post_ids.append(post_id)
            posts.append({
                "id": post_id,
                "page_id": facebook_page["id"],
                "store_id": str(store_id),
                "message": f"Check out our latest products! #{i} #shopping #deals",
                "created_time": now - timedelta(days=i*3),
                "permalink_url": f"https://facebook.com/{facebook_page['id']}/posts/{post_id}",
                "type": random.choice(["photo", "video", "status", "link"]),
            "platform": "facebook",
            "attachments": [
                {
                    "media_type": "image",
                        "url": f"https://picsum.photos/600/400?random={store_id}_{i}_fb"
                    }
                ],
                "is_mock": True
            })
        
        # Instagram posts
        for i in range(5):
            post_id = f"ig_post_{i}_{store_id}"
            post_ids.append(post_id)
            posts.append({
                "id": post_id,
                "page_id": instagram_page["id"],
                "store_id": str(store_id),
                "message": f"New arrivals just dropped! ✨ #{i} #fashion #style",
                "created_time": now - timedelta(days=i*2),
                "permalink_url": f"https://instagram.com/p/{post_id}",
                "type": random.choice(["photo", "video", "carousel_album"]),
                "platform": "instagram",
                "attachments": [
                    {
                        "media_type": "image",
                        "url": f"https://picsum.photos/1080/1080?random={store_id}_{i}_ig"
                    }
                ],
                "is_mock": True
            })
        
        # Insert posts
        db.meta_posts.insert_many(posts)
        logger.info(f"Created {len(posts)} mock posts for store {store_id}")
        
        # Create mock post metrics
        post_metrics = []
        for post in posts:
            # Generate different engagement levels based on platform
            if post["platform"] == "facebook":
                likes = random.randint(10, 200)
                comments = random.randint(0, 50)
                shares = random.randint(0, 30)
                impressions = random.randint(200, 2000)
                reach = random.randint(100, impressions)
                saved = 0  # Not applicable for Facebook
            else:  # Instagram
                likes = random.randint(20, 300)
                comments = random.randint(0, 70)
                shares = 0  # Not directly trackable on Instagram
                impressions = random.randint(300, 3000)
                reach = random.randint(150, impressions)
                saved = random.randint(5, 100)
            
            # Calculate engagement rate
            engagement_rate = ((likes + comments + (shares if shares else 0)) / reach) * 100 if reach > 0 else 0
            
            post_metrics.append({
                "post_id": post["id"],
                "page_id": post["page_id"],
                "store_id": str(store_id),
                "likes": likes,
                "comments": comments,
                "shares": shares,
                "impressions": impressions,
                "reach": reach,
                "engagement_rate": round(engagement_rate, 2),
                "saved": saved,
                "date": post["created_time"],
                "is_mock": True
            })
        
        # Insert post metrics
        db.meta_post_metrics.insert_many(post_metrics)
        logger.info(f"Created {len(post_metrics)} mock post metrics for store {store_id}")
        
        # Create mock comments
        comments = []
        for post in posts:
            # Generate 0-5 comments per post
            num_comments = random.randint(0, 5)
            for i in range(num_comments):
                comments.append({
                    "id": f"comment_{uuid.uuid4().hex[:12]}",
                    "post_id": post["id"],
                    "page_id": post["page_id"],
                    "store_id": str(store_id),
                    "message": random.choice([
                        "Love this! 😍",
                        "When will you have more in stock?",
                        "Just ordered mine!",
                        "Great prices!",
                        "Do you ship internationally?",
                        "This looks amazing!",
                        "Can't wait to try this",
                        "What are your store hours?",
                        "Is this available in other colors?",
                        "Just what I was looking for!"
                    ]),
                    "created_time": post["created_time"] + timedelta(hours=random.randint(1, 24)),
                    "from": {
                        "id": f"user_{random.randint(1000, 9999)}",
                        "name": random.choice(["John Smith", "Maria Garcia", "Emma Johnson", "James Williams", "Sofia Martinez"])
                    },
                    "sentiment_score": random.uniform(0, 1),
                    "is_mock": True
                })
        
        # Insert comments
        if comments:
            db.meta_comments.insert_many(comments)
            logger.info(f"Created {len(comments)} mock comments for store {store_id}")
        
        # Create mock follower data (30 days)
        follower_data = []
        for page in [facebook_page, instagram_page]:
            # Starting follower count
            base_followers = page["followers_count"] - random.randint(50, 200)
            
            # Daily changes
            net_follows = []
            current_followers = base_followers
            
            for i in range(30):
                day = now - timedelta(days=29-i)
                # More growth on some days, losses on others
                change = random.randint(-5, 20)
                current_followers += change
                
                net_follows.append({
                    "date": day,
                    "value": change
                })
            
            # Calculate growth rate
            growth_rate = ((current_followers - base_followers) / base_followers) * 100 if base_followers > 0 else 0
            
            follower_data.append({
                "page_id": page["id"],
                "store_id": str(store_id),
                "total": current_followers,
            "date": now,
                "net_follows": net_follows,
                "growth_rate": round(growth_rate, 2),
                "is_mock": True
            })
        
        # Insert follower data
        db.meta_followers.insert_many(follower_data)
        logger.info(f"Created follower growth data for store {store_id}")
        
        # Create mock demographics
        demographics = []
        for page in [facebook_page, instagram_page]:
            # Slightly different demographics for Facebook vs Instagram
            if page["platform"] == "facebook":
                age_ranges = [
                    {"range": "18-24", "percentage": 0.15},
                    {"range": "25-34", "percentage": 0.25},
                    {"range": "35-44", "percentage": 0.30},
                    {"range": "45-54", "percentage": 0.20},
                    {"range": "55+", "percentage": 0.10}
                ]
                gender = [
                    {"type": "female", "percentage": 0.60},
                    {"type": "male", "percentage": 0.40}
                ]
            else:  # Instagram
                age_ranges = [
                    {"range": "18-24", "percentage": 0.35},
                    {"range": "25-34", "percentage": 0.40},
                    {"range": "35-44", "percentage": 0.15},
                    {"range": "45-54", "percentage": 0.07},
                    {"range": "55+", "percentage": 0.03}
                ]
                gender = [
                    {"type": "female", "percentage": 0.70},
                    {"type": "male", "percentage": 0.30}
                ]
            
            # Common locations (modify based on your needs)
            top_locations = [
                {"country": "US", "city": "New York", "percentage": 0.20},
                {"country": "US", "city": "Los Angeles", "percentage": 0.15},
                {"country": "UK", "city": "London", "percentage": 0.10},
                {"country": "CA", "city": "Toronto", "percentage": 0.08},
                {"country": "AU", "city": "Sydney", "percentage": 0.05}
            ]
            
            demographics.append({
                "page_id": page["id"],
                "store_id": str(store_id),
                "age_ranges": age_ranges,
                "gender": gender,
                "top_locations": top_locations,
                "date": now,
                "is_mock": True
            })
        
        # Insert demographics
        db.meta_demographics.insert_many(demographics)
        logger.info(f"Created demographic data for store {store_id}")
        
        # Create mock insights
        insights = []
        
        # Content insights
        insights.append({
            "id": f"insight_content_{uuid.uuid4().hex[:8]}",
            "page_id": facebook_page["id"],
            "store_id": str(store_id),
            "insight_type": "content_analysis",
            "insight_text": "Posts with product images receive 45% higher engagement than text-only posts.",
            "title": "Product Images Boost Engagement",
            "confidence_score": 0.85,
            "recommendations": [
                "Include high-quality product images in all posts",
                "Use carousel posts to showcase multiple products",
                "Add product tags to increase click-through rates"
            ],
            "timestamp": now,
            "source_data_type": "posts",
            "source_data_id": post_ids[0],
            "post_ids": post_ids[:3],
            "topics": ["product", "promotion", "engagement"],
            "sentiment_score": 0.75,
            "is_mock": True
        })
        
        # Engagement insights
        insights.append({
            "id": f"insight_engagement_{uuid.uuid4().hex[:8]}",
            "page_id": instagram_page["id"],
            "store_id": str(store_id),
            "insight_type": "engagement_analysis",
            "insight_text": "Your engagement rate of 3.2% is above the industry average of 2.1% for retail businesses.",
            "title": "Above Average Engagement",
            "confidence_score": 0.92,
            "recommendations": [
                "Maintain posting frequency of 3-4 times per week",
                "Continue using relevant hashtags to expand reach",
                "Respond to comments within 24 hours to boost relationship with followers"
            ],
            "timestamp": now,
            "source_data_type": "metrics",
            "source_data_id": "engagement_metrics",
            "is_mock": True
        })
        
        # Audience insights
        insights.append({
            "id": f"insight_audience_{uuid.uuid4().hex[:8]}",
            "page_id": facebook_page["id"],
            "store_id": str(store_id),
            "insight_type": "audience_analysis",
            "insight_text": "Your primary audience segment is women ages 25-34 in urban areas, with interests in fashion and lifestyle content.",
            "title": "Primary Audience Segment",
            "confidence_score": 0.88,
            "recommendations": [
                "Create content specifically targeting female urban professionals",
                "Schedule posts between 7-9pm when your audience is most active",
                "Consider partnerships with lifestyle influencers relevant to your audience"
            ],
            "timestamp": now,
            "source_data_type": "demographics",
            "source_data_id": "audience_demographics",
            "is_mock": True
        })
        
        # Correlation insights
        insights.append({
            "id": f"insight_correlation_{uuid.uuid4().hex[:8]}",
            "page_id": facebook_page["id"],
            "store_id": str(store_id),
            "insight_type": "correlation",
            "insight_text": "We've identified a strong correlation (0.78) between promotional posts and website traffic spikes within 48 hours.",
            "title": "Promotions Drive Traffic",
            "confidence_score": 0.81,
            "recommendations": [
                "Schedule promotional posts 2-3 days before key sales events",
                "Use Facebook ads to amplify your best-performing promotional content",
                "Include clear CTAs in promotional posts to maximize conversion"
            ],
            "timestamp": now,
            "source_data_type": "correlation",
            "source_data_id": "traffic_correlation",
            "correlation_score": 0.78,
            "is_mock": True
        })
        
        # Insert insights
        db.meta_insights.insert_many(insights)
        logger.info(f"Created {len(insights)} mock insights for store {store_id}")
        
        # Create mock ad campaigns
        campaigns = []
        for page in [facebook_page, instagram_page]:
            campaign_id = f"campaign_{uuid.uuid4().hex[:8]}"
            campaigns.append({
                "id": campaign_id,
                "page_id": page["id"],
                "store_id": str(store_id),
                "name": f"Summer Collection Promotion - {page['platform'].capitalize()}",
                "status": "ACTIVE",
                "objective": "CONVERSIONS",
                "start_time": now - timedelta(days=15),
                "end_time": now + timedelta(days=15),
                "budget": random.uniform(300, 1000),
                "conversion_rate": random.uniform(0.02, 0.08),
                "correlation_metric": random.uniform(0.5, 0.9),
                "correlation_data": [
                    {
                        "date": now - timedelta(days=i),
                        "conversion_rate": random.uniform(0.01, 0.1),
                        "sales_value": random.uniform(500, 2000)
                    } for i in range(15)
                ],
                "is_mock": True
            })
            
            # Create mock ad metrics for this campaign
            ad_metrics = []
            for i in range(15):
                day = now - timedelta(days=14-i)
                # Generate consistent but slightly varied metrics
                impressions = random.randint(1000, 5000)
                reach = int(impressions * random.uniform(0.6, 0.8))
                clicks = int(impressions * random.uniform(0.02, 0.06))
                ctr = (clicks / impressions) if impressions > 0 else 0
                cpc = random.uniform(0.5, 1.5)
                spend = clicks * cpc
                conversions = int(clicks * random.uniform(0.05, 0.15))
                cost_per_conversion = spend / conversions if conversions > 0 else 0
                roi = random.uniform(1.2, 4.0)
                
                ad_metrics.append({
                    "campaign_id": campaign_id,
                    "page_id": page["id"],
                    "store_id": str(store_id),
                    "date": day,
                    "impressions": impressions,
                    "reach": reach,
                    "clicks": clicks,
                    "ctr": round(ctr, 4),
                    "cpc": round(cpc, 2),
                    "spend": round(spend, 2),
                    "conversions": conversions,
                    "cost_per_conversion": round(cost_per_conversion, 2),
                    "roi": round(roi, 2),
                    "is_mock": True
                })
            
            # Insert ad metrics
            db.meta_ad_metrics.insert_many(ad_metrics)
            logger.info(f"Created {len(ad_metrics)} mock ad metrics for campaign {campaign_id}")
        
        # Insert campaigns
        db.meta_ad_campaigns.insert_many(campaigns)
        logger.info(f"Created {len(campaigns)} mock campaigns for store {store_id}")
        
        # Update global_analysis with meta_integration
        meta_integration_data = {
            "connected": True,
            "business_id": f"business_{store_id}",
            "pages": [
                {
                    "id": facebook_page["id"],
                    "name": facebook_page["name"],
                    "access_token": facebook_page["access_token"],
                    "category": facebook_page["category"],
                    "platform": facebook_page["platform"]
                },
                {
                    "id": instagram_page["id"],
                    "name": instagram_page["name"],
                    "access_token": instagram_page["access_token"],
                    "category": instagram_page["category"],
                    "platform": instagram_page["platform"]
                }
            ],
            "last_sync": now,
            "insights": {
                "page_views": random.randint(500, 5000),
                "page_likes": facebook_page["followers_count"],
                "post_engagement": random.randint(100, 1000),
                "reach": random.randint(1000, 10000)
            }
        }
        
        # Check if global_analysis document exists for this store
        existing_doc = db.global_analysis.find_one({"_id": str(store_id)})
        
        if existing_doc:
            # Update existing document
            db.global_analysis.update_one(
                {"_id": str(store_id)},
                {"$set": {"meta_integration": meta_integration_data}}
            )
        else:
            # Create new document
            db.global_analysis.insert_one({
                "_id": str(store_id),
                "meta_integration": meta_integration_data,
                "store": {
                    "id_store": int(store_id) if store_id.isdigit() else store_id,
                    "name": f"Store {store_id}",
                },
                "analysis": {},
                "metadata": {},
                "metrics": {},
                "order_statistics": {},
                "visits": {},
                "social_media": {},
                "coupons": {}
            })
        
        logger.info(f"Updated global_analysis document for store {store_id} with meta_integration data")
        
        return {
            "facebook_page_id": facebook_page["id"],
            "instagram_page_id": instagram_page["id"]
        }
        
    except Exception as e:
        logger.error(f"Error initializing mock data for store {store_id}: {str(e)}")
        return None
    finally:
        if 'client' in locals():
            client.close()

async def print_collection_statistics(db):
    """Prints basic statistics for relevant collections using logger."""
    collections_to_check = [
        "meta_pages",
        "meta_posts",
        "meta_post_metrics",
        "meta_comments",
        "meta_followers",
        "meta_demographics",
        "meta_ad_campaigns",
        "meta_ad_metrics",
        "meta_insights"
    ]
    
    logger.debug("--- Collection Statistics ---")
    
    for collection_name in collections_to_check:
        collection = db[collection_name]
        try:
            total_docs = await collection.count_documents({})
            mock_docs = await collection.count_documents({"is_mock": True})
            store_ids = await collection.distinct("store_id")
            
            sample = await collection.find_one({}, {"_id": 0}) # Get a sample doc
            
            logger.debug(f"{collection_name}: Total={total_docs}, Mock={mock_docs}, Stores={len(store_ids)}")
            if sample:
                logger.debug(f"{collection_name} sample fields: {list(sample.keys())}")
        except Exception as e:
            logger.error(f"Error getting stats for collection '{collection_name}': {e}")

async def main():
    """Main function to initialize mock data"""
    try:
        # Connect to MongoDB
        client = MongoClient(MONGODB_CONNECTION)
        db = client[MONGODB_ANALYSIS_DB]
        
        # Create collections and set up indexes
        create_meta_collections(db)
        
        # Initialize mock data for each hardcoded store ID (only if enabled)
        if ENABLE_MOCK_DATA:
            logger.info("Mock data creation is ENABLED. Creating mock data for test stores...")
            for store_id in STORE_IDS:
                result = initialize_mock_data_for_store(store_id)
                if result:
                    logger.info(f"Successfully initialized mock data for store ID: {store_id}")
                else:
                    logger.error(f"Failed to initialize mock data for store ID: {store_id}")
        else:
            logger.info("Mock data creation is DISABLED (ENABLE_META_MOCK_DATA=false). Skipping mock data creation.")
            logger.info("Collections and indexes have been created for real Meta data only.")
        
        # Print collection statistics
        await print_collection_statistics(db)
        
        client.close()
        logger.info("Mock data initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        raise

if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 