import { fetchWithDeduplication } from './apiService';
import { csrfService } from './csrfService';
import { logger } from '../utils/logger';

export interface FormSubmissionResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  csrfRetried?: boolean;
  message?: string;
  access_token?: string;
}

export interface FormSubmissionConfig {
  url: string;
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data?: Record<string, unknown>;
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  validateBeforeSubmit?: boolean;
}

export interface FormValidationRule {
  field: string;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: unknown) => string | null;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  fieldErrors: Record<string, string>;
}

/**
 * Centralized form service for consistent form submission across the application
 * Handles CSRF tokens, validation, error recovery, and loading states
 */
class FormService {
  /**
   * Submit a form with automatic CSRF handling and error recovery
   */
  async submitForm<T = unknown>(config: FormSubmissionConfig): Promise<FormSubmissionResult<T>> {
    const {      url,      method,      data,      headers = {}    } = config;

    // Endpoints that don't require a CSRF token because they are publicly accessible
    const csrfExempt = [
      '/api/auth/token',
      '/api/auth/dunit/register',
      '/api/auth/dunit/verify-registration',
      '/api/auth/forgot-password',
      '/api/auth/password-reset/request',
      '/api/auth/password-reset/verify',
      '/api/auth/2fa/verify-login'
    ];

    logger.debug(`FormService: Submitting form to ${url} with method ${method}`);

    try {
      // Ensure CSRF token is fresh
      if (!csrfExempt.includes(url)) {
        await this.ensureFreshCsrfToken();
      }

      const requestHeaders = {
        'Content-Type': 'application/json',
        ...headers
      };

      // Submit with retry logic handled by fetchWithDeduplication
      const response = await fetchWithDeduplication<T>(url, {
        method,
        headers: requestHeaders,
        body: data ? JSON.stringify(data) : undefined,
      });

      logger.info(`FormService: Form submission successful to ${url}`);
      
      // Safely extract message and access_token if they exist in the response
      const responseData = response;
      const message = (responseData && typeof responseData === 'object' && 'message' in responseData) 
        ? (responseData as { message: unknown }).message as string | undefined
        : undefined;
      const accessToken = (responseData && typeof responseData === 'object' && 'access_token' in responseData)
        ? (responseData as { access_token: unknown }).access_token as string | undefined
        : undefined;
      
      return {
        success: true,
        data: response,
        message,
        access_token: accessToken
      };
    } catch (error: unknown) {
      logger.error(`FormService: Form submission failed to ${url}:`, error);

      const errorMessage = this.extractErrorMessage(error);
      
      return {
        success: false,
        error: errorMessage,
        csrfRetried: (error && typeof error === 'object' && 'csrfRetried' in error) 
          ? Boolean((error as { csrfRetried: unknown }).csrfRetried)
          : false
      };
    }
  }

  /**
   * Validate form data against provided rules
   */
  validateForm(data: Record<string, unknown>, rules: FormValidationRule[]): FormValidationResult {
    const errors: Record<string, string> = {};
    const fieldErrors: Record<string, string> = {};

    for (const rule of rules) {
      const value = data[rule.field];
      const fieldError = this.validateField(value, rule);
      
      if (fieldError) {
        errors[rule.field] = fieldError;
        fieldErrors[rule.field] = fieldError;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      fieldErrors
    };
  }

  /**
   * Submit form with validation
   */
  async submitFormWithValidation<T = unknown>(
    config: FormSubmissionConfig,
    validationRules: FormValidationRule[]
  ): Promise<FormSubmissionResult<T>> {
    if (config.validateBeforeSubmit !== false && config.data) {
      const validation = this.validateForm(config.data, validationRules);
      
      if (!validation.isValid) {
        const errorMessages = Object.values(validation.errors).filter(Boolean);
        return {
          success: false,
          error: errorMessages.join(', ') || 'Form validation failed'
        };
      }
    }

    return await this.submitForm<T>(config);
  }

  /**
   * Sanitize form data to prevent XSS and other security issues
   */
  sanitizeFormData(data: Record<string, unknown>): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // Basic sanitization - remove script tags and normalize whitespace
        sanitized[key] = value
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .trim();
      } else if (value !== null && value !== undefined) {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Create a form submission config for common patterns
   */
  createConfig(
    url: string,
    method: 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    data?: Record<string, unknown>
  ): FormSubmissionConfig {
    return {
      url,
      method,
      data: data ? this.sanitizeFormData(data) : undefined
    };
  }

  /**
   * Batch submit multiple forms (useful for complex forms with multiple sections)
   */
  async submitMultipleForms<T = unknown>(
    configs: FormSubmissionConfig[]
  ): Promise<FormSubmissionResult<T>[]> {
    const results = await Promise.allSettled(
      configs.map(config => this.submitForm<T>(config))
    );

    return results.map(result => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          error: result.reason?.message || 'Form submission failed'
        };
      }
    });
  }

  /**
   * Ensure CSRF token is fresh before form submission
   */
  private async ensureFreshCsrfToken(): Promise<void> {
    try {
      if (!csrfService.hasValidToken()) {
        logger.debug('FormService: Refreshing CSRF token before form submission');
        await csrfService.getToken();
      }
    } catch (error) {
      logger.warn('FormService: Failed to ensure fresh CSRF token:', error);
      // Continue without fresh token - fetchWithDeduplication will handle retry
    }
  }

  /**
   * Validate a single field against its rule
   */
  private validateField(value: unknown, rule: FormValidationRule): string | null {
    // Required validation
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return `${rule.field} is required`;
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && !value.trim())) {
      return null;
    }

    const stringValue = String(value);

    // Length validations
    if (rule.minLength && stringValue.length < rule.minLength) {
      return `${rule.field} must be at least ${rule.minLength} characters`;
    }

    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return `${rule.field} must not exceed ${rule.maxLength} characters`;
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return `${rule.field} format is invalid`;
    }

    // Custom validation
    if (rule.customValidator) {
      return rule.customValidator(value);
    }

    return null;
  }

  /**
   * Extract user-friendly error message from error object
   */
  private extractErrorMessage(error: unknown): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object' && 'response' in error) {
      const errorWithResponse = error as { response: { data?: unknown } };
      const data = errorWithResponse.response.data;
      
      if (data && typeof data === 'object') {
        if ('detail' in data && typeof (data as { detail: unknown }).detail === 'string') {
          return (data as { detail: string }).detail;
        }
        if ('message' in data && typeof (data as { message: unknown }).message === 'string') {
          return (data as { message: string }).message;
        }
        if ('error' in data && typeof (data as { error: unknown }).error === 'string') {
          return (data as { error: string }).error;
        }
      }
    }

    if (error && typeof error === 'object' && 'message' in error) {
      const errorWithMessage = error as { message: unknown };
      if (typeof errorWithMessage.message === 'string') {
        return errorWithMessage.message;
      }
    }

    return 'An unexpected error occurred';
  }
}

// Export a singleton instance
export const formService = new FormService();
export default formService; 