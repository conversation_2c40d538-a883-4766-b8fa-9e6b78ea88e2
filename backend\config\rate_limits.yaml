# Rate Limiting Configuration for D-Unit API
# This file defines rate limiting policies for different user types and endpoints

global_defaults:
  # Rate limits for anonymous (unauthenticated) users
  anonymous_users:
    requests_per_minute: 10
    requests_per_hour: 100
    requests_per_day: 500
    burst_allowance: 5
    window_size: 60

  # Rate limits for authenticated users
  authenticated_users:
    requests_per_minute: 60
    requests_per_hour: 1000
    requests_per_day: 10000
    burst_allowance: 20
    window_size: 60

  # Rate limits for admin users
  admin_users:
    requests_per_minute: 200
    requests_per_hour: 5000
    requests_per_day: 50000
    burst_allowance: 50
    window_size: 60

  # Rate limits for premium users (if implemented)
  premium_users:
    requests_per_minute: 120
    requests_per_hour: 2000
    requests_per_day: 20000
    burst_allowance: 30
    window_size: 60

# Endpoint-specific rate limits (override global defaults)
endpoint_specific:
  # Authentication endpoints - stricter limits to prevent brute force
  "/api/auth/token":
    requests_per_minute: 5
    requests_per_hour: 30
    requests_per_day: 100
    burst_allowance: 2
    cost_weight: 1.0
    rate_limit_on_failure: true

  "/api/auth/2fa/verify-login":
    requests_per_minute: 3
    requests_per_hour: 20
    requests_per_day: 50
    burst_allowance: 1
    cost_weight: 1.0

  "/api/auth/forgot-password":
    requests_per_minute: 2
    requests_per_hour: 10
    requests_per_day: 20
    burst_allowance: 1
    cost_weight: 1.0

  # Chat endpoints - AI calls are expensive
  "/api/chat/**":
    requests_per_minute: 30
    requests_per_hour: 300
    requests_per_day: 1000
    burst_allowance: 5
    cost_weight: 3.0
    external_api_calls: true

  # Meta API endpoints - external API limits
  "/api/meta/**":
    requests_per_minute: 20
    requests_per_hour: 500
    requests_per_day: 2000
    burst_allowance: 10
    cost_weight: 2.0
    external_api_calls: true

  # Meta direct endpoints - cached data, more lenient
  "/api/meta/direct-metrics":
    requests_per_minute: 40
    requests_per_hour: 800
    requests_per_day: 3000
    burst_allowance: 15
    cost_weight: 1.5

  # Store endpoints
  "/api/store/**":
    requests_per_minute: 50
    requests_per_hour: 1000
    requests_per_day: 5000
    burst_allowance: 20
    cost_weight: 1.0

  # Insights endpoints - heavy database operations
  "/api/insights/**":
    requests_per_minute: 15
    requests_per_hour: 200
    requests_per_day: 800
    burst_allowance: 5
    cost_weight: 2.5
    external_api_calls: true

  # Admin endpoints - higher limits but monitoring
  "/api/admin/**":
    requests_per_minute: 100
    requests_per_hour: 2000
    requests_per_day: 10000
    burst_allowance: 30
    cost_weight: 1.0
    admin_only: true

  # Health check - unlimited
  "/health":
    requests_per_minute: 1000
    requests_per_hour: 60000
    requests_per_day: 1000000
    burst_allowance: 100
    cost_weight: 0.1

  # Root endpoint - moderate limits
  "/":
    requests_per_minute: 30
    requests_per_hour: 300
    requests_per_day: 1000
    burst_allowance: 10
    cost_weight: 0.5

# Time-based rate limit variations
time_based_limits:
  # Business hours (9 AM - 6 PM UTC) - higher limits
  business_hours:
    start_hour: 9
    end_hour: 18
    multiplier: 1.5

  # Off-hours - lower limits to save resources
  off_hours:
    start_hour: 22
    end_hour: 6
    multiplier: 0.7

  # Weekend - moderate limits
  weekend:
    multiplier: 0.8

# Geographic-based limits (if needed)
geographic_limits:
  # Default for all regions
  default:
    multiplier: 1.0

  # Higher limits for primary regions
  primary_regions:
    regions:
      - "SA"  # South America
      - "US"  # United States
    multiplier: 1.2

  # Lower limits for high-risk regions
  restricted_regions:
    regions:
      - "CN"  # China
      - "RU"  # Russia
    multiplier: 0.5

# Store-tier based limits
store_tier_limits:
  # Free tier stores
  free:
    requests_per_minute: 30
    requests_per_hour: 500
    requests_per_day: 2000
    cost_weight_multiplier: 1.0

  # Basic tier stores
  basic:
    requests_per_minute: 60
    requests_per_hour: 1000
    requests_per_day: 5000
    cost_weight_multiplier: 0.8

  # Premium tier stores
  premium:
    requests_per_minute: 120
    requests_per_hour: 2000
    requests_per_day: 10000
    cost_weight_multiplier: 0.6

  # Enterprise tier stores
  enterprise:
    requests_per_minute: 200
    requests_per_hour: 5000
    requests_per_day: 25000
    cost_weight_multiplier: 0.4

# Emergency limits - activated during system stress
emergency_limits:
  # When system load is high
  high_load:
    multiplier: 0.3
    trigger_cpu_threshold: 80
    trigger_memory_threshold: 85

  # When external API quotas are being exceeded
  api_quota_protection:
    multiplier: 0.1
    openai_quota_threshold: 0.9
    meta_quota_threshold: 0.9

  # DDoS protection
  ddos_protection:
    requests_per_minute: 5
    requests_per_hour: 50
    requests_per_day: 200
    trigger_request_spike: 10  # 10x normal traffic

# Rate limit bypass conditions
bypass_conditions:
  # Internal health checks
  health_checks:
    user_agents:
      - "HealthCheck"
      - "AWS-ELB-HealthChecker"
    paths:
      - "/health"
      - "/api/health"

  # Trusted IP addresses (load balancers, CDN)
  trusted_ips:
    - "127.0.0.1"
    - "::1"
    # Add AWS ELB IP ranges as needed

  # Service accounts
  service_accounts:
    - "<EMAIL>"
    - "<EMAIL>" 