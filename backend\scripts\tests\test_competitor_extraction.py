#!/usr/bin/env python3
"""
Test script to demonstrate the improved competitor extraction logic
"""

import re

def extract_competitor_names_improved(search_results: str, country: str) -> list:
    """Improved competitor extraction function"""
    print(f"Starting competitor extraction for {country}. Search results length: {len(search_results)}")
    
    # Known optical store chains to look for specifically
    known_optical_chains = {
        'Uruguay': ['grandvision', 'optica americana', 'óptica florida', 'mytho eyewear', 'opticentro', 'vision center', 'optica del sol'],
        'Argentina': ['grandvision', 'optica pearle', 'óptica look', 'optica universitaria'],
        'Mexico': ['opticas lux', 'devlyn', 'ben&frank'],
        'Spain': ['multiópticas', 'alain afflelou', 'optica universitaria'],
        'Brazil': ['óticas carol', 'óticas diniz']
    }
    
    potential_brands = set()
    
    # First, look for known optical chains
    known_chains = known_optical_chains.get(country, [])
    for chain in known_chains:
        if chain.lower() in search_results.lower():
            potential_brands.add(chain.title())
            print(f"Found known optical chain: {chain}")
    
    # Enhanced patterns for brand names in search results
    brand_patterns = [
        # From page titles (most reliable)
        r'Title:\s*([A-Z][a-zA-Z0-9\s&\'-]+?)\s*(?:-|\||–|\.|,)',
        # From domain names
        r'Domain:\s*(?:www\.)?([a-zA-Z0-9\-]+)\.(?:com\.uy|uy|com)',
        # Store/company names mentioned directly
        r'(?:tienda|store|empresa|company|óptica|optica)\s+([A-Z][a-zA-Z0-9\s&\'-]+)',
        # Names followed by descriptive text
        r'([A-Z][a-zA-Z0-9\s&\'-]+)(?:\s+(?:óptica|optica|lentes|anteojos|eyewear|vision))',
        # Business listings format
        r'([A-Z][a-zA-Z0-9\s&\'-]+)(?:\s+is\s+a?\s*(?:leading|top|major|popular))',
        # Directory listings
        r'(?:including|such as|like)\s+([A-Z][a-zA-Z0-9\s&\'-]+)',
    ]
    
    # Extract country-specific domain patterns
    country_domains = {
        'Uruguay': ['.com.uy', '.uy'],
        'Argentina': ['.com.ar', '.ar'],
        'Mexico': ['.com.mx', '.mx'],
        'Spain': ['.es', '.com.es'],
        'Brazil': ['.com.br', '.br']
    }
    
    country_tlds = country_domains.get(country, [])
    print(f"Looking for domains with TLDs: {country_tlds}")
    
    # Extract from domain names (most reliable for local businesses)
    for tld in country_tlds:
        url_pattern = rf'https?://(?:www\.)?([a-zA-Z0-9\-]+)\{re.escape(tld)}'
        url_matches = re.findall(url_pattern, search_results, re.IGNORECASE)
        for domain in url_matches:
            if len(domain) > 2 and domain.lower() not in ['google', 'facebook', 'instagram', 'youtube', 'mercadolibre']:
                clean_name = domain.replace('-', ' ').title()
                potential_brands.add(clean_name)
                print(f"Extracted from domain: {clean_name} (from {domain}{tld})")
    
    # Apply patterns to extract brand names
    for pattern in brand_patterns:
        matches = re.findall(pattern, search_results, re.MULTILINE | re.IGNORECASE)
        for match in matches:
            # Clean up the match
            brand = match.strip()
            # Filter out common words and very short names
            excluded_words = ['the', 'and', 'for', 'with', 'best', 'top', 'online', 'store', 'shop', 'optical', 'optica', 'lentes', 'en', 'de', 'la', 'del']
            if len(brand) > 2 and brand.lower() not in excluded_words:
                # Additional validation: check if it looks like a business name
                if re.match(r'^[A-Z][a-zA-Z0-9\s&\'-]+$', brand) and len(brand.split()) <= 4:
                    potential_brands.add(brand)
                    print(f"Extracted from pattern: {brand}")
    
    # Filter out obvious non-competitors
    filtered_brands = []
    for brand in potential_brands:
        if any(word in brand.lower() for word in ['facebook', 'instagram', 'google', 'mercadolibre', 'amazon', 'ebay']):
            continue
        filtered_brands.append(brand)
    
    print(f"Final extracted competitors: {filtered_brands}")
    return filtered_brands[:20]


# Test with sample search results
sample_search_results = """
Title: Grandvision Uruguay - Lentes y Anteojos
Domain: grandvision.com.uy
Description: Grandvision es una cadena líder de ópticas en Uruguay. Ofrecemos lentes de sol, anteojos recetados y lentes de contacto.
URL: https://grandvision.com.uy/

Title: Óptica Americana - Tienda Online
Domain: opticaamericana.uy
Description: Óptica Americana es una empresa uruguaya especializada en lentes y anteojos. Fundada en Montevideo.
URL: https://opticaamericana.uy/

Title: Mytho Eyewear Uruguay - Diseño y Estilo
Domain: mythoeyewear.com.uy
Description: Mytho Eyewear ofrece anteojos de diseño en Uruguay. Tienda física en Montevideo y venta online.
URL: https://mythoeyewear.com.uy/

Title: Vision Center - Centro de Visión
Domain: visioncenter.uy
Description: Vision Center es un centro de visión en Uruguay que ofrece exámenes de la vista y venta de anteojos.
URL: https://visioncenter.uy/

Title: Falso Competidor Español
Domain: falsotienda.es
Description: Esta es una tienda falsa de España que no debería aparecer en resultados de Uruguay.
URL: https://falsotienda.es/
"""

print("=== TESTING IMPROVED COMPETITOR EXTRACTION ===")
print("Sample search results contain both real Uruguay competitors and a fake Spanish one.")
print("\nExpected: Should find Grandvision, Optica Americana, Mytho Eyewear, Vision Center")
print("Should NOT find the Spanish fake competitor\n")

extracted = extract_competitor_names_improved(sample_search_results, 'Uruguay')

print(f"\n=== RESULTS ===")
print(f"Extracted {len(extracted)} competitors: {extracted}")

if len(extracted) >= 4:
    print("✓ SUCCESS: Found multiple real competitors")
else:
    print("✗ ISSUE: Found fewer competitors than expected")

real_competitors = ['Grandvision', 'Optica Americana', 'Mytho Eyewear', 'Vision Center']
found_real = [comp for comp in extracted if any(real.lower() in comp.lower() for real in real_competitors)]
print(f"\nReal competitors found: {found_real}")

if 'Falso' not in str(extracted) and 'falsotienda' not in str(extracted).lower():
    print("✓ SUCCESS: Correctly filtered out foreign competitor")
else:
    print("✗ ISSUE: Foreign competitor was not filtered out")
