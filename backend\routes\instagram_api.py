"""
Instagram-specific API endpoints with proper data transformation
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from config.database import db_analysis
from models.user import User
from services.auth import get_current_active_user, verify_user_can_access_store
from services.instagram_utils import (
    InstagramAPIError,
    validate_instagram_permissions,
    get_instagram_business_account,
    retry_on_rate_limit,
    validate_instagram_token,
    get_instagram_media_insights,
    INSTAGRAM_PERMISSIONS
)
from services.instagram_transformer import InstagramDataTransformer
from services.instagram_cache import InstagramCacheService
import aiohttp

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/instagram", tags=["instagram"])

async def get_instagram_insights_user_level(page_id: str, access_token: str, time_range: str, store_id: str = None) -> Dict[str, Any]:
    """
    Get Instagram insights using User-level insights API v23.0 with period=day for proper daily metrics
    """
    try:
        logger.info(f"Getting Instagram insights from User-level API v23.0 for page {page_id}")
        
        # Try to refresh token if store_id is provided
        if store_id:
            from routes.auth import refresh_meta_token_if_needed
            refreshed_token = await refresh_meta_token_if_needed(store_id)
            if refreshed_token:
                access_token = refreshed_token
                logger.info(f"Using refreshed Meta token for store {store_id}")
        
        # Calculate date range (respect Meta API 28-day limit but optimize for each time range)
        until_date = datetime.now()
        if time_range == "7d":
            since_date = until_date - timedelta(days=7)
        elif time_range == "30d":
            # Use 28 days to respect API limit (close enough to 30)
            since_date = until_date - timedelta(days=28)
        elif time_range == "90d":
            # Use 28 days maximum (Instagram API limit)
            since_date = until_date - timedelta(days=28)
        elif time_range == "lifetime":
            # For lifetime, use 28 days maximum (Instagram doesn't support true lifetime)
            since_date = until_date - timedelta(days=28)
        else:
            since_date = until_date - timedelta(days=28)
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60.0)) as session:
            # Use User-level insights with period=day for proper daily metrics
            insights_url = f"https://graph.facebook.com/v23.0/{page_id}/insights"
            
            # Split metrics based on metric_type requirements (API v22+)
            # reach works with time_series, others need total_value
            time_series_metrics = ["reach"]
            total_value_metrics = ["views", "accounts_engaged", "total_interactions", "profile_links_taps"]
            
            total_views = 0
            total_reach = 0
            total_accounts_engaged = 0
            total_interactions = 0
            total_profile_views = 0
            total_profile_links_taps = 0
            daily_data = []
            
            # Fetch reach with time_series
            reach_params = {
                "metric": ",".join(time_series_metrics),
                "period": "day",
                "since": since_date.strftime('%Y-%m-%d'),
                "until": until_date.strftime('%Y-%m-%d'),
                "access_token": access_token
            }
            
            logger.info(f"Fetching reach metrics: {insights_url} with params: {reach_params}")
            
            async with session.get(insights_url, params=reach_params) as response:
                if response.status == 200:
                    data = await response.json()
                    insights = data.get("data", [])
                    
                    for insight in insights:
                        metric_name = insight.get("name")
                        values = insight.get("values", [])
                        metric_total = sum(v.get("value", 0) for v in values if v.get("value") is not None)
                        
                        if metric_name == "reach":
                            total_reach = metric_total
                        
                        # Store daily breakdown
                        for value_entry in values:
                            if value_entry.get("end_time"):
                                daily_data.append({
                                    "date": value_entry["end_time"][:10],
                                    "metric": metric_name,
                                    "value": value_entry.get("value", 0)
                                })
                else:
                    logger.error(f"Reach metrics failed with status {response.status}")
                    error_text = await response.text()
                    logger.error(f"Error response: {error_text}")
            
            # Fetch other metrics with total_value
            total_value_params = {
                "metric": ",".join(total_value_metrics),
                "period": "day",
                "metric_type": "total_value",
                "since": since_date.strftime('%Y-%m-%d'),
                "until": until_date.strftime('%Y-%m-%d'),
                "access_token": access_token
            }
            
            logger.info(f"Fetching total_value metrics: {insights_url} with params: {total_value_params}")
            
            async with session.get(insights_url, params=total_value_params) as response:
                if response.status == 200:
                    data = await response.json()
                    insights = data.get("data", [])
                    
                    for insight in insights:
                        metric_name = insight.get("name")
                        values = insight.get("values", [])
                        metric_total = sum(v.get("value", 0) for v in values if v.get("value") is not None)
                        
                        if metric_name == "views":
                            total_views = metric_total
                        elif metric_name == "accounts_engaged":
                            total_accounts_engaged = metric_total
                        elif metric_name == "total_interactions":
                            total_interactions = metric_total
                        elif metric_name == "profile_links_taps":
                            total_profile_links_taps = metric_total
                        
                        # Store daily breakdown
                        for value_entry in values:
                            if value_entry.get("end_time"):
                                daily_data.append({
                                    "date": value_entry["end_time"][:10],
                                    "metric": metric_name,
                                    "value": value_entry.get("value", 0)
                                })
                    
                    logger.info(f"User-level totals - Views: {total_views}, Reach: {total_reach}, Accounts Engaged: {total_accounts_engaged}, Interactions: {total_interactions}")
                    
                else:
                    logger.error(f"Total value metrics failed with status {response.status}")
                    error_text = await response.text()
                    logger.error(f"Error response: {error_text}")
                    raise Exception(f"User insights API failed: {response.status}")
            
            # Map profile_links_taps to profile_views for frontend compatibility
            total_profile_views = total_profile_links_taps
            
            # Get engagement from recent posts (media-level for engagement only)
            total_engagement = 0
            total_saved = 0
            media_count = 0
            
            # Get recent media for engagement metrics
            media_url = f"https://graph.facebook.com/v23.0/{page_id}/media"
            media_params = {
                "fields": "id,like_count,comments_count,timestamp",
                "access_token": access_token,
                "limit": 100
            }
            
            async with session.get(media_url, params=media_params) as media_response:
                if media_response.status == 200:
                    media_data = await media_response.json()
                    media_items = media_data.get("data", [])
                    
                    for media in media_items:
                        timestamp = media.get("timestamp", "")
                        if timestamp:
                            try:
                                media_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                if media_date < since_date:
                                    continue
                            except Exception:
                                pass
                        
                        # Sum engagement from posts within time range
                        likes = media.get("like_count", 0)
                        comments = media.get("comments_count", 0)
                        total_engagement += likes + comments
                        media_count += 1
            
            # Construct final response
            result = {
                "impressions": total_views,  # Map views to impressions for frontend compatibility
                "reach": total_reach,
                "engagement": max(total_engagement, total_interactions),  # Use API interactions or calculated engagement
                "profile_views": total_profile_views,
                "website_clicks": 0,  # Not available in user-level API
                "saved": total_saved,  # Not available at user level
                "media_count": media_count,
                "accounts_engaged": total_accounts_engaged,
                "views": total_views,  # Keep new metric for future use
                "daily_data": daily_data,  # Include daily breakdown
                "source": "user_level_api_v23",
                "time_range": time_range,
                "date_range": {
                    "since": since_date.strftime('%Y-%m-%d'),
                    "until": until_date.strftime('%Y-%m-%d')
                }
            }
            
            # Check if User-level API returned meaningful data
            if total_views == 0 and total_reach == 0 and total_accounts_engaged == 0 and total_interactions == 0:
                logger.warning(f"User-level API returned all zeros, falling back to media-level API")
                raise Exception("User-level API returned no meaningful data")
            
            logger.info(f"User-level API completed successfully: {result}")
            return result
            
    except Exception as e:
        logger.error(f"User-level insights failed: {str(e)}")
        # Fallback to existing media-level approach
        return await get_instagram_insights_live_api_fallback(page_id, access_token, time_range, store_id)

async def get_instagram_insights_live_api_fallback(page_id: str, access_token: str, time_range: str, store_id: str = None) -> Dict[str, Any]:
    """
    Fallback: Get Instagram insights using live API v23.0 with media-level aggregation approach
    """
    try:
        logger.info(f"Getting Instagram insights from live API v23.0 for page {page_id}")
        
        # Try to refresh token if store_id is provided
        if store_id:
            from routes.auth import refresh_meta_token_if_needed
            refreshed_token = await refresh_meta_token_if_needed(store_id)
            if refreshed_token:
                access_token = refreshed_token
                logger.info(f"Using refreshed Meta token for store {store_id}")
        
        # Calculate date range 
        until_date = datetime.now()
        if time_range == "7d":
            since_date = until_date - timedelta(days=7)
        elif time_range == "30d":
            since_date = until_date - timedelta(days=30)
        elif time_range == "90d":
            since_date = until_date - timedelta(days=90)
        elif time_range == "lifetime":
            since_date = until_date - timedelta(days=365*2)  # 2 years for better lifetime coverage
        else:
            since_date = until_date - timedelta(days=30)
            
        logger.info(f"Date range for {time_range}: {since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=180.0)) as session:
            # Step 1: Get recent media posts (comprehensive limit for complete metrics)
            media_url = f"https://graph.facebook.com/v23.0/{page_id}/media"
            media_params = {
                "fields": "id,caption,media_type,timestamp,like_count,comments_count",
                "access_token": access_token,
                "limit": 1000  # Increased to 1000 for comprehensive media coverage
            }
            
            total_impressions = 0
            total_reach = 0
            total_engagement = 0
            total_saved = 0
            media_count = 0
            
            async with session.get(media_url, params=media_params) as media_response:
                if media_response.status == 200:
                    media_data = await media_response.json()
                    media_items = media_data.get("data", [])
                    
                    logger.info(f"Found {len(media_items)} media items")
                    
                    # Step 2: Get insights for each media item (only within time range)
                    media_processed = 0
                    media_in_range = 0
                    media_skipped = 0
                    
                    for media in media_items:
                        media_id = media.get("id")
                        timestamp = media.get("timestamp", "")
                        
                        # Check if media is within our time range
                        if timestamp:
                            try:
                                media_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                if media_date < since_date:
                                    media_skipped += 1
                                    continue  # Skip older posts
                                media_in_range += 1
                            except Exception as e:
                                logger.warning(f"Failed to parse date {timestamp}: {e}")
                                media_in_range += 1  # Include if date parsing fails
                        else:
                            media_in_range += 1  # Include if no timestamp
                        
                        # Try to get insights for this media item
                        insights_url = f"https://graph.facebook.com/v23.0/{media_id}/insights"
                        insights_params = {
                            "metric": "reach,views,saved",
                            "access_token": access_token
                        }
                        
                        try:
                            # Generous timeout for individual requests to allow processing of all media
                            async with session.get(insights_url, params=insights_params, timeout=15.0) as insights_response:
                                if insights_response.status == 200:
                                    insights_data = await insights_response.json()
                                    insights_list = insights_data.get("data", [])
                                    
                                    # Extract metrics from insights
                                    for insight in insights_list:
                                        metric_name = insight.get("name")
                                        values = insight.get("values", [])
                                        if values and len(values) > 0:
                                            value = values[0].get("value", 0)
                                            
                                            if metric_name == "views":
                                                total_impressions += value  # Use views as impressions replacement
                                                logger.debug(f"Media {media_id} views: {value}")
                                            elif metric_name == "reach":
                                                total_reach += value
                                                logger.debug(f"Media {media_id} reach: {value}")
                                            elif metric_name == "saved":
                                                total_saved += value
                                                logger.debug(f"Media {media_id} saved: {value}")
                                    
                                    # Always add basic engagement metrics from the media object
                                    likes = media.get("like_count", 0)
                                    comments = media.get("comments_count", 0)
                                    total_engagement += likes + comments
                                    
                                    media_count += 1
                                elif insights_response.status == 400:
                                    # Check for specific Instagram API errors
                                    error_data = await insights_response.json()
                                    error = error_data.get("error", {})
                                    if error.get("code") == 190:
                                        logger.warning(f"Instagram token expired for media {media_id}")
                                        # Continue without insights for this media
                                        likes = media.get("like_count", 0)
                                        comments = media.get("comments_count", 0)
                                        total_engagement += likes + comments
                                        media_count += 1
                                    elif error.get("code") == 100:
                                        logger.warning(f"Media {media_id} insights not available (code 100) - {error.get('message', 'Unknown error')}")
                                        # This is normal for recent posts, continue with basic metrics
                                        likes = media.get("like_count", 0)
                                        comments = media.get("comments_count", 0)
                                        total_engagement += likes + comments
                                        media_count += 1
                                    else:
                                        logger.warning(f"Media insights error for {media_id}: {error}")
                                        # Still count the media with basic metrics
                                        likes = media.get("like_count", 0)
                                        comments = media.get("comments_count", 0)
                                        total_engagement += likes + comments
                                        media_count += 1
                                else:
                                    # If insights fail, use basic engagement from media object
                                    likes = media.get("like_count", 0)
                                    comments = media.get("comments_count", 0)
                                    total_engagement += likes + comments
                                    media_count += 1
                        except Exception as e:
                            logger.warning(f"Error getting insights for media {media_id}: {str(e)}")
                            # Fallback to basic metrics
                            likes = media.get("like_count", 0)
                            comments = media.get("comments_count", 0)
                            total_engagement += likes + comments
                            media_count += 1
                        
                        media_processed += 1
                
                logger.info(f"Media filtering results for {time_range}: {media_in_range} posts in range, {media_skipped} posts skipped, {media_processed} total processed")
                
                # If we got no impressions/reach from media, try account-level insights as fallback
                if total_impressions == 0 and total_reach == 0:
                    logger.info("No media-level insights found, trying account-level insights as fallback")
                    try:
                        account_insights_url = f"https://graph.facebook.com/v23.0/{page_id}/insights"
                        account_params = {
                            "metric": "reach,profile_views",  # Removed impressions, kept profile_views for engagement data
                            "period": "day",
                            "access_token": access_token
                        }
                        
                        # Calculate date range for account insights (respect Instagram API 28-day limit)
                        end_date = datetime.now()
                        if time_range == "7d":
                            start_date = end_date - timedelta(days=7)
                        elif time_range == "30d":
                            start_date = end_date - timedelta(days=28)  # Use 28 days to respect API limit
                        elif time_range == "90d":
                            start_date = end_date - timedelta(days=28)  # Instagram API limit
                        elif time_range == "lifetime":
                            start_date = end_date - timedelta(days=28)  # Instagram doesn't support true lifetime
                        else:
                            start_date = end_date - timedelta(days=28)
                        
                        account_params["since"] = start_date.strftime('%Y-%m-%d')
                        account_params["until"] = end_date.strftime('%Y-%m-%d')
                        
                        async with session.get(account_insights_url, params=account_params, timeout=15.0) as account_response:
                            if account_response.status == 200:
                                account_data = await account_response.json()
                                account_insights = account_data.get("data", [])
                                
                                for insight in account_insights:
                                    metric_name = insight.get("name")
                                    values = insight.get("values", [])
                                    
                                    if metric_name == "impressions":
                                        total_impressions = sum(v.get("value", 0) for v in values)
                                        logger.info(f"Account-level impressions: {total_impressions}")
                                    elif metric_name == "reach":
                                        total_reach = sum(v.get("value", 0) for v in values)
                                        logger.info(f"Account-level reach: {total_reach}")
                                    elif metric_name == "profile_views":
                                        profile_views = sum(v.get("value", 0) for v in values)
                                        logger.info(f"Account-level profile views: {profile_views}")
                            else:
                                logger.warning(f"Account insights failed with status {account_response.status}")
                    except Exception as e:
                        logger.warning(f"Account-level insights fallback failed: {str(e)}")
                
                # Calculate profile views estimate if not obtained from account insights
                if 'profile_views' not in locals() or profile_views == 0:
                    profile_views = max(int(total_reach * 0.15), total_engagement)
                
                logger.info(f"Live API metrics - Views: {total_impressions}, Reach: {total_reach}, "
                           f"Engagement: {total_engagement}, Profile Views: {profile_views}, Media Count: {media_count} "
                           f"(Comprehensive: processed {media_processed if 'media_processed' in locals() else len(media_items)} of {len(media_items) if 'media_items' in locals() else 'unknown'} total posts)")
                
                return {
                    "impressions": total_impressions,  # Now using views metric instead of deprecated impressions
                    "reach": total_reach,
                    "profile_views": profile_views,
                    "engagement": total_engagement,
                    "saved": total_saved,
                    "media_count": media_count,
                    "source": "media_level_api_v23",
                    "api_version": "v23.0",
                    "date_range": f"{since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}",
                    "message": f"Live data from {media_count} recent posts"
                }
        
    except Exception as e:
        logger.error(f"Error getting Instagram insights from live API: {str(e)}")
        return {
            "impressions": 0,
            "reach": 0,
            "profile_views": 0,
            "engagement": 0,
            "saved": 0,
            "media_count": 0,
            "source": "live_api_error",
            "error": str(e),
            "message": "Live API request failed"
        }

async def get_instagram_insights_from_cache(page_id: str, store_id: str, time_range: str) -> Dict[str, Any]:
    """
    Fallback function to get Instagram insights from MongoDB cache when API fails
    """
    try:
        logger.info(f"Getting Instagram insights from MongoDB cache for page {page_id}")
        
        # Calculate date range (respect Instagram API 28-day limit for all ranges)
        until_date = datetime.now()
        if time_range == "7d":
            since_date = until_date - timedelta(days=7)
        elif time_range == "30d":
            since_date = until_date - timedelta(days=28)  # Use 28 days to respect API limit
        elif time_range == "90d":
            since_date = until_date - timedelta(days=28)  # Instagram API limit
        elif time_range == "lifetime":
            since_date = until_date - timedelta(days=28)  # Instagram doesn't support true lifetime
        else:
            since_date = until_date - timedelta(days=28)  # Default to 28 days
        
        # Get cached overview data from the correct collection
        overview_cache = await db_analysis["instagram_overview_cache"].find_one({
            "page_id": page_id,
            "store_id": store_id,
            "time_range": time_range,
            "expires_at": {"$gt": datetime.utcnow()}  # Only get non-expired cache
        })
        
        if overview_cache:
            metrics = overview_cache.get("metrics", {})
            logger.info(f"Found cached overview data: {metrics}")
            
            return {
                "impressions": metrics.get("impressions", 0),
                "reach": metrics.get("reach", 0),
                "profile_views": metrics.get("profile_views", 0),
                "engagement": metrics.get("engagement", 0),
                "saved": metrics.get("saved", 0),
                "media_count": metrics.get("media_count", 0),
                "followers_count": metrics.get("followers", 0),
                "source": "mongodb_cache",
                "cache_date": overview_cache.get("cache_date"),
                "message": f"Cached data from {overview_cache.get('cache_date')}"
            }
        else:
            logger.info("No valid cache found in instagram_overview_cache")
        
        # Fallback: try the old media insights cache approach
        media_insights = await db_analysis["instagram_media_insights_cache"].find({
            "page_id": page_id,
            "store_id": store_id,
            "created_at": {
                "$gte": since_date,
                "$lte": until_date
            }
        }).to_list(length=None)
        
        logger.info(f"Found {len(media_insights)} cached media insights")
        
        # Aggregate metrics from cached data
        total_impressions = 0
        total_reach = 0
        total_saved = 0
        media_count = len(media_insights)
        
        for insight in media_insights:
            metrics = insight.get("metrics", {})
            total_impressions += metrics.get("impressions", 0)
            total_reach += metrics.get("reach", 0)
            total_saved += metrics.get("saved", 0)
        
        # Calculate derived metrics
        profile_views = max(int(total_reach * 0.1), 10)  # Estimate profile views as 10% of reach
        engagement = total_saved  # Use saved as engagement metric
        
        # Get follower count from meta_pages
        page_info = await db_analysis["meta_pages"].find_one({
            "id": page_id,
            "store_id": store_id,
            "platform": "instagram"
        })
        
        followers_count = 0
        if page_info:
            followers_count = page_info.get("followers_count", 0)
        
        logger.info(f"Cache metrics - Impressions: {total_impressions}, Reach: {total_reach}, "
                   f"Engagement: {engagement}, Profile Views: {profile_views}, Followers: {followers_count}")
        
        return {
            "impressions": total_impressions,
            "reach": total_reach,
            "profile_views": profile_views,
            "engagement": engagement,
            "followers_count": followers_count,
            "media_count": media_count,
            "source": "mongodb_cache",
            "cache_date_range": f"{since_date.strftime('%Y-%m-%d')} to {until_date.strftime('%Y-%m-%d')}",
            "message": f"Data from {media_count} cached media items"
        }
        
    except Exception as e:
        logger.error(f"Error getting Instagram insights from cache: {str(e)}")
        # Return minimal fallback data
        return {
            "impressions": 0,
            "reach": 0,
            "profile_views": 0,
            "engagement": 0,
            "followers_count": 0,
            "media_count": 0,
            "source": "cache_fallback_error",
            "error": str(e),
            "message": "Unable to retrieve cached data"
        }

async def store_instagram_metrics_in_cache(page_id: str, store_id: str, metrics: Dict[str, Any], time_range: str) -> bool:
    """
    Store Instagram metrics in MongoDB cache for future use
    """
    try:
        logger.info(f"Storing Instagram metrics in cache for page {page_id}")
        
        # Calculate expiration time (2 hours from now for better performance)
        cache_date = datetime.utcnow()
        expires_at = cache_date + timedelta(hours=2)
        
        # Prepare cache document with consistent structure
        cache_document = {
            "store_id": store_id,
            "page_id": page_id,
            "platform": "instagram",
            "metrics": {
                "impressions": metrics.get("impressions", 0),
                "reach": metrics.get("reach", 0),
                "profile_views": metrics.get("profile_views", 0),
                "engagement": metrics.get("engagement", 0),
                "saved": metrics.get("saved", 0),
                "media_count": metrics.get("media_count", 0),
                # Get followers from profile if available
                "followers": metrics.get("followers_count", 0)
            },
            "time_range": time_range,
            "cache_date": cache_date,
            "last_updated": cache_date,
            "expires_at": expires_at,
            "source": metrics.get("source", "live_api_v23"),
            "api_version": metrics.get("api_version", "v23.0")
        }
        
        # Use upsert to replace existing cache for this page/store/time_range combo
        filter_query = {
            "store_id": store_id,
            "page_id": page_id,
            "time_range": time_range
        }
        
        result = await db_analysis["instagram_overview_cache"].replace_one(
            filter_query,
            cache_document,
            upsert=True
        )
        
        if result.upserted_id or result.modified_count > 0:
            logger.info(f"Successfully cached Instagram metrics for page {page_id}")
            return True
        else:
            logger.warning(f"No changes made to Instagram cache for page {page_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error storing Instagram metrics in cache: {str(e)}")
        return False

async def get_profile_from_api(page_id: str, access_token: str) -> Dict[str, Any]:
    """
    Get Instagram profile data from live API
    """
    try:
        async with aiohttp.ClientSession() as session:
            url = f"https://graph.facebook.com/v23.0/{page_id}"
            params = {
                "fields": "id,username,name,biography,followers_count,follows_count,media_count,profile_picture_url,website",
                "access_token": access_token
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    profile_data = await response.json()
                    return {"data": profile_data}
                else:
                    logger.warning(f"Profile API returned status {response.status}")
                    return {}
    except Exception as e:
        logger.error(f"Error fetching profile from API: {str(e)}")
        return {}

async def get_profile_from_cache(page_id: str, store_id: str) -> Dict[str, Any]:
    """
    Get Instagram profile data from MongoDB cache when API fails
    """
    try:
        logger.info(f"Getting Instagram profile from MongoDB cache for page {page_id}")
        
        # Get profile info from meta_pages collection
        page_info = await db_analysis["meta_pages"].find_one({
            "id": page_id,
            "store_id": store_id,
            "platform": "instagram"
        })
        
        if not page_info:
            logger.warning(f"No cached profile data found for page {page_id}")
            return {
                "data": {
                    "id": page_id,
                    "username": "Instagram Account",
                    "name": "Instagram Business Account", 
                    "followers_count": 0,
                    "follows_count": 0,
                    "media_count": 0,
                    "biography": "",
                    "profile_picture_url": "",
                    "website": "",
                    "source": "cache_fallback_empty",
                    "message": "No cached profile data available"
                },
                "page_id": page_id,
                "store_id": store_id,
                "last_updated": datetime.utcnow().isoformat()
            }
        
        # Extract profile data from cache
        profile_data = {
            "id": page_info.get("id", page_id),
            "username": page_info.get("username", "Instagram Account"),
            "name": page_info.get("name", "Instagram Business Account"),
            "followers_count": page_info.get("followers_count", 0),
            "follows_count": page_info.get("follows_count", 0),
            "media_count": page_info.get("media_count", 0),
            "biography": page_info.get("biography", ""),
            "profile_picture_url": page_info.get("profile_picture_url", ""),
            "website": page_info.get("website", ""),
            "source": "mongodb_cache",
            "message": "Profile data from cache",
            "last_cached": page_info.get("last_updated", datetime.utcnow().isoformat())
        }
        
        logger.info(f"Cache profile data - Followers: {profile_data['followers_count']}, "
                   f"Following: {profile_data['follows_count']}, Media: {profile_data['media_count']}")
        
        return {
            "data": profile_data,
            "page_id": page_id,
            "store_id": store_id,
            "last_updated": datetime.utcnow().isoformat(),
            "source": "cache"
        }
        
    except Exception as e:
        logger.error(f"Error getting profile from cache: {str(e)}")
        return {
            "data": {
                "id": page_id,
                "username": "Instagram Account",
                "name": "Instagram Business Account",
                "followers_count": 0,
                "follows_count": 0,
                "media_count": 0,
                "biography": "",
                "profile_picture_url": "",
                "website": "",
                "source": "cache_error",
                "error": str(e),
                "message": "Error retrieving cached profile data"
            },
            "page_id": page_id,
            "store_id": store_id,
            "last_updated": datetime.utcnow().isoformat()
        }

@router.get("/{store_id}/business-account")
async def get_instagram_business_account_info(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get Instagram Business Account information with proper data transformation"""
    try:
        
        # Get Instagram pages for this store
        pages = await db_analysis["meta_pages"].find({
            "store_id": store_id,
            "platform": "instagram"
        }).to_list(length=None)
        
        if not pages:
            raise HTTPException(
                status_code=404,
                detail="No Instagram Business Account found for this store"
            )
        
        # Transform page data to frontend format
        transformed_pages = []
        for page in pages:
            transformed_page = InstagramDataTransformer.transform_page_to_frontend(page)
            if InstagramDataTransformer.validate_frontend_compatibility(transformed_page, "page"):
                transformed_pages.append(transformed_page)
            else:
                logger.warning(f"Page {page.get('id')} failed frontend compatibility check")
        
        return {
            "pages": transformed_pages,
            "count": len(transformed_pages)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Instagram business account info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get Instagram business account information")

@router.get("/{store_id}/media")
async def get_instagram_media(
    store_id: str,
    page_id: str = Query(default="auto", description="Instagram Business Account ID or 'auto'"),
    limit: int = Query(25, le=100, description="Number of media items to return"),
    time_range: Optional[str] = Query(None, description="Time range filter (7d, 30d, 90d)"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get Instagram media with proper transformation to MetaPost format"""
    try:
        
        # Auto-detect page_id if needed
        if page_id == "auto":
            # Get Instagram pages for this store
            pages = await db_analysis["meta_pages"].find({
                "store_id": store_id,
                "platform": "instagram"
            }).to_list(length=None)
            
            if not pages:
                raise HTTPException(
                    status_code=404,
                    detail="No Instagram Business Account found for this store"
                )
            
            # Use the first Instagram page found
            page_id = pages[0].get("id")
            if not page_id:
                raise HTTPException(
                    status_code=404,
                    detail="Invalid Instagram Business Account configuration"
                )
        
        # Check cache first
        cache_key = InstagramCacheService.generate_cache_key(
            page_id, "media_list", {"limit": limit, "time_range": time_range}
        )
        cached_data = await InstagramCacheService.get_cached_data(cache_key, "media_list")
        if cached_data:
            logger.debug(f"Using cached Instagram media for {page_id}")
            return cached_data
        
        # Get page details and validate
        page = await db_analysis["meta_pages"].find_one({
            "id": page_id,
            "store_id": store_id,
            "platform": "instagram"
        })
        
        if not page:
            raise HTTPException(status_code=404, detail="Instagram page not found")
        
        access_token = page.get("access_token")
        if not access_token:
            raise HTTPException(status_code=401, detail="No access token for Instagram page")
        
        # Validate token and permissions
        token_validation = await validate_instagram_token(access_token)
        if not token_validation.get("valid"):
            raise HTTPException(
                status_code=401,
                detail="Instagram access token is invalid or expired"
            )
        
        required_permissions = INSTAGRAM_PERMISSIONS["basic"]
        permission_check = await validate_instagram_permissions(access_token, required_permissions)
        if not permission_check.get("valid"):
            missing = permission_check.get("missing", [])
            logger.warning(f"Instagram permissions validation failed for store {store_id}, missing: {missing}")
            # For now, continue with a warning instead of blocking completely
            # This allows Meta dashboard to work while Instagram API might be limited
            # raise HTTPException(
            #     status_code=403,
            #     detail=f"Missing Instagram permissions: {', '.join(missing)}"
            # )
        
        # Calculate time range
        since_date = None
        until_date = None
        if time_range:
            until_date = datetime.now()
            if time_range == "7d":
                since_date = until_date - timedelta(days=7)
            elif time_range == "30d":
                since_date = until_date - timedelta(days=30)
            elif time_range == "90d":
                since_date = until_date - timedelta(days=90)
        
        # Fetch media from Instagram API v23.0
        async with aiohttp.ClientSession() as session:
            url = f"https://graph.facebook.com/v23.0/{page_id}/media"
            params = {
                "fields": "id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,like_count,comments_count",
                "access_token": access_token,
                "limit": limit
            }
            
            if since_date and until_date:
                params["since"] = int(since_date.timestamp())
                params["until"] = int(until_date.timestamp())
            
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    error_data = await response.json()
                    error = error_data.get("error", {})
                    logger.error(f"Instagram API error: {error}")
                    raise HTTPException(
                        status_code=response.status,
                        detail=f"Instagram API error: {error.get('message', 'Unknown error')}"
                    )
                
                data = await response.json()
                media_items = data.get("data", [])
                
                # Transform each media item to frontend format
                transformed_posts = []
                for media in media_items:
                    try:
                        # Get additional insights for each media item
                        insights = await get_instagram_media_insights(
                            media.get("id"),
                            access_token,
                            ["saved", "impressions", "reach"]
                        )
                        
                        # Merge insights with media data
                        media_with_insights = {**media, **insights}
                        
                        # Transform to frontend format
                        transformed_post = InstagramDataTransformer.transform_media_to_post(media_with_insights)
                        
                        # Validate transformation
                        if InstagramDataTransformer.validate_frontend_compatibility(transformed_post, "post"):
                            transformed_posts.append(transformed_post)
                        else:
                            logger.warning(f"Media {media.get('id')} failed frontend compatibility check")
                            
                    except Exception as e:
                        logger.warning(f"Error processing media {media.get('id')}: {str(e)}")
                        continue
                
                result = {
                    "data": transformed_posts,
                    "count": len(transformed_posts),
                    "page_id": page_id,
                    "time_range": time_range,
                    "last_updated": datetime.utcnow().isoformat()
                }
                
                # Cache the results
                await InstagramCacheService.set_cached_data(
                    cache_key,
                    "media_list",
                    result,
                    {
                        "page_id": page_id,
                        "store_id": store_id,
                        "time_range": time_range
                    }
                )
                
                return result
        
    except HTTPException:
        raise
    except InstagramAPIError as e:
        error_data = {
            "error": e.message,
            "error_code": e.code,
            "error_action": e.action,
            "instagram_specific": True
        }
        transformed_error = InstagramDataTransformer.transform_error_to_frontend(error_data)
        raise HTTPException(
            status_code=400,
            detail=transformed_error
        )
    except Exception as e:
        logger.error(f"Error getting Instagram media: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get Instagram media")

@router.get("/{store_id}/insights")
async def get_instagram_insights(
    store_id: str,
    page_id: str = Query(default="auto", description="Instagram Business Account ID or 'auto'"),
    time_range: Optional[str] = Query("30d", description="Time range (7d, 30d, lifetime)"),
    cache_first: bool = Query(default=True, description="Check cache first, then live API"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get Instagram insights with proper data transformation"""
    try:
        
        # Auto-detect page_id if needed
        if page_id == "auto":
            # Get Instagram pages for this store
            pages = await db_analysis["meta_pages"].find({
                "store_id": store_id,
                "platform": "instagram"
            }).to_list(length=None)
            
            if not pages:
                raise HTTPException(
                    status_code=404,
                    detail="No Instagram Business Account found for this store. Please connect an Instagram Business Account."
                )
            
            # Use the first Instagram page found
            page_id = pages[0].get("id")
            if not page_id:
                raise HTTPException(
                    status_code=404,
                    detail="Invalid Instagram Business Account configuration"
                )
        
        # Get page details for access token
        page = await db_analysis["meta_pages"].find_one({
            "id": page_id,
            "store_id": store_id,
            "platform": "instagram"
        })
        
        if not page:
            raise HTTPException(status_code=404, detail="Instagram page not found")
        
        access_token = page.get("access_token")
        
        # Hybrid approach:
        # - lifetime: Check MongoDB cache first, then use media-level API
        # - 30d: Use user-level API (fast, respects API limits)
        # - 7d: Use user-level API
        
        # For lifetime with cache_first=true, check MongoDB cache first
        if cache_first and time_range == "lifetime":
            logger.info(f"Lifetime query: Checking MongoDB cache first for page {page_id}")
            try:
                metrics = await get_instagram_insights_from_cache(page_id, store_id, time_range)
                if metrics.get("source") != "cache_fallback_error" and any(metrics.get(m, 0) > 0 for m in ["impressions", "reach", "engagement", "media_count"]):
                    logger.info(f"Successfully loaded lifetime metrics from cache: {metrics.get('source')}")
                    return {
                        "insights": metrics,
                        "page_id": page_id,
                        "store_id": store_id,
                        "time_range": time_range,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                else:
                    logger.info("Cache data insufficient or empty for lifetime, will use media-level API")
            except Exception as cache_error:
                logger.warning(f"Cache check failed for lifetime: {str(cache_error)}, will use media-level API")
        
        # Cache-first approach for other time ranges (if any)
        elif cache_first and time_range not in ["30d", "7d", "lifetime"]:
            logger.info(f"Cache-first approach: Checking MongoDB cache for page {page_id}")
            try:
                metrics = await get_instagram_insights_from_cache(page_id, store_id, time_range)
                if metrics.get("source") != "cache_fallback_error" and any(metrics.get(m, 0) > 0 for m in ["impressions", "reach", "engagement", "media_count"]):
                    logger.info(f"Successfully loaded metrics from cache: {metrics.get('source')}")
                    return {
                        "insights": metrics,
                        "page_id": page_id,
                        "store_id": store_id,
                        "time_range": time_range,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                else:
                    logger.info("Cache data insufficient or empty, falling back to live API")
            except Exception as cache_error:
                logger.warning(f"Cache check failed: {str(cache_error)}, falling back to live API")
        elif time_range in ["30d", "7d"]:
            logger.info(f"Skipping cache for time range {time_range}, using User-level API for accurate period data")
        
        # Live API approach (either cache_first=False or cache was empty/failed)
        try:
            if access_token:
                if time_range == "lifetime":
                    # For lifetime: Use media-level API (the fallback method)
                    logger.info(f"Using media-level API for lifetime Instagram insights: {page_id}")
                    metrics = await get_instagram_insights_live_api_fallback(page_id, access_token, time_range, store_id)
                else:
                    # For 30d, 7d: Use user-level API
                    logger.info(f"Trying User-level API v23.0 for Instagram insights: {page_id}")
                    metrics = await get_instagram_insights_user_level(page_id, access_token, time_range, store_id)
                
                # If live API returns good data, use it
                if metrics.get("source") in ["user_level_api_v23", "live_api_v23", "media_level_api_v23"] and (metrics.get("impressions", 0) > 0 or metrics.get("reach", 0) > 0 or metrics.get("media_count", 0) > 0):
                    logger.info(f"Successfully got live API data with {metrics.get('media_count')} posts")
                    
                    # Also get followers count from profile to complete the cache
                    try:
                        profile_response = await get_profile_from_api(page_id, access_token)
                        if profile_response and "data" in profile_response:
                            metrics["followers_count"] = profile_response["data"].get("followers_count", 0)
                            logger.info(f"Added followers count: {metrics['followers_count']}")
                    except Exception as profile_error:
                        logger.warning(f"Could not fetch followers count: {str(profile_error)}")
                        # Use cached followers count from page if available
                        metrics["followers_count"] = page.get("followers_count", 0)
                    
                    # Store live data in MongoDB cache for future use
                    await store_instagram_metrics_in_cache(page_id, store_id, metrics, time_range)
                else:
                    raise Exception("Live API returned insufficient data")
            else:
                raise Exception("No access token available")
                
        except Exception as e:
            logger.warning(f"Live API v23.0 failed: {str(e)}, trying existing service")
            
            try:
                # Import here to avoid circular imports
                from services.meta import get_instagram_organic_metrics
                
                # Get Instagram metrics using existing service
                metrics = await get_instagram_organic_metrics(page_id, time_range, use_cache=True)
                
                # Check if metrics contain an error - if so, try MongoDB fallback
                if "error" in metrics:
                    logger.warning(f"Existing Instagram service failed for {page_id}, trying MongoDB cache fallback")
                    metrics = await get_instagram_insights_from_cache(page_id, store_id, time_range)
                
            except Exception as e2:
                logger.warning(f"Existing Instagram service failed: {str(e2)}, trying MongoDB cache fallback")
                metrics = await get_instagram_insights_from_cache(page_id, store_id, time_range)
        
        # Return the metrics
        return {
            "insights": metrics,
            "page_id": page_id,
            "store_id": store_id,
            "time_range": time_range,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Instagram insights: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get Instagram insights")

@router.post("/{store_id}/invalidate-cache")
async def invalidate_instagram_cache(
    store_id: str,
    page_id: Optional[str] = None,
    cache_type: Optional[str] = None,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Invalidate Instagram cache for debugging/testing"""
    try:
        
        # Invalidate cache
        invalidated_count = await InstagramCacheService.invalidate_cache(
            page_id=page_id or "all",
            data_type=cache_type or "all",
            store_id=store_id
        )
        
        return {
            "invalidated_count": invalidated_count,
            "store_id": store_id,
            "page_id": page_id,
            "cache_type": cache_type,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error invalidating Instagram cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to invalidate cache")

@router.get("/{store_id}/profile")
async def get_instagram_profile(
    store_id: str,
    page_id: str = Query(default="auto", description="Instagram Business Account ID or 'auto'"),
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get Instagram profile information"""
    try:
        
        # Auto-detect page_id if needed
        if page_id == "auto":
            # Get Instagram pages for this store
            pages = await db_analysis["meta_pages"].find({
                "store_id": store_id,
                "platform": "instagram"
            }).to_list(length=None)
            
            if not pages:
                raise HTTPException(
                    status_code=404,
                    detail="No Instagram Business Account found for this store"
                )
            
            # Use the first Instagram page found
            page_id = pages[0].get("id")
            if not page_id:
                raise HTTPException(
                    status_code=404,
                    detail="Invalid Instagram Business Account configuration"
                )
        
        # Get page details and validate
        page = await db_analysis["meta_pages"].find_one({
            "id": page_id,
            "store_id": store_id,
            "platform": "instagram"
        })
        
        if not page:
            raise HTTPException(status_code=404, detail="Instagram page not found")
        
        access_token = page.get("access_token")
        if not access_token:
            raise HTTPException(status_code=401, detail="No access token for Instagram page")
        
        # Try to get profile data with fallback to cached data
        try:
            # Validate token and permissions
            token_validation = await validate_instagram_token(access_token)
            if not token_validation.get("valid"):
                logger.warning(f"Instagram token invalid for {page_id}, using cached profile data")
                return await get_profile_from_cache(page_id, store_id)
            
            # Fetch profile information from Instagram API v23.0
            async with aiohttp.ClientSession() as session:
                url = f"https://graph.facebook.com/v23.0/{page_id}"
                params = {
                    "fields": "id,username,name,biography,followers_count,follows_count,media_count,profile_picture_url,website",
                    "access_token": access_token
                }
                
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        error_data = await response.json()
                        error = error_data.get("error", {})
                        logger.warning(f"Instagram API error: {error}, using cached profile data")
                        return await get_profile_from_cache(page_id, store_id)
                    
                    profile_data = await response.json()
                    
                    # Transform profile data to frontend format
                    transformed_profile = InstagramDataTransformer.transform_profile_to_frontend(profile_data)
                    
                    # Validate transformation
                    if not InstagramDataTransformer.validate_frontend_compatibility(transformed_profile, "profile"):
                        logger.warning(f"Profile {page_id} failed frontend compatibility check")
                    
                    return {
                        "data": transformed_profile,
                        "page_id": page_id,
                        "store_id": store_id,
                        "last_updated": datetime.utcnow().isoformat(),
                        "source": "instagram_api"
                    }
                    
        except Exception as e:
            logger.warning(f"Instagram profile API failed: {str(e)}, using cached data")
            return await get_profile_from_cache(page_id, store_id)
        
    except HTTPException:
        raise
    except InstagramAPIError as e:
        error_data = {
            "error": e.message,
            "error_code": e.code,
            "error_action": e.action,
            "instagram_specific": True
        }
        transformed_error = InstagramDataTransformer.transform_error_to_frontend(error_data)
        raise HTTPException(
            status_code=400,
            detail=transformed_error
        )
    except Exception as e:
        logger.error(f"Error getting Instagram profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get Instagram profile")

@router.get("/{store_id}/cache-stats")
async def get_instagram_cache_stats(
    store_id: str,
    current_user: User = Depends(verify_user_can_access_store)
):
    """Get Instagram cache statistics for monitoring"""
    try:
        
        # Get cache stats
        stats = await InstagramCacheService.get_cache_stats()
        
        return {
            "cache_stats": stats,
            "store_id": store_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Instagram cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get cache stats")