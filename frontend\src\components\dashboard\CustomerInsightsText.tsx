import React from 'react';
import { Box, Typography, Grid, Divider } from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import ShoppingBasketIcon from '@mui/icons-material/ShoppingBasket';
import RepeatIcon from '@mui/icons-material/Repeat';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import RemoveShoppingCartIcon from '@mui/icons-material/RemoveShoppingCart';

interface CustomerInsightsTextProps {
  totalCustomers: number;
  analysisText: string;
  enhancedData?: {
    with_orders?: number;
    repeat?: number;
    abandoned_cart_count?: number;
    abandoned_cart_total_value?: number;
    pending_cart_count?: number;
    pending_cart_total_value?: number;
    average_spend?: number;
  };
  currencySymbol?: string;
}

const CustomerInsightsText: React.FC<CustomerInsightsTextProps> = ({ 
  totalCustomers, 
  analysisText,
  enhancedData = {},
  currencySymbol = '$'
}) => {
  // Parse the text to separate active customers and behavior
  const [mainInfo, behaviorSection] = analysisText.split(' - Customer Behavior: ');

  // Format the text to highlight numbers and important information
  const formattedActiveCustomers = mainInfo
    .split(' - ')
    .map(section => section.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>'))
    .join('\n- ')
    .replace(/^/, '- ') // Add dash to the first line
    .replace(/^- - /, '- '); // Fix double dash at the beginning

  // Format currency values
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value).replace('$', currencySymbol);
  };

  return (
    <Box sx={{
      mt: 4,
      p: 3,
      bgcolor: 'rgba(236, 242, 255, 0.5)',
      borderRadius: 2,
      border: '1px solid rgba(224, 231, 255, 0.8)'
    }}>
      {/* Customer Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Customers */}
        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              bgcolor: '#0D6EFD',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2
            }}>
              <PeopleIcon sx={{ color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 500 }}>
                {totalCustomers.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Customers
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Customers with Orders */}
        {enhancedData.with_orders !== undefined && (
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: '#6C757D',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2
              }}>
                <ShoppingBasketIcon sx={{ color: 'white' }} />
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  {enhancedData.with_orders.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Customers with Orders
                </Typography>
              </Box>
            </Box>
          </Grid>
        )}

        {/* Repeat Customers */}
        {enhancedData.repeat !== undefined && (
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: '#28A745',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2
              }}>
                <RepeatIcon sx={{ color: 'white' }} />
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  {enhancedData.repeat.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Repeat Customers
                </Typography>
              </Box>
            </Box>
          </Grid>
        )}

        {/* Average Spend */}
        {enhancedData.average_spend !== undefined && (
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: '#FFC107',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2
              }}>
                <MonetizationOnIcon sx={{ color: 'white' }} />
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  {formatCurrency(enhancedData.average_spend)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average Spend per Customer
                </Typography>
              </Box>
            </Box>
          </Grid>
        )}

        {/* Abandoned Carts */}
        {enhancedData.abandoned_cart_count !== undefined && (
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: '#DC3545',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2
              }}>
                <RemoveShoppingCartIcon sx={{ color: 'white' }} />
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  {enhancedData.abandoned_cart_count.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Abandoned Carts
                </Typography>
                {enhancedData.abandoned_cart_total_value !== undefined && (
                  <Typography variant="caption" color="text.secondary">
                    Value: {formatCurrency(enhancedData.abandoned_cart_total_value)}
                  </Typography>
                )}
              </Box>
            </Box>
          </Grid>
        )}

        {/* Pending Carts */}
        {enhancedData.pending_cart_count !== undefined && (
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: '#17A2B8',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2
              }}>
                <ShoppingCartIcon sx={{ color: 'white' }} />
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  {enhancedData.pending_cart_count.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pending Carts
                </Typography>
                {enhancedData.pending_cart_total_value !== undefined && (
                  <Typography variant="caption" color="text.secondary">
                    Value: {formatCurrency(enhancedData.pending_cart_total_value)}
                  </Typography>
                )}
              </Box>
            </Box>
          </Grid>
        )}
      </Grid>

      <Divider sx={{ mb: 3 }} />

      {/* Active Customers Info */}
      <Box sx={{ mb: 3 }}>
        <Typography
          dangerouslySetInnerHTML={{ 
            __html: formattedActiveCustomers.replace(/\n/g, '<br />') 
          }}
          sx={{
            color: 'text.secondary',
            lineHeight: 2, // Increased line height for better spacing
            '& strong': {
              color: 'text.primary',
              fontWeight: 600
            }
          }}
        />
      </Box>

      {/* Customer Behavior Insight */}
      {behaviorSection && (
        <Box sx={{
          mt: 2,
          p: 2,
          bgcolor: 'white',
          borderRadius: 1,
          border: '1px solid rgba(224, 231, 255, 0.8)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
            <Box sx={{
              width: 24,
              height: 24,
              borderRadius: '50%',
              bgcolor: 'rgba(13, 110, 253, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              mt: 0.5,
              flexShrink: 0
            }}>
              <ShoppingCartIcon sx={{ width: 16, height: 16, color: '#0D6EFD' }} />
            </Box>
            <Box>
              <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600, mb: 0.5 }}>
                Customer Behavior:
              </Typography>
              <Typography sx={{ color: 'text.secondary', lineHeight: 1.6 }}>
                {behaviorSection}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default CustomerInsightsText; 