# New unit tests for password reset flow
import pytest
from datetime import datetime, timedelta, timezone

REQUEST_EP = "/api/auth/password-reset/request"
VERIFY_EP = "/api/auth/password-reset/verify"


def _request_code(client, email: str):
    return client.post(REQUEST_EP, json={"email": email})


def test_password_reset_request_success(client):
    test_client, env = client
    resp = _request_code(test_client, "<EMAIL>")
    assert resp.status_code == 200
    code = env["sent_codes"].get("<EMAIL>")
    assert code is not None and len(code) == 6


def test_password_reset_verify_success(client):
    test_client, env = client
    # Step 1 request code
    _request_code(test_client, "<EMAIL>")
    code = env["sent_codes"].get("<EMAIL>")
    assert code is not None

    payload = {
        "email": "<EMAIL>",
        "verification_code": code,
        "new_password": "StrongPass9$",
        "confirm_password": "StrongPass9$"
    }
    verify_resp = test_client.post(VERIFY_EP, json=payload)
    assert verify_resp.status_code == 200
    data = verify_resp.json()
    assert "access_token" in data


def test_password_reset_wrong_code(client):
    test_client, _ = client
    _request_code(test_client, "<EMAIL>")
    payload = {
        "email": "<EMAIL>",
        "verification_code": "000000",
        "new_password": "StrongPass9$",
        "confirm_password": "StrongPass9$"
    }
    resp = test_client.post(VERIFY_EP, json=payload)
    assert resp.status_code == 400


def test_password_reset_code_expired(client):
    test_client, env = client
    # Step 1 request code
    _request_code(test_client, "<EMAIL>")
    code = env["sent_codes"].get("<EMAIL>")

    # Expire the code manually
    db_main = env["db_main"]
    db_main["store_users"]._collection.update_one(
        {"email": "<EMAIL>"},
        {"$set": {"password_reset_pending.expiry": datetime.now(timezone.utc) - timedelta(minutes=1)}}
    )

    payload = {
        "email": "<EMAIL>",
        "verification_code": code,
        "new_password": "StrongPass9$",
        "confirm_password": "StrongPass9$"
    }
    resp = test_client.post(VERIFY_EP, json=payload)
    assert resp.status_code == 400 