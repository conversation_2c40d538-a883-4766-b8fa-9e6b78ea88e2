import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/apiService';
import { authService } from '../services/authService';
import {
  Box,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
  CircularProgress,
  Alert,
  Pagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { ApiError } from '../services/apiService';
import { useTranslation } from 'react-i18next';

interface Store {
  id: string;
  name: string;
}

const AdminPortal: React.FC = () => {
  const { t } = useTranslation();
  const { isAuthenticated, setViewStoreID } = useAuth();
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // --- Credits Modal State ---
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  const [addingCredits, setAddingCredits] = useState(false);
  const [addError, setAddError] = useState<string | null>(null);
  const itemsPerPage = 20;

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setPage(1);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  useEffect(() => {
    const fetchStores = async () => {
      setLoading(true);
      setError(null);
      try {
        const currentToken = authService.getToken();
        if (!currentToken) {
          setError(t('adminPortal.errorNoToken'));
          setLoading(false);
          return;
        }

        const skip = (page - 1) * itemsPerPage;
        const response = await apiService.getAdminStores(currentToken, skip, itemsPerPage, debouncedSearchTerm);
        
        setStores(response.stores);
        setTotalPages(Math.max(1, Math.ceil(response.total_count / itemsPerPage)));

      } catch (err) {
        if (err instanceof ApiError) {
          setError(err.message);
        } else if (err instanceof Error) {
          setError(err.message);
        } else {
          setError(t('adminPortal.errorUnknownFetch'));
        }
        setStores([]);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchStores();
    } else {
      setLoading(false);
      setError(t('adminPortal.errorAuthRequired'));
      setStores([]);
      setTotalPages(1);
    }
  }, [isAuthenticated, page, debouncedSearchTerm, t]);

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <Box sx={{ padding: '2rem' }}>
      <Typography 
        variant="h4" 
        component="h1" 
        gutterBottom
        sx={{
          fontWeight: 700,
          background: 'linear-gradient(90deg, rgba(0,163,255,1) 0%, rgba(0,212,255,1) 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          letterSpacing: '1px',
          textTransform: 'uppercase',
        }}
      >
        {t('adminPortal.title')}
      </Typography>
      
      <TextField 
        fullWidth 
        label={t('adminPortal.searchLabel') || 'Search Stores (ID or Name)'}
        variant="outlined"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value.trim())}
        sx={{ mb: 2 }}
      />
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 2 }}>
            {t('adminPortal.storeSelectionTitle')}
          </Typography>
          
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          )}
          
          {error && (
            <Alert severity="error" sx={{ my: 2 }}>
              {error}
            </Alert>
          )}
          
          {!loading && !error && stores.length === 0 && (
            <Alert severity="info" sx={{ my: 2, textAlign: 'center' }}>
              {t('adminPortal.infoNoStoresFound')}
            </Alert>
          )}
          
          {!loading && !error && stores.length > 0 && (
            <>
              <Box sx={{ maxHeight: 'calc(100vh - 400px)', overflowY: 'auto', mb: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <List disablePadding>
                  {stores.map((store, index) => (
                    <ListItem 
                      key={store.id} 
                      divider={index < stores.length - 1}
                      sx={{ py: 1.5 }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Typography variant="body1" sx={{ flexGrow: 1 }}>
                          {store.name || t('adminPortal.unnamedStore')} (ID: {store.id})
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Link to={`/store/${store.id}/overview`} style={{ textDecoration: 'none' }}>
                            <Button
                              variant="contained"
                              size="small"
                              color="primary"
                              onClick={() => setViewStoreID(store.id)}
                            >
                              {t('adminPortal.viewDashboardButton')}
                            </Button>
                          </Link>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => {
                              setSelectedStore(store);
                              setCreditAmount('');
                              setAddError(null);
                            }}
                          >
                            {t('adminPortal.addCreditsButton', 'Add Credits')}
                          </Button>
                        </Box>
                      </Box>
                    </ListItem>
                  ))}
                </List>
              </Box>
              
              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Credits Dialog */}
      <Dialog open={Boolean(selectedStore)} onClose={() => setSelectedStore(null)}>
        <DialogTitle>
          {t('adminPortal.addCreditsTitle', 'Add Credits to Store')}
        </DialogTitle>
        <DialogContent sx={{ minWidth: 300 }}>
          {addError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {addError}
            </Alert>
          )}
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            {selectedStore?.name} (ID: {selectedStore?.id})
          </Typography>
          <TextField
            type="number"
            label={t('adminPortal.amountLabel', 'Amount')}
            value={creditAmount}
            onChange={(e) => setCreditAmount(e.target.value)}
            fullWidth
            disabled={addingCredits}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedStore(null)} disabled={addingCredits}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            variant="contained"
            onClick={async () => {
              if (!selectedStore) return;
              const amount = Number(creditAmount);
              if (!amount || amount <= 0) {
                setAddError(t('adminPortal.invalidAmount', 'Enter a positive amount'));
                return;
              }
              setAddingCredits(true);
              setAddError(null);
              try {
                await apiService.addStoreCredits(selectedStore.id, amount);
                setSelectedStore(null);
              } catch (err) {
                if (err instanceof ApiError) {
                  setAddError(err.message);
                } else if (err instanceof Error) {
                  setAddError(err.message);
                } else {
                  setAddError(t('adminPortal.errorUnknownAdd', 'Failed to add credits'));
                }
              } finally {
                setAddingCredits(false);
              }
            }}
            disabled={addingCredits}
          >
            {addingCredits ? (
              <CircularProgress size={20} />
            ) : (
              t('adminPortal.confirm', 'Add')
            )}
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
};

export default AdminPortal; 