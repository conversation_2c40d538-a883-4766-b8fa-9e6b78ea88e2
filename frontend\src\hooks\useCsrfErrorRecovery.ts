import { useCallback, useState } from 'react';
import { csrfService } from '../services/csrfService';
import { logger } from '../utils/logger';

export interface CsrfErrorRecoveryResult {
  isRecovering: boolean;
  retryRequest: <T>(
    requestFn: () => Promise<T>,
    maxRetries?: number
  ) => Promise<T>;
  clearError: () => void;
  lastError: string | null;
}

export interface ApiError {
  response?: {
    status: number;
    data?: {
      detail?: string;
      message?: string;
    };
  };
  message?: string;
}

/**
 * Hook for automatic CSRF error detection and recovery
 * Provides retry mechanism with token refresh for CSRF failures
 */
export const useCsrfErrorRecovery = (): CsrfErrorRecoveryResult => {
  const [isRecovering, setIsRecovering] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);

  const isCSRFError = useCallback((error: ApiError): boolean => {
    if (!error?.response) return false;
    
    const status = error.response.status;
    const data = error.response.data || {};
    
    // Check for CSRF-specific error patterns
    const isStatusMatch = status === 401 || status === 403;
    const hasCSRFMessage = (
      (typeof data.detail === 'string' && 
       (data.detail.toLowerCase().includes('csrf') || 
        data.detail.toLowerCase().includes('token') ||
        data.detail.toLowerCase().includes('expired'))) ||
      (typeof data.message === 'string' && 
       (data.message.toLowerCase().includes('csrf') ||
        data.message.toLowerCase().includes('token') ||
        data.message.toLowerCase().includes('expired')))
    );

    // Exclude authentication errors from CSRF retry logic
    const isAuthError = (
      status === 401 &&
      (
        (typeof data.detail === 'string' && 
         (data.detail.toLowerCase().includes('authentication') ||
          data.detail.toLowerCase().includes('unauthorized') ||
          data.detail.toLowerCase().includes('invalid token'))) ||
        (typeof data.message === 'string' && 
         (data.message.toLowerCase().includes('authentication') ||
          data.message.toLowerCase().includes('unauthorized') ||
          data.message.toLowerCase().includes('invalid token')))
      )
    );

    return isStatusMatch && hasCSRFMessage && !isAuthError;
  }, []);

  const retryRequest = useCallback(async <T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 2
  ): Promise<T> => {
    let attempt = 0;
    const delays = [500, 1000]; // Exponential backoff delays

    while (attempt <= maxRetries) {
      try {
        if (attempt > 0) {
          setIsRecovering(true);
          logger.debug(`CSRF recovery attempt ${attempt}/${maxRetries}`);
        }

        const result = await requestFn();
        
        if (attempt > 0) {
          logger.info('CSRF recovery successful');
          setLastError(null);
        }
        
        return result;
      } catch (error: unknown) {
        const apiError = error as ApiError;
        const isCsrfError = isCSRFError(apiError);
        
        if (isCsrfError && attempt < maxRetries) {
          logger.warn(`CSRF error detected, attempting recovery (${attempt + 1}/${maxRetries + 1})`);
          
          try {
            // Clear and refresh CSRF token
            csrfService.clearToken();
            await csrfService.getToken(); // Force refresh
            
            // Wait before retry with exponential backoff
            if (delays[attempt]) {
              await new Promise(resolve => setTimeout(resolve, delays[attempt]));
            }
            
            attempt++;
            continue;
          } catch (refreshError) {
            logger.error('Failed to refresh CSRF token:', refreshError);
            setLastError('Failed to refresh security token. Please try again.');
            break;
          }
        } else {
          // Not a CSRF error or exceeded retries
          if (isCsrfError) {
            setLastError('Security token error. Please refresh the page and try again.');
          } else {
            setLastError(apiError.message || 'Request failed');
          }
          throw error;
        }
      } finally {
        setIsRecovering(false);
      }
    }

    throw new Error(lastError || 'Maximum retry attempts exceeded');
  }, [isCSRFError, lastError]);

  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  return {
    isRecovering,
    retryRequest,
    clearError,
    lastError
  };
}; 