import argparse
import os
import sys
from dotenv import load_dotenv
from pymongo.errors import ConnectionFailure, OperationFailure

# Add project root to sys.path to allow importing modules under the 'backend' package
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
backend_root = os.path.join(project_root, 'backend')
sys.path.insert(0, project_root)

# --- Load environment variables FIRST --- #
# Construct the path to the .env file relative to this script's location
# Go up one level from scripts/ to the backend directory
dotenv_path = os.path.join(script_dir, '..', '.env.development')
if not os.path.exists(dotenv_path):
    # Fallback if script is run from a different location
    dotenv_path_fallback = os.path.join(backend_root, '.env.development')
    if os.path.exists(dotenv_path_fallback):
        dotenv_path = dotenv_path_fallback
    else:
        print(f"Error: Could not find .env file at {dotenv_path} or {dotenv_path_fallback}", file=sys.stderr)
        sys.exit(1)

print(f"Loading environment variables from: {dotenv_path}", file=sys.stderr)
load_dotenv(dotenv_path=dotenv_path)
# --- End Load environment variables --- #

# Import backend modules
from backend.config.database import get_mongodb_connection
from backend.models.user import User
from backend.services.auth import get_password_hash

def reset_admin_password(email: str, new_password: str):
    """
    Resets the password for an admin user in the database.
    """
    client = None # Initialize client to None for finally block
    try:
        # Get the synchronous client using the function
        client = get_mongodb_connection()
        if not client:
            print("Error: Failed to get MongoDB connection.", file=sys.stderr)
            sys.exit(1)

        # Assuming the User model might define the database/collection, but let's be explicit
        # Need to confirm the correct database name ('D-Unit' or other) and collection ('store_users')
        db_name = os.getenv("MONGODB_DATABASE", "D-Unit") # Default to D-Unit if not in env
        db = client[db_name]
        users_collection = db["store_users"] # Confirm collection name

        # Find the user
        user_data = users_collection.find_one({"email": email})

        if not user_data:
            print(f"Error: User with email '{email}' not found.", file=sys.stderr)
            sys.exit(1)

        # Validate if the user is an admin
        # Check the actual field name for role, assuming 'role'
        if user_data.get("role") != "admin":
            print(f"Error: User '{email}' is not an admin.", file=sys.stderr)
            sys.exit(1)

        # Hash the new password
        hashed_password = get_password_hash(new_password)

        # Update the user document - Target 'pass_dunit' field
        result = users_collection.update_one(
            {"email": email},
            {"$set": {"pass_dunit": hashed_password}} # Update pass_dunit
        )

        if result.modified_count == 1:
            print(f"Successfully reset password (pass_dunit) for admin user '{email}'.")
        else:
            # This case should ideally not happen if the user was found
            print(f"Error: Failed to update password (pass_dunit) for user '{email}'. No document modified.", file=sys.stderr)
            sys.exit(1)

    except ConnectionFailure as e:
        print(f"Database connection error: {e}", file=sys.stderr)
        sys.exit(1)
    except OperationFailure as e:
        print(f"Database operation error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        if 'client' in locals() and client:
            client.close()
            # print("Database connection closed.")


if __name__ == "__main__":
    # Security Warning
    print("WARNING: This script directly modifies the database and should only be run by authorized personnel.", file=sys.stderr)

    parser = argparse.ArgumentParser(description="Reset the password for an admin user.")
    parser.add_argument("--email", required=True, help="Email address of the admin user.")
    parser.add_argument("--new-password", required=True, help="The new password for the admin user.")

    args = parser.parse_args()

    # Call the main function
    reset_admin_password(args.email, args.new_password)

    # Exit with success code if the function didn't exit with an error
    sys.exit(0) 