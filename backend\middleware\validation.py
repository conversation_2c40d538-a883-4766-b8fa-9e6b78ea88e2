import re
import json
import yaml
import logging
from datetime import datetime, timezone
from typing import Dict, Optional, Any, List, Set
from pathlib import Path
import asyncio
import os
import traceback

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import ValidationError as PydanticValidationError

from models.security import ValidationResult, ValidationError, SecurityEvent, SecurityEventType, ThreatLevel
from utils.security_utils import (
    detect_attack_patterns, 
    sanitize_sql_input, 
    generate_trace_id,
    detect_suspicious_user_agent,
    extract_client_ip, 
    sanitize_input, 
    mask_sensitive_data
)

logger = logging.getLogger(__name__)

class RequestValidationMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive request validation middleware with configurable rules
    """
    
    def __init__(self, app, config_path: str = "config/validation_rules.yaml"):
        super().__init__(app)
        self.config_path = config_path
        self.config = self._load_config()
        self.enabled = True
        
        # Compile regex patterns for performance
        self._compiled_patterns = self._compile_patterns()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load validation configuration from YAML file"""
        try:
            # Use absolute path resolution
            if not os.path.isabs(self.config_path):
                # Get the project root directory (assuming backend is in project root)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = Path(project_root) / self.config_path
            else:
                config_file = Path(self.config_path)
                
            logger.info(f"Loading validation config from: {config_file.absolute()}")
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"Successfully loaded validation configuration from {config_file}")
                return config or {}
            else:
                logger.warning(f"Validation config file not found at: {config_file.absolute()}")
                logger.warning(f"Current working directory: {os.getcwd()}")
                logger.warning(f"Config directory contents: {list(config_file.parent.iterdir()) if config_file.parent.exists() else 'Directory does not exist'}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load validation config from {self.config_path}: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default validation configuration"""
        return {
            "global_rules": {
                "max_request_size": "10MB",
                "max_header_size": "8KB",
                "max_url_length": 2048,
                "allowed_content_types": [
                    "application/json",
                    "multipart/form-data",
                    "application/x-www-form-urlencoded"
                ],
                "required_headers": ["User-Agent"],
                "security_headers": {
                    "block_suspicious_user_agents": True,
                    "validate_origin": True
                }
            },
            "endpoint_rules": {},
            "security_rules": {
                "sql_injection": {"enabled": True},
                "xss_protection": {"enabled": True},
                "path_traversal": {"enabled": True},
                "command_injection": {"enabled": True}
            }
        }
    
    def _compile_patterns(self) -> Dict[str, List[re.Pattern]]:
        """Compile regex patterns for performance"""
        compiled = {}
        
        security_rules = self.config.get("security_rules", {})
        
        # SQL injection patterns
        sql_patterns = security_rules.get("sql_injection", {}).get("patterns", [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)",
            r"'.*OR.*=.*",
            r"UNION.*SELECT",
            r"--",
            r"/\*.*\*/"
        ])
        compiled["sql_injection"] = [re.compile(pattern, re.IGNORECASE) for pattern in sql_patterns]
        
        # XSS patterns
        xss_patterns = security_rules.get("xss_protection", {}).get("patterns", [
            r"<script",
            r"javascript:",
            r"onload=",
            r"onerror=",
            r"eval\("
        ])
        compiled["xss_protection"] = [re.compile(pattern, re.IGNORECASE) for pattern in xss_patterns]
        
        # Path traversal patterns
        path_patterns = security_rules.get("path_traversal", {}).get("patterns", [
            r"\.\.",
            r"/etc/passwd",
            r"windows/system32"
        ])
        compiled["path_traversal"] = [re.compile(pattern, re.IGNORECASE) for pattern in path_patterns]
        
        # Command injection patterns
        cmd_patterns = security_rules.get("command_injection", {}).get("patterns", [
            r";\s*(cat|ls|rm|wget|curl)\s+",
            r"\|\s*(cat|ls|rm)\s+",
            r"`.*`",
            r"\$\(.*\)"
        ])
        compiled["command_injection"] = [re.compile(pattern, re.IGNORECASE) for pattern in cmd_patterns]
        
        return compiled
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string (e.g., '10MB') to bytes"""
        if not size_str:
            return 0
        
        size_str = size_str.upper()
        multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        }
        
        for unit, multiplier in multipliers.items():
            if size_str.endswith(unit):
                number = size_str[:-len(unit)]
                try:
                    return int(float(number) * multiplier)
                except ValueError:
                    return 0
        
        # If no unit specified, assume bytes
        try:
            return int(size_str)
        except ValueError:
            return 0
    
    def _get_endpoint_config(self, request: Request) -> Dict[str, Any]:
        """Get validation configuration for specific endpoint"""
        path = request.url.path
        endpoint_rules = self.config.get("endpoint_rules", {})
        
        # Look for exact match first
        if path in endpoint_rules:
            return endpoint_rules[path]
        
        # Look for pattern matches
        for pattern, config in endpoint_rules.items():
            if self._match_endpoint_pattern(path, pattern):
                return config
        
        return {}
    
    def _match_endpoint_pattern(self, path: str, pattern: str) -> bool:
        """Match request path against endpoint pattern"""
        if pattern.endswith("/**"):
            base_pattern = pattern[:-3]
            return path.startswith(base_pattern)
        elif pattern.endswith("/*"):
            base_pattern = pattern[:-2]
            path_parts = path.split("/")
            pattern_parts = base_pattern.split("/")
            return len(path_parts) == len(pattern_parts) + 1 and path.startswith(base_pattern)
        else:
            return path == pattern
    
    def _validate_request_size(self, request: Request, config: Dict[str, Any]) -> List[ValidationError]:
        """Validate request size limits"""
        errors = []
        
        # Get size limits
        global_rules = self.config.get("global_rules", {})
        endpoint_config = self._get_endpoint_config(request)
        
        max_size_str = endpoint_config.get("max_request_size") or global_rules.get("max_request_size", "10MB")
        max_size = self._parse_size(max_size_str)
        
        # Check content-length header
        content_length_str = request.headers.get("content-length")
        if content_length_str:
            try:
                content_length = int(content_length_str)
                if content_length > max_size:
                    errors.append(ValidationError(
                        field="content-length",
                        message=f"Request size {content_length} bytes exceeds maximum {max_size} bytes",
                        value=content_length,
                        rule="max_request_size"
                    ))
            except ValueError:
                errors.append(ValidationError(
                    field="content-length",
                    message="Invalid content-length header",
                    value=content_length_str,
                    rule="content_length_format"
                ))
        
        # Check URL length
        max_url_length = global_rules.get("max_url_length", 2048)
        url_length = len(str(request.url))
        if url_length > max_url_length:
            errors.append(ValidationError(
                field="url",
                message=f"URL length {url_length} exceeds maximum {max_url_length}",
                value=str(request.url),
                rule="max_url_length"
            ))
        
        return errors
    
    def _validate_headers(self, request: Request, config: Dict[str, Any]) -> List[ValidationError]:
        """Validate request headers"""
        errors = []
        
        global_rules = self.config.get("global_rules", {})
        endpoint_config = self._get_endpoint_config(request)
        
        # Check required headers
        required_headers = endpoint_config.get("required_headers") or global_rules.get("required_headers", [])
        for header in required_headers:
            if header.lower() not in [h.lower() for h in request.headers.keys()]:
                errors.append(ValidationError(
                    field="headers",
                    message=f"Required header '{header}' is missing",
                    value=None,
                    rule="required_headers"
                ))
        
        # Check content type
        allowed_content_types = endpoint_config.get("allowed_content_types") or global_rules.get("allowed_content_types", [])
        content_type = request.headers.get("content-type", "").split(";")[0].strip()
        
        if request.method in ["POST", "PUT", "PATCH"] and content_type:
            if content_type not in allowed_content_types:
                errors.append(ValidationError(
                    field="content-type",
                    message=f"Content type '{content_type}' not allowed",
                    value=content_type,
                    rule="allowed_content_types"
                ))
        
        # Check suspicious user agents
        security_headers = global_rules.get("security_headers", {})
        if security_headers.get("block_suspicious_user_agents", True):
            user_agent = request.headers.get("user-agent", "")
            if detect_suspicious_user_agent(user_agent):
                errors.append(ValidationError(
                    field="user-agent",
                    message="Suspicious user agent detected",
                    value=user_agent,
                    rule="suspicious_user_agent"
                ))
        
        return errors
    
    def _validate_security_patterns(self, request: Request, body_data: Optional[str] = None) -> List[ValidationError]:
        """Validate against security attack patterns"""
        errors = []
        
        # Combine URL and query parameters for checking
        url_data = str(request.url.path) + str(request.query_params)
        
        # Check all data sources
        data_sources = [
            ("url", url_data),
            ("body", body_data or "")
        ]
        
        for source_name, data in data_sources:
            if not data:
                continue
                
            # Check SQL injection
            if self.config.get("security_rules", {}).get("sql_injection", {}).get("enabled", True):
                for pattern in self._compiled_patterns.get("sql_injection", []):
                    if pattern.search(data):
                        errors.append(ValidationError(
                            field=source_name,
                            message="Potential SQL injection detected",
                            value=data[:100] + "..." if len(data) > 100 else data,
                            rule="sql_injection"
                        ))
                        break
            
            # Check XSS
            if self.config.get("security_rules", {}).get("xss_protection", {}).get("enabled", True):
                for pattern in self._compiled_patterns.get("xss_protection", []):
                    if pattern.search(data):
                        errors.append(ValidationError(
                            field=source_name,
                            message="Potential XSS attack detected",
                            value=data[:100] + "..." if len(data) > 100 else data,
                            rule="xss_protection"
                        ))
                        break
            
            # Check path traversal
            if self.config.get("security_rules", {}).get("path_traversal", {}).get("enabled", True):
                for pattern in self._compiled_patterns.get("path_traversal", []):
                    if pattern.search(data):
                        errors.append(ValidationError(
                            field=source_name,
                            message="Potential path traversal detected",
                            value=data[:100] + "..." if len(data) > 100 else data,
                            rule="path_traversal"
                        ))
                        break
            
            # Check command injection
            if self.config.get("security_rules", {}).get("command_injection", {}).get("enabled", True):
                for pattern in self._compiled_patterns.get("command_injection", []):
                    if pattern.search(data):
                        errors.append(ValidationError(
                            field=source_name,
                            message="Potential command injection detected",
                            value=data[:100] + "..." if len(data) > 100 else data,
                            rule="command_injection"
                        ))
                        break
        
        return errors
    
    def _validate_field_rules(self, request: Request, body_data: Optional[Dict[str, Any]] = None) -> List[ValidationError]:
        """Validate specific field rules for endpoint"""
        errors = []
        
        endpoint_config = self._get_endpoint_config(request)
        field_validation = endpoint_config.get("field_validation", {})
        
        if not field_validation or not body_data:
            return errors
        
        # Check required fields
        required_fields = endpoint_config.get("required_fields", [])
        for field in required_fields:
            if field not in body_data:
                errors.append(ValidationError(
                    field=field,
                    message="This field is required",
                    value=None,
                    rule="required"
                ))
        
        # Validate individual fields
        for field_name, rules in field_validation.items():
            if field_name not in body_data:
                continue
                
            field_value = body_data[field_name]
            
            # Check required
            if rules.get("required", False) and not field_value:
                errors.append(ValidationError(
                    field=field_name,
                    message="This field is required",
                    value=field_value,
                    rule="required"
                ))
                continue
            
            # Skip validation if field is empty and not required
            if not field_value and not rules.get("required", False):
                continue
            
            # Check max length
            max_length = rules.get("max_length")
            if max_length and isinstance(field_value, str) and len(field_value) > max_length:
                errors.append(ValidationError(
                    field=field_name,
                    message=f"Field length {len(field_value)} exceeds maximum {max_length}",
                    value=field_value,
                    rule="max_length"
                ))
            
            # Check min length
            min_length = rules.get("min_length")
            if min_length and isinstance(field_value, str) and len(field_value) < min_length:
                errors.append(ValidationError(
                    field=field_name,
                    message=f"Field length {len(field_value)} is below minimum {min_length}",
                    value=field_value,
                    rule="min_length"
                ))
            
            # Check pattern
            pattern = rules.get("pattern")
            if pattern and isinstance(field_value, str):
                # Get pattern from global patterns if it's a reference
                global_patterns = self.config.get("global_rules", {}).get("patterns", {})
                if pattern in global_patterns:
                    pattern = global_patterns[pattern]
                
                if not re.match(pattern, field_value):
                    errors.append(ValidationError(
                        field=field_name,
                        message="Field format is invalid",
                        value=field_value,
                        rule="pattern"
                    ))
        
        return errors
    
    def _log_validation_failure(self, request: Request, validation_result: ValidationResult):
        """Log validation failure for monitoring"""
        client_ip = extract_client_ip(request)
        
        security_event = SecurityEvent(
            event_type=SecurityEventType.VALIDATION_FAILURE,
            user_id=getattr(request.state, 'user_id', None),
            store_id=getattr(request.state, 'store_id', None),
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            endpoint=request.url.path,
            method=request.method,
            threat_level=ThreatLevel.MEDIUM,
            details={
                "errors": [
                    {
                        "field": error.field,
                        "rule": error.rule,
                        "message": error.message
                    }
                    for error in validation_result.errors
                ]
            },
            trace_id=generate_trace_id()
        )
        
        logger.warning(f"Validation failed for {client_ip} on {request.method} {request.url.path}")
    
    async def dispatch(self, request: Request, call_next):
        """Main middleware dispatch method"""
        if not self.enabled:
            return await call_next(request)
        
        validation_errors = []
        
        # Validate request size and headers
        validation_errors.extend(self._validate_request_size(request, self.config))
        validation_errors.extend(self._validate_headers(request, self.config))
        
        # Read and validate body if present
        body_data = None
        body_str = None
        
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body_bytes = await request.body()
                if body_bytes:
                    body_str = body_bytes.decode('utf-8')
                    
                    # Try to parse JSON if content type is application/json
                    content_type = request.headers.get("content-type", "").split(";")[0].strip()
                    if content_type == "application/json":
                        try:
                            body_data = json.loads(body_str)
                        except json.JSONDecodeError as e:
                            validation_errors.append(ValidationError(
                                field="body",
                                message=f"Invalid JSON: {str(e)}",
                                value=body_str[:100] + "..." if len(body_str) > 100 else body_str,
                                rule="json_format"
                            ))
                            
            except Exception as e:
                validation_errors.append(ValidationError(
                    field="body",
                    message=f"Failed to read request body: {str(e)}",
                    value=None,
                    rule="body_read_error"
                ))
        
        # Validate security patterns
        validation_errors.extend(self._validate_security_patterns(request, body_str))
        
        # Validate field-specific rules
        if body_data:
            validation_errors.extend(self._validate_field_rules(request, body_data))
        
        # Check if validation passed
        validation_result = ValidationResult(
            valid=len(validation_errors) == 0,
            errors=validation_errors,
            sanitized_data=body_data
        )
        
        if not validation_result.valid:
            # Log validation failure
            self._log_validation_failure(request, validation_result)
            
            # Determine if we should return detailed errors
            error_handling = self.config.get("error_handling", {})
            return_detailed = error_handling.get("return_detailed_errors", False)
            
            if return_detailed:
                error_details = [
                    {
                        "field": error.field,
                        "message": error.message,
                        "rule": error.rule
                    }
                    for error in validation_errors
                ]
            else:
                error_details = [error_handling.get("generic_error_message", "Invalid input provided")]
            
            return JSONResponse(
                status_code=400,
                content={
                    "error": "Validation failed",
                    "details": error_details,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        # Store validation result in request state for downstream use
        request.state.validation_result = validation_result
        
        return await call_next(request) 