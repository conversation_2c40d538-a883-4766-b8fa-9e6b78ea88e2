import React from 'react';
import { Box, Typography } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';

interface MarketPositionTextProps {
  analysisText: string;
}

const MarketPositionText: React.FC<MarketPositionTextProps> = ({
  analysisText
}) => {
  const formatText = (text: string) => {
    return text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => {
        // Remove numbered prefixes
        line = line.replace(/^\d+\.\s+(.+)$/, '$1');
        
        // Remove asterisks
        line = line.replace(/\*\*([^*]+)\*\*/g, '$1');
        
        // Make section titles bold but keep the text after colon normal
        if (line.includes(':')) {
          return line.replace(/^[:-\s]*([^:]+):(.*)$/, (_, title, rest) => {
            return `<strong>${title}</strong>:${rest}`;
          });
        }

        return line;
      })
      .join('\n');
  };

  // Split the text into pricing and promotional sections
  const sections = analysisText.split('-').filter(Boolean);
  const pricingAnalysis = sections[0] ? sections[0].trim() : '';
  const promotionalStrategies = sections[1] ? sections[1].trim() : '';

  return (
    <Box sx={{
      mt: 4,
      p: 3,
      bgcolor: 'rgba(236, 242, 255, 0.5)',
      borderRadius: 2,
      border: '1px solid rgba(224, 231, 255, 0.8)'
    }}>
      {/* Pricing Analysis Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: '50%',
            bgcolor: '#0D6EFD',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mr: 2
          }}>
            <LocalOfferIcon sx={{ color: 'white' }} />
          </Box>
          <Typography variant="h6" sx={{ fontWeight: 500 }}>
            Pricing Analysis
          </Typography>
        </Box>
        <Typography
          component="div"
          sx={{
            color: 'text.secondary',
            lineHeight: 2,
            pl: 7,
            '& strong': {
              color: 'text.primary',
              fontWeight: 600
            }
          }}
          dangerouslySetInnerHTML={{ __html: formatText(pricingAnalysis).split('\n').map(line => `${line}<br/>`).join('') }}
        />
      </Box>

      {/* Promotional Strategies Section */}
      {promotionalStrategies && (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              bgcolor: '#0D6EFD',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2
            }}>
              <TrendingUpIcon sx={{ color: 'white' }} />
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 500 }}>
              Promotional Strategies
            </Typography>
          </Box>
          <Typography
            component="div"
            sx={{
              color: 'text.secondary',
              lineHeight: 2,
              pl: 7,
              '& strong': {
                color: 'text.primary',
                fontWeight: 600
              }
            }}
            dangerouslySetInnerHTML={{ __html: formatText(promotionalStrategies).split('\n').map(line => `${line}<br/>`).join('') }}
          />
        </Box>
      )}
    </Box>
  );
};

export default MarketPositionText; 