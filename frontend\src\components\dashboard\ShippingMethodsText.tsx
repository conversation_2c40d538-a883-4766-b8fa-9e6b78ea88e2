import React from 'react';
import { Box, Typography } from '@mui/material';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import InfoIcon from '@mui/icons-material/Info';

interface ShippingMethodsTextProps {
  methodName: string; // Used for identification, displayed in parent component
  timesUsed: number;
  methodAnalysis: string; // Detailed analysis passed from parent, used for context
  generalAnalysis: string;
}

const ShippingMethodsText: React.FC<ShippingMethodsTextProps> = ({ 
  // methodName - intentionally not used directly in this component but required for parent component integration
  timesUsed, 
  // methodAnalysis - intentionally not used directly but required for parent component integration
  generalAnalysis 
}) => {
  const formatContent = (text: string) => {
    if (!text) return null;

    const sections = text.split(/###\s+/).filter(Boolean);
    return sections.map((section, sectionIndex) => {
      const [title, ...content] = section.split('\n').filter(Boolean);
      
      // Skip the main title and conclusion sections
      if (title.includes('Analysis of Shipping Methods') || title.toLowerCase().includes('conclusion')) {
        return null;
      }

      return (
        <Box key={sectionIndex} sx={{ mb: 4, '&:last-child': { mb: 0 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{
              width: 24,
              height: 24,
              borderRadius: '50%',
              bgcolor: 'rgba(13, 110, 253, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              flexShrink: 0
            }}>
              <LocalShippingIcon sx={{ width: 16, height: 16, color: '#0D6EFD' }} />
            </Box>
            <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600 }}>
              {title.replace(/^\d+\.\s+/, '')}
            </Typography>
          </Box>

          <Box sx={{ ml: 6 }}>
            {content.map((line, lineIndex) => {
              // Handle bullet points
              const bulletMatch = line.match(/^-\s+(.+)/);
              if (bulletMatch) {
                let text = bulletMatch[1];
                // Handle bold text
                text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
                
                return (
                  <Typography 
                    key={`bullet-${lineIndex}`} 
                    variant="body2" 
                    sx={{ 
                      mb: 1.5,
                      color: 'text.secondary',
                      position: 'relative',
                      '&::before': {
                        content: '"•"',
                        position: 'absolute',
                        left: -12,
                        color: '#666'
                      },
                      '& strong': {
                        color: 'text.primary',
                        fontWeight: 600
                      }
                    }}
                    dangerouslySetInnerHTML={{ __html: text }}
                  />
                );
              }

              // Handle sub-bullet points
              const subBulletMatch = line.match(/^\s+-\s+(.+)/);
              if (subBulletMatch) {
                let text = subBulletMatch[1];
                // Handle bold text
                text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
                
                return (
                  <Typography 
                    key={`sub-bullet-${lineIndex}`} 
                    variant="body2" 
                    sx={{ 
                      ml: 2,
                      mb: 1.5,
                      color: 'text.secondary',
                      position: 'relative',
                      '&::before': {
                        content: '"-"',
                        position: 'absolute',
                        left: -12,
                        color: '#666'
                      },
                      '& strong': {
                        color: 'text.primary',
                        fontWeight: 600
                      }
                    }}
                    dangerouslySetInnerHTML={{ __html: text }}
                  />
                );
              }

              // Regular text
              return (
                <Typography 
                  key={`text-${lineIndex}`} 
                  variant="body2" 
                  sx={{ 
                    mb: 1.5,
                    color: 'text.secondary',
                    lineHeight: 1.6,
                    '& strong': {
                      color: 'text.primary',
                      fontWeight: 600
                    }
                  }}
                  dangerouslySetInnerHTML={{ 
                    __html: line.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>') 
                  }}
                />
              );
            })}
          </Box>
        </Box>
      );
    }).filter(Boolean);
  };

  // Extract conclusion from the general analysis
  const getConclusion = (text: string) => {
    if (!text) return '';
    const conclusionMatch = text.match(/Conclusion[:\s]+([\s\S]+)$/i);
    return conclusionMatch ? conclusionMatch[1].trim() : '';
  };

  return (
    <Box sx={{ mb: 4 }}>
      {/* Shipping Methods Counter */}
      <Box sx={{
        mt: 4,
        p: 3,
        bgcolor: 'rgba(236, 242, 255, 0.5)',
        borderRadius: 2,
        border: '1px solid rgba(224, 231, 255, 0.8)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: '50%',
            bgcolor: '#0D6EFD',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mr: 2
          }}>
            <LocalShippingIcon sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 500 }}>
              {timesUsed.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Shipments
            </Typography>
          </Box>
        </Box>

        {/* Analysis Content */}
        {formatContent(generalAnalysis)}

        {/* Conclusion Box */}
        {generalAnalysis && generalAnalysis.includes('Conclusion') && (
          <Box sx={{
            mt: 3,
            p: 2,
            bgcolor: 'white',
            borderRadius: 1,
            border: '1px solid rgba(224, 231, 255, 0.8)'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <Box sx={{
                width: 24,
                height: 24,
                borderRadius: '50%',
                bgcolor: 'rgba(13, 110, 253, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                mt: 0.5,
                flexShrink: 0
              }}>
                <InfoIcon sx={{ width: 16, height: 16, color: '#0D6EFD' }} />
              </Box>
              <Box>
                <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600, mb: 0.5 }}>
                  Conclusion
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary', lineHeight: 1.6 }}>
                  {getConclusion(generalAnalysis)}
                </Typography>
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ShippingMethodsText; 