import React, { useState, useRef, useEffect } from 'react';
import { Box, TextField, IconButton, Tooltip, Button, ButtonGroup } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import PsychologyIcon from '@mui/icons-material/Psychology';
import SearchIcon from '@mui/icons-material/Search';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import CancelIcon from '@mui/icons-material/Cancel';
import ExpandMoreIcon from '@mui/icons-material/KeyboardArrowDown';
import ExpandLessIcon from '@mui/icons-material/KeyboardArrowUp';
import Collapse from '@mui/material/Collapse';
import MetaPermissionsAlert from './MetaPermissionsAlert';
import { MetaPermissionKey } from '../../services/types';
import { useTranslation } from 'react-i18next';

interface ChatInputWrapperProps {
  onSendMessage: (message: string, mode?: string | null, image?: File | null) => void;
  onManagePermissions: () => void;
  loading: boolean;
  lastResponse?: {
    meta_permission_error?: boolean;
    missing_permissions?: MetaPermissionKey[];
    response?: string;
  };
}

export const ChatInputWrapper: React.FC<ChatInputWrapperProps> = ({
  onSendMessage,
  onManagePermissions,
  loading,
  lastResponse
}) => {
  const [input, setInput] = useState('');
  const [pendingMessage, ] = useState('');
  const [selectedMode, setSelectedMode] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { t } = useTranslation();

  // Collapse state synced with localStorage so it remembers across reloads
  // Default to collapsed (hidden) state
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    try {
      const stored = localStorage.getItem('chatInputCollapsed');
      const version = localStorage.getItem('chatInputVersion');

      // If this is the first time or version changed, reset to default (collapsed)
      if (version !== '2.0') {
        localStorage.setItem('chatInputVersion', '2.0');
        localStorage.setItem('chatInputCollapsed', '1');
        return true;
      }

      return stored !== null ? stored === '1' : true; // Default to true (collapsed)
    } catch {
      return true; // Default to collapsed
    }
  });

  // Flag to temporarily disable mouse tracking after manual toggle
  const [manualToggleDisabled, setManualToggleDisabled] = useState(false);

  const toggleCollapse = () => {
    setIsCollapsed((prev) => {
      const newVal = !prev;
      try {
        localStorage.setItem('chatInputCollapsed', newVal ? '1' : '0');
      } catch {/* ignore */}
      return newVal;
    });

    // Temporarily disable mouse tracking for 2 seconds after manual toggle
    setManualToggleDisabled(true);
    setTimeout(() => setManualToggleDisabled(false), 2000);
  };

  // Mouse tracking for auto-expand/collapse functionality (desktop only)
  useEffect(() => {
    // Only enable mouse tracking on desktop (screen width > 768px) and when not manually disabled
    const isDesktop = window.innerWidth > 768;
    if (!isDesktop || manualToggleDisabled) return;

    const BOTTOM_THRESHOLD = 120; // Distance from bottom to trigger expansion
    const COLLAPSE_THRESHOLD = 180; // Distance from bottom to trigger collapse
    let mouseTrackingTimeout: NodeJS.Timeout;

    const handleMouseMove = (e: MouseEvent) => {
      // Skip if manual toggle is disabled
      if (manualToggleDisabled) return;

      // Clear any existing timeout
      clearTimeout(mouseTrackingTimeout);

      // Add a small delay to prevent rapid toggling
      mouseTrackingTimeout = setTimeout(() => {
        const windowHeight = window.innerHeight;
        const distanceFromBottom = windowHeight - e.clientY;

        if (isCollapsed) {
          // When collapsed, expand if mouse is near bottom
          if (distanceFromBottom <= BOTTOM_THRESHOLD) {
            setIsCollapsed(false);
          }
        } else {
          // When expanded, collapse if mouse moves away from bottom
          if (distanceFromBottom > COLLAPSE_THRESHOLD) {
            setIsCollapsed(true);
          }
        }
      }, 100); // 100ms delay to prevent rapid toggling
    };

    // Add event listener
    window.addEventListener('mousemove', handleMouseMove);

    // Cleanup
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(mouseTrackingTimeout);
    };
  }, [isCollapsed, manualToggleDisabled]);

  // Check if the last response was a Meta permission error
  const hasMetaPermissionError = lastResponse?.meta_permission_error === true;
  const missingPermissions = lastResponse?.missing_permissions || [];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleSend = () => {
    if (!input.trim()) return;

    // Proceed with sending with mode and image
    onSendMessage(input, selectedMode, selectedImage);
    setInput('');
    
    // Reset image after sending
    if (selectedImage) {
      setSelectedImage(null);
      setImagePreviewUrl(null);
    }
    
    // Keep mode selected for next message (do not reset)
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Handle mode selection
  const handleModeSelect = (mode: string) => {
    // If the same mode is clicked again, toggle it off
    if (selectedMode === mode) {
      setSelectedMode(null);
    } else {
      setSelectedMode(mode);
    }
  };

  // Handle file input change
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle file input button click
  const handleFileButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle removing selected image
  const handleRemoveImage = () => {
    setSelectedImage(null);
    setImagePreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Box sx={{ mt: isCollapsed ? 0 : 2, width: '100%', maxWidth: { xs: '100%', md: '800px' } }}>
      {/* Collapse toggle button */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: isCollapsed ? 0 : 1 }}>
        <IconButton
          onClick={toggleCollapse}
          size="small"
          sx={{
            bgcolor: 'rgba(0,0,0,0.05)',
            '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },
            borderRadius: '50%',
            zIndex: 1501, // Ensure button stays above bottom nav on mobile
            position: 'relative',
            // Make button larger and more accessible on mobile
            width: { xs: '44px', sm: '32px' },
            height: { xs: '44px', sm: '32px' },
            // Add more padding for easier touch interaction
            p: { xs: 1.5, sm: 1 },
            // Ensure minimum touch target size on mobile
            minWidth: { xs: '44px', sm: 'auto' },
            minHeight: { xs: '44px', sm: 'auto' }
          }}
          aria-label={isCollapsed ? 'Expand input' : 'Collapse input'}
        >
          {isCollapsed ?
            <ExpandMoreIcon sx={{ fontSize: { xs: '1.5rem', sm: '1.25rem' } }} /> :
            <ExpandLessIcon sx={{ fontSize: { xs: '1.5rem', sm: '1.25rem' } }} />
          }
        </IconButton>
      </Box>

      <Collapse in={!isCollapsed} timeout="auto" unmountOnExit>
      {/* Show detailed Meta permission error if present in last response */}
      {hasMetaPermissionError && missingPermissions.length > 0 && (
        <MetaPermissionsAlert
          message={pendingMessage || input}
          missingPermissions={missingPermissions as MetaPermissionKey[]}
          onManagePermissions={onManagePermissions}
        />
      )}

      {/* Mode Selection Buttons */}
      <Box sx={{
        mb: 2,
        display: 'flex',
        justifyContent: 'center',
        px: { xs: 0.5, sm: 0 },
        overflow: 'hidden'
      }}>
        <ButtonGroup
          variant="outlined"
          aria-label="chat mode selection"
          sx={{
            borderRadius: '8px',
            width: { xs: '100%', sm: 'auto' },
            '& .MuiButton-root': {
              fontSize: { xs: '0.5rem', sm: '0.875rem' },
              px: { xs: 0.25, sm: 2 },
              py: { xs: 0.5, sm: 1 },
              minWidth: { xs: '0', sm: '64px' },
              flex: { xs: '1', sm: 'none' },
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              lineHeight: { xs: 1.2, sm: 1.5 }
            }
          }}
        >
          <Tooltip title={t('chat.actions.thinkTooltip')}>
            <Button
              onClick={() => handleModeSelect('think')}
              variant={selectedMode === 'think' ? 'contained' : 'outlined'}
              startIcon={<PsychologyIcon sx={{ fontSize: { xs: '0.7rem', sm: '1.25rem' } }} />}
              className={`mode-button ${selectedMode === 'think' ? 'active' : ''}`}
              sx={{
                '& .MuiButton-startIcon': {
                  mr: { xs: 0.25, sm: 1 },
                  ml: { xs: 0, sm: 0 }
                }
              }}
            >
              <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
                {t('chat.modeButton.think')}
              </Box>
              <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                Pensar
              </Box>
            </Button>
          </Tooltip>
          <Tooltip title={t('chat.actions.deepSearchTooltip')}>
            <Button
              onClick={() => handleModeSelect('deepsearch')}
              variant={selectedMode === 'deepsearch' ? 'contained' : 'outlined'}
              startIcon={<SearchIcon sx={{ fontSize: { xs: '0.7rem', sm: '1.25rem' } }} />}
              className={`mode-button ${selectedMode === 'deepsearch' ? 'active' : ''}`}
              sx={{
                '& .MuiButton-startIcon': {
                  mr: { xs: 0.25, sm: 1 },
                  ml: { xs: 0, sm: 0 }
                }
              }}
            >
              <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
                {t('chat.modeButton.deepSearch')}
              </Box>
              <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                Búsqueda
              </Box>
            </Button>
          </Tooltip>
          <Tooltip title={t('chat.actions.deeperSearchTooltip')}>
            <Button
              onClick={() => handleModeSelect('deepersearch')}
              variant={selectedMode === 'deepersearch' ? 'contained' : 'outlined'}
              startIcon={<ManageSearchIcon sx={{ fontSize: { xs: '0.7rem', sm: '1.25rem' } }} />}
              className={`mode-button ${selectedMode === 'deepersearch' ? 'active' : ''}`}
              sx={{
                '& .MuiButton-startIcon': {
                  mr: { xs: 0.25, sm: 1 },
                  ml: { xs: 0, sm: 0 }
                }
              }}
            >
              <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
                {t('chat.modeButton.deeperSearch')}
              </Box>
              <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                Más Profunda
              </Box>
            </Button>
          </Tooltip>
        </ButtonGroup>
      </Box>
      
      {/* Image Preview if an image is selected */}
      {imagePreviewUrl && (
        <Box sx={{ mb: 2, position: 'relative', width: 'fit-content', mx: 'auto' }}>
          <img 
            src={imagePreviewUrl} 
            alt="Selected" 
            style={{ 
              maxWidth: '100%', 
              maxHeight: '200px',
              borderRadius: '8px',
              border: '1px solid #e0e0e0'
            }} 
          />
          <IconButton 
            onClick={handleRemoveImage}
            sx={{ 
              position: 'absolute', 
              top: -10, 
              right: -10, 
              bgcolor: 'white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              '&:hover': { bgcolor: '#f5f5f5' } 
            }}
          >
            <CancelIcon color="error" fontSize="small" />
          </IconButton>
        </Box>
      )}
      
      {/* Input Field and Buttons */}
      <Box
        className="chat-input-container"
        sx={{
          display: 'flex',
          alignItems: 'center',
          bgcolor: 'var(--input-background)',
          p: { xs: 1, sm: 1.5 },
          '&:hover': {
            bgcolor: 'var(--input-background-hover)',
          },
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          gap: { xs: 0.5, sm: 1 }
        }}
      >
        {/* Image Upload Button */}
        <Tooltip title={t('chat.attachImageTooltip')}>
          <IconButton
            onClick={handleFileButtonClick}
            sx={{
              mr: { xs: 0.5, sm: 1 },
              color: '#00A3FF',
              '&:hover': { bgcolor: 'rgba(0, 163, 255, 0.1)' },
              p: { xs: 0.5, sm: 1 }
            }}
          >
            <AttachFileIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
          </IconButton>
        </Tooltip>
        <input 
          type="file" 
          ref={fileInputRef} 
          onChange={handleFileChange} 
          accept="image/*" 
          style={{ display: 'none' }} 
        />
        {/* Text Input Field Styling */}
        <TextField
          fullWidth
          variant="standard"
          multiline
          maxRows={6}
          value={input}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={t('chat.inputPlaceholder')}
          disabled={loading}
          InputProps={{
            disableUnderline: true,
            sx: {
              fontSize: { xs: '0.9rem', sm: '0.95rem' },
              borderRadius: '24px',
              px: { xs: 1.5, sm: 2 },
              py: { xs: 0.75, sm: 1 },
              '&::placeholder': {
                opacity: 0.6,
              },
            }
          }}
          sx={{
            bgcolor: 'transparent',
            mr: { xs: 0.5, sm: 1 },
            '& .MuiInput-root': {
              border: 'none',
              outline: 'none',
              boxShadow: 'none',
              borderRadius: '24px',
            }
          }}
        />
        {/* Send Button */}
        <IconButton
          className="send-button"
          disabled={!input.trim() || loading}
          onClick={handleSend}
          sx={{
            p: { xs: 0.5, sm: 1 }
          }}
        >
          <SendIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
        </IconButton>
      </Box>
      </Collapse>
    </Box>
  );
}; 



