"""
Service for storing and retrieving daily Meta/Instagram organic metrics
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta, timezone
from config.database import db_analysis

logger = logging.getLogger(__name__)

class MetaDailyMetricsService:
    """Service for managing daily organic metrics storage"""
    
    @staticmethod
    async def store_daily_metric(
        store_id: str,
        page_id: str,
        platform: str,
        metric_type: str,
        value: float,
        date: datetime,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store a daily metric value
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            metric_type: Type of metric (followers, reach, engagement, profile_views, impressions)
            value: Metric value
            date: Date for the metric
            metadata: Additional metadata
            
        Returns:
            Success boolean
        """
        try:
            # Ensure date is timezone-aware and at start of day
            if date.tzinfo is None:
                date = date.replace(tzinfo=timezone.utc)
            date = date.replace(hour=0, minute=0, second=0, microsecond=0)
            
            document = {
                "store_id": store_id,
                "page_id": page_id,
                "platform": platform,
                "metric_type": metric_type,
                "value": value,
                "date": date,
                "created_at": datetime.now(timezone.utc),
                "metadata": metadata or {}
            }
            
            # Use upsert to avoid duplicates for same day/metric
            await db_analysis["meta_daily_metrics"].update_one(
                {
                    "store_id": store_id,
                    "page_id": page_id,
                    "platform": platform,
                    "metric_type": metric_type,
                    "date": date
                },
                {"$set": document},
                upsert=True
            )
            
            logger.debug(f"Stored daily metric: {platform} {metric_type} = {value} for {date.date()}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing daily metric: {str(e)}")
            return False
    
    @staticmethod
    async def store_daily_metrics_batch(
        store_id: str,
        page_id: str,
        platform: str,
        metrics_data: Dict[str, List[Dict[str, Any]]]
    ) -> bool:
        """
        Store multiple daily metrics in batch
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            metrics_data: Dict with metric_type as key and list of {date, value, metadata} as values
            
        Returns:
            Success boolean
        """
        try:
            operations = []
            current_time = datetime.now(timezone.utc)
            
            for metric_type, daily_values in metrics_data.items():
                for daily_data in daily_values:
                    date = daily_data['date']
                    value = daily_data['value']
                    metadata = daily_data.get('metadata', {})
                    
                    # Ensure date is timezone-aware and at start of day
                    if date.tzinfo is None:
                        date = date.replace(tzinfo=timezone.utc)
                    date = date.replace(hour=0, minute=0, second=0, microsecond=0)
                    
                    document = {
                        "store_id": store_id,
                        "page_id": page_id,
                        "platform": platform,
                        "metric_type": metric_type,
                        "value": value,
                        "date": date,
                        "created_at": current_time,
                        "metadata": metadata
                    }
                    
                    operations.append({
                        "update_one": {
                            "filter": {
                                "store_id": store_id,
                                "page_id": page_id,
                                "platform": platform,
                                "metric_type": metric_type,
                                "date": date
                            },
                            "update": {"$set": document},
                            "upsert": True
                        }
                    })
            
            if operations:
                result = await db_analysis["meta_daily_metrics"].bulk_write(operations)
                logger.info(f"Stored {len(operations)} daily metrics: {result.upserted_count} new, {result.modified_count} updated")
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing daily metrics batch: {str(e)}")
            return False
    
    @staticmethod
    async def get_daily_metrics(
        store_id: str,
        page_id: str,
        platform: str,
        metric_type: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """
        Retrieve daily metrics for a specific metric type and date range
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            metric_type: Type of metric to retrieve
            start_date: Start date
            end_date: End date
            
        Returns:
            List of daily metric documents
        """
        try:
            # Ensure dates are timezone-aware
            if start_date.tzinfo is None:
                start_date = start_date.replace(tzinfo=timezone.utc)
            if end_date.tzinfo is None:
                end_date = end_date.replace(tzinfo=timezone.utc)
            
            cursor = db_analysis["meta_daily_metrics"].find({
                "store_id": store_id,
                "page_id": page_id,
                "platform": platform,
                "metric_type": metric_type,
                "date": {
                    "$gte": start_date.replace(hour=0, minute=0, second=0, microsecond=0),
                    "$lte": end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                }
            }).sort("date", 1)
            
            metrics = await cursor.to_list(length=None)
            logger.debug(f"Retrieved {len(metrics)} daily metrics for {metric_type}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error retrieving daily metrics: {str(e)}")
            return []
    
    @staticmethod
    async def get_all_metrics_for_period(
        store_id: str,
        page_id: str,
        platform: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Retrieve all metrics for a specific page and date range
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            start_date: Start date
            end_date: End date
            
        Returns:
            Dict with metric_type as key and list of daily values
        """
        try:
            # Ensure dates are timezone-aware
            if start_date.tzinfo is None:
                start_date = start_date.replace(tzinfo=timezone.utc)
            if end_date.tzinfo is None:
                end_date = end_date.replace(tzinfo=timezone.utc)
            
            cursor = db_analysis["meta_daily_metrics"].find({
                "store_id": store_id,
                "page_id": page_id,
                "platform": platform,
                "date": {
                    "$gte": start_date.replace(hour=0, minute=0, second=0, microsecond=0),
                    "$lte": end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                }
            }).sort("date", 1)
            
            all_metrics = await cursor.to_list(length=None)
            
            # Group by metric type
            grouped_metrics = {}
            for metric in all_metrics:
                metric_type = metric['metric_type']
                if metric_type not in grouped_metrics:
                    grouped_metrics[metric_type] = []
                grouped_metrics[metric_type].append(metric)
            
            logger.debug(f"Retrieved {len(all_metrics)} total daily metrics grouped into {len(grouped_metrics)} metric types")
            return grouped_metrics
            
        except Exception as e:
            logger.error(f"Error retrieving all metrics for period: {str(e)}")
            return {}
    
    @staticmethod
    async def get_latest_metric_value(
        store_id: str,
        page_id: str,
        platform: str,
        metric_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get the most recent value for a specific metric
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            metric_type: Type of metric
            
        Returns:
            Latest metric document or None
        """
        try:
            metric = await db_analysis["meta_daily_metrics"].find_one(
                {
                    "store_id": store_id,
                    "page_id": page_id,
                    "platform": platform,
                    "metric_type": metric_type
                },
                sort=[("date", -1)]
            )
            
            return metric
            
        except Exception as e:
            logger.error(f"Error getting latest metric value: {str(e)}")
            return None
    
    @staticmethod
    async def calculate_metric_trends(
        store_id: str,
        page_id: str,
        platform: str,
        metric_type: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Calculate trends for a specific metric over the last N days
        
        Args:
            store_id: Store identifier
            page_id: Facebook/Instagram page ID
            platform: 'facebook' or 'instagram'
            metric_type: Type of metric
            days: Number of days to analyze
            
        Returns:
            Trend analysis including growth rate, average, etc.
        """
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            metrics = await MetaDailyMetricsService.get_daily_metrics(
                store_id, page_id, platform, metric_type, start_date, end_date
            )
            
            if len(metrics) < 2:
                return {"error": "Insufficient data for trend analysis"}
            
            values = [m['value'] for m in metrics]
            first_value = values[0]
            last_value = values[-1]
            
            # Calculate growth rate
            growth_rate = 0
            if first_value > 0:
                growth_rate = ((last_value - first_value) / first_value) * 100
            
            # Calculate average
            average = sum(values) / len(values)
            
            # Calculate daily change average
            daily_changes = []
            for i in range(1, len(values)):
                daily_changes.append(values[i] - values[i-1])
            
            avg_daily_change = sum(daily_changes) / len(daily_changes) if daily_changes else 0
            
            return {
                "metric_type": metric_type,
                "period_days": days,
                "first_value": first_value,
                "last_value": last_value,
                "growth_rate": growth_rate,
                "average": average,
                "avg_daily_change": avg_daily_change,
                "total_change": last_value - first_value,
                "data_points": len(metrics)
            }
            
        except Exception as e:
            logger.error(f"Error calculating metric trends: {str(e)}")
            return {"error": str(e)}

    @staticmethod
    async def create_indexes():
        """Create necessary indexes for the daily metrics collection"""
        try:
            # Composite index for efficient queries
            await db_analysis["meta_daily_metrics"].create_index([
                ("store_id", 1),
                ("page_id", 1),
                ("platform", 1),
                ("metric_type", 1),
                ("date", -1)
            ])
            
            # Index for date range queries
            await db_analysis["meta_daily_metrics"].create_index([
                ("date", -1)
            ])
            
            # Index for store queries
            await db_analysis["meta_daily_metrics"].create_index([
                ("store_id", 1)
            ])
            
            logger.info("Created indexes for meta_daily_metrics collection")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")