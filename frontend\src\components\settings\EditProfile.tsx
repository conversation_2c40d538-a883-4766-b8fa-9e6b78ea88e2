import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { authService } from '../../services/authService';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';
import { useSecureForm } from '../../hooks/useSecureForm';
import { fetchWithDeduplication } from '../../services/apiService';
import {
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Grid,
  Paper,
  CircularProgress,
} from '@mui/material';

interface ProfileData {
  name: string;
  email: string;
  last_name: string;
  dni: string;
  business_type: string;
  tax_id: string;
  address: string;
  contact_name: string;
  phone: string;
  website: string;
  [key: string]: unknown;
}

interface UpdateProfileData {
  name: string;
  last_name: string;
  dni: string;
  business_type: string;
  tax_id: string;
  address: string;
  contact_name: string;
  phone: string;
  website: string;
}

interface UserResponse {
  id_user: number;
  name: string;
  email: string;
  last_name: string;
  dni: string;
  business_type: string;
  tax_id: string;
  address: string;
  contact_name: string;
  phone: string;
  website: string;
}

const EditProfile = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { isSubmitting, isRecovering, lastError, submitSecureForm, clearError } = useSecureForm();
  const [formData, setFormData] = useState<ProfileData>({
    name: '',
    email: '',
    last_name: '',
    dni: '',
    business_type: '',
    tax_id: '',
    address: '',
    contact_name: '',
    phone: '',
    website: ''
  });
  const [message, setMessage] = useState<{ type: string; text: string }>({ type: '', text: '' });

  // Validation states
  const [validationErrors, setValidationErrors] = useState({
    name: '',
    last_name: '',
    contact_name: '',
    business_type: '',
    tax_id: '',
    phone: '',
    dni: '',
    website: '',
    address: ''
  });

  // Add refs for focus management
  const fieldRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  // Validation functions
  const validateCompanyName = (value: string): string => {
    if (!value.trim()) return t('settings.validation.companyNameRequired', 'Company name is required');
    if (value.length < 2) return t('settings.validation.companyNameTooShort', 'Company name must be at least 2 characters');
    if (value.length > 100) return t('settings.validation.companyNameTooLong', 'Company name must not exceed 100 characters');
    if (!/^[a-zA-Z0-9\s'.,-]+$/.test(value)) return t('settings.validation.companyNameInvalidChars', 'Company name contains invalid characters');
    return '';
  };

  const validateContactName = (value: string): string => {
    if (!value.trim()) return t('settings.validation.contactNameRequired', 'Contact name is required');
    if (value.length < 2) return t('settings.validation.contactNameTooShort', 'Contact name must be at least 2 characters');
    if (value.length > 50) return t('settings.validation.contactNameTooLong', 'Contact name must not exceed 50 characters');
    if (!/^[a-zA-Z\s'-]+$/.test(value)) return t('settings.validation.contactNameInvalidChars', 'Contact name can only contain letters, spaces, hyphens, and apostrophes');
    return '';
  };

  const validateBusinessType = (value: string): string => {
    if (!value.trim()) return t('settings.validation.businessTypeRequired', 'Business type is required');
    if (value.length < 2) return t('settings.validation.businessTypeTooShort', 'Business type must be at least 2 characters');
    if (value.length > 50) return t('settings.validation.businessTypeTooLong', 'Business type must not exceed 50 characters');
    if (!/^[a-zA-Z\s-]+$/.test(value)) return t('settings.validation.businessTypeInvalidChars', 'Business type can only contain letters, spaces, and hyphens');
    return '';
  };

  const validateTaxId = (value: string): string => {
    if (!value.trim()) return t('settings.validation.taxIdRequired', 'Tax ID is required');
    if (value.length < 8) return t('settings.validation.taxIdTooShort', 'Tax ID must be at least 8 characters');
    if (value.length > 20) return t('settings.validation.taxIdTooLong', 'Tax ID must not exceed 20 characters');
    if (!/^[a-zA-Z0-9-]+$/.test(value)) return t('settings.validation.taxIdInvalidChars', 'Tax ID can only contain letters, numbers, and hyphens');
    return '';
  };

  const validatePhone = (value: string): string => {
    if (!value.trim()) return t('settings.validation.phoneRequired', 'Phone number is required');
    if (value.length < 10) return t('settings.validation.phoneTooShort', 'Phone number must be at least 10 characters');
    if (value.length > 18) return t('settings.validation.phoneTooLong', 'Phone number must not exceed 18 characters');
    if (!/^[+]?[0-9\s()-]+$/.test(value)) return t('settings.validation.phoneInvalid', 'Please enter a valid phone number');
    return '';
  };

  const validateDni = (value: string): string => {
    if (!value.trim()) return t('settings.validation.dniRequired', 'DNI is required');
    if (value.length < 7) return t('settings.validation.dniTooShort', 'DNI must be at least 7 characters');
    if (value.length > 12) return t('settings.validation.dniTooLong', 'DNI must not exceed 12 characters');
    if (!/^[a-zA-Z0-9]+$/.test(value)) return t('settings.validation.dniInvalidChars', 'DNI can only contain letters and numbers');
    return '';
  };

  const validateWebsite = (value: string): string => {
    if (value && value.trim()) {
      if (value.length > 255) return t('settings.validation.websiteTooLong', 'Website URL must not exceed 255 characters');
      if (!/^https?:\/\/.+\..+/.test(value)) return t('settings.validation.websiteInvalid', 'Please enter a valid website URL (including http:// or https://)');
    }
    return '';
  };

  const validateAddress = (value: string): string => {
    if (!value.trim()) return t('settings.validation.addressRequired', 'Address is required');
    if (value.length < 5) return t('settings.validation.addressTooShort', 'Address must be at least 5 characters');
    if (value.length > 200) return t('settings.validation.addressTooLong', 'Address must not exceed 200 characters');
    if (!/^[a-zA-Z0-9\s.,#/-]+$/.test(value)) return t('settings.validation.addressInvalidChars', 'Address contains invalid characters');
    return '';
  };

  // Validation handler
  const handleFieldValidation = (field: keyof typeof validationErrors) => (value: string) => {
    let error = '';
    switch (field) {
      case 'name':
        error = validateCompanyName(value);
        break;
      case 'contact_name':
        error = validateContactName(value);
        break;
      case 'business_type':
        error = validateBusinessType(value);
        break;
      case 'tax_id':
        error = validateTaxId(value);
        break;
      case 'phone':
        error = validatePhone(value);
        break;
      case 'dni':
        error = validateDni(value);
        break;
      case 'website':
        error = validateWebsite(value);
        break;
      case 'address':
        error = validateAddress(value);
        break;
    }
    setValidationErrors(prev => ({ ...prev, [field]: error }));
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        if (!user?.id_store) {
          throw new Error('No store ID available');
        }

        const data: UserResponse = await fetchWithDeduplication<UserResponse>(
          `/api/users/${user.id_store}`,
          {
            headers: {
              'Authorization': `Bearer ${authService.getToken()}`
            }
          }
        );
        setFormData({
          name: data.name || '',
          email: data.email || '',
          last_name: data.last_name || '',
          dni: data.dni || '',
          business_type: data.business_type || '',
          tax_id: data.tax_id || '',
          address: data.address || '',
          contact_name: data.contact_name || '',
          phone: data.phone || '',
          website: data.website || ''
        });
      } catch (error) {
        logger.error('Error fetching user data:', error);
        setMessage({ type: 'error', text: 'Error loading user data' });
      }
    };

    if (user?.id_store) {
      fetchUserData();
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear any existing messages
    setMessage({ type: '', text: '' });
    
    // Trigger validation for the field
    if (name in validationErrors) {
      handleFieldValidation(name as keyof typeof validationErrors)(value);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields before submission
    const nameError = validateCompanyName(formData.name);
    const contactNameError = validateContactName(formData.contact_name);
    const businessTypeError = validateBusinessType(formData.business_type);
    const taxIdError = validateTaxId(formData.tax_id);
    const phoneError = validatePhone(formData.phone);
    const dniError = validateDni(formData.dni);
    const websiteError = validateWebsite(formData.website);
    const addressError = validateAddress(formData.address);

    const newValidationErrors = {
      name: nameError,
      last_name: '', // Not validated in this form
      contact_name: contactNameError,
      business_type: businessTypeError,
      tax_id: taxIdError,
      phone: phoneError,
      dni: dniError,
      website: websiteError,
      address: addressError
    };

    setValidationErrors(newValidationErrors);

    // Check if there are any validation errors
    const hasErrors = Object.values(newValidationErrors).some(error => error !== '');
    if (hasErrors) {
      setMessage({ type: 'error', text: t('settings.companyProfile.validationError', 'Please fix the validation errors before submitting') });
      
      // Focus on the first field with an error
      const fieldOrder = ['name', 'business_type', 'tax_id', 'address', 'dni', 'contact_name', 'phone', 'website'];
      for (const fieldName of fieldOrder) {
        if (newValidationErrors[fieldName as keyof typeof newValidationErrors]) {
          const fieldRef = fieldRefs.current[fieldName];
          if (fieldRef) {
            fieldRef.focus();
            break;
          }
        }
      }
      return;
    }

    setMessage({ type: '', text: '' });
    clearError();

    try {
      if (!user?.id_store) {
        throw new Error('No store ID available');
      }

      // Get the user's id_user from the API first
      const userData: UserResponse = await fetchWithDeduplication<UserResponse>(
        `/api/users/${user.id_store}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );

      // Now send the update request using secure form
      await submitSecureForm<UpdateProfileData, UserResponse>({
        url: `/api/users/${userData.id_user}`,
        method: 'PUT',
        data: {
          name: formData.name,
          last_name: formData.last_name,
          dni: formData.dni,
          business_type: formData.business_type,
          tax_id: formData.tax_id,
          address: formData.address,
          contact_name: formData.contact_name,
          phone: formData.phone,
          website: formData.website
        },
        onSuccess: (response: unknown) => {
          const updatedData = response as UserResponse;
          setMessage({ type: 'success', text: 'Profile updated successfully' });
          // Update form data with the response
          setFormData(prev => ({
            ...prev,
            ...updatedData
          }));
        },
        onError: (error: unknown) => {
          const errorMessage = typeof error === 'string' ? error : 'Failed to update profile';
          setMessage({ type: 'error', text: lastError || errorMessage });
        }
      });
    } catch (error) {
      logger.error('Error updating profile:', error);
      setMessage({ type: 'error', text: lastError || (error instanceof Error ? error.message : 'Error updating profile') });
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Company Profile
      </Typography>
      
      {message.text && (
        <Alert severity={message.type as 'error' | 'success'} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Company Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('name')(formData.name)}
                fullWidth
                required
                autoComplete="organization"
                error={!!validationErrors.name}
                helperText={validationErrors.name}
                inputProps={{
                  maxLength: 100
                }}
                ref={(el) => (fieldRefs.current['name'] = el)}
              />
              <TextField
                label="Business Type"
                name="business_type"
                value={formData.business_type}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('business_type')(formData.business_type)}
                fullWidth
                required
                autoComplete="off"
                error={!!validationErrors.business_type}
                helperText={validationErrors.business_type}
                inputProps={{
                  maxLength: 50
                }}
                ref={(el) => (fieldRefs.current['business_type'] = el)}
              />
              <TextField
                label="Tax ID"
                name="tax_id"
                value={formData.tax_id}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('tax_id')(formData.tax_id)}
                fullWidth
                required
                autoComplete="off"
                error={!!validationErrors.tax_id}
                helperText={validationErrors.tax_id}
                inputProps={{
                  maxLength: 20
                }}
                ref={(el) => (fieldRefs.current['tax_id'] = el)}
              />
              <TextField
                label="Address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('address')(formData.address)}
                fullWidth
                multiline
                rows={2}
                required
                autoComplete="street-address"
                error={!!validationErrors.address}
                helperText={validationErrors.address}
                inputProps={{
                  maxLength: 200
                }}
                ref={(el) => (fieldRefs.current['address'] = el)}
              />
              <TextField
                label="DNI"
                name="dni"
                value={formData.dni}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('dni')(formData.dni)}
                fullWidth
                required
                autoComplete="off"
                error={!!validationErrors.dni}
                helperText={validationErrors.dni}
                inputProps={{
                  maxLength: 12
                }}
                ref={(el) => (fieldRefs.current['dni'] = el)}
              />
            </Box>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Contact Name"
                name="contact_name"
                value={formData.contact_name}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('contact_name')(formData.contact_name)}
                fullWidth
                required
                autoComplete="name"
                error={!!validationErrors.contact_name}
                helperText={validationErrors.contact_name}
                inputProps={{
                  maxLength: 50
                }}
                ref={(el) => (fieldRefs.current['contact_name'] = el)}
              />
              <TextField
                label="Email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                fullWidth
                disabled
                autoComplete="email"
              />
              <TextField
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('phone')(formData.phone)}
                fullWidth
                required
                autoComplete="tel"
                error={!!validationErrors.phone}
                helperText={validationErrors.phone}
                inputProps={{
                  maxLength: 18
                }}
                ref={(el) => (fieldRefs.current['phone'] = el)}
              />
              <TextField
                label="Website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                onBlur={() => handleFieldValidation('website')(formData.website)}
                fullWidth
                autoComplete="url"
                error={!!validationErrors.website}
                helperText={validationErrors.website}
                inputProps={{
                  maxLength: 255
                }}
                ref={(el) => (fieldRefs.current['website'] = el)}
              />
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isSubmitting || isRecovering || Object.values(validationErrors).some(error => error !== '')}
            startIcon={(isSubmitting || isRecovering) ? <CircularProgress size={20} /> : undefined}
          >
            {isRecovering ? 'Updating security...' : isSubmitting ? 'Updating...' : 'Update Profile'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default EditProfile; 