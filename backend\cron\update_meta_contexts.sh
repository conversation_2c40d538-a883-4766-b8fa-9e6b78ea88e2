#!/bin/bash
# <PERSON>ript to update meta_chat_context for all stores in D-Unit-AnalysisGPT
# This script should be scheduled to run daily using cron
# Example crontab entry:
# 0 3 * * * /path/to/update_meta_contexts.sh >> /path/to/logs/meta_update.log 2>&1

# Change to the project directory
PROJECT_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
cd "$PROJECT_DIR"

# Activate virtual environment if needed
# source /path/to/venv/bin/activate

# Run the update script
echo "Starting meta_chat_context update in D-Unit-AnalysisGPT at $(date)"
python update_meta_contexts_scheduled.py

# Log completion
echo "Completed meta_chat_context update at $(date)"
exit 0 
