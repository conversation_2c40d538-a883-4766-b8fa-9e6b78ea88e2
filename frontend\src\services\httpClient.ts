import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { authService } from './authService';
import { csrfService } from './csrfService';
import { logger } from '../utils/logger';
import { API_URL } from '../config/api';
import { DISABLE_CSRF } from '../config/api';

/**
 * Type definition for request data that covers all legitimate use cases
 * while maintaining type safety and avoiding 'any'
 */
type RequestData = 
  | Record<string, unknown>  // JSON objects
  | FormData                 // File uploads  
  | URLSearchParams         // Form-encoded data
  | string                  // Raw strings
  | number                  // Numeric data
  | boolean                 // Boolean data
  | null                    // No data
  | undefined;              // No data

/**
 * Production-ready HTTP client with automatic CSRF token handling
 * Supports both regular requests and FormData uploads
 */
class HttpClient {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_URL,
      timeout: 330000, // 330 second timeout
      withCredentials: true, // Enable sending cookies with cross-origin requests
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - Add auth and CSRF tokens
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Add authentication token
        const authToken = authService.getToken();
        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        }

        // Add CSRF token for protected methods
        const protectedMethods = ['post', 'put', 'patch', 'delete'];
        const authExemptPaths = [
          '/api/auth/token',
          '/api/auth/google',
          '/api/auth/meta',
          '/api/auth/dunit/register',
          '/api/auth/dunit/verify-registration',
          '/api/auth/forgot-password',
          '/api/auth/2fa/verify-login',
          '/api/auth/csrf-token'
        ];

        const isAuthEndpoint = authExemptPaths.some(path => 
          config.url?.includes(path)
        );

        if (config.method && 
            protectedMethods.includes(config.method.toLowerCase()) && 
            !isAuthEndpoint &&
            authToken) {
          if (DISABLE_CSRF) {
            return config;
          }
          try {
            const csrfHeaders = await csrfService.getHeaders();
            Object.assign(config.headers, csrfHeaders);
            logger.debug('CSRF token added to request:', config.url);
          } catch (error) {
            logger.warn('Failed to add CSRF token to request:', error);
            // Continue without CSRF token for graceful degradation
          }
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - Handle errors and CSRF issues
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Handle authentication errors
          if (window.location.pathname !== '/login') {
            authService.clearTokens();
            localStorage.removeItem('user');
            window.location.href = '/login';
          }
        } else if (error.response?.status === 403) {
          if (DISABLE_CSRF) {
            return Promise.reject(error);
          }
          // Handle CSRF token errors
          const errorData = error.response.data;
          if (errorData && (
            errorData.error === 'CSRF token missing' || 
            errorData.error === 'Invalid CSRF token'
          )) {
            logger.warn('CSRF token error detected, clearing token');
            csrfService.clearToken();
            // Don't retry automatically to avoid infinite loops
          }
        } else if (error.response?.status === 402) {
          // Insufficient credits – surface friendly message using translation key
          return Promise.reject(new Error('errors.insufficientCredits'));
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a standard HTTP request
   */
  async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.request(config);
      return response.data;
    } catch (error) {
      logger.error('HTTP request failed:', error);
      throw error;
    }
  }

  /**
   * Upload FormData with automatic CSRF token handling
   * Perfect for chat requests with images
   */
  async uploadFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...config?.headers,
          // Don't set Content-Type for FormData - let browser set it with boundary
        },
      });
      return response.data;
    } catch (error) {
      logger.error('FormData upload failed:', error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }
}

// Export singleton instance
export const httpClient = new HttpClient();
export default httpClient; 