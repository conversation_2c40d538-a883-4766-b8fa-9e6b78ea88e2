import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Divider } from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import { PERMISSION_DESCRIPTIONS, MetaPermissionKey } from "../../services/types";
import { useTranslation } from 'react-i18next';

// Define which permissions are Instagram-specific vs Facebook-specific
const INSTAGRAM_PERMISSIONS = [
  'instagram_basic',
  'instagram_manage_insights',
  'instagram_manage_comments',
  'instagram_branded_content_brand'
];

// Mapping between permission keys and human-readable descriptions
const PERMISSION_IMPACT_MAP: Record<MetaPermissionKey, string> = {
  'instagram_basic': 'Basic Instagram account information and follower counts',
  'instagram_manage_insights': 'Instagram reach, impressions, and detailed performance data',
  'instagram_manage_comments': 'Instagram comments and engagement metrics',
  'instagram_branded_content_brand': 'Branded content and partnership information',
  'pages_read_engagement': 'Engagement metrics for Facebook/Instagram pages',
  'pages_read_user_content': 'User content and interaction data',
  'ads_read': 'Basic advertising metrics and performance data',
  'ads_management': 'Detailed ad campaign management and statistics'
};

interface MetaPermissionsAlertProps {
  message: string;
  missingPermissions: MetaPermissionKey[];
  onManagePermissions: () => void;
}

const MetaPermissionsAlert: React.FC<MetaPermissionsAlertProps> = ({
  message,
  missingPermissions,
  onManagePermissions
}) => {
  const { t } = useTranslation();
  // Determine if we're dealing with Instagram or Facebook permissions
  const hasInstagramPermissions = missingPermissions.some(p => INSTAGRAM_PERMISSIONS.includes(p));
  const platform = hasInstagramPermissions ? 'Instagram' : 'Meta';
  const PlatformIcon = hasInstagramPermissions ? InstagramIcon : FacebookIcon;
  const iconColor = hasInstagramPermissions ? '#C13584' : '#1877F2';
  const buttonColor = hasInstagramPermissions ? '#C13584' : '#1877F2';
  const buttonHoverColor = hasInstagramPermissions ? '#A1286A' : '#0B5FBF';
  const bgColor = `rgba(${hasInstagramPermissions ? '193, 53, 132' : '24, 119, 242'}, 0.05)`;
  const borderColor = `rgba(${hasInstagramPermissions ? '193, 53, 132' : '24, 119, 242'}, 0.1)`;

  return (
    <Box sx={{ mb: 3, mt: 2 }}>
      <Alert 
        severity="warning" 
        icon={<PlatformIcon sx={{ color: iconColor }} />}
        sx={{ 
          mb: 2,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          {platform} Data Unavailable
        </Typography>
        
        {message && (
          <Typography 
            variant="body2" 
            sx={{ 
              mb: 2, 
              p: 1.5, 
              bgcolor: bgColor, 
              borderRadius: 1,
              border: `1px solid ${borderColor}`
            }}
          >
            Your query: "{message}"
          </Typography>
        )}

        <Typography variant="body2" paragraph>
          You've revoked some {platform} permissions needed to access this information:
        </Typography>
        
        <Box sx={{ mb: 2, pl: 2 }}>
          {missingPermissions.map(permission => (
            <Typography 
              key={permission} 
              variant="body2" 
              sx={{ mb: 0.5, display: 'flex', alignItems: 'flex-start' }}
            >
              <Box component="span" sx={{ mr: 1, mt: '3px' }}>•</Box>
              <Box>
                <strong>{PERMISSION_DESCRIPTIONS[permission]?.nameKey ? t(PERMISSION_DESCRIPTIONS[permission]!.nameKey) : permission}</strong>
                <Typography 
                  component="div" 
                  variant="body2" 
                  color="text.secondary" 
                  sx={{ mt: 0.5 }}
                >
                  {PERMISSION_IMPACT_MAP[permission] ||
                   (PERMISSION_DESCRIPTIONS[permission]?.impactKey ? t(PERMISSION_DESCRIPTIONS[permission]?.impactKey) : undefined) ||
                   'Access to related data'}
                </Typography>
              </Box>
            </Typography>
          ))}
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ bgcolor: bgColor, p: 2, borderRadius: 1, mb: 2 }}>
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            Currently unavailable data:
          </Typography>
          
          <Box component="ul" sx={{ pl: 2, mb: 0 }}>
            {missingPermissions.includes('instagram_basic') && (
              <>
                <Typography component="li" variant="body2">Instagram profile information</Typography>
                <Typography component="li" variant="body2">Follower counts</Typography>
                <Typography component="li" variant="body2">Post counts and basic metrics</Typography>
              </>
            )}
            
            {missingPermissions.includes('instagram_manage_insights') && (
              <>
                <Typography component="li" variant="body2">Reach and impression data</Typography>
                <Typography component="li" variant="body2">Audience demographics and insights</Typography>
                <Typography component="li" variant="body2">Detailed post performance statistics</Typography>
              </>
            )}
            
            {missingPermissions.includes('instagram_manage_comments') && (
              <>
                <Typography component="li" variant="body2">Comment counts and content</Typography>
                <Typography component="li" variant="body2">User engagement data</Typography>
                <Typography component="li" variant="body2">Comment metrics and trends</Typography>
              </>
            )}
            
            {missingPermissions.includes('instagram_branded_content_brand') && (
              <>
                <Typography component="li" variant="body2">Branded content information</Typography>
                <Typography component="li" variant="body2">Sponsor and partnership data</Typography>
                <Typography component="li" variant="body2">Brand collaboration metrics</Typography>
              </>
            )}
            
            {missingPermissions.includes('ads_read') && (
              <>
                <Typography component="li" variant="body2">Ad performance metrics</Typography>
                <Typography component="li" variant="body2">Campaign overview data</Typography>
                <Typography component="li" variant="body2">Ad spend and ROI information</Typography>
              </>
            )}
          </Box>
        </Box>
        
        <Typography variant="body2" paragraph>
          To access this data, you'll need to reconnect and grant the missing permissions in the Meta Dashboard.
        </Typography>
        
        <Button 
          variant="contained" 
          size="small" 
          onClick={onManagePermissions}
          sx={{ bgcolor: buttonColor, '&:hover': { bgcolor: buttonHoverColor } }}
        >
          Manage Meta Permissions
        </Button>
      </Alert>
    </Box>
  );
};

export default MetaPermissionsAlert; 