import React from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent 
} from '@mui/material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend
} from 'recharts';
import ChartContainer from '../common/ChartContainer';

interface PriceHistoryData {
  date: string;
  our_price: number;
  competitor_avg: number;
}

interface MarketPositionChartProps {
  priceHistory: PriceHistoryData[];
  title?: string;
  height?: number;
}

/**
 * A component for visualizing market position and price trends
 */
export const MarketPositionChart: React.FC<MarketPositionChartProps> = ({
  priceHistory,
  title = 'Price Trends',
  height = 300
}) => {
  return (
    <Card elevation={1} sx={{ mt: 2 }}>
      <CardContent>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        
        <Box sx={{ height: height, width: '100%' }}>
          <ChartContainer width="100%" height="100%">
            <LineChart
              data={priceHistory}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip 
                formatter={(value) => [`${value.toLocaleString()} UYU`, '']}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="our_price" 
                name="Our Price" 
                stroke="#8884d8" 
                activeDot={{ r: 8 }} 
              />
              <Line 
                type="monotone" 
                dataKey="competitor_avg" 
                name="Competitor Avg" 
                stroke="#82ca9d" 
              />
            </LineChart>
          </ChartContainer>
        </Box>
      </CardContent>
    </Card>
  );
}; 