import React from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  Area,
  ComposedChart,
  Line
} from 'recharts';
import { Box, Typography } from '@mui/material';
import ChartContainer from '../common/ChartContainer';

interface ForecastValue {
  date: string;
  value: number;
}

interface ConfidenceInterval {
  date: string;
  lower: number;
  upper: number;
}

interface ForecastChartProps {
  forecastValues: ForecastValue[];
  confidenceIntervals?: ConfidenceInterval[];
  historicalData?: ForecastValue[];
  metricName: string;
  title?: string;
}

// Interface for chart data points
interface ChartDataPoint {
  date: string;
  historical: number | null;
  forecast: number | null;
  lower?: number | null;
  upper?: number | null;
}

/**
 * Component for displaying forecast data with confidence intervals
 */
export const ForecastChart: React.FC<ForecastChartProps> = ({ 
  forecastValues,
  confidenceIntervals,
  historicalData = [],
  metricName,
  title
}) => {
  // Combine historical and forecast data
  const prepareData = () => {
    const data: ChartDataPoint[] = [];
    
    // Add historical data
    historicalData.forEach(item => {
      data.push({
        date: new Date(item.date).toLocaleDateString(),
        historical: item.value,
        forecast: null,
        lower: null,
        upper: null
      });
    });
    
    // Add forecast data
    forecastValues.forEach((item, index) => {
      const dataPoint: ChartDataPoint = {
        date: new Date(item.date).toLocaleDateString(),
        historical: null,
        forecast: item.value
      };
      
      // Add confidence intervals if available
      if (confidenceIntervals && confidenceIntervals[index]) {
        dataPoint.lower = confidenceIntervals[index].lower;
        dataPoint.upper = confidenceIntervals[index].upper;
      }
      
      data.push(dataPoint);
    });
    
    return data;
  };
  
  const data = prepareData();
  
  return (
    <Box sx={{ width: '100%', height: 400, p: 2 }}>
      <Typography variant="h6" gutterBottom>
        {title || `${metricName} Forecast`}
      </Typography>
      <ChartContainer width="100%" height="90%">
        <ComposedChart
          data={data}
          margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            label={{ value: 'Date', position: 'insideBottom', offset: -10 }}
          />
          <YAxis 
            label={{ value: metricName, angle: -90, position: 'insideLeft' }}
          />
          <Tooltip />
          <Legend />
          
          {/* Historical data line */}
          <Line 
            type="monotone" 
            dataKey="historical" 
            stroke="#8884d8" 
            name="Historical" 
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          {/* Forecast data line */}
          <Line 
            type="monotone" 
            dataKey="forecast" 
            stroke="#82ca9d" 
            name="Forecast" 
            strokeWidth={2}
            strokeDasharray="5 5"
            dot={{ r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          {/* Confidence interval area */}
          {confidenceIntervals && (
            <Area 
              type="monotone" 
              dataKey="upper" 
              stroke="transparent" 
              fill="#82ca9d" 
              fillOpacity={0.2}
              name="Upper Bound"
            />
          )}
          
          {confidenceIntervals && (
            <Area 
              type="monotone" 
              dataKey="lower" 
              stroke="transparent" 
              fill="#82ca9d" 
              fillOpacity={0.2}
              name="Lower Bound"
            />
          )}
        </ComposedChart>
      </ChartContainer>
    </Box>
  );
}; 