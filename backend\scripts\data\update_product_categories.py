import os
import sys
import logging
import mysql.connector
import time
import json
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional, cast, Set
import argparse
import pymongo

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    PRODUCT_DETAILS_CACHE_COLLECTION = 'product_details_cache'
    
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    
    # Add constants for store filtering to match update_active_stores.py
    INACTIVE_STORE_EXCEPTIONS = getattr(settings, 'INACTIVE_STORE_EXCEPTIONS', [])
    NEW_STORE_CUTOFF_YEAR = getattr(settings, 'NEW_STORE_CUTOFF_YEAR', 2025)
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# Default batch processing threshold
DEFAULT_BATCH_THRESHOLD = 200  # Lowered from 500 to 200
# Failed stores log file
FAILED_STORES_LOG = os.path.join(os.path.dirname(__file__), 'failed_category_updates.json')

# --- Helper Functions ---

def get_mysql_connection():
    """Establish and return a MySQL connection."""
    try:
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB_LANUBE,
            port=MYSQL_PORT,
            connect_timeout=10
        )
        logger.info("MySQL connection successful")
        return mysql_conn
    except mysql.connector.Error as err:
        logger.error(f"MySQL connection error: {err}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error establishing MySQL connection: {e}", exc_info=True)
        return None

def get_mongodb_connection_with_timeouts(socket_timeout_ms=30000, connect_timeout_ms=30000):
    """Get MongoDB connection with specific timeouts.
    
    This is a wrapper around the existing get_mongodb_connection function that
    properly sets MongoDB client timeout parameters without modifying the original function.
    """
    try:
        logger.info(f"Attempting MongoDB connection with timeouts: socket={socket_timeout_ms}ms, connect={connect_timeout_ms}ms")
        
        # Get MongoDB credentials from environment
        mongodb_connection = settings.MONGODB_CONNECTION
        if not mongodb_connection:
            logger.error("MongoDB connection string not found in environment variables")
            return None
        
        # Create MongoDB client directly with our custom timeouts
        client = pymongo.MongoClient(
            mongodb_connection,
            serverSelectionTimeoutMS=60000,  # 1 minute
            connectTimeoutMS=connect_timeout_ms,
            socketTimeoutMS=socket_timeout_ms,
            maxPoolSize=50,
            retryWrites=True,
            retryReads=True
        )
        
        # Test the connection
        client.admin.command('ping')
        logger.info("Successfully connected to MongoDB with custom timeouts")
        
        return client
    except Exception as e:
        logger.error(f"Failed to establish MongoDB connection with timeouts: {e}", exc_info=True)
        # Fall back to default connection as a last resort
        logger.info("Attempting connection with default timeout settings...")
        return get_mongodb_connection()

def get_stores_to_process(mysql_conn) -> List[Dict[str, Any]]:
    """
    Fetches active stores from the MySQL database, using the same criteria as
    update_active_stores.py to ensure consistency.
    """
    stores: List[Dict[str, Any]] = []
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available for get_stores_to_process.")
        return stores
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            exception_clause = f"OR s.id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
            
            query = f"""
            SELECT 
                s.id_store, s.symbol, s.name
            FROM stores s
            WHERE 
                (s.active = 1
                {exception_clause}
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
            ORDER BY s.id_store
            """
            cursor.execute(query)
            stores = cursor.fetchall()
            logger.info(f"Found {len(stores)} stores to process using consistent criteria.")
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching stores: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching stores from MySQL: {e}", exc_info=True)
    return stores

def parse_grouped_field(grouped_field: Optional[str], separator: str = ',') -> List[str]:
    """Parse a GROUP_CONCAT result into a list."""
    if not grouped_field:
        return []
    return [item.strip() for item in grouped_field.split(separator)]

def load_failed_stores() -> Set[int]:
    """Load previously failed store IDs from log file."""
    failed_stores = set()
    if os.path.exists(FAILED_STORES_LOG):
        try:
            with open(FAILED_STORES_LOG, 'r') as f:
                data = json.load(f)
                # Convert string IDs to integers
                failed_stores = set(int(store_id) for store_id in data.get('failed_stores', []))
            logger.info(f"Loaded {len(failed_stores)} previously failed stores from log")
        except Exception as e:
            logger.error(f"Error loading failed stores log: {e}", exc_info=True)
    return failed_stores

def save_failed_stores(failed_stores: Set[int]):
    """Save failed store IDs to log file."""
    try:
        # Convert integer IDs to strings for JSON serialization
        data = {
            'failed_stores': list(str(store_id) for store_id in failed_stores),
            'timestamp': datetime.now().isoformat()
        }
        with open(FAILED_STORES_LOG, 'w') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Saved {len(failed_stores)} failed stores to log")
    except Exception as e:
        logger.error(f"Error saving failed stores log: {e}", exc_info=True)

# --- Main Product Categories Functions ---

def get_product_categories_for_store(store_id: int, mysql_conn) -> Dict[str, Dict[str, Any]]:
    """
    Fetches all product category data for a specific store from MySQL.
    Only includes categories for ACTIVE, NON-DELETED products.
    Returns a dictionary with product_id as key and category info as value.
    """
    product_categories: Dict[str, Dict[str, Any]] = {}
    
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error(f"MySQL connection is not available for get_product_categories_for_store (store: {store_id}).")
        return product_categories
    
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # First, get statistics about products
            stats_query = """
            SELECT 
                COUNT(DISTINCT p.id_product) as total_products,
                COUNT(DISTINCT CASE WHEN p.active = 1 AND p.deleted_at IS NULL THEN p.id_product END) as active_products,
                COUNT(DISTINCT CASE WHEN p.active = 0 THEN p.id_product END) as inactive_products,
                COUNT(DISTINCT CASE WHEN p.deleted_at IS NOT NULL THEN p.id_product END) as deleted_products
            FROM products p
            WHERE p.id_store = %s
            """
            cursor.execute(stats_query, (store_id,))
            stats = cursor.fetchone()
            
            if stats:
                logger.info(f"Store {store_id} product stats: "
                           f"{stats['active_products']} active products (out of {stats['total_products']} total, "
                           f"{stats['inactive_products']} inactive, {stats['deleted_products']} deleted)")
            
            # Updated query to include only active, non-deleted products
            query = """
            SELECT 
                p.id_product,
                p.featured,
                p.offer,
                
                -- Assigned Store Categories (linked via products_subcategories)
                GROUP_CONCAT(DISTINCT CASE WHEN ps.active = 1 THEN sc.id_category END) AS store_category_ids,
                GROUP_CONCAT(DISTINCT CASE WHEN ps.active = 1 THEN sc.name END) AS store_category_names,
                
                -- Assigned Subcategories (linked via products_subcategories)
                GROUP_CONCAT(DISTINCT CASE WHEN ps.active = 1 THEN ssc.id_subcategory END) AS subcategory_ids,
                GROUP_CONCAT(DISTINCT CASE WHEN ps.active = 1 THEN ssc.name END) AS subcategory_names,
                
                -- Main Category (from products table)
                p.id_category AS main_category_id,
                c_primary.name AS main_category_name
                
            FROM products p
            -- Link product to its category/subcategory assignments
            LEFT JOIN products_subcategories ps ON p.id_product = ps.id_product
            -- Get assigned store category name
            LEFT JOIN store_categories sc ON ps.id_category = sc.id_category AND sc.id_store = p.id_store
            -- Get assigned store subcategory name
            LEFT JOIN store_subcategories ssc ON ps.id_subcategory = ssc.id_subcategory AND ssc.id_store = p.id_store
            -- Get primary category name from products table
            LEFT JOIN categories c_primary ON p.id_category = c_primary.id_category
            WHERE p.id_store = %s 
                AND p.active = 1              -- Only active products
                AND p.status = 1              -- Only online/visible products
                AND p.deleted_at IS NULL      -- Exclude soft-deleted products
            GROUP BY p.id_product
            """
            
            cursor.execute(query, (store_id,))
            results = cursor.fetchall()
            
            logger.info(f"Fetched category data for {len(results)} active products from store {store_id}")
            
            for row in results:
                # Cast each row to Dict to satisfy the type checker
                row_dict = cast(Dict[str, Any], row)
                
                product_id = str(row_dict.get('id_product', ''))
                
                if not product_id:
                    continue
                    
                # Parse grouped fields into lists
                store_category_ids = parse_grouped_field(row_dict.get('store_category_ids'))
                store_category_names = parse_grouped_field(row_dict.get('store_category_names'))
                subcategory_ids = parse_grouped_field(row_dict.get('subcategory_ids'))
                subcategory_names = parse_grouped_field(row_dict.get('subcategory_names'))
                
                # Create store categories list
                store_categories = []
                for i in range(min(len(store_category_ids), len(store_category_names))):
                    store_categories.append({
                        'id': store_category_ids[i],
                        'name': store_category_names[i]
                    })
                
                # Create subcategories list
                subcategories = []
                for i in range(min(len(subcategory_ids), len(subcategory_names))):
                    if subcategory_names[i]:  # Only add if name is not empty
                        subcategories.append({
                            'id': subcategory_ids[i],
                            'name': subcategory_names[i]
                        })
                
                # Create primary category object
                primary_category = None
                if row_dict.get('main_category_id') and row_dict.get('main_category_name'):
                    primary_category = {
                        'id': row_dict.get('main_category_id'),
                        'name': row_dict.get('main_category_name')
                    }
                
                # Create category data object
                product_categories[product_id] = {
                    'is_featured': bool(row_dict.get('featured', 0)),
                    'is_on_sale': bool(row_dict.get('offer', 0)),
                    'store_categories': store_categories,
                    'subcategories': subcategories,
                    'primary_category': primary_category
                }
            
            logger.info(f"Structured category data for {len(product_categories)} active products for store {store_id}")
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching product categories for store {store_id}: {err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching product categories for store {store_id}: {e}", exc_info=True)
    
    return product_categories

def calculate_category_metrics(store_id: int, product_categories: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate store-level category metrics from product category data.
    
    Returns:
    - Dict with category metrics and summaries
    """
    # Initialize counters
    category_counts = {}
    featured_category_counts = {}
    sale_category_counts = {}
    subcategory_counts = {}
    
    # Track which products are in each category
    products_by_category = {}
    
    # Process each product
    for product_id, category_data in product_categories.items():
        is_featured = category_data.get('is_featured', False)
        is_on_sale = category_data.get('is_on_sale', False)
        
        # Process store categories
        for category in category_data.get('store_categories', []):
            category_name = category.get('name', '')
            category_id = category.get('id', '')
            
            if not category_name:
                continue
                
            # Initialize if first time seeing this category
            if category_name not in category_counts:
                category_counts[category_name] = 0
                featured_category_counts[category_name] = 0
                sale_category_counts[category_name] = 0
                products_by_category[category_name] = set()
            
            # Increment counters
            category_counts[category_name] += 1
            products_by_category[category_name].add(product_id)
            
            if is_featured:
                featured_category_counts[category_name] += 1
                
            if is_on_sale:
                sale_category_counts[category_name] += 1
        
        # Process subcategories
        for subcategory in category_data.get('subcategories', []):
            subcategory_name = subcategory.get('name', '')
            
            if not subcategory_name:
                continue
                
            if subcategory_name not in subcategory_counts:
                subcategory_counts[subcategory_name] = 0
                
            subcategory_counts[subcategory_name] += 1
    
    # Get top categories by product count
    top_categories = [
        {"name": name, "product_count": count}
        for name, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    ]
    
    # Get top categories with featured products
    top_featured_categories = [
        {"name": name, "featured_count": count}
        for name, count in sorted(featured_category_counts.items(), key=lambda x: x[1], reverse=True)
        if count > 0
    ][:5]
    
    # Get top categories with sale products
    top_sale_categories = [
        {"name": name, "sale_count": count}
        for name, count in sorted(sale_category_counts.items(), key=lambda x: x[1], reverse=True)
        if count > 0
    ][:5]
    
    # Get category distribution (percentage of products in each category)
    total_products = len(product_categories)
    category_distribution = {}
    if total_products > 0:
        for category, count in category_counts.items():
            category_distribution[category] = round((count / total_products) * 100, 2)
    
    # Assemble the metrics
    category_metrics = {
        "total_products_with_categories": total_products,
        "unique_categories": len(category_counts),
        "unique_subcategories": len(subcategory_counts),
        "top_categories": top_categories,
        "top_featured_categories": top_featured_categories,
        "top_sale_categories": top_sale_categories,
        "category_distribution": category_distribution,
        "active_products_only": True  # Flag to indicate these metrics are for active products only
    }
    
    logger.info(f"Calculated category metrics for store {store_id}: {len(category_counts)} categories, {len(subcategory_counts)} subcategories")
    
    return category_metrics

def update_cache_with_categories(store_id: int, product_categories: Dict[str, Dict[str, Any]], 
                                 category_metrics: Dict[str, Any], mongo_db) -> bool:
    """
    Updates the product_details_cache collection with category data.
    Adds category data to each product and store-level metrics.
    
    Parameters:
    - store_id: The store ID
    - product_categories: Dictionary mapping product_id to category info
    - category_metrics: Store-level category summary metrics
    - mongo_db: MongoDB database connection
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Check if mongo_db is None first
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Fetch the existing document for this store
        existing_doc = collection.find_one({"_id": str(store_id)})
        
        if existing_doc is None:
            logger.warning(f"Cache document for store {store_id} not found. Categories cannot be added. Run update_product_details.py first?")
            return False
        
        if 'products' not in existing_doc or not isinstance(existing_doc['products'], list):
            logger.error(f"Cache document for store {store_id} does not have a valid 'products' array.")
            return False
        
        # Check if the cache contains only active products
        active_products_only = existing_doc.get('active_products_only', False)
        if not active_products_only:
            logger.warning(f"Cache document for store {store_id} may contain inactive products. "
                         f"Consider running update_product_details.py with the latest version first.")
        
        # Iterate through products and add category data
        modified_count = 0
        products_without_categories = 0
        
        for product_dict in existing_doc['products']:
            if not isinstance(product_dict, dict):
                continue
                
            product_id = str(product_dict.get('product_id', ''))
            
            if not product_id:
                continue
                
            # Look up category data for this product
            category_data = product_categories.get(product_id)
            
            if category_data:
                # Add category data to the product dictionary
                product_dict['categories'] = category_data
                modified_count += 1
            else:
                products_without_categories += 1
        
        # Update the document in MongoDB
        update_result = collection.update_one(
            {"_id": str(store_id)},
            {
                "$set": {
                    "products": existing_doc['products'],
                    "category_summary": category_metrics,
                    "categories_last_updated": datetime.now(timezone.utc),
                    "categories_active_only": True  # Flag to indicate categories are for active products only
                }
            }
        )
        
        if update_result.modified_count > 0:
            logger.info(f"Updated product details cache with category data for store {store_id}. "
                       f"Modified {modified_count} products with categories, "
                       f"{products_without_categories} products have no categories.")
        else:
            logger.info(f"No changes needed for product details cache for store {store_id} (data identical or no products with categories).")
        
        return update_result.acknowledged
        
    except Exception as e:
        logger.error(f"Error updating MongoDB cache with categories for store {store_id}: {e}", exc_info=True)
        return False

def update_cache_with_categories_batched(store_id: int, product_categories: Dict[str, Dict[str, Any]], 
                                       category_metrics: Dict[str, Any], mongo_db) -> bool:
    """
    Updates the product_details_cache collection with category data in batches to avoid timeouts.
    Used for stores with very large numbers of products.
    
    Parameters:
    - store_id: The store ID
    - product_categories: Dictionary mapping product_id to category info
    - category_metrics: Store-level category summary metrics
    - mongo_db: MongoDB database connection
    
    Returns:
    - bool: True if successful, False otherwise
    """
    try:
        # Check if mongo_db is None first
        if mongo_db is None:
            logger.error(f"MongoDB client/db object is None for store {store_id}. Cannot update cache.")
            return False
            
        # Get the collection
        collection = mongo_db[PRODUCT_DETAILS_CACHE_COLLECTION]
        
        # Instead of fetching the entire document, only get the product count and flags
        pipeline = [
            {"$match": {"_id": str(store_id)}},
            {"$project": {
                "_id": 0, 
                "product_count": {"$size": {"$ifNull": ["$products", []]}},
                "active_products_only": 1
            }}
        ]
        count_result = list(collection.aggregate(pipeline))
        if not count_result or 'product_count' not in count_result[0]:
            logger.error(f"Could not retrieve product count for store {store_id}. Skipping.")
            return False
        
        product_count = count_result[0]['product_count']
        active_products_only = count_result[0].get('active_products_only', False)
        
        # Check if the cache contains only active products
        if not active_products_only:
            logger.warning(f"Cache document for store {store_id} may contain inactive products. "
                         f"Consider running update_product_details.py with the latest version first.")
        
        if product_count == 0:
            logger.info(f"Store {store_id} has no products in the cache document. Skipping category update.")
            # Update timestamp and summary if needed, even with 0 products
            collection.update_one(
                {"_id": str(store_id)},
                {"$set": {
                    "category_summary": category_metrics,
                    "categories_last_updated": datetime.now(timezone.utc),
                    "categories_active_only": True
                }}
            )
            return True
        
        # Process products in batches of 50
        batch_size = 50
        total_batches = (product_count + batch_size - 1) // batch_size  # Ceiling division
        modified_count = 0
        products_without_categories = 0
        success = True
        
        logger.info(f"Processing {product_count} products in {total_batches} batches for store {store_id}")
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            
            # Fetch only the slice of products needed for this batch
            batch_doc = collection.find_one(
                {"_id": str(store_id)},
                {"_id": 0, "products": {"$slice": [start_idx, batch_size]}}
            )
            
            if not batch_doc or 'products' not in batch_doc:
                logger.warning(f"Could not fetch product batch {batch_num + 1}/{total_batches} for store {store_id}. Skipping batch.")
                time.sleep(1)  # Still sleep before next attempt
                continue
                
            batch_products = batch_doc['products']
            logger.info(f"Processing batch {batch_num + 1}/{total_batches} (products {start_idx} to {start_idx + len(batch_products) - 1}) for store {store_id}")
            
            # Update products in this batch
            update_operations = {}
            batch_modified_count = 0
            
            for i, product_dict in enumerate(batch_products):
                if not isinstance(product_dict, dict):
                    continue
                    
                product_id = str(product_dict.get('product_id', ''))
                
                if not product_id:
                    continue
                    
                # Look up category data for this product
                category_data = product_categories.get(product_id)
                
                if category_data:
                    # Update the dictionary in memory
                    product_dict['categories'] = category_data
                    # Prepare the $set operation for MongoDB using the absolute index
                    update_operations[f"products.{start_idx + i}.categories"] = category_data
                    batch_modified_count += 1
                    modified_count += 1
                else:
                    products_without_categories += 1
            
            # Add store-level summary and timestamp ONCE, e.g., on the last batch
            if batch_num == total_batches - 1:
                update_operations["category_summary"] = category_metrics
                update_operations["categories_last_updated"] = datetime.now(timezone.utc)
                update_operations["categories_active_only"] = True
            
            # Only perform update if there are changes in this batch
            if update_operations:
                update_result = collection.update_one(
                    {"_id": str(store_id)},
                    {"$set": update_operations}
                )
                
                if not update_result.acknowledged:
                    logger.error(f"Failed to update batch {batch_num + 1} for store {store_id}")
                    success = False
                else:
                    logger.info(f"Batch {batch_num + 1} update acknowledged. {batch_modified_count} products in batch had categories added.")
            else:
                logger.info(f"Batch {batch_num + 1} for store {store_id} had no products needing category updates.")
            
            # Small delay between batches to prevent overloading the database
            time.sleep(1)
        
        logger.info(f"Completed updating {modified_count} products with categories for store {store_id} in {total_batches} batches. "
                   f"{products_without_categories} products have no categories.")
        return success
        
    except Exception as e:
        logger.error(f"Error updating MongoDB cache with categories for store {store_id} in batches: {e}", exc_info=True)
        return False

def process_store_with_retry(store_id: int, store_name: str, mysql_conn, mongo_db, batch_threshold: int, max_retries: int = 3) -> bool:
    """
    Process a single store with retry capability.
    
    Parameters:
    - store_id: The store ID
    - store_name: Store name for logging
    - mysql_conn: MySQL connection
    - mongo_db: MongoDB database connection
    - batch_threshold: Product count threshold for using batched processing
    - max_retries: Maximum number of retry attempts
    
    Returns:
    - bool: True if successful, False otherwise
    """
    logger.info(f"--- Processing store ID: {store_id} ({store_name}) ---")
    
    # Check if this store has split products (large store fix)
    collection = mongo_db['product_details_cache']
    store_doc = collection.find_one({"_id": str(store_id)}, {"products_in_separate_collection": 1, "product_count": 1})
    
    if store_doc and store_doc.get('products_in_separate_collection', False):
        logger.info(f"Store {store_id} has products in separate collection due to size limits - skipping category update")
        logger.info(f"This store needs manual category processing due to MongoDB document size limits")
        return True  # Skip processing but don't fail the pipeline
    
    for attempt in range(1, max_retries + 1):
        try:
            if attempt > 1:
                # Add exponential backoff for retries
                backoff_time = 2 ** (attempt - 1)
                logger.info(f"Retry attempt {attempt}/{max_retries} for store {store_id} after {backoff_time}s delay")
                time.sleep(backoff_time)
            
            # Get product categories for this store
            product_categories = get_product_categories_for_store(store_id, mysql_conn)
            
            # Only update if we have category data
            if not product_categories:
                logger.info(f"No category data found for active products in store {store_id}")
                return True  # Not a failure, just no data
                
            # Calculate category metrics
            category_metrics = calculate_category_metrics(store_id, product_categories)
            
            # Check if this is a large store (more than batch_threshold products)
            product_count = len(product_categories)
            logger.info(f"Store {store_id} has {product_count} active products with category data")
            
            # Update MongoDB cache with categories - use batched version for large stores
            if product_count > batch_threshold:
                logger.info(f"Using batched processing for store {store_id} due to large product count ({product_count})")
                success = update_cache_with_categories_batched(store_id, product_categories, category_metrics, mongo_db)
            else:
                success = update_cache_with_categories(store_id, product_categories, category_metrics, mongo_db)
            
            if success:
                logger.info(f"Successfully updated categories for store {store_id}")
                return True
            else:
                logger.warning(f"Failed to update categories for store {store_id} on attempt {attempt}")
                # Continue to next retry attempt
                
        except Exception as e:
            logger.error(f"Error processing store {store_id} on attempt {attempt}: {e}", exc_info=True)
            # Continue to next retry attempt
    
    # If we reach here, all attempts failed
    logger.error(f"All {max_retries} attempts failed for store {store_id}")
    return False

def show_product_categories(product_id: str, store_id: Optional[int], mysql_conn):
    """
    Look up and display categories for a specific product ID.
    
    Parameters:
    - product_id: The ID of the product to look up
    - store_id: The store ID (optional, if None will search across all stores)
    - mysql_conn: MySQL connection
    """
    if not mysql_conn or not mysql_conn.is_connected():
        logger.error("MySQL connection is not available")
        return
        
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # If store_id is provided, limit to that store
            store_filter = f"AND p.id_store = {store_id}" if store_id else ""
            
            query = f"""
            SELECT 
                p.id_product,
                p.name AS product_name,
                s.id_store,
                s.name AS store_name,
                p.featured,
                p.offer,
                p.active,
                p.deleted_at,
                
                -- Store Categories
                GROUP_CONCAT(DISTINCT sc.id_category) AS store_category_ids,
                GROUP_CONCAT(DISTINCT sc.name) AS store_category_names,
                
                -- Subcategories
                GROUP_CONCAT(DISTINCT ps.id_subcategory) AS subcategory_ids,
                GROUP_CONCAT(DISTINCT sub.name) AS subcategory_names,
                
                -- Main Category (from products table)
                p.id_category AS main_category_id,
                c.name AS main_category_name
                
            FROM products p
            JOIN stores s ON p.id_store = s.id_store
            LEFT JOIN store_categories sc ON sc.id_store = p.id_store
            LEFT JOIN products_subcategories ps ON p.id_product = ps.id_product
            LEFT JOIN store_subcategories sub ON ps.id_subcategory = sub.id_subcategory AND sub.id_store = p.id_store
            LEFT JOIN categories c ON p.id_category = c.id_category
            WHERE p.id_product = %s {store_filter}
            GROUP BY p.id_product
            """
            
            cursor.execute(query, (product_id,))
            result = cursor.fetchone()
            
            if not result:
                logger.info(f"No product found with ID {product_id}")
                return
                
            # Print product and category information
            logger.debug("\n========== PRODUCT CATEGORY DETAILS ==========")
            logger.debug(f"Product ID: {result['id_product']}")
            logger.debug(f"Product Name: {result['product_name']}")
            logger.debug(f"Store ID: {result['id_store']}")
            logger.debug(f"Store Name: {result['store_name']}")
            logger.debug(f"Active: {'Yes' if result['active'] else 'No'}")
            logger.debug(f"Deleted: {'Yes' if result['deleted_at'] else 'No'}")
            logger.debug(f"Featured: {'Yes' if result['featured'] else 'No'}")
            logger.debug(f"On Sale: {'Yes' if result['offer'] else 'No'}")
            logger.debug("\n---------- CATEGORIES ----------")
            
            # Parse and print store categories
            store_category_ids = parse_grouped_field(result.get('store_category_ids'))
            store_category_names = parse_grouped_field(result.get('store_category_names'))
            
            logger.debug("\nStore Categories:")
            if store_category_names:
                for i in range(min(len(store_category_ids), len(store_category_names))):
                    logger.debug(f"  • {store_category_names[i]} (ID: {store_category_ids[i]})")
            else:
                logger.debug("  None")
                
            # Parse and print subcategories
            subcategory_ids = parse_grouped_field(result.get('subcategory_ids'))
            subcategory_names = parse_grouped_field(result.get('subcategory_names'))
            
            logger.debug("\nSubcategories:")
            if subcategory_names:
                for i in range(min(len(subcategory_ids), len(subcategory_names))):
                    if subcategory_names[i]:  # Only add if name is not empty
                        logger.debug(f"  • {subcategory_names[i]} (ID: {subcategory_ids[i]})")
            else:
                logger.debug("  None")
                
            # Print primary category
            logger.debug("\nPrimary Category:")
            if result.get('main_category_id') and result.get('main_category_name'):
                logger.debug(f"  • {result['main_category_name']} (ID: {result['main_category_id']})")
            else:
                logger.debug("  None")
                
            logger.debug("\n============================================")
    
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error looking up product categories: {err}")
    except Exception as e:
        logger.error(f"Error looking up product categories: {e}", exc_info=True)

# --- Main Execution ---

def main():
    """Main execution function."""
    # Process command-line arguments
    parser = argparse.ArgumentParser(description='Update product categories in the product_details_cache collection')
    parser.add_argument('--store_id', type=str, help='Process only a specific store ID')
    parser.add_argument('--batch_threshold', type=int, default=DEFAULT_BATCH_THRESHOLD, 
                        help=f'Product count threshold for using batched processing (default: {DEFAULT_BATCH_THRESHOLD})')
    parser.add_argument('--retry_failed', action='store_true', 
                        help='Only process stores that failed in previous runs')
    parser.add_argument('--socket_timeout', type=int, default=30000,
                        help='MongoDB socket timeout in milliseconds (default: 30000)')
    parser.add_argument('--connect_timeout', type=int, default=30000,
                        help='MongoDB connection timeout in milliseconds (default: 30000)')
    parser.add_argument('--max_retries', type=int, default=3,
                        help='Maximum number of retry attempts for failed stores (default: 3)')
    parser.add_argument('--lookup_product', type=str,
                        help='Look up categories for a specific product ID')
    args = parser.parse_args()
    
    specific_store_id = args.store_id
    batch_threshold = args.batch_threshold
    retry_failed_only = args.retry_failed
    socket_timeout = args.socket_timeout
    connect_timeout = args.connect_timeout
    max_retries = args.max_retries
    lookup_product = args.lookup_product
    
    # Configure logging depending on the operation
    if lookup_product:
        # Simplify logging for product lookup to make console output cleaner
        logging.getLogger().setLevel(logging.WARNING)
    
    logger.info("Starting product categories update script")
    logger.info("NOTE: This script now fetches categories only for ACTIVE, NON-DELETED products.")
    
    # Handle product lookup separately
    if lookup_product:
        logger.info(f"Looking up categories for product ID: {lookup_product}")
        mysql_conn = get_mysql_connection()
        if mysql_conn:
            try:
                # Convert store_id to int if provided
                store_id = int(specific_store_id) if specific_store_id else None
                show_product_categories(lookup_product, store_id, mysql_conn)
            finally:
                mysql_conn.close()
        return
    
    logger.info(f"Batch processing threshold: {batch_threshold} products")
    
    if specific_store_id:
        logger.info(f"Processing only store ID: {specific_store_id}")
    elif retry_failed_only:
        logger.info("Processing only previously failed stores")
    else:
        logger.info("Processing all stores")
    
    # Initialize tracking variables
    mysql_conn = None
    mongo_client = None
    processed_count = 0
    failed_count = 0
    failed_stores = set()
    total_categories_added = 0
    
    try:
        # 1. Establish Connections
        logger.info(f"Connecting to MySQL host: {MYSQL_HOST}, database: {MYSQL_DB_LANUBE}")
        mysql_conn = get_mysql_connection()
        if mysql_conn is None:
            logger.error("Failed to establish MySQL connection. Exiting.")
            return

        logger.info(f"Connecting to MongoDB with timeouts: socket={socket_timeout}ms, connect={connect_timeout}ms")
        mongo_client = get_mongodb_connection_with_timeouts(
            socket_timeout_ms=socket_timeout,
            connect_timeout_ms=connect_timeout
        )
        if mongo_client is None:
            logger.error("Failed to establish MongoDB connection. Exiting.")
            return

        mongo_db = mongo_client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"MongoDB connection successful (DB: {MONGO_DB_ANALYSIS_NAME})")

        # 2. Get stores to process
        if specific_store_id:
            # If specific store_id is provided, just process that one
            store = None
            with mysql_conn.cursor(dictionary=True) as cursor:
                # Use the same filtering criteria as get_stores_to_process
                exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
                exception_clause = f"OR id_store IN ({exception_ids_str})" if INACTIVE_STORE_EXCEPTIONS else ""
                
                query = f"""
                SELECT id_store, symbol, name  
                FROM stores
                WHERE id_store = %s 
                    AND ((active = 1 {exception_clause} OR YEAR(created_at) >= {NEW_STORE_CUTOFF_YEAR})
                    AND LOWER(name) NOT LIKE '%test%'
                    AND LOWER(name) NOT LIKE '%demo%')
                """
                cursor.execute(query, (specific_store_id,))
                store = cursor.fetchone()
            
            if store:
                # Cast store to Dict to satisfy the type checker
                store_dict = cast(Dict[str, Any], store)
                stores = [store_dict]
                
                store_name = store_dict.get('name', 'Unknown')
                store_id_str = str(store_dict.get('id_store', 'Unknown'))
                logger.info(f"Found store: {store_name} (ID: {store_id_str})")
            else:
                logger.error(f"Store with ID {specific_store_id} not found or not matching processing criteria")
                return
        else:
            # Get all active stores
            stores = get_stores_to_process(mysql_conn)
            
            # If retry_failed_only, filter to only include previously failed stores
            if retry_failed_only:
                failed_store_ids = load_failed_stores()
                if not failed_store_ids:
                    logger.warning("No previously failed stores found. Exiting.")
                    return
                
                # Filter stores list to only include failed stores
                stores = [store for store in stores if int(store.get('id_store', 0)) in failed_store_ids]
                logger.info(f"Filtered to {len(stores)} previously failed stores")
            
        if not stores:
            logger.warning("No stores found to process. Exiting.")
            return

        # 3. Process each store
        logger.info(f"Processing {len(stores)} stores...")
        for store in stores:
            store_id = None
            try:
                # Get store ID
                if not isinstance(store, dict):
                    logger.warning(f"Store data is not a dictionary: {store}. Skipping.")
                    failed_count += 1
                    continue

                if 'id_store' not in store:
                    logger.warning(f"Skipping store entry due to missing 'id_store': {store}")
                    failed_count += 1
                    continue
                
                store_id_val = store['id_store']
                
                # Convert to int
                try:
                    store_id = int(str(store_id_val))
                except (ValueError, TypeError):
                    logger.warning(f"Cannot convert store ID '{store_id_val}' to integer. Skipping store.")
                    failed_count += 1
                    continue
                
                store_name = store.get('name', 'Unknown Store')
                
                # Process store with retry capability
                if process_store_with_retry(
                    store_id, 
                    store_name,
                    mysql_conn,
                    mongo_db,
                    batch_threshold,
                    max_retries
                ):
                    processed_count += 1
                    # Get count of categories added for this store (if available)
                    product_categories = get_product_categories_for_store(store_id, mysql_conn)
                    total_categories_added += len(product_categories)
                else:
                    failed_count += 1
                    failed_stores.add(store_id)

            except Exception as e:
                logger.error(f"Error processing store {store_id}: {e}", exc_info=True)
                failed_count += 1
                if store_id:
                    failed_stores.add(store_id)

        logger.info("--- Store processing finished ---")
        
        # Save failed stores for future retry if any failures occurred
        if failed_stores:
            save_failed_stores(failed_stores)

    except mysql.connector.Error as db_err:
        logger.error(f"Database connection error: {db_err}")
    except Exception as e:
        logger.error(f"Unexpected error during script execution: {e}", exc_info=True)
    finally:
        # Close connections
        logger.info("Closing database connections...")
        if mysql_conn and mysql_conn.is_connected():
            try:
                mysql_conn.close()
                logger.info("MySQL connection closed.")
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
        if mongo_client:
            try:
                mongo_client.close()
                logger.info("MongoDB connection closed.")
            except Exception as e:
                logger.error(f"Error closing MongoDB connection: {e}")

        logger.info("--- Script Summary ---")
        logger.info(f"Successfully processed/updated categories for: {processed_count} stores")
        logger.info(f"Failed to process/update categories for: {failed_count} stores")
        logger.info(f"Total category assignments added for active products: {total_categories_added}")
        if failed_count > 0:
            logger.info(f"Failed store IDs saved to: {FAILED_STORES_LOG}")
            logger.info("To retry failed stores, run with: --retry_failed")
        logger.info("Script finished.")

if __name__ == "__main__":
    main()