#!/usr/bin/env python
"""
Scheduled Script to Update Meta Chat Contexts

This script should be run on a schedule (e.g., daily) to update the meta_chat_context collection
for all stores. It uses the build_meta_context_from_collections logic to pull data from various
meta_ collections and store it in meta_chat_context.

Usage:
    python update_meta_contexts_scheduled.py [--stores store_id1,store_id2,...]

Example:
    # Update all stores
    python update_meta_contexts_scheduled.py
    
    # Update specific stores
    python update_meta_contexts_scheduled.py --stores 3,961,1024
"""

import argparse
import logging
import time
from datetime import datetime
import pymongo
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Direct import
from update_meta_context_from_collections import (
    connect_to_mongo,
    get_meta_collections,
    get_store_data,
    build_meta_chat_context
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("meta_context_update.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def update_stores(store_ids=None):
    """Update meta_chat_context for specified stores or all stores"""
    try:
        client, db = connect_to_mongo()
        
        # Get all store IDs if not specified
        if not store_ids:
            store_ids = [doc["_id"] for doc in db.global_analysis.find({}, {"_id": 1})]
            logger.info(f"Found {len(store_ids)} stores to process")
        
        # Process each store
        start_time = time.time()
        successful = 0
        for i, store_id in enumerate(store_ids):
            logger.info(f"Processing store {store_id} ({i+1}/{len(store_ids)})")
            
            try:
                success = build_meta_chat_context(db, store_id)
                if success:
                    successful += 1
                    logger.info(f"Successfully processed store {store_id}")
                else:
                    logger.error(f"Failed to process store {store_id}")
                
                # Add a small delay between stores to avoid overwhelming the database
                time.sleep(0.2)
            except Exception as e:
                logger.error(f"Error processing store {store_id}: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Update completed in {duration:.2f} seconds")
        logger.info(f"Processed {successful} out of {len(store_ids)} stores successfully")
        
        client.close()
        return successful
    except Exception as e:
        logger.error(f"Error in update_stores: {e}")
        return 0

def main():
    """Main function to run the script"""
    parser = argparse.ArgumentParser(description='Update meta_chat_context for stores')
    parser.add_argument('--stores', help='Comma-separated list of store IDs to update (default: all stores)')
    
    args = parser.parse_args()
    
    logger.info("====== Starting meta_chat_context update ======")
    
    # Parse store IDs if provided
    store_ids = None
    if args.stores:
        store_ids = args.stores.split(',')
        logger.info(f"Updating specific stores: {store_ids}")
    
    # Update stores
    successful = update_stores(store_ids)
    
    logger.info(f"====== Completed meta_chat_context update: {successful} stores updated ======")

if __name__ == "__main__":
    main() 