import { createTheme, responsiveFontSizes } from '@mui/material/styles';
import { PaletteMode } from '@mui/material';

// Define base colors
const primaryColor = '#00A3FF';
const secondaryColor = '#FF6B6B';
const lightBackgroundColor = '#F5F5F5';
// const darkBackgroundColor = '#121212'; // Removed unused variable
const darkPaperColor = '#1E1E1E';

export const getAppTheme = (mode: PaletteMode) => {
  let theme = createTheme({
    palette: {
      mode, // 'light' or 'dark'
      primary: {
        main: primaryColor,
        light: '#33B5FF',
        dark: '#0082CC',
        contrastText: '#FFFFFF',
      },
      secondary: {
        main: secondaryColor,
        light: '#FF8A8A',
        dark: '#CC5555',
        contrastText: '#FFFFFF',
      },
      ...(mode === 'light'
        ? {
            // Light Mode Palette
            background: {
              default: lightBackgroundColor,
              paper: '#FFFFFF',
            },
            text: {
              primary: 'rgba(51, 51, 51, 0.95)',
              secondary: 'rgba(102, 102, 102, 0.85)',
            },
          }
        : {
            // Dark Mode Palette
            background: {
              default: '#1E1E1E', // Darkest color for the bottom area
              paper: '#2D2D2D', // Grey color for chat area and sidebar
            },
            text: {
              primary: '#ffffff',
              secondary: 'rgba(255, 255, 255, 0.7)',
            },
          }),
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ffa000',
      },
      info: {
        main: '#0288d1',
      },
      success: {
        main: '#2e7d32',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontWeight: 600,
        fontSize: '2.5rem',
      },
      h2: {
        fontWeight: 600,
        fontSize: '2rem',
      },
      h3: {
        fontWeight: 500,
        fontSize: '1.75rem',
      },
      h4: {
        fontWeight: 500,
        fontSize: '1.5rem',
      },
      h5: {
        fontWeight: 500,
        fontSize: '1.25rem',
      },
      h6: {
        fontWeight: 500,
        fontSize: '1rem',
      },
      subtitle1: {
        fontWeight: 500,
        fontSize: '0.95rem',
      },
      subtitle2: {
        fontWeight: 500,
        fontSize: '0.875rem',
      },
      body1: {
        fontWeight: 400,
        fontSize: '1rem',
        lineHeight: 1.6,
      },
      body2: {
        fontWeight: 400,
        fontSize: '1rem',
        lineHeight: 1.5,
      },
      button: {
        fontWeight: 500,
        textTransform: 'none',
        fontSize: '0.9rem',
      },
    },
    shape: {
      borderRadius: 8,
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          '*': {
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
            boxSizing: 'border-box',
          },
          'html, body': {
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          },
          ':root': {
            '--background-paper': mode === 'dark' ? '#2D2D2D' : '#FFFFFF',
            '--background-paper-hover': mode === 'dark' ? '#333333' : '#F5F5F5',
            '--scrollbar-track': mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            '--scrollbar-thumb': mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            '--scrollbar-thumb-hover': mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            // Chat history
            '--chat-history-hover': mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
            // Mode buttons
            '--mode-button-bg': mode === 'dark' ? '#2D2D2D' : '#F5F5F5',
            '--mode-button-hover': mode === 'dark' ? '#333333' : '#E0E0E0',
            '--mode-button-active-text': mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
            '--primary-color': '#00A3FF',
            '--primary-shadow': 'rgba(0, 163, 255, 0.2)',
            // Input field
            '--input-background': mode === 'dark' ? '#1E1E1E' : '#F5F5F5',
            '--input-background-hover': mode === 'dark' ? '#2D2D2D' : '#EBEBEB',
            '--input-border-color': mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            '--input-border-color-hover': mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            '--primary-color-hover': '#0091EA',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            borderRadius: 8,
            padding: '8px 16px',
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
            backgroundColor: mode === 'dark' ? '#2D2D2D' : '#00A3FF', // Changed to light blue in light mode
            color: mode === 'dark' ? 'inherit' : '#FFFFFF', // Ensure text is white against blue background
            '&:hover': {
              backgroundColor: mode === 'dark' ? '#333333' : '#0091EA',
              boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
            },
          },
          contained: {
            boxShadow: 'none',
          },
        },
      },
      MuiTypography: {
        styleOverrides: {
          root: {
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          },
        },
      },
      MuiInputBase: {
        styleOverrides: {
          root: {
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
            backgroundColor: mode === 'dark' ? '#2D2D2D' : '#FFFFFF',
            '&:hover': {
              backgroundColor: mode === 'dark' ? '#333333' : '#F5F5F5',
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: 'none',
            border: 'none', // Explicitly remove borders
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'dark' ? '#2D2D2D' : '#FFFFFF',
            boxShadow: 'none', // Remove shadow/border
            '&.chat-input-container': {
              backgroundColor: mode === 'dark' ? '#2D2D2D' : '#FFFFFF',
            }
          },
          rounded: {
            borderRadius: 12,
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            backgroundColor: mode === 'dark' ? '#2D2D2D' : '#FFFFFF',
            color: mode === 'dark' ? '#FFFFFF' : '#000000',
            '&:hover': {
              backgroundColor: mode === 'dark' ? '#333333' : '#F5F5F5',
            },
          },
        },
      },
      MuiTab: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            fontWeight: 500,
            minWidth: 'auto',
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            padding: '12px 16px',
          },
          head: {
            fontWeight: 600,
            backgroundColor: mode === 'dark' ? darkPaperColor : '#f5f5f5',
          },
        },
      },
      // Sidebar container
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
          }
        }
      },
      // List items in sidebar
      MuiListItem: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
            '&:hover': {
              backgroundColor: mode === 'dark' ? '#2D2D2D' : '#F5F5F5',
            }
          }
        }
      },
    }
  });
  
  theme = responsiveFontSizes(theme);
  return theme;
};

// Keep the old theme for backward compatibility until we fully transition
export const theme = getAppTheme('light'); 










