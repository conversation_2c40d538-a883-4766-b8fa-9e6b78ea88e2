#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to update the active stores collection in MongoDB for LaNube dashboard
This script fetches data for active stores from MySQL and updates MongoDB
Reimplemented with a comprehensive 3-part approach
"""

import os
import sys
import logging
import mysql.connector
from pymongo import MongoClient
from pymongo.errors import BulkWriteError
from datetime import datetime, timezone, timedelta
import traceback
import json
from typing import Dict, List, Optional, Union, Any, Tuple

# Helper function to convert MySQL results to proper dictionaries
def mysql_row_to_dict(row: Any) -> Dict[str, Any]:
    """
    Convert MySQL connector row result to a proper dictionary.
    Handles both RowType and dict results safely.
    """
    if row is None:
        return {}
    
    # If it's already a dict, return it
    if isinstance(row, dict):
        return row
    
    # If it has _fields attribute (NamedTuple-like), convert to dict
    if hasattr(row, '_fields') and hasattr(row, '_asdict'):
        return row._asdict()
    
    # Try to convert using dict() constructor
    try:
        return dict(row)
    except (TypeError, ValueError):
        # Fallback: if it's iterable, try to zip with column names
        if hasattr(row, '__iter__'):
            return {f'col_{i}': val for i, val in enumerate(row)}
        return {}

# Constants
ACTIVITY_WINDOW_YEARS = 3  # Consider stores active if they had activity in the last 3 years
NEW_STORE_CUTOFF_YEAR = 2025  # Stores created from 2025 onwards are considered new and should be included
BATCH_SIZE = 100  # Process stores in batches to manage memory
INACTIVE_STORE_EXCEPTIONS = {
    # High revenue stores (>300,000)
    10,    # Diego galey
    4,     # Elfiko
    349,   # Ingeniería y Servicios
    503,   # MoMa Shoes
    1018,  # Arte y Gaucho
    573,   # Tienda Online de ENEA
    757,   # mari oneill
    947,   # Belrrus
    470,   # BIKINIS DEEP
    627,   # Online Car
    854,   # Kala
    195,   # <EMAIL>
    263,   # Linduras
    1094,  # Angelina Brand
    687,   # Ignacia uy
    927,   # Sacramento Jeans
    591,   # VAFRUTA
    
    # Stores with recent activity (2024 onwards)
    904,   # La Cucha
    1107,  # PalomitayJoyeria
    1105,  # Aym
    889,   # Siena
    1092,  # Amapola
    749,   # Avopunta
    57,    # Palermo Design
    381,   # Love Sushi
    1067,  # Calzados Veloz
    769,   # Planeta Juguetes
    793,   # Second Chance by Angelina Martinez
    745,   # Flor de Venus
    725,   # Ramona Warrior
    1044,  # Espacio Jacinta
    1038,  # Bermudez
    841,   # Shine
    1011,  # SINS
    1003,  # Saturnia
    1004,  # Laura Joyas y Accesorios
    1131,  # puravida
    998,   # PADMA
    1023,  # Demkra
    995,   # Lore Garcia
    991,   # Simple Tienda & Taller
    1112,  # Donizetti
    1074,  # Big Bang Joyas
    463,   # Pecas Baby Accesorios
    983,   # donlucky
    981,   # Delicias_de_Algodon
    978,   # Melania Dress
    975,   # Estampas Locas
    1005,  # Alexis Ruiz Calzados
    972,   # Flores de Jazmin
    1163,  # Fabi Cocina
    1010,  # Alas
    926,   # Chic Woman
    973,   # MaR insumos
    146,   # IMA
    1122,  # Abelarda.uy
    924,   # Tienda Pippo's
} 

print("--- EXECUTING FIXED VERSION OF update_active_stores.py ---")

# --- Setup ---

# Add project root to sys.path to allow importing config
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary config components
try:
    from config.database import get_mongodb_connection
    from config.settings import get_settings
    settings = get_settings()
    print("Successfully imported settings")
except ImportError as e:
    # Use print for early errors before logging might be configured
    print(f"FATAL: Error importing config/settings: {e}. Ensure PYTHONPATH includes project root or run from backend directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"FATAL: Error initializing settings: {e}", file=sys.stderr)
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# --- Constants ---
try:
    MONGO_DB_ANALYSIS_NAME = settings.MONGODB_ANALYSIS_DB
    TARGET_COLLECTION_NAME = 'active_stores_cache'
    # Ensure MySQL settings are present
    MYSQL_HOST = settings.MYSQL_HOST
    MYSQL_USER = settings.MYSQL_USER
    MYSQL_PASSWORD = settings.MYSQL_PASSWORD
    MYSQL_DB_LANUBE = settings.MYSQL_DB_LANUBE
    MYSQL_PORT = settings.MYSQL_PORT
    # MongoDB settings
    MONGO_CONNECTION_STRING = settings.MONGODB_CONNECTION
    MONGO_DB_NAME = settings.MONGODB_MAIN_DB
    # Activity window for determining active stores
    ACTIVITY_WINDOW_YEARS = 3
    # New store creation cutoff date
    NEW_STORE_CUTOFF_YEAR = 2025
    print("Successfully loaded all required settings")
except AttributeError as e:
    logger.fatal(f"Missing required setting: {e}. Check config/settings.py and your .env file.")
    sys.exit(1)

# Country code to name mapping
COUNTRY_CODES_LOOKUP = {
    # South America
    'URY': 'Uruguay',
    'ARG': 'Argentina',
    'PRY': 'Paraguay',
    'PER': 'Peru',
    'CHL': 'Chile',
    'BRA': 'Brasil',
    'COL': 'Colombia',
    'BOL': 'Bolivia',
    'VEN': 'Venezuela',
    # Europe
    'ESP': 'España',
    'FRA': 'Francia',
    'DEU': 'Alemania',
    'ITA': 'Italia',
    'GBR': 'Reino Unido',
    'CHE': 'Suiza',
    'SWE': 'Suecia',
    'NOR': 'Noruega',
    'DNK': 'Dinamarca',
    'POL': 'Polonia',
    'CZE': 'República Checa',
    'ROU': 'Rumania'
}

# Currency rates to USD
CURRENCY_RATES = {
    # South America
    'PEN': 0.27,    # Peru Sol
    'ARS': 0.00096, # Argentina Peso
    'UYU': 0.026,   # Uruguay Peso
    'CLP': 0.0012,  # Chile Peso
    'COP': 0.00025, # Colombia Peso
    'BRL': 0.20,    # Brazil Real
    'BOB': 0.14,    # Bolivia Boliviano
    'PYG': 0.00014, # Paraguay Guarani
    'VES': 0.018,   # Venezuela Bolivar
    'USD': 1.0,     # US Dollar
    # Europe
    'EUR': 1.09,    # Euro
    'GBP': 1.27,    # British Pound
    'CHF': 1.13,    # Swiss Franc
    'SEK': 0.096,   # Swedish Krona
    'NOK': 0.095,   # Norwegian Krone
    'DKK': 0.15,    # Danish Krone
    'PLN': 0.25,    # Polish Zloty
    'CZK': 0.044,   # Czech Koruna
    'RON': 0.22     # Romanian Leu
}

# --- Helper Functions ---

def ensure_mysql_connection(mysql_conn):
    """Ensure MySQL connection is active, reconnect if necessary."""
    try:
        if not mysql_conn or not mysql_conn.is_connected():
            logger.warning("MySQL connection lost. Attempting reconnect...")
            if mysql_conn:
                mysql_conn.reconnect(attempts=3, delay=2)
            else:
                mysql_conn = mysql.connector.connect(
                    host=MYSQL_HOST,
                    user=MYSQL_USER,
                    password=MYSQL_PASSWORD,
                    database=MYSQL_DB_LANUBE,
                    port=MYSQL_PORT,
                    connect_timeout=10
                )
            if mysql_conn.is_connected():
                logger.info("Successfully reconnected to MySQL.")
            else:
                logger.error("Failed to reconnect to MySQL.")
                return None
    except mysql.connector.Error as err:
        logger.error(f"MySQL reconnection error: {err}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error during MySQL reconnection: {e}")
        return None
    return mysql_conn

def validate_currency_code(currency_code: Optional[str], store_id: int) -> str:
    """Validate and normalize currency code."""
    if not currency_code:
        return 'USD'
    
    # Handle numeric currency codes (like "3")
    if currency_code.isdigit():
        logger.warning(f"Invalid numeric currency code '{currency_code}' for store {store_id}, defaulting to USD")
        return 'USD'
    
    # Ensure it's a valid 3-letter code
    if not currency_code.isalpha() or len(currency_code) != 3:
        logger.warning(f"Invalid currency code format '{currency_code}' for store {store_id}, defaulting to USD")
        return 'USD'
    
    # Check if it's in our rates dictionary
    if currency_code not in CURRENCY_RATES:
        logger.warning(f"Unknown currency code '{currency_code}' for store {store_id}, defaulting to USD")
        return 'USD'
    
    return currency_code

def fetch_basic_store_data(mysql_conn) -> List[Dict[str, Any]]:
    """Fetches basic static data for all active stores or stores in the exception list.
    Also includes stores created from 2025 onwards.
    Optimized to use store table fields directly instead of joining with type_currencys.
    
    Args:
        mysql_conn: MySQL connection object.
        
    Returns:
        A list of dictionaries, each containing basic data for one store.
    """
    basic_store_data_list: List[Dict[str, Any]] = []
    
    mysql_conn = ensure_mysql_connection(mysql_conn)
    if not mysql_conn:
        logger.error("MySQL connection is not available for fetch_basic_store_data.")
        return basic_store_data_list
        
    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            # Convert exception list to string for SQL IN clause
            exception_ids_str = ','.join(str(store_id) for store_id in INACTIVE_STORE_EXCEPTIONS)
            
            # Simplified query - removed unnecessary join with type_currencys
            query_basic = f"""
            SELECT 
                s.id_store, s.name, s.email, s.address, s.telephone, 
                s.country AS store_country_raw, s.code_currency, 
                s.name_currency AS currency_name_native, 
                s.symbol AS currency_symbol_native,
                s.id_heading, s.url_store, s.tax, s.created_at AS store_created_at, 
                s.updated_at AS store_updated_at, s.active AS store_is_active,
                h.name AS business_type_name,
                COALESCE(sconf.facebook, '') as facebook,
                COALESCE(sconf.instagram, '') as instagram,
                COALESCE(sconf.tiktok, '') as tiktok,
                COALESCE(sconf.x_twitter, '') as x_twitter,
                COALESCE(sconf.youtube, '') as youtube,
                COALESCE(sconf.pixel_id, '') as pixel_id,
                COALESCE(sconf.keywords, '') as keywords,
                COALESCE(sconf.whatsapp, '') as whatsapp
            FROM stores s
            LEFT JOIN store_configurations sconf ON s.id_store = sconf.id_store
            LEFT JOIN headings h ON s.id_heading = h.id_heading
            WHERE 
                (s.active = 1
                OR s.id_store IN ({exception_ids_str})
                OR YEAR(s.created_at) >= {NEW_STORE_CUTOFF_YEAR})
                AND LOWER(s.name) NOT LIKE '%test%'
                AND LOWER(s.name) NOT LIKE '%demo%'
            ORDER BY s.id_store;
            """
            logger.info("Executing query to fetch basic data for all active stores and exception stores.")
            cursor.execute(query_basic)
            raw_results = cursor.fetchall()
            basic_store_data_list: List[Dict[str, Any]] = [mysql_row_to_dict(row) for row in raw_results]
            logger.info(f"Fetched basic data for {len(basic_store_data_list)} stores.")
            
    except mysql.connector.Error as err:
        logger.error(f"MySQL Error fetching basic store data: {err}")
        # Return empty list on error to prevent partial processing
        return [] 
    except Exception as e:
        logger.error(f"Unexpected error fetching basic store data: {e}", exc_info=True)
        return []
        
    return basic_store_data_list

def detect_country(address='', telephone='', dni='', city='', province=''):
    """
    Enhanced country detection based on multiple data points.
    Returns standardized country code (e.g., 'URY', 'ARG').
    
    Args:
        address: Store's address string
        telephone: Store's phone number
        dni: Document number
        city: City name 
        province: Province/state name
        
    Returns:
        String country code or None if not detectable
    """
    country_code = None
    
    # Ensure inputs are strings and not None
    address = str(address) if address else ''
    telephone = str(telephone) if telephone else ''
    city = str(city) if city else ''
    province = str(province) if province else ''
    
    # 1. Province/State/City Detection
    if province or city:
        location_lower = (province or city).lower().strip()
        
        # Uruguay locations (including Montevideo neighborhoods)
        uruguay_locations = [
            'montevideo', 'maldonado', 'canelones', 'colonia', 'salto',
            'paysandú', 'rivera', 'artigas', 'tacuarembó',
            # Montevideo neighborhoods
            'malvin', 'pocitos', 'punta carretas', 'ciudad vieja', 'centro',
            'cordón', 'parque rodó', 'buceo', 'carrasco', 'prado'
        ]
        
        # Argentina
        if any(state in location_lower for state in [
            'buenos aires', 'córdoba', 'santa fe', 'mendoza', 'tucumán', 
            'entre ríos', 'salta', 'chaco', 'corrientes', 'misiones', 'formosa'
        ]):
            country_code = 'ARG'
        
        # Uruguay - check first as it's more specific
        elif any(loc in location_lower for loc in uruguay_locations):
            country_code = 'URY'
        
        # Peru
        elif any(state in location_lower for state in [
            'lima', 'arequipa', 'cusco', 'trujillo', 'piura', 'chiclayo',
            'ica', 'huancayo', 'tacna', 'cajamarca', 'ilo'
        ]):
            country_code = 'PER'
        
        # Spain
        elif any(state in location_lower for state in [
            'madrid', 'barcelona', 'valencia', 'sevilla', 'zaragoza',
            'málaga', 'murcia', 'palma', 'bilbao', 'alicante', 'córdoba',
            'valladolid', 'vigo', 'gijón'
        ]):
            country_code = 'ESP'
        
        # Chile
        elif any(state in location_lower for state in [
            'santiago', 'valparaíso', 'biobío', 'antofagasta', 'maule',
            'o\'higgins', 'los lagos', 'tarapacá', 'atacama'
        ]):
            country_code = 'CHL'
        
        # Colombia
        elif any(state in location_lower for state in [
            'bogotá', 'medellín', 'cali', 'barranquilla', 'cartagena',
            'cúcuta', 'bucaramanga', 'pereira', 'santa marta'
        ]):
            country_code = 'COL'
        
        # Mexico
        elif any(state in location_lower for state in [
            'ciudad de méxico', 'jalisco', 'nuevo león', 'puebla', 'guanajuato',
            'chihuahua', 'baja california', 'sonora', 'tamaulipas'
        ]):
            country_code = 'MEX'
    
    # 2. Phone Number Format Detection
    if not country_code and telephone:
        phone_clean = telephone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        # Uruguay specific patterns first
        if (phone_clean.startswith('09') and len(phone_clean) == 9) or \
           (phone_clean.startswith('2') and len(phone_clean) == 8) or \
           phone_clean.startswith('+598') or phone_clean.startswith('598'):
            country_code = 'URY'
        
        # Argentina patterns
        elif (phone_clean.startswith('11') and len(phone_clean) == 10) or \
             (phone_clean.startswith('15') and len(phone_clean) == 10) or \
             phone_clean.startswith('+54') or phone_clean.startswith('54'):
            country_code = 'ARG'
        
        # Peru patterns
        elif (phone_clean.startswith('1') and len(phone_clean) == 7) or \
             (phone_clean.startswith('9') and len(phone_clean) == 9) or \
             phone_clean.startswith('+51') or phone_clean.startswith('51'):
            country_code = 'PER'
        
        # Spain patterns
        elif (phone_clean.startswith('6') and len(phone_clean) == 9) or \
             (phone_clean.startswith('7') and len(phone_clean) == 9) or \
             (phone_clean.startswith('8') and len(phone_clean) == 9) or \
             (phone_clean.startswith('9') and len(phone_clean) == 9) or \
             phone_clean.startswith('+34') or phone_clean.startswith('34'):
            country_code = 'ESP'
        
        # Chile patterns
        elif (phone_clean.startswith('2') and len(phone_clean) == 9) or \
             (phone_clean.startswith('9') and len(phone_clean) == 9) or \
             phone_clean.startswith('+56') or phone_clean.startswith('56'):
            country_code = 'CHL'
        
        # Colombia patterns
        elif (phone_clean.startswith('1') and len(phone_clean) == 10) or \
             (phone_clean.startswith('3') and len(phone_clean) == 10) or \
             (phone_clean.startswith('60') and len(phone_clean) == 10) or \
             phone_clean.startswith('+57') or phone_clean.startswith('57'):
            country_code = 'COL'
        
        # Mexico patterns
        elif (phone_clean.startswith('55') and len(phone_clean) == 10) or \
             (phone_clean.startswith('81') and len(phone_clean) == 10) or \
             (phone_clean.startswith('33') and len(phone_clean) == 10) or \
             phone_clean.startswith('+52') or phone_clean.startswith('52'):
            country_code = 'MEX'
    
    # 3. Address Keywords Detection
    if not country_code and address:
        address_lower = address.lower().strip()
        
        # Uruguay specific streets and areas first
        uruguay_streets = [
            'estrazulas', '18 de julio', 'avenida italia', 'rivera',
            'agraciada', 'boulevard artigas', 'rambla', 'malvin',
            'pocitos', 'punta carretas'
        ]
        
        if any(street in address_lower for street in uruguay_streets):
            country_code = 'URY'
        # Then check other countries
        elif any(term in address_lower for term in [
            'argentina', 'caba', 'buenos aires', 'rosario', 'córdoba', 'mendoza'
        ]):
            country_code = 'ARG'
        elif any(term in address_lower for term in [
            'uruguay', 'montevideo', 'maldonado', 'punta del este', 'colonia'
        ]):
            country_code = 'URY'
        elif any(term in address_lower for term in [
            'perú', 'peru', 'lima', 'arequipa', 'cusco', 'trujillo', 'ilo'
        ]):
            country_code = 'PER'
        elif any(term in address_lower for term in [
            'españa', 'spain', 'madrid', 'barcelona', 'valencia', 'sevilla'
        ]):
            country_code = 'ESP'
        elif any(term in address_lower for term in [
            'chile', 'santiago', 'valparaíso', 'concepción', 'antofagasta'
        ]):
            country_code = 'CHL'
        elif any(term in address_lower for term in [
            'colombia', 'bogotá', 'medellín', 'cali', 'barranquilla'
        ]):
            country_code = 'COL'
        elif any(term in address_lower for term in [
            'méxico', 'mexico', 'cdmx', 'guadalajara', 'monterrey'
        ]):
            country_code = 'MEX'
    
    return country_code 

def format_phone(phone_number: str, country_code: str, prefixes_lookup: Dict[str, str]) -> Dict[str, Any]:
    """
    Clean and format telephone number with country code
    
    Args:
        phone_number: Raw phone number string
        country_code: ISO3 country code (e.g., 'URY')
        prefixes_lookup: Dictionary mapping country codes to phone prefixes
        
    Returns:
        Dictionary with original, formatted, and prefix information
    """
    if not phone_number or not isinstance(phone_number, str):
        return {
            "number": "Suggestion to add a telephone number",
            "country_code": None,
            "formatted": "Suggestion to add a telephone number"
        }
    
    # Clean the phone number by removing common separators and spaces
    phone_clean = phone_number.strip()
    phone_clean = phone_clean.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('.',  '')
    
    # If phone is empty after cleaning
    if not phone_clean:
        return {
            "number": "Suggestion to add a telephone number",
            "country_code": None,
            "formatted": "Suggestion to add a telephone number"
        }
    
    # If country code is provided and exists in prefixes lookup
    prefix = None
    if country_code and country_code in prefixes_lookup:
        prefix = prefixes_lookup.get(country_code)
    
    # Remove existing country prefix if present (to avoid duplication)
    if prefix:
        # Remove prefix with + sign
        if phone_clean.startswith(f"+{prefix}"):
            phone_clean = phone_clean[len(prefix)+1:] 
        # Remove prefix without + sign
        elif phone_clean.startswith(prefix):
            phone_clean = phone_clean[len(prefix):]
    
    # Format the phone number
    if prefix:
        formatted_phone = f"+{prefix} {phone_clean}"
    else:
        formatted_phone = phone_clean
    
    return {
        "number": phone_clean,
        "country_code": prefix,
        "formatted": formatted_phone
    } 

def structure_mongo_document(store_sql_data: Dict[str, Any], prefixes_lookup: Dict[str, str]) -> Dict[str, Any]:
    """
    Convert the SQL query result row into a structured MongoDB document.
    
    Args:
        store_sql_data: Dictionary containing one row result from get_active_stores_data
        prefixes_lookup: Dictionary mapping country codes to phone prefixes
        
    Returns:
        Structured document ready for MongoDB storage
    """
    # Start with basic ID
    store_id = store_sql_data.get('id_store')
    mongo_doc: Dict[str, Any] = {
        "_id": str(store_id),
    }
    
    # Add core store fields, checking for None values
    mongo_doc["name"] = store_sql_data.get('name', 'Unknown Store')
    mongo_doc["email"] = store_sql_data.get('email')
    mongo_doc["url_store"] = store_sql_data.get('url_store')
    mongo_doc["created_at"] = store_sql_data.get('store_created_at')
    mongo_doc["updated_at"] = store_sql_data.get('store_updated_at')
    
    # Handle country determination
    store_country_raw = store_sql_data.get('store_country_raw')
    detected_country_code: Optional[str] = None
    
    if not store_country_raw:
        # Try to detect country based on other fields
        detected_country_code = detect_country(
            address=store_sql_data.get('address', ''),
            telephone=store_sql_data.get('telephone', ''),
            city='',  # We don't have these in SQL data but keeping parameters complete
            province=''
        )
    else:
        # Use the raw country as a starting point if it's a code format
        if isinstance(store_country_raw, str) and len(store_country_raw) == 3:
            detected_country_code = store_country_raw
    
    # Structure country information
    country_info: Dict[str, str] = {}
    if detected_country_code and detected_country_code in COUNTRY_CODES_LOOKUP:
        country_info = {
            "code": detected_country_code,
            "name": COUNTRY_CODES_LOOKUP.get(detected_country_code, "")
        }
    else:
        country_info = {
            "code": "Not specified",
            "name": "No country information provided"
        }
    mongo_doc["country"] = country_info
    
    # Structure currency information - now using direct fields from stores table
    currency_code = store_sql_data.get('code_currency')
    currency_name = store_sql_data.get('currency_name_native')
    currency_symbol = store_sql_data.get('currency_symbol_native')
    
    # Validate and normalize currency code
    currency_code = validate_currency_code(currency_code, store_id or 0)
    
    # Assign default currency based on country if not set
    if not currency_code and detected_country_code:
        # Define common currency codes by country
        country_default_currencies = {
            'URY': 'UYU',
            'ARG': 'ARS',
            'PER': 'PEN',
            'ESP': 'EUR',
            'CHL': 'CLP',
            'COL': 'COP',
            'MEX': 'MXN'
        }
        currency_code = country_default_currencies.get(detected_country_code, 'USD')
    
    # Default currency if still not set
    if not currency_code:
        currency_code = 'USD'
    
    # Ensure we have a name and symbol
    if not currency_name:
        # Map of common currency names
        currency_names = {
            'UYU': 'Peso Uruguayo',
            'ARS': 'Peso Argentino',
            'PEN': 'Sol Peruano',
            'EUR': 'Euro',
            'USD': 'US Dollar',
            'CLP': 'Peso Chileno',
            'COP': 'Peso Colombiano',
            'MXN': 'Peso Mexicano'
        }
        currency_name = currency_names.get(currency_code, f"{currency_code} Currency")
    
    if not currency_symbol:
        # Map of common currency symbols
        currency_symbols = {
            'UYU': '$',
            'ARS': '$',
            'PEN': 'S/.',
            'EUR': '€',
            'USD': '$',
            'CLP': '$',
            'COP': '$',
            'MXN': '$'
        }
        currency_symbol = currency_symbols.get(currency_code, '$')
    
    # Get USD exchange rate
    usd_rate = CURRENCY_RATES.get(currency_code, 1.0)
    
    # Structure the currency information
    currency_info: Dict[str, Any] = {
        "native": {
            "code": currency_code,
            "name": currency_name,
            "symbol": currency_symbol
        },
        "usd_rate": usd_rate,
        "converted_to_usd": True
    }
    mongo_doc["currency"] = currency_info
    
    # Format telephone
    phone = store_sql_data.get('telephone', '')
    whatsapp = store_sql_data.get('whatsapp', '')
    
    # Use whatsapp if telephone is empty
    if (not phone or phone.strip() == "") and whatsapp and whatsapp.strip() != "":
        phone = whatsapp
    
    # Safely pass country code to format_phone
    country_code_for_phone = detected_country_code if detected_country_code else ""
    mongo_doc["telephone"] = format_phone(phone, country_code_for_phone, prefixes_lookup)
    
    # Format address
    address = store_sql_data.get('address', '')
    if not address or not isinstance(address, str) or address.strip() == "":
        mongo_doc["address"] = "Suggestion to add an address"
    else:
        mongo_doc["address"] = address.strip()
    
    # Add business type
    business_type = store_sql_data.get('business_type_name')
    if not business_type or not isinstance(business_type, str) or business_type.strip() == "":
        mongo_doc["business_type"] = "Suggestion to add business type"
    else:
        mongo_doc["business_type"] = business_type.strip()
    
    # Add aggregated metrics
    total_orders = store_sql_data.get('total_orders', 0) or 0
    product_revenue = float(store_sql_data.get('product_revenue', 0) or 0)
    total_revenue = float(store_sql_data.get('total_revenue', 0) or 0)
    shipping_revenue = float(store_sql_data.get('shipping_revenue', 0) or 0)
    
    # Calculate in USD
    product_revenue_usd = product_revenue * usd_rate
    total_revenue_usd = total_revenue * usd_rate
    shipping_revenue_usd = shipping_revenue * usd_rate
    
    # Calculate average order values
    avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
    avg_order_value_usd = avg_order_value * usd_rate
    avg_product_value = product_revenue / total_orders if total_orders > 0 else 0
    avg_product_value_usd = avg_product_value * usd_rate
    
    # Add metrics to document
    metrics_info: Dict[str, Union[int, float]] = {
        "total_orders": total_orders,
        "product_revenue": product_revenue,
        "total_revenue": total_revenue,
        "shipping_revenue": shipping_revenue,
        "product_revenue_usd": round(product_revenue_usd, 2),
        "total_revenue_usd": round(total_revenue_usd, 2),
        "shipping_revenue_usd": round(shipping_revenue_usd, 2),
        "average_order_value": avg_order_value,
        "average_order_value_usd": round(avg_order_value_usd, 2),
        "average_product_value": avg_product_value,
        "average_product_value_usd": round(avg_product_value_usd, 2),
        "total_products": store_sql_data.get('total_products', 0) or 0,
        "total_visits": store_sql_data.get('total_visits', 0) or 0,
        "total_customers": store_sql_data.get('total_customers', 0) or 0,
        "active_customers": store_sql_data.get('active_customers', 0) or 0,
        "customers_with_abandoned_carts": store_sql_data.get('customers_with_abandoned_carts', 0) or 0,
    }
    mongo_doc["metrics"] = metrics_info
    
    # Add key dates
    key_dates_info: Dict[str, Any] = {
        "last_order_date": store_sql_data.get('last_order_date'),
        "last_visit_date": store_sql_data.get('last_visit_date'),
        "last_customer_registration_date": store_sql_data.get('last_customer_registration_date'),
        "last_product_added_date": store_sql_data.get('last_product_added_date')
    }
    mongo_doc["key_dates"] = key_dates_info
    
    # Add social media information
    social_media_info: Dict[str, str] = {
        "facebook": (store_sql_data.get('facebook') or '').strip() or "Suggest to Add a Facebook Page",
        "instagram": (store_sql_data.get('instagram') or '').strip() or "Suggest to Add an Instagram Page",
        "tiktok": (store_sql_data.get('tiktok') or '').strip() or "Suggest to Add a TIk Tok account",
        "x_twitter": (store_sql_data.get('x_twitter') or '').strip() or "Suggest to Add a X account",
        "youtube": (store_sql_data.get('youtube') or '').strip() or "Suggest to Add a Youtube account",
        "pixel_id": (store_sql_data.get('pixel_id') or '').strip() or "Suggest to Add a Facebook Pixel id"
    }
    mongo_doc["social_media"] = social_media_info
    
    # Add keywords for SEO
    keywords = store_sql_data.get('keywords')
    if not keywords or not isinstance(keywords, str) or keywords.strip() == "":
        mongo_doc["keywords"] = "Suggest to add SEO words that improve how the clients search your store"
    else:
        mongo_doc["keywords"] = keywords.strip()
    
    # Add script timestamp
    mongo_doc["last_updated_script"] = datetime.now(timezone.utc).isoformat()
    
    return mongo_doc

def update_mongo_cache_batch(store_data: List[Dict[str, Any]]) -> Tuple[int, int]:
    """
    Updates the MongoDB collection with active store data from MySQL in batches.
    
    Args:
        store_data: List of dictionaries with structured store data
        
    Returns:
        Tuple with (number of stores processed, number of stores updated/inserted)
    """
    try:
        import pymongo
        from pymongo.errors import PyMongoError
        
        # Establish connection to MongoDB with retry logic
        logger.info(f"Connecting to MongoDB: {MONGO_CONNECTION_STRING}")
        client = None
        max_retries = 3
        for attempt in range(max_retries):
            try:
                client = pymongo.MongoClient(
                    MONGO_CONNECTION_STRING,
                    maxPoolSize=50,
                    serverSelectionTimeoutMS=30000,
                    connectTimeoutMS=30000,
                    socketTimeoutMS=30000,
                    retryWrites=True
                )
                # Test connection
                client.admin.command('ping')
                logger.info(f"MongoDB connection successful on attempt {attempt + 1}")
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Failed to connect to MongoDB after {max_retries} attempts: {e}")
                    raise
                logger.warning(f"MongoDB connection attempt {attempt + 1} failed: {e}, retrying in {2 ** attempt} seconds...")
                import time
                time.sleep(2 ** attempt)  # Exponential backoff
        
        # Access the analysis database
        db = client[MONGO_DB_ANALYSIS_NAME]
        logger.info(f"Connected to MongoDB database: {MONGO_DB_ANALYSIS_NAME}")
        
        # Get or create the target collection
        collection = db[TARGET_COLLECTION_NAME]
        logger.info(f"Using collection: {TARGET_COLLECTION_NAME}")
        
        # Create indexes on useful fields (not _id as it's automatically indexed)
        try:
            collection.create_index([("name", pymongo.ASCENDING)])
            collection.create_index([("email", pymongo.ASCENDING)])
            collection.create_index([("country.code", pymongo.ASCENDING)])
            logger.info("Successfully created/verified indexes")
        except PyMongoError as index_err:
            logger.warning(f"Warning creating indexes: {str(index_err)}")
        
        # Track stats
        stores_processed = 0
        stores_updated = 0
        
        # Log if we have no stores to process
        if len(store_data) == 0:
            logger.warning("No store data available to process.")
            return 0, 0
            
        # Process stores in batches
        for i in range(0, len(store_data), BATCH_SIZE):
            batch = store_data[i:i+BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1} ({len(batch)} stores)")
            
            # Use bulk operations for better performance
            bulk_operations = []
            
            for store_doc in batch:
                stores_processed += 1
                
                # Extract the store ID
                store_id = store_doc.get("_id")
                
                if not store_id:
                    logger.warning(f"Skipping store without valid ID")
                    continue
                
                # Create bulk operation
                bulk_operations.append(
                    pymongo.ReplaceOne(
                        {"_id": store_id},
                        store_doc,
                        upsert=True
                    )
                )
            
            # Execute bulk operations for this batch
            if bulk_operations:
                try:
                    result = collection.bulk_write(bulk_operations)
                    stores_updated += result.modified_count + result.upserted_count
                    logger.info(f"Batch complete: {result.modified_count} modified, {result.upserted_count} inserted")
                except PyMongoError as e:
                    logger.error(f"Error executing bulk operations: {str(e)}")
        
        # Log success
        logger.info(f"MongoDB update complete. Processed {stores_processed} stores, updated {stores_updated} documents.")
        
        return stores_processed, stores_updated
    
    except ImportError:
        logger.error("Failed to import pymongo. Make sure it's installed (pip install pymongo).")
        return 0, 0
    
    except Exception as e:
        logger.error(f"Failed to update MongoDB: {str(e)}")
        return 0, 0

def fetch_aggregate_data(store_id: int, mysql_conn) -> Dict[str, Any]:
    """Fetches aggregate/dynamic data for a single store ID using optimized queries.
    
    Args:
        store_id: The ID of the store to fetch aggregates for.
        mysql_conn: MySQL connection object.
        
    Returns:
        A dictionary containing the combined aggregate data. Returns default values (0 or None)
        if queries fail or return no results.
    """
    aggregate_data = {
        'total_orders': 0,
        'total_revenue': 0.0,
        'last_order_date': None,
        'total_visits': 0,
        'last_visit_date': None,
        'total_customers': 0,
        'active_customers': 0,
        'last_customer_registration_date': None,
        'total_products': 0,
        'last_product_added_date': None,
        'customers_with_abandoned_carts': 0
    }

    mysql_conn = ensure_mysql_connection(mysql_conn)
    if not mysql_conn:
        logger.error(f"MySQL connection not available for fetch_aggregate_data (Store ID: {store_id}).")
        return aggregate_data

    try:
        with mysql_conn.cursor(dictionary=True) as cursor:
            
            # 1. Get Order Aggregates - SIMPLIFIED
            try:
                query_orders = """
                WITH all_orders AS (
                    SELECT id_store_order,
                           id_store,
                           subtotal,
                           cost_shipping,
                           discount,
                           created_at,
                           id_order_status
                    FROM store_orders
                    WHERE id_store = %s
                    UNION ALL
                    SELECT id_order              AS id_store_order,
                           id_store,
                           amount                AS subtotal,
                           0                     AS cost_shipping,
                           0                     AS discount,
                           date                  AS created_at,
                           id_status             AS id_order_status
                    FROM orders
                    WHERE id_store = %s
                )
                SELECT
                    COUNT(DISTINCT id_store_order) AS total_orders,
                    SUM(subtotal - COALESCE(discount, 0)) AS product_revenue,
                    SUM(subtotal + COALESCE(cost_shipping, 0) - COALESCE(discount, 0)) AS total_revenue,
                    SUM(COALESCE(cost_shipping, 0)) AS shipping_revenue,
                    MAX(created_at) AS last_order_date
                FROM all_orders
                WHERE id_order_status IN (2,5,7);
                """
                cursor.execute(query_orders, (store_id, store_id))
                order_data = cursor.fetchone()
                if order_data:
                    order_dict = mysql_row_to_dict(order_data)
                    aggregate_data.update(order_dict)
                    # Ensure correct types after update
                    aggregate_data['total_orders'] = int(aggregate_data.get('total_orders', 0) or 0)
                    aggregate_data['product_revenue'] = float(aggregate_data.get('product_revenue', 0.0) or 0.0)
                    aggregate_data['total_revenue'] = float(aggregate_data.get('total_revenue', 0.0) or 0.0)
                    aggregate_data['shipping_revenue'] = float(aggregate_data.get('shipping_revenue', 0.0) or 0.0)
            except mysql.connector.Error as order_err:
                 logger.warning(f"MySQL error fetching order aggregates for store ID {store_id}: {order_err}")

            # 2. Get Visit Aggregates
            try:
                query_visits = """
                SELECT
                    COUNT(DISTINCT sv.id_store_visits) AS total_visits,
                    MAX(sv.created_at) AS last_visit_date
                FROM store_visits sv
                WHERE sv.id_store = %s
                """
                cursor.execute(query_visits, (store_id,))
                visit_data = cursor.fetchone()
                if visit_data:
                    visit_dict = mysql_row_to_dict(visit_data)
                    aggregate_data.update(visit_dict)
                    aggregate_data['total_visits'] = int(aggregate_data.get('total_visits', 0) or 0)
            except mysql.connector.Error as visit_err:
                 logger.warning(f"MySQL error fetching visit aggregates for store ID {store_id}: {visit_err}")

            # 3. Get Customer Aggregates (Total Customers and Last Registration)
            try:
                query_customers = """
                SELECT
                    COUNT(DISTINCT sc.id_store_customer) AS total_customers,
                    MAX(sc.created_at) AS last_customer_registration_date
                FROM store_customers sc
                WHERE sc.id_store = %s
                """
                cursor.execute(query_customers, (store_id,))
                customer_data = cursor.fetchone()
                if customer_data:
                    customer_dict = mysql_row_to_dict(customer_data)
                    aggregate_data.update(customer_dict)
                    aggregate_data['total_customers'] = int(aggregate_data.get('total_customers', 0) or 0)
            except mysql.connector.Error as cust_err:
                 logger.warning(f"MySQL error fetching customer aggregates for store ID {store_id}: {cust_err}")

            # 3b. Get Active Customers by Purchase
            try:
                query_active_customers_purchase = """
                WITH all_orders AS (
                    SELECT id_store_customer, id_store, id_order_status
                    FROM store_orders
                    WHERE id_store = %s
                    UNION ALL
                    SELECT id_store_customer, id_store, id_status AS id_order_status
                    FROM orders
                    WHERE id_store = %s
                )
                SELECT COUNT(DISTINCT id_store_customer) AS active_customers_by_purchase
                FROM all_orders
                WHERE id_order_status IN (2,5,7);
                """
                cursor.execute(query_active_customers_purchase, (store_id, store_id))
                active_customer_data = cursor.fetchone()
                if active_customer_data:
                    active_customer_dict = mysql_row_to_dict(active_customer_data)
                    aggregate_data['active_customers'] = int(active_customer_dict.get('active_customers_by_purchase', 0) or 0)
                else:
                    aggregate_data['active_customers'] = 0
            except mysql.connector.Error as active_cust_err:
                 logger.warning(f"MySQL error fetching active customers by purchase for store ID {store_id}: {active_cust_err}")
                 aggregate_data['active_customers'] = 0

            # 3c. Get Customers with Abandoned Carts
            try:
                query_abandoned_carts = """
                SELECT COUNT(DISTINCT scart.id_customer) AS customers_with_abandoned_carts
                FROM store_carshop scart
                WHERE scart.id_store = %s AND scart.active = 0;
                """
                cursor.execute(query_abandoned_carts, (store_id,))
                abandoned_cart_data = cursor.fetchone()
                if abandoned_cart_data:
                    abandoned_cart_dict = mysql_row_to_dict(abandoned_cart_data)
                    aggregate_data['customers_with_abandoned_carts'] = int(abandoned_cart_dict.get('customers_with_abandoned_carts', 0) or 0)
            except mysql.connector.Error as abandoned_err:
                 logger.warning(f"MySQL error fetching abandoned cart customer count for store ID {store_id}: {abandoned_err}")

            # 4. Get Product Aggregates
            try:
                query_products = """
                SELECT
                    COUNT(DISTINCT p.id_product) AS total_products,
                    MAX(p.created_at) AS last_product_added_date
                FROM products p
                WHERE p.id_store = %s
                """
                cursor.execute(query_products, (store_id,))
                product_data = cursor.fetchone()
                if product_data:
                    product_dict = mysql_row_to_dict(product_data)
                    aggregate_data.update(product_dict)
                    aggregate_data['total_products'] = int(aggregate_data.get('total_products', 0) or 0)
            except mysql.connector.Error as prod_err:
                 logger.warning(f"MySQL error fetching product aggregates for store ID {store_id}: {prod_err}")
            
            # Make dates timezone-aware
            for date_field in ['last_order_date', 'last_visit_date', 'last_customer_registration_date', 'last_product_added_date']:
                date_value = aggregate_data.get(date_field)
                if date_value and isinstance(date_value, datetime) and date_value.tzinfo is None:
                    aggregate_data[date_field] = date_value.replace(tzinfo=timezone.utc)
            
            logger.debug(f"Successfully fetched aggregate data for store ID: {store_id}")
            
    except mysql.connector.Error as cursor_err:
        logger.error(f"MySQL error during aggregate data query for store ID {store_id}: {cursor_err}")
    except Exception as e:
        logger.error(f"Unexpected error fetching aggregate data for store ID {store_id}: {e}", exc_info=True)
        
    return aggregate_data

# --- Main function to run the script ---
def main():
    """Main function to execute the script workflow using a 3-part approach with batch processing."""
    try:
        print("Script starting...")
        logger.info("Starting update_active_stores.py script - Fixed Version with Optimizations")
        
        # Define activity cutoff date (3 years ago)
        activity_cutoff = datetime.now(timezone.utc) - timedelta(days=365 * ACTIVITY_WINDOW_YEARS)
        logger.info(f"Activity cutoff date: {activity_cutoff.isoformat()}")
        
        # Define store creation cutoff date
        creation_cutoff = datetime(NEW_STORE_CUTOFF_YEAR, 1, 1, tzinfo=timezone.utc)
        logger.info(f"New store creation cutoff date: {creation_cutoff.isoformat()}")
        
        # Set up phone prefix mapping
        phone_prefixes = {
            'URY': '598', 'ARG': '54', 'PER': '51', 'ESP': '34',
            'CHL': '56', 'COL': '57', 'MEX': '52'
        }
        
        # Connect to MySQL
        mysql_conn = None
        logger.info(f"Connecting to MySQL at {MYSQL_HOST}:{MYSQL_PORT}")
        try:
            mysql_conn = mysql.connector.connect(
                host=MYSQL_HOST,
                user=MYSQL_USER,
                password=MYSQL_PASSWORD,
                database=MYSQL_DB_LANUBE,
                port=MYSQL_PORT,
                connect_timeout=10,
                autocommit=False  # Better for batch operations
            )
            logger.info("MySQL connection established")
        except mysql.connector.Error as err:
            logger.error(f"MySQL connection error: {err}. Exiting script.")
            sys.exit(1)
        except Exception as conn_e:
            logger.error(f"Unexpected MySQL connection error: {conn_e}. Exiting script.")
            sys.exit(1)

        # Part 1: Fetch Basic Static Data for all active stores
        logger.info("Part 1: Fetching basic static data for all active stores.")
        basic_store_data_list = fetch_basic_store_data(mysql_conn)
        logger.info(f"Retrieved basic data for {len(basic_store_data_list)} stores.")

        if not basic_store_data_list:
            logger.info("No active stores found matching initial criteria. Exiting.")
            if mysql_conn and mysql_conn.is_connected(): 
                mysql_conn.close()
            sys.exit(0)

        mongo_documents_to_cache = []
        stores_processed_count = 0
        stores_meeting_activity_criteria = 0
        total_stores_to_process = len(basic_store_data_list)

        # Part 2 & 3: Process stores in batches
        logger.info(f"Part 2 & 3: Processing {total_stores_to_process} stores in batches of {BATCH_SIZE}.")
        
        for batch_start in range(0, total_stores_to_process, BATCH_SIZE):
            batch_end = min(batch_start + BATCH_SIZE, total_stores_to_process)
            batch = basic_store_data_list[batch_start:batch_end]
            
            logger.info(f"Processing batch {batch_start//BATCH_SIZE + 1}: stores {batch_start + 1} to {batch_end}")
            
            batch_documents = []
            
            for basic_store_data in batch:
                stores_processed_count += 1
                store_id = basic_store_data.get('id_store')

                if store_id is None:
                    logger.warning(f"Skipping store with missing ID in basic data: {basic_store_data.get('name', '{No Name}')}")
                    continue

                logger.info(f"--- Processing Store ID: {store_id} ({stores_processed_count}/{total_stores_to_process}) ---")
                
                # Part 2: Fetch aggregate data for this specific store
                aggregate_data = fetch_aggregate_data(store_id, mysql_conn)
                
                # Part 3a: Combine basic and aggregate data
                store_details = {**basic_store_data, **aggregate_data}

                # Part 3b: Apply the enhanced activity filter with multiple criteria
                last_order_date = store_details.get('last_order_date')
                last_visit_date = store_details.get('last_visit_date')
                total_products = store_details.get('total_products', 0)
                store_created_at = store_details.get('store_created_at')
                
                # Make dates timezone-aware if needed
                if store_created_at and isinstance(store_created_at, datetime) and store_created_at.tzinfo is None:
                    store_created_at = store_created_at.replace(tzinfo=timezone.utc)
                    
                # Check if store meets any of the inclusion criteria
                is_active_by_criteria = False
                is_recent_store = False
                is_in_exception_list = False
                
                # 1. Check recent activity criteria
                if last_order_date and isinstance(last_order_date, datetime) and last_order_date >= activity_cutoff:
                    is_active_by_criteria = True
                    logger.debug(f"Store ID {store_id} is active based on last_order_date: {last_order_date}")
                elif last_visit_date and isinstance(last_visit_date, datetime) and last_visit_date >= activity_cutoff:
                    is_active_by_criteria = True
                    logger.debug(f"Store ID {store_id} is active based on last_visit_date: {last_visit_date}")
                elif total_products > 0:
                     is_active_by_criteria = True
                     logger.debug(f"Store ID {store_id} is active based on having {total_products} products.")
                
                # 2. Check if store is recent creation
                if store_created_at and isinstance(store_created_at, datetime) and store_created_at >= creation_cutoff:
                    is_recent_store = True
                    logger.debug(f"Store ID {store_id} is a recent store (created at {store_created_at}).")
                
                # 3. Check if store is in exception list
                if store_id in INACTIVE_STORE_EXCEPTIONS:
                    is_in_exception_list = True
                    logger.debug(f"Store ID {store_id} is in the exception list.")
                
                # Skip if the store doesn't meet any criteria
                if not is_active_by_criteria and not is_recent_store and not is_in_exception_list:
                    logger.info(f"Store ID {store_id} does not meet any inclusion criteria. Skipping MongoDB update.")
                    continue
                    
                stores_meeting_activity_criteria += 1
                logger.info(f"Store ID {store_id} meets inclusion criteria. Proceeding to structure document.")

                # Part 3c: Structure the combined data for MongoDB
                mongo_doc = structure_mongo_document(store_details, phone_prefixes)
                if mongo_doc:
                    batch_documents.append(mongo_doc)
                else:
                     logger.warning(f"Failed to structure MongoDB document for store ID {store_id}")
            
            # Add batch documents to the main list
            mongo_documents_to_cache.extend(batch_documents)
            
            # Log batch progress
            logger.info(f"Batch complete: {len(batch_documents)} stores qualified for MongoDB update")

        logger.info(f"--- Finished processing all {total_stores_to_process} stores --- ")
        logger.info(f"Found {stores_meeting_activity_criteria} stores meeting the final inclusion criteria.")

        # Part 3d: Update MongoDB cache with batch processing
        if mongo_documents_to_cache:
            logger.info(f"Updating MongoDB cache with {len(mongo_documents_to_cache)} documents.")
            stores_processed_mongo, stores_updated_mongo = update_mongo_cache_batch(mongo_documents_to_cache)
            logger.info(f"MongoDB update complete: {stores_processed_mongo} stores processed, {stores_updated_mongo} updated in MongoDB.")
        else:
            logger.info("No stores met the final inclusion criteria for MongoDB update.")
        
        # Close MySQL connection
        if mysql_conn and mysql_conn.is_connected():
            mysql_conn.close()
            logger.info("MySQL connection closed")
        
        logger.info("Script completed successfully (Fixed Version)")
        print("Script completed successfully (Fixed Version)")
        
    except Exception as e:
        logger.error(f"Unexpected error in main execution: {e}", exc_info=True)
        print(f"Script failed with error: {e}")
        # Ensure connection is closed even on unexpected error
        if mysql_conn and mysql_conn.is_connected():
            try: 
                mysql_conn.close() 
            except: 
                pass
        sys.exit(1)

# Execute main function when script is run directly
if __name__ == "__main__":
    main()