// Configuration
const SDK_LOAD_TIMEOUT = 15000; // 15 seconds
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // Initial delay of 2 seconds
const SDK_READY_EVENT = 'facebook-sdk-ready';
const SDK_ERROR_EVENT = 'facebook-sdk-error';

// SDK state tracking
let sdkInitialized = false;
let initializationInProgress = false;
let initializationQueue: Array<() => void> = [];
let retryCount = 0;

// Define a type for SDK event details
type SdkEventDetail = { error?: unknown } | Record<string, unknown>;

// Import MetaAuthService
import { MetaAuthService } from "./auth";
import logger from "../utils/logger"; // Import the logger



// Global suppression of Facebook access token warnings
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

console.warn = (...args: unknown[]) => {
  const message = args.join(' ');
  if (message.includes('overriding current access token') || 
      message.includes('some other app is expecting different access token') ||
      message.includes('You are overriding current access token') ||
      message.includes('probably break things')) {
    // Suppress Facebook access token warnings globally
    return;
  }
  originalConsoleWarn.apply(console, args);
};

console.log = (...args: unknown[]) => {
  const message = args.join(' ');
  if (message.includes('overriding current access token') || 
      message.includes('some other app is expecting different access token') ||
      message.includes('You are overriding current access token') ||
      message.includes('probably break things')) {
    // Suppress Facebook access token warnings globally
    return;
  }
  originalConsoleLog.apply(console, args);
};

console.error = (...args: unknown[]) => {
  const message = args.join(' ');
  if (message.includes('overriding current access token') || 
      message.includes('some other app is expecting different access token') ||
      message.includes('You are overriding current access token') ||
      message.includes('probably break things')) {
    // Suppress Facebook access token warnings globally
    return;
  }
  originalConsoleError.apply(console, args);
};

/**
 * Dispatch SDK status events
 */
const dispatchSdkEvent = (eventName: string, detail: SdkEventDetail = {}) => {
  const event = new CustomEvent(eventName, { detail });
  window.dispatchEvent(event);
  logger.debug(`Meta SDK event dispatched: ${eventName}`, detail);
};

/**
 * Initialize the Meta SDK with retry capability
 */
export const initMetaSDK = async (forceRetry = false): Promise<boolean> => {
  // If initialization is already in progress, don't start another one
  if (initializationInProgress && !forceRetry) {
    logger.debug('Meta SDK initialization already in progress');
    return new Promise((resolve) => {
      // Add to queue to resolve when initialization is complete
      initializationQueue.push(() => resolve(sdkInitialized));
    });
  }
  
  // Reset state if forcing retry
  if (forceRetry) {
    logger.debug('Forcing Meta SDK reinitialization');
    sdkInitialized = false;
    retryCount = 0;
  }
  
  // If SDK is already initialized, return success
  if (sdkInitialized && typeof window.FB !== 'undefined') {
    logger.debug('Meta SDK already initialized');
    return true;
  }
  
  initializationInProgress = true;
  
  try {
    logger.debug('Initializing Meta SDK...');
    
    // Check if we're using HTTPS
    if (window.location.protocol !== 'https:') {
      logger.warn('Facebook Login requires HTTPS. Current protocol:', window.location.protocol);
      
      // In development, log warning but continue
      if (import.meta.env.DEV && window.location.hostname === 'localhost') {
        logger.warn('Using non-HTTPS in development environment. Meta features may not work properly.');
      } else {
        // In production, throw error if not HTTPS
        throw new Error('Meta integration requires HTTPS in production');
      }
    }
    
    // Check if FB is already available
    if (window.FB) {
      logger.debug('Facebook SDK is already available');
      const appId = import.meta.env.VITE_FACEBOOK_APP_ID;
      const appVersion = import.meta.env.VITE_FACEBOOK_APP_VERSION || 'v22.0';
      
      // Only initialize if it hasn't been initialized yet or if forcing reinitialization
      if (!sdkInitialized || forceRetry) {
        // Detect Safari for ITP handling
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        let useCookie = true;
        if (isSafari) {
          try {
            if (document.requestStorageAccess) {
              const hasAccess = await document.hasStorageAccess();
              if (!hasAccess) {
                await document.requestStorageAccess();
              }
            }
          } catch (error) {
            logger.warn('Storage access request failed, falling back to cookie: false', error);
            useCookie = false;
          }
        }
        window.FB.init({
          appId: appId,
          cookie: useCookie,
          xfbml: true,
          version: appVersion
        });
        
        // Check status after initialization
        window.FB.getLoginStatus((response) => {
          logger.debug('Initial login status:', response.status);
        });
        
        logger.debug('Facebook SDK initialized successfully');
        sdkInitialized = true;
      } else {
        logger.debug('Facebook SDK already initialized, skipping initialization');
      }
      
      dispatchSdkEvent(SDK_READY_EVENT);
      
      // Process queued callbacks
      processInitializationQueue(true);
      
      return true;
    }
    
    // Create a promise that resolves when the SDK is loaded
    return await new Promise<boolean>((resolve, reject) => {
      // Set a timeout to reject if the SDK doesn't load
      const timeoutId = setTimeout(() => {
        logger.error(`Facebook SDK loading timeout after ${SDK_LOAD_TIMEOUT/1000} seconds`);
        
        // Retry logic
        if (retryCount < MAX_RETRY_ATTEMPTS) {
          retryCount++;
          logger.debug(`Retrying Meta SDK initialization (attempt ${retryCount} of ${MAX_RETRY_ATTEMPTS})...`);
          
          // Clear current state
          initializationInProgress = false;
          
          // Use exponential backoff
          const exponentialDelay = RETRY_DELAY * Math.pow(2, retryCount - 1);
          setTimeout(() => {
            initMetaSDK(true).then(resolve).catch(reject);
          }, exponentialDelay);
        } else {
          logger.error('Facebook SDK initialization failed after maximum retry attempts');
          dispatchSdkEvent(SDK_ERROR_EVENT, { error: 'Facebook SDK loading timeout' });
          processInitializationQueue(false);
          initializationInProgress = false;
          reject(new Error('Facebook SDK loading timeout after maximum retries'));
        }
      }, SDK_LOAD_TIMEOUT);
      
      window.fbAsyncInit = function() {
        try {
          clearTimeout(timeoutId);
          
          if (!window.FB) {
            logger.error('Facebook SDK loaded but FB object is not available');
            dispatchSdkEvent(SDK_ERROR_EVENT, { error: 'FB object not available' });
            processInitializationQueue(false);
            initializationInProgress = false;
            reject(new Error('Facebook SDK loaded but FB object is not available'));
            return;
          }
          
          const appId = import.meta.env.VITE_FACEBOOK_APP_ID;
          const appVersion = import.meta.env.VITE_FACEBOOK_APP_VERSION || 'v22.0';
          
          logger.debug(`Initializing FB with App ID: ${appId} and version: ${appVersion}`);
          
          // Only initialize if it hasn't been initialized yet
          if (!sdkInitialized) {
            window.FB.init({
              appId: appId,
              cookie: true,
              xfbml: true,
              version: appVersion
            });
            
            // Check login status immediately after initialization
            window.FB.getLoginStatus((response) => {
              logger.debug('Initial login status:', response.status);
            });
            
            logger.debug('Facebook SDK initialized successfully');
            sdkInitialized = true;
          } else {
            logger.debug('Facebook SDK already initialized, skipping initialization');
          }
          
          dispatchSdkEvent(SDK_READY_EVENT);
          
          // Process queued callbacks
          processInitializationQueue(true);
          
          initializationInProgress = false;
          resolve(true);
        } catch (error) {
          logger.error('Error initializing Facebook SDK:', error);
          dispatchSdkEvent(SDK_ERROR_EVENT, { error });
          processInitializationQueue(false);
          initializationInProgress = false;
          reject(error);
        }
      };
      
      loadFacebookSDK().catch(error => {
        logger.error('Error loading Facebook SDK:', error);
        clearTimeout(timeoutId);
        dispatchSdkEvent(SDK_ERROR_EVENT, { error });
        processInitializationQueue(false);
        initializationInProgress = false;
        reject(error);
      });
    });
    
  } catch (error) {
    logger.error('Error setting up Facebook SDK:', error);
    dispatchSdkEvent(SDK_ERROR_EVENT, { error });
    processInitializationQueue(false);
    initializationInProgress = false;
    throw error;
  }
};

/**
 * Process the initialization queue
 */
function processInitializationQueue(success: boolean) {
  // Log initialization status
  logger.debug(`Processing queue after ${success ? 'successful' : 'failed'} initialization`);
  const queue = [...initializationQueue];
  initializationQueue = [];
  
  for (const callback of queue) {
    try {
      callback();
    } catch (error) {
      logger.error('Error processing initialization queue callback:', error);
    }
  }
}

/**
 * Load the Facebook SDK script
 */
async function loadFacebookSDK(): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    // Check if the SDK script is already loaded
    const existingScript = document.querySelector('script[src*="connect.facebook.net"]');
    
    // If the script doesn't exist, create and append it
    if (!existingScript) {
      logger.debug('Facebook SDK script not found, adding it to the document');
      const script = document.createElement('script');
      script.src = 'https://connect.facebook.net/en_US/sdk.js';
      script.async = true;
      script.defer = true;
      script.crossOrigin = 'anonymous';
      script.onload = () => {
        logger.debug('Facebook SDK script loaded');
        resolve();
      };
      script.onerror = (e) => {
        logger.error('Failed to load Facebook SDK script', e);
        reject(new Error('Failed to load Facebook SDK script'));
      };
      document.body.appendChild(script);
    } else {
      logger.debug('Facebook SDK script already exists in the document');
      // If script exists but FB is not defined, we need to trigger fbAsyncInit manually
      if (typeof window.FB === 'undefined' && typeof window.fbAsyncInit === 'function') {
        logger.debug('Manually triggering fbAsyncInit');
        setTimeout(() => {
          window.fbAsyncInit();
        }, 500);
      }
      resolve();
    }
  });
}

/**
 * Listen for SDK ready event
 * @param callback Function to call when SDK is ready
 * @returns Cleanup function to remove listener
 */
export const onSdkReady = (callback: (event: CustomEvent) => void): (() => void) => {
  const handler = (event: Event) => callback(event as CustomEvent);
  window.addEventListener(SDK_READY_EVENT, handler);
  return () => window.removeEventListener(SDK_READY_EVENT, handler);
};

/**
 * Listen for SDK error event
 * @param callback Function to call when SDK has an error
 * @returns Cleanup function to remove listener
 */
export const onSdkError = (callback: (event: CustomEvent) => void): (() => void) => {
  const handler = (event: Event) => callback(event as CustomEvent);
  window.addEventListener(SDK_ERROR_EVENT, handler);
  return () => window.removeEventListener(SDK_ERROR_EVENT, handler);
};

// Helper function to check if the SDK is ready
export const isFacebookSDKReady = async (): Promise<boolean> => {
  if (sdkInitialized && window.FB) {
    logger.debug('Facebook SDK is already available');
    return true;
  }
  
  try {
    logger.debug('Waiting for Facebook SDK to load...');
    return await initMetaSDK();
  } catch (error) {
    logger.error('Facebook SDK not ready:', error);
    return false;
  }
};

/**
 * Check if there's a stored Meta token and validate it on startup
 * This helps maintain authentication across page navigations 
 * Uses debouncing to prevent excessive validation attempts
 */
let lastTokenValidation = 0;
const TOKEN_VALIDATION_INTERVAL = 30000; // 30 seconds between validations

export const validateStoredToken = async (): Promise<boolean> => {
  try {
    // Debounce validation to prevent excessive calls
    const now = Date.now();
    if (now - lastTokenValidation < TOKEN_VALIDATION_INTERVAL) {
      logger.debug('Skipping token validation, validated recently');
      return MetaAuthService.hasValidToken();
    }
    
    lastTokenValidation = now;
    logger.debug('Validating stored Meta token at startup...');
    
    // First check if we have a token
    const token = MetaAuthService.getAccessToken();
    if (!token) {
      logger.debug('No stored Meta token found');
      return false;
    }
    
    logger.debug('Found stored Meta token, validating...');
    // Check if the SDK is ready
    const isReady = await isFacebookSDKReady();
    if (!isReady) {
      logger.warn('Facebook SDK not loaded, cannot validate token yet');
      return false;
    }
    
    // Try to validate the token with the SDK
    const isValid = await MetaAuthService.checkTokenStatus();
    if (isValid) {
      logger.debug('Stored Meta token is valid');
      
      // Dispatch an event that the token is valid to notify components
      // Only dispatch if we haven't recently to prevent event storm
      const validTokenEvent = new CustomEvent('meta-valid-token', { 
        detail: { timestamp: now, token }
      });
      document.dispatchEvent(validTokenEvent);
      return true;
    } else {
      logger.warn('Stored Meta token is invalid, clearing...');
      MetaAuthService.clearToken();
      return false;
    }
  } catch (error) {
    logger.error('Error validating stored Meta token:', error);
    return false;
  }
};
