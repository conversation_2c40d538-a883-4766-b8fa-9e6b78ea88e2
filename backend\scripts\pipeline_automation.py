#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D-Unit Data Pipeline Automation Script
Runs all data processing scripts in the correct order with:
- Progress tracking and resume capability
- Error handling with clear reporting
- Automatic scheduling (daily at 9 AM)
- Skip logic for scripts with no data updates
"""

import os
import sys
import json
import subprocess
import logging
import time
import schedule
import signal
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    # Load development environment by default
    env_file = Path(__file__).parent.parent / ".env.development"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"Loaded environment from: {env_file}")
    else:
        print("No .env.development file found")
except ImportError:
    print("python-dotenv not installed, skipping .env file loading")

# Setup logging
LOG_DIR = Path("logs/pipeline")
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Configure logging with both file and console output
log_filename = LOG_DIR / f"pipeline_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Progress tracking file
PROGRESS_FILE = Path("pipeline_progress.json")

# Pipeline configuration - ALL SCRIPTS RUN SEQUENTIALLY
PIPELINE_GROUPS = {
    "1_database_init": {
        "name": "Database Initialization",
        "scripts": [
            ("meta_db_init.py", "Initialize Meta database collections"),
        ]
    },
    "2_foundation": {
        "name": "Foundation Scripts",
        "scripts": [
            ("update_store_users.py", "User data migration"),
            ("update_active_stores.py", "Active store identification"),
        ]
    },
    "3_product_pipeline": {
        "name": "Product Enhancement Pipeline",
        "scripts": [
            ("update_product_details.py", "Base product cache"),
            ("fix_large_store_documents.py", "Handle large store documents"),
            ("update_product_variations.py", "Add variations"),
            ("optimize_large_store_products.py", "Optimize large store products"),
            ("consolidate_optimized_store.py", "Consolidate optimized stores"),
            ("update_product_categories.py", "Add categories"),
        ]
    },
    "4_analytics": {
        "name": "Analytics Scripts",
        "scripts": [
            ("update_customers_relationships.py", "Customer analytics"),
            ("update_store_activity_metrics.py", "Activity tracking"),
            ("update_meta_sales_correlation.py", "Meta-sales correlation"),
        ]
    },
    "5_database_sync": {
        "name": "Database Synchronization",
        "scripts": [
            ("sync_shipping_faqs_to_mongo.py", "Sync shipping FAQs to MongoDB"),
            ("migrate_meta_format.py", "Migrate Meta data format"),
        ]
    },
    "6_data_validation": {
        "name": "Data Consistency Validation",
        "scripts": [
            ("data_consistency_checker_enhanced.py", "Comprehensive data integrity validation"),
        ]
    },
    "7_ai_analysis": {
        "name": "AI Analysis Pipeline",
        "scripts": [
            ("market_analyzer_batch.py", "AI business analysis (batch mode)"),
            ("update_competitor_analysis.py", "Add competitor insights"),
            ("embeddings_generator_analysis.py", "Generate embeddings"),
        ]
    },
    "8_finalization": {
        "name": "Finalization Scripts",
        "scripts": [
            ("translate_mongo_collections_async.py", "Translate MongoDB collections"),
        ]
    }
}

# Calculate total scripts for progress tracking
TOTAL_SCRIPTS = sum(len(group['scripts']) for group in PIPELINE_GROUPS.values())

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    global shutdown_requested
    logger.info("Shutdown requested. Will stop after current script completes...")
    shutdown_requested = True

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

class PipelineProgress:
    """Manages pipeline execution progress"""
    
    def __init__(self, progress_file: Path):
        self.progress_file = progress_file
        self.progress = self.load_progress()
    
    def load_progress(self) -> Dict:
        """Load progress from file or create new"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load progress file: {e}")
        
        return {
            "last_run": None,
            "completed_scripts": [],
            "failed_script": None,
            "pipeline_complete": False,
            "run_id": None
        }
    
    def save_progress(self):
        """Save current progress to file"""
        try:
            with open(self.progress_file, 'w') as f:
                json.dump(self.progress, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save progress: {e}")
    
    def start_new_run(self):
        """Initialize a new pipeline run"""
        self.progress = {
            "last_run": datetime.now().isoformat(),
            "completed_scripts": [],
            "failed_script": None,
            "pipeline_complete": False,
            "run_id": datetime.now().strftime('%Y%m%d_%H%M%S')
        }
        self.save_progress()
    
    def mark_completed(self, script: str):
        """Mark a script as completed"""
        if script not in self.progress["completed_scripts"]:
            self.progress["completed_scripts"].append(script)
        self.save_progress()
    
    def mark_failed(self, script: str, error: str):
        """Mark a script as failed"""
        self.progress["failed_script"] = {
            "script": script,
            "error": error,
            "timestamp": datetime.now().isoformat()
        }
        self.save_progress()
    
    def mark_pipeline_complete(self):
        """Mark entire pipeline as complete"""
        self.progress["pipeline_complete"] = True
        self.save_progress()
    
    def should_skip_script(self, script: str) -> bool:
        """Check if script should be skipped (already completed)"""
        return script in self.progress["completed_scripts"]
    
    def can_resume(self) -> bool:
        """Check if we can resume from previous run"""
        return (not self.progress["pipeline_complete"] and 
                self.progress["last_run"] is not None)

def run_script_with_args(script_name: str, description: str, args: Optional[List[str]] = None) -> Tuple[bool, str]:
    """
    Run a single script with additional arguments and capture output
    Returns: (success, output_or_error)
    """
    # Get backend directory and try to find script in scripts/data/ or scripts/database/
    backend_dir = Path(__file__).parent.parent  # Go up from scripts/ to backend/
    script_path = backend_dir / "scripts/data" / script_name
    if not script_path.exists():
        script_path = backend_dir / "scripts/database" / script_name
    
    if not script_path.exists():
        return False, f"Script not found in scripts/data/ or scripts/database/: {script_name}"
    
    # Make path relative to backend directory for subprocess
    relative_script_path = script_path.relative_to(backend_dir)
    
    logger.info(f"Running: {description} ({script_name})")
    if args:
        logger.info(f"Arguments: {' '.join(args)}")
    logger.info("-" * 80)
    
    try:
        # Build command with arguments
        cmd = [sys.executable, str(relative_script_path)]
        if args:
            cmd.extend(args)
        
        # Run the script with output capture from backend directory
        env = os.environ.copy()
        env['PYTHONPATH'] = str(backend_dir)
        
        # Set different timeouts for different scripts (4x longer for reliability)
        timeout_minutes = 480  # 8 hours default (was 2 hours)
        if script_name in ["market_analyzer.py", "market_analyzer_batch.py"]:
            timeout_minutes = 1440  # 24 hours for full market analysis (was 6 hours)
        elif script_name in ["embeddings_generator_analysis.py"]:
            timeout_minutes = 120  # 2 hours for embeddings (was 30 minutes)
        elif script_name in ["update_competitor_analysis.py"]:
            timeout_minutes = 180  # 3 hours for competitor analysis (was 45 minutes)
        elif script_name in ["update_customers_relationships.py", "update_store_activity_metrics.py"]:
            timeout_minutes = 240  # 4 hours for customer and activity analysis (was 1 hour)
        elif script_name in ["update_product_categories.py", "update_product_details.py", "update_product_variations.py"]:
            timeout_minutes = 360  # 6 hours for product processing (was 1.5 hours)
        elif script_name in ["fix_large_store_documents.py", "optimize_large_store_products.py", "consolidate_optimized_store.py"]:
            timeout_minutes = 80  # 80 minutes for optimization scripts (was 20 minutes)
        elif script_name in ["data_consistency_checker.py", "data_consistency_checker_enhanced.py"]:
            timeout_minutes = 120  # 2 hours for comprehensive data validation (was 30 minutes)
        elif script_name in ["translate_mongo_collections_async.py"]:
            timeout_minutes = 120  # 2 hours for translation (was 30 minutes)
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            cwd=backend_dir,  # Run from backend directory
            env=env,  # Set PYTHONPATH to backend directory
            text=True,
            timeout=timeout_minutes * 60  # Convert to seconds
        )
        
        # Log output
        if result.stdout:
            logger.info(f"Output:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"Errors/Warnings:\n{result.stderr}")
        
        # Combine output for analysis
        output_text = result.stdout + result.stderr
        
        # Special logging for enhanced consistency checker
        if script_name == "data_consistency_checker_enhanced.py" and result.returncode == 0:
            # Extract health score from output if available
            if "Overall Health Score:" in output_text:
                for line in output_text.split('\n'):
                    if "Overall Health Score:" in line:
                        logger.info(f"[HEALTH SCORE] {line.strip()}")
                        break
        
        # Check if script detected no data updates
        no_update_indicators = [
            "No new data",
            "No changes needed",
            "Skipping - no updates",
            "Already up to date",
            "No new records"
        ]
        if any(indicator in output_text for indicator in no_update_indicators):
            logger.info(f"[COMPLETED] Script completed - no updates needed")
            return True, "No updates needed"
        
        if result.returncode == 0:
            logger.info(f"[SUCCESS] Script completed successfully")
            return True, "Success"
        elif script_name in ["data_consistency_checker.py", "data_consistency_checker_enhanced.py"]:
            # Special handling for data consistency checkers
            if result.returncode == 1:
                logger.warning(f"[WARNING] Data consistency checker found warnings but data is usable")
                return True, "Warnings found but proceeding"
            elif result.returncode == 2:
                error_msg = f"[CRITICAL] Data consistency checker found critical issues - stopping pipeline"
                if result.stderr:
                    error_msg += f"\nDetails: {result.stderr[-1000:]}"
                return False, error_msg
            else:
                error_msg = f"Data consistency checker failed with unexpected return code {result.returncode}"
                if result.stderr:
                    error_msg += f"\nError: {result.stderr[-1000:]}"
                return False, error_msg
        else:
            error_msg = f"Script failed with return code {result.returncode}"
            if result.stderr:
                error_msg += f"\nError: {result.stderr[-1000:]}"  # Last 1000 chars
            return False, error_msg
            
    except subprocess.TimeoutExpired:
        return False, f"Script timed out after {timeout_minutes} minutes"
    except Exception as e:
        return False, f"Unexpected error: {str(e)}"

def run_script(script_name: str, description: str) -> Tuple[bool, str]:
    """
    Run a single script and capture output
    Returns: (success, output_or_error)
    """
    return run_script_with_args(script_name, description, None)

def show_progress(completed_scripts: int, total_scripts: int, current_script: Optional[str] = None):
    """Display global progress bar"""
    percentage = (completed_scripts / total_scripts) * 100
    filled = int(50 * completed_scripts // total_scripts)
    bar = '#' * filled + '-' * (50 - filled)
    
    status_line = f"Progress: [{bar}] {percentage:.1f}% ({completed_scripts}/{total_scripts})"
    if current_script:
        status_line += f" - Current: {current_script}"
    
    logger.info("=" * 80)
    logger.info(status_line)
    logger.info("=" * 80)

def run_scripts_sequential(scripts: List[Tuple[str, str]], progress: PipelineProgress, store_id: Optional[str] = None) -> bool:
    """Run scripts sequentially"""
    for script, description in scripts:
        if shutdown_requested:
            return False
            
        if progress.should_skip_script(script):
            logger.info(f"Skipping already completed: {script}")
            # Show progress even for skipped scripts
            show_progress(len(progress.progress["completed_scripts"]), TOTAL_SCRIPTS, f"{script} (SKIPPED)")
            continue
        
        # Show progress before starting script
        show_progress(len(progress.progress["completed_scripts"]), TOTAL_SCRIPTS, script)
        
        args = ["--store-id", store_id] if store_id and script not in ["update_product_variations.py", "update_product_categories.py"] else None
        success, output = run_script_with_args(script, description, args)
        
        if success:
            progress.mark_completed(script)
            # Show updated progress after completion
            show_progress(len(progress.progress["completed_scripts"]), TOTAL_SCRIPTS, f"{script} (COMPLETED)")
        else:
            # Check if this is a timeout for AI/analysis scripts - we can skip these and continue
            skippable_scripts = [
                "market_analyzer.py", 
                "market_analyzer_batch.py",
                "embeddings_generator_analysis.py", 
                "update_competitor_analysis.py",
                "translate_mongo_collections_async.py"
            ]
            if "timed out" in output and script in skippable_scripts:
                logger.warning(f"[SKIPPED] {script} - {output}")
                logger.warning(f"Continuing pipeline - this script can be run separately later")
                progress.mark_completed(f"{script}_skipped")  # Mark as completed but skipped
                show_progress(len(progress.progress["completed_scripts"]), TOTAL_SCRIPTS, f"{script} (SKIPPED)")
            else:
                progress.mark_failed(script, output)
                logger.error(f"[FAILED] {script}")
                logger.error(f"Error details: {output}")
                return False
    
    return True

def run_pipeline(store_id: Optional[str] = None):
    """Run the complete data pipeline"""
    logger.info("=" * 80)
    logger.info("D-Unit Data Pipeline Execution Started")
    logger.info("=" * 80)
    
    progress = PipelineProgress(PROGRESS_FILE)
    
    # Check if we're resuming
    if progress.can_resume():
        logger.info(f"Resuming pipeline from previous run: {progress.progress['run_id']}")
        logger.info(f"Already completed: {len(progress.progress['completed_scripts'])} scripts")
        if progress.progress['failed_script']:
            logger.warning(f"Previous failure: {progress.progress['failed_script']['script']}")
    else:
        logger.info("Starting new pipeline run")
        progress.start_new_run()
    
    start_time = time.time()
    
    # Show initial progress
    show_progress(len(progress.progress["completed_scripts"]), TOTAL_SCRIPTS)
    
    # Execute pipeline groups - ALL SCRIPTS RUN SEQUENTIALLY
    for group_key, group_config in PIPELINE_GROUPS.items():
        if shutdown_requested:
            logger.warning("Pipeline interrupted by user")
            return
        
        logger.info(f"\n{'='*60}")
        logger.info(f"Starting: {group_config['name']}")
        logger.info(f"{'='*60}")
        
        scripts = group_config['scripts']
        
        # All scripts run sequentially now
        success = run_scripts_sequential(scripts, progress, store_id)
        
        if not success:
            logger.error(f"\n{'!'*60}")
            logger.error(f"Pipeline FAILED in group: {group_config['name']}")
            logger.error(f"Failed script: {progress.progress['failed_script']['script']}")
            logger.error(f"Error: {progress.progress['failed_script']['error']}")
            logger.error(f"{'!'*60}")
            logger.error("\nTo resume, run this script again. It will continue from where it left off.")
            return
    
    # Pipeline completed successfully
    progress.mark_pipeline_complete()
    elapsed_time = time.time() - start_time
    
    logger.info(f"\n{'='*60}")
    logger.info("[SUCCESS] Pipeline completed successfully!")
    logger.info(f"Total execution time: {elapsed_time/60:.2f} minutes")
    logger.info(f"{'='*60}")
    
    # Generate pipeline completion report
    generate_pipeline_completion_report(progress, elapsed_time)
    
    # Clean up progress file for next run
    try:
        PROGRESS_FILE.unlink()
    except:
        pass

def generate_pipeline_completion_report(progress: PipelineProgress, elapsed_time: float):
    """Generate a comprehensive pipeline completion report"""
    try:
        # Look for the most recent enhanced consistency checker report
        data_dir = Path(__file__).parent / "data"
        report_files = list(data_dir.glob("consistency_report_enhanced_*.json"))
        
        if report_files:
            # Get the most recent report
            latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
            
            try:
                with open(latest_report, 'r') as f:
                    consistency_report = json.load(f)
                
                logger.info("\n" + "="*60)
                logger.info("PIPELINE COMPLETION SUMMARY")
                logger.info("="*60)
                
                # Basic pipeline stats
                completed_count = len(progress.progress["completed_scripts"])
                logger.info(f"✓ Scripts completed: {completed_count}/{TOTAL_SCRIPTS}")
                logger.info(f"⏱ Total execution time: {elapsed_time/60:.2f} minutes")
                logger.info(f"🆔 Run ID: {progress.progress['run_id']}")
                
                # Data quality summary from enhanced checker
                summary = consistency_report.get('summary', {})
                health_score = consistency_report.get('health_score', 0)
                
                logger.info(f"\n📊 DATA QUALITY METRICS:")
                logger.info(f"   Overall Health Score: {health_score:.1f}%")
                logger.info(f"   Stores Checked: {summary.get('stores_checked', 0)}")
                logger.info(f"   Critical Issues: {summary.get('critical_issues', 0)}")
                logger.info(f"   Warnings: {summary.get('warnings', 0)}")
                
                # Specific issue breakdown
                if summary.get('revenue_consistency_issues', 0) > 0:
                    logger.info(f"   💰 Revenue Issues: {summary['revenue_consistency_issues']}")
                if summary.get('pipeline_integrity_issues', 0) > 0:
                    logger.info(f"   🔧 Pipeline Issues: {summary['pipeline_integrity_issues']}")
                if summary.get('ai_quality_issues', 0) > 0:
                    logger.info(f"   🤖 AI Quality Issues: {summary['ai_quality_issues']}")
                
                # Top recommendations
                recommendations = consistency_report.get('recommendations', [])
                if recommendations:
                    logger.info(f"\n📋 KEY RECOMMENDATIONS:")
                    for i, rec in enumerate(recommendations[:3], 1):
                        logger.info(f"   {i}. {rec}")
                    if len(recommendations) > 3:
                        logger.info(f"   ... and {len(recommendations) - 3} more (see full report)")
                
                # Collection status
                collection_status = consistency_report.get('collection_status', {})
                if collection_status:
                    stale_collections = [name for name, info in collection_status.items() 
                                       if info.get('is_stale', False)]
                    if stale_collections:
                        logger.info(f"\n⚠ STALE COLLECTIONS: {', '.join(stale_collections)}")
                    else:
                        logger.info(f"\n✓ All collections are fresh (< 24h old)")
                
                logger.info(f"\n📄 Full consistency report: {latest_report.name}")
                logger.info("="*60)
                
            except Exception as e:
                logger.warning(f"Could not parse consistency report: {e}")
        else:
            logger.info("\n📊 No enhanced consistency report found - basic completion only")
            
    except Exception as e:
        logger.warning(f"Error generating completion report: {e}")

def run_scheduled():
    """Run the pipeline on schedule"""
    logger.info("Starting D-Unit Pipeline Scheduler")
    logger.info("Scheduled to run daily at 09:00")
    
    # Schedule daily run at 9 AM
    schedule.every().day.at("09:00").do(run_pipeline)
    
    # Also run immediately if this is the first start
    if not PROGRESS_FILE.exists():
        logger.info("Running pipeline immediately for first time...")
        run_pipeline()
    
    while not shutdown_requested:
        schedule.run_pending()
        time.sleep(60)  # Check every minute
    
    logger.info("Scheduler stopped")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='D-Unit Data Pipeline Automation')
    parser.add_argument('--once', action='store_true', 
                       help='Run once immediately instead of scheduling')
    parser.add_argument('--schedule', action='store_true',
                       help='Run on schedule (daily at 9 AM)')
    parser.add_argument('--status', action='store_true',
                       help='Show current pipeline status')
    parser.add_argument('--validate-only', action='store_true',
                       help='Run only the enhanced data consistency checker')
    parser.add_argument('--quick-validate', action='store_true',
                       help='Run enhanced consistency checker in quick mode (10% sample)')
    parser.add_argument('--store-id', type=str, help='Run the pipeline for a specific store ID')
    
    args = parser.parse_args()
    
    if args.status:
        progress = PipelineProgress(PROGRESS_FILE)
        if progress.progress['last_run']:
            print(f"Last run: {progress.progress['last_run']}")
            print(f"Completed scripts: {len(progress.progress['completed_scripts'])}")
            print(f"Pipeline complete: {progress.progress['pipeline_complete']}")
            if progress.progress['failed_script']:
                print(f"Failed script: {progress.progress['failed_script']['script']}")
        else:
            print("No pipeline runs recorded")
        return
    
    if args.validate_only or args.quick_validate:
        logger.info("Running enhanced data consistency checker independently...")
        script_name = "data_consistency_checker_enhanced.py"
        description = "Enhanced data consistency validation"
        
        # Add quick mode flag if requested
        if args.quick_validate:
            # We need to modify run_script to accept additional arguments
            success, output = run_script_with_args(script_name, description, ["--quick"])
        else:
            success, output = run_script(script_name, description)
        
        if success:
            logger.info("Data consistency validation completed successfully")
        else:
            logger.error(f"Data consistency validation failed: {output}")
            sys.exit(1)
        return
    
    if args.schedule:
        run_scheduled()
    else:
        # Default: run once
        run_pipeline(args.store_id)

if __name__ == "__main__":
    main()