#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Attempt to consolidate optimized store products back into main collection.
This script tries to move optimized products from chunks back to the main document.
"""

import os
import sys
from pymongo import MongoClient
from datetime import datetime, timezone
import logging

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

from config.database import get_mongodb_connection
from config.settings import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def consolidate_store_products(store_id: str):
    """Try to consolidate optimized store products back into main collection"""
    settings = get_settings()
    
    # Connect to MongoDB
    logger.info("Connecting to MongoDB...")
    mongo_client = get_mongodb_connection()
    mongo_db = mongo_client['D-Unit-AnalysisGPT']
    
    # Get collections
    main_collection = mongo_db['product_details_cache']
    large_store_collection = mongo_db['large_store_products']
    
    logger.info(f"Attempting to consolidate store {store_id} products...")
    
    # Get all chunks for this store
    chunks = list(large_store_collection.find({"store_id": store_id}).sort("chunk_index", 1))
    
    if not chunks:
        logger.warning(f"No chunks found for store {store_id}")
        return
    
    # Combine all products from chunks
    all_products = []
    for chunk in chunks:
        all_products.extend(chunk['products'])
    
    logger.info(f"Collected {len(all_products)} products from {len(chunks)} chunks")
    
    # Estimate document size (rough calculation)
    import json
    sample_doc = {
        "_id": store_id,
        "products": all_products,
        "product_count": len(all_products),
        "products_in_separate_collection": False,
        "consolidation_attempt": datetime.now(timezone.utc)
    }
    
    # Estimate size
    estimated_size = len(json.dumps(sample_doc, default=str))
    size_mb = estimated_size / (1024 * 1024)
    
    logger.info(f"Estimated document size: {size_mb:.2f} MB")
    
    if size_mb < 15:  # Leave some safety margin
        logger.info("Size is acceptable, attempting consolidation...")
        
        try:
            # Update main document with consolidated products
            result = main_collection.update_one(
                {"_id": store_id},
                {
                    "$set": {
                        "products": all_products,
                        "product_count": len(all_products),
                        "products_in_separate_collection": False,
                        "consolidated_at": datetime.now(timezone.utc),
                        "optimization_preserved": True
                    }
                }
            )
            
            if result.modified_count > 0:
                logger.info("✅ Successfully consolidated products back to main collection!")
                
                # Archive the chunks (don't delete immediately, just mark as archived)
                large_store_collection.update_many(
                    {"store_id": store_id},
                    {
                        "$set": {
                            "archived": True,
                            "archived_at": datetime.now(timezone.utc),
                            "reason": "Consolidated back to main collection after optimization"
                        }
                    }
                )
                
                logger.info("✅ Marked chunks as archived")
                return True
                
            else:
                logger.error("❌ Failed to update main collection")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during consolidation: {e}")
            return False
    
    else:
        logger.warning(f"❌ Document size ({size_mb:.2f} MB) still too large, keeping in separate collection")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Consolidate optimized store products')
    parser.add_argument('--store-id', default='961', help='Store ID to consolidate (default: 961)')
    
    args = parser.parse_args()
    
    logger.info("Starting product consolidation...")
    success = consolidate_store_products(args.store_id)
    
    if success:
        logger.info("🎉 Consolidation completed successfully!")
    else:
        logger.info("⚠️  Consolidation not possible, products remain in separate collection")