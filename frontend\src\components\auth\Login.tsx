import React, { useState } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Link,
  Paper,
  Alert,
  IconButton,
  InputAdornment,
  Divider,
  CircularProgress,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { CredentialResponse, useGoogleLogin } from '@react-oauth/google';
import AuthLayout from './AuthLayout';
import { authService } from '../../services/authService';
import { MetaAuthService } from '../../services/auth';
import { logger } from '../../utils/logger';
import { useTranslation } from 'react-i18next';
import { useThemeContext } from '../../contexts/ThemeContext';
import ErrorBoundary from '../../components/ErrorBoundary';

interface ApiError {
  response?: {
    data?: {
      detail?: string;
      message?: string;
      requires_2fa?: boolean;
    };
    status?: number;
  };
  message?: string;
}

// SVG for the classic Google logo
const GoogleLogo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 48 48">
    <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"/>
    <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"/>
    <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"/>
    <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"/>
    <path fill="none" d="M0 0h48v48H0z"/>
  </svg>
);

const Login = () => {
  const { t } = useTranslation();
  const { mode } = useThemeContext();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loadingMethod, setLoadingMethod] = useState<string | null>(null);
  const [requires2FA, setRequires2FA] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [verifying2FA, setVerifying2FA] = useState(false);
  const navigate = useNavigate();
  const { login: authLogin, googleLogin, metaLogin, setUser, setIsAuthenticated, isAuthenticated, performLogoutActions } = useAuth();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const nextPath = params.get('next');

  // Configure Google login
  const handleGoogleSuccess = async (credentialResponse: CredentialResponse) => {
    try {
      logger.debug('Google login success, processing credential response:', credentialResponse);
      if (!credentialResponse.credential) {
        throw new Error('No credential received from Google');
      }
      
      // Log the credential details
      logger.debug('Google credential:', credentialResponse.credential);
      const segments = credentialResponse.credential.split('.');
      logger.debug('Token segments:', segments);
      logger.debug('Number of segments:', segments.length);
      
      // Process the login
      await googleLogin(credentialResponse.credential);
      logger.info('Successfully logged in with Google');
      setTimeout(() => navigate(nextPath || '/dashboard'), 500);
    } catch (err: unknown) {
      logger.error('Google login error:', err);
      const error = err as ApiError;
      if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else {
        setError(error.message || t('errorGoogleLoginFailedGeneral'));
      }
      setLoadingMethod(null);
    }
  };

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      try {
        const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
          headers: { Authorization: `Bearer ${tokenResponse.access_token}` },
        });
        await response.json();
        
        // Create a credential response similar to what GoogleLogin would provide
        const credentialResponse: CredentialResponse = {
          credential: tokenResponse.access_token,
          clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          select_by: "user"
        };
        
        await handleGoogleSuccess(credentialResponse);
      } catch (err) {
        logger.error('Google login error:', err);
        setError(t('errorGoogleLoginFailedTryAgain'));
        setLoadingMethod(null);
      } finally {
        setLoadingMethod(null);
      }
    },
    onError: () => {
      logger.error('Google login failed');
      setError(t('errorGoogleLoginFailedTryAgain'));
      setLoadingMethod(null);
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoadingMethod('email');

    try {
      if (!email || !password) {
        setError(t('errorEnterEmailPassword'));
        setLoadingMethod(null);
        return;
      }
      
      logger.debug('Attempting login...');
      
      const response = await authService.login(email, password);
      logger.debug('Login response:', response);
      
      // Check if this is a 2FA response
      if (response.requires_2fa) {
        logger.debug('2FA required, showing verification form');
        setRequires2FA(true);
        setLoadingMethod(null);
        return;
      }
      
      // Normal login flow
      await authLogin(email, password);
      logger.info('Login successful');
      setTimeout(() => navigate(nextPath || '/dashboard'), 500);
      
    } catch (err: unknown) {
      logger.error('Login error:', err);
      const error = err as ApiError;
      
      // Check if this is a 2FA response
      if (error.response?.data?.requires_2fa) {
        logger.debug('2FA required from error response, showing verification form');
        setRequires2FA(true);
        return;
      }
      
      if (error.response?.status === 401) {
        setError(t('errorInvalidEmailPassword'));
      } else if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else {
        setError(t('errorLoginGeneral'));
      }
    } finally {
      if (!requires2FA) {
         setLoadingMethod(null);
      }
    }
  };

  const handleClearSession = () => {
    logger.info('Manual session clear triggered from login page.');
    performLogoutActions(); // This will clear all data and reload the page
  };

  const handle2FASubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setVerifying2FA(true);

    try {
      logger.debug('Submitting 2FA verification code...');
      
      if (!verificationCode) {
        setError(t('error2FANoCode'));
        setVerifying2FA(false);
        return;
      }
      
      logger.debug('Verifying 2FA code for email:', email);
      const data = await authService.verify2FA(email, verificationCode);
      logger.debug('2FA verification response:', data);
      
      if (!data.access_token) {
        throw new Error(t('error2FANoToken'));
      }
      
      // Set the token using authService for consistent cookie-based storage
      authService.setToken(data.access_token);
      if (data.refresh_token) {
        authService.setRefreshToken(data.refresh_token);
      }
      logger.debug('[Login] Set token using authService');
      
      // Get user data
      logger.debug('Getting user profile after 2FA verification');
      const userData = await authService.getUserProfile();
      logger.debug('User profile received:', userData);
      
      // Store user data
      localStorage.setItem('user', JSON.stringify(userData));
      
      // Update auth context
      setUser(userData);
      setIsAuthenticated(true);
      
      logger.info('2FA verification successful, navigating to dashboard');
      setTimeout(() => navigate(nextPath || '/dashboard'), 500);
    } catch (err: unknown) {
      logger.error('2FA verification error:', err);
      const error = err as ApiError;
      if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else {
        setError(t('error2FAFailed'));
      }
    } finally {
      setVerifying2FA(false);
      setLoadingMethod(null);
      setRequires2FA(false);
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // Custom Facebook login handler that properly integrates with AuthContext
  const handleFacebookLogin = async (platform: 'facebook' | 'instagram') => {
    try {
      // Clear any previously stored Meta token to avoid unintended auto-login
      MetaAuthService.clearToken();
      logger.debug(`Login: Starting direct ${platform} login flow`);
      setLoadingMethod('meta');
      setError('');
      
      // Step 1: Get auth response from Facebook SDK
      const authResponse = await MetaAuthService.login(platform);
      if (authResponse.status === 'connected') {
        // Log the response from the Facebook SDK (Removed for security)
        // logger.debug('Login: Facebook SDK auth response received:', authResponse);

        if (!authResponse || !authResponse.authResponse || !authResponse.authResponse.accessToken) {
          throw new Error(t('errorFacebookNoToken'));
        }
        
        // Step 2: Use the auth context's metaLogin function to properly update auth state
        await metaLogin(authResponse.authResponse.accessToken, platform);
        logger.info('Login: Facebook login successful, auth state updated');
        
        // Step 3: Now navigate to the dashboard
        logger.debug('Login: Auth state should be updated, navigating to dashboard');
        setTimeout(() => navigate(nextPath || '/dashboard'), 500);
      } else {
        logger.warn('Login: Facebook login cancelled or failed. Status:', authResponse.status);
        setError(t('errorFacebookLoginFailed'));
      }
    } catch (error: unknown) {
      logger.error('Login: Facebook login error:', error);
      const err = error as ApiError;
      setError(err.message || t('errorFacebookLoginFailed'));
    } finally {
      setLoadingMethod(null);
    }
  };

  const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
  };

  // Reset loading state if popup is closed without completing login
  React.useEffect(() => {
    if (loadingMethod === 'google' || loadingMethod === 'meta') {
      const handleFocus = () => {
        if (!isAuthenticated) {
          setLoadingMethod(null);
        }
      };
      window.addEventListener('focus', handleFocus);
      return () => {
        window.removeEventListener('focus', handleFocus);
      };
    }
  }, [loadingMethod, isAuthenticated]);

  return (
    <ErrorBoundary>
      <AuthLayout>
        <Paper
          elevation={3}
          sx={{
            p: { xs: 2, sm: 4 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            backgroundColor: (theme) => theme.palette.mode === 'dark' 
              ? 'rgba(30, 30, 30, 0.9)' 
              : 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: 2,
            width: { xs: '92%', sm: '100%' },
            height: 'auto',
            minHeight: { xs: 'auto', sm: '600px' },
            maxWidth: { xs: 'none', sm: '100%' },
            mx: 'auto',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            position: 'relative',
          }}
        >
          <Box
            component="img"
            src="/logo.png"
            alt="D-Unit Logo"
            sx={{
              width: { xs: '120px', sm: '150px' },
              height: 'auto',
              objectFit: 'contain',
              mb: 3,
            }}
          />

          <Typography 
            component="h1" 
            variant="h5" 
            sx={{ 
              mb: 0.5,
              fontWeight: 500,
              color: (theme) => theme.palette.text.primary 
            }}
          >
            {t('welcomeBack')}
          </Typography>

          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ 
              mb: 2,
              fontSize: '14px',
              textAlign: 'center'
            }}
          >
            {t('signInPrompt')}
          </Typography>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              display: 'block',
              textAlign: 'center',
              mt: -1,
              mb: 2
            }}
          >
            {t('registerNote')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {error}
            </Alert>
          )}



          {!requires2FA ? (
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3, width: '100%' }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label={t('emailLabel')}
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                sx={{ 
                  mb: 2,
                  '& .MuiInputBase-root': {
                    height: 'calc(2.4em + 8px)', // Increase height by approximately 20%
                  }
                }}
                variant="outlined"
                size="small"
                disabled={loadingMethod !== null}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label={t('passwordLabel')}
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                sx={{ 
                  '& .MuiInputBase-root': {
                    height: 'calc(2.4em + 8px)', // Increase height by approximately 20%
                  }
                }}
                variant="outlined"
                size="small"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                        disabled={loadingMethod !== null}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Box sx={{ textAlign: 'right', mt: 0.5 }}>
                <Link 
                  href="/forgot-password" 
                  variant="body2" 
                  sx={{ 
                    color: '#2196f3',
                    textDecoration: 'none',
                    fontSize: '14px',
                    '&:hover': {
                      textDecoration: 'underline',
                    }
                  }}
                >
                  {t('forgotPasswordLink')}
                </Link>
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                sx={{
                  mt: 3,
                  mb: 2,
                  padding: '10px',
                  fontWeight: 'bold',
                  backgroundColor: '#00A3FF',
                  '&:hover': {
                    backgroundColor: '#007acc'
                  },
                  minHeight: '48px'
                }}
                disabled={loadingMethod !== null}
              >
                {loadingMethod === 'email' ? <CircularProgress size={24} color="inherit" /> : t('loginButton')}
              </Button>

              <Box sx={{ width: '100%', my: 2, display: 'flex', alignItems: 'center' }}>
                <Divider sx={{ flex: 1 }} />
                <Typography variant="body2" color="text.secondary" sx={{ mx: 2 }}>
                  {t('orDivider')}
                </Typography>
                <Divider sx={{ flex: 1 }} />
              </Box>

              {/* Custom Google Button */}
              <Button
                fullWidth
                variant="contained"
                onClick={() => { setLoadingMethod('google'); handleGoogleLogin(); }}
                sx={{
                  mb: 2,
                  py: 1,
                  backgroundColor: '#4285F4',
                  textTransform: 'none',
                  position: 'relative',
                  minHeight: '48px',
                  '&:hover': {
                    backgroundColor: '#3367D6',
                  },
                }}
                disabled={loadingMethod !== null}
              >
                {loadingMethod === 'google' ? (
                  <CircularProgress size={24} sx={{ color: 'white' }} />
                ) : (
                  <>
                    <Box sx={{ 
                      position: 'absolute', 
                      left: 16, 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      backgroundColor: 'white',
                      borderRadius: '50%',
                      width: 24,
                      height: 24,
                    }}>
                      <GoogleLogo />
                    </Box>
                    <Typography sx={{ fontWeight: 500, width: '100%', textAlign: 'center', color: 'white' }}>
                      {t('continueWithGoogle')}
                    </Typography>
                  </>
                )}
              </Button>

              {/* Custom Meta Button */}
              <Button
                fullWidth
                variant="contained"
                onClick={() => handleFacebookLogin('facebook')}
                sx={{
                  mb: 2,
                  py: 1,
                  backgroundColor: mode === 'dark' ? '#42a5f5' : '#0668E1',
                  color: 'white',
                  textTransform: 'none',
                  position: 'relative',
                  minHeight: '48px',
                  '&:hover': {
                    backgroundColor: mode === 'dark' ? '#1e88e5' : '#0557BC',
                  },
                }}
                disabled={loadingMethod !== null}
              >
                {loadingMethod === 'meta' ? (
                   <CircularProgress size={24} sx={{ color: 'white' }} />
                ) : (
                  <>
                    <Box sx={{ 
                      position: 'absolute', 
                      left: 16, 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      backgroundColor: 'white',
                      borderRadius: '50%',
                      width: 24,
                      height: 24,
                    }}>
                      <Box 
                        component="img"
                        src="/meta.png" 
                        alt="Meta Logo"
                        sx={{ 
                          width: '18px', 
                          height: '18px',
                          objectFit: 'contain', 
                        }}
                      />
                    </Box>
                    <Typography sx={{ fontWeight: 500, width: '100%', textAlign: 'center' }}>
                      {t('continueWithMeta')}
                    </Typography>
                  </>
                )}
              </Button>

              <Button onClick={handleClearSession} fullWidth variant="text" sx={{ mt: 1, color: 'text.secondary' }}>
                {t('login.clearSession', 'Clear Session & Reload')}
              </Button>



              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="textSecondary" sx={{ fontSize: '14px' }}>
                  {t('noAccountPrompt')} <Link 
                    href="/register" 
                    sx={{ 
                      color: '#2196f3',
                      textDecoration: 'none',
                      '&:hover': {
                        textDecoration: 'underline',
                      }
                    }}
                  >
                    {t('signUpLink')}
                  </Link>
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box component="form" onSubmit={handle2FASubmit} sx={{ mt: 3, width: '100%' }}>
              <Alert severity="info" sx={{ mb: 2 }}>
                {t('enterVerificationCodePrompt', 'Please enter the verification code sent to your email.')}
              </Alert>
              <TextField
                margin="normal"
                required
                fullWidth
                id="verificationCode"
                label={t('verificationCodeLabel', 'Verification Code')}
                name="verificationCode"
                autoComplete="off"
                autoFocus
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                sx={{ 
                  mb: 2,
                  '& .MuiInputBase-root': {
                    height: 'calc(2.4em + 8px)', // Increase height by approximately 20%
                  }
                }}
                variant="outlined"
                size="small"
                disabled={verifying2FA || loadingMethod !== null}
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                disabled={verifying2FA || loadingMethod !== null}
                sx={{
                  mt: 2,
                  mb: 2,
                  backgroundColor: '#2196f3',
                  textTransform: 'uppercase',
                  fontWeight: 'bold',
                  '&:hover': {
                    backgroundColor: '#1976d2',
                  },
                  '&:disabled': {
                    backgroundColor: 'rgba(33, 150, 243, 0.5)',
                  }
                }}
              >
                {verifying2FA ? <CircularProgress size={24} sx={{ color: 'white' }} /> : t('verifyCodeButton', 'Verify Code')}
              </Button>
            </Box>
          )}
        </Paper>


      </AuthLayout>
    </ErrorBoundary>
);
}

export default Login;
