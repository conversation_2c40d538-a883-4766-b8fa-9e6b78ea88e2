"""Logging configuration for the application."""

import logging
import os
import sys
import re  # Added import for regex
from contextvars import ContextVar
from logging.handlers import RotatingFileHandler

# SensitiveDataFilter class definition moved directly into this file
class SensitiveDataFilter(logging.Filter):
    """Filter to remove sensitive data from logs"""
    
    def __init__(self):
        super().__init__()
        self.patterns = [
            (r'password=[\w\-\.]+', 'password=***'),
            (r'token=[\w\-\.]+', 'token=***'),
            (r'key=[\w\-\.]+', 'key=***'),
            (r'email=[\w\-.@]+', 'email=***'),
            (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 'EMAIL_REDACTED'),
            (r'\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b', 'CARD_REDACTED')
        ]

    def filter(self, record):
        if isinstance(record.msg, str):
            for pattern, replacement in self.patterns:
                record.msg = re.sub(pattern, replacement, record.msg)
        return True

# Cache of messages already logged during the current request
message_cache: ContextVar[set] = ContextVar("message_cache", default=set())

class DeduplicateFilter(logging.Filter):
    """Filter to suppress duplicate log messages within one request."""
    def filter(self, record):
        cache = message_cache.get()
        if record.getMessage() in cache:
            return False
        cache.add(record.getMessage())
        return True

# Determine application environment (default to development)
APP_ENV = os.getenv('APP_ENV', 'development')

# Set log level based on environment
LOG_LEVEL = logging.INFO  # Changed from DEBUG to INFO for cleaner logs
CONSOLE_LEVEL = 'INFO' if APP_ENV == 'development' else 'WARNING'
FILE_LEVEL = 'DEBUG' if APP_ENV == 'development' else 'INFO'

# Ensure logs directory exists
logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Define the logging configuration dictionary
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,  # Preserve loggers from libraries
    'formatters': {
        'standard': {
            # Format includes timestamp, level, logger name, and message
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'simple': {
            'format': '[%(levelname)s] %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s [%(filename)s:%(lineno)d]: %(message)s'
        },
    },
    'filters': {
        'sensitive': {
            '()': SensitiveDataFilter,
        },
        'deduplicate': {
            '()': DeduplicateFilter,
        },
    },
    'handlers': {
        'console': {
            'level': CONSOLE_LEVEL,  # Dynamic log level
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
            'filters': ['sensitive', 'deduplicate'],  # Apply both filters
            'stream': 'ext://sys.stdout',  # Log to standard output
        },
        'file': {
            'level': FILE_LEVEL,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filters': ['sensitive'],
            'filename': os.path.join(logs_dir, 'application.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'encoding': 'utf8'
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filters': ['sensitive'],
            'filename': os.path.join(logs_dir, 'errors.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'encoding': 'utf8'
        },
    },
    'loggers': {
        '': {  # Root logger configuration
            'handlers': ['console', 'file'],  # Use console and file handlers
            'level': LOG_LEVEL,  # Set root logger level based on APP_ENV
            'propagate': False # Prevent root logger messages from propagating further
        },
        # Application loggers
        '__main__': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,
            'propagate': False
        },
        'services': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,
            'propagate': False
        },
        'routes': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,
            'propagate': False
        },
        'middleware': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,
            'propagate': False
        },
        'config': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,
            'propagate': False
        },
        # Third-party library loggers - reduce verbosity
        'uvicorn': {
            'handlers': ['console'],
            'level': logging.INFO if APP_ENV == 'development' else logging.WARNING,
            'propagate': False
        },
        'uvicorn.error': {
            'handlers': ['console'],
            'level': logging.INFO if APP_ENV == 'development' else logging.WARNING,
            'propagate': False
        },
        'uvicorn.access': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Make access logs less verbose
            'propagate': False
        },
        'passlib': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce passlib noise
            'propagate': False
        },
        'passlib.utils': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'passlib.utils.compat': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'passlib.registry': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'passlib.handlers': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'passlib.handlers.bcrypt': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'multipart': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce multipart form parsing noise
            'propagate': False
        },
        'multipart.multipart': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'httpcore': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce HTTP client noise
            'propagate': False
        },
        'httpcore.connection': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'httpcore.http11': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        },
        'httpx': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce HTTP client noise
            'propagate': False
        },
        'openai': {
            'handlers': ['console', 'file'],
            'level': logging.WARNING,  # Keep only warnings and errors from OpenAI
            'propagate': False
        },
        'openai._base_client': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce OpenAI client debugging
            'propagate': False
        },
        'tzlocal': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce timezone library noise
            'propagate': False
        },
        'asyncio': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce asyncio debugging
            'propagate': False
        },
        'apscheduler': {
            'handlers': ['console', 'file'],
            'level': logging.INFO,  # Keep scheduler logs at INFO
            'propagate': False
        },
        'apscheduler.scheduler': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False
        },
        'sqlalchemy': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce SQLAlchemy noise
            'propagate': False
        },
        'sqlalchemy.engine': {
            'handlers': ['console'],
            'level': logging.WARNING,  # Reduce SQLAlchemy engine noise
            'propagate': False
        },
        'faker': {
            'handlers': ['console'],
            'level': logging.WARNING,
            'propagate': False
        }
    }
} 