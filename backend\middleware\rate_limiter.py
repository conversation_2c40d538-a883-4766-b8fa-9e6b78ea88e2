import time
import json
import yaml
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any, List
from collections import defaultdict, deque
from dataclasses import dataclass, field
from threading import RLock
import asyncio
from pathlib import Path
import os
import traceback

from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from starlette.middleware.base import BaseHTTPMiddleware

from models.security import RateLimitResult, SecurityEvent, SecurityEventType, ThreatLevel
from utils.security_utils import extract_client_ip, generate_trace_id, calculate_request_complexity

logger = logging.getLogger(__name__)

@dataclass
class TimestampComplexity:
    """Helper class to store timestamp with complexity score"""
    timestamp: float
    complexity: int

@dataclass
class RateLimitEntry:
    """Rate limit tracking entry for a specific key"""
    count: int = 0
    window_start: float = field(default_factory=time.time)
    last_request: float = field(default_factory=time.time)
    blocked_until: Optional[float] = None
    complexity_score: int = 1
    request_timestamps: deque = field(default_factory=deque)  # For sliding window algorithm
    
class InMemoryRateLimitStore:
    """In-memory storage for rate limiting data with automatic cleanup"""
    
    def __init__(self, cleanup_interval: int = 300):  # 5 minutes
        self._store: Dict[str, RateLimitEntry] = {}
        self._lock = RLock()
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
        
    def get_entry(self, key: str) -> RateLimitEntry:
        """Get or create rate limit entry for a key"""
        with self._lock:
            if key not in self._store:
                self._store[key] = RateLimitEntry()
            return self._store[key]
    
    def update_entry(self, key: str, entry: RateLimitEntry):
        """Update rate limit entry"""
        with self._lock:
            self._store[key] = entry
            
    def cleanup_expired_entries(self, max_age: int = 3600):  # 1 hour
        """Remove expired entries to prevent memory leaks"""
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
            
        with self._lock:
            expired_keys = [
                key for key, entry in self._store.items()
                if current_time - entry.last_request > max_age
            ]
            for key in expired_keys:
                del self._store[key]
            
            self.last_cleanup = current_time
            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired rate limit entries")

class RateLimiterMiddleware(BaseHTTPMiddleware):
    """
    Advanced rate limiting middleware with multiple algorithms and dynamic configuration
    """
    
    def __init__(self, app, config_path: str = "config/rate_limits.yaml"):
        super().__init__(app)
        self.config_path = config_path
        self.config = self._load_config()
        self.store = InMemoryRateLimitStore()
        self.enabled = True
        
        # Rate limit algorithms
        self.algorithms = {
            "token_bucket": self._token_bucket_check,
            "sliding_window": self._sliding_window_check,
            "fixed_window": self._fixed_window_check
        }
        
    def _load_config(self) -> Dict[str, Any]:
        """Load rate limiting configuration from YAML file"""
        try:
            # Use absolute path resolution
            if not os.path.isabs(self.config_path):
                # Get the project root directory (assuming backend is in project root)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = Path(project_root) / self.config_path
            else:
                config_file = Path(self.config_path)
                
            logger.info(f"Loading rate limit config from: {config_file.absolute()}")
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"Successfully loaded rate limiting configuration from {config_file}")
                return config or {}
            else:
                logger.warning(f"Rate limit config file not found at: {config_file.absolute()}")
                logger.warning(f"Current working directory: {os.getcwd()}")
                logger.warning(f"Config directory contents: {list(config_file.parent.iterdir()) if config_file.parent.exists() else 'Directory does not exist'}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load rate limit config from {self.config_path}: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Default rate limiting configuration"""
        return {
            "global_defaults": {
                "anonymous_users": {
                    "requests_per_minute": 10,
                    "requests_per_hour": 100,
                    "requests_per_day": 500,
                    "burst_allowance": 5,
                    "window_size": 60
                },
                "authenticated_users": {
                    "requests_per_minute": 60,
                    "requests_per_hour": 1000,
                    "requests_per_day": 10000,
                    "burst_allowance": 20,
                    "window_size": 60
                }
            },
            "endpoint_specific": {}
        }
    
    def _get_user_type(self, request: Request) -> str:
        """Determine user type from request"""
        # Check if user is authenticated
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # Check if admin user (you would implement this based on JWT token)
            # For now, return authenticated
            return "authenticated_users"
        return "anonymous_users"
    
    def _get_rate_limit_config(self, request: Request) -> Dict[str, Any]:
        """Get rate limit configuration for specific request"""
        user_type = self._get_user_type(request)
        
        # Check for endpoint-specific config
        path = request.url.path
        endpoint_config = None
        
        for endpoint_pattern, config in self.config.get("endpoint_specific", {}).items():
            if self._match_endpoint_pattern(path, endpoint_pattern):
                endpoint_config = config
                break
        
        # Get default config for user type
        default_config = self.config.get("global_defaults", {}).get(user_type, {})
        
        # Merge endpoint config with defaults
        if endpoint_config:
            merged_config = default_config.copy()
            merged_config.update(endpoint_config)
            return merged_config
        
        return default_config
    
    def _match_endpoint_pattern(self, path: str, pattern: str) -> bool:
        """Match request path against endpoint pattern (supports wildcards)"""
        if pattern.endswith("/**"):
            base_pattern = pattern[:-3]
            return path.startswith(base_pattern)
        elif pattern.endswith("/*"):
            base_pattern = pattern[:-2]
            path_parts = path.split("/")
            pattern_parts = base_pattern.split("/")
            return len(path_parts) == len(pattern_parts) + 1 and path.startswith(base_pattern)
        else:
            return path == pattern
    
    def _generate_rate_limit_key(self, request: Request, config: Dict[str, Any]) -> str:
        """Generate unique key for rate limiting"""
        client_ip = extract_client_ip(request)
        user_type = self._get_user_type(request)
        
        # Include endpoint in key for endpoint-specific limits
        endpoint_key = request.url.path
        
        return f"rate_limit:{user_type}:{client_ip}:{endpoint_key}"
    
    def _token_bucket_check(self, key: str, config: Dict[str, Any], complexity: int = 1) -> RateLimitResult:
        """Token bucket rate limiting algorithm"""
        entry = self.store.get_entry(key)
        current_time = time.time()
        
        # Get limits from config
        requests_per_minute = config.get("requests_per_minute", 60)
        burst_allowance = config.get("burst_allowance", 10)
        window_size = config.get("window_size", 60)
        
        # Calculate tokens to add
        time_passed = current_time - entry.window_start
        tokens_to_add = (time_passed / window_size) * requests_per_minute
        
        # Update token count (with burst allowance)
        max_tokens = requests_per_minute + burst_allowance
        entry.count = min(max_tokens, entry.count + tokens_to_add)
        entry.window_start = current_time
        
        # Check if request is allowed (considering complexity)
        tokens_needed = complexity
        allowed = entry.count >= tokens_needed
        
        if allowed:
            entry.count -= tokens_needed
        
        entry.last_request = current_time
        self.store.update_entry(key, entry)
        
        # Calculate reset time
        reset_time = datetime.now(timezone.utc) + timedelta(seconds=window_size)
        retry_after = None if allowed else int(window_size - (tokens_needed - entry.count) * window_size / requests_per_minute)
        
        return RateLimitResult(
            allowed=allowed,
            current_count=int(max_tokens - entry.count),
            limit=requests_per_minute,
            window_seconds=window_size,
            reset_time=reset_time,
            retry_after=retry_after,
            rate_limit_key=key
        )
    
    def _sliding_window_check(self, key: str, config: Dict[str, Any], complexity: int = 1) -> RateLimitResult:
        """Sliding window rate limiting algorithm"""
        entry = self.store.get_entry(key)
        current_time = time.time()
        
        # Get limits from config
        requests_per_minute = config.get("requests_per_minute", 60)
        window_size = config.get("window_size", 60)
        
        # Remove old requests outside the window
        window_start = current_time - window_size
        while (entry.request_timestamps and 
               entry.request_timestamps[0].timestamp < window_start):
            entry.request_timestamps.popleft()
        
        # Check if adding this request exceeds limit
        total_complexity = sum(ts.complexity for ts in entry.request_timestamps) + complexity
        allowed = total_complexity <= requests_per_minute
        
        if allowed:
            # Add current request with complexity
            timestamp_entry = TimestampComplexity(
                timestamp=current_time,
                complexity=complexity
            )
            entry.request_timestamps.append(timestamp_entry)
        
        entry.last_request = current_time
        self.store.update_entry(key, entry)
        
        # Calculate reset time and retry after
        reset_time = datetime.now(timezone.utc) + timedelta(seconds=window_size)
        retry_after = None if allowed else int(window_size / 2)  # Conservative estimate
        
        return RateLimitResult(
            allowed=allowed,
            current_count=len(entry.request_timestamps),
            limit=requests_per_minute,
            window_seconds=window_size,
            reset_time=reset_time,
            retry_after=retry_after,
            rate_limit_key=key
        )
    
    def _fixed_window_check(self, key: str, config: Dict[str, Any], complexity: int = 1) -> RateLimitResult:
        """Fixed window rate limiting algorithm"""
        entry = self.store.get_entry(key)
        current_time = time.time()
        
        # Get limits from config
        requests_per_minute = config.get("requests_per_minute", 60)
        window_size = config.get("window_size", 60)
        
        # Calculate current window
        current_window = int(current_time // window_size)
        entry_window = int(entry.window_start // window_size)
        
        # Reset count if we're in a new window
        if current_window != entry_window:
            entry.count = 0
            entry.window_start = current_time
        
        # Check if request is allowed
        allowed = (entry.count + complexity) <= requests_per_minute
        
        if allowed:
            entry.count += complexity
        
        entry.last_request = current_time
        self.store.update_entry(key, entry)
        
        # Calculate reset time
        next_window_start = (current_window + 1) * window_size
        reset_time = datetime.fromtimestamp(next_window_start, timezone.utc)
        retry_after = None if allowed else int(next_window_start - current_time)
        
        return RateLimitResult(
            allowed=allowed,
            current_count=entry.count,
            limit=requests_per_minute,
            window_seconds=window_size,
            reset_time=reset_time,
            retry_after=retry_after,
            rate_limit_key=key
        )
    
    def _should_bypass_rate_limit(self, request: Request) -> bool:
        """Check if request should bypass rate limiting"""
        bypass_config = self.config.get("bypass_conditions", {})
        
        # Check trusted IPs
        client_ip = extract_client_ip(request)
        trusted_ips = bypass_config.get("trusted_ips", [])
        if client_ip in trusted_ips:
            return True
        
        # Check health check paths
        health_check_config = bypass_config.get("health_checks", {})
        health_paths = health_check_config.get("paths", [])
        if request.url.path in health_paths:
            return True
        
        # Check health check user agents
        user_agent = request.headers.get("user-agent", "")
        health_user_agents = health_check_config.get("user_agents", [])
        if user_agent in health_user_agents:
            return True
        
        return False
    
    def _log_rate_limit_violation(self, request: Request, result: RateLimitResult):
        """Log rate limit violation for monitoring"""
        client_ip = extract_client_ip(request)
        
        security_event = SecurityEvent(
            event_type=SecurityEventType.RATE_LIMIT_VIOLATION,
            user_id=getattr(request.state, 'user_id', None),
            store_id=getattr(request.state, 'store_id', None),
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            endpoint=request.url.path,
            method=request.method,
            threat_level=ThreatLevel.MEDIUM,
            details={
                "rate_limit_key": result.rate_limit_key,
                "current_count": result.current_count,
                "limit": result.limit,
                "retry_after": result.retry_after
            },
            trace_id=generate_trace_id()
        )
        
        logger.warning(f"Rate limit exceeded for {client_ip} on {request.method} {request.url.path}")
    
    async def dispatch(self, request: Request, call_next):
        """Main middleware dispatch method"""
        if not self.enabled:
            return await call_next(request)
        
        # Check if should bypass rate limiting
        if self._should_bypass_rate_limit(request):
            return await call_next(request)
        
        # Cleanup expired entries periodically
        self.store.cleanup_expired_entries()
        
        # Get rate limit configuration
        config = self._get_rate_limit_config(request)
        if not config:
            return await call_next(request)
        
        # Calculate request complexity
        complexity = calculate_request_complexity(request)
        
        # Generate rate limit key
        rate_limit_key = self._generate_rate_limit_key(request, config)
        
        # Use sliding window algorithm by default
        algorithm = config.get("algorithm", "sliding_window")
        rate_limit_check = self.algorithms.get(algorithm, self._sliding_window_check)
        
        # Check rate limit
        result = rate_limit_check(rate_limit_key, config, complexity)
        
        if not result.allowed:
            # Log the violation
            self._log_rate_limit_violation(request, result)
            
            # Return rate limit exceeded response
            headers = {
                "X-RateLimit-Limit": str(result.limit),
                "X-RateLimit-Remaining": str(max(0, result.limit - result.current_count)),
                "X-RateLimit-Reset": str(int(result.reset_time.timestamp())),
                "X-RateLimit-Window": str(result.window_seconds),
                "Retry-After": str(result.retry_after) if result.retry_after else "60"
            }
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Try again in {result.retry_after or 60} seconds.",
                    "retry_after": result.retry_after or 60,
                    "limit": result.limit,
                    "window_seconds": result.window_seconds
                },
                headers=headers
            )
        
        # Add rate limit headers to successful responses
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(result.limit)
        response.headers["X-RateLimit-Remaining"] = str(max(0, result.limit - result.current_count))
        response.headers["X-RateLimit-Reset"] = str(int(result.reset_time.timestamp()))
        response.headers["X-RateLimit-Window"] = str(result.window_seconds)
        
        return response 