import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { API_URL } from '../../config/api';
import { authService } from '../../services/authService';
import { useTranslation } from 'react-i18next';
import { logger } from '../../utils/logger';
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Alert,
  Divider,
} from '@mui/material';
import { SecurityOutlined } from '@mui/icons-material';

const TwoFactorAuth: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [showVerification, setShowVerification] = useState(false);
  const [alert, setAlert] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Validation state for verification code
  const [verificationCodeError, setVerificationCodeError] = useState('');

  // Validation function for 2FA code
  const validate2FACode = (value: string): string => {
    if (!value.trim()) return t('settings.validation.codeRequired', 'Verification code is required');
    if (!/^\d{6}$/.test(value)) return t('settings.validation.codeInvalid', 'Verification code must be exactly 6 digits');
    return '';
  };

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await fetch(`${API_URL}/api/auth/2fa/status/${user?.email}`, {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        });
        if (response.ok) {
          const data = await response.json();
          setIs2FAEnabled(data.enabled);
        }
      } catch (error) {
        logger.error('Error checking 2FA status:', error);
      }
    };
    if (user?.email) {
      checkStatus();
    }
  }, [user?.email]);

  const handleError = (error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    return {
      type: 'error' as const,
      message: errorMessage
    };
  };

  const handle2FAToggle = async () => {
    try {
      setIsLoading(true);
      if (!is2FAEnabled) {
        const response = await fetch(`${API_URL}/api/auth/2fa/enable`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authService.getToken()}`
          },
          body: JSON.stringify({ email: user?.email }),
        });

        if (response.ok) {
          setShowVerification(true);
          setAlert({
            type: 'info',
            message: 'Please check your email for the verification code'
          });
        } else {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to enable 2FA');
        }
      } else {
        const response = await fetch(`${API_URL}/api/auth/2fa/disable`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authService.getToken()}`,
          },
        });

        if (response.ok) {
          setIs2FAEnabled(false);
          setShowVerification(false);
          setAlert({
            type: 'success',
            message: 'Two-factor authentication has been disabled'
          });
        } else {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to disable 2FA');
        }
      }
    } catch (error) {
      setAlert(handleError(error));
      // Reset the toggle if there was an error
      setIs2FAEnabled(prev => !prev);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    // Validate the code before submission
    const codeError = validate2FACode(verificationCode);
    setVerificationCodeError(codeError);
    
    if (codeError) {
      setAlert({
        type: 'error',
        message: 'Please enter a valid 6-digit verification code'
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/api/auth/2fa/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({
          email: user?.email,
          code: verificationCode,
        }),
      });

      if (response.ok) {
        setIs2FAEnabled(true);
        setShowVerification(false);
        setVerificationCode('');
        setVerificationCodeError('');
        setAlert({
          type: 'success',
          message: 'Two-factor authentication has been enabled successfully'
        });
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Invalid verification code');
      }
    } catch (error) {
      setAlert(handleError(error));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SecurityOutlined sx={{ mr: 2, color: 'primary.main' }} />
        <Typography variant="h6">Two-Factor Authentication</Typography>
      </Box>
      
      <Divider sx={{ mb: 3 }} />
      
      {alert && (
        <Alert 
          severity={alert.type} 
          sx={{ mb: 3 }}
          onClose={() => setAlert(null)}
        >
          {alert.message}
        </Alert>
      )}

      <Box sx={{ mb: 3 }}>
        <FormControlLabel
          control={
            <Switch
              checked={is2FAEnabled}
              onChange={handle2FAToggle}
              color="primary"
              disabled={isLoading || showVerification}
            />
          }
          label="Enable Two-Factor Authentication"
        />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Enhance your account security by enabling two-factor authentication.
          You'll receive a verification code via email when signing in.
        </Typography>
      </Box>

      {showVerification && (
        <Box sx={{ mt: 3 }}>
          <TextField
            fullWidth
            label="Verification Code"
            variant="outlined"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            onBlur={() => {
              const error = validate2FACode(verificationCode);
              setVerificationCodeError(error);
            }}
            autoComplete="one-time-code"
            error={!!verificationCodeError}
            helperText={verificationCodeError}
            inputProps={{
              maxLength: 6,
              inputMode: "numeric"
            }}
            sx={{ mb: 2 }}
            disabled={isLoading}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={handleVerifyCode}
            disabled={!verificationCode || isLoading || !!verificationCodeError}
          >
            {isLoading ? 'Verifying...' : 'Verify Code'}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default TwoFactorAuth; 