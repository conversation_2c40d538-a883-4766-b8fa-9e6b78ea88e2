import { MetricCardConfig } from '../components/dashboard/StaticMetricGrid';
import { StoreAnalysis } from '../services/storeService';
import { TFunction } from 'i18next';

/**
 * Generates metric card configurations from store analysis data
 */
export function generateMetricCards(
  storeAnalysis: StoreAnalysis | null,
  t: TFunction<"translation", undefined>
): MetricCardConfig[] {

  if (!storeAnalysis) {
    return [];
  }

  const metrics: MetricCardConfig[] = [
    // Core metrics (always included)
    {
      id: 'total_revenue',
      type: 'metric',
      title: t('dashboard.metrics.totalRevenue', 'Total Revenue'),
      value: `UYU ${Number(storeAnalysis?.metrics?.total_revenue || 0).toLocaleString()}`,
      trend: (storeAnalysis?.changes?.revenue_change_pct ?? 0) > 0 ? 'up' :
             (storeAnalysis?.changes?.revenue_change_pct ?? 0) < 0 ? 'down' : 'neutral',
      trendPercentage: Math.abs(storeAnalysis?.changes?.revenue_change_pct || 5),
      trendLabel: t('dashboard.metrics.fromLastMonth', 'from last month'),
      chartData: [
        { value: Math.max((Number(storeAnalysis?.metrics?.total_revenue || 0) * 0.80), 1000) },
        { value: Math.max((Number(storeAnalysis?.metrics?.total_revenue || 0) * 0.85), 1500) },
        { value: Math.max((Number(storeAnalysis?.metrics?.total_revenue || 0) * 0.90), 2000) },
        { value: Math.max((Number(storeAnalysis?.metrics?.total_revenue || 0) * 0.95), 2500) },
        { value: Number(storeAnalysis?.metrics?.total_revenue || 0) }
      ],
      tooltip: storeAnalysis?.insights?.performance_metrics_summary
    },
    {
      id: 'total_orders',
      type: 'metric',
      title: t('dashboard.metrics.orderCount', 'Total Orders'),
      value: storeAnalysis?.metrics?.order_count || 0,
      trend: (storeAnalysis?.changes?.orders_change_pct ?? 0) > 0 ? 'up' :
             (storeAnalysis?.changes?.orders_change_pct ?? 0) < 0 ? 'down' : 'neutral',
      trendPercentage: Math.abs(storeAnalysis?.changes?.orders_change_pct || 3),
      trendLabel: t('dashboard.metrics.fromLastMonth', 'from last month'),
      chartData: [
        { value: Math.max((Number(storeAnalysis?.metrics?.order_count || 0) * 0.80), 5) },
        { value: Math.max((Number(storeAnalysis?.metrics?.order_count || 0) * 0.85), 10) },
        { value: Math.max((Number(storeAnalysis?.metrics?.order_count || 0) * 0.90), 15) },
        { value: Math.max((Number(storeAnalysis?.metrics?.order_count || 0) * 0.95), 20) },
        { value: Number(storeAnalysis?.metrics?.order_count || 0) }
      ],
      tooltip: storeAnalysis?.insights?.performance_metrics_summary
    },
    {
      id: 'avg_order_value',
      type: 'metric',
      title: t('dashboard.metrics.avgOrderValue', 'Average Order Value'),
      value: `UYU ${Number(storeAnalysis?.metrics?.avg_order_value || 0).toLocaleString()}`,
      trend: (storeAnalysis?.changes?.avg_order_value_change_pct ?? 0) > 0 ? 'up' :
             (storeAnalysis?.changes?.avg_order_value_change_pct ?? 0) < 0 ? 'down' : 'neutral',
      trendPercentage: Math.abs(storeAnalysis?.changes?.avg_order_value_change_pct || 2),
      trendLabel: t('dashboard.metrics.fromLastMonth', 'from last month'),
      chartData: [
        { value: Math.max((Number(storeAnalysis?.metrics?.avg_order_value || 0) * 0.80), 100) },
        { value: Math.max((Number(storeAnalysis?.metrics?.avg_order_value || 0) * 0.85), 150) },
        { value: Math.max((Number(storeAnalysis?.metrics?.avg_order_value || 0) * 0.90), 200) },
        { value: Math.max((Number(storeAnalysis?.metrics?.avg_order_value || 0) * 0.95), 250) },
        { value: Number(storeAnalysis?.metrics?.avg_order_value || 0) }
      ],
      tooltip: storeAnalysis?.insights?.performance_metrics_summary
    },
    {
      id: 'customer_count',
      type: 'metric',
      title: t('dashboard.metrics.customerCount', 'Total Customers'),
      value: storeAnalysis?.metrics?.customer_count || 0,
      trend: (storeAnalysis?.changes?.customers_change_pct ?? 0) > 0 ? 'up' :
             (storeAnalysis?.changes?.customers_change_pct ?? 0) < 0 ? 'down' : 'neutral',
      trendPercentage: Math.abs(storeAnalysis?.changes?.customers_change_pct || 1),
      trendLabel: t('dashboard.metrics.fromLastMonth', 'from last month'),
      chartData: [
        { value: Math.max((Number(storeAnalysis?.metrics?.customer_count || 0) * 0.80), 5) },
        { value: Math.max((Number(storeAnalysis?.metrics?.customer_count || 0) * 0.85), 10) },
        { value: Math.max((Number(storeAnalysis?.metrics?.customer_count || 0) * 0.90), 15) },
        { value: Math.max((Number(storeAnalysis?.metrics?.customer_count || 0) * 0.95), 20) },
        { value: Number(storeAnalysis?.metrics?.customer_count || 0) }
      ],
      tooltip: storeAnalysis?.insights?.customer_insights
    }
  ];

  // Add enhanced metrics
  if (storeAnalysis?.products?.total !== undefined) {
    metrics.push({
      id: 'total_products',
      type: 'metric',
      title: t('dashboard.metrics.productCount', 'Total Products'),
      value: storeAnalysis.products.total,
      trend: 'neutral',
      trendLabel: t('dashboard.metrics.activeProducts', 'active products'),
      tooltip: storeAnalysis?.insights?.product_insights
    });
  }

  if (storeAnalysis?.activity?.visit_count_30d !== undefined) {
    const prev30dVisits = (storeAnalysis.activity.visit_count_90d - storeAnalysis.activity.visit_count_30d) / 2;
    metrics.push({
      id: 'visits_30d',
      type: 'metric',
      title: t('dashboard.metrics.visitsCount', 'Visits (30d)'),
      value: storeAnalysis.activity.visit_count_30d,
      trend: storeAnalysis.activity.visit_count_30d > prev30dVisits ? 'up' : 'down',
      trendPercentage: prev30dVisits > 0 
        ? Math.round(Math.abs((storeAnalysis.activity.visit_count_30d - prev30dVisits) / prev30dVisits) * 100) 
        : 4,
      trendLabel: t('dashboard.metrics.vsPrevPeriod', 'vs previous period'),
      tooltip: t('dashboard.tooltips.visitsCount', 'Number of visits to your store in the last 30 days.')
    });
  }

  if (storeAnalysis?.customers?.abandoned_cart_count !== undefined) {
    metrics.push({
      id: 'abandoned_carts',
      type: 'metric',
      title: t('dashboard.metrics.abandonedCarts', 'Abandoned Carts'),
      value: storeAnalysis.customers.abandoned_cart_count,
      trend: 'down', // Usually want this metric to go down
      trendPercentage: 2,
      trendLabel: t('dashboard.metrics.potentialSales', 'potential sales'),
      tooltip: `${t('dashboard.tooltips.abandonedCartValue', 'Abandoned cart value')}: UYU ${Number(storeAnalysis.customers.abandoned_cart_total_value || 0).toLocaleString()}`
    });
  }

  if (storeAnalysis?.meta?.total_followers !== undefined) {
    metrics.push({
      id: 'meta_followers',
      type: 'metric',
      title: t('dashboard.metrics.socialFollowers', 'Social Followers'),
      value: storeAnalysis.meta.total_followers,
      trend: 'up', // Usually want this metric to go up
      trendPercentage: 3,
      trendLabel: t('dashboard.metrics.activeProfiles', 'active profiles'),
      tooltip: storeAnalysis?.insights?.social_media_insights
    });
  }

  if (storeAnalysis?.meta?.total_engagement !== undefined) {
    metrics.push({
      id: 'meta_engagement',
      type: 'metric',
      title: t('dashboard.metrics.socialEngagement', 'Social Engagement'),
      value: storeAnalysis.meta.total_engagement,
      trend: 'up', // Usually want this metric to go up
      trendPercentage: 5,
      trendLabel: t('dashboard.metrics.interactions', 'interactions'),
      tooltip: storeAnalysis?.insights?.social_media_insights
    });
  }

  return metrics;
} 