# D-Unit Frontend Documentation

## Overview

D-Unit is a modern React application built with TypeScript, providing an AI-driven e-commerce analytics platform with Meta (Facebook and Instagram) integration. This document serves as a complete guide for understanding the frontend architecture, implementation details, and component references.

**Latest Updates:**
- **Enhanced Backend Security**: Frontend now works with enterprise-grade 4-layer security gateway
- **Comprehensive Testing**: Backend testing infrastructure ensures reliable API interactions
- **Advanced Error Handling**: Enhanced error handling for security and cost management scenarios
- **General Fixes**: For a comprehensive list of recent bug fixes and improvements, refer to the [General Fixes Documentation](../../docs/general-fixes.md).

## Table of Contents

1. [Overview](#overview)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Environment Configuration](#environment-configuration)
5. [Architecture Overview](#architecture-overview)
   - [HTTPS Configuration](#https-configuration)
   - [Backend Security Integration](#backend-security-integration)
   - [Meta Integration Components](#meta-integration-components)
   - [Meta Authentication Components](#meta-authentication-components)
   - [Meta Authentication Flow](#meta-authentication-flow)
   - [Typography Standards](#typography-standards)
6. [Core Features](#core-features)
   - [Authentication System](#authentication-system)
   - [Dashboard Components](#dashboard-components)
   - [Meta Integration](#meta-integration)
   - [Data Visualization](#data-visualization)
   - [Chat Application](#chat-application)
   - [Cookie Management](#cookie-management)
   - [Cookie Policy System](#cookie-policy-system)
7. [Component Reference](#component-reference)
   - [Common Components](#common-components)
   - [Meta Components](#meta-components)
   - [Dashboard Components](#dashboard-components)
   - [Auth Components](#auth-components)
   - [Settings Components](#settings-components)
   - [Chat Components](#chat-components)
   - [Admin Components](#admin-components)
8. [Context API Implementation](#context-api-implementation)
   - [Auth Context](#auth-context)
   - [Meta Permissions Context](#meta-permissions-context)
9. [Services](#services)
   - [API Service](#api-service)
   - [Auth Service](#auth-service)
   - [Store Service](#store-service)
   - [Meta Store Service](#meta-store-service)
10. [Development Workflow](#development-workflow)
11. [Routing and Navigation](#routing-and-navigation)
12. [Troubleshooting](#troubleshooting)
    - [Meta Integration Issues](#troubleshooting-meta-integration)
13. [Recent Updates](#recent-updates)
14. [Future Enhancements](#future-enhancements)

## Backend Security Integration

### Security Gateway Integration
The frontend integrates seamlessly with the backend's enterprise-grade security gateway:

#### **Security Features Affecting Frontend**
- **Rate Limiting**: Frontend handles rate limit responses gracefully with user-friendly messages
- **Request Validation**: All user input is validated both client-side and server-side
- **Cost Management**: Real-time budget monitoring with tier-based controls
- **Security Headers**: Enhanced HTTPS configuration with OWASP Top 10 protection

#### **Error Handling Enhancement**
The frontend now handles additional security-related error scenarios:

```typescript
// Security-aware error handling
export interface SecurityError {
  type: 'RATE_LIMIT' | 'VALIDATION' | 'COST_LIMIT' | 'SECURITY_THREAT';
  message: string;
  details?: any;
  retryAfter?: number;
}

// Enhanced API service with security error handling
class ApiService {
  handleSecurityError(error: SecurityError) {
    switch (error.type) {
      case 'RATE_LIMIT':
        // Show rate limit notification with retry time
        break;
      case 'COST_LIMIT':
        // Show budget limit reached notification
        break;
      case 'SECURITY_THREAT':
        // Show security alert and potentially logout
        break;
    }
  }
}
```

#### **Cost Management Integration**
Frontend components now work with backend cost tracking:

- **Budget Awareness**: Chat and API-heavy components display budget status
- **Tier Limitations**: UI adapts based on user's store tier (Free, Basic, Premium, Enterprise)
- **Cost Optimization**: Smart request batching to minimize API costs
- **Real-time Monitoring**: Live cost tracking display in admin dashboard

#### **Testing Integration Benefits**
The comprehensive backend testing ensures:

- **Reliable API Responses**: 100% test coverage prevents frontend breaking changes
- **Performance Consistency**: Load testing ensures frontend performance expectations are met
- **Security Validation**: Security tests prevent vulnerabilities that could affect frontend
- **Cost Accuracy**: Cost tracking tests ensure accurate billing display in frontend

## HTTPS Configuration

The frontend supports HTTPS for secure communication, which is essential for Meta integration and secure authentication.

### SSL Certificate Setup

The frontend uses SSL certificates located in the `certs` directory:

```
frontend/certs/
├── cert.crt          # SSL certificate
├── cert.key          # Private key
├── ca.crt            # Certificate Authority certificate
└── ca.key            # Certificate Authority private key
```

## Meta Integration Components

### Overview Components

The Meta integration includes several key components for data visualization and analysis:

1. **MetaAdMetricsPanel.tsx**
   
   **Component Implementation**
   This component presents Meta advertising metrics in a clean, organized grid with:
   - Comprehensive error handling for various API and data issues
   - Proper loading state display with a user-friendly indicator
   - Responsive grid layout using Material-UI's Grid system
   - Dynamic metric rendering based on available data
   - Consistent formatting through the MetricCard components
   - Clear visual organization of multiple metric types
   - Accessible layout with proper hierarchy and navigation
   - Clean separation of presentation and data handling logic

2. **Instagram Business Integration**
   
   **Component Implementation**
   Instagram business metrics are handled through platform-specific implementations in existing Meta components:
   - MetaAdMetricsPanel.tsx with platform-specific data fetching
   - Platform detection and appropriate metric endpoint routing
   - Instagram-specific API parameter handling in dataService.ts
   - Specialized error handling for Instagram API limitations
   - Integration with existing MetaDashboard.tsx for unified experience

### Meta Authentication Components

The Meta authentication system has been enhanced with several key improvements to ensure reliable authentication and session management:

1. **MetaLoginButton.tsx**
   
   The enhanced Meta login button provides a comprehensive authentication experience with improved security and error handling:

   **Component Properties**
   - Flexible configuration through props (loginMode, onSuccess, onFailure, className)
   - Support for customized button text based on state
   - Optional success and failure callbacks for better integration
   - Styling customization through className prop

   **State Management**
   - Loading state tracking for UI feedback during authentication
   - Error state management for displaying clear user messages
   - Clean separation of concerns with proper state initialization
   - Type-safe implementation with TypeScript interfaces

   **Security Features**
   - CSRF protection using state parameter for request validation
   - Secure token generation and validation with error checking
   - Protection against replay attacks and cross-site vulnerabilities
   - Secure token storage in sessionStorage with proper encryption

   **SDK Integration**
   - Reliable SDK initialization with comprehensive error handling
   - Proper permission scope management for different Meta API features
   - Configurability for different authentication use cases
   - Consistent error propagation to parent components

   **Error Handling**
   - Comprehensive try/catch implementation for all authentication steps
   - Detailed error messaging for troubleshooting
   - Clear visual feedback for users when authentication fails
   - Proper error propagation to parent components with specific error types

   **User Experience**
   - Clear loading indicator during authentication process
   - Disabled button state during processing to prevent double-clicks
   - Error display for authentication failures with retry options
   - Clean, modern UI with Material UI components and consistent styling

   This component provides a secure, user-friendly way to authenticate with Meta services while implementing best practices for security and error handling.

2. **MetaAuthService (auth.ts)**
   
   The enhanced authentication service provides comprehensive session management functionality:

   **Token Management**
   - Secure token storage in sessionStorage with expiration tracking
   - Clear separation of token and expiration time storage for better security
   - Automatic token validation before API calls to prevent failed requests
   - Token expiration detection with proper refresh handling

   **Session Validation**
   - Complete session validation logic with status checks
   - Expiration time checking with time drift compensation
   - Automatic session clearing for invalid or expired tokens
   - Proper type safety throughout the implementation with TypeScript

   **SDK Initialization**
   - Robust SDK initialization with retry mechanisms for reliability
   - Exponential backoff for failed initialization attempts to prevent rate limiting
   - Proper error handling and logging for troubleshooting
   - Status monitoring during initialization with event dispatching

   **Authentication Flow**
   - Secure login implementation with CSRF protection against attacks
   - Comprehensive permission request handling for Facebook and Instagram
   - State parameter validation to prevent cross-site attacks
   - Clear error messaging for different authentication failure scenarios

   **Error Handling**
   - Detailed error classification and handling for specific API errors
   - User-friendly error messages for common authentication issues
   - Proper type safety for error handling with TypeScript interfaces
   - Consistent error propagation to calling components

   This service ensures reliable Meta authentication while implementing security best practices for token handling and session management.

3. **SDKInitialization (init.ts)**
   
   The SDK initialization module provides robust initialization of the Meta JavaScript SDK with:

   **Event System**
   - Custom events for tracking SDK initialization status
   - SDK_READY_EVENT triggered when initialization is complete
   - SDK_ERROR_EVENT fired when errors occur during initialization
   - Comprehensive event detail information for debugging

   **Queue Management**
   - Initialization promise for preventing duplicate initialization attempts
   - Proper reference handling to prevent memory leaks
   - Initialization state tracking for better reliability
   - Request queueing during initialization for consistent behavior

   **Script Loading**
   - Safe asynchronous script element creation with error handling
   - Proper error handling for script loading failures
   - Cross-origin safety with appropriate script attributes
   - Version and environment awareness for SDK compatibility

   **API Initialization**
   - Secure configuration of the Meta JavaScript SDK with proper settings
   - Proper App ID and version configuration from environment variables
   - Cookie management settings for better security
   - Status monitoring during initialization with event dispatching

   **Retry System**
   - Comprehensive retry mechanism for reliability in unstable networks
   - Exponential backoff with configurable attempt limits
   - Detailed error logging for debugging initialization issues
   - Timeout handling for unresponsive SDK initialization

   **Security Checks**
   - HTTPS requirement verification for Meta SDK compliance
   - Automatic redirection to secure protocol when needed in development
   - Environment-aware protocol handling for different environments
   - Clear warning messages for insecure connection attempts

   This implementation ensures reliable SDK initialization even in challenging network conditions while implementing best practices for security and error recovery.

4. **Configuration Validation**
   
   The configuration validation module ensures that all required Meta integration settings are properly configured:

   **Environment Variable Checks**
   - Verification of required environment variables for Meta integration
   - Validation of Meta App ID configuration for correct format
   - Client token verification for proper authentication
   - API version compatibility checks for feature support

   **Security Configuration**
   - HTTPS protocol verification in production environments
   - Warning generation for insecure connection attempts
   - Clear documentation of security requirements for developers
   - Protocol guidance for local development environments

   **Validation Process**
   - Structured validation with comprehensive issue collection
   - Detailed logging of configuration problems for troubleshooting
   - Clear, actionable error messages for resolving issues
   - Guidance for resolving common configuration problems

   **Startup Integration**
   - Automatic validation during application startup
   - Non-blocking validation for better user experience
   - Console warnings for development visibility
   - Graceful handling of missing configurations in development

   This validation ensures that the Meta integration is properly configured before attempting to use the SDK, preventing runtime errors and providing clear guidance for resolving configuration issues.

### Meta Authentication Flow

The improved authentication flow provides a more reliable and secure experience:

1. **Initialization Phase**
   - Application startup triggers SDK initialization with retry logic
   - Configuration validation ensures all required variables are present
   - HTTPS verification prevents common integration issues
   - SDK loading uses asynchronous script loading with proper error handling

2. **Authentication Phase**
   - User clicks "Log in with Meta" button
   - CSRF protection with state parameter prevents attacks
   - Comprehensive permission requests for both Facebook and Instagram
   - Proper error handling for permission denials
   - Token storage in sessionStorage with expiration tracking

3. **Session Management**
   - Automatic token validation before API calls
   - Token expiration detection to prevent failed requests
   - Re-authentication prompts when sessions expire
   - Seamless refresh for uninterrupted user experience
   - Clear error messages for authentication issues

4. **Error Recovery**
   - Automatic retry for transient errors
   - Exponential backoff to prevent API rate limiting
   - Detailed error messages for troubleshooting
   - Graceful degradation when authentication fails
   - User-friendly re-authentication flow

### Instagram Business Integration (March 2024)

The frontend has been enhanced with specialized support for Instagram Business accounts, which have different API endpoints and data structures compared to Facebook pages.

#### IGUser Support

- **Enhanced Platform Detection**: The system now properly identifies Instagram Business accounts through both the `platform` field and `category` field detection
- **Direct IGUser Node Type Detection**: Added support for identifying Instagram user nodes in the Meta Graph API
- **Proper Business Account ID Retrieval**: Implemented robust ID extraction for Instagram business accounts
- **Support for Different Account Types**: Added compatibility with both creator and business Instagram accounts

#### Ad Metrics Processing

- **Advanced Platform Detection**: The system detects platform type and uses appropriate metrics endpoints
- **Instagram-Specific Metrics**: Support for Instagram-specific metrics like:
  - Total Spend
  - Impressions
  - Clicks
  - Conversions
  - CTR
  - CPC
  - Cost per Conversion
  - ROI

#### API Compatibility Updates

- **Meta Graph API v22.0 Support**: Updated API calls to comply with the latest Graph API version
- **Removed Deprecated Metrics**: Eliminated usage of deprecated insights metrics
- **Enhanced Error Handling**: Improved error messages for Instagram-specific API issues
- **Date Range Management**: Better handling of date ranges compatible with both platforms

### Troubleshooting Meta Authentication

If you encounter issues with Meta authentication, check these common problems:

1. **SSL/HTTPS Issues**
   - Meta SDK requires HTTPS, even in local development
   - Check that your development server is using a valid SSL certificate
   - Verify that browser doesn't block mixed content
   - Use proper certificate paths in both frontend and backend configuration

2. **Configuration Problems**
   - Verify all required environment variables are set correctly:
     ```
     VITE_FACEBOOK_APP_ID=your_app_id
     VITE_FACEBOOK_APP_CLIENT_TOKEN=your_client_token
     VITE_FACEBOOK_APP_VERSION=v22.0
     ```
   - Check Meta Developer Console for correct app settings
   - Verify that OAuth redirect URIs are properly configured
   - Ensure app domains match your development and production environments

3. **Permission Issues**
   - Make sure your app requests all required permissions
   - Check that the Meta app has been properly reviewed for required permissions
   - Verify user is granting all requested permissions
   - Use `auth_type: 'rerequest'` to prompt for denied permissions

4. **Browser Issues**
   - Clear browser cookies and sessionStorage
   - Check browser console for specific error messages
   - Disable privacy extensions that might block Meta scripts
   - Try an incognito/private browsing window

5. **API Limitations**
   - Check for API rate limiting issues
   - Verify app is in appropriate development/production mode
   - Ensure API version compatibility
   - Review Meta Developer Console for app status and issues

For persistent issues, check the browser console for detailed error messages and the network tab for failed API requests.

### Data Visualization Components

1. **MetricCard.tsx**
   
   This reusable component provides a standardized way to display metric information:

   **Component Features**
   - Clean, card-based design for consistent metric presentation across the application
   - Support for loading states with skeleton placeholder for better user experience
   - Change indicator with color coding for trend visualization (green for positive, red for negative)
   - Properly typed props for type safety and developer assistance
   - Customizable formatting options for different metric types (number, currency, percentage)

   **Visual Elements**
   - Card header with clear metric title for easy identification
   - Large, prominent value display for important metrics
   - Optional change indicator with directional styling for trend visualization
   - Consistent spacing and typography following application standards
   - Support for additional context information below the main value

   **Accessibility**
   - Clear visual hierarchy for screen readers and better user understanding
   - Proper contrast ratios for text elements ensuring readability
   - Semantic markup for better screen reader support and SEO
   - Keyboard navigation support for improved accessibility
   - Appropriate aria attributes for screen reader compatibility

   **Integration**
   - Easily composable within grid layouts for dashboard construction
   - Consistent styling with the application theme for visual coherence
   - Small footprint for efficient rendering in dashboard grids
   - Responsive design that adapts to all screen sizes automatically
   - Support for different data precision and formatting requirements

2. **Chart Components**
   
   Data visualization is handled through specialized chart components:

   **Available Chart Components**
   - ProductPerformanceChart.tsx for product analytics
   - DemographicsChart.tsx for customer demographics
   - MarketPositionChart.tsx for price trend analysis
   - CorrelationHeatmap.tsx for advanced correlation analysis
   - Various Meta-specific charts for social media metrics

   **Chart Features**
   - Responsive design that adapts to container size
   - Interactive tooltips with detailed information
   - Consistent styling with application theme
   - Loading states and error handling
   - TypeScript integration for type safety

### Mock Data Handling

The frontend includes comprehensive mock data handling for development and testing:

1. **Mock Data Service**
   
   This service facilitates the fetching of mock metrics for development and testing:

   **API Integration**
   - Consistent interface with real API endpoints for seamless switching
   - Query parameter support for filtering and customizing mock data
   - Proper error handling and logging for debugging
   - Async/await pattern for Promise-based requests that match real API behavior
   - Type-safe implementation with TypeScript interfaces

   **Configuration**
   - Supports store ID, page ID, and time range parameters for realistic data
   - Uses the same response format as real API endpoints for consistency
   - Provides realistic mock data that mimics production patterns
   - Includes proper types for type-safe integration with components
   - Configurable data variance for simulating different scenarios

   **Error Handling**
   - Comprehensive try/catch implementation for robustness
   - Detailed error logging for debugging and troubleshooting
   - Consistent error format with real API for unified error handling
   - Support for simulating specific error conditions for testing
   - Clear distinction between mock and real data with is_mock flag

2. **Mock Data Hook**
   
   This custom hook provides a standardized way to access mock data:

   **State Management**
   - Complete React state management for data, loading, and error states
   - Proper TypeScript types for all state variables
   - Clean state initialization and updates
   - Cancellation handling for unmounted components
   - Support for data refetching with parameter changes

   **Data Fetching**
   - Consistent interface with real data hooks for seamless switching
   - Support for customizing mock data parameters
   - Proper error handling and state updates
   - Loading state management for UI feedback
   - Optional caching for performance optimization

   **Component Integration**
   - Designed to be a drop-in replacement for real data hooks
   - Consistent return format with real hooks for easy switching
   - Support for time range and other filtering options
   - Proper error propagation to components
   - Clear indication of mock data status

3. **Error Handling**
   
   The Meta error boundary component provides robust error handling:

   **Error Capture**
   - Catches and processes errors from Meta components
   - Prevents entire application crashes from Meta API issues
   - Provides fallback UI for error conditions
   - Supports development-specific error messaging
   - Maintains application state despite component failures

   **Fallback UI**
   - Clean, user-friendly error display with Material UI components
   - Clear error messaging appropriate for end users
   - Development-specific detailed error information when needed
   - Visual indicators for different error types
   - Options for retrying operations when appropriate

   **Development Features**
   - Enhanced error details in development mode
   - Mock data suggestion in development environments
   - Detailed console logging for debugging
   - Clear distinction between development and production behavior
   - Support for error tracking and reporting in production

### Store-Specific Components

Components that handle store-specific Meta data:

1. **StoreMetaOverview.tsx**
   
   This component provides a comprehensive overview of store Meta data:

   **Data Integration**
   - Uses the mock data hook for development and testing
   - Handles loading states with appropriate UI feedback
   - Properly types all data with TypeScript interfaces
   - Separates Facebook and Instagram data for platform-specific display
   - Supports real API data seamlessly with the same interface

   **UI Organization**
   - Structured display of Facebook and Instagram metrics
   - Clean, card-based layout for each platform's data
   - Proper loading states with skeleton placeholders
   - Error handling with user-friendly messages
   - Responsive layout that adapts to different screen sizes

   **Component Composition**
   - Composed of platform-specific metric components
   - Maintains separation of concerns for better maintainability
   - Consistent styling across platform components
   - Proper prop drilling for necessary data
   - Clear component boundaries for better code organization

2. **StorePlatformSelector.tsx**
   
   This component enables selection between different Meta platforms for a store:

   **Data Integration**
   - Uses the mock data hook to determine available platforms
   - Dynamically shows available platforms based on store data
   - Properly handles selection state management
   - Passes selection changes to parent components
   - Consistent interface with real data API

   **UI Implementation**
   - Clean Material UI Select component for platform selection
   - Proper labeling of platforms with store context
   - Disabled state handling when no platforms are available
   - Visual distinction between Facebook and Instagram options
   - Support for keyboard navigation and accessibility

   **User Experience**
   - Intuitive platform selection with clear options
   - Immediate feedback on selection change
   - Proper error handling for unavailable platforms
   - Consistent styling with application theme
   - Full keyboard accessibility and screen reader support

## Typography Standards

The D-Unit frontend uses a consistent typography system based on the 'Inter' font family. This modern, clean sans-serif font provides excellent readability across all screen sizes and device types.

### Font Implementation

The Inter font is implemented across the application through:

1. **Google Fonts Import**: Loaded in the main HTML file
   This imports the Inter font family with multiple weight variants (300, 400, 500, 600, 700) using Google Fonts CDN, providing optimized font delivery and browser caching benefits while maintaining consistency across all browsers and devices.

2. **Theme Configuration**: Set as the primary font in the MUI theme
   The theme configuration defines Inter as the primary font for all MUI components, with fallback fonts specified to ensure consistent typography even if the Inter font fails to load. This global configuration centralizes font management in the application.

3. **Tailwind Configuration**: Configured as the default sans font
   This sets Inter as the default sans-serif font in the Tailwind configuration, affecting all Tailwind typography classes. The configuration includes a comprehensive fallback chain for maximum compatibility across platforms.

4. **Global CSS**: Applied to all elements as the default font
   A global CSS reset ensures that all elements consistently use the Inter font family, providing typographic harmony throughout the application. This approach prevents inconsistencies from default browser styles.

### Font Weights

The following standard font weights are used throughout the application:
- Light: 300 (used sparingly for large headings)
- Regular: 400 (body text and general content)
- Medium: 500 (emphasis, buttons, and interactive elements)
- Semibold: 600 (page headings and important elements)
- Bold: 700 (used sparingly for very high emphasis)

Note that we prefer lighter font weights (500 instead of 700) for most headings to achieve a more modern and contemporary look. The specific weights are configured in the theme.ts file.

### Typography Styling Guidelines

For consistent text styling throughout the application, follow these guidelines:

1. **Color Opacity**: Use rgba colors with appropriate opacity values to enhance readability:
   The example demonstrates proper color opacity usage with rgba values for body text (0.85 opacity) and headings (0.95 opacity), which creates visual hierarchy while improving text readability, especially on varying background colors.

2. **Font Sizing**: Use the predefined font sizes from the theme when possible:
   This section demonstrates the comprehensive typography scale implemented through MUI's Typography component variants, showing the exact sizing for headings (h1 through h6) and body text variants with their respective sizes and line heights.

3. **Line Height**: Use appropriate line heights to improve readability:
   Specific line height guidelines are provided for different text elements: headings (1.2-1.4), body text (1.5-1.6), and small text (1.5), ensuring optimal readability while maintaining consistent vertical rhythm throughout the interface.

4. **Consistent Components**: Use Typography components instead of native HTML elements:
   This guideline emphasizes using MUI's Typography components over native HTML elements to ensure consistent styling, proper theme integration, and accessibility benefits across the application.

These styling choices create a clean, modern look while ensuring optimal readability across all device sizes.

### Vite HTTPS Configuration

**vite.config.ts**
This configuration file sets up the development server with HTTPS support by:
- Setting the server port to 5173
- Loading SSL certificates from the certs directory
- Configuring PostCSS with Tailwind and Autoprefixer
- This ensures secure communication for Meta integration which requires HTTPS, even in development

### Environment Configuration

The environment variables are updated to use HTTPS URLs:

The environment configuration includes essential variables for:
- API URL configuration with HTTPS protocol
- Environment identification for conditional logic
- Frontend URL for callback and redirect functions
- Meta API credentials including App ID, client token and API version
- OAuth redirect URL configuration
All these settings ensure proper secure communication between services while maintaining environment-specific configuration.

### Axios HTTPS Configuration

**src/config/axios.ts**
The Axios configuration module handles HTTPS in development by:
- Creating a custom HTTPS agent that accepts self-signed certificates in development mode
- Ensuring secure API communication with the backend
- Maintaining proper security in production environments
- This allows developers to work with HTTPS locally without certificate validation errors

### Meta SDK HTTPS Support

**src/services/meta/init.ts**
The Meta SDK initialization module:
- Initializes the Facebook JavaScript SDK with proper app configuration
- Implements HTTPS protocol checking to ensure secure connections
- Provides automatic redirection to HTTPS if accessed via HTTP in development
- Shows clear warning messages about HTTPS requirements
- This ensures that Meta authentication works correctly in all environments

### Running with HTTPS

To start the frontend with HTTPS, use:

This command starts the development server with HTTPS enabled, serving the application at https://localhost:5173 with all HTTPS security features active, including custom certificate support and proper secure cookie handling.

### Integrated HTTPS Startup Script

**start-https.sh**
This script automates the startup of both frontend and backend with HTTPS by:
- Starting the Python backend with SSL support
- Launching the frontend Vite server with HTTPS
- Managing process termination with proper cleanup
- Providing clear instructions on accessing the services
This simplifies the development workflow when working with Meta integration

### Browser Security Considerations

When accessing the application via HTTPS with self-signed certificates, browsers will show security warnings. To proceed:

1. Click "Advanced" or "Details"
2. Click "Proceed to site" or "Accept the risk and continue"
3. Do this for both the frontend and backend URLs

## Technology Stack

### Core Technologies
- **React 18+**: Frontend library with hooks and functional components
- **TypeScript 4.9+**: Static typing and enhanced developer experience
- **Vite 4.0+**: Modern build tool with HMR and Fast Refresh
- **Material-UI (MUI)**: React component library for consistent UI
- **React Router**: Navigation and routing management
- **Context API**: State management solution
- **Meta SDK**: Facebook/Meta platform integration (v22.0)

## Development Tools

- **ESLint**: Code quality and style enforcement
- **Prettier**: Code formatting standardization
- **Jest & React Testing Library**: Unit and integration testing

## Environment Configuration

The frontend uses Vite's built-in environment handling with environment-specific files:

### Environment Files
- `.env.development` - Development environment configuration
- `.env.production` - Production environment configuration
- `.env.template` - Template for creating new environment files

Environment files are automatically loaded based on the command used:

These NPM commands automatically select the appropriate environment file for configuration:
- Development mode uses .env.development configuration with debugging enabled
- Production mode uses .env.production configuration with optimized settings

### Environment Variables
All frontend environment variables must be prefixed with `VITE_` to be exposed to the application:

The environment configuration includes multiple important categories:
- API Configuration: Defines backend API URL and environment name
- Frontend URL: Specifies the URL for the frontend application
- Meta API Configuration: Contains Meta App ID, client token, and API version
- Google OAuth Configuration: Includes Google client ID for authentication
These configuration values ensure proper integrations with external services and consistent communication between system components.

### Usage in Code
Access environment variables in your components:

Environment variables can be accessed throughout the application using Vite's import.meta.env object. The example demonstrates accessing the API URL and application environment variables, which can be used for environment-specific logic, API endpoint configuration, and conditional feature enabling/disabling.

## Recent Updates

### Meta Integration Enhancements (March 2024)
- **Platform-Specific Time Range Customization**
  - Implemented customized time ranges for Instagram metrics:
    - Engagement metrics: 1-year period for comprehensive trend analysis
    - Follower metrics: 7-day period for accurate growth tracking
    - Impressions and profile views: 30-day period for standard reporting
  - Facebook metrics continue using 30-day periods for consistency
  - Components updated: `MetaMetricsPanel.tsx`, `MetaOverviewPanel.tsx`, `TimeRangeFilter.tsx`

- **UI/UX Improvements**
  - Updated `TimeRangeFilter` component to properly display appropriate default time ranges
  - Fixed time range state synchronization issues across multiple metric panels
  - Removed persistent Meta session expiration warning from dashboard
  - Enhanced error handling to reduce unnecessary authentication alerts

- **Time Range Implementation**
  - Added `defaultPreset` prop to `TimeRangeFilter` component for explicit time range control
  - Enhanced date range detection logic to better identify existing ranges
  - Added consistent time range presets for all metric types
  - Improved UI state management for more intuitive user experience

### UI Improvements
- Added global scroll-to-top button for better navigation
- Implemented pagination in Product Details chart (20 items per page)
- Enhanced component reusability
- Improved responsive design

### Performance Optimizations
- Implemented lazy loading for components
- Added proper error boundaries
- Optimized image loading
- Enhanced caching strategies

### Feature Additions
- Added product pagination support
- Improved error handling and user feedback
- Enhanced data visualization components
- Added loading states and animations

### Development Workflow
- Updated environment configuration
- Enhanced build process
- Improved development server setup
- Added comprehensive documentation

## Project Structure

The project is organized as follows:

frontend/                    # React + TypeScript frontend
├── certs/                   # SSL certificates for HTTPS
│   ├── cert.crt             # SSL certificate
│   ├── cert.key             # Private key
│   ├── ca.crt               # Certificate Authority certificate
│   └── ca.key               # Certificate Authority private key
├── dist/                    # Built assets for production
├── public/                  # Static assets
│   ├── dunit.mp4            # Demo video
│   ├── faviconV2.png        # Site favicon
│   ├── index.html           # Static HTML file
│   ├── locales/             # Internationalization
│   │   ├── en/translation.json
│   │   └── es/translation.json
│   ├── logo.png             # Application logo
│   ├── logonube.png         # La Nube logo
│   ├── meta.png             # Meta integration image
│   └── vite.svg             # Vite logo
├── readme/                  # Documentation
│   └── UIREADME.md          # This documentation file
├── src/                     # Source code
│   ├── assets/              # Static assets
│   │   ├── faviconV2.png
│   │   ├── logo.png
│   │   ├── react.svg
│   │   └── vite.svg
│   ├── components/          # React components
│   │   ├── auth/            # Authentication components
│   │   │   ├── AdminProtectedRoute.tsx
│   │   │   ├── AuthLayout.tsx
│   │   │   ├── ForgotPassword.tsx
│   │   │   ├── Login.tsx
│   │   │   ├── ProtectedRoute.tsx
│   │   │   └── Register.tsx
│   │   ├── charts/          # Chart components
│   │   │   ├── CorrelationHeatmap.tsx
│   │   │   ├── DemographicsChart.tsx
│   │   │   └── ForecastChart.tsx
│   │   ├── common/          # Shared components
│   │   │   ├── CSRFErrorBoundary.tsx
│   │   │   ├── ChartContainer.tsx
│   │   │   ├── CookieBanner.tsx
│   │   │   ├── CookieSettings.tsx
│   │   │   ├── ErrorDisplay.tsx
│   │   │   ├── FontCheck.tsx
│   │   │   ├── GlobalAppSpinner.tsx
│   │   │   ├── LanguageSelector.tsx
│   │   │   ├── LoadingIndicator.tsx
│   │   │   ├── LoadingSpinner.tsx
│   │   │   ├── Navigation.tsx
│   │   │   ├── NavigationWrapper.tsx
│   │   │   ├── PageContainer.tsx
│   │   │   ├── SilentResponsiveContainer.tsx
│   │   │   └── TimeRangeFilter.tsx
│   │   ├── dashboard/       # Dashboard components
│   │   │   ├── CompetitorAnalysis.tsx
│   │   │   ├── CompetitorAnalysisText.tsx
│   │   │   ├── CouponPaymentShippingCard.tsx
│   │   │   ├── CustomerAggregatedMetricsCard.tsx
│   │   │   ├── CustomerAnalysisCard.tsx
│   │   │   ├── CustomerDemographicsChart.tsx
│   │   │   ├── CustomerDetailView.tsx
│   │   │   ├── CustomerInsightsText.tsx
│   │   │   ├── CustomerList.tsx
│   │   │   ├── DetailedMetricsCard.tsx
│   │   │   ├── KeyDatesCard.tsx
│   │   │   ├── KeywordsCard.tsx
│   │   │   ├── MarketPositionChart.tsx
│   │   │   ├── MarketPositionText.tsx
│   │   │   ├── MetricCard.tsx
│   │   │   ├── Navbar.tsx
│   │   │   ├── PieChartLabels.tsx
│   │   │   ├── ProductAnalysis.tsx
│   │   │   ├── ProductAnalysisText.tsx
│   │   │   ├── ProductCategorySummaryCard.tsx
│   │   │   ├── ProductPerformanceChart.tsx
│   │   │   ├── ProductSalesHistoryChart.tsx
│   │   │   ├── ProductVariationDetailCard.tsx
│   │   │   ├── ProfilePanel.tsx
│   │   │   ├── RecommendationCard.tsx
│   │   │   ├── RosenProductChart.tsx
│   │   │   ├── RosenProductDetails.tsx
│   │   │   ├── SeoRecommendationsDisplay.tsx
│   │   │   ├── ShippingAnalysis.tsx
│   │   │   ├── ShippingMethodsChart.tsx
│   │   │   ├── ShippingMethodsText.tsx
│   │   │   ├── SocialMediaCard.tsx
│   │   │   ├── StaticMetricGrid.tsx
│   │   │   └── SummaryCard.tsx
│   │   ├── Feedback/        # Feedback components
│   │   │   └── FeedbackModal.tsx
│   │   ├── legal/           # Legal components
│   │   │   └── CookiePolicyPage.tsx
│   │   ├── meta/            # Meta integration components
│   │   │   ├── ChatInputWrapper.tsx
│   │   │   ├── InsightCard.tsx
│   │   │   ├── InstagramPermissionAlert.tsx
│   │   │   ├── MetaAIInsightsPanel.tsx
│   │   │   ├── MetaAdMetricsPanel.tsx
│   │   │   ├── MetaAdvancedAnalyticsPanel.tsx
│   │   │   ├── MetaAudienceDemographics.tsx
│   │   │   ├── MetaAuthRequired.tsx
│   │   │   ├── MetaBusinessConnect.tsx
│   │   │   ├── MetaCampaignsComparison.tsx
│   │   │   ├── MetaDashboard.tsx
│   │   │   ├── MetaLoginButton.tsx
│   │   │   ├── MetaMetricsPanel.tsx
│   │   │   ├── MetaOverviewPanel.tsx
│   │   │   ├── MetaPermissionAlert.tsx
│   │   │   ├── MetaPermissionsAlert.tsx
│   │   │   ├── MetaPermissionsPanel.tsx
│   │   │   ├── MetaPostsAnalytics.tsx
│   │   │   └── RestrictedWidget.tsx
│   │   ├── settings/        # Settings components
│   │   │   ├── CookieManagementCard.tsx
│   │   │   ├── EditProfile.tsx
│   │   │   ├── Settings.tsx
│   │   │   └── TwoFactorAuth.tsx
│   │   ├── Dashboard.tsx    # Main dashboard
│   │   ├── ErrorBoundary.tsx
│   │   └── ScrollToTop.tsx
│   ├── config/              # Configuration files
│   │   ├── api.ts
│   │   ├── axios.ts
│   │   ├── cookies.ts
│   │   └── environment.ts
│   ├── contexts/            # React contexts
│   │   ├── AuthContext.tsx
│   │   ├── FeedbackContext.tsx
│   │   ├── MetaPermissionsContext.tsx
│   │   ├── ThemeContext.tsx
│   │   └── feedback.context.ts
│   ├── hooks/               # Custom hooks
│   │   ├── useCookieConsent.ts
│   │   ├── useCsrfErrorRecovery.ts
│   │   ├── useMetaAuth.ts
│   │   └── useSecureForm.ts
│   ├── pages/               # Page components
│   │   ├── AdminPortal.tsx
│   │   ├── BackendLogin.tsx
│   │   ├── CookiePolicy.tsx
│   │   ├── SocialMedia.tsx
│   │   └── StoreView.tsx
│   ├── services/            # API services
│   │   ├── apiService.ts
│   │   ├── auth.ts
│   │   ├── authChecker.ts
│   │   ├── authService.ts
│   │   ├── config.ts
│   │   ├── cookieService.ts
│   │   ├── csrfService.ts
│   │   ├── dataService.ts
│   │   ├── formService.ts
│   │   ├── httpClient.ts
│   │   ├── init.ts
│   │   ├── metaApiProxy.ts
│   │   ├── metaStoreService.ts
│   │   ├── metaTimeRanges.ts
│   │   ├── permissions.ts
│   │   ├── storeService.ts
│   │   └── types.ts
│   ├── types/               # Type definitions
│   │   ├── common.ts
│   │   └── cookies.ts
│   ├── util/                # Utilities
│   │   ├── metaTypeGuards.ts
│   │   └── permissionHelpers.ts
│   ├── utils/               # More utilities
│   │   ├── authUtils.ts
│   │   ├── consoleFilter.ts
│   │   ├── errorUtils.ts
│   │   ├── localizationUtils.ts
│   │   ├── locationUtils.ts
│   │   ├── logerrors.ts
│   │   ├── logger.ts
│   │   ├── metricCards.ts
│   │   └── validation.ts
│   ├── App.css
│   ├── App.tsx
│   ├── Router.tsx
│   ├── i18n.ts
│   ├── index.css
│   ├── main.tsx
│   ├── theme.ts
│   ├── types.d.ts
│   └── vite-env.d.ts
├── build-test.ts            # Build testing
├── deployment-script.sh     # Deployment automation
├── distribution-config.json # CloudFront configuration
├── eslint.config.js         # ESLint configuration
├── fix-build.sh             # Build fix script
├── index.html               # Root HTML template
├── mime-types-config.json   # MIME type configuration
├── package-lock.json        # NPM lock file
├── package.json             # NPM dependencies
├── postcss.config.js        # PostCSS configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.app.json        # TypeScript app config
├── tsconfig.json            # TypeScript configuration
├── tsconfig.node.json       # TypeScript node config
└── vite.config.ts           # Vite configuration

## Meta Integration

### Overview

The D-Unit platform includes comprehensive Meta (Facebook and Instagram) integration for social media analytics, allowing users to connect their Meta business accounts and analyze performance metrics, audience demographics, and campaign performance.

### Meta SDK Integration

The frontend uses the Meta JavaScript SDK (v22.0) for authentication and API access. The SDK is initialized in `main.tsx` with the appropriate app ID and API version.

### Authentication Flow

1. User clicks "CONNECT TO META" in the Meta dashboard
2. Meta authentication dialog appears (handled by SDK)
3. User authorizes the app with necessary permissions
4. On successful authorization, the app fetches business accounts and pages
5. User can then access analytics for the selected page

### API Endpoints

#### Meta API Integration

The frontend interacts with several specialized endpoints for handling Instagram metrics differently from Facebook metrics:

##### Platform Detection

**Platform Detection Function**
This function provides intelligent identification of Instagram accounts by:
- Checking the `platform` property for the value 'instagram'
- Looking for 'INSTAGRAM_ACCOUNT' in the `category` property
- Searching for the word 'Instagram' in the category string
- Providing a consistent interface for platform-specific code paths
- Enabling proper routing of API requests to appropriate endpoints

##### Mock Data Strategy

When real data is unavailable (user not connected or API failure), the system provides mock data with a clear strategy:

1. Try to use cached data if available
2. Fallback to platform-specific mock data generation
3. Clearly label mock data in the UI
4. Provide realistic but non-misleading mock metrics

##### Instagram Specific API Handling

**getPageInsights Function Implementation**
This critical function handles platform-specific API requests by:
- First fetching basic page details to identify the platform type
- Implementing platform-specific code paths for Instagram vs Facebook
- Using different endpoint structures for Instagram business accounts
- Handling different metrics naming conventions and data structures
- Providing consistent response formatting for both platforms
- Implementing proper error handling for platform-specific issues

##### Response Format Consistency

Despite the different data sources, the API ensures a consistent JSON response format:

**Example Response Schema**
This standardized response format provides:
- A hierarchical metrics object containing individual metrics
- Each metric includes time-series data with proper timestamps
- Title and description fields for UI display
- Clear indication if data is real or mock with the is_mock flag
- Platform identification for client-side handling
- Page ID for data correlation and tracking

The `is_mock` field ensures transparency in data provenance.

### Troubleshooting Meta Integration

If you encounter issues with the Meta integration, check the following:

1. **API Rate Limits**:
   - The Meta Graph API has rate limits that may affect data retrieval
   - If you see "API Error" messages, wait a few minutes before trying again
   - In development, use mock data to avoid hitting rate limits

2. **Permission Issues**:
   - Ensure the Meta app has all required permissions: email, pages_show_list, business_management, pages_read_engagement, ads_read
   - If permission errors occur, disconnect and reconnect to Meta with the full permission set

3. **Data Sync Problems**:
   - If campaign data doesn't match sales data in the Campaigns Comparison feature, check the time ranges
   - Verify that store ID is correctly set in local storage for proper data association
   - Refresh the page data using the "Refresh Pages" button if metrics seem outdated

4. **Correlation Calculation Issues**:
   - If correlation metrics show unexpected values, ensure sufficient data points exist in the selected time range
   - Very short time ranges (less than 7 days) may not provide statistically significant correlations
   - If correlation values are always 0, check that both conversion data and sales data are properly loaded

5. **Chart Display Issues**:
   - If charts don't render correctly, try adjusting the browser window size or refreshing the page
   - For dual-axis charts in Campaigns Comparison, ensure both data series have appropriate scale differences
   - If chart labels overlap, try selecting a shorter time range or use a larger display

6. **Mock Data vs. Real Data**:
   - The platform clearly indicates when using mock data vs. real data
   - To switch to real data, use the "CONNECT TO META" button when viewing mock data
   - If connected to Meta but still seeing mock data, check console logs for API errors

For persistent issues with the Meta integration, check the browser console for detailed error messages and ensure your Meta app is properly configured in the Meta Developers Console.

## Mock Data Implementation - TODO: Replace with Real Data

> **⚠️ MIGRATION NEEDED**: The current implementation uses mock data for development and testing. This section documents what needs to be replaced with real API implementations.

## Todo: Replace Mock Data with Real Implementations

### Current Mock Data Components Requiring Real Data Implementation:

#### 1. Meta Integration Mock Data
**Files that need real data implementation:**
- `src/services/metaStoreService.ts` - Mock Meta data service
- `src/services/dataService.ts` - Mock data fallbacks
- `src/components/meta/MetaDashboard.tsx` - Mock data mode handling
- `src/components/meta/MetaOverviewPanel.tsx` - Mock data integration
- `src/components/meta/MetaMetricsPanel.tsx` - Mock metrics display
- `src/components/meta/MetaPostsAnalytics.tsx` - Mock posts data
- `src/components/meta/MetaAudienceDemographics.tsx` - Mock audience data
- `src/components/meta/MetaAIInsightsPanel.tsx` - Mock insights generation
- `src/components/meta/MetaAdvancedAnalyticsPanel.tsx` - Mock correlation/forecast
- `src/components/meta/MetaCampaignsComparison.tsx` - Mock campaign data
- `src/components/meta/MetaPermissionsPanel.tsx` - Mock permissions

**Actions needed:**
1. **Replace Meta API Mock Calls** - Implement direct calls to Meta Graph API
2. **Remove Mock Data Endpoints** - Eliminate `/api/meta/mock-data` backend endpoints
3. **Update Type Definitions** - Remove `is_mock` flags from interfaces in `src/services/types.ts`
4. **Implement Real Meta Authentication** - Replace mock auth with actual Meta OAuth flow
5. **Add Real-time Data Sync** - Implement periodic data refresh from Meta APIs
6. **Remove Mock Data UI Indicators** - Eliminate "viewing mock data" banners and buttons

#### 2. Dashboard Analytics Mock Data
**Components using mock data patterns:**
- Dashboard components may have fallback mock data for development
- Chart components might use sample data when real data is unavailable

**Actions needed:**
1. **Audit Dashboard Data Sources** - Verify all dashboard components use real backend APIs
2. **Remove Development Fallbacks** - Eliminate any mock data fallbacks in production
3. **Implement Error Handling** - Replace mock data fallbacks with proper error states

#### 3. Store Data Mock Implementations
**Potential mock data usage:**
- Store metrics that might use sample data
- Product analytics with mock performance data
- Customer demographics with sample data

**Actions needed:**
1. **Verify Real Data Integration** - Ensure all store data comes from MySQL/MongoDB
2. **Remove Sample Data** - Eliminate any hardcoded sample metrics
3. **Implement Data Validation** - Add proper validation for real data sources

### Implementation Priority:

#### High Priority (Production Blockers):
1. **Meta API Integration** - Replace all mock Meta data with real API calls
2. **Authentication Flow** - Implement real Meta OAuth instead of mock authentication
3. **Data Persistence** - Ensure real data is cached and persisted properly

#### Medium Priority (User Experience):
1. **Real-time Updates** - Implement live data refresh mechanisms
2. **Error Handling** - Replace mock data fallbacks with proper error states
3. **Performance Optimization** - Optimize real API calls for production load

#### Low Priority (Polish):
1. **UI Cleanup** - Remove all mock data indicators and banners
2. **Development Tools** - Keep mock data available only in development mode
3. **Documentation** - Update docs to reflect real data implementation

### Technical Requirements for Real Data Implementation:

1. **Meta Graph API Integration**:
   - Implement proper App ID and secret management
   - Add rate limiting and quota management
   - Implement token refresh and session management
   - Add proper error handling for API failures

2. **Backend API Development**:
   - Create real endpoints to replace mock data endpoints
   - Implement data caching strategies
   - Add data validation and sanitization
   - Implement proper database schemas

3. **Frontend Updates**:
   - Remove all mock data service calls
   - Update components to handle real API responses
   - Implement proper loading and error states
   - Add real-time data refresh capabilities

4. **Testing Strategy**:
   - Implement integration tests with real APIs
   - Add proper error scenario testing
   - Create performance tests for real data loads
   - Maintain development-only mock data for testing

The current mock data implementation provides a seamless user experience for development and testing, but must be replaced with real data sources for production deployment.

### Frontend Changes

#### MetaStoreService
- **Path**: `src/services/metaStoreService.ts`
- **Description**: Enhanced to support mock data retrieval
- **New Methods**:
  - `getMockData()`: Fetches mock Meta data for a store
  - `getMockPosts(pageId)`: Fetches mock posts for a specific page
  - `getMockMetrics(pageId)`: Fetches mock metrics for a specific page
  - `getMockAudience(pageId)`: Fetches mock audience demographics for a specific page

#### MetaDashboard Component
- **Path**: `src/components/meta/MetaDashboard.tsx`
- **Description**: Updated to support mock data mode
- **Changes**:
  - Added `usingMockData` state to track when using mock data
  - Modified `checkMetaLoginStatus()` to fetch mock data when not connected to Meta
  - Added an informational banner to indicate when viewing mock data
  - Added a "Connect to Meta" button when viewing mock data
  - Passed `useMockData` prop to child components

#### Analytics Components
The following components were updated to support mock data:

1. **MetaMetricsPanel**
   - **Path**: `src/components/meta/MetaAdMetricsPanel.tsx`
   - **Changes**: Added support for fetching and displaying mock metrics data

2. **MetaPostsAnalytics**
   - **Path**: `src/components/meta/MetaPostsAnalytics.tsx`
   - **Changes**: Added support for fetching and displaying mock posts data

3. **MetaAudienceDemographics**
   - **Path**: `src/components/meta/MetaAudienceDemographics.tsx`
   - **Changes**: Added support for fetching and displaying mock audience data

4. **MetaAIInsightsPanel**
   - **Path**: `src/components/meta/MetaAIInsightsPanel.tsx`
   - **Changes**: Added support for generating and displaying mock insights

5. **MetaAdvancedAnalyticsPanel**
   - **Path**: `src/components/meta/MetaAdvancedAnalyticsPanel.tsx`
   - **Changes**: Added support for generating and displaying mock correlation and forecast data

### Type Definitions

- **Path**: `src/services/meta/types.ts`
- **Description**: Updated type definitions to support mock data
- **Changes**:
  - Added optional `is_mock` property to insight interfaces
  - Enhanced type definitions to support mock data structures
  - Added proper type safety for all mock data implementations

### User Experience

The mock data implementation provides a seamless user experience:

1. **Automatic Detection**:
   - When a user is not logged in with Meta, the system automatically switches to mock data mode
   - No user action required to view mock data

2. **Visual Indication**:
   - A banner at the top of the dashboard indicates when viewing mock data
   - Clear distinction between real and mock data

3. **Easy Connection**:
   - "Connect to Meta" button allows users to easily switch to real data when desired
   - Seamless transition between mock and real data

4. **Consistent UI**:
   - The UI remains consistent whether using mock or real data
   - All features and functionality are available in mock data mode

### Recent Updates and Improvements

#### Code Optimization (June 2024)

#### MetaAdMetricsPanel Component Optimization
- **Path**: `src/components/meta/MetaAdMetricsPanel.tsx`
- **Changes**:
  - Removed unused imports (`Divider` and `MetaDataService`) to improve code cleanliness
  - Optimized the PieChart component by replacing unused variables with underscore notation
  - Maintained all existing functionality while reducing unnecessary code

#### Meta Components Mock Data Implementation
- All Meta components now properly support mock data through the `useMockData` prop:
  - `MetaAIInsightsPanel` - Passes the `useMockData` prop to the `MetaAdvancedAnalyticsPanel`
  - `MetaAdvancedAnalyticsPanel` - Uses mock data for correlation and forecast insights
  - `MetaAdMetricsPanel` - Fetches mock ad metrics when `useMockData` is true
  - `MetaAudienceDemographics` - Displays mock audience data when not connected to Meta
  - `MetaPostsAnalytics` - Shows mock posts and engagement metrics

#### Mock Data Flow Improvements
- **Seamless Transition**: The application now smoothly transitions between mock and real data
- **Consistent UI**: The user interface remains consistent whether using mock or real data
- **Data Integrity**: Mock data is clearly marked with `is_mock: true` for transparency
- **User Experience**: Added informational banner to indicate when viewing mock data

### Meta Integration Enhancements

#### Authentication Error Handling Improvements (March 2024)
- **Enhanced Session Management**
  - Implemented intelligent detection of `AUTH_FAILED` error types
  - Added user-friendly authentication error messages with clear instructions
  - Implemented "Log in again" button for immediate re-authentication when sessions expire
  - Developed automatic data refetching after successful re-authentication
  - Components updated: `MetaDashboard.tsx`, `MetaMetricsPanel.tsx`, `dataService.ts`

#### Error Type System Refinements
- **Improved Error Handling**
  - Added clear distinction between authentication errors and other API issues
  - Implemented consistent error state management across components
  - Eliminated mock data fallbacks when authentication errors occur
  - Created specific error displays based on error type
  - Added dedicated `authError` state in components for better UI control

#### Code Cleanup
- **Removed Unused Imports**
  - Eliminated unused `MetaStoreService` import from `MetaDashboard.tsx`
  - Removed `MetaErrorType` and `MetricInsight` imports from `metaStoreService.ts` where not used
  - Fixed TypeScript linter errors while maintaining all functionality

#### Advanced Analytics Integration
- Improved the integration between `MetaAIInsightsPanel` and `MetaAdvancedAnalyticsPanel`
- Enhanced data flow between components with proper prop passing
- Ensured consistent error handling and loading states across components

#### Meta Dashboard Navigation
- Added consistent navigation experience across all Meta components
- Improved tab management for better user experience
- Enhanced error handling for Meta API connection issues

### Code Quality Improvements

#### TypeScript Optimization
- Removed unused imports and variables across Meta components
- Improved type safety with proper interface implementations
- Enhanced component props documentation

#### TypeScript Error Resolution (July 2024)

##### MetaAdMetricsPanel Component Optimization
- **Path**: `src/components/meta/MetaAdMetricsPanel.tsx`
- **Changes**:
  - Fixed TypeScript errors related to unused imports and variables
  - Removed unused imports (`Divider`, `PieChart`, `Pie`, and `Cell`) to improve code cleanliness
  - Removed unused `getMockAdMetrics` function, `useMockDataFallback` state, and `prepareObjectivePerformanceData` function
  - Completely removed the `useMockData` prop that was no longer needed
  - Added proper Campaign interface instead of using 'any' type for better type safety
  - Enhanced type safety and maintenance without changing functionality
  - Eliminated all TypeScript linter warnings

##### Multiple Component Error Resolution
- **Fixed TypeScript errors in various components:**
  - **RosenProductDetails.tsx**: Resolved d3.js text function type error by casting to string
  - **PieChartLabels.tsx**: Added JSX pragma for React import and fixed unused parameter warning
  - **PageContainer.tsx**: Implemented conditional styling using previously unused `isChatPage` variable
  - **ShippingMethodsText.tsx**: Implemented method-specific analysis display for unused props
  - **InstagramPermissionAlert.tsx**: Enhanced alert with message context and permission-specific messages
  - **ErrorDisplay.tsx**: Removed unused Box and Collapse imports

##### Improved TypeScript Patterns
- Implemented standardized approaches for handling common TypeScript issues:
  - Using underscore prefix for unused parameters: `(_d, i) => ...`
  - Adding JSX pragma comments for React imports: `// @ts-expect-error 'React' is actually needed for JSX`
  - Explicit type casting for library functions: `String(d)` for d3.js
  - Creating explicit interfaces instead of using 'any' types
  - ESLint disable comments for unavoidable linter issues

#### Performance Enhancements
- Optimized rendering of chart components
- Improved data fetching with proper loading states
- Enhanced error handling for better user experience

#### UI Improvements (July 2024)
- **Path**: `src/components/meta/MetaDashboard.tsx`
- **Changes**:
  - Fixed duplicated "CONNECT TO META" button issue by removing the standalone button
  - Standardized button text to all caps "CONNECT TO META" for consistency with design
  - Maintained proper alignment of the button with the "Select Page" dropdown
  - Improved overall UI cleanliness and user experience
  - Eliminated redundant UI elements while preserving all functionality
  - Implemented responsive design improvements for mobile users

### Documentation Updates

#### Meta Integration Documentation
- Updated documentation to reflect the latest changes to Meta components
- Added detailed information about mock data implementation
- Included troubleshooting guidance for Meta integration issues

#### Component Documentation
- Enhanced documentation for all Meta components
- Added usage examples and prop descriptions
- Included information about data flow and component relationships

### Troubleshooting Meta Integration

If you encounter issues with the Meta integration, check the following:

1. **HTTPS Configuration**
   - Ensure you're accessing the application via HTTPS (`https://localhost:5173`)
   - Verify that the certificates are properly loaded in `vite.config.ts`
   - Make sure you've trusted the CA certificate in your browser/system

2. **Meta API Version**
   - Verify that the Meta API version is set to v22.0 in your environment files
   - Check that `src/services/meta/init.ts` is using the correct version

3. **Browser Issues**
   - Clear browser cache and cookies
   - Try using an incognito/private window
   - Check browser console for specific error messages

4. **Certificate Errors**
   - If you see certificate errors, ensure you've trusted the CA certificate
   - For Windows: Import `ca.crt` into "Trusted Root Certification Authorities"
   - For macOS: Add `ca.crt` to Keychain Access and trust it
   - For Linux: Add `ca.crt` to system certificates

### Securing the Application with HTTPS

#### Local Development Security

The current setup uses self-signed certificates for local development. To make browsers fully trust these certificates:

1. **Trust the CA Certificate**:
   - **Windows**: Import `ca.crt` into "Trusted Root Certification Authorities" store
   - **macOS**: Add to Keychain Access and set to "Always Trust"
   - **Linux**: Add to system certificates (varies by distribution)

2. **Verify Secure Connection**:
   - Access the application via `https://localhost:5173`
   - Look for the padlock icon in your browser's address bar
   - No security warnings should appear

#### Production Environment Security

For production deployment, use one of these approaches:

1. **AWS CloudFront with ACM (Recommended)**:
   **AWS ACM Certificate Request Command**
   This command secures your domain with a trusted SSL certificate by:
   - Requesting a certificate from Amazon Certificate Manager
   - Using DNS validation for domain ownership verification
   - Supporting wildcard subdomains if needed
   - Providing automatic renewal management
   - Integrating seamlessly with CloudFront distributions

   **CloudFront Distribution Configuration**
   This configuration applies the certificate to your CloudFront distribution by:
   - Referencing the ACM certificate's ARN
   - Enabling SNI-based SSL support for broad compatibility
   - Enforcing modern TLS protocol versions for security
   - Setting proper SSL/TLS security policies
   - Handling certificate rotation automatically

2. **Let's Encrypt with Certbot**:
   **Certbot Installation and Configuration**
   This approach provides free, trusted SSL certificates by:
   - Installing the Certbot tool for certificate management
   - Obtaining certificates using the standalone verification method
   - Automatically handling domain validation
   - Creating certificate files in the standard location
   - Configuring your web server (Nginx example) to use the certificates
   - Setting up automatic renewal for continued security

3. **Redirect HTTP to HTTPS**:
   **Express.js HTTPS Redirect Configuration**
   This Express.js middleware ensures all traffic uses HTTPS by:
   - Checking the protocol used in the request
   - Redirecting HTTP traffic to the HTTPS version automatically
   - Preserving URL paths and query parameters
   - Supporting proxy environments with X-Forwarded-Proto header
   - Maintaining a clean implementation with proper middleware structure

#### Security Best Practices

1. **Content Security Policy (CSP)**:
   **CSP Meta Tag Configuration**
   This security header protects against XSS attacks by:
   - Defining allowed sources for various resource types
   - Restricting script execution to trusted domains only
   - Allowing styles only from the application itself
   - Permitting Meta SDK scripts from their official domain
   - Implementing a reasonably strict security policy

2. **HTTP Strict Transport Security (HSTS)**:
   **HSTS Header Configuration**
   This security header ensures continued use of HTTPS by:
   - Setting a long expiration time (1 year) for the policy
   - Including all subdomains in the security policy
   - Supporting preloading in browser HSTS lists
   - Preventing protocol downgrade attacks
   - Enhancing overall transport security

3. **Secure Cookies**:
   **Secure Cookie Implementation**
   This code ensures cookies are properly secured by:
   - Setting the Secure flag to restrict cookies to HTTPS only
   - Using SameSite=Strict to prevent CSRF attacks
   - Implementing proper cookie security attributes
   - Preventing cookie theft through man-in-the-middle attacks
   - Following modern web security best practices

4. **Regular Certificate Renewal**:
   **Certificate Renewal Cron Job**
   This cron job ensures certificates never expire by:
   - Running the certbot renewal command monthly
   - Running silently to avoid unnecessary notifications
   - Automating the renewal process completely
   - Preventing service interruptions due to expired certificates
   - Following Let's Encrypt's recommended renewal practices

### Advanced Analytics Features

The Meta integration now includes advanced analytics capabilities that provide deeper insights and predictive analytics:

#### Advanced Correlation Analysis

The advanced correlation analysis feature helps users understand the complex relationships between their social media activities and business outcomes:

1. **Multi-dimensional Analysis**:
   - Analyzes correlations across multiple dimensions (engagement, likes, comments, shares)
   - Visualizes correlation patterns using interactive heatmaps
   - Identifies which social media metrics have the strongest impact on business outcomes

2. **Time-lagged Correlation**:
   - Measures delayed effects of social media activities on sales
   - Allows users to configure time lag settings (0-30 days)
   - Identifies optimal timing between posts and expected business impact

3. **Segmentation**:
   - Supports analysis by product category, customer segment, or post type
   - Provides more targeted insights for specific business areas
   - Allows users to select segments through an intuitive interface

4. **User Interface**:
   - Interactive form for configuring analysis parameters
   - Visual representation of correlation patterns
   - Actionable insights with specific recommendations
   - Loading states and error handling

#### Time-series Forecasting

The forecasting feature helps users anticipate future trends and make proactive decisions:

1. **Metric Selection**:
   - Supports forecasting for engagement, followers, and sales metrics
   - User-friendly dropdown for metric selection
   - Configurable forecast period (7-90 days)

2. **Visualization**:
   - Line chart showing historical and forecasted values
   - Confidence interval visualization
   - Clear distinction between historical and predicted data
   - Interactive tooltips with detailed information

3. **Insight Generation**:
   - Automatically identifies trends in forecasted data
   - Provides trend strength assessment (significant, moderate)
   - Generates actionable recommendations based on trends
   - Displays forecast accuracy metrics

4. **User Interface**:
   - Simple form for forecast configuration
   - Visual representation of forecast results
   - Actionable insights with specific recommendations
   - Loading states and error handling

### Integration with Meta AI Insights Panel

The advanced analytics features are integrated into the Meta AI Insights Panel:

1. **Tabbed Interface**:
   - Added "Advanced Analytics" tab to the existing insights panel
   - Seamless navigation between different insight types
   - Consistent user experience across all analytics features

2. **Shared Components**:
   - Uses the same InsightCard component for displaying insights
   - Maintains consistent styling and interaction patterns
   - Leverages common loading and error handling mechanisms

3. **Data Flow**:
   - Fetches data from the same API endpoints as other insights
   - Shares authentication and page context
   - Maintains consistent data refresh mechanisms

### Future Enhancements

Planned enhancements for the Meta integration include:
- Instagram-specific analytics
- Ad campaign performance analysis
- Content recommendation engine based on engagement patterns
- Competitive analysis features
- Automated reporting and scheduling

Planned enhancements for the advanced analytics features include:
- Attribution modeling for connecting social media activities to conversions
- Anomaly detection for identifying unusual patterns in data
- Scenario modeling for "what-if" analysis
- Competitive benchmarking for performance comparison
- Automated insight generation with machine learning
- Scheduled forecasts and alerts for significant changes

#### Meta API Version Update
- Updated Meta API version from v18.0 to v22.0 across all files
- Ensured consistent API version usage in:
  - `src/services/meta/init.ts`
  - Environment configuration files
  - Documentation references

#### HTTPS Configuration for Meta Integration
Meta's Graph API requires HTTPS for authentication and API calls. We've implemented the following improvements:

1. **Vite HTTPS Configuration**
   - Updated `vite.config.ts` to enable HTTPS for local development
   - Added proper SSL certificate configuration

2. **Self-Signed Certificate Setup**
   - Created a certificate directory: `ui/frontend/certs/`
   - Generated self-signed certificates using mkcert:
     - `ca.crt`: Certificate Authority certificate
     - `ca.key`: CA private key
     - `cert.crt`: Server certificate
     - `cert.key`: Server private key

3. **Enhanced Error Handling**
   - Improved `MetaLoginButton.tsx` to detect and handle HTTPS requirements
   - Added protocol checking to prevent errors on HTTP connections
   - Enhanced error messages for better troubleshooting

4. **Meta SDK Initialization Improvements**
   - Updated `initMetaSDK` function to include status checking
   - Added protocol validation to warn about HTTPS requirements
   - Improved error handling for SDK initialization failures

#### Navigation Consistency
- Added the common Navigation component to the Meta Dashboard
- Ensured consistent navigation experience across all main sections:
  - Chat
  - Dashboard
  - Meta
  - Settings

## Enhanced Dashboard

The Dashboard component has been significantly enhanced with specialized visualization components and improved type safety. The new Dashboard provides a comprehensive view of store analytics with interactive visualizations and detailed insights.

### Key Features

- **Enhanced Visualizations**: Specialized chart components for different types of data
- **Interactive Sections**: Collapsible sections for detailed information
- **Comprehensive Analytics**: Complete view of store performance metrics
- **Type-Safe Implementation**: Proper TypeScript interfaces for all data structures
- **Responsive Design**: Optimized for all screen sizes with Material-UI components

### Dashboard Sections

1. **Key Metrics**
   - Total Revenue
   - Total Orders
   - Average Order Value
   - Total Customers
   - Trend indicators and mini charts

2. **Recommendations**
   - AI-generated actionable insights
   - Impact level indicators
   - Action items with checkmarks

3. **Customer Insights**
   - Detailed customer analysis
   - Customer behavior patterns
   - Purchasing trends

4. **Customer Demographics**
   - Interactive visualization of customer distribution by country
   - Detailed city-level breakdown
   - Percentage indicators and progress bars

5. **Competitor Analysis**
   - Market position analysis
   - Direct competitor information
   - Strategic recommendations

6. **Shipping Methods**
   - Analysis of shipping options
   - Usage statistics
   - Performance metrics

7. **Product Analysis**
   - Top-performing products
   - Sales and revenue metrics
   - Detailed product breakdown

8. **Market Position**
   - Price trends compared to competitors
   - Historical price data visualization
   - Market positioning analysis

9. **Social Media Presence**
   - Social media account status
   - Platform integration information
   - Connection status indicators

10. **Feedback**
    - User feedback submission
    - Previous feedback history
    - Success notifications

### Visualization Components

The Dashboard uses specialized visualization components for different types of data:

1. **MetricCard**
   - Displays key metrics with trend indicators
   - Includes mini sparkline charts
   - Color-coded based on trend direction

2. **RecommendationCard**
   - Displays structured recommendations
   - Shows impact level and action items
   - Clean, modern card design

3. **ProductPerformanceChart**
   - Bar chart visualization of product performance
   - Dual-axis for revenue and units sold
   - Interactive tooltips with detailed information

4. **DemographicsChart**
   - Pie or bar chart visualization of demographic data
   - Custom color palette
   - Interactive tooltips with percentage information

5. **MarketPositionChart**
   - Line chart visualization of price trends
   - Comparison between store prices and competitor averages
   - Interactive tooltips with currency formatting

### Type Safety Improvements

The Dashboard component now uses proper TypeScript interfaces for all data structures, ensuring type safety throughout the application. Key improvements include:

1. Extended the `Analysis` interface to include the `price_history` property
2. Created specialized interfaces for each visualization component
3. Added proper type annotations for all function parameters
4. Implemented type-safe state management with generics

For detailed implementation information, please refer to [UIREADME2.md](./UIREADME2.md). For component descriptions, please refer to [UIREADME3.md](./UIREADME3.md).

## Recent Updates

### Dashboard Component Fixes (May 2024)

The Dashboard component has been updated with the following improvements:

1. **Fixed Feedback Submission**:
   - Implemented proper error handling for feedback submission
   - Added backend route integration for storing feedback
   - Added visual feedback to users when submission is successful

2. **Code Cleanup**:
   - Removed unused state variables (`expandedSection` and `setExpandedSection`)
   - Fixed TypeScript linting errors
   - Improved code organization and readability

3. **UI Improvements**:
   - Enhanced feedback form with better error handling
   - Improved display of previous feedback entries
   - Fixed layout and styling issues in various dashboard sections

The dashboard now provides a complete end-to-end feedback system where users can submit their comments about the platform and view their previous feedback entries. The implementation includes:

- A dedicated "Feedback" tab in the dashboard
- A form with a text area for entering feedback
- A submission button that is disabled until text is entered
- Success/error alerts to inform the user about the submission status
- A list of previous feedback entries displayed below the form

This update completes the feedback loop and enhances the user experience by providing a way for users to communicate with the platform administrators.

## Component Overview

### Dashboard Components

The D-Unit dashboard provides comprehensive analytics and insights through various specialized components:

1. **Core Dashboard**
   - Displays key metrics and analytics
   - Implements tabbed navigation for different data views
   - Integrates various visualization components

2. **Product Analysis**
   - Enhanced vertical bar chart visualization for top products
   - Beautifully formatted product analysis text
   - **Data Source**: Product performance data (units sold, revenue) displayed in this section is fetched from the `/api/store/{store_id}/products` backend endpoint. This endpoint serves paginated data cached in the `product_details_cache` MongoDB collection. This cache is populated periodically by the `ui/backend/scripts/update_product_details.py` script, which processes historical sales data from the MySQL database to provide efficient access to performance metrics.
   - Components:
     - `RosenProductChart`: Displays top products with vertical bars
     - `ProductAnalysisText`: Formats and displays product analysis with:
       - Total products counter with icon
       - Formatted product list with bold names
       - Market insight section
       - Clean, modern design with subtle blue background

3. **Customer Demographics**
   - Interactive visualization of customer distribution
   - Detailed country and city breakdown
   - Collapsible sections for detailed information

## Code Maintenance and Linter Error Handling

The D-Unit frontend codebase follows strict TypeScript linting rules to ensure code quality. Recent maintenance efforts have focused on resolving unused variables and imports to improve code cleanliness and performance.

### Login Component Optimization

The Login component (`ui/frontend/src/components/auth/Login.tsx`) underwent several optimizations to resolve TypeScript linter warnings:

## Project Structure

### `src/utils/`

## Logging Strategy

The frontend employs a custom logging utility found at `src/utils/logger.ts` to standardize log messages and control output based on the environment.

- **Replacement of `console.*`:** Standard browser `console.log`, `console.error`, etc., calls have been replaced with methods from the `logger.ts` utility (`logger.info`, `logger.debug`, `logger.error`, `logger.warn`, `logger.logSensitive`).
- **Environment Control:** The logger checks `process.env.NODE_ENV`. Debug-level messages (`logger.debug`) are only outputted during development (`NODE_ENV === 'development'`) to avoid cluttering the console in production.
- **Sensitive Data:** The `logger.logSensitive` method is used for potentially sensitive information that might be needed during debugging but should ideally be suppressed in production or logged with caution.


1. **Removal of Unused Imports**:
   - Removed unused `GoogleLogin` import since the application uses `useGoogleLogin` hook instead
   - Removed unused `FacebookIcon` import as Meta login is handled by the `MetaLoginButton` component
   - Removed `MetaAuthService` import that was no longer needed after refactoring

2. **Handling Unused Variables**:
   - Fixed `userInfo` variable that was declared but never read by awaiting the response without assigning it
   - Removed `metaLoading` state variable that was only used in code that was no longer needed
   - Removed `metaLogin` from the `useAuth` hook destructuring since it's now handled by the `MetaLoginButton` component

3. **Cleanup of Obsolete Code**:
   - Removed the unused `handleMetaLogin` function as its functionality is now encapsulated in the `MetaLoginButton` component

These changes maintain all existing functionality while making the code cleaner and more maintainable:
- Email/password login continues to work as before
- Google sign-in functionality is preserved
- 2FA verification remains intact
- Meta/Facebook login works through the specialized `MetaLoginButton` component

The same approach to code maintenance is being applied throughout the codebase to ensure consistent code quality and minimal TypeScript warnings.

## Meta Integration Components

### Data Service (`dataService.ts`)
The data service handles all API interactions with Meta's Graph API for both Facebook and Instagram platforms.

#### Key Features
- Platform-specific metric mappings for Facebook and Instagram
- Smart ID detection for Instagram business accounts
- Chunked data fetching for improved performance
- Comprehensive error handling with platform-specific messages

#### Instagram Business Integration
- Uses Instagram API v22+ compatible metrics
- Handles multiple metric responses for single requests
- Implements graceful fallbacks for business account fetching
- Provides clear error messages for permission and authentication issues

#### Facebook Page Integration
- Direct page metrics fetching
- Standard Facebook Insights API metrics support
- Efficient data caching and batch requests

### Error Handling
- Platform-specific error codes and messages
- Clear user feedback for permission issues
- Graceful fallbacks for missing data
- Automatic retry mechanisms for transient failures

### Instagram Data Services

The frontend includes specialized services for handling Instagram-specific API requirements, particularly for audience demographics:

1. **Instagram Audience Demographics Service**
   
   **src/services/meta/dataService.ts - getAudienceDemographics method**
   
   This comprehensive method handles Instagram-specific audience demographics with:
   - Platform-specific implementation path for Instagram vs Facebook
   - Multi-step demographic data retrieval strategy designed for Instagram's API limitations
   - Account follower count retrieval as a baseline for demographics calculation
   - Individual breakdown fetching for different demographic dimensions (age-gender, country, city)
   - Privacy threshold handling for Instagram's restriction on small demographic segments
   - Data normalization to create consistent output format regardless of platform
   - Proper error handling and fallbacks for unavailable demographic data
   - Comprehensive type safety with TypeScript interfaces
   - Clear documentation of the process flow and limitations

2. **Production-Safe Logging**
   
   **src/services/meta/dataService.ts - debugLog method**
   
   This utility method implements environment-aware logging that:
   - Only outputs debugging information in development environments
   - Completely suppresses logs in production to protect sensitive data
   - Supports both message-only and data-with-message logging formats
   - Handles different log types safely with proper type checking
   - Provides consistent debugging capabilities across the service
   - Follows best practices for production code security
   - Offers clean implementation with minimal overhead

3. **Instagram API Parameter Handling**
   
   **src/services/meta/dataService.ts - addMetricTypeParams method**
   
   This specialized helper method:
   - Handles Instagram-specific API parameter requirements
   - Enhances requests for specific metrics that require metric_type parameter
   - Supports multiple engagement metrics (likes, comments, saved, shares)
   - Handles demographic-specific parameters with proper categorization
   - Modifies the params object in-place for efficient parameter management
   - Ensures API compatibility with Instagram Graph API requirements
   - Centralizes parameter logic for consistent implementation across the service

### Store-Specific Components

Components that handle store-specific Meta data:

1. **StoreMetaOverview.tsx**
   
   **Store-specific overview component**
   
   This specialized component provides store-level Meta metrics visualization with:
   - Store-specific data fetching through the useMockMetaData hook
   - Proper loading state handling with LoadingSpinner component
   - Platform-specific metrics separation for Facebook and Instagram
   - Clean component composition with dedicated metrics components per platform
   - Type-safe implementation with proper TypeScript interfaces
   - Elegant container styling for consistent layout
   - Clear separation of concerns between data fetching and visualization
   - Support for both mock and real Meta data through the same interface

2. **StorePlatformSelector.tsx**
   
   **Platform selection for specific stores**
   
   This user interface component enables platform selection with:
   - Store-specific data retrieval to determine available platforms
   - Dynamic rendering of platform options based on available data
   - Integration with Material-UI Select component for consistent styling
   - Platform selection through onChange event handler
   - Proper value and state management for controlled component behavior
   - Store ID-specific labeling of platform options for clear identification
   - Support for multiple platform options within the same store
   - Clean prop interface with callback for selection changes
   - Proper type safety with TypeScript interfaces

### Recent Updates (March 2024)

#### Instagram Business Integration
1. **Enhanced Metrics Display**
   - Support for Instagram-specific ad metrics
   - Improved error handling and data validation
   - Real-time metrics updates

2. **MetaAdMetricsPanel Updates**
   - Support for Instagram business accounts
   - Enhanced metrics display:
     - Total Spend
     - Impressions
     - Clicks
     - Conversions
     - CTR
     - CPC
     - Cost per Conversion
     - ROI

### Overview Components

1. **MetaAdMetricsPanel.tsx**
   
   **Updated metrics panel with Instagram support**
   
   This enhanced component delivers platform-specific metrics visualization with:
   - Support for both Facebook and Instagram platforms through a platform prop
   - Custom data hook integration via useMetaAdMetrics for platform-aware data fetching
   - Comprehensive metrics definition with proper formatting configurations
   - Clear error display with user-friendly error messages
   - Loading state indication for better user experience
   - Responsive grid layout using Material-UI's Grid system
   - Dynamic metric rendering with consistent formatting through MetricCard components
   - Proper metric formatting for currency, numbers, and percentages
   - Complete set of advertising metrics including spend, impressions, clicks, and conversions
   - Performance metrics such as CTR, CPC, cost per conversion, and ROI
   - Clean component interface with minimal required props

2. **InstagramBusinessMetrics.tsx**
   
   **New component for Instagram business metrics**
   
   This specialized Instagram metrics component provides:
   - Instagram-specific data fetching through the useInstagramMetrics hook
   - Comprehensive metrics display via the MetaAdMetricsPanel with platform="instagram"
   - Time series visualization with the TimeSeriesChart component
   - Multiple metrics tracking including spend, impressions, and clicks
   - Detailed metrics breakdown with the MetricsTable component
   - Loading state handling for consistent user experience
   - Clean component structure with logical organization
   - Proper data flow from hook to child components
   - Comprehensive Instagram business metrics in a single container
   - Modular component architecture for better maintainability

### Data Hooks

1. **useMetaAdMetrics.ts**
   
   This specialized hook provides platform-aware data fetching for Meta ad metrics:

   **State Management**
   - Complete React state management for data, loading, and error states
   - Proper TypeScript types for all state variables
   - Clean state initialization and updates
   - Type-safe error handling

   **Endpoint Selection**
   - Dynamic endpoint selection based on platform parameter
   - Proper API path construction for different platforms
   - Support for both Facebook and Instagram metrics
   - Consistent API interface regardless of platform

   **Data Fetching**
   - Axios-based API requests with proper error handling
   - Complete try/catch implementation
   - Detailed error logging
   - Loading state management

   **Dependency Handling**
   - Proper dependency array in useEffect
   - Re-fetching when page ID or platform changes
   - Cleanup and cancellation of in-flight requests
   - Prevention of memory leaks

   This hook ensures consistent data fetching regardless of platform while providing a clean, type-safe interface for components to consume.

## Error Handling and Resilience

### Handling Database Timeout Errors

D-Unit implements robust error handling strategies to ensure a smooth user experience even when backend database operations experience timeouts or performance issues.

#### Retry Mechanism with Exponential Backoff

**fetchWithRetry Function Implementation**
This sophisticated retry mechanism enhances API resilience by:
- Implementing up to 3 retry attempts for failed requests
- Using exponential backoff starting at 1 second
- Adding random jitter to prevent thundering herd problems
- Implementing cache-busting on retry attempts
- Only retrying network errors and server 5xx errors
- Providing detailed logging of retry attempts
- Deduplicating identical requests during retries
- Maintaining a clean promise-based interface

This mechanism is particularly important for:
- Store products API calls with large inventories
- Long-running analytics queries
- Batch operations on multiple items

### Pagination Implementation

**Dashboard Pagination Implementation**
For stores with large product inventories, this pagination system:
- Configures flexible page size (default: 100 items)
- Implements first/previous/next/last navigation controls
- Displays current page indicator for user orientation
- Calculates total pages based on item count
- Integrates with backend's optimized queries
- Loads data efficiently without excessive memory usage
- Maintains consistent UI state during page transitions

The pagination system ensures efficient loading of large datasets while maintaining responsive performance.

### API Call Optimization

**Meta Dashboard API Optimization**
The system now includes comprehensive prevention of redundant API calls, particularly beneficial when users are not logged in:
- Tracking initialization state to prevent duplicate initialization
- Using state variables to track ongoing API operations
- Implementing request cancellation for unmounted components
- Adding rate limit detection with automatic cooldown
- Including timeout handling with clear user feedback
- Delaying initialization to ensure SDK loading is complete
- Using proper cleanup in useEffect hooks
- Providing detailed logging for debugging

These improvements significantly enhance the Meta integration experience by:

1. Providing better detection of permission changes
2. Improving the login flow when no business accounts are found
3. Protecting against API rate limiting
4. Ensuring proper loading states and UI visibility
5. Optimizing API calls to reduce unnecessary requests
6. Updating the Meta logo for consistent branding

## Meta Integration Structure

### Platform-Specific Implementations

#### Instagram Business Account Integration

**API Endpoints and Metrics**
- Different metric mappings for Instagram API v22+:
  ```typescript
  const instagramMetricMap = {
    'page_impressions': 'reach,profile_views,impressions',
    'page_engagement': 'reach,profile_views,impressions,follower_count',
    'page_fans': 'follower_count',
    'page_views_total': 'profile_views'
  };
  ```

**Minimum Required Permissions**
- `instagram_basic`
- `instagram_insights`
- `instagram_manage_insights`
- `pages_show_list`
- `pages_read_engagement`

**Error Handling**
- Specific error codes and messages for Instagram:
  - Code 190: Invalid access token
  - Code 100: Missing required permissions
  - Custom error messages for better user feedback

#### Facebook Page Integration

**API Endpoints and Metrics**
- Facebook-specific metric mappings:
  ```typescript
  const facebookMetricMap = {
    'page_impressions': 'page_impressions',
    'page_engagement': 'page_post_engagements',
    'page_fans': 'page_fans',
    'page_views_total': 'page_views_total'
  };
  ```

**Required Permissions**
- `pages_show_list`
- `pages_read_engagement`
- `read_insights`

### MongoDB Collections and Document Schemas

The Meta integration uses these MongoDB collections with the following document structures:

#### 1. `meta_pages`
Stores Meta page information (Facebook and Instagram pages)

**Schema:**
- `_id`: ObjectId
- `id`: String (Meta page ID)
- `store_id`: String (Reference to store in D-Unit)
- `name`: String (Page name)
- `access_token`: String (Page access token - **CRITICAL**: This token should be used for all API calls related to this page)
- `category`: String (Page category)
- `platform`: String ("facebook" or "instagram")
- `business_id`: String
- `instagram_business_account`: Object (Optional, structure varies)
  - `id`: String
  - `username`: String
  - `name`: String
- `updated_at`: Date
- `username`: String (Instagram only, optional)
- `profile_picture_url`: String (optional, not always present)
- `followers_count`: Number (optional, not always present)
- `created_at`: Date (when the page was added to D-Unit)
- `is_mock`: Boolean (whether this is mock data)

#### 2. `meta_posts`
Stores posts from Facebook and Instagram pages

**Schema:**
- `_id`: ObjectId
- `id`: String (Meta post ID)
- `page_id`: String (ID of the page this post belongs to)
- `store_id`: String (Reference to store in D-Unit)
- `message`: String (Post text content)
- `created_time`: Date (when the post was published)
- `permalink_url`: String (URL to view the post)
- `type`: String ("photo", "video", "status", "link", "offer")
- `platform`: String ("facebook" or "instagram")
- `attachments`: Array of Objects
  - `media_type`: String
  - `url`: String
- `is_mock`: Boolean (whether this is mock data)

#### 3. `meta_ad_campaigns`
Stores ad campaign information and performance

**Schema:**
- `_id`: ObjectId
- `id`: String (Campaign ID)
- `page_id`: String (ID of the page this campaign belongs to)
- `store_id`: String (Reference to store in D-Unit)
- `name`: String (Campaign name)
- `status`: String (e.g., "ACTIVE", "PAUSED")
- `objective`: String (Campaign objective)
- `start_time`: Date (Campaign start date)
- `end_time`: Date (Campaign end date, optional)
- `budget`: Number (Campaign budget)
- `spend`: Number (Total spend)
- `currency`: String
- `platform`: String ("FACEBOOK", "INSTAGRAM", etc.)
- `results`: Number
- `cost_per_result`: Number
- `updated_at`: Date
- `conversion_rate`: Number
- `correlation_data`: Array of Objects
  - `date`: String
  - `conversion_rate`: Number
  - `sales_value`: Number
- `correlation_metric`: Number
- `is_mock`: Boolean (whether this is mock data)

## Frontend-Backend API Relationships

### Authentication Endpoints (`/api/auth`)

- `POST /api/auth/token`
  - **Called By:** `frontend/src/services/authService.ts` (login function)
  - **Frontend Context:** Used for standard email/password login to obtain JWT.

- `POST /api/auth/google`
  - **Called By:** `frontend/src/services/authService.ts` (googleLogin function)
  - **Frontend Context:** Handles Google OAuth authentication flow.

- `POST /api/auth/meta`
  - **Called By:** `frontend/src/services/authService.ts` (metaLogin function), `frontend/src/services/meta/auth.ts`
  - **Frontend Context:** Handles authentication using Meta (Facebook/Instagram) credentials.

### Store Endpoints (`/api/store`)

- `GET /api/store/{store_id}/analysis`
  - **Called By:** `frontend/src/services/storeService.ts`
  - **Frontend Context:** Fetches store analysis data for dashboard displays. Includes recent activity and Meta summary metrics for the Overview tab.
  - **Response Fields:**
    - `metrics.revenue_30d` — Revenue in the last 30 days
    - `metrics.order_count_30d` — Orders in the last 30 days
    - `metrics.visit_count_30d` — Visits in the last 30 days
    - `metrics.traffic_light` — Store activity status (e.g., green/yellow/red)
    - `meta_summary.total_followers` — Total Meta followers (aggregated)
    - `meta_summary.ad_spend_30d` — Meta ad spend in the last 30 days

- `GET /api/store/{store_id}/product-list`
  - **Called By:** `frontend/src/services/storeService.ts` (getStoreProductList function), `frontend/src/components/Dashboard.tsx`
  - **Frontend Context:** Retrieves paginated detailed product list from `product_details_cache` for the Dashboard Products tab.

### Meta Endpoints (`/api/meta`)

- `GET /api/meta/mock-data`
  - **Called By:** `frontend/src/services/metaStoreService.ts` (getMockData function)
  - **Frontend Context:** Retrieves mock Meta data for testing and development.

- `GET /api/meta/{store_id}/pages/{page_id}/posts`
  - **Called By:** `frontend/src/services/metaStoreService.ts` (getPostsByPage function)
  - **Frontend Context:** Retrieves posts for a specific Meta page with pagination.

- `GET /api/meta/pages/{page_id}/ad-metrics`
  - **Called By:** `frontend/src/services/metaStoreService.ts` (getAdMetrics function)
  - **Frontend Context:** Retrieves ad metrics for a specific Meta page, with options for time range and organic metrics.

### Chat Endpoints (`/api/chat`)

- `POST /api/chat/{store_id}`
  - **Called By:** `frontend/src/components/meta/ChatInputWrapper.tsx`
  - **Frontend Context:** Used by the chat interface components to send user messages to the AI assistant.

- `GET /api/chat/history/{store_id}`
  - **Called By:** `frontend/src/App.tsx`
  - **Frontend Context:** Fetches chat history for display and cache invalidation.

## Error Handling and Testing

### Error Types
```typescript
enum MetaErrorType {
  AUTH_FAILED = 'AUTH_FAILED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_METRIC = 'INVALID_METRIC',
  RATE_LIMIT = 'RATE_LIMIT',
  NO_DATA = 'NO_DATA'
}
```

### Error Messages
- Authentication: "Please reconnect your Meta account"
- Permissions: "Missing required permissions"
- Rate Limit: "Too many requests, please try again later"
- Invalid Metric: "Some metrics are currently unavailable"
- Network: "Connection error, please check your internet"

### API Field Guidance

The correct fields for Meta API requests:

`***************/posts?fields=id,created_time,message,permalink_url,from,is_published,status_type&limit=5&since=2024-05-07&until=2025-05-07`

**Deprecated fields (v3.3):**
- `caption`
- `description`
- `link`
- `name`
- `object_id`
- `source`
- `type`

### Time Range Handling

```typescript
interface TimeRange {
  since: string;
  until: string;
  preset?: TimeRangePreset;
}
```

**Presets:** 7d, 28d, 30d, 90d, 180d, 1y, week, month, quarter, lifetime, custom

### MongoDB Fallback

**Implementation:**
1. Primary attempt: Meta API
2. On failure: Check MongoDB cache
3. If cache miss: Retry API
4. On success: Update cache

**Caching Strategy:**
- Cache successful API responses
- Include timestamp for cache invalidation
- Store by page ID and time range
- Automatic cleanup of old cache entries

## Development Guidelines

### Mock Data Updates
- Avoid mock data

### Testing
- Test real data flows
- Verify data consistency
- Check error handling
- Validate metrics calculations

### Maintenance
- Regular updates to avoid mock data patterns
- Monitoring of data quality
- Performance optimization
- Documentation updates

## Future Enhancements

Planned enhancements for the D-Unit platform development:

### Meta Integration
- Instagram-specific analytics enhancements
- Advanced ad campaign performance analysis
- Content recommendation engine based on engagement patterns
- Competitive analysis features
- Automated reporting and scheduling

### Advanced Analytics
- Attribution modeling for connecting social media activities to conversions
- Anomaly detection for identifying unusual patterns in data
- Scenario modeling for "what-if" analysis
- Competitive benchmarking for performance comparison
- Automated insight generation with machine learning
- Scheduled forecasts and alerts for significant changes

### General Fixes and Type Safety Enhancements (2024-07-30)
- Se abordaron problemas de linting relacionados con dependencias de React Hook innecesarias en `MetaAIInsightsPanel.tsx` y `MetaMetricsPanel.tsx`.
- Se eliminaron variables no utilizadas (`formatTimeRange`) en `MetaAdMetricsPanel.tsx`.
- Se mejoró la seguridad de tipo al reemplazar el tipo `any` con tipos más específicos en `MetaOverviewPanel.tsx`, `MetaPostsAnalytics.tsx` y `UnifiedMetaApiService.ts`, junto con mejoras en el manejo de errores.

<!-- APPENDED BY AI: Dashboard Responsiveness Update -->
## Dashboard Responsiveness & Bottom Navigation (July 2025)

The main store dashboard now adapts to phones (<640 px) and tablets (640-1024 px):

* Cards stack vertically on mobile and flow into 2-3-column grids on larger screens via Tailwind breakpoints (`sm:`, `md:`).
* Metric and information cards received `rounded-lg`, `shadow-md`, and dark background classes for modern aesthetics.
* All draggable behaviour was disabled for touch friendliness.
* A new bottom navigation bar appears on mobile / tablet (`< md`).  The right-side icon bar remains for desktop.
* Remember to include extra bottom padding (`pb-24`) on pages using `NavigationWrapper` so content is not obscured.

No APIs, data-fetches, or routes were changed.
