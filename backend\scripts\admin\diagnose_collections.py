#!/usr/bin/env python3
"""
Diagnostic script to check what collections and data exist in the database.
This will help debug why update_competitor_analysis.py isn't finding stores.
"""

import os
import sys
from dotenv import load_dotenv

try:
    from pymongo import MongoClient
except ImportError:
    print("PyMongo not installed. Please install it with: pip install pymongo")
    sys.exit(1)

def main():
    # Load environment variables
    load_dotenv()
    mongodb_uri = os.getenv('MONGODB_CONNECTION')
    
    if not mongodb_uri:
        print("MONGODB_CONNECTION not found in environment variables")
        return
    
    print(f"Connecting to MongoDB...")
    try:
        client = MongoClient(mongodb_uri)
        analysis_db = client['D-Unit-AnalysisGPT']
        
        print("\n=== DATABASE COLLECTIONS ===")
        collections = analysis_db.list_collection_names()
        print(f"Found {len(collections)} collections:")
        for collection in collections:
            count = analysis_db[collection].count_documents({})
            print(f"  {collection}: {count} documents")
        
        print("\n=== GLOBAL_ANALYSIS COLLECTION DETAILS ===")
        global_analysis = analysis_db['global_analysis']
        total_docs = global_analysis.count_documents({})
        print(f"Total documents: {total_docs}")
        
        if total_docs > 0:
            # Get sample documents
            print("\nSample document structure:")
            sample_docs = list(global_analysis.find({}).limit(3))
            for i, doc in enumerate(sample_docs, 1):
                print(f"Document {i}:")
                print(f"  _id: {doc.get('_id')}")
                print(f"  Top-level fields: {list(doc.keys())}")
                if 'analysis' in doc:
                    analysis_field = doc['analysis']
                    if isinstance(analysis_field, dict):
                        print(f"  Analysis sub-fields: {list(analysis_field.keys())}")
                        if 'competitor_analysis' in analysis_field:
                            comp_analysis = analysis_field['competitor_analysis']
                            if comp_analysis:
                                print(f"  Has competitor analysis: Yes (length: {len(str(comp_analysis))})")
                            else:
                                print(f"  Has competitor analysis: No/Empty")
                    else:
                        print(f"  Analysis field type: {type(analysis_field)}")
                print()
                
            # Check stores without competitor analysis
            stores_without_analysis = global_analysis.count_documents({
                "$or": [
                    {"analysis": {"$exists": False}},
                    {"analysis.competitor_analysis": {"$exists": False}},
                    {"analysis.competitor_analysis": ""},
                    {"analysis.competitor_analysis": None}
                ]
            })
            print(f"Stores without competitor analysis: {stores_without_analysis}")
            
            # Check stores with analysis field
            stores_with_analysis = global_analysis.count_documents({"analysis": {"$exists": True}})
            print(f"Stores with analysis field: {stores_with_analysis}")
            
        else:
            print("No documents found in global_analysis collection")
            print("\nChecking other collections for store data...")
            
            # Check other collections that might have store data
            for collection_name in ['active_stores_cache', 'store_customers_cache', 'product_details_cache']:
                try:
                    collection = analysis_db[collection_name]
                    count = collection.count_documents({})
                    print(f"\n{collection_name}: {count} documents")
                    if count > 0:
                        # Get sample IDs
                        sample_docs = list(collection.find({}, {"_id": 1}).limit(5))
                        sample_ids = [doc["_id"] for doc in sample_docs]
                        print(f"  Sample store IDs: {sample_ids}")
                        
                        # Check if we can create entries in global_analysis
                        print(f"  These store IDs could be used to initialize global_analysis collection")
                except Exception as e:
                    print(f"  Error checking {collection_name}: {str(e)}")
        
        print("\n=== RECOMMENDATIONS ===")
        if total_docs == 0:
            print("The global_analysis collection is empty. You may need to:")
            print("1. Run initialization scripts to populate it with store data")
            print("2. Check if stores exist in other collections and migrate them")
            print("3. Verify that your application is using the correct database name")
        elif stores_without_analysis == total_docs:
            print("All stores lack competitor analysis. The script should process all of them.")
            print("If it's not working, there might be an environment or dependency issue.")
        else:
            print(f"Found {stores_without_analysis} stores that need competitor analysis.")
            print("The script should process these stores.")
            
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        print("Please check your MONGODB_CONNECTION environment variable")

if __name__ == "__main__":
    main()