import React from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent,
  useTheme
} from '@mui/material';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  <PERSON>Axis, 
  CartesianGrid, 
  Tooltip, 
  Legend
} from 'recharts';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';

interface ProductData {
  name: string;
  revenue: number;
  units: number;
  price?: number;
}

interface ProductPerformanceChartProps {
  products: ProductData[];
  title?: string;
  height?: number;
}

/**
 * A component for visualizing product performance data
 */
export const ProductPerformanceChart: React.FC<ProductPerformanceChartProps> = ({
  products,
  title,
  height = 300
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  // Format product names to prevent long labels
  const formattedProducts = products.map(product => ({
    ...product,
    name: product.name.length > 15 ? `${product.name.substring(0, 15)}...` : product.name
  }));

  return (
    <Card elevation={1} sx={{ mt: 2 }}>
      <CardContent>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        {!title && (
          <Typography variant="h6" gutterBottom>
            {t('productPerformance.cardTitle', 'Product Performance')}
          </Typography>
        )}
        
        <Box sx={{ height: height, width: '100%' }}>
          <ChartContainer width="100%" height="100%">
            <BarChart
              data={formattedProducts}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" orientation="left" stroke="#88CEEB" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip 
                formatter={(value, name) => {
                  if (name === t('dashboard.products.legendRevenue', 'Ingresos')) 
                    return [`${Number(value).toFixed(2)} UYU`, name];
                  return [value, name];
                }}
                contentStyle={theme.palette.mode === 'dark' ? { background: '#fff', color: '#111', borderRadius: 8 } : {}}
                itemStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
                labelStyle={theme.palette.mode === 'dark' ? { color: '#111' } : {}}
              />
              <Legend />
              <Bar 
                yAxisId="left" 
                dataKey="revenue" 
                name={t('productDetails.revenue', 'Revenue')} 
                fill="#88CEEB" 
              />
              <Bar 
                yAxisId="right" 
                dataKey="units" 
                name={t('productDetails.units', 'Units Sold')} 
                fill="#82ca9d" 
              />
            </BarChart>
          </ChartContainer>
        </Box>
      </CardContent>
    </Card>
  );
}; 