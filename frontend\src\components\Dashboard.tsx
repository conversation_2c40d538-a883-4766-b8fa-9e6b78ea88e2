import React, { useEffect, useState, useCallback, useRef } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Grid, 
  CircularProgress, 
  TextField, 
  Button, 
  Alert, 
  IconButton, 
  Divider,
  Tabs,
  Tab,
  useTheme,
  List,
  ListItem,
  ListItemText,
  Card
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import NavigationWrapper from './common/NavigationWrapper';
import PageContainer from './common/PageContainer';
import { storeService, StoreAnalysis, Analysis, ProductDetail, Customer, FeedbackEntry, SeoRecommendation, CategorySummary, ProductSalesDetail } from '../services/storeService';
import { Country } from '../utils/locationUtils';
import { TimeRange } from '../services/types';
import { MetricCard } from './dashboard/MetricCard';
import { RosenProductDetails } from './dashboard/RosenProductDetails';
import ProductAnalysisText from './dashboard/ProductAnalysisText';
import { TimeRangeFilter } from './common/TimeRangeFilter';
import { User } from '../contexts/AuthContext';
import SeoRecommendationsDisplay from './dashboard/SeoRecommendationsDisplay';
import { useTranslation } from 'react-i18next';
import { logger } from '../utils/logger';
import ThumbUpAltOutlinedIcon from '@mui/icons-material/ThumbUpAltOutlined';
import ThumbDownAltOutlinedIcon from '@mui/icons-material/ThumbDownAltOutlined';
import ShippingAnalysis from './dashboard/ShippingAnalysis';
import StaticMetricGrid, { DashboardItemConfig, MetricCardConfig } from './dashboard/StaticMetricGrid';
import LoadingSpinner from './common/LoadingSpinner';
import { ErrorDisplay } from './common/ErrorDisplay';
import ProductCategorySummaryCard from './dashboard/ProductCategorySummaryCard';
import ProductVariationDetailCard from './dashboard/ProductVariationDetailCard';
import ProductSalesHistoryChart from './dashboard/ProductSalesHistoryChart';
import CompetitorAnalysis from './dashboard/CompetitorAnalysis';
import { ProductPerformanceChart } from './dashboard/ProductPerformanceChart';
import StorefrontIcon from '@mui/icons-material/Storefront';
import BusinessCenterIcon from '@mui/icons-material/BusinessCenter';
import PublicIcon from '@mui/icons-material/Public';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import CustomerAnalysisCard from './dashboard/CustomerAnalysisCard';
import CustomerDemographicsChart from './dashboard/CustomerDemographicsChart';
import CustomerAggregatedMetricsCard from './dashboard/CustomerAggregatedMetricsCard';
import CustomerList from './dashboard/CustomerList';
import CustomerDetailView from './dashboard/CustomerDetailView';
import CouponPaymentShippingCard from './dashboard/CouponPaymentShippingCard';
import { getLocalizedDbText } from '../utils/localizationUtils';
import { FirstTimeCookieNotice } from './common/CookieBanner';
import cookieService from '../services/cookieService';

// Define interface for price history data
interface PriceHistoryItem {
  date: string;
  our_price: number;
  competitor_avg: number;
}

// Define basic structures for demographics data
interface DemographicsAgeData {
  range: string;
  percentage: number;
}
interface DemographicsGenderData {
  type: string; 
  percentage: number;
}
interface DemographicsData {
  age?: DemographicsAgeData[];
  gender?: DemographicsGenderData[];
  countries?: Country[];
}

// Extend the Analysis interface to include price_history and demographics
export interface ExtendedAnalysis extends Analysis {
  price_history?: PriceHistoryItem[];
  demographics?: DemographicsData;
}

// Extend the StoreAnalysis interface to use ExtendedAnalysis
interface ExtendedStoreAnalysis extends Omit<StoreAnalysis, 'analysis'> {
  analysis: ExtendedAnalysis;
  store?: {
    name?: string;
    shipping_methods?: Array<{
      name: string;
      times_used: number;
    }>;
    business_type?: string;
    id_store?: string;
    country?: {
      code?: string;
      name?: string;
    };
  };
  metrics?: {
    total_revenue: number;
    order_count: number;
    avg_order_value: number;
    customer_count: number;
    product_count?: number;
    total_products?: number;
    relationships?: {
      product_performance?: {
        sales?: Record<string, number>;
        revenue?: Record<string, number>;
      };
    };
    revenue_30d?: number;
    order_count_30d?: number;
    visit_count_30d?: number;
    traffic_light?: string;
    total_revenue_usd?: number;
    total_visits?: number;
    active_customers?: number;
    customers_with_abandoned_carts?: number;
    store_ratings_count?: number;
    store_average_rating?: number;
    total_gross_revenue?: number; // Added for gross revenue card
    total_net_revenue?: number; // Added for net revenue card
  };
  customers?: import('../services/storeService').CustomerMetricsData;
}

// Tab Panel Component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      <Box sx={{ pt: 3 }}>
        {children}
      </Box>
    </div>
  );
};

// Define props interface
interface DashboardProps {
  storeUser?: User;
}

const Dashboard = ({ storeUser }: DashboardProps) => {
  const theme = useTheme();
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  // Use storeUser if provided, otherwise use the authenticated user
  const { user: authUser } = useAuth() as { user: { id_store?: string, name?: string } };
  const user = storeUser || authUser;
  const [analysis, setAnalysis] = useState<ExtendedStoreAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [feedbackText, setFeedbackText] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [analysisData, setAnalysisData] = useState<ExtendedStoreAnalysis | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState<TimeRange | null>(null);
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);

  // Add state for feedback list
  const [feedbackList, setFeedbackList] = useState<FeedbackEntry[]>([]);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [feedbackError, setFeedbackError] = useState('');

  // Add state for like/dislike counts
  const [feedbackLikes, setFeedbackLikes] = useState(0);
  const [feedbackDislikes, setFeedbackDislikes] = useState(0);

  // Add state for metrics
  const [dashboardItems, setDashboardItems] = useState<DashboardItemConfig[]>([]);

  // State for detailed product list and selection
  const [detailedProductList, setDetailedProductList] = useState<ProductDetail[]>([]);
  const [selectedProductDetail, setSelectedProductDetail] = useState<ProductDetail | null>(null);
  const [categorySummary, setCategorySummary] = useState<CategorySummary | null>(null);
  const [storeRatingsCount, setStoreRatingsCount] = useState<number | null>(null);
  const [storeAverageRating, setStoreAverageRating] = useState<number | null>(null);
  const [storeSalesByDate, setStoreSalesByDate] = useState<ProductSalesDetail[]>([]);
  const [selectedProductSalesHistory, setSelectedProductSalesHistory] = useState<ProductSalesDetail[] | null>(null);

  // --- State for Product List Pagination ---
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [visibleProductCount, setVisibleProductCount] = useState(10);

  // --- State for Chart Pagination ---
  const [chartPage, setChartPage] = useState(0);
  const chartPageSize = 10;

  // --- State for Customer Details ---
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // --- useRef for feedback input focus management ---
  const feedbackInputRef = useRef<HTMLInputElement | null>(null);
  useEffect(() => {
    if (showSuccessMessage && feedbackInputRef.current) {
      feedbackInputRef.current.focus();
    }
  }, [showSuccessMessage]);

  // SEO Specific State
  const [seoRecommendations, setSeoRecommendations] = useState<SeoRecommendation[]>([]);
  const [seoLoading, setSeoLoading] = useState(false);
  const [seoError, setSeoError] = useState<string | null>(null);
  const [seoHasFetched, setSeoHasFetched] = useState(false);
  
  // Add ref to track previous language for SEO
  const previousLanguageRef = useRef<string>(currentLang);

  // Add state for first-time cookie notice
  const [showFirstTimeCookieNotice, setShowFirstTimeCookieNotice] = useState(false);

  // Create fetchFeedbackOnly function
  const fetchFeedbackOnly = useCallback(async () => {
    if (!user?.id_store) return;
    setFeedbackLoading(true);
    setFeedbackError('');
    logger.debug('[Dashboard] Fetching feedback data...');
    try {
      const data = await storeService.getStoreFeedback(user.id_store);
      setFeedbackList(data.feedback || []);
      logger.debug('[Dashboard] Feedback data fetched successfully:', data.feedback);
    } catch (_err) {
       // Only call t here if needed
       setFeedbackError((t && typeof t === 'function') ? t('dashboard.errorFetchFeedback') : 'Failed to fetch feedback.');
       setFeedbackList([]);
    } finally {
       setFeedbackLoading(false);
    }
  }, [user?.id_store, t]);

  const fetchSeoDataInBackground = useCallback(async (lang?: string) => {
    const language = lang || currentLang;
    if (!user?.id_store || seoHasFetched) {
      logger.debug('[Dashboard] SEO fetch skipped: No user or data already fetched/being fetched.', { userId: user?.id_store, seoHasFetched });
      return;
    }
    logger.debug('[Dashboard] Starting background SEO data fetch...', { userId: user?.id_store });
    setSeoLoading(true);
    setSeoError(null);
    try {
      const recommendations = await storeService.getSeoRecommendations(user.id_store, language);
      setSeoRecommendations(recommendations.recommendations);
      setSeoHasFetched(true);
      logger.info('[Dashboard] SEO recommendations fetched successfully.', { count: recommendations.recommendations.length });
    } catch (_err) {
      setSeoError('Failed to load SEO recommendations. Please try again later.');
      setSeoRecommendations([]);
    } finally {
      setSeoLoading(false);
      logger.debug('[Dashboard] Background SEO data fetch finished.');
    }
  }, [user?.id_store, seoHasFetched, currentLang]);

  const fetchData = useCallback(async () => {
    if (!user?.id_store) {
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      setError('');
      logger.debug('[Dashboard] Fetching main analysis and products data...');
      // Time range filtering will be handled in the frontend using period-specific fields
      const analysisResponse = await storeService.getStoreAnalysis(user.id_store, timeRange ? { since: timeRange.start_date, until: timeRange.end_date } : undefined);
      setAnalysis(analysisResponse);
      setAnalysisData(analysisResponse);

      // Fetch product list separately to get sales_by_date and other product-specific data
      const productListData = await storeService.getStoreProductList(user.id_store, {
        page: 1, // Default to first page for overall product list
        page_size: 100, // Fetch a reasonable number of products
        since: timeRange?.start_date,
        until: timeRange?.end_date
      });

      setDetailedProductList(productListData.products || []);
      setCategorySummary(productListData.category_summary || null);
      setStoreRatingsCount(productListData.store_aggregations?.store_ratings_count || null);
      setStoreAverageRating(productListData.store_aggregations?.store_average_rating || null);
      setStoreSalesByDate(productListData.sales_by_date || []);
      setSelectedProductDetail(null); 
      fetchFeedbackOnly();
    } catch (err) {
      logger.error('Error fetching dashboard data:', err);
      setError((t && typeof t === 'function') ? t('dashboard.errorFetchData') : 'Failed to fetch dashboard data.');
    } finally {
      setLoading(false);
    }
  }, [user?.id_store, timeRange, fetchFeedbackOnly]);

  // Optimize useEffect dependencies for fetchData
  useEffect(() => {
    if (user?.id_store) {
      logger.debug('[Dashboard] useEffect triggered: Fetching initial data for main dashboard.');
      fetchData();
    }
    setSelectedCustomer(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id_store, timeRange]); // Remove fetchData from dependencies

  // useEffect to trigger background SEO fetch after main loading is complete
  useEffect(() => {
    if (!loading && user?.id_store && !seoHasFetched) {
      logger.debug('[Dashboard] Main loading complete, triggering background SEO fetch.');
      fetchSeoDataInBackground(currentLang);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading, user?.id_store, seoHasFetched, currentLang]); // Remove fetchSeoDataInBackground from dependencies

  // useEffect to reset/re-fetch SEO data on language change
  useEffect(() => {
    if (previousLanguageRef.current !== i18n.language) {
      logger.debug(`[Dashboard] Language changed from ${previousLanguageRef.current} to: ${i18n.language}. Resetting seoHasFetched.`);
      setSeoHasFetched(false); // Reset to allow re-fetch
      previousLanguageRef.current = i18n.language; // Update ref
    }
  }, [i18n.language]);

  // Add event listener for first-time cookie notice
  useEffect(() => {
    const handleShowFirstTimeNotice = () => {
      setShowFirstTimeCookieNotice(true);
    };

    window.addEventListener('show-first-time-cookie-notice', handleShowFirstTimeNotice);
    
    return () => {
      window.removeEventListener('show-first-time-cookie-notice', handleShowFirstTimeNotice);
    };
  }, []);

  // Add event listener for showing cookie banner from first-time notice
  useEffect(() => {
    const handleShowCookieBanner = () => {
      // Reset cookie service to show the main banner
      cookieService.resetConsent();
      window.location.reload(); // Simple way to refresh and show the banner
    };

    window.addEventListener('show-cookie-banner', handleShowCookieBanner);
    
    return () => {
      window.removeEventListener('show-cookie-banner', handleShowCookieBanner);
    };
  }, []);

  const handleCloseFirstTimeCookieNotice = () => {
    cookieService.markFirstTimeNoticeShown();
    setShowFirstTimeCookieNotice(false);
  };

  const submitFeedback = useCallback(async () => {
    if (!user?.id_store || !feedbackText.trim()) return;
    logger.debug('[Dashboard] submitFeedback called.');
    try {
      setIsSubmittingFeedback(true);
      // Construct the feedback data object
      const feedbackData = {
        feedback_text: feedbackText,
        likes: feedbackLikes,
        dislikes: feedbackDislikes,
        source: 'dashboard' // Indicate the source
      };
      logger.debug('[Dashboard] Submitting feedback data:', feedbackData); 
      // Call the updated service function
      await storeService.submitFeedback(user.id_store, feedbackData);
      logger.info('[Dashboard] Feedback submitted via API.');
      setShowSuccessMessage(true);
      setFeedbackText(''); // Clear input
      // Reset like/dislike counts
      setFeedbackLikes(0);
      setFeedbackDislikes(0);
      setTimeout(() => setShowSuccessMessage(false), 3000);
      logger.debug('[Dashboard] Calling fetchFeedbackOnly to refresh list...');
      fetchFeedbackOnly(); // Re-fetch feedback list
    } catch (err) {
      logger.error('Error submitting feedback:', err);
      setError(t('dashboard.errorSubmitFeedback') || 'Failed to submit feedback.');
    } finally {
      setIsSubmittingFeedback(false);
    }
  // Update dependencies for submitFeedback
  }, [user?.id_store, feedbackText, feedbackLikes, feedbackDislikes, fetchFeedbackOnly, t]); 

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Helper for tab accessibility
  const a11yProps = (index: number) => {
    return {
      id: `dashboard-tab-${index}`,
      'aria-controls': `dashboard-tabpanel-${index}`,
    };
  };

  // useEffect to initialize/update metrics state when analysisData changes
  useEffect(() => {
    if (analysisData && user?.id_store) {
      const initialItems: DashboardItemConfig[] = [];

      // --- NEW: Dynamically Generate Metric Cards ---
      const metrics = analysisData.metrics;
      const currencySymbol = analysisData.currency?.symbol || '$';

      // Helper function to get metric values with time range support
      const getMetricValue = (baseField: string, fallbackField?: string): number => {
        if (!metrics) return 0;
        
        const metricsObj = metrics as Record<string, unknown>;
        
        // If timeRange is null (lifetime), use lifetime fields
        if (timeRange === null) {
          // Map base fields to their lifetime equivalents
          let lifetimeField = baseField;
          if (baseField === 'revenue') lifetimeField = 'total_revenue';
          if (baseField === 'orders') lifetimeField = 'total_orders';
          if (baseField === 'visits') lifetimeField = 'total_visits';
          if (baseField === 'avg_order_value') lifetimeField = 'average_order_value';
          if (baseField === 'customers') lifetimeField = 'total_customers';
          
          const lifetimeValue = metricsObj[lifetimeField];
          if (typeof lifetimeValue === 'number') return lifetimeValue;
          
          // Fallback to provided fallback field for backward compatibility
          if (fallbackField) {
            const fallbackValue = metricsObj[fallbackField];
            if (typeof fallbackValue === 'number') return fallbackValue;
          }
          return 0;
        }
        
        // Determine time period suffix based on selected time range
        // Note: Backend only has 30d, 90d, and 365d metrics available
        let periodSuffix = '';
        if (timeRange?.preset === '7d' || timeRange?.preset === '30d') {
          periodSuffix = '_30d'; // 7d maps to 30d as closest available
        } else if (timeRange?.preset === '90d') {
          periodSuffix = '_90d';
        } else if (timeRange?.preset === '1y') {
          periodSuffix = '_365d';
        }
        
        // If we have a period suffix, try to get the period-specific field
        if (periodSuffix) {
          let periodField = '';
          if (baseField === 'revenue') periodField = `revenue${periodSuffix}`;
          if (baseField === 'orders') periodField = `order_count${periodSuffix}`;
          if (baseField === 'visits') periodField = `visit_count${periodSuffix}`;
          // Note: avg_order_value and customers don't have period-specific fields
          
          if (periodField) {
            const periodValue = metricsObj[periodField];
            if (typeof periodValue === 'number') return periodValue;
          }
        }
        
        // Fallback to lifetime fields if period-specific not available
        let lifetimeField = baseField;
        if (baseField === 'revenue') lifetimeField = 'total_revenue';
        if (baseField === 'orders') lifetimeField = 'total_orders';
        if (baseField === 'visits') lifetimeField = 'total_visits';
        if (baseField === 'avg_order_value') lifetimeField = 'average_order_value';
        if (baseField === 'customers') lifetimeField = 'total_customers';
        
        const lifetimeValue = metricsObj[lifetimeField];
        if (typeof lifetimeValue === 'number') return lifetimeValue;
        
        // Final fallback to provided fallback field
        if (fallbackField) {
          const fallbackValue = metricsObj[fallbackField];
          if (typeof fallbackValue === 'number') return fallbackValue;
        }
        
        return 0;
      };

      // Unified metrics from active_stores_cache.metrics
      const metricCardConfigs: MetricCardConfig[] = [
        {
          id: 'gross_revenue_card',
          type: 'metric',
          title: t('dashboard.metrics.grossRevenue', 'Gross Revenue'),
          value: metrics?.total_gross_revenue ?? 0,
          extra: { currency: currencySymbol },
        },
        {
          id: 'total_net_revenue_card',
          type: 'metric',
          title: t('dashboard.metrics.totalNetRevenue', 'Total Net Revenue'),
          value: metrics?.total_net_revenue ?? getMetricValue('revenue'),
          extra: { currency: currencySymbol },
        },
        {
          id: 'orders_card',
          type: 'metric',
          title: t('dashboard.metrics.orders', 'Orders'),
          value: getMetricValue('orders', 'order_count'),
        },
        {
          id: 'visits_card',
          type: 'metric',
          title: t('dashboard.metrics.visits', 'Visits'),
          value: getMetricValue('visits'),
        },
        {
          id: 'avg_order_value_card',
          type: 'metric',
          title: t('dashboard.metrics.avgOrderValue', 'Avg. Order Value'),
          value: parseFloat(getMetricValue('avg_order_value').toFixed(2)),
          extra: { currency: currencySymbol },
        },
        {
          id: 'total_revenue_usd_card',
          type: 'metric',
          title: t('dashboard.metrics.totalRevenueUSD', 'Total Revenue (USD)'),
          value: metrics?.total_revenue_usd ?? 0,
          extra: { currency: '$' },
        },
        {
          id: 'total_customers_card',
          type: 'metric',
          title: t('dashboard.metrics.totalCustomers', 'Total Customers'),
          value: getMetricValue('customers', 'customer_count'),
        },
        {
          id: 'active_customers_card',
          type: 'metric',
          title: t('dashboard.metrics.activeCustomers', 'Active Customers'),
          value: metrics?.active_customers ?? 0,
        },
        {
          id: 'abandoned_carts_card',
          type: 'metric',
          title: t('dashboard.metrics.abandonedCarts', 'Abandoned Carts'),
          value: metrics?.customers_with_abandoned_carts ?? 0,
        },
      ];

      // 1. Add Summary Card FIRST
      initialItems.push({
        id: 'summary_card',
        type: 'summary',
        title: t('dashboard.overview.summaryTitle', 'Summary'),
        analysisData: analysisData?.analysis,
      });

      // 2. Add Metric Cards SECOND
      initialItems.push(...metricCardConfigs);
      // --- END NEW: Dynamically Generate Metric Cards ---

      // --- ADD REMAINING STATIC METRIC CARDS ---
      // Total Products
      if (analysisData.metrics?.product_count !== undefined && analysisData.metrics?.product_count !== null) {
        initialItems.push({
          id: 'total_products_card',
          type: 'metric',
          title: t('dashboard.metrics.totalProducts', 'Total Products'),
          value: analysisData.metrics.product_count
        });
      }

      // Meta Summary Cards (Still part of Step 2)
      if (analysisData?.meta_summary?.total_followers !== undefined) {
        initialItems.push({
          id: 'meta_total_followers_card',
          type: 'metric',
          title: t('dashboard.metrics.metaFollowers', 'Meta Followers'),
          value: analysisData.meta_summary.total_followers
        });
      }
      if (analysisData?.meta_summary?.ad_spend_30d !== undefined) {
        initialItems.push({
          id: 'meta_ad_spend_30d_card',
          type: 'metric',
          title: t('dashboard.metrics.metaAdSpend30d', 'Meta Ad Spend (30d)'),
          value: analysisData.meta_summary.ad_spend_30d,
          extra: { currency: currencySymbol }
        });
      }
      // --- END ADD STATIC METRIC CARDS / Meta Summary Cards ---

      // --- 3. Add Keywords Card THIRD ---
      if (analysisData.keywords && typeof analysisData.keywords === 'string' && analysisData.keywords.trim().length > 0) {
        const keywordsArray = analysisData.keywords.split(',').map(k => k.trim()).filter(Boolean);
        initialItems.push({
          id: 'keywords_card',
          type: 'keywords',
          title: t('dashboard.titles.keywords', 'Keywords'),
          keywords: keywordsArray
        });
      }

      // 3. Add Key Dates Card (Now effectively 4th in this sequence)
      if (analysisData.key_dates) {
        initialItems.push({
          id: 'key_dates_card',
          type: 'key_dates',
          title: t('dashboard.titles.keyDates', 'Key Dates'),
          dates: analysisData.key_dates
        });
      }

      // 4. Add Social Media Card FOURTH
      if (analysisData.social_media && Object.keys(analysisData.social_media).length > 0) {
        initialItems.push({
          id: 'social_media_card',
          type: 'social_media',
          title: t('dashboard.titles.socialMedia', 'Social Media Profiles'),
          socialMedia: analysisData.social_media,
          analysisData: analysisData?.analysis,
        });
      }

      // 6. Add DETAILED METRICS CARD SIXTH
      const detailedMetricsText = getLocalizedDbText(analysisData?.analysis, 'metrics', currentLang);
      if (detailedMetricsText) {
        initialItems.push({
          id: 'detailed_metrics_card',
          type: 'detailed_metrics',
          title: t('dashboard.titles.detailedMetrics', 'Detailed Metrics Summary'),
          content: detailedMetricsText
        });
      }

      // 7. Add Recommendation Card LAST
      const recommendationsText = getLocalizedDbText(analysisData?.analysis, 'recommendations', currentLang);
      initialItems.push({
        id: 'ai_recommendations_card',
        type: 'recommendation',
        title: t('dashboard.overview.recommendationsTitle', 'AI Recommendations'),
        content: recommendationsText,
      });
      // --- END ADD Summary and Recommendation Cards ---

      // Set dashboard items directly
      setDashboardItems(initialItems);
    } else {
      setDashboardItems([]);
    }
  }, [analysisData, user?.id_store, t, timeRange, currentLang]); // Added timeRange dependency here
  
  const handleProductSelect = useCallback(async (product: ProductDetail) => {
    setSelectedProductDetail(product);
    // Fetch product-specific sales history
    if (user?.id_store && product.product_id) {
      // setSelectedProductSalesHistoryLoading(true);
      // setSelectedProductSalesHistoryError(null);
      try {
        const history = await storeService.getProductSalesHistory(user.id_store, product.product_id);
        setSelectedProductSalesHistory(history);
        logger.debug(`Fetched sales history for product ${product.name}:`, history);
      } catch (err) {
        logger.error(`Error fetching sales history for product ${product.name}:`, err);
        // setSelectedProductSalesHistoryError(t('dashboard.errorFetchProductSalesHistory'));
        setSelectedProductSalesHistory(null); // Clear sales history on error
      } finally {
        // setSelectedProductSalesHistoryLoading(false);
      }
    } else {
      setSelectedProductSalesHistory(null); // Clear if no product or storeId
    }
  }, [user?.id_store, t]);

  // --- LoadingSpinner used for loading state ---
  if (loading) {
    return (
      <Box sx={{ minHeight: '100vh' }}>
        <NavigationWrapper />
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center', 
          minHeight: 'calc(100vh - 64px)', 
          color: theme.palette.text.primary
        }}>
          <LoadingSpinner />
        </Box>
      </Box>
    );
  }

  // --- ErrorDisplay used for error state ---
  if (error) {
    return (
      <Box sx={{ minHeight: '100vh' }}>
        <NavigationWrapper />
        <Container maxWidth="lg" sx={{ pt: 4, pb: 8 }}>
          <ErrorDisplay error={error} />
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh' }} className="pb-24 sm:pb-0">
      <NavigationWrapper />
      <PageContainer>
        <Box sx={{ my: 4 }}>
          <Typography 
            variant="h4" 
            component="h1" 
            gutterBottom
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(90deg, rgba(0,163,255,1) 0%, rgba(0,212,255,1) 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              letterSpacing: '1px',
              textTransform: 'uppercase',
            }}
          >
            {t('dashboard.title')}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" paragraph>
            {t('dashboard.welcomeMessage', { name: user?.name || t('dashboard.defaultUserName') })}
          </Typography>
          
          {/* Time Range Indicator */}
          {timeRange !== null && (
            <Typography variant="body2" color="primary.main" sx={{ mb: 2, fontStyle: 'italic' }}>
              (Showing data for: {timeRange?.preset || 'custom range'})
            </Typography>
          )}
          
          <Divider sx={{ my: 3 }} />

          {/* Store Info Panel */}
          <Card elevation={3} sx={{ p: 3, mb: 4, borderRadius: 3, background: theme.palette.background.paper }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, justifyContent: { xs: 'flex-start', md: 'center' } }}>
                <StorefrontIcon sx={{ color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="body2" color="text.secondary">{t('dashboard.storeInfo.storeLabel', 'Store')}</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700 }}>{analysis?.store?.name || 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, justifyContent: { xs: 'flex-start', md: 'center' } }}>
                <BusinessCenterIcon sx={{ color: 'secondary.main', mr: 2 }} />
                <Box>
                  <Typography variant="body2" color="text.secondary">{t('dashboard.storeInfo.businessTypeLabel', 'Business Type')}</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700 }}>{analysis?.store?.business_type || 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, justifyContent: { xs: 'flex-start', md: 'center' } }}>
                <PublicIcon sx={{ color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography variant="body2" color="text.secondary">{t('dashboard.storeInfo.countryLabel', 'Country')}</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700 }}>{analysis?.store?.country?.name || 'N/A'}</Typography>
                </Box>
              </Grid>
            </Grid>
          </Card>
          
          {/* Tabs Navigation */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
            <TimeRangeFilter 
              value={timeRange}
              onChange={(newRange) => {
                setTimeRange(newRange);
              }}
              presets={['lifetime', '30d', '90d', '1y']}
            />
          </Box>
        
          <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="dashboard tabs"
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab label={t('dashboard.tabs.overview')} {...a11yProps(0)} />
                <Tab label={t('dashboard.tabs.products')} {...a11yProps(1)} />
                <Tab label={t('dashboard.tabs.customers')} {...a11yProps(2)} />
                <Tab label={t('dashboard.tabs.competition')} {...a11yProps(3)} />
                <Tab label={t('dashboard.tabs.shipping')} {...a11yProps(4)} />
                <Tab label={t('dashboard.tabs.seo')} {...a11yProps(5)} />
                <Tab label={t('dashboard.tabs.feedback', 'Feedback')} {...a11yProps(6)} />
              </Tabs>
            </Box>
          
            {/* Overview Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ mb: 4 }}>
                {dashboardItems.length > 0 ? (
                  <StaticMetricGrid
                    items={dashboardItems}
                  />
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
                    <MetricCard title="Sample Metric" value={42} trend="up" trendPercentage={5} trendLabel="5%" />
                  </Box>
                )}
              </Box>
            </TabPanel>
          
            {/* Products Tab */}
            <TabPanel value={tabValue} index={1}>
              <Grid container spacing={3}>
                {/* MOVED: Product Overview Card - Full Width */}
                <Grid item xs={12}>
                   <ProductAnalysisText
                     totalOnlineProducts={(analysisData?.metrics as any)?.products_online || 0}
                     analysisData={analysisData?.analysis}
                     storeRatingsCount={storeRatingsCount}
                     storeAverageRating={storeAverageRating}
                   />
                 </Grid>

                {/* Product Performance Chart - Full Width */}
                <Grid item xs={12}>
                  <Paper elevation={0} variant="outlined" sx={{ p: 3, mb: 3 }}>
                    <Typography variant="h6" gutterBottom>{t('dashboard.products.performanceTitle', 'Product Performance')}</Typography>
                    {(() => {
                      const fullSortedProducts = [...detailedProductList]
                        .sort((a, b) => b.revenue - a.revenue)
                        .map(p => ({
                          name: p.name,
                          revenue: p.revenue,
                          units: p.sales_units
                        }));
                      
                      const chartStartIndex = chartPage * chartPageSize;
                      const chartEndIndex = chartStartIndex + chartPageSize;
                      const paginatedChartData = fullSortedProducts.slice(chartStartIndex, chartEndIndex);
                      const totalChartPages = Math.ceil(fullSortedProducts.length / chartPageSize);

                      return (
                        <React.Fragment>
                          <ProductPerformanceChart
                            products={paginatedChartData} // Pass paginated data
                          />
                          {totalChartPages > 1 && (
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
                              <IconButton
                                onClick={() => setChartPage(prev => Math.max(0, prev - 1))}
                                disabled={chartPage === 0}
                                size="small"
                              >
                                <ArrowBackIosNewIcon fontSize="inherit" />
                              </IconButton>
                              <Typography variant="caption" sx={{ mx: 1 }}>
                                Page {chartPage + 1} of {totalChartPages}
                              </Typography>
                              <IconButton
                                onClick={() => setChartPage(prev => Math.min(totalChartPages - 1, prev + 1))}
                                disabled={chartPage >= totalChartPages - 1}
                                size="small"
                              >
                                <ArrowForwardIosIcon fontSize="inherit" />
                              </IconButton>
                            </Box>
                          )}
                        </React.Fragment>
                      );
                    })()}
                  </Paper>
                </Grid>

                {/* Product Details Table - Left Side */}
                <Grid item xs={12} md={7}> 
                  <RosenProductDetails 
                    products={detailedProductList.slice(0, visibleProductCount)} // Use state for slicing
                    onProductSelect={handleProductSelect} 
                    selectedProductFromDashboard={selectedProductDetail}
                    productSalesHistoryFromDashboard={selectedProductSalesHistory}
                  />
                  {/* Show More/Less Button - Moved inside */} 
                  {detailedProductList.length > 10 && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Button
                        variant="outlined"
                        onClick={() => {
                          const newShowAll = !showAllProducts;
                          setShowAllProducts(newShowAll);
                          setVisibleProductCount(newShowAll ? detailedProductList.length : 10);
                        }}
                      >
                        {showAllProducts ? t('productDetails.showLess', 'Show Less') : t('productDetails.showMore', 'Show More')}
                      </Button>
                    </Box>
                  )}
                </Grid>
                
                {/* Variations Card - Right Side */}
                <Grid item xs={12} md={5}> 
                  <ProductVariationDetailCard 
                    variations={selectedProductDetail?.variations}
                    currencySymbol={selectedProductDetail?.currency_symbol || analysis?.currency?.symbol}
                  />
                </Grid>

                {/* Sales History Chart - Full Width Below */}
                {selectedProductDetail && (
                  <Grid item xs={12}>
                    <ProductSalesHistoryChart
                      salesDetails={selectedProductSalesHistory || storeSalesByDate}
                      productName={selectedProductDetail.name}
                    />
                  </Grid>
                )}

                {/* Category Summary Card - Full Width Below */}
                <Grid item xs={12}>
                   <ProductCategorySummaryCard categorySummary={categorySummary} />
                </Grid>
              </Grid>
            </TabPanel>
            
            {/* Customers Tab */}
            <TabPanel value={tabValue} index={2}>
              {/* ** MODIFICATION START ** */}
              {/* Top Row: Analysis/Chart and Customer List/Details */}
              <Grid container item xs={12} spacing={3} sx={{ mb: 3 }}> 
                {/* Left Column (Top Row) */}
                <Grid item xs={12} md={5} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  {/* MOVED: CustomerDemographicsChart first */}
                  <CustomerDemographicsChart 
                    countryData={analysisData?.customers?.country_distribution}
                  />
                  {/* MOVED: CustomerAnalysisCard second */}
                  <CustomerAnalysisCard
                    analysisData={analysisData?.analysis}
                  />
                  {/* CustomerAggregatedMetricsCard MOVED below */}
                </Grid>

                {/* Right Column (Top Row) */}
                <Grid item xs={12} md={7} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <CustomerList
                    customers={analysisData?.customers?.customers}
                    onSelectCustomer={setSelectedCustomer}
                  />
                  <CustomerDetailView
                    customer={selectedCustomer}
                  />
                </Grid>
              </Grid> {/* End Top Row */}

              {/* Bottom Row: Aggregated Metrics & New Coupon/Payment/Shipping Card */}
              {/* Wrap bottom cards in a container grid */}
              <Grid container item xs={12} spacing={3}>
                <Grid item xs={12} md={6}> {/* Adjust md sizing as needed */}
                  <CustomerAggregatedMetricsCard
                    metrics={analysisData?.customers}
                  />
                </Grid>
                <Grid item xs={12} md={6}> {/* Adjust md sizing as needed */}
                  <CouponPaymentShippingCard data={analysisData?.customers} />
                </Grid>
              </Grid> {/* End Bottom Row */}
              {/* ** MODIFICATION END ** */}
            </TabPanel>
            
            {/* Competitors Tab - Updated Structure */}
            <TabPanel value={tabValue} index={3}>
              <CompetitorAnalysis analysisData={analysisData?.analysis} />
            </TabPanel>
            
            {/* Shipping Tab */}
            <TabPanel value={tabValue} index={4}>
              <ShippingAnalysis storeId={user?.id_store} storeAnalysisData={analysisData} />
            </TabPanel>
            
            {/* SEO Tab */}
            <TabPanel value={tabValue} index={5}>
              <SeoRecommendationsDisplay 
                recommendations={seoRecommendations}
                isLoading={seoLoading}
                error={seoError}
                hasFetched={seoHasFetched}
              />
            </TabPanel>

            {/* Feedback Tab */}
            <TabPanel value={tabValue} index={6}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Paper elevation={0} variant="outlined" sx={{ p: 3, height: '100%' }}>
                    <Typography variant="h6" gutterBottom>
                      {t('dashboard.feedback.title', 'Your Feedback')}
                    </Typography>
                    {/* Feedback submission form */}
                    <Box component="form" sx={{ mb: 4 }}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        placeholder={t('dashboard.feedback.placeholder', 'Share your thoughts on the dashboard...')}
                        value={feedbackText}
                        onChange={(e) => setFeedbackText(e.target.value)}
                        disabled={isSubmittingFeedback}
                        inputRef={feedbackInputRef}
                        sx={{ mb: 2 }}
                      />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Box>
                          <IconButton 
                            onClick={() => setFeedbackLikes(prev => prev + 1)} 
                            color={feedbackLikes > 0 ? "primary" : "default"}
                            disabled={isSubmittingFeedback}
                          >
                            <ThumbUpAltOutlinedIcon />
                          </IconButton>
                          <IconButton 
                            onClick={() => setFeedbackDislikes(prev => prev + 1)} 
                            color={feedbackDislikes > 0 ? "error" : "default"}
                            disabled={isSubmittingFeedback}
                          >
                            <ThumbDownAltOutlinedIcon />
                          </IconButton>
                        </Box>
                        <Button 
                          variant="contained"
                          disabled={!feedbackText.trim() || isSubmittingFeedback}
                          onClick={submitFeedback}
                          endIcon={isSubmittingFeedback ? <CircularProgress size={16} /> : null}
                        >
                          {isSubmittingFeedback
                            ? t('dashboard.feedback.submitting', 'Submitting...')
                            : t('dashboard.feedback.submit', 'Submit Feedback')}
                        </Button>
                      </Box>
                      {showSuccessMessage && (
                        <Alert severity="success" sx={{ mb: 2 }}>
                          {t('dashboard.feedback.success', 'Thank you for your feedback!')}
                        </Alert>
                      )}
                    </Box>
                    {/* Feedback list */}
                    <Typography variant="h6" gutterBottom>
                      {t('dashboard.feedback.previousFeedback', 'Previous Feedback')}
                    </Typography>
                    {feedbackLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                        <CircularProgress size={28} />
                      </Box>
                    ) : feedbackError ? (
                      <Alert severity="error">{feedbackError}</Alert>
                    ) : feedbackList.length > 0 ? (
                      <List>
                        {feedbackList.map((feedbackItem, index) => (
                          <ListItem key={index} divider={index < feedbackList.length - 1}>
                            <ListItemText 
                              primary={feedbackItem.text} 
                              secondary={`On ${feedbackItem.timestamp ? new Date(feedbackItem.timestamp).toLocaleDateString() : 'N/A'} - Source: ${feedbackItem.source || 'Unknown'} - Likes: ${feedbackItem.likes || 0}, Dislikes: ${feedbackItem.dislikes || 0}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        {t('dashboard.feedback.noFeedback', 'No previous feedback found.')}
                      </Typography>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </TabPanel>
          </Box>
        </Box>
      </PageContainer>
      <FirstTimeCookieNotice 
        open={showFirstTimeCookieNotice}
        onClose={handleCloseFirstTimeCookieNotice}
      />
    </Box>
  );
};

export default Dashboard;

