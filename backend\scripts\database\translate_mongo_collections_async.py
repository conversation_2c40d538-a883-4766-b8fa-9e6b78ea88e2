import asyncio
import logging
import os
import sys
import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any, Union
from dataclasses import dataclass, field
from dotenv import load_dotenv
import motor.motor_asyncio
import pymongo
from pymongo.errors import BulkWriteError
from openai import AsyncOpenAI
import hashlib
try:
    from tqdm.asyncio import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    logging.warning("tqdm not available. Progress bars will be disabled.")

# --- Configuration ---
# Configure logging first so we can see any issues with imports/loading
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s')
logger = logging.getLogger(__name__)

# Ensure the backend root is in the Python path
backend_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, backend_root)

# Try to find and load .env file from various locations
def load_environment():
    # Explicitly check for .env.development first
    development_env_path = Path(backend_root) / '.env.development'
    if development_env_path.exists():
        logger.info(f"Loading environment from prioritized file: {development_env_path.absolute()}")
        load_dotenv(dotenv_path=str(development_env_path.absolute()))
        return True
    else:
        logger.info(f"Prioritized .env.development not found at {development_env_path.absolute()}. Checking standard .env locations...")

    # Try multiple possible locations for the standard .env file
    possible_paths = [
        Path('.env'),                     # Current directory
        Path('../.env'),                  # One level up (scripts -> backend)
        Path('../../.env'),               # Two levels up (scripts -> D-Unit)
        Path(backend_root) / '.env',      # Backend root
        Path(backend_root).parent / '.env'  # Project root
    ]
    
    for env_path in possible_paths:
        if env_path.exists():
            logger.info(f"Loading environment from fallback file: {env_path.absolute()}")
            load_dotenv(dotenv_path=str(env_path.absolute()), override=False) # Don't override if .env.dev loaded
            return True
    
    logger.warning("No .env or .env.development file found in any of the expected locations")
    return False

# Parse command-line arguments
def parse_args():
    parser = argparse.ArgumentParser(description='Translate MongoDB collection fields to Spanish.')
    parser.add_argument('--mongodb-connection', help='MongoDB connection URI (replaces MONGODB_URI)')
    parser.add_argument('--mongodb-db', help='MongoDB database name for analysis')
    parser.add_argument('--openai-key', help='OpenAI API key')
    parser.add_argument('--store-ids', nargs='+', default=None,
                        help='Store IDs to process (default: all stores)')
    parser.add_argument('--batch-size', type=int, default=50,
                        help='Batch size for processing documents')
    parser.add_argument('--concurrent-calls', type=int, default=10,
                        help='Maximum concurrent API calls to OpenAI')
    parser.add_argument('--model', default="gpt-4.1-nano",
                        help='OpenAI model to use for translation')
    parser.add_argument('--enable-cache', action='store_true',
                        help='Enable translation caching')
    parser.add_argument('--checkpoint-interval', type=int, default=100,
                        help='Save checkpoint every N documents')
    return parser.parse_args()

# Load environment
found_env = load_environment()
if not found_env:
    logger.warning("Will rely on command-line arguments or environment variables already set in the shell")

# Import utilities after environment is set up
try:
    from utils.translation_utils import translate_text_async
except ImportError as e:
    logger.error(f"Error importing translation_utils: {e}")
    logger.error(f"Current sys.path: {sys.path}")
    sys.exit(1)

# Get arguments
args = parse_args()

# Environment Variables & Constants
MONGODB_CONNECTION_STRING = args.mongodb_connection or os.getenv('MONGODB_CONNECTION')
MONGODB_ANALYSIS_DB = args.mongodb_db or os.getenv('MONGODB_ANALYSIS_DB') or "D-Unit-AnalysisGPT"
OPENAI_API_KEY = args.openai_key or os.getenv('OPENAI_API_KEY')
TARGET_LANGUAGE = "Spanish"
TARGET_LANGUAGE_CODE = "es"
BATCH_SIZE = args.batch_size
MAX_CONCURRENT_API_CALLS = args.concurrent_calls
OPENAI_MODEL = args.model
ENABLE_CACHE = args.enable_cache
CHECKPOINT_INTERVAL = args.checkpoint_interval

# Target store IDs - default to None (all stores)
TARGET_STORE_IDS = args.store_ids

# More unique separator to avoid translation issues
ARRAY_SEPARATOR = "\n<<ARRAY_ITEM_SEPARATOR_DO_NOT_TRANSLATE>>\n"

# Collections and Fields to Translate
COLLECTIONS_AND_FIELDS = {
    "global_analysis": [
        "analysis.summary",
        "analysis.metrics",
        "analysis.customer_analysis",
        "analysis.product_performance",
        "analysis.market_position",
        "analysis.social_media_strategy",
        "analysis.recommendations", # Array field
        "analysis.competitor_analysis",
        "analysis.shipping_analysis.analysis_text",
        "analysis.shipping_analysis.recommendations" # Array field
    ],
    "meta_insights": [
        "title",
        "insight_text",
        "recommendations" # Array field
    ]
}

# Fields in these collections that are true arrays. Other text fields are treated as strings.
ARRAY_FIELDS = {
    "global_analysis": [
        "analysis.shipping_analysis.recommendations"
    ],
    "meta_insights": ["recommendations"]
}

# --- Data Classes ---
@dataclass
class TranslationMetrics:
    """Track translation process metrics."""
    start_time: float = field(default_factory=time.time)
    documents_processed: int = 0
    fields_translated: int = 0
    cache_hits: int = 0
    api_calls: int = 0
    errors: int = 0
    total_chars: int = 0
    updates_executed: int = 0
    
    def print_summary(self):
        """Print metrics summary."""
        duration = time.time() - self.start_time
        logger.info("=== Translation Summary ===")
        logger.info(f"Duration: {duration:.2f}s")
        logger.info(f"Documents processed: {self.documents_processed}")
        logger.info(f"Fields translated: {self.fields_translated}")
        logger.info(f"Updates executed: {self.updates_executed}")
        logger.info(f"API calls: {self.api_calls}")
        logger.info(f"Cache hits: {self.cache_hits}")
        logger.info(f"Errors: {self.errors}")
        logger.info(f"Total characters: {self.total_chars}")
        if duration > 0:
            logger.info(f"Avg chars/sec: {self.total_chars / duration:.2f}")
        logger.info("==========================")

class TranslationCache:
    """Simple translation cache."""
    def __init__(self, max_size: int = 10000):
        self.cache: Dict[str, str] = {}
        self.max_size = max_size
    
    def get(self, text_hash: str) -> Optional[str]:
        """Get cached translation."""
        return self.cache.get(text_hash)
    
    def set(self, text_hash: str, translation: str):
        """Cache a translation."""
        if len(self.cache) < self.max_size:
            self.cache[text_hash] = translation
    
    def clear(self):
        """Clear the cache."""
        self.cache.clear()

class TranslationCheckpoint:
    """Handle checkpointing for resume capability."""
    def __init__(self, collection_name: str):
        self.file = f"checkpoint_{collection_name}_translation.json"
        self.data = self.load()
    
    def load(self) -> Dict[str, Any]:
        """Load checkpoint from file."""
        if os.path.exists(self.file):
            try:
                with open(self.file, 'r') as f:
                    data = json.load(f)
                    # Convert list back to set for processed_ids
                    data["processed_ids"] = set(data.get("processed_ids", []))
                    return data
            except Exception as e:
                logger.warning(f"Could not load checkpoint: {e}")
        return {"processed_ids": set(), "last_id": None}
    
    def save(self):
        """Save checkpoint to file."""
        try:
            with open(self.file, 'w') as f:
                json.dump({
                    "processed_ids": list(self.data["processed_ids"]),
                    "last_id": self.data["last_id"],
                    "timestamp": time.time()
                }, f)
        except Exception as e:
            logger.error(f"Could not save checkpoint: {e}")
    
    def mark_processed(self, doc_id: str):
        """Mark document as processed."""
        self.data["processed_ids"].add(str(doc_id))
        self.data["last_id"] = str(doc_id)
    
    def is_processed(self, doc_id: str) -> bool:
        """Check if document was already processed."""
        return str(doc_id) in self.data["processed_ids"]
    
    def cleanup(self):
        """Remove checkpoint file."""
        if os.path.exists(self.file):
            try:
                os.remove(self.file)
                logger.info(f"Removed checkpoint file: {self.file}")
            except Exception as e:
                logger.warning(f"Could not remove checkpoint file: {e}")

# --- Helper Functions ---

def calculate_hash(data: Union[str, List[str]]) -> str:
    """Calculates the SHA-256 hash of the input string or list."""
    hasher = hashlib.sha256()
    if isinstance(data, list):
        # Preserve order, just join (don't sort)
        string_representation = "|||".join([str(item) for item in data])
    elif isinstance(data, str):
        string_representation = data
    else:
        string_representation = str(data) # Fallback for unexpected types
    
    hasher.update(string_representation.encode('utf-8'))
    return hasher.hexdigest()

def get_nested_value(doc: Dict[str, Any], key: str) -> Any:
    """Safely retrieve a value from a nested dictionary using dot notation."""
    if not doc or not key:
        return None
    keys = key.split('.')
    value = doc
    try:
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
            elif isinstance(value, list) and k.isdigit(): # Basic list index access
                value = value[int(k)]
            else:
                return None # Key not found or value is not a dict/list where expected
            if value is None:
                 return None # Stop if any intermediate key is missing
        return value
    except (KeyError, IndexError, TypeError):
        return None

def set_nested_value(update_doc: Dict[str, Any], key: str, value: Any) -> Dict[str, Any]:
    """Builds the $set dictionary key for updates using dot notation."""
    update_doc[key] = value
    return update_doc

def build_projection(fields_to_translate: List[str], lang_code: str) -> Dict[str, int]:
    """Build optimized projection based on what's needed."""
    projection = {"_id": 1}
    
    for field in fields_to_translate:
        projection[field] = 1
        projection[f"{field}_{lang_code}"] = 1
        projection[f"{field}_en_hash"] = 1
    
    return projection

# --- Core Translation Functions ---

async def translate_with_retry(
    text: str,
    target_language: str,
    openai_client: AsyncOpenAI,
    model: str,
    max_retries: int = 3
) -> Optional[str]:
    """Translate with retry logic and exponential backoff."""
    for attempt in range(max_retries):
        try:
            return await translate_text_async(text, target_language, openai_client, model)
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"Translation failed after {max_retries} attempts: {e}")
                return None
            wait_time = 2 ** attempt  # Exponential backoff
            logger.warning(f"Translation failed, retrying in {wait_time}s: {e}")
            await asyncio.sleep(wait_time)
    return None

async def translate_single_field(
    doc_id: str,
    field_key: str,
    source_data: Union[str, List[str]],
    source_hash: str,
    is_array_field: bool,
    lang_code: str,
    openai_client: AsyncOpenAI,
    semaphore: asyncio.Semaphore,
    target_language: str,
    model: str,
    cache: Optional[TranslationCache] = None,
    metrics: Optional[TranslationMetrics] = None
) -> Optional[pymongo.UpdateOne]:
    """Translates a single field and returns a PyMongo UpdateOne operation."""
    target_field_key = f"{field_key}_{lang_code}"
    hash_field_key = f"{field_key}_en_hash"
    text_to_translate = ""
    
    # Check cache first
    if cache:
        cached_translation = cache.get(source_hash)
        if cached_translation:
            logger.debug(f"Using cached translation for {field_key}")
            if metrics:
                metrics.cache_hits += 1
            
            # Process cached translation
            if is_array_field and ARRAY_SEPARATOR in cached_translation:
                final_translation = [item.strip() for item in cached_translation.split(ARRAY_SEPARATOR) if item.strip()]
            else:
                final_translation = cached_translation
                
            update_doc = {}
            set_nested_value(update_doc, target_field_key, final_translation)
            set_nested_value(update_doc, hash_field_key, source_hash)
            return pymongo.UpdateOne({"_id": doc_id}, {"$set": update_doc})

    if is_array_field and isinstance(source_data, list):
        # Filter out empty strings and join the list for translation
        filtered_list = [item for item in source_data if isinstance(item, str) and item.strip()]
        if not filtered_list:
            logger.debug(f"Skipping array field {field_key} for doc {doc_id}: list is empty or contains no valid strings.")
            return None
        text_to_translate = ARRAY_SEPARATOR.join(filtered_list)
        logger.debug(f"Joined array field {field_key} for doc {doc_id} for translation.")
    elif isinstance(source_data, str):
        text_to_translate = source_data
    else:
        logger.warning(f"Skipping field {field_key} for doc {doc_id}: Unexpected data type {type(source_data)}.")
        return None

    if not text_to_translate.strip():
        logger.debug(f"Skipping field {field_key} for doc {doc_id}: Source text is empty after processing.")
        return None

    # Track character count
    if metrics:
        metrics.total_chars += len(text_to_translate)

    async with semaphore:
        logger.debug(f"Attempting translation for doc {doc_id}, field {field_key}")
        translated_text_block = await translate_with_retry(
            text=text_to_translate,
            target_language=target_language,
            openai_client=openai_client,
            model=model
        )
        
        if metrics:
            metrics.api_calls += 1

    if translated_text_block:
        # Cache the translation
        if cache:
            cache.set(source_hash, translated_text_block)
            
        update_doc = {}
        final_translation: Union[str, List[str]]
        
        if is_array_field:
            # Split the translated block back into a list
            final_translation = [item.strip() for item in translated_text_block.split(ARRAY_SEPARATOR) if item.strip()]
            logger.info(f"Translated and split array for doc {doc_id}, field {target_field_key}. Items: {len(final_translation)}")
        else:
            final_translation = translated_text_block
            logger.info(f"Translated string for doc {doc_id}, field {target_field_key}")

        # Include both translation and the hash of the source text in the update
        set_nested_value(update_doc, target_field_key, final_translation)
        set_nested_value(update_doc, hash_field_key, source_hash)
        
        if metrics:
            metrics.fields_translated += 1
            
        return pymongo.UpdateOne({"_id": doc_id}, {"$set": update_doc})
    else:
        logger.warning(f"Translation failed for doc {doc_id}, field {field_key}")
        if metrics:
            metrics.errors += 1
        return None

async def process_document(
    doc: Dict[str, Any],
    collection_name: str,
    fields_to_translate: List[str],
    lang_code: str,
    openai_client: AsyncOpenAI,
    semaphore: asyncio.Semaphore,
    cache: Optional[TranslationCache] = None,
    metrics: Optional[TranslationMetrics] = None
) -> List[pymongo.UpdateOne]:
    """Process all fields of a single document concurrently."""
    doc_id = doc['_id']
    field_tasks = []
    
    for field_key in fields_to_translate:
        source_data_raw = get_nested_value(doc, field_key)
        target_field_key = f"{field_key}_{lang_code}"
        hash_field_key = f"{field_key}_en_hash"
        existing_translation = get_nested_value(doc, target_field_key)
        existing_hash = get_nested_value(doc, hash_field_key)
        is_array = collection_name in ARRAY_FIELDS and field_key in ARRAY_FIELDS[collection_name]

        # Check if source data is valid and prepare it
        valid_source_for_translation: Optional[Union[str, List[str]]] = None
        valid_source_found = False
        current_source_hash = None
        
        if is_array:
            if isinstance(source_data_raw, list) and source_data_raw:
                valid_items = [item for item in source_data_raw if isinstance(item, str) and item.strip()]
                if valid_items:
                    valid_source_for_translation = valid_items
                    valid_source_found = True
                    current_source_hash = calculate_hash(valid_source_for_translation)
        elif isinstance(source_data_raw, str) and source_data_raw.strip():
            valid_source_for_translation = source_data_raw
            valid_source_found = True
            current_source_hash = calculate_hash(valid_source_for_translation)
        
        # Decide whether to translate
        should_translate = False
        if valid_source_found:
            if existing_translation is None:
                should_translate = True
                logger.debug(f"Reason: No existing translation for {field_key}")
            elif existing_hash is None:
                should_translate = True
                logger.debug(f"Reason: Existing translation but no hash for {field_key}")
            elif current_source_hash != existing_hash:
                should_translate = True
                logger.debug(f"Reason: Hash mismatch for {field_key}")
            else:
                logger.debug(f"Skipping {field_key}: Hashes match, source unchanged.")
        else:
            logger.debug(f"Skipping {field_key}: Invalid or empty source data")

        if should_translate and valid_source_for_translation is not None and current_source_hash is not None:
            logger.info(f"Creating translation task for Doc ID: {doc_id}, Field: {field_key}")
            task = translate_single_field(
                doc_id,
                field_key,
                valid_source_for_translation,
                current_source_hash,
                is_array,
                lang_code,
                openai_client,
                semaphore,
                TARGET_LANGUAGE,
                OPENAI_MODEL,
                cache,
                metrics
            )
            field_tasks.append(task)
    
    # Process all fields of this document concurrently
    if field_tasks:
        updates = await asyncio.gather(*field_tasks)
        return [upd for upd in updates if upd is not None]
    return []

async def process_collection(
    collection_name: str,
    fields_to_translate: List[str],
    db: motor.motor_asyncio.AsyncIOMotorDatabase,
    openai_client: AsyncOpenAI,
    semaphore: asyncio.Semaphore,
    lang_code: str,
    cache: Optional[TranslationCache] = None,
    metrics: Optional[TranslationMetrics] = None
):
    """Processes a single collection with improved batching and progress tracking."""
    collection = db[collection_name]
    logger.info(f"Processing collection: {collection_name}")
    
    # Initialize checkpoint
    checkpoint = TranslationCheckpoint(collection_name)
    
    processed_docs = 0
    updates_executed = 0

    # Build projection
    projection_fields = build_projection(fields_to_translate, lang_code)

    try:
        # Apply filter for target store IDs if provided
        query_filter = {}
        if TARGET_STORE_IDS:
            query_filter["_id"] = {"$in": TARGET_STORE_IDS}
            logger.info(f"Applying filter for stores: {TARGET_STORE_IDS}")
        else:
            logger.info("Processing all stores (no store ID filter applied).")
        
        # Count total documents for progress tracking
        total_docs = await collection.count_documents(query_filter)
        logger.info(f"Total documents to process in {collection_name}: {total_docs}")
        
        # Create cursor with batch size
        cursor = collection.find(query_filter, projection_fields).batch_size(100)
        
        # Progress bar if available
        progress_bar = None
        if TQDM_AVAILABLE:
            progress_bar = tqdm(total=total_docs, desc=f"Processing {collection_name}")
        
        batch_updates = []
        batch_count = 0
        
        async for doc in cursor:
            doc_id = str(doc['_id'])
            
            # Skip if already processed (from checkpoint)
            if checkpoint.is_processed(doc_id):
                processed_docs += 1
                if progress_bar:
                    progress_bar.update(1)
                continue
            
            # Process document
            updates = await process_document(
                doc, collection_name, fields_to_translate,
                lang_code, openai_client, semaphore, cache, metrics
            )
            
            batch_updates.extend(updates)
            processed_docs += 1
            
            if metrics:
                metrics.documents_processed += 1
            
            # Mark as processed
            checkpoint.mark_processed(doc_id)
            
            # Update progress
            if progress_bar:
                progress_bar.update(1)
            
            # Process batch when it reaches the size limit
            if len(batch_updates) >= BATCH_SIZE:
                if batch_updates:
                    try:
                        result = await collection.bulk_write(batch_updates, ordered=False)
                        updates_executed += result.modified_count
                        if metrics:
                            metrics.updates_executed += result.modified_count
                        logger.info(f"Bulk write for {collection_name} completed. Modified: {result.modified_count}")
                    except BulkWriteError as bwe:
                        logger.error(f"Bulk write error in {collection_name}: {bwe.details}")
                        if metrics:
                            metrics.errors += 1
                    batch_updates = []
                
                # Save checkpoint periodically
                batch_count += 1
                if batch_count % (CHECKPOINT_INTERVAL // BATCH_SIZE) == 0:
                    checkpoint.save()
                    logger.debug(f"Saved checkpoint for {collection_name}")

        # Process any remaining updates
        if batch_updates:
            try:
                result = await collection.bulk_write(batch_updates, ordered=False)
                updates_executed += result.modified_count
                if metrics:
                    metrics.updates_executed += result.modified_count
                logger.info(f"Final bulk write for {collection_name} completed. Modified: {result.modified_count}")
            except BulkWriteError as bwe:
                logger.error(f"Final bulk write error in {collection_name}: {bwe.details}")
                if metrics:
                    metrics.errors += 1
        
        # Close progress bar
        if progress_bar:
            progress_bar.close()
        
        # Clean up checkpoint on successful completion
        checkpoint.cleanup()
        
        logger.info(f"Finished collection {collection_name}. Processed: {processed_docs}, Updated: {updates_executed}")

    except Exception as e:
        logger.error(f"Error processing collection {collection_name}: {e}", exc_info=True)
        if metrics:
            metrics.errors += 1
        # Save checkpoint on error
        checkpoint.save()
        logger.info(f"Saved checkpoint for {collection_name} due to error")

async def main():
    """Main function to initialize clients and orchestrate collection processing."""
    # Initialize metrics
    metrics = TranslationMetrics()
    
    # Print configuration for verification
    logger.info(f"MongoDB Connection: {'is set' if MONGODB_CONNECTION_STRING else 'NOT SET'}")
    logger.info(f"MongoDB Analysis DB: {MONGODB_ANALYSIS_DB if MONGODB_ANALYSIS_DB else 'NOT SET'}")
    logger.info(f"OpenAI API Key: {'is set' if OPENAI_API_KEY else 'NOT SET'}")
    logger.info(f"Model: {OPENAI_MODEL}")
    logger.info(f"Cache enabled: {ENABLE_CACHE}")
    
    if not all([MONGODB_CONNECTION_STRING, MONGODB_ANALYSIS_DB, OPENAI_API_KEY]):
        logger.critical("Missing required environment variables (MONGODB_CONNECTION, MONGODB_ANALYSIS_DB, OPENAI_API_KEY)")
        logger.critical("Please set these in .env file or provide via command-line arguments.")
        logger.critical("Example: python translate_mongo_collections_async.py --mongodb-connection 'mongodb+srv://...' --mongodb-db 'D-Unit-AnalysisGPT' --openai-key 'sk-...'")
        return
    
    # Explicitly assert that MONGODB_ANALYSIS_DB is not None for type checking
    assert MONGODB_ANALYSIS_DB is not None, "MONGODB_ANALYSIS_DB environment variable is not set"

    mongo_client = None # Define outside try block
    cache = TranslationCache() if ENABLE_CACHE else None
    
    try:
        # Initialize MongoDB client with connection pooling
        mongo_client = motor.motor_asyncio.AsyncIOMotorClient(
            MONGODB_CONNECTION_STRING,
            maxPoolSize=50,
            minPoolSize=10,
            maxIdleTimeMS=30000
        )
        db = mongo_client[MONGODB_ANALYSIS_DB]
        
        # Test connection
        await db.command('ping')
        logger.info("Successfully connected to MongoDB.")
    except Exception as e:
        logger.critical(f"Failed to connect to MongoDB: {e}", exc_info=True)
        return

    try:
        openai_client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        logger.info("Successfully initialized OpenAI client.")
    except Exception as e:
        logger.critical(f"Failed to initialize OpenAI client: {e}", exc_info=True)
        if mongo_client:
            mongo_client.close()
        return

    semaphore = asyncio.Semaphore(MAX_CONCURRENT_API_CALLS)
    processing_tasks = []

    logger.info("Starting translation process for configured collections...")
    
    # Process collections concurrently (but not too many at once)
    collection_semaphore = asyncio.Semaphore(2)  # Process 2 collections at once
    
    async def process_collection_with_semaphore(collection_name: str, fields: List[str]):
        async with collection_semaphore:
            await process_collection(
                collection_name,
                fields,
                db,
                openai_client,
                semaphore,
                TARGET_LANGUAGE_CODE,
                cache,
                metrics
            )
    
    for collection_name, fields in COLLECTIONS_AND_FIELDS.items():
        if fields:
            task = asyncio.create_task(
                process_collection_with_semaphore(collection_name, fields)
            )
            processing_tasks.append(task)
        else:
            logger.warning(f"Skipping collection {collection_name} as no fields are configured for translation.")

    if processing_tasks:
        try:
            await asyncio.gather(*processing_tasks)
            logger.info("All collection processing tasks completed.")
        except Exception as e:
            logger.error(f"Error during collection processing: {e}", exc_info=True)
    else:
        logger.info("No collections were configured for processing.")

    # Cleanup
    if mongo_client:
        mongo_client.close()
        logger.info("MongoDB connection closed.")
    
    if cache:
        cache.clear()
        logger.info("Translation cache cleared.")
    
    # Print metrics
    metrics.print_summary()

if __name__ == "__main__":
    # Print configuration for clarity
    store_info = f"stores {TARGET_STORE_IDS}" if TARGET_STORE_IDS else "all stores"
    logger.info(f"Starting MongoDB translation script for {store_info}...")
    logger.info(f"Processing collections: {', '.join(COLLECTIONS_AND_FIELDS.keys())}")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Translation interrupted by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
    finally:
        logger.info("MongoDB translation script finished.")