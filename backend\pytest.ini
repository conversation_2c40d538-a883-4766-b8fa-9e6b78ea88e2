[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: marks tests as unit tests (deselect with '-m "not unit"')
    integration: marks tests as integration tests (deselect with '-m "not integration"')
    load: marks tests as load tests (deselect with '-m "not load"')
    security: marks tests as security tests (deselect with '-m "not security"')
    cost: marks tests as cost tracking tests (deselect with '-m "not cost"')
    slow: marks tests as slow running (deselect with '-m "not slow"')
    requires_database: marks tests requiring database connection
asyncio_mode = auto
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning 