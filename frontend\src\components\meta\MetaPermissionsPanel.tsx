import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Switch,
  FormControlLabel,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  Tooltip,
  Link
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import InfoIcon from '@mui/icons-material/Info';
import LockIcon from '@mui/icons-material/Lock';
import RefreshIcon from '@mui/icons-material/Refresh';
import { MetaAuthService } from '../../services/auth';
import { PERMISSION_DESCRIPTIONS, META_PERMISSIONS, MetaPage, MetaError } from '../../services/types';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';

interface MetaPermissionsPanelProps {
  page: MetaPage | null;
  useMockData?: boolean;
}

// TabContentProps was defined but not used, removing it to fix linting error
// If a similar interface is needed in the future, define it where it's used

export const MetaPermissionsPanel: React.FC<MetaPermissionsPanelProps> = ({ page, useMockData = false }) => {
  const { t } = useTranslation();
  // State declarations
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [permissions, setPermissions] = useState<Array<{permission: string, status: string}>>([]);
  const [confirmationOpen, setConfirmationOpen] = useState<boolean>(false);
  const [selectedPermission, setSelectedPermission] = useState<string | null>(null);
  const [categoryTab, setCategoryTab] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [requiredPermInfoOpen, setRequiredPermInfoOpen] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  
  // Get the context for refreshing permissions throughout the app
  const { refreshPermissions: refreshAppPermissions } = useMetaPermissions();
  
  // Categories for permissions
  const categories = ['all', 'page', 'business', 'content', 'instagram', 'advertising', 'other'];
  
  // Handle error display, ensuring messages from MetaAuthService are also translatable if they are keys
  const displayError = useCallback((err: unknown, defaultKey: string) => {
    if (typeof err === 'string') {
      setError(t(err, { defaultValue: err })); 
    } else if ((err as MetaError).message) {
      const message = (err as MetaError).message;
      setError(t(message, { defaultValue: message }));
    } else {
      setError(t(defaultKey));
    }
  }, [t]);
  
  const handleRefreshPermissions = async () => {
    try {
      setRefreshing(true);
      setError(null);
      if (useMockData) {
        const mockPermissions = META_PERMISSIONS.facebook.map(perm => ({ permission: perm, status: 'granted' }));
        setPermissions(mockPermissions);
      } else {
        const permissionsData = await MetaAuthService.getCurrentPermissions(true);
        setPermissions(permissionsData);
        await refreshAppPermissions(true);
      }
    } catch (err: unknown) {
      displayError(err, 'metaPermissions.errors.refreshFailed');
    } finally {
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    const fetchPermissions = async () => {
      if (!page && !useMockData) return;
      setLoading(true);
      setError(null);
      try {
        if (useMockData) {
          const mockPermissions = META_PERMISSIONS.facebook.map(perm => ({ permission: perm, status: 'granted' }));
          setPermissions(mockPermissions);
        } else {
          const permissionsData = await MetaAuthService.getCurrentPermissions();
          setPermissions(permissionsData);
        }
      } catch (err: unknown) {
        displayError(err, 'metaPermissions.errors.fetchFailed');
      } finally {
        setLoading(false);
      }
    };
    fetchPermissions();
  }, [useMockData, page, displayError]);
  
  const handlePermissionToggle = async (permission: string) => {
    if (PERMISSION_DESCRIPTIONS[permission]?.isRequired) return;
    const currentStatus = permissions.find(p => p.permission === permission)?.status;
    if (currentStatus === 'granted') {
      setSelectedPermission(permission);
      setConfirmationOpen(true);
    } else if (currentStatus === 'declined') {
      setError(t('metaPermissions.errors.alreadyRevoked'));
    }
  };
  
  const handleRevokePermission = async () => {
    if (!selectedPermission) return;
    setConfirmationOpen(false);
    setLoading(true);
    try {
      const success = await MetaAuthService.revokePermission(selectedPermission);
      if (success) {
        const updatedPermissions = permissions.map(p =>
          p.permission === selectedPermission ? { ...p, status: 'declined' } : p
        );
        setPermissions(updatedPermissions);
        await refreshAppPermissions(true);
      } else {
        setError(t('metaPermissions.errors.revokeFailed', { permissionName: selectedPermission }));
      }
    } catch (err: unknown) {
      displayError(err, 'metaPermissions.errors.revokeFailedGeneric');
    } finally {
      setLoading(false);
    }
  };
  
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCategoryTab(newValue);
  };
  
  const filteredPermissions = permissions.filter(p => {
    const permDetails = PERMISSION_DESCRIPTIONS[p.permission];
    let translatedName = p.permission;
    let translatedDescription = '';

    if (permDetails) {
      translatedName = t(permDetails.nameKey, { defaultValue: p.permission });
      translatedDescription = t(permDetails.descriptionKey, { defaultValue: t('metaPermissions.details.unavailableDescription') });
    } else {
      translatedName = t('metaPermissions.details.unknownPermission.name', { permissionId: p.permission });
      translatedDescription = t('metaPermissions.details.unknownPermission.description');
    }

    const matchesSearch = searchTerm === '' ||
      p.permission.toLowerCase().includes(searchTerm.toLowerCase()) ||
      translatedName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      translatedDescription.toLowerCase().includes(searchTerm.toLowerCase());

    const category = permDetails?.category || 'other';
    const matchesCategory = categoryTab === 0 || categories[categoryTab] === category;
    return matchesSearch && matchesCategory;
  });
  
  const handleRequiredPermClick = () => {
    setRequiredPermInfoOpen(true);
  };
  
  if (loading && permissions.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>;
  }
  
  const renderTabContent = (index: number, value: number, children: React.ReactNode) => (
    <div role="tabpanel" hidden={value !== index} id={`permission-tabpanel-${index}`} aria-labelledby={`permission-tab-${index}`}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
  
  return (
    <Box>
      <Paper variant="outlined" sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>{t('metaPermissions.title')}</Typography>
        <Typography variant="body1" color="text.secondary" paragraph>{t('metaPermissions.description')}</Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Button variant="outlined" color="primary" startIcon={<RefreshIcon />} onClick={handleRefreshPermissions} disabled={refreshing || loading} sx={{ mr: 2 }}>
            {refreshing ? t('metaPermissions.refreshingButton') : t('metaPermissions.refreshButton')}
          </Button>
          <TextField placeholder={t('metaPermissions.searchPlaceholder')} size="small" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{ startAdornment: (<InputAdornment position="start"><SearchIcon /></InputAdornment>) }}
          />
        </Box>
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={categoryTab} onChange={handleTabChange} aria-label={t('metaPermissions.tabs.ariaLabel', 'permission categories')}>
            <Tab label={t('metaPermissions.tabs.all')} />
            <Tab label={t('metaPermissions.tabs.page')} />
            <Tab label={t('metaPermissions.tabs.business')} />
            <Tab label={t('metaPermissions.tabs.content')} />
            <Tab label={t('metaPermissions.tabs.instagram')} />
            <Tab label={t('metaPermissions.tabs.advertising')} />
            <Tab label={t('metaPermissions.tabs.other')} />
          </Tabs>
        </Box>
        
        {renderTabContent(categoryTab, categoryTab, (
          <Grid container spacing={3}>
            {filteredPermissions.length > 0 ? filteredPermissions.map((perm) => {
              const permDetails = PERMISSION_DESCRIPTIONS[perm.permission];
              const isRevoked = perm.status === 'declined';
              
              let name, description, impactText;
              if (permDetails) {
                name = t(permDetails.nameKey, { defaultValue: perm.permission });
                description = t(permDetails.descriptionKey, { defaultValue: t('metaPermissions.details.unavailableDescription') });
                impactText = t(permDetails.impactKey, { defaultValue: t('metaPermissions.details.unavailableImpact') });
              } else {
                name = t('metaPermissions.details.unknownPermission.name', { permissionId: perm.permission });
                description = t('metaPermissions.details.unknownPermission.description');
                impactText = t('metaPermissions.details.unknownPermission.impact');
              }

              return (
                <Grid item xs={12} key={perm.permission}>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', borderColor: isRevoked ? 'grey.300' : 'divider', opacity: isRevoked ? 0.7 : 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box sx={{ flexGrow: 1, mr: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>{name}</Typography>
                      </Box>
                      <FormControlLabel control={<Switch checked={perm.status === 'granted'} onChange={() => handlePermissionToggle(perm.permission)} disabled={permDetails?.isRequired || loading || isRevoked}/>} label={name} labelPlacement="start" sx={{ ml: 0, mr: -1.5 }} />
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>{description}</Typography>
                    <Box sx={{ mt: 1.5, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="caption" color="text.secondary">
                        {permDetails?.isRequired ? (
                          <Tooltip title={t('metaPermissions.status.whyRequiredTooltip')}>
                            <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center', color: 'warning.main', cursor: 'pointer' }} onClick={handleRequiredPermClick}>
                              <LockIcon fontSize="small" sx={{ mr: 0.5 }} />
                              {t('metaPermissions.status.required')}
                              <Button size="small" sx={{ ml: 0.5, minWidth: 'auto', p: '2px 4px', textTransform: 'none', fontSize: '0.75rem' }}>{t('metaPermissions.status.whyButton')}</Button>
                            </Box>
                          </Tooltip>
                        ) : t('metaPermissions.status.optional')}
                      </Typography>
                      {isRevoked && (
                        <Typography variant="caption" color="error.main" sx={{ fontWeight: 'medium' }}>
                          {t('metaPermissions.status.revoked')}
                        </Typography>
                      )}
                    </Box>
                    {perm.status === 'granted' && !permDetails?.isRequired && permDetails?.impactKey && (
                      <Alert severity="warning" icon={<InfoIcon fontSize="inherit" />} sx={{ mt: 1, fontSize: '0.75rem', p: '0px 12px' }}>
                        {t('metaPermissions.warnings.genericImpact', { impactText })}
                      </Alert>
                    )}
                  </Paper>
                </Grid>
              );
            }) : (
              <Grid item xs={12}>
                <Typography align="center" color="text.secondary" sx={{ py: 5 }}>{t('metaPermissions.noMatchFilters')}</Typography>
              </Grid>
            )}
          </Grid>
        ))}
      </Paper>
      
      <Dialog open={confirmationOpen} onClose={() => setConfirmationOpen(false)}>
        <DialogTitle>{t('metaPermissions.revokeDialog.title')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {selectedPermission && (
              t('metaPermissions.revokeDialog.confirmText', { 
                permissionName: PERMISSION_DESCRIPTIONS[selectedPermission] ? 
                                t(PERMISSION_DESCRIPTIONS[selectedPermission]!.nameKey, {defaultValue: selectedPermission}) : 
                                t('metaPermissions.details.unknownPermission.name', {permissionId: selectedPermission})
              })
            )}
          </DialogContentText>
          {selectedPermission && PERMISSION_DESCRIPTIONS[selectedPermission]?.impactKey && (
            <DialogContentText sx={{mt: 1, fontSize: '0.9rem', color: 'text.secondary'}}>
                {t('metaPermissions.revokeDialog.impactPrefix', { 
                    impactText: t(PERMISSION_DESCRIPTIONS[selectedPermission]!.impactKey, {defaultValue: t('metaPermissions.revokeDialog.unknownImpact')})
                })}
            </DialogContentText>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmationOpen(false)}>{t('metaPermissions.revokeDialog.cancelButton')}</Button>
          <Button onClick={handleRevokePermission} color="error">{t('metaPermissions.revokeDialog.revokeButton')}</Button>
        </DialogActions>
      </Dialog>
      
      <Dialog open={requiredPermInfoOpen} onClose={() => setRequiredPermInfoOpen(false)}>
        <DialogTitle>{t('metaPermissions.status.required')}</DialogTitle>
        <DialogContent>
          <DialogContentText component="div">
            <Typography paragraph>{t('metaPermissions.requiredInfoDialogText')}</Typography>
            <Typography variant="subtitle2" color="primary" gutterBottom>{t('metaPermissions.requiredInfoDialog.whyRequired.title')}</Typography>
            <Typography paragraph>
              <strong>{t('metaPermissions.requiredInfoDialog.whyRequired.accessPagesList')}</strong> {t('metaPermissions.requiredInfoDialog.whyRequired.and')} <strong>{t('metaPermissions.requiredInfoDialog.whyRequired.businessManagement')}</strong> {t('metaPermissions.requiredInfoDialog.whyRequired.areFundamental')}
              <ul>
                <li>{t('metaPermissions.requiredInfoDialog.whyRequired.listItem1')}</li>
                <li>{t('metaPermissions.requiredInfoDialog.whyRequired.listItem2')}</li>
                <li>{t('metaPermissions.requiredInfoDialog.whyRequired.listItem3')}</li>
              </ul>
            </Typography>
            <Typography variant="subtitle2" color="primary" gutterBottom>{t('metaPermissions.requiredInfoDialog.howToRevokeAll.title')}</Typography>
            <Typography paragraph>
              {t('metaPermissions.requiredInfoDialog.howToRevokeAll.text')}
              <ol>
                <li>{t('metaPermissions.requiredInfoDialog.howToRevokeAll.step1', { disconnectButton: t('metaDashboard.disconnectButton') })}</li>
                <li><Link href="https://www.facebook.com/settings?tab=applications" target="_blank" rel="noopener noreferrer">{t('metaPermissions.requiredInfoDialog.howToRevokeAll.step2')}</Link></li>
                <li>{t('metaPermissions.requiredInfoDialog.howToRevokeAll.step3')}</li>
              </ol>
              <strong>{t('metaPermissions.requiredInfoDialog.howToRevokeAll.note')}</strong>
            </Typography>
            <Alert severity="warning" sx={{ mt: 2 }}>{t('metaPermissions.requiredInfoDialog.warning')}</Alert>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRequiredPermInfoOpen(false)}>{t('metaPermissions.common.okButton')}</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}; 