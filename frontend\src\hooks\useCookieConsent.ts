import { useState, useEffect, useCallback } from 'react';
import { CookiePreferences, CookieConsent } from '../types/cookies';
import cookieService from '../services/cookieService';

export const useCookieConsent = () => {
  const [hasConsent, setHasConsent] = useState(cookieService.hasConsent());
  const [shouldShowBanner, setShouldShowBanner] = useState(cookieService.shouldShowBanner());
  const [preferences, setPreferences] = useState<CookiePreferences>(cookieService.getPreferences());
  const [consentRecord, setConsentRecord] = useState<CookieConsent | null>(cookieService.getConsentRecord());

  const updateState = useCallback(() => {
    setHasConsent(cookieService.hasConsent());
    setShouldShowBanner(cookieService.shouldShowBanner());
    setPreferences(cookieService.getPreferences());
    setConsentRecord(cookieService.getConsentRecord());
  }, []);

  useEffect(() => {
    const handleCookieUpdate = () => {
      updateState();
    };

    // Listen for cookie preference updates
    window.addEventListener('cookiePreferencesUpdated', handleCookieUpdate);
    window.addEventListener('cookieCategoryUpdate', handleCookieUpdate);

    return () => {
      window.removeEventListener('cookiePreferencesUpdated', handleCookieUpdate);
      window.removeEventListener('cookieCategoryUpdate', handleCookieUpdate);
    };
  }, [updateState]);

  const acceptAll = useCallback(() => {
    cookieService.acceptAll();
  }, []);

  const rejectAll = useCallback(() => {
    cookieService.rejectAll();
  }, []);

  const updatePreferences = useCallback((newPreferences: CookiePreferences) => {
    cookieService.updatePreferences(newPreferences);
  }, []);

  const giveConsent = useCallback((preferences?: CookiePreferences) => {
    cookieService.giveConsent(preferences);
  }, []);

  const resetConsent = useCallback(() => {
    cookieService.resetConsent();
  }, []);

  const isCategoryEnabled = useCallback((category: keyof CookiePreferences) => {
    return cookieService.isCategoryEnabled(category);
  }, []);

  const setCookie = useCallback((
    name: string, 
    value: string, 
    category: keyof CookiePreferences, 
    days?: number
  ) => {
    return cookieService.setCookie(name, value, category, days);
  }, []);

  const getCookie = useCallback((name: string) => {
    return cookieService.getCookie(name);
  }, []);

  return {
    // State
    hasConsent,
    shouldShowBanner,
    preferences,
    consentRecord,
    
    // Actions
    acceptAll,
    rejectAll,
    updatePreferences,
    giveConsent,
    resetConsent,
    
    // Utilities
    isCategoryEnabled,
    setCookie,
    getCookie,
    
    // Manual state refresh
    refresh: updateState
  };
};

export default useCookieConsent; 