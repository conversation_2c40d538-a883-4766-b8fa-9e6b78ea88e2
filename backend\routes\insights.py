from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
import logging
import json
from bson import json_util # Import BSON utility for JSON serialization

from models.insights import (
    ProcessPostsRequest,
    ProcessCommentsRequest,
    EngagementInsightsRequest,
    AudienceInsightsRequest,
    AdInsightsRequest,
    CorrelatePostsSalesRequest,
    AdvancedCorrelationRequest,
    ForecastRequest,
    MetaInsight
)
from services.insights import InsightsService
from services.auth import get_current_user
from models.user import User # Add import if it's missing, adjust path if needed

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/insights",
    tags=["insights"],
    responses={404: {"description": "Not found"}},
)

@router.post("/process-posts", response_model=List[Dict[str, Any]])
async def process_posts(
    request: ProcessPostsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Process Meta posts to generate content insights
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.process_posts(request.posts, store_id)

@router.post("/process-comments", response_model=List[Dict[str, Any]])
async def process_comments(
    request: ProcessCommentsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Process Meta comments to generate sentiment insights
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.process_comments(request.comments, store_id)

@router.post("/engagement", response_model=List[Dict[str, Any]])
async def generate_engagement_insights(
    request: EngagementInsightsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Generate insights from engagement metrics
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.generate_engagement_insights(request.metrics, store_id)

@router.post("/audience", response_model=List[Dict[str, Any]])
async def generate_audience_insights(
    request: AudienceInsightsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Generate insights from audience data
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.generate_audience_insights(
        request.followers, 
        request.demographics, 
        store_id
    )

@router.post("/ads", response_model=List[Dict[str, Any]])
async def generate_ad_insights(
    request: AdInsightsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Generate insights from ad performance data
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.generate_ad_insights(request.performance, store_id)

@router.post("/correlate-posts-sales", response_model=List[Dict[str, Any]])
async def correlate_posts_with_sales(
    request: CorrelatePostsSalesRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Correlate posts with sales data to identify patterns
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    return await InsightsService.correlate_posts_with_sales(
        request.posts, 
        request.sales, 
        store_id
    )

@router.get("/page/{page_id}", response_model=List[MetaInsight])
async def get_insights_for_page(
    page_id: str,
    insight_type: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get all insights for a specific Meta page
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    try:
        # Fetch raw data from the service
        insights_raw = await InsightsService.get_insights_for_page(page_id, store_id, insight_type)
        
        # Service layer returns mock data if no insights found,
        # so no need for explicit 404 check here unless service itself fails.

        insights_models: List[MetaInsight] = []
        for insight_dict in insights_raw:
            try:
                # Map _id to id and convert ObjectId to str
                if "_id" in insight_dict:
                    insight_dict["id"] = str(insight_dict.pop("_id"))
                
                # Validate and create the Pydantic model instance
                # This will also handle datetime serialization correctly via FastAPI
                insights_models.append(MetaInsight.model_validate(insight_dict))
            except Exception as model_exc:
                # Log error for the specific insight that failed conversion
                insight_id = insight_dict.get('id', 'unknown') # Use new 'id' if available
                logger.error(f"Error converting insight {insight_id} for page {page_id} to Pydantic model: {model_exc}", exc_info=True)
                # Optionally, skip this insight or raise an error
                # For now, we'll just skip the problematic one to avoid full request failure
                continue 

        # Return the list of Pydantic models; FastAPI handles serialization
        return insights_models

    except HTTPException as http_exc:
        # Re-raise HTTPExceptions (like auth errors)
        raise http_exc
    except Exception as e:
        # Catch-all for other unexpected errors during service call or processing
        logger.error(f"Error fetching insights for page {page_id}, store {store_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while fetching insights."
        )

@router.post("/advanced-correlation", response_model=List[Dict[str, Any]])
async def perform_advanced_correlation(
    request: AdvancedCorrelationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Perform advanced correlation analysis between Meta and store data
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    time_lag = request.time_lag if request.time_lag is not None else 0
    
    return await InsightsService.perform_advanced_correlation(
        request.meta_data,
        request.store_data,
        store_id,
        time_lag,
        request.segment,
        request.dimensions
    )

@router.post("/forecast", response_model=List[Dict[str, Any]])
async def forecast_metrics(
    request: ForecastRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Forecast future values for specified metrics
    """
    store_id = current_user.get("store_id")
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    confidence_level = request.confidence_level if request.confidence_level is not None else 0.95
    
    return await InsightsService.forecast_metrics(
        request.metric_type,
        request.historical_data,
        request.forecast_periods,
        store_id,
        confidence_level
    )

@router.post("/generate/{page_id}")
async def generate_insights_for_page(
    page_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Generate comprehensive AI insights for a specific Meta page
    """
    store_id = current_user.id_store
    if not store_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    logger.info(f"Generating insights for page {page_id}, store {store_id}")
    
    try:
        # Generate insights using the insights service
        insights = await InsightsService.generate_comprehensive_insights(page_id, store_id)
        
        logger.info(f"Successfully generated {len(insights)} insights for page {page_id}")
        return {"success": True, "insights_count": len(insights), "message": "Insights generated successfully"}
        
    except Exception as e:
        logger.error(f"Error generating insights for page {page_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate insights: {str(e)}"
        )

@router.get("/direct/page/{page_id}")
async def get_direct_insights_for_page(
    page_id: str,
    current_user: User = Depends(get_current_user) # Changed type hint
):
    """
    [Direct Access] Get raw insights data for a specific Meta page, bypassing complex service logic.
    Handles BSON types (ObjectId, datetime) for direct JSON response.
    """
    store_id = current_user.id_store # Changed to attribute access
    if not store_id:
        logger.error(f"Direct insights access denied for page {page_id}: Store ID missing for user {current_user.email}") # Changed id_user to email
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Store ID not found in user profile"
        )
    
    logger.info(f"Direct insights request for page {page_id}, store {store_id}")
    
    # <<< Checklist Item 4: Add hardcoded return and comment out original logic >>>
    logger.info(f"Direct insights request for page {page_id}, store {store_id} - RETURNING HARDCODED DATA")
    # Return a simple list of dicts that is easily JSON serializable
    return [
        {"id": "test_id_1", "insight_type": "test", "insight_text": "This is hardcoded insight 1.", "source_data_type": "test", "source_data_id": page_id, "timestamp": "2023-01-01T12:00:00Z"},
        {"id": "test_id_2", "insight_type": "test", "insight_text": "This is hardcoded insight 2.", "source_data_type": "test", "source_data_id": page_id, "timestamp": "2023-01-02T12:00:00Z"}
    ]
    #
    # try:
    #     # Call a new, simplified service method (to be created in Checklist Item 3)
    #     insights_raw = await InsightsService.get_direct_raw_insights(page_id, store_id)
    #     
    #     if not insights_raw:
    #         logger.warning(f"Direct insights: No raw insights found for page {page_id}, store {store_id}. Returning empty list.")
    #         return []
    #
    #     # Serialize using bson.json_util to handle ObjectId and datetime
    #     # This ensures the response is valid JSON even with BSON types
    #     # Using dumps and then loads is a common pattern to get a serializable dict/list
    #     serializable_insights = json.loads(json_util.dumps(insights_raw))
    #     
    #     logger.info(f"Direct insights: Successfully retrieved and serialized {len(serializable_insights)} insights for page {page_id}")
    #     return serializable_insights
    #
    # except HTTPException as http_exc:
    #     logger.error(f"Direct insights HTTP error for page {page_id}, store {store_id}: {http_exc.detail}", exc_info=True)
    #     raise http_exc
    # except Exception as e:
    #     logger.error(f"Direct insights internal error for page {page_id}, store {store_id}: {e}", exc_info=True)
    #     raise HTTPException(
    #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    #         detail=f"An internal error occurred while fetching direct insights: {e}"
    #     )
    # <<< End Checklist Item 4 >>>
# <<< End Checklist Item 1 >>> 