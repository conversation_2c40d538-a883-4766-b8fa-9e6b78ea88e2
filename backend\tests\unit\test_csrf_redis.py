import pytest
import asyncio
import fakeredis.asyncio

from services.csrf_service import CSRFTokenService
from config import redis_client


@pytest.fixture(autouse=True)
def patch_redis(monkeypatch):
    """Patch get_redis() to return an in-memory fake Redis instance."""
    fake = fakeredis.asyncio.FakeRedis()
    monkeypatch.setattr(redis_client, "get_redis", lambda: fake)
    return fake


@pytest.mark.asyncio
async def test_token_roundtrip():
    svc = CSRFTokenService(token_ttl_minutes=1)
    uid = "<EMAIL>"
    token = await svc.generate_token(uid)
    assert await svc.validate_token(token, uid)
    assert not await svc.validate_token("bad-token", uid)


@pytest.mark.asyncio
async def test_rotation_invalidates_old_token():
    svc = CSRFTokenService(token_ttl_minutes=1)
    uid = "<EMAIL>"
    token = await svc.generate_token(uid)
    new_token = await svc.rotate_token_on_sensitive_operation(uid, token)

    # new token valid, old invalid
    assert await svc.validate_token(new_token, uid)
    assert not await svc.validate_token(token, uid)


@pytest.mark.asyncio
async def test_invalidate_user_tokens():
    svc = CSRFTokenService(token_ttl_minutes=1)
    uid = "<EMAIL>"
    t1 = await svc.generate_token(uid)
    t2 = await svc.generate_token(uid)

    # both valid initially
    assert await svc.validate_token(t1, uid)
    assert await svc.validate_token(t2, uid)

    await svc.invalidate_user_tokens(uid)

    assert not await svc.validate_token(t1, uid)
    assert not await svc.validate_token(t2, uid) 