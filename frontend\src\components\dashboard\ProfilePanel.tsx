import React, { useState } from 'react';
import { logger } from '../../utils/logger';
import { useSecureForm } from '../../hooks/useSecureForm';

interface ProfileData {
  name: string;
  email: string;
  company: string;
  phone: string;
  address: string;
  website: string;
  logo: string;
  businessType: string;
  taxId: string;
  [key: string]: unknown;
}

interface ProfilePanelProps {
  data: ProfileData;
}

const ProfilePanel: React.FC<ProfilePanelProps> = ({ data }) => {  const [isEditing, setIsEditing] = useState(false);  const { isSubmitting, isRecovering, lastError, submitSecureForm, clearError } = useSecureForm();  const [formData, setFormData] = useState<ProfileData>(data || {    name: '',    email: '',    company: '',    phone: '',    address: '',    website: '',    logo: '',    businessType: '',    taxId: ''  });  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

    const handleSubmit = async (e: React.FormEvent) => {    e.preventDefault();    setMessage(null);    clearError();    try {      await submitSecureForm<ProfileData, unknown>({        url: '/api/profile/update',        method: 'PUT',        data: formData,        onSuccess: () => {          setIsEditing(false);          setMessage({ type: 'success', text: 'Profile updated successfully' });        },        onError: (error: unknown) => {          const errorMessage = typeof error === 'string' ? error : 'Failed to update profile';          setMessage({ type: 'error', text: lastError || errorMessage });        }      });    } catch (error) {      logger.error('Error updating profile:', error);      setMessage({ type: 'error', text: lastError || 'Failed to update profile' });    }  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6">
                <div className="flex justify-between items-center mb-6">          <h3 className="text-xl font-semibold text-gray-800">Company Profile</h3>          <button            onClick={() => setIsEditing(!isEditing)}            className="px-4 py-2 text-sm font-medium text-white bg-[#0D6EFD] rounded-md hover:bg-[#0B5ED7] transition-colors"            disabled={isSubmitting || isRecovering}          >            {isEditing ? 'Cancel' : 'Edit Profile'}          </button>        </div>        {message && (          <div className={`mb-4 p-4 rounded-md ${            message.type === 'success'               ? 'bg-green-50 border border-green-200 text-green-800'               : 'bg-red-50 border border-red-200 text-red-800'          }`}>            {message.text}          </div>        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            {/* Company Information */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Business Type
                </label>
                <input
                  type="text"
                  name="businessType"
                  value={formData.businessType}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax ID
                </label>
                <input
                  type="text"
                  name="taxId"
                  value={formData.taxId}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
                />
              </div>
            </div>
          </div>

          {/* Full Width Fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Logo URL
              </label>
              <input
                type="url"
                name="logo"
                value={formData.logo}
                onChange={handleChange}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#0D6EFD] focus:border-[#0D6EFD] disabled:bg-gray-50"
              />
            </div>
          </div>

                    {isEditing && (            <div className="flex justify-end">              <button                type="submit"                disabled={isSubmitting || isRecovering}                className="px-6 py-2.5 bg-[#0D6EFD] text-white font-medium rounded-md hover:bg-[#0B5ED7] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"              >                {(isSubmitting || isRecovering) && (                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>                )}                {isRecovering ? 'Updating security...' : isSubmitting ? 'Saving...' : 'Save Changes'}              </button>            </div>          )}
        </form>
      </div>
    </div>
  );
};

export default ProfilePanel; 