import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON>,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import {
  Warning,
  AccountCircle,
  Business,
  Refresh,
  VerifiedUser,
  Policy
} from '@mui/icons-material';

interface InstagramError {
  error: string;
  error_code: number;
  error_action: string;
  instagram_specific?: boolean;
}

interface InstagramErrorHandlerProps {
  error: InstagramError;
  onRetry?: () => void;
  onReconnect?: () => void;
}

const InstagramErrorHandler: React.FC<InstagramErrorHandlerProps> = ({
  error,
  onRetry,
  onReconnect
}) => {
  const getErrorSeverity = (errorCode: number): 'error' | 'warning' | 'info' => {
    if ([190, 10].includes(errorCode)) return 'error';
    if ([100, 2500].includes(errorCode)) return 'warning';
    return 'info';
  };

  const getErrorIcon = (action: string) => {
    switch (action) {
      case 'reauth':
        return <AccountCircle />;
      case 'refresh_token':
        return <Refresh />;
      case 'convert_account':
        return <Business />;
      case 'verify_business':
        return <VerifiedUser />;
      case 'content_blocked':
        return <Policy />;
      default:
        return <Warning />;
    }
  };

  const getActionSteps = (action: string): string[] => {
    switch (action) {
      case 'reauth':
        return [
          'Click "Reconnect Instagram" below',
          'Grant all required permissions',
          'Make sure to select the correct Instagram Business Account'
        ];
      case 'refresh_token':
        return [
          'Your Instagram connection has expired',
          'Click "Reconnect Instagram" to refresh your access',
          'This happens automatically every 60 days'
        ];
      case 'convert_account':
        return [
          'Convert your Instagram account to a Business Account',
          'Go to Instagram app → Settings → Account → Switch to Professional Account',
          'Choose "Business" when prompted',
          'Connect it to your Facebook Page'
        ];
      case 'verify_business':
        return [
          'Your Instagram Business Account needs verification',
          'Go to Facebook Business Manager',
          'Complete business verification process',
          'This may take 1-3 business days'
        ];
      case 'content_blocked':
        return [
          'Some of your Instagram content violates Meta policies',
          'Review your content in Instagram Business Suite',
          'Remove or edit flagged content',
          'Wait 24-48 hours before trying again'
        ];
      case 'rate_limit':
      case 'wait':
        return [
          'Instagram API rate limit reached',
          'Please wait 1 hour before trying again',
          'This is a temporary limit imposed by Instagram'
        ];
      default:
        return [
          'Try refreshing the page',
          'If the problem persists, contact support'
        ];
    }
  };

  const severity = getErrorSeverity(error.error_code);
  const steps = getActionSteps(error.error_action);
  const icon = getErrorIcon(error.error_action);

  return (
    <Alert 
      severity={severity} 
      icon={icon}
      sx={{ 
        mb: 2,
        '& .MuiAlert-message': {
          width: '100%'
        }
      }}
    >
      <AlertTitle>
        Instagram Integration Error
        {error.error_code && (
          <Typography 
            variant="caption" 
            sx={{ ml: 1, opacity: 0.7 }}
          >
            (Code: {error.error_code})
          </Typography>
        )}
      </AlertTitle>
      
      <Typography variant="body2" sx={{ mb: 2 }}>
        {error.error}
      </Typography>

      {steps.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            How to fix this:
          </Typography>
          <List dense>
            {steps.map((step, index) => (
              <ListItem key={index} sx={{ py: 0.5, pl: 0 }}>
                <ListItemText 
                  primary={`${index + 1}. ${step}`}
                  primaryTypographyProps={{
                    variant: 'body2'
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      <Stack direction="row" spacing={1}>
        {(['reauth', 'refresh_token'].includes(error.error_action)) && onReconnect && (
          <Button 
            variant="contained" 
            size="small"
            onClick={onReconnect}
            startIcon={<AccountCircle />}
          >
            Reconnect Instagram
          </Button>
        )}
        
        {(['rate_limit', 'wait', 'retry'].includes(error.error_action)) && onRetry && (
          <Button 
            variant="outlined" 
            size="small"
            onClick={onRetry}
            startIcon={<Refresh />}
          >
            Try Again
          </Button>
        )}

        {error.error_action === 'convert_account' && (
          <Button 
            variant="outlined" 
            size="small"
            href="https://business.facebook.com/overview"
            target="_blank"
            rel="noopener noreferrer"
            startIcon={<Business />}
          >
            Open Business Manager
          </Button>
        )}
      </Stack>
    </Alert>
  );
};

export default InstagramErrorHandler;