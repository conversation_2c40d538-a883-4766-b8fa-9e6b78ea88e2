import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>,
  Legend
} from 'recharts';
import { Box, Typography, Paper } from '@mui/material';
import ChartContainer from '../common/ChartContainer';

interface CorrelationData {
  dimension: string;
  lag: number;
  correlation: number;
}

interface CorrelationHeatmapProps {
  correlationData: Record<string, Record<string, number>>;
  title?: string;
}

// Interface for the tooltip props
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: CorrelationData;
    [key: string]: unknown;
  }>;
  label?: string;
}

/**
 * Component for displaying correlation data as a heatmap
 */
export const CorrelationHeatmap: React.FC<CorrelationHeatmapProps> = ({ 
  correlationData,
  title = 'Correlation Heatmap'
}) => {
  // Transform the correlation data into a format suitable for the chart
  const transformData = (): CorrelationData[] => {
    const data: CorrelationData[] = [];
    
    Object.entries(correlationData).forEach(([dimension, lagValues]) => {
      Object.entries(lagValues).forEach(([lag, correlation]) => {
        data.push({
          dimension,
          lag: parseInt(lag, 10),
          correlation
        });
      });
    });
    
    return data;
  };
  
  const data = transformData();
  
  // Generate color based on correlation value
  const getColor = (correlation: number) => {
    // Red for negative correlations, blue for positive
    if (correlation < 0) {
      const intensity = Math.min(255, Math.round(Math.abs(correlation) * 255));
      return `rgb(${intensity}, 0, 0)`;
    } else {
      const intensity = Math.min(255, Math.round(correlation * 255));
      return `rgb(0, 0, ${intensity})`;
    }
  };
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Paper elevation={3} sx={{ p: 1, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
          <Typography variant="body2"><strong>Dimension:</strong> {data.dimension}</Typography>
          <Typography variant="body2"><strong>Lag:</strong> {data.lag} day{data.lag !== 1 ? 's' : ''}</Typography>
          <Typography variant="body2">
            <strong>Correlation:</strong> {data.correlation.toFixed(2)}
          </Typography>
        </Paper>
      );
    }
    
    return null;
  };
  
  return (
    <Box sx={{ width: '100%', height: 400, p: 2 }}>
      <Typography variant="h6" gutterBottom>{title}</Typography>
      <ChartContainer width="100%" height="90%">
        <ScatterChart
          margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
        >
          <XAxis 
            type="number" 
            dataKey="lag" 
            name="Lag (days)" 
            domain={[0, 'dataMax']}
            label={{ value: 'Lag (days)', position: 'insideBottom', offset: -10 }}
          />
          <YAxis 
            type="category" 
            dataKey="dimension" 
            name="Dimension"
            label={{ value: 'Dimension', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Scatter name="Correlation" data={data} fill="#8884d8">
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={getColor(entry.correlation)}
                r={Math.abs(entry.correlation) * 20 + 5} // Size based on correlation strength
              />
            ))}
          </Scatter>
        </ScatterChart>
      </ChartContainer>
    </Box>
  );
}; 