import React from 'react';
import { Card, CardContent, Typography, Box, Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface KeywordsCardProps {
  title: string;
  keywords?: string[];
}

const KeywordsCard: React.FC<KeywordsCardProps> = ({ title, keywords }) => {
  const { t } = useTranslation();
  const keywordList = Array.isArray(keywords) ? keywords : [];

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom component="div">
          {title}
        </Typography>
        {keywordList.length > 0 ? (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {keywordList.map((keyword, index) => (
              <Chip key={index} label={keyword} size="small" />
            ))}
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary">
            {t('common.noDataAvailable', 'No keywords available.')}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default KeywordsCard; 