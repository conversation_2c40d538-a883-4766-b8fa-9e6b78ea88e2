import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone, timedelta
import time

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .rate_limiter import RateLimiterMiddleware
from .validation import RequestValidationMiddleware
from .cost_control import CostControlMiddleware
from models.security import SecurityEvent, SecurityEventType, ThreatLevel, SecurityConfig
from utils.security_utils import (
    generate_trace_id, 
    extract_client_ip, 
    detect_attack_patterns,
    generate_request_fingerprint
)

logger = logging.getLogger(__name__)

class SecurityGatewayMiddleware(BaseHTTPMiddleware):
    """
    Central security gateway that coordinates all security middleware components
    """
    
    def __init__(self, app, config: Optional[SecurityConfig] = None, security_service=None):
        super().__init__(app)
        self.config = config or SecurityConfig()
        self.enabled = True
        self.security_service = security_service
        
        # Initialize sub-middleware components
        self.rate_limiter = None
        self.validator = None
        self.cost_controller = None
        
        try:
            if self.config.rate_limiting_enabled:
                self.rate_limiter = RateLimiterMiddleware(app)
                self.rate_limiter.enabled = True
                
            if self.config.validation_enabled:
                self.validator = RequestValidationMiddleware(app)
                self.validator.enabled = True
                
            if self.config.cost_control_enabled:
                self.cost_controller = CostControlMiddleware(app)
                self.cost_controller.enabled = True
        except ImportError as e:
            logger.error(f"Failed to initialize security middleware components: {e}")
            self.enabled = False
        
        # Security event tracking (fallback to memory if no service)
        self.security_events: List[SecurityEvent] = []
        self.threat_patterns = {
            "brute_force": self._detect_brute_force,
            "bot_activity": self._detect_bot_activity,
            "anomalous_behavior": self._detect_anomalous_behavior
        }
        
    def _should_bypass_security(self, request: Request) -> bool:
        """Check if request should bypass all security checks"""
        # Health check endpoints
        if request.url.path in ["/health", "/api/health", "/"]:
            return True
            
        # Internal service requests
        if request.headers.get("x-internal-service") == "true":
            return True
            
        return False
    
    def _detect_brute_force(self, request: Request) -> Optional[SecurityEvent]:
        """Detect brute force attack patterns"""
        # This would be more sophisticated in production
        client_ip = extract_client_ip(request)
        
        # Check for rapid authentication attempts
        if "/auth" in request.url.path and request.method == "POST":
            # In production, you'd track failed attempts in database/cache
            # For now, just detect suspicious patterns in headers/requests
            user_agent = request.headers.get("user-agent", "")
            if len(user_agent) < 10 or "bot" in user_agent.lower():
                return SecurityEvent(
                    event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                    ip_address=client_ip,
                    user_agent=user_agent,
                    endpoint=request.url.path,
                    method=request.method,
                    threat_level=ThreatLevel.MEDIUM,
                    details={"pattern": "potential_brute_force", "user_agent": user_agent},
                    trace_id=generate_trace_id()
                )
        
        return None
    
    def _detect_bot_activity(self, request: Request) -> Optional[SecurityEvent]:
        """Detect automated bot activity"""
        client_ip = extract_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # Check for bot patterns
        bot_indicators = [
            "bot", "crawler", "spider", "scraper", "curl", "wget",
            "python-requests", "java", "go-http-client"
        ]
        
        if any(indicator in user_agent.lower() for indicator in bot_indicators):
            return SecurityEvent(
                event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                ip_address=client_ip,
                user_agent=user_agent,
                endpoint=request.url.path,
                method=request.method,
                threat_level=ThreatLevel.LOW,
                details={"pattern": "bot_activity", "indicators": bot_indicators},
                trace_id=generate_trace_id()
            )
        
        return None
    
    def _detect_anomalous_behavior(self, request: Request) -> Optional[SecurityEvent]:
        """Detect anomalous request patterns"""
        client_ip = extract_client_ip(request)
        
        # Check for unusual request patterns
        anomalies = []
        
        # Unusual query parameters
        if len(request.query_params) > 20:
            anomalies.append("excessive_query_params")
        
        # Unusual headers
        if len(request.headers) > 30:
            anomalies.append("excessive_headers")
        
        # Unusual path patterns
        if len(request.url.path) > 500:
            anomalies.append("excessive_path_length")
        
        # Check for attack patterns in request
        attack_patterns = detect_attack_patterns(request)
        if attack_patterns:
            anomalies.extend(attack_patterns)
        
        if anomalies:
            threat_level = ThreatLevel.HIGH if len(anomalies) > 2 else ThreatLevel.MEDIUM
            return SecurityEvent(
                event_type=SecurityEventType.MALICIOUS_REQUEST,
                ip_address=client_ip,
                user_agent=request.headers.get("user-agent"),
                endpoint=request.url.path,
                method=request.method,
                threat_level=threat_level,
                details={"anomalies": anomalies, "attack_patterns": attack_patterns},
                trace_id=generate_trace_id()
            )
        
        return None
    
    def _analyze_security_threats(self, request: Request) -> List[SecurityEvent]:
        """Analyze request for various security threats"""
        if not self.config.threat_detection_enabled:
            return []
        
        threats = []
        
        # Run all threat detection patterns
        for pattern_name, detector in self.threat_patterns.items():
            try:
                threat = detector(request)
                if threat:
                    threats.append(threat)
            except Exception as e:
                logger.error(f"Error in threat detection pattern {pattern_name}: {e}")
        
        return threats
    
    def _add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        if not self.config.security_headers_enabled:
            return response
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # Rate limiting info (if available)
        if hasattr(response, 'headers') and "X-RateLimit-Limit" not in response.headers:
            response.headers["X-Security-Gateway"] = "active"
        
        return response
    
    async def _log_security_event(self, event: SecurityEvent, request: Optional[Request] = None):
        """Log security event for monitoring with database persistence"""
        # Add to memory as fallback
        self.security_events.append(event)
        
        # Try to get SecurityService from app state if available
        security_service = None
        if request and hasattr(request.app, 'state') and hasattr(request.app.state, 'security_service'):
            security_service = request.app.state.security_service
        elif self.security_service:
            security_service = self.security_service
        
        # Persist to database if service available
        if security_service:
            try:
                await security_service.log_security_event(event)
            except Exception as e:
                logger.error(f"Failed to persist security event to database: {e}")
        
        # Log to console for immediate visibility
        logger.warning(f"Security Event: {event.event_type.value} from {event.ip_address} - {event.details}")
        
        # Keep only recent events in memory (prevent memory leaks)
        if len(self.security_events) > 1000:
            self.security_events = self.security_events[-500:]
        
        # Log based on threat level
        if event.threat_level == ThreatLevel.CRITICAL:
            logger.critical(f"CRITICAL SECURITY EVENT: {event.event_type} from {event.ip_address}")
        elif event.threat_level == ThreatLevel.HIGH:
            logger.error(f"HIGH SECURITY EVENT: {event.event_type} from {event.ip_address}")
        elif event.threat_level == ThreatLevel.MEDIUM:
            logger.warning(f"MEDIUM SECURITY EVENT: {event.event_type} from {event.ip_address}")
        else:
            logger.info(f"SECURITY EVENT: {event.event_type} from {event.ip_address}")
        
        # In production, you would also:
        # - Send to SIEM system
        # - Trigger alerts for high-priority events
        # - Store in security database
        # - Send notifications to security team
        
    def _should_block_request(self, threats: List[SecurityEvent]) -> bool:
        """Determine if request should be blocked based on threats"""
        if not threats:
            return False
        
        # Block immediately for critical threats
        critical_threats = [t for t in threats if t.threat_level == ThreatLevel.CRITICAL]
        if critical_threats:
            return True
        
        # Block for multiple high threats
        high_threats = [t for t in threats if t.threat_level == ThreatLevel.HIGH]
        if len(high_threats) >= 2:
            return True
        
        # Block for many medium threats
        medium_threats = [t for t in threats if t.threat_level == ThreatLevel.MEDIUM]
        if len(medium_threats) >= 3:
            return True
        
        return False
    
    def _create_security_response(self, threats: List[SecurityEvent]) -> JSONResponse:
        """Create appropriate response for security threats"""
        max_threat_level = max((t.threat_level for t in threats), default=ThreatLevel.LOW)
        
        if max_threat_level == ThreatLevel.CRITICAL:
            status_code = 403
            message = "Request blocked due to security policy"
        elif max_threat_level == ThreatLevel.HIGH:
            status_code = 429
            message = "Request rate limited due to suspicious activity"
        else:
            status_code = 400
            message = "Request rejected due to validation failure"
        
        return JSONResponse(
            status_code=status_code,
            content={
                "error": "Security validation failed",
                "message": message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "trace_id": threats[0].trace_id if threats else generate_trace_id()
            }
        )
    
    async def dispatch(self, request: Request, call_next):
        """Main security gateway dispatch"""
        if not self.enabled or self._should_bypass_security(request):
            return await call_next(request)
        
        # Get SecurityService from app state if available
        security_service = getattr(request.app.state, 'security_service', None)
        
        start_time = time.time()
        client_ip = extract_client_ip(request)
        trace_id = generate_trace_id()
        
        # Add trace ID to request state
        request.state.trace_id = trace_id
        request.state.security_start_time = start_time
        
        try:
            # Quick security check before processing
            if not await self._quick_security_check(request):
                # Log security violation if SecurityService is available
                if security_service:
                    try:
                        security_event = SecurityEvent(
                            event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                            user_id=getattr(request.state, 'user_id', None),
                            store_id=getattr(request.state, 'store_id', None),
                            ip_address=client_ip,
                            user_agent=request.headers.get("user-agent"),
                            endpoint=request.url.path,
                            method=request.method,
                            threat_level=ThreatLevel.HIGH,
                            details={"reason": "Failed quick security check"},
                            trace_id=trace_id
                        )
                        await security_service.log_security_event(security_event)
                    except Exception as e:
                        logger.error(f"Failed to log security event: {e}")
                
                return JSONResponse(
                    status_code=403,
                    content={"error": "Request blocked by security policy"}
                )
            
            # Process request through middleware chain
            response = await call_next(request)
            
            # Track successful request
            processing_time = time.time() - start_time
            
            # Different thresholds for different endpoints
            slow_threshold = 5.0  # Default threshold
            if "/instagram/" in str(request.url.path) and "/insights" in str(request.url.path):
                slow_threshold = 120.0  # 2 minutes for Instagram insights (can process up to 1000 posts)
            
            if processing_time > slow_threshold:  # Log slow requests
                logger.warning(f"Slow request detected: {request.method} {request.url.path} took {processing_time:.2f}s")
            
            return response
            
        except Exception as e:
            # Log security-related errors
            logger.error(f"Security gateway error for {request.method} {request.url.path}: {e}")
            
            # Log as security event if SecurityService is available
            if security_service:
                try:
                    security_event = SecurityEvent(
                        event_type=SecurityEventType.VALIDATION_FAILURE,
                        user_id=getattr(request.state, 'user_id', None),
                        store_id=getattr(request.state, 'store_id', None),
                        ip_address=client_ip,
                        user_agent=request.headers.get("user-agent"),
                        endpoint=request.url.path,
                        method=request.method,
                        threat_level=ThreatLevel.MEDIUM,
                        details={"error": str(e), "processing_time": time.time() - start_time},
                        trace_id=trace_id
                    )
                    await security_service.log_security_event(security_event)
                except Exception as log_error:
                    logger.error(f"Failed to log security error event: {log_error}")
            
            # Re-raise the exception
            raise
    
    async def _quick_security_check(self, request: Request) -> bool:
        """Perform quick security validation without full middleware dispatch"""
        try:
            # Check for obvious malicious patterns
            attack_patterns = detect_attack_patterns(request)
            if attack_patterns:
                logger.warning(f"Attack patterns detected: {attack_patterns}")
                return False
            
            # Check for suspicious user agents
            user_agent = request.headers.get("user-agent", "").lower()
            suspicious_agents = ["bot", "crawler", "spider", "scraper"]
            if any(agent in user_agent for agent in suspicious_agents):
                # Allow legitimate bots but log them
                logger.info(f"Bot detected: {user_agent}")
            
            # Check request size
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
                logger.warning(f"Large request detected: {content_length} bytes")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Quick security check error: {e}")
            return True  # Allow on error to avoid blocking legitimate requests
    
    async def _check_rate_limits(self, request: Request) -> bool:
        """Check rate limits without full middleware dispatch"""
        try:
            # This is a simplified check - in production you'd want to
            # integrate more closely with the rate limiter
            client_ip = extract_client_ip(request)
            
            # Basic rate limit check (you'd implement actual logic here)
            # For now, just return True to allow requests
            return True
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            return True  # Allow on error to avoid blocking legitimate requests
    
    async def _validate_request(self, request: Request) -> bool:
        """Validate request without full middleware dispatch"""
        try:
            # Basic validation check
            # Check for obvious malicious patterns
            attack_patterns = detect_attack_patterns(request)
            return len(attack_patterns) == 0
        except Exception as e:
            logger.error(f"Validation check error: {e}")
            return True  # Allow on error
    
    async def _check_cost_limits(self, request: Request) -> bool:
        """Check cost limits without full middleware dispatch"""
        try:
            # Basic cost check
            # For now, just return True
            return True
        except Exception as e:
            logger.error(f"Cost check error: {e}")
            return True  # Allow on error
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get comprehensive security summary"""
        return {
            "status": {
                "enabled": self.enabled,
                "rate_limiting": self.rate_limiter is not None and self.rate_limiter.enabled,
                "validation": self.validator is not None and self.validator.enabled,
                "cost_control": self.cost_controller is not None and self.cost_controller.enabled,
                "threat_detection": self.config.threat_detection_enabled,
                "database_integration": self.security_service is not None
            },
            "recent_events": len(self.security_events),
            "threat_patterns_active": len(self.threat_patterns),
            "service_health": "active" if self.enabled else "disabled"
        } 