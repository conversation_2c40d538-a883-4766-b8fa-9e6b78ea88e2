import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Stack,
  Chip,
  But<PERSON>,
  Fab
} from '@mui/material';
import {
  <PERSON><PERSON> as CookieIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  <PERSON>ne as TuneIcon,
  Campaign as MarketingIcon,
  Speed as SpeedIcon,
  KeyboardArrowUp as ArrowUpIcon,
  Settings as SettingsIcon,
  Policy as PolicyIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { COOKIE_CONFIG, COOKIE_CATEGORIES, translateDuration } from '../../config/cookies';

const categoryIcons = {
  essential: <SecurityIcon color="error" />,
  functional: <TuneIcon color="primary" />,
  analytics: <AnalyticsIcon color="warning" />,
  performance: <SpeedIcon color="success" />,
  marketing: <MarketingIcon color="secondary" />
};

const categoryColors = {
  essential: 'error',
  functional: 'primary',
  analytics: 'warning',
  performance: 'success',
  marketing: 'secondary'
} as const;

export const CookiePolicyPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Create a wrapper function to match translateDuration's expected signature
  const translateWrapper = (key: string, fallback?: string) => {
    return t(key, { defaultValue: fallback || key });
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const openCookieSettings = () => {
    navigate('/settings');
    // Scroll to cookie management section after navigation
    setTimeout(() => {
      const cookieSection = document.getElementById('cookie-management');
      if (cookieSection) {
        cookieSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  const currentDate = new Date().toLocaleDateString(
    t('language') === 'es' ? 'es-ES' : 'en-US',
    { year: 'numeric', month: 'long', day: 'numeric' }
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Paper elevation={3} sx={{ p: 4, mb: 4, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
        <Box display="flex" alignItems="center" mb={2}>
          <CookieIcon sx={{ fontSize: 40, mr: 2 }} />
          <Box>
            <Typography variant="h3" component="h1" gutterBottom>
              {t('cookiePolicy.title')}
            </Typography>
            <Typography variant="h6" component="h2">
              {t('cookiePolicy.subtitle')}
            </Typography>
          </Box>
        </Box>
        <Typography variant="body1">
          {t('cookiePolicy.lastUpdated', { date: currentDate })}
        </Typography>
        <Typography variant="body2">
          {t('cookiePolicy.effectiveDate')}
        </Typography>
      </Paper>

      {/* Quick Navigation */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Navegación Rápida / Quick Navigation
          </Typography>
          <Stack direction="row" flexWrap="wrap" gap={1}>
            <Button 
              startIcon={<SettingsIcon />}
              variant="contained" 
              onClick={openCookieSettings}
              size="small"
            >
              {t('cookiePolicy.navigation.manageCookies')}
            </Button>
            <Button 
              startIcon={<PolicyIcon />}
              variant="outlined"
              href="https://d-unit.world/privacy-policy/"
              target="_blank"
              size="small"
            >
              {t('cookiePolicy.navigation.viewPrivacyPolicy')}
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Introduction */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.introduction.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.introduction.content')}
          </Typography>
        </CardContent>
      </Card>

      {/* What Are Cookies */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.whatAreCookies.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.whatAreCookies.content')}
          </Typography>
        </CardContent>
      </Card>

      {/* Why We Use Cookies */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.whyWeUseCookies.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.whyWeUseCookies.content')}
          </Typography>
        </CardContent>
      </Card>

      {/* Cookie Categories */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.categories.title')}
          </Typography>
          
          {COOKIE_CATEGORIES.map((category) => (
            <Card key={category.id} variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  {categoryIcons[category.id as keyof typeof categoryIcons]}
                  <Typography variant="h5" component="h3" sx={{ ml: 1 }}>
                    {t(`cookiePolicy.categories.${category.id}.title`)}
                  </Typography>
                  <Chip 
                    label={category.essential ? 'Required' : 'Optional'}
                    color={category.essential ? 'error' : 'default'}
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Box>
                <Typography variant="body1">
                  {t(`cookiePolicy.categories.${category.id}.description`)}
                </Typography>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* Detailed Cookie Inventory */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.cookieInventory.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.cookieInventory.description')}
          </Typography>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.name')}</strong></TableCell>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.purpose')}</strong></TableCell>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.category')}</strong></TableCell>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.duration')}</strong></TableCell>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.type')}</strong></TableCell>
                  <TableCell><strong>{t('cookiePolicy.cookieInventory.tableHeaders.provider')}</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {COOKIE_CONFIG.cookies.map((cookie, index) => (
                  <TableRow key={`${cookie.name}-${index}`}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <code style={{ fontSize: '0.875rem' }}>{cookie.name}</code>
                        {cookie.essential && (
                          <Chip 
                            label="Essential" 
                            color="error" 
                            size="small" 
                            sx={{ ml: 1 }} 
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {t(cookie.purpose, cookie.purpose)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        icon={categoryIcons[cookie.category as keyof typeof categoryIcons]}
                        label={t(`cookies.categories.${cookie.category}.name`, cookie.category)}
                        color={categoryColors[cookie.category as keyof typeof categoryColors]}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {translateDuration(cookie.duration, translateWrapper)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {t(`cookies.types.${cookie.type}`, cookie.type)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {cookie.provider}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Cookie Management */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.management.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.management.content')}
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body1" paragraph>
              {t('cookiePolicy.management.options.browserSettings')}
            </Typography>
            <Typography component="li" variant="body1" paragraph>
              {t('cookiePolicy.management.options.ourSettings')}
            </Typography>
            <Typography component="li" variant="body1" paragraph>
              {t('cookiePolicy.management.options.optOut')}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            startIcon={<SettingsIcon />}
            onClick={openCookieSettings}
            sx={{ mt: 2 }}
          >
            {t('cookiePolicy.navigation.manageCookies')}
          </Button>
        </CardContent>
      </Card>

      {/* Your Rights */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.yourRights.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.yourRights.content')}
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.access')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.rectification')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.erasure')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.portability')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.withdraw')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.yourRights.rights.object')}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Legal Basis */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.legalBasis.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.legalBasis.content')}
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.legalBasis.grounds.consent')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.legalBasis.grounds.legitimate')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.legalBasis.grounds.necessary')}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Third-Party Cookies */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.thirdParty.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.thirdParty.content')}
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.thirdParty.services.meta')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.thirdParty.services.google')}
            </Typography>
            <Typography component="li" variant="body1">
              {t('cookiePolicy.thirdParty.services.aws')}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Regulatory Compliance */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.compliance.title')}
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body1">
              • {t('cookiePolicy.compliance.frameworks.gdpr')}
            </Typography>
            <Typography variant="body1">
              • {t('cookiePolicy.compliance.frameworks.ccpa')}
            </Typography>
            <Typography variant="body1">
              • {t('cookiePolicy.compliance.frameworks.lgpd')}
            </Typography>
            <Typography variant="body1">
              • {t('cookiePolicy.compliance.frameworks.pipeda')}
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Updates */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.updates.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.updates.content')}
          </Typography>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" component="h2" gutterBottom>
            {t('cookiePolicy.contact.title')}
          </Typography>
          <Typography variant="body1" paragraph>
            {t('cookiePolicy.contact.content')}
          </Typography>
          <Typography variant="body1">
            {t('cookiePolicy.contact.email')}
          </Typography>
          <Typography variant="body1">
            {t('cookiePolicy.contact.address')}
          </Typography>
        </CardContent>
      </Card>

      {/* Back to Top Fab */}
      <Fab
        color="primary"
        size="small"
        onClick={scrollToTop}
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 1000
        }}
      >
        <ArrowUpIcon />
      </Fab>
    </Container>
  );
}; 