import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import App from './App';
import Dashboard from './components/Dashboard';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import ForgotPassword from './components/auth/ForgotPassword';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminProtectedRoute from './components/auth/AdminProtectedRoute';
import Settings from './components/settings/Settings';
import { MetaDashboard } from './components/meta/MetaDashboard';
import AdminPortal from './pages/AdminPortal';
import StoreView from './pages/StoreView';
import BackendLogin from './pages/BackendLogin';
import CookiePolicy from './pages/CookiePolicy';

const Router: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public Routes */}
        <Route path="/cookie-policy" element={<CookiePolicy />} />
        
        {/* Auth Routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/backend" element={<BackendLogin />} />
        <Route path="/register" element={<Register />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        
        {/* Admin Routes */}
        <Route path="/admin" element={
          <AdminProtectedRoute>
            <AdminPortal />
          </AdminProtectedRoute>
        } />
        
        {/* Store View for Admins */}
        <Route path="/store/:storeId/overview" element={
          <AdminProtectedRoute>
            <StoreView />
          </AdminProtectedRoute>
        } />
        
        {/* Protected Routes */}
        <Route path="/chat" element={
          <ProtectedRoute>
            <App />
          </ProtectedRoute>
        } />
        
        {/* Redirect root to dashboard to avoid duplicate rendering */}
        <Route path="/" element={
          <ProtectedRoute>
            <Navigate to="/dashboard" replace />
          </ProtectedRoute>
        } />
        
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        
        <Route path="/meta" element={
          <ProtectedRoute>
            <MetaDashboard />
          </ProtectedRoute>
        } />
        
        <Route path="/settings" element={
          <ProtectedRoute>
            <Settings />
          </ProtectedRoute>
        } />
        
        {/* Store-specific routes for admin context-aware navigation */}
        <Route path="/store/:storeId/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
        <Route path="/store/:storeId/meta" element={<ProtectedRoute><MetaDashboard /></ProtectedRoute>} />
        <Route path="/store/:storeId/chat" element={<ProtectedRoute><App /></ProtectedRoute>} />
      </Routes>
    </BrowserRouter>
  );
};

export default Router; 