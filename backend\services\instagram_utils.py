"""
Instagram-specific utilities and error handling
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
import aiohttp
from functools import wraps
import time

logger = logging.getLogger(__name__)

# Instagram-specific error codes
INSTAGRAM_ERROR_CODES = {
    10: {
        "message": "Instagram permission denied. Please re-authenticate and grant necessary permissions.",
        "action": "reauth"
    },
    100: {
        "message": "Invalid Instagram media or user ID. The content may have been deleted.",
        "action": "skip_media"
    },
    190: {
        "message": "Instagram access token has expired. Please reconnect your Instagram account.",
        "action": "refresh_token"
    },
    2500: {
        "message": "This endpoint requires an Instagram Business Account. Please convert your Instagram account to a Business account.",
        "action": "convert_account"
    },
    3: {
        "message": "Instagram API application limit reached. Please try again later.",
        "action": "wait"
    },
    4: {
        "message": "Instagram API user request limit reached. Please wait before making more requests.",
        "action": "rate_limit"
    },
    17: {
        "message": "Instagram API rate limit exceeded. Please wait before making more requests.",
        "action": "rate_limit"
    },
    24: {
        "message": "This Instagram account requires business verification to access insights.",
        "action": "verify_business"
    },
    368: {
        "message": "Instagram content has been blocked due to policy violations.",
        "action": "content_blocked"
    }
}

# Instagram-specific permissions
INSTAGRAM_PERMISSIONS = {
    "basic": ["instagram_basic"],
    "insights": ["instagram_manage_insights", "instagram_basic"],
    "comments": ["instagram_manage_comments", "instagram_basic"],
    "content": ["instagram_content_publish", "instagram_basic"],
    "shopping": ["instagram_shopping_tag_products", "instagram_basic"]
}

class InstagramAPIError(Exception):
    """Custom exception for Instagram API errors"""
    def __init__(self, code: int, message: str, action: str = "retry"):
        self.code = code
        self.message = message
        self.action = action
        super().__init__(self.message)

def handle_instagram_error(error: Dict[str, Any]) -> InstagramAPIError:
    """
    Convert Instagram API error response to custom exception
    """
    error_code = error.get("code", 0)
    error_info = INSTAGRAM_ERROR_CODES.get(
        error_code, 
        {
            "message": error.get("message", "Unknown Instagram API error"),
            "action": "retry"
        }
    )
    
    return InstagramAPIError(
        code=error_code,
        message=error_info["message"],
        action=error_info["action"]
    )

async def validate_instagram_permissions(
    access_token: str, 
    required_permissions: List[str]
) -> Dict[str, Any]:
    """
    Validate that the user has granted required Instagram permissions
    """
    try:
        async with aiohttp.ClientSession() as session:
            # Get current permissions
            url = "https://graph.facebook.com/v22.0/me/permissions"
            params = {"access_token": access_token}
            
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    error_data = await response.json()
                    error = error_data.get("error", {})
                    
                    # Handle specific error codes more gracefully
                    if error.get("code") == 100:
                        # Invalid user ID - likely using page token instead of user token
                        logger.info("Permissions check failed with code 100 - likely using page-specific token")
                        return {
                            "valid": True,  # Allow to continue
                            "granted": [],  # Unknown permissions
                            "missing": [],  # Can't determine
                            "partial": True,
                            "note": "Unable to verify permissions with page token"
                        }
                    elif error.get("code") == 190 and error.get("error_subcode") == 467:
                        # User logged out
                        logger.warning("User has logged out - token invalid")
                        return {
                            "valid": False,
                            "granted": [],
                            "missing": required_permissions,
                            "error": "User logged out - reconnection required"
                        }
                    
                    raise handle_instagram_error(error)
                
                data = await response.json()
                granted_permissions = {
                    perm["permission"] 
                    for perm in data.get("data", []) 
                    if perm.get("status") == "granted"
                }
                
                missing_permissions = set(required_permissions) - granted_permissions
                
                # Log missing permissions but don't fail validation completely
                if missing_permissions:
                    logger.warning(f"Missing Instagram permissions: {list(missing_permissions)}")
                    logger.info("Continuing with available permissions for basic functionality")
                
                return {
                    "valid": True,  # Allow to continue even with missing permissions
                    "granted": list(granted_permissions),
                    "missing": list(missing_permissions),
                    "partial": len(missing_permissions) > 0
                }
    
    except Exception as e:
        logger.error(f"Error validating Instagram permissions: {str(e)}")
        return {
            "valid": False,
            "granted": [],
            "missing": required_permissions,
            "error": str(e)
        }

async def get_instagram_business_account(page_id: str, access_token: str) -> Optional[str]:
    """
    Get Instagram Business Account ID from Facebook Page
    """
    try:
        async with aiohttp.ClientSession() as session:
            url = f"https://graph.facebook.com/v22.0/{page_id}"
            params = {
                "fields": "instagram_business_account",
                "access_token": access_token
            }
            
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    error_data = await response.json()
                    error = error_data.get("error", {})
                    if error.get("code") == 100:
                        logger.warning(f"Page {page_id} has no Instagram Business Account connected")
                        return None
                    raise handle_instagram_error(error)
                
                data = await response.json()
                instagram_account = data.get("instagram_business_account")
                
                if not instagram_account:
                    return None
                
                return instagram_account.get("id")
    
    except Exception as e:
        logger.error(f"Error getting Instagram Business Account: {str(e)}")
        return None

def retry_on_rate_limit(max_retries: int = 3, initial_backoff: float = 1.0):
    """
    Decorator for retrying Instagram API calls with exponential backoff
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            backoff = initial_backoff
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except InstagramAPIError as e:
                    last_exception = e
                    if e.action not in ["rate_limit", "wait"]:
                        # Don't retry non-rate-limit errors
                        raise
                    
                    if attempt < max_retries - 1:
                        wait_time = backoff * (2 ** attempt)
                        logger.info(f"Rate limit hit, waiting {wait_time}s before retry {attempt + 1}/{max_retries}")
                        await asyncio.sleep(wait_time)
                except Exception as e:
                    # Don't retry unexpected errors
                    raise
            
            # If we've exhausted retries, raise the last exception
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator

async def validate_instagram_token(access_token: str) -> Dict[str, Any]:
    """
    Validate Instagram access token by testing a simple API call
    """
    try:
        async with aiohttp.ClientSession() as session:
            # Test token by making a simple call to /me
            url = "https://graph.facebook.com/v22.0/me"
            params = {
                "access_token": access_token,
                "fields": "id,name"
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "valid": True,
                        "user_id": data.get("id"),
                        "name": data.get("name")
                    }
                else:
                    # Parse error response
                    error_data = await response.json()
                    error = error_data.get("error", {})
                    error_code = error.get("code", response.status)
                    
                    # Check for token expiry (error code 190)
                    if error_code == 190:
                        subcode = error.get("error_subcode")
                        if subcode == 467:
                            return {
                                "valid": False,
                                "error": "User logged out - Meta session invalid",
                                "error_code": 190,
                                "error_subcode": 467
                            }
                        else:
                            return {
                                "valid": False,
                                "error": "Token expired",
                                "error_code": 190
                            }
                    
                    return {
                        "valid": False,
                        "error": error.get("message", "Token validation failed"),
                        "error_code": error_code
                    }
    
    except Exception as e:
        logger.error(f"Error validating Instagram token: {str(e)}")
        return {
            "valid": False,
            "error": str(e)
        }

def transform_instagram_media_to_post(media: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform Instagram media object to match expected post structure
    """
    return {
        "id": media.get("id"),
        "message": media.get("caption", ""),
        "created_time": media.get("timestamp"),
        "type": media.get("media_type", "IMAGE").lower(),
        "permalink": media.get("permalink"),
        "thumbnail": media.get("thumbnail_url") or media.get("media_url"),
        "likes": {
            "summary": {
                "total_count": media.get("like_count", 0)
            }
        },
        "comments": {
            "summary": {
                "total_count": media.get("comments_count", 0)
            }
        },
        "shares": {
            "count": 0  # Instagram doesn't provide share count via API
        },
        "saved": media.get("saved", 0),  # Requires additional permission
        "reach": media.get("reach", 0),
        "impressions": media.get("impressions", 0),
        "engagement": media.get("engagement", 0)
    }

async def get_instagram_media_insights(
    media_id: str, 
    access_token: str,
    metrics: Optional[List[str]] = None,
    use_cache: bool = True
) -> Dict[str, Any]:
    """
    Get insights for a specific Instagram media with caching
    """
    if metrics is None:
        metrics = ["engagement", "impressions", "reach", "saved"]
    
    try:
        # Import here to avoid circular imports
        from services.instagram_cache import InstagramCacheService
        
        # Check cache first if enabled
        if use_cache:
            cache_key = InstagramCacheService.generate_cache_key(
                media_id, "media_insights", {"metrics": ",".join(sorted(metrics))}
            )
            cached_data = await InstagramCacheService.get_cached_data(
                cache_key, "media_insights"
            )
            if cached_data:
                logger.debug(f"Using cached Instagram media insights for {media_id}")
                return cached_data
        
        async with aiohttp.ClientSession() as session:
            url = f"https://graph.facebook.com/v22.0/{media_id}/insights"
            params = {
                "metric": ",".join(metrics),
                "access_token": access_token
            }
            
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    error_data = await response.json()
                    error = error_data.get("error", {})
                    
                    # Handle specific error codes
                    if error.get("code") == 100:
                        # Media doesn't support insights (e.g., too new)
                        return {}
                    
                    raise handle_instagram_error(error)
                
                data = await response.json()
                insights = {}
                
                for metric in data.get("data", []):
                    metric_name = metric.get("name")
                    values = metric.get("values", [])
                    if values:
                        insights[metric_name] = values[0].get("value", 0)
                
                # Cache the results if cache is enabled
                if use_cache:
                    await InstagramCacheService.set_cached_data(
                        cache_key, "media_insights", insights, {"media_id": media_id}
                    )
                
                return insights
    
    except InstagramAPIError:
        raise
    except Exception as e:
        logger.error(f"Error getting Instagram media insights: {str(e)}")
        return {}