import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class CSRFViolationEvent:
    """Represents a CSRF violation event"""
    timestamp: datetime
    user_id: str
    ip_address: str
    endpoint: str
    method: str
    violation_type: str  # 'missing_token', 'invalid_token', 'expired_token', 'double_submit_mismatch'
    user_agent: str
    severity: str  # 'low', 'medium', 'high', 'critical'

@dataclass
class CSRFAttackPattern:
    """Represents a detected CSRF attack pattern"""
    pattern_id: str
    attack_type: str  # 'brute_force', 'distributed', 'systematic', 'replay'
    severity: str
    start_time: datetime
    end_time: datetime
    total_violations: int
    unique_ips: int
    unique_users: int
    affected_endpoints: Set[str]
    confidence_score: float  # 0.0 to 1.0

class CSRFAttackDetector:
    """
    Advanced CSRF attack detection system with rate-based analysis
    and pattern recognition capabilities
    """
    
    def __init__(self, 
                 time_window_minutes: int = 15,
                 violation_threshold: int = 10,
                 ip_threshold: int = 5,
                 user_threshold: int = 3):
        """
        Initialize CSRF attack detector
        
        Args:
            time_window_minutes: Time window for analysis (default: 15 minutes)
            violation_threshold: Total violations to trigger alert
            ip_threshold: Violations per IP to trigger alert
            user_threshold: Violations per user to trigger alert
        """
        self.time_window = timedelta(minutes=time_window_minutes)
        self.violation_threshold = violation_threshold
        self.ip_threshold = ip_threshold
        self.user_threshold = user_threshold
        
        # In-memory storage for violation events (sliding window)
        self.violation_events: deque = deque()
        
        # Rate tracking per IP and user
        self.ip_violations: Dict[str, deque] = defaultdict(deque)
        self.user_violations: Dict[str, deque] = defaultdict(deque)
        
        # Attack patterns cache
        self.detected_patterns: List[CSRFAttackPattern] = []
        
        # Adaptive thresholds
        self.adaptive_thresholds: Dict[str, int] = {}
        
        # Cleanup interval
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()

    async def track_violation(self, 
                            user_id: str, 
                            ip_address: str, 
                            endpoint: str,
                            method: str,
                            violation_type: str,
                            user_agent: str = "") -> None:
        """Track a CSRF violation event"""
        
        # Create violation event
        event = CSRFViolationEvent(
            timestamp=datetime.now(timezone.utc),
            user_id=user_id,
            ip_address=ip_address,
            endpoint=endpoint,
            method=method,
            violation_type=violation_type,
            user_agent=user_agent,
            severity=self._calculate_severity(violation_type, ip_address, user_id)
        )
        
        # Store event
        self.violation_events.append(event)
        self.ip_violations[ip_address].append(event)
        self.user_violations[user_id].append(event)
        
        # Clean up old events
        await self._cleanup_old_events()
        
        # Analyze patterns
        await self._analyze_attack_patterns()
        
        logger.info(f"CSRF violation tracked: {violation_type} from {ip_address} for user {user_id} on {endpoint}")

    async def detect_attack_patterns(self, hours: int = 1) -> List[CSRFAttackPattern]:
        """Detect CSRF attack patterns in the specified time window"""
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        recent_events = [event for event in self.violation_events if event.timestamp > cutoff_time]
        
        patterns = []
        
        # Detect brute force attacks (high frequency from single IP)
        patterns.extend(await self._detect_brute_force_attacks(recent_events))
        
        # Detect distributed attacks (coordinated attacks from multiple IPs)
        patterns.extend(await self._detect_distributed_attacks(recent_events))
        
        # Detect systematic attacks (targeting specific endpoints)
        patterns.extend(await self._detect_systematic_attacks(recent_events))
        
        # Update detected patterns cache
        self.detected_patterns = patterns
        
        return patterns

    async def should_escalate_response(self, ip_address: str, user_id: str) -> Tuple[bool, str]:
        """Determine if response should be escalated based on violation patterns"""
        
        # Check IP-based violations
        ip_count = len([e for e in self.ip_violations[ip_address] 
                       if e.timestamp > datetime.now(timezone.utc) - self.time_window])
        
        # Check user-based violations
        user_count = len([e for e in self.user_violations[user_id] 
                         if e.timestamp > datetime.now(timezone.utc) - self.time_window])
        
        # Get adaptive threshold
        adaptive_ip_threshold = self.get_adaptive_threshold(ip_address)
        
        if ip_count >= adaptive_ip_threshold:
            return True, f"IP {ip_address} exceeded threshold ({ip_count}/{adaptive_ip_threshold})"
        
        if user_count >= self.user_threshold:
            return True, f"User {user_id} exceeded threshold ({user_count}/{self.user_threshold})"
        
        # Check for active attack patterns
        for pattern in self.detected_patterns:
            if pattern.severity in ['high', 'critical']:
                if ip_address in [e.ip_address for e in self.violation_events 
                                if e.timestamp >= pattern.start_time]:
                    return True, f"IP {ip_address} involved in {pattern.attack_type} attack"
        
        return False, "No escalation needed"

    def get_adaptive_threshold(self, ip_address: str) -> int:
        """Get adaptive threshold for an IP address"""
        
        # Base threshold
        base_threshold = self.ip_threshold
        
        # Check if this IP has historical violations
        if ip_address in self.adaptive_thresholds:
            # Lower threshold for repeat offenders
            return max(1, self.adaptive_thresholds[ip_address] // 2)
        
        # Check if this is a new IP with suspicious patterns
        recent_events = [e for e in self.ip_violations[ip_address] 
                        if e.timestamp > datetime.now(timezone.utc) - timedelta(hours=1)]
        
        if len(recent_events) > 0:
            # Calculate violation rate
            violation_rate = len(recent_events) / 60  # violations per minute
            
            if violation_rate > 0.5:  # More than 1 violation every 2 minutes
                # Adaptive threshold - more aggressive for high-rate violations
                adaptive = max(1, base_threshold - int(violation_rate * 2))
                self.adaptive_thresholds[ip_address] = adaptive
                return adaptive
        
        return base_threshold

    async def get_threat_statistics(self) -> Dict:
        """Get comprehensive threat statistics"""
        
        now = datetime.now(timezone.utc)
        last_hour = now - timedelta(hours=1)
        last_24h = now - timedelta(hours=24)
        
        recent_events = [e for e in self.violation_events if e.timestamp > last_hour]
        daily_events = [e for e in self.violation_events if e.timestamp > last_24h]
        
        return {
            "total_violations_1h": len(recent_events),
            "total_violations_24h": len(daily_events),
            "unique_ips_1h": len(set(e.ip_address for e in recent_events)),
            "unique_ips_24h": len(set(e.ip_address for e in daily_events)),
            "unique_users_1h": len(set(e.user_id for e in recent_events)),
            "unique_users_24h": len(set(e.user_id for e in daily_events)),
            "top_violation_types": self._get_top_violation_types(recent_events),
            "top_targeted_endpoints": self._get_top_targeted_endpoints(recent_events),
            "active_attack_patterns": len([p for p in self.detected_patterns 
                                         if p.end_time > last_hour]),
            "high_severity_patterns": len([p for p in self.detected_patterns 
                                         if p.severity in ['high', 'critical'] and p.end_time > last_hour])
        }

    def _calculate_severity(self, violation_type: str, ip_address: str, user_id: str) -> str:
        """Calculate severity of a violation"""
        
        # Base severity by violation type
        base_severity = {
            'missing_token': 'medium',
            'invalid_token': 'high',
            'expired_token': 'low',
            'double_submit_mismatch': 'high'
        }.get(violation_type, 'medium')
        
        # Check recent violations from same IP
        ip_recent = len([e for e in self.ip_violations[ip_address] 
                        if e.timestamp > datetime.now(timezone.utc) - timedelta(minutes=5)])
        
        # Escalate severity based on frequency
        if ip_recent >= 5:
            return 'critical'
        elif ip_recent >= 3:
            return 'high'
        
        return base_severity

    async def _analyze_attack_patterns(self) -> None:
        """Analyze current events for attack patterns"""
        
        # Only analyze if we have sufficient events
        if len(self.violation_events) < 5:
            return
        
        # Detect patterns in real-time
        patterns = await self.detect_attack_patterns(hours=1)
        
        # Log significant patterns
        for pattern in patterns:
            if pattern.severity in ['high', 'critical']:
                logger.warning(f"CSRF attack pattern detected: {pattern.attack_type} "
                             f"(severity: {pattern.severity}, confidence: {pattern.confidence_score:.2f})")

    async def _detect_brute_force_attacks(self, events: List[CSRFViolationEvent]) -> List[CSRFAttackPattern]:
        """Detect brute force attack patterns"""
        
        patterns = []
        ip_counts = defaultdict(list)
        
        # Group events by IP
        for event in events:
            ip_counts[event.ip_address].append(event)
        
        # Check for brute force patterns
        for ip, ip_events in ip_counts.items():
            if len(ip_events) >= self.ip_threshold:
                pattern = CSRFAttackPattern(
                    pattern_id=f"brute_force_{ip}_{int(time.time())}",
                    attack_type="brute_force",
                    severity="high" if len(ip_events) >= self.ip_threshold * 2 else "medium",
                    start_time=min(e.timestamp for e in ip_events),
                    end_time=max(e.timestamp for e in ip_events),
                    total_violations=len(ip_events),
                    unique_ips=1,
                    unique_users=len(set(e.user_id for e in ip_events)),
                    affected_endpoints=set(e.endpoint for e in ip_events),
                    confidence_score=min(1.0, len(ip_events) / (self.ip_threshold * 3))
                )
                patterns.append(pattern)
        
        return patterns

    async def _detect_distributed_attacks(self, events: List[CSRFViolationEvent]) -> List[CSRFAttackPattern]:
        """Detect distributed attack patterns"""
        
        patterns = []
        
        # Check for coordinated attacks from multiple IPs
        unique_ips = set(e.ip_address for e in events)
        
        if len(unique_ips) >= 3 and len(events) >= self.violation_threshold:
            # Check if attacks are coordinated (similar timing/endpoints)
            endpoint_counts = defaultdict(int)
            for event in events:
                endpoint_counts[event.endpoint] += 1
            
            # If majority of attacks target same endpoints, likely coordinated
            max_endpoint_count = max(endpoint_counts.values()) if endpoint_counts else 0
            coordination_ratio = max_endpoint_count / len(events)
            
            if coordination_ratio > 0.6:  # 60% targeting same endpoint
                pattern = CSRFAttackPattern(
                    pattern_id=f"distributed_{int(time.time())}",
                    attack_type="distributed",
                    severity="critical" if len(unique_ips) >= 10 else "high",
                    start_time=min(e.timestamp for e in events),
                    end_time=max(e.timestamp for e in events),
                    total_violations=len(events),
                    unique_ips=len(unique_ips),
                    unique_users=len(set(e.user_id for e in events)),
                    affected_endpoints=set(e.endpoint for e in events),
                    confidence_score=coordination_ratio
                )
                patterns.append(pattern)
        
        return patterns

    async def _detect_systematic_attacks(self, events: List[CSRFViolationEvent]) -> List[CSRFAttackPattern]:
        """Detect systematic attack patterns"""
        
        patterns = []
        endpoint_counts = defaultdict(list)
        
        # Group by endpoint
        for event in events:
            endpoint_counts[event.endpoint].append(event)
        
        # Check for systematic targeting of specific endpoints
        for endpoint, endpoint_events in endpoint_counts.items():
            if len(endpoint_events) >= self.violation_threshold // 2:
                unique_ips = len(set(e.ip_address for e in endpoint_events))
                
                pattern = CSRFAttackPattern(
                    pattern_id=f"systematic_{endpoint.replace('/', '_')}_{int(time.time())}",
                    attack_type="systematic",
                    severity="high" if unique_ips >= 3 else "medium",
                    start_time=min(e.timestamp for e in endpoint_events),
                    end_time=max(e.timestamp for e in endpoint_events),
                    total_violations=len(endpoint_events),
                    unique_ips=unique_ips,
                    unique_users=len(set(e.user_id for e in endpoint_events)),
                    affected_endpoints={endpoint},
                    confidence_score=min(1.0, len(endpoint_events) / self.violation_threshold)
                )
                patterns.append(pattern)
        
        return patterns

    async def _cleanup_old_events(self) -> None:
        """Clean up old events to maintain memory efficiency"""
        
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        cutoff_time = datetime.now(timezone.utc) - self.time_window * 2  # Keep 2x time window
        
        # Clean main events
        self.violation_events = deque([e for e in self.violation_events if e.timestamp > cutoff_time])
        
        # Clean IP-based events
        for ip in list(self.ip_violations.keys()):
            self.ip_violations[ip] = deque([e for e in self.ip_violations[ip] if e.timestamp > cutoff_time])
            if not self.ip_violations[ip]:
                del self.ip_violations[ip]
        
        # Clean user-based events
        for user in list(self.user_violations.keys()):
            self.user_violations[user] = deque([e for e in self.user_violations[user] if e.timestamp > cutoff_time])
            if not self.user_violations[user]:
                del self.user_violations[user]
        
        # Clean old patterns
        self.detected_patterns = [p for p in self.detected_patterns if p.end_time > cutoff_time]
        
        self._last_cleanup = current_time
        logger.debug("CSRF attack detector cleanup completed")

    def _get_top_violation_types(self, events: List[CSRFViolationEvent]) -> Dict[str, int]:
        """Get top violation types from events"""
        counts = defaultdict(int)
        for event in events:
            counts[event.violation_type] += 1
        return dict(sorted(counts.items(), key=lambda x: x[1], reverse=True)[:5])

    def _get_top_targeted_endpoints(self, events: List[CSRFViolationEvent]) -> Dict[str, int]:
        """Get top targeted endpoints from events"""
        counts = defaultdict(int)
        for event in events:
            counts[event.endpoint] += 1
        return dict(sorted(counts.items(), key=lambda x: x[1], reverse=True)[:5])

# Global attack detector instance
csrf_attack_detector = CSRFAttackDetector() 