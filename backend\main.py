"""
D-Unit Backend Main Application

Recent Critical Fixes Implemented:
1. Fixed OpenAI API response handling in SEO service with enhanced null validation
2. Fixed configuration file path resolution in middleware with absolute path handling
3. Fixed database index creation error with proper TTL parameter handling
4. Implemented authentication caching to reduce database calls
5. Added startup validation for all critical systems and dependencies
6. Enhanced error handling and logging throughout the application
7. Added individual error handling for database index creation

All fixes maintain backward compatibility while improving system reliability and performance.
"""

import logging
import logging.config
import os
import ssl
import sys
import traceback
from pathlib import Path
from typing import Optional
from datetime import datetime

# --- Centralized Logging Setup ---
# Get APP_ENV, default to development
APP_ENV = os.getenv('APP_ENV', 'development') 

# Import the configuration dictionary
try:
    from config.logging_config import LOGGING_CONFIG
except ImportError:
    # Fallback basic config if import fails (should not happen in normal operation)
    logging.basicConfig(level=logging.INFO)
    logging.warning("Could not import LOGGING_CONFIG. Falling back to basic logging.")
    LOGGING_CONFIG = None

# Apply the configuration
if LOGGING_CONFIG:
    logging.config.dictConfig(LOGGING_CONFIG)

# Get the root logger (or a specific logger)
logger = logging.getLogger(__name__) # Use the module name
logger.info(f"Application started in {APP_ENV} mode.")
# --- End Centralized Logging Setup ---

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from apscheduler.schedulers.asyncio import AsyncIOScheduler
import subprocess
from contextlib import asynccontextmanager
from fastapi import Request
from config.logging_config import message_cache
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

# Import security middleware
from middleware.security_gateway import SecurityGatewayMiddleware
from middleware.security_headers import SecurityHeadersMiddleware
from middleware.csrf import CSRFMiddleware
from models.security import SecurityConfig

try:
    # Import configuration
    from config.database import mongodb_client, db_main, db_analysis, verify_connections
    from config.database import verify_database_setup
    from config.settings import get_settings
    from services.auth import get_jwt_secret_key

    # Import routers
    from routes.auth import router as auth_router
    from routes.chat import router as chat_router
    from routes.store import router as store_router
    from routes.meta import router as meta_router
    from routes.meta_permissions import router as meta_permissions_router
    from routes.meta_direct import router as meta_direct_router
    from routes.insights import router as insights_router

    from routes.admin import router as admin_router
    from routes.security import router as security_router
    from routes.instagram_api import router as instagram_router

    # Import middleware
    from middleware.cors import setup_cors
    
    # Import scheduled tasks
    from services.scheduled_tasks import meta_sync_task, check_and_refresh_meta_tokens

    # Import security service
    from services.security_service import SecurityService

    # Security configuration
    security_config = SecurityConfig(
        enabled=True,
        rate_limiting_enabled=True,
        validation_enabled=True,
        cost_control_enabled=True,
        security_headers_enabled=True,
        monitoring_enabled=True,
        threat_detection_enabled=True
    )

    def validate_yaml_file(file_path: Path) -> tuple[bool, Optional[str]]:
        """Validate YAML file syntax and return status with error message if any"""
        try:
            import yaml
            with open(file_path, 'r') as f:
                yaml.safe_load(f)
            return True, None
        except yaml.YAMLError as e:
            return False, f"YAML syntax error: {e}"
        except Exception as e:
            return False, f"File error: {e}"

    async def _populate_test_security_data(security_service):
        """Add test data to verify SecurityService collections are working"""
        try:
            from models.security import SecurityEvent, SecurityEventType, ThreatLevel, BudgetAlert
            from utils.security_utils import generate_trace_id
            
            # Add a test security event
            test_event = SecurityEvent(
                event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                user_id="test_user",
                store_id="566", 
                ip_address="127.0.0.1",
                user_agent="Test Agent",
                endpoint="/api/test",
                method="POST",
                threat_level=ThreatLevel.LOW,
                details={"reason": "Test security event during startup"},
                trace_id=generate_trace_id()
            )
            await security_service.log_security_event(test_event)
            
            # Add a test budget alert
            test_alert = BudgetAlert(
                alert_id=f"test_startup_{datetime.now().strftime('%Y%m%d')}",
                store_id="566",
                alert_type="startup_test",
                current_amount=0.01,
                threshold=0.10,
                percentage_used=0.1
            )
            await security_service.log_budget_alert(test_alert)
            
            # Test cost tracking
            await security_service.track_cost(
                store_id="566",
                service="test_service",
                cost=0.001,
                request_count=1
            )
            
            logger.info("✅ Test security data populated successfully")
            
        except Exception as e:
            logger.error(f"Failed to populate test security data: {e}")

    async def startup_validation():
        """Validate all critical systems and configurations on startup"""
        validation_errors = []
        
        try:
            # Validate database connections
            logger.info("Validating database connections...")
            await verify_connections()
            logger.info("✅ Database connections validated")
        except Exception as e:
            validation_errors.append(f"Database connection failed: {e}")
        
        try:
            # Validate configuration files
            logger.info("Validating configuration files...")
            config_files = [
                "backend/config/rate_limits.yaml",
                "backend/config/validation_rules.yaml", 
                "backend/config/cost_limits.yaml"
            ]
            
            for config_file in config_files:
                if not os.path.isabs(config_file):
                    # Get the project root directory (one level up from backend)
                    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    config_path = Path(project_root) / config_file
                else:
                    config_path = Path(config_file)
                    
                if not config_path.exists():
                    validation_errors.append(f"Configuration file not found: {config_path}")
                else:
                    # Validate YAML syntax
                    is_valid, error_msg = validate_yaml_file(config_path)
                    if not is_valid:
                        validation_errors.append(f"Configuration file {config_path.name} has syntax error: {error_msg}")
                    else:
                        logger.info(f"✅ Configuration file validated: {config_path}")
            
        except Exception as e:
            validation_errors.append(f"Configuration validation failed: {e}")
        
        try:
            # Validate OpenAI API key
            if not settings.OPENAI_API_KEY:
                validation_errors.append("OPENAI_API_KEY environment variable not set")
            else:
                logger.info("✅ OpenAI API key configured")
        except Exception as e:
            validation_errors.append(f"OpenAI API key validation failed: {e}")
        
        try:
            # Validate JWT secret key
            jwt_secret = get_jwt_secret_key()
            if not jwt_secret or len(jwt_secret) < 32:
                validation_errors.append("JWT secret key is not properly configured")
            else:
                logger.info("✅ JWT secret key configured")
        except Exception as e:
            validation_errors.append(f"JWT secret key validation failed: {e}")
        
        # Report validation results
        if validation_errors:
            logger.error("🚨 Startup validation failed with the following errors:")
            for error in validation_errors:
                logger.error(f"  - {error}")
            logger.warning("⚠️ Server will continue but may have limited functionality")
        else:
            logger.info("✅ All startup validations passed successfully")
        
        return len(validation_errors) == 0

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # Startup tasks
        logger.info("Starting D-Unit backend server...")
        
        # Run startup validation
        await startup_validation()
        
        # Ensure database indexes
        await ensure_indexes()
        
        # Initialize SecurityService
        logger.info("Initializing SecurityService...")
        try:
            security_service = SecurityService(database=db_analysis)
            await security_service.initialize()
            app.state.security_service = security_service
            logger.info("✅ SecurityService initialized successfully")
            
            # Add some test data to verify collections are working
            await _populate_test_security_data(security_service)
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize SecurityService: {e}")
            # Continue without SecurityService to avoid blocking startup
            app.state.security_service = None
        
        # Initialize and start the background scheduler for meta sales correlation updates
        logger.info("Initializing background scheduler...")
        def run_meta_sales_correlation_update():
            try:
                from scripts.data.update_meta_sales_correlation import main as update_meta_sales
                logger.info("Running meta sales correlation update...")
                update_meta_sales()
                logger.info("Meta sales correlation update completed")
            except Exception as e:
                logger.error(f"Background task failed: {str(e)}")
        
        # Schedule the task to run daily at 2 AM
        scheduler = AsyncIOScheduler()
        scheduler.add_job(
            run_meta_sales_correlation_update,
            trigger="cron",
            hour=2,
            minute=0,
            id="meta_sales_correlation_update",
            name="Meta Sales Correlation Update",
            max_instances=1
        )
        
        try:
            scheduler.start()
            logger.info("Background scheduler started successfully")
        except Exception as e:
            logger.error(f"Failed to start scheduler: {str(e)}")
        
        logger.info("🚀 D-Unit backend server startup completed")
        
        yield
        
        # Shutdown tasks
        logger.info("Shutting down D-Unit backend server...")
        try:
            scheduler.shutdown()
            logger.info("Background scheduler shutdown completed")
        except Exception as e:
            logger.error(f"Error during scheduler shutdown: {str(e)}")
        
        logger.info("✅ D-Unit backend server shutdown completed")

    # Initialize FastAPI app
    app = FastAPI(
        title="D-Unit API",
        description="Secure D-Unit API with comprehensive security controls",
        version="2.0.0",
        lifespan=lifespan
    )
    settings = get_settings()
    
    # Initialize scheduler
    scheduler = AsyncIOScheduler()

    # SecurityService will be initialized during app startup via lifespan
    # Removed duplicate initialization to fix async event loop error

    # Add Security Headers Middleware first (outermost layer)
    app.add_middleware(SecurityHeadersMiddleware)

    # Add CSRF Protection Middleware
    app.add_middleware(CSRFMiddleware)

    # Add Security Gateway Middleware (SecurityService will be injected via app.state during request handling)
    app.add_middleware(SecurityGatewayMiddleware, config=security_config)

    # Use setup_cors function from middleware.cors instead of inline configuration
    # This ensures we use the proper origins from settings.CORS_ORIGINS
    setup_cors(app)

    # Deduplication log cache middleware
    @app.middleware("http")
    async def clear_log_cache(request: Request, call_next):
        # Reset the per-request message cache before handling
        message_cache.set(set())
        response = await call_next(request)
        return response

    # Include routers
    app.include_router(auth_router)
    app.include_router(store_router)
    app.include_router(chat_router)
    app.include_router(meta_router)
    app.include_router(meta_permissions_router)
    app.include_router(meta_direct_router, prefix="/api/meta", tags=["meta_direct"])
    app.include_router(insights_router)
    app.include_router(admin_router) # Register the admin router using the imported name
    app.include_router(security_router, prefix="/api/security", tags=["security"])
    app.include_router(instagram_router)

    @app.get("/")
    async def root():
        return {"message": "D-Unit API is running"}

    async def ensure_indexes():
        """Ensure all required database indexes exist"""
        try:
            # Get required collections
            meta_chat_context = db_analysis["meta_chat_context"]
            
            # Create primary index for meta_chat_context collection
            await meta_chat_context.create_index("store_id", unique=False)
            logger.info("Ensured primary index for meta_chat_context collection")
            
            # Create compound index with store_id and last_updated for efficient querying
            await meta_chat_context.create_index([
                ("store_id", 1),
                ("last_updated", -1)
            ], name="store_time_idx")
            logger.info("Ensured compound index for meta_chat_context collection")
            
            # Security Collections and Indexes
            logger.info("Creating security collections and indexes...")
            
            # Security Events Collection
            security_events = db_analysis["security_events"]
            security_event_indexes = [
                [("timestamp", -1)],  # Time-based queries
                [("event_type", 1)],  # Filter by event type
                [("threat_level", 1)],  # Filter by threat level
                [("ip_address", 1)],  # IP-based queries
                [("store_id", 1)],  # Store-specific queries
                [("user_id", 1)],  # User-specific queries
                [("endpoint", 1)],  # Endpoint-specific queries
                [("timestamp", -1), ("threat_level", 1)],  # Compound index for threat analysis
                [("store_id", 1), ("timestamp", -1)],  # Store-specific time queries
                [("ip_address", 1), ("timestamp", -1)]  # IP-based time queries
            ]
            
            # Create indexes with individual error handling
            for i, index in enumerate(security_event_indexes):
                try:
                    await security_events.create_index(index)
                except Exception as idx_err:
                    logger.warning(f"Failed to create security events index {index}: {idx_err}")
            
            # TTL index for automatic cleanup (90 days) - fixed parameter handling
            try:
                ttl_seconds = 90 * 24 * 60 * 60  # Calculate TTL in seconds
                await security_events.create_index("timestamp", expireAfterSeconds=ttl_seconds)
                logger.info("Created TTL index for security_events collection")
            except Exception as ttl_err:
                logger.warning(f"Failed to create TTL index for security_events: {ttl_err}")
            
            logger.info("Created indexes for security_events collection")
            
            # Cost Tracking Collection
            cost_tracking = db_analysis["cost_tracking"]
            cost_tracking_indexes = [
                [("date", -1)],  # Date-based queries
                [("store_id", 1)],  # Store-specific queries
                [("service", 1)],  # Service-specific queries (openai, meta_api, etc.)
                [("store_id", 1), ("date", -1)],  # Store-specific time queries
                [("service", 1), ("date", -1)],  # Service-specific time queries
                [("store_id", 1), ("service", 1), ("date", -1)]  # Combined queries
            ]
            
            # Create indexes with individual error handling
            for i, index in enumerate(cost_tracking_indexes):
                try:
                    await cost_tracking.create_index(index)
                except Exception as idx_err:
                    logger.warning(f"Failed to create cost tracking index {index}: {idx_err}")
            
            # TTL index for cost data (1 year retention) - fixed parameter handling
            try:
                ttl_seconds = 365 * 24 * 60 * 60  # Calculate TTL in seconds
                await cost_tracking.create_index("date", expireAfterSeconds=ttl_seconds)
                logger.info("Created TTL index for cost_tracking collection")
            except Exception as ttl_err:
                logger.warning(f"Failed to create TTL index for cost_tracking: {ttl_err}")
            
            logger.info("Created indexes for cost_tracking collection")
            
            # Budget Alerts Collection
            budget_alerts = db_analysis["budget_alerts"]
            budget_alert_indexes = [
                [("timestamp", -1)],  # Time-based queries
                [("store_id", 1)],  # Store-specific queries
                [("alert_type", 1)],  # Alert type queries
                [("escalated", 1)],  # Escalation status
                [("store_id", 1), ("timestamp", -1)],  # Store-specific time queries
                [("alert_type", 1), ("timestamp", -1)]  # Alert type time queries
            ]
            
            # Create indexes with individual error handling
            for i, index in enumerate(budget_alert_indexes):
                try:
                    await budget_alerts.create_index(index)
                except Exception as idx_err:
                    logger.warning(f"Failed to create budget alerts index {index}: {idx_err}")
            
            # TTL index for budget alerts (90 days) - fixed parameter handling
            try:
                ttl_seconds = 90 * 24 * 60 * 60  # Calculate TTL in seconds
                await budget_alerts.create_index("timestamp", expireAfterSeconds=ttl_seconds)
                logger.info("Created TTL index for budget_alerts collection")
            except Exception as ttl_err:
                logger.warning(f"Failed to create TTL index for budget_alerts: {ttl_err}")
            
            logger.info("Created indexes for budget_alerts collection")
            
            # Threat Alerts Collection
            threat_alerts = db_analysis["threat_alerts"]
            threat_alert_indexes = [
                [("timestamp", -1)],  # Time-based queries
                [("threat_type", 1)],  # Threat type queries
                [("severity", 1)],  # Severity-based queries
                [("source_ip", 1)],  # IP-based queries
                [("user_id", 1)],  # User-specific queries
                [("auto_resolved", 1)],  # Resolution status
                [("threat_type", 1), ("timestamp", -1)],  # Threat type time queries
                [("severity", 1), ("timestamp", -1)],  # Severity time queries
                [("source_ip", 1), ("timestamp", -1)]  # IP time queries
            ]
            
            # Create indexes with individual error handling
            for i, index in enumerate(threat_alert_indexes):
                try:
                    await threat_alerts.create_index(index)
                except Exception as idx_err:
                    logger.warning(f"Failed to create threat alerts index {index}: {idx_err}")
            
            # TTL index for threat alerts (90 days) - fixed parameter handling
            try:
                ttl_seconds = 90 * 24 * 60 * 60  # Calculate TTL in seconds
                await threat_alerts.create_index("timestamp", expireAfterSeconds=ttl_seconds)
                logger.info("Created TTL index for threat_alerts collection")
            except Exception as ttl_err:
                logger.warning(f"Failed to create TTL index for threat_alerts: {ttl_err}")
            
            logger.info("Created indexes for threat_alerts collection")
            
            # Rate Limit Records Collection
            rate_limit_records = db_analysis["rate_limit_records"]
            rate_limit_indexes = [
                [("timestamp", -1)],  # Time-based queries
                [("ip_address", 1)],  # IP-based queries
                [("user_id", 1)],  # User-specific queries
                [("endpoint", 1)],  # Endpoint-specific queries
                [("violation_type", 1)],  # Violation type queries
                [("ip_address", 1), ("timestamp", -1)],  # IP time queries
                [("user_id", 1), ("timestamp", -1)],  # User time queries
                [("endpoint", 1), ("timestamp", -1)]  # Endpoint time queries
            ]
            
            # Create indexes with individual error handling
            for i, index in enumerate(rate_limit_indexes):
                try:
                    await rate_limit_records.create_index(index)
                except Exception as idx_err:
                    logger.warning(f"Failed to create rate limit records index {index}: {idx_err}")
            
            # TTL index for rate limit records (30 days) - fixed parameter handling
            try:
                ttl_seconds = 30 * 24 * 60 * 60  # Calculate TTL in seconds
                await rate_limit_records.create_index("timestamp", expireAfterSeconds=ttl_seconds)
                logger.info("Created TTL index for rate_limit_records collection")
            except Exception as ttl_err:
                logger.warning(f"Failed to create TTL index for rate_limit_records: {ttl_err}")
            
            logger.info("Created indexes for rate_limit_records collection")
            
            # Text indexes for log searching - with individual error handling
            try:
                await security_events.create_index([("details", "text"), ("message", "text")])
                logger.info("Created text index for security_events collection")
            except Exception as text_err:
                logger.warning(f"Failed to create text index for security_events: {text_err}")
            
            try:
                await threat_alerts.create_index([("description", "text"), ("recommended_action", "text")])
                logger.info("Created text index for threat_alerts collection")
            except Exception as text_err:
                logger.warning(f"Failed to create text index for threat_alerts: {text_err}")
            
            logger.info("All security collections and indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error ensuring indexes: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            # Don't re-raise the exception to prevent startup failure

except ImportError as e:
    logger.critical(f"Critical import error: {str(e)}. Application cannot start.")
    raise

if __name__ == "__main__":
    try:
        import uvicorn

        # Check if SSL certificates exist (only use in development)
        cert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "certs")
        ssl_keyfile = os.path.join(cert_dir, "cert.key")
        ssl_certfile = os.path.join(cert_dir, "cert.crt")

        use_ssl = (APP_ENV == 'development' and
                  os.path.exists(ssl_keyfile) and
                  os.path.exists(ssl_certfile))

        if use_ssl:
            # Create SSL context for development
            ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            ssl_context.load_cert_chain(ssl_certfile, ssl_keyfile)

            config = uvicorn.Config(
                app=app,
                host="127.0.0.1",
                port=8000,
                log_config=LOGGING_CONFIG,
                reload=True if APP_ENV == 'development' else False,
                ssl_keyfile=ssl_keyfile,
                ssl_certfile=ssl_certfile
            )
            protocol = "https"
        else:
            # No SSL for production or when certificates don't exist
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",  # Listen on all interfaces in production
                port=8000,
                log_config=LOGGING_CONFIG,
                reload=True if APP_ENV == 'development' else False
            )
            protocol = "http"

        server = uvicorn.Server(config)
        logger.info(f"Starting Uvicorn server on {protocol}://{config.host}:{config.port}")
        server.run()
    except Exception as e:
        logger.critical(f"Server failed to start: {str(e)}")
        raise

# db_main is ONLY to be used for user authentication (login) purposes. All other data access must use db_analysis or allowed collections.

