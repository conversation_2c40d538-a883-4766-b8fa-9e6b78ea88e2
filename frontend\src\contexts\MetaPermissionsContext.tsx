import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import { MetaAuthService } from '../services/auth';
import { MetaErrorType, MetaError } from '../services/types'; // Import both MetaErrorType enum and MetaError interface
import { logger } from '../utils/logger';

// Define proper TypeScript interfaces for permission maps
interface PermissionMap {
  [key: string]: string[]; // Allow any string key to access permissions
}

// Define mapping between features and required permissions by platform
// eslint-disable-next-line react-refresh/only-export-components
export const FEATURE_PERMISSION_MAP: PermissionMap = {
  // Dashboard widgets
  pageOverview: ['pages_show_list', 'pages_read_engagement'],
  pageEngagement: ['pages_read_engagement'],
  postAnalytics: ['pages_read_engagement'],
  audienceDemographics: ['pages_read_engagement'],
  adMetrics: ['ads_management', 'ads_read', 'business_management'],
  campaignsComparison: ['ads_management', 'ads_read', 'business_management'],
  
  // Instagram features
  instagramBasic: ['instagram_basic'],
  instagramInsights: ['instagram_basic', 'instagram_manage_insights'],
  
  // Other Meta features
  pageMessaging: ['pages_messaging'],
  
  // Feature keys for internal use
  page_impressions: ['pages_read_engagement'],
  page_engagement: ['pages_read_engagement'],
  page_fans: ['pages_read_engagement'],
  page_views: ['pages_read_engagement'],
  posts_analytics: ['pages_read_engagement'],
  audience_demographics: ['pages_read_engagement'],
  ad_metrics: ['ads_management', 'ads_read', 'business_management'],
  campaigns_comparison: ['ads_management', 'ads_read', 'business_management'],
  ai_insights: ['pages_read_engagement', 'instagram_basic', 'instagram_manage_insights'],
  overview: ['pages_show_list', 'pages_read_engagement'],
  
  // Data features
  postData: ['pages_read_engagement'],
  audienceData: ['pages_read_engagement'],
  adPerformance: ['ads_read'],
  instagramData: ['instagram_basic'],
  instagramMetrics: ['instagram_basic', 'instagram_manage_insights']
};

// Platform-specific feature permission map
// eslint-disable-next-line react-refresh/only-export-components
export const PLATFORM_FEATURE_PERMISSION_MAP: {
  facebook: PermissionMap;
  instagram: PermissionMap;
} = {
  facebook: {
    ...FEATURE_PERMISSION_MAP,
    // Facebook-specific overrides go here
  },
  instagram: {
    // Instagram basic features that only require instagram_basic permission
    instagramBasic: ['instagram_basic'],
    page_engagement: ['instagram_basic'], // Page engagement only needs instagram_basic
    
    // Instagram insights features that require both permissions
    instagramInsights: ['instagram_basic', 'instagram_manage_insights'],
    page_impressions: ['instagram_basic', 'instagram_manage_insights'], // Requires insights permission
    page_fans: ['instagram_basic', 'instagram_manage_insights'], // Requires insights permission for follower count
    page_views: ['instagram_basic', 'instagram_manage_insights'], // Correct permissions for page_views
    
    // Other Instagram features requiring both permissions
    overview: ['instagram_basic', 'instagram_manage_insights'],
    posts_analytics: ['instagram_basic', 'instagram_manage_insights'],
    audience_demographics: ['instagram_basic', 'instagram_manage_insights'],
    ad_metrics: ['instagram_basic', 'instagram_manage_insights', 'ads_read', 'business_management'],
    adMetrics: ['instagram_basic', 'instagram_manage_insights', 'ads_read', 'business_management'],
    campaigns_comparison: ['instagram_basic', 'instagram_manage_insights', 'ads_read', 'business_management'],
    campaignsComparison: ['instagram_basic', 'instagram_manage_insights', 'ads_read', 'business_management'],
    ai_insights: ['instagram_basic', 'instagram_manage_insights'],
  }
};

// Complete mapping between snake_case and camelCase feature keys
const FEATURE_KEY_MAP: Record<string, string> = {
  'ad_metrics': 'adMetrics',
  'pageOverview': 'overview',
  'campaigns_comparison': 'campaignsComparison',
  'audience_demographics': 'audienceDemographics',
  'posts_analytics': 'postAnalytics',
  'page_engagement': 'pageEngagement',
  'page_impressions': 'pageImpressions',
  'page_fans': 'pageFans',
  'page_views': 'pageViews',
  'page_views_total': 'pageViews', // Verify this mapping is correct
  'ai_insights': 'aiInsights'
};

// NEW: Mapping from API endpoint names to permission feature keys
const API_ENDPOINT_TO_FEATURE_KEY: Record<string, string> = {
  'page_impressions': 'page_impressions',
  'page_engagement': 'page_engagement',
  'page_fans': 'page_fans',
  'page_views': 'page_views',
  'page_views_total': 'page_views', // Changed from 'page_views_total'
  'posts_engagement': 'page_engagement',
  'follower_count': 'page_fans',
  'reach': 'page_impressions'
};

interface MetaPermissionsContextType {
  permissions: Array<{permission: string, status: string}>;
  isLoading: boolean;
  error: string | null;
  refreshPermissions: (forceRefresh?: boolean) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  getPermissionStatus: (permission: string) => 'granted' | 'declined' | 'unknown';
  getRevokedPermissions: () => string[];
  canAccessFeature: (featureKey: string, platform?: 'facebook' | 'instagram') => { 
    canAccess: boolean; 
    missingPermissions: string[];
    revokedPermissions: string[];
  };
  isInitialized: boolean;
}

// eslint-disable-next-line react-refresh/only-export-components
export const MetaPermissionsContext = createContext<MetaPermissionsContextType | undefined>(undefined);

// --- Checklist Item 1: Define Cache Constants ---
const PERMISSIONS_CACHE_KEY = 'meta_permissions_cache';
const PERMISSIONS_CACHE_TIMESTAMP_KEY = 'meta_permissions_cache_timestamp';
const PERMISSIONS_CACHE_DURATION_MS = 10 * 60 * 1000; // 10 minutes
// --- End Checklist Item 1 ---

export const MetaPermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [permissions, setPermissions] = useState<Array<{permission: string, status: string}>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Track initialization state globally
  const hasInitialized = useRef(false);
  const permissionRefreshTimeout = useRef<NodeJS.Timeout | null>(null);

  // Wrap refreshPermissions in useCallback to fix dependency issues
  const refreshPermissions = useCallback(async (forceRefresh: boolean = false) => {
    // --- Checklist Item 3: Implement Cache Check ---
    if (!forceRefresh) {
      try {
        const timestampStr = localStorage.getItem(PERMISSIONS_CACHE_TIMESTAMP_KEY);
        if (timestampStr) {
          const timestamp = parseInt(timestampStr, 10);
          const cacheAge = Date.now() - timestamp;
          if (cacheAge < PERMISSIONS_CACHE_DURATION_MS) {
            const cachedDataStr = localStorage.getItem(PERMISSIONS_CACHE_KEY);
            if (cachedDataStr) {
              try {
                const cachedPermissionsData = JSON.parse(cachedDataStr);

                setPermissions(cachedPermissionsData);
                setIsInitialized(true); // Ensure initialized state is set
                setIsLoading(false); // Ensure loading state is false
                setError(null); // Clear any previous error
                return; // Skip API call
              } catch (parseError) {
                logger.warn('[MetaPermissions] Failed to parse cached permissions, fetching fresh data.', parseError);
                // Clear potentially corrupted cache
                localStorage.removeItem(PERMISSIONS_CACHE_KEY);
                localStorage.removeItem(PERMISSIONS_CACHE_TIMESTAMP_KEY);
              }
            }
          } else {
            logger.debug('[MetaPermissions] Permissions cache expired, fetching fresh data.');
          }
        }
      } catch (cacheReadError) {
        logger.error('[MetaPermissions] Error reading permissions cache:', cacheReadError);
      }
    }
    // --- End Checklist Item 3 ---
    
    try {
      // Check if user is logged in first by checking access token
      const accessToken = MetaAuthService.getAccessToken();
      if (!accessToken) {
        logger.debug('[MetaPermissions] No access token available, skipping permissions refresh');
        // If no token, ensure state reflects this
        setPermissions([]);
        setIsLoading(false);
        setIsInitialized(true);
        setError(null);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      let permissionsData;
      
      if (forceRefresh) {
        logger.debug('[MetaPermissions] Forcing permissions refresh...');
        const currentPermissions = [...permissions];
        permissionsData = await MetaAuthService.getCurrentPermissions(forceRefresh, currentPermissions);
        logger.debug('[MetaPermissions] Retrieved fresh permissions:', permissionsData);
        
        const changedPermissions = permissionsData.filter(newPerm => {
          const oldPerm = currentPermissions.find(p => p.permission === newPerm.permission);
          return oldPerm && oldPerm.status !== newPerm.status;
        });
        
        if (changedPermissions.length > 0) {
          logger.debug('[MetaPermissions] Detected permissions changed:', changedPermissions);
        }
      } else {
        permissionsData = await MetaAuthService.getCurrentPermissions(forceRefresh);
                  logger.debug('[MetaPermissions] Retrieved permissions:', permissionsData);
      }
      
      // --- Checklist Item 4: Implement Cache Update ---
      try {
        localStorage.setItem(PERMISSIONS_CACHE_KEY, JSON.stringify(permissionsData));
        localStorage.setItem(PERMISSIONS_CACHE_TIMESTAMP_KEY, Date.now().toString());
        logger.debug('[MetaPermissions] Updated permissions cache.');
      } catch (cacheWriteError) {
                  logger.error('[MetaPermissions] Error writing permissions cache:', cacheWriteError);
      }
      // --- End Checklist Item 4 ---
      
      const instagramInsights = permissionsData.find(p => p.permission === 'instagram_manage_insights');
      logger.debug('[MetaPermissions] Instagram Insights permission status:', instagramInsights?.status || 'not found');
      
      setPermissions(permissionsData);
      setIsInitialized(true);
    } catch (err: unknown) {
      logger.error('[MetaPermissions] Error fetching permissions:', err);
      
      // Check if the error is a MetaError with a 'type' property
      if (err && typeof err === 'object' && 'type' in err) {
        const metaError = err as MetaError; // Type assertion to MetaError interface, not MetaErrorType enum
        if (metaError.type === MetaErrorType.RATE_LIMIT) {
          logger.warn('[MetaPermissions] Rate limit error detected.');
          setError("Meta API request limit reached when checking permissions. Some features might be temporarily unavailable. Please try again later.");
        } else {
          // Handle other known MetaError types
          setError(metaError.message || 'An unknown API error occurred while fetching permissions.');
        }
      } else if (err instanceof Error) {
        // Handle generic JavaScript errors
        setError(err.message || 'An unexpected error occurred while fetching permissions.');
      } else {
        // Fallback for unknown error types
        setError('An unknown error occurred while fetching permissions.');
      }
      // Ensure initialized is set even on error, so UI doesn't hang indefinitely
      if (!isInitialized) {
        setIsInitialized(true);
      }
    } finally {
      setIsLoading(false);
    }
  }, [permissions, isInitialized]); // Added isInitialized dependency

  // Listen for successful Meta login and logout
  useEffect(() => {
    // Prevent multiple initializations
    if (hasInitialized.current) {
      return;
    }
    
    const handleMetaLogin = (event: Event) => {
      // Type assertion for the custom event
      const loginEvent = event as CustomEvent<{ platform: string; accessToken: string }>;
      logger.debug('[MetaPermissions] Login detected, refreshing permissions...', loginEvent.detail.platform);
      
      // Clear any existing timeout
      if (permissionRefreshTimeout.current) {
        clearTimeout(permissionRefreshTimeout.current);
      }
      
      // We don't refresh immediately to allow UI to update first
      // Use debouncing to prevent multiple rapid refreshes
      permissionRefreshTimeout.current = setTimeout(() => {
        refreshPermissions(true).catch(err => {
          logger.error('[MetaPermissions] Error refreshing permissions after login:', err);
        });
      }, 1000); // Increased from 500ms to 1000ms for better debouncing
    };
    
    const handleMetaLogout = () => {
      logger.debug('[MetaPermissions] Logout detected, clearing permissions state and cache');
      setPermissions([]);
      setIsInitialized(true);
      setIsLoading(false);
      setError(null);
      // --- Checklist Item 6: Clear Cache on Logout ---
      try {
        localStorage.removeItem(PERMISSIONS_CACHE_KEY);
        localStorage.removeItem(PERMISSIONS_CACHE_TIMESTAMP_KEY);
      } catch (cacheClearError) {
        logger.error('[MetaPermissions] Error clearing permissions cache on logout:', cacheClearError);
      }
      // --- End Checklist Item 6 ---
    };
    
    // Check if we have a valid token at startup
    const checkInitialToken = async () => {
      try {
        hasInitialized.current = true;
        setIsLoading(true);
        
        // If we have a token, refresh permissions
        if (MetaAuthService.getAccessToken()) {
          await refreshPermissions(false);
          logger.debug('[MetaPermissions] Initial permissions loaded');
        } else {
                      logger.debug('[MetaPermissions] No access token at startup, skipping permission load');
          // Still mark as initialized even if no token
          setIsInitialized(true);
          setIsLoading(false);
        }
      } catch (err) {
        logger.error('[MetaPermissions] Error loading initial permissions:', err);
        // Mark as initialized even on error to prevent blocking the UI
        setIsInitialized(true);
        setIsLoading(false);
      }
    };
    
    // Register event listeners
    document.addEventListener('meta-login-success', handleMetaLogin);
    document.addEventListener('meta-logout', handleMetaLogout);
    
    // Initial check
    if (!hasInitialized.current) {
      checkInitialToken().catch(err => {
        logger.error('[MetaPermissions] Unhandled error during initial token check:', err);
        // Optionally set an error state here if needed
      });
    }
    
    // Cleanup
    return () => {
      document.removeEventListener('meta-login-success', handleMetaLogin);
      document.removeEventListener('meta-logout', handleMetaLogout);
      
      // Clear any pending timeout
      if (permissionRefreshTimeout.current) {
        clearTimeout(permissionRefreshTimeout.current);
      }
    };
  }, [refreshPermissions]); // refreshPermissions is the dependency here

  // Set up periodic permissions check with reduced frequency and debouncing
  useEffect(() => {
    let timerId: NodeJS.Timeout;
    let isActive = true;
    let lastPermissionCheck = Date.now();
    const MIN_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes between checks
    
    // Debounced refresh function to prevent multiple rapid refreshes
    const debouncedRefresh = () => {
      const now = Date.now();
      if (now - lastPermissionCheck < MIN_CHECK_INTERVAL) {
        logger.debug('[MetaPermissions] Skipping check, too soon since last check');
        return;
      }
      
      lastPermissionCheck = now;
      logger.debug('[MetaPermissions] Performing periodic permission check');
      
      refreshPermissions(false).catch(err => {
                  logger.error('[MetaPermissions] Error during periodic permission check:', err);
      });
    };
    
    // Only start periodic checks if we have permissions and are initialized
    if (permissions.length > 0 && isInitialized) {
      logger.debug('[MetaPermissions] Setting up periodic permission checks');
      
      // Check every 15 minutes
      timerId = setInterval(debouncedRefresh, 15 * 60 * 1000);
      
      // First check after 2 minutes
      setTimeout(() => {
        if (isActive) {
          debouncedRefresh();
        }
      }, 2 * 60 * 1000);
    }
    
    return () => {
      isActive = false;
      if (timerId) {
        clearInterval(timerId);
      }
    }
  }, [permissions.length, isInitialized, refreshPermissions]); // Include refreshPermissions in the dependency array

  // Check if a specific permission is granted
  const hasPermission = (permission: string): boolean => {
    const foundPermission = permissions.find(p => p.permission === permission);
    return foundPermission?.status === 'granted';
  };

  // Get permission status
  const getPermissionStatus = (permission: string): 'granted' | 'declined' | 'unknown' => {
    const foundPermission = permissions.find(p => p.permission === permission);
    if (!foundPermission) return 'unknown';
    return foundPermission.status as 'granted' | 'declined';
  };

  // Get list of explicitly revoked permissions
  const getRevokedPermissions = (): string[] => {
    return permissions
      .filter(p => p.status === 'declined')
      .map(p => p.permission);
  };

  // Check if user can access a specific feature based on permissions
  const canAccessFeature = (featureKey: string, platform: 'facebook' | 'instagram' = 'facebook') => {
    // Try to find the key directly in the map, or check if there's an API endpoint mapping
    const apiFeatureKey = API_ENDPOINT_TO_FEATURE_KEY[featureKey] || featureKey;
    
    // First, normalize the feature key using our mapping
    const normalizedKey = FEATURE_KEY_MAP[apiFeatureKey] || apiFeatureKey;
    
    // Get the permission map based on platform
    const permissionMap = platform === 'instagram' 
      ? PLATFORM_FEATURE_PERMISSION_MAP.instagram
      : PLATFORM_FEATURE_PERMISSION_MAP.facebook;
    
    // Get the required permissions
    const requiredPermissions = permissionMap[apiFeatureKey] || permissionMap[normalizedKey];
    
    if (!requiredPermissions) {
      logger.warn(`[MetaPermissions] No permission requirements found for feature: ${featureKey}`);
      return { canAccess: true, missingPermissions: [], revokedPermissions: [] };
    }
    
    // Check each required permission
    const missingPermissions = requiredPermissions.filter(permission => {
      const status = getPermissionStatus(permission);
      return status !== 'granted';
    });
    
    // Check which missing permissions were explicitly declined
    const revokedPermissions = missingPermissions.filter(permission => {
      const status = getPermissionStatus(permission);
      return status === 'declined';
    });
    
    return {
      canAccess: missingPermissions.length === 0,
      missingPermissions,
      revokedPermissions
    };
  };

  const contextValue = {
    permissions,
    isLoading,
    error,
    refreshPermissions,
    hasPermission,
    getPermissionStatus,
    getRevokedPermissions,
    canAccessFeature,
    isInitialized
  };

  return (
    <MetaPermissionsContext.Provider value={contextValue}>
      {children}
    </MetaPermissionsContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useMetaPermissions = () => {
  const context = useContext(MetaPermissionsContext);
  if (context === undefined) {
    throw new Error('useMetaPermissions must be used within a MetaPermissionsProvider');
  }
  return context;
}; 