import logging
import json
import uuid
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple
import openai
from scipy import stats
from motor.motor_asyncio import AsyncIOMotorDatabase
import os

# Check if statsmodels is available
STATSMODELS_AVAILABLE = False
try:
    # We'll only check if it's importable, but won't import it globally
    import statsmodels
    STATSMODELS_AVAILABLE = True
except ImportError:
    # Log a warning but continue - we'll handle this in the specific methods
    logging.getLogger(__name__).warning(
        "statsmodels package is not installed. Forecasting functionality will be limited."
    )

from config.database import db_analysis
from config.settings import get_settings
from models.insights import (
    MetaInsight, 
    ContentInsight, 
    CommentInsight, 
    EngagementInsight,
    AudienceInsight,
    CorrelationInsight,
    AdvancedCorrelationInsight,
    ForecastInsight
)

# Import security service for cost tracking
from services.security_service import SecurityService

# Setup
logger = logging.getLogger(__name__)
settings = get_settings()

# SecurityService will be injected via dependency injection
# Removed global initializations to fix async event loop error

# Initialize OpenAI client
client = openai.Client(api_key=settings.OPENAI_API_KEY)

async def track_insights_cost(store_id: str, operation: str, estimated_tokens: int = 1000) -> float:
    """Track cost for insights operations using the same pricing as chat.py and the default model."""
    try:
        # Use the same pricing as chat.py for the default model
        pricing = {
            "gpt-4.1-mini": {"input": 0.40, "output": 1.60},
            "gpt-4o": {"input": 2.50, "output": 10.00},
            "gpt-4o-mini": {"input": 0.15, "output": 0.60},
            "o4-mini": {"input": 1.10, "output": 4.40},
            "o3-mini": {"input": 1.10, "output": 4.40},
            "o1-mini": {"input": 1.10, "output": 4.40},
            "gpt-4": {"input": 30.00, "output": 60.00},
            "gpt-4-turbo": {"input": 10.00, "output": 30.00},
            "gpt-3.5-turbo": {"input": 0.50, "output": 1.50},
        }
        model = settings.OPENAI_DEFAULT_MODEL
        model_key = model.lower().replace("gpt-", "gpt-").replace("_", "-")
        if model_key not in pricing:
            logger.warning(f"Unknown model {model}, using GPT-4.1 Mini pricing as fallback")
            model_key = "gpt-4.1-mini"
        model_pricing = pricing[model_key]
        # Assume 50% input, 50% output tokens for estimation
        input_tokens = estimated_tokens // 2
        output_tokens = estimated_tokens - input_tokens
        input_cost = (input_tokens / 1_000_000) * model_pricing["input"]
        output_cost = (output_tokens / 1_000_000) * model_pricing["output"]
        estimated_cost = input_cost + output_cost
        # Track the cost using security service
        security_service = SecurityService(database=db_analysis)
        await security_service.track_cost(
            store_id=store_id,
            service="insights_analysis",
            cost=estimated_cost,
            request_count=1
        )
        logger.info(f"Tracked insights cost: ${estimated_cost:.6f} for operation {operation} using model {model}")
        return estimated_cost
    except Exception as e:
        logger.error(f"Failed to track insights cost: {e}")
        return 0.0

async def check_insights_budget(store_id: str, estimated_cost: float = 0.01) -> bool:
    """Check if insights operation is within budget limits"""
    try:
        # Get daily limit from settings (default 10% of total daily limit for insights)
        daily_limit = float(os.getenv("DEFAULT_DAILY_COST_LIMIT", "50.0")) * 0.1  # 10% for insights
        
        current_insights_cost = await track_insights_cost(store_id, "budget_check")
        if current_insights_cost + estimated_cost > daily_limit:
            logger.warning(f"Insights budget exceeded for store {store_id}: {current_insights_cost + estimated_cost:.4f} > {daily_limit:.4f}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Failed to check insights budget: {e}")
        return True  # Allow operation if check fails

class InsightsService:
    """Service for generating AI-powered insights from Meta data"""
    
    @staticmethod
    async def process_posts(
        posts: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        store = None  # Removed store_coro line
        """
        Process post content to generate insights
        
        Args:
            posts: List of Meta posts
            store_id: Store ID
            
        Returns:
            List of content insights
        """
        try:
            # Check if we have enough posts to analyze
            if len(posts) < 3:
                logger.info(f"Not enough posts to analyze for store {store_id}")
                return InsightsService._get_mock_content_insights(posts[0]["id"] if posts else "unknown")
            
            # Extract post IDs
            post_ids = [post["id"] for post in posts]
            
            # Check if we already have insights for these posts
            existing_insights_cursor = db_analysis["meta_insights"].find({
                "store_id": store_id,
                "insight_type": "content_analysis",
                "post_ids": {"$in": post_ids}
            })
            existing_insights = await existing_insights_cursor.to_list(length=None)
            
            if existing_insights:
                # Return existing insights if they're recent (less than 7 days old)
                newest_insight = max(existing_insights, key=lambda x: x.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)))
                insight_age = datetime.now(timezone.utc) - newest_insight.get("timestamp", datetime.min.replace(tzinfo=timezone.utc))
                
                if insight_age.days < 7:
                    logger.info(f"Using existing content insights for store {store_id}")
                    return existing_insights
            
            # Generate new insights
            insights = await InsightsService._generate_content_insights(posts, store_id)
            
            # Store insights in database
            if insights:
                await db_analysis["meta_insights"].insert_many(insights)
                
            return insights
            
        except Exception as e:
            logger.error(f"Error processing posts: {str(e)}")
            return InsightsService._get_mock_content_insights(posts[0]["id"] if posts else "unknown")
    
    @staticmethod
    async def process_comments(
        comments: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Process comments to generate insights
        
        Args:
            comments: List of Meta comments
            store_id: Store ID
            
        Returns:
            List of comment insights
        """
        try:
            # Check if we have enough comments to analyze
            if len(comments) < 5:
                logger.info(f"Not enough comments to analyze for store {store_id}")
                return InsightsService._get_mock_comment_insights(
                    comments[0]["post_id"] if comments else "unknown"
                )
            
            # Extract comment IDs
            comment_ids = [comment["id"] for comment in comments]
            
            # Check if we already have insights for these comments
            existing_insights_cursor = db_analysis["meta_insights"].find({
                "store_id": store_id,
                "insight_type": "comment_analysis",
                "comment_ids": {"$in": comment_ids}
            })
            existing_insights = await existing_insights_cursor.to_list(length=None)
            
            if existing_insights:
                logger.info(f"Found {len(existing_insights)} existing comment insights for store {store_id}")
                return existing_insights
            
            # Generate insights using AI
            insights = await InsightsService._generate_comment_insights(comments, store_id)
            
            # If AI generation fails, use mock data
            if not insights:
                logger.warning(f"Failed to generate comment insights for store {store_id}, using mock data")
                return InsightsService._get_mock_comment_insights(comments[0]["post_id"])
            
            # Store insights in database
            for insight in insights:
                await db_analysis["meta_insights"].insert_one(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error processing comments: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_comment_insights(
                comments[0]["post_id"] if comments else "unknown"
            )
    
    @staticmethod
    async def generate_engagement_insights(
        metrics: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Generate insights from engagement metrics
        
        Args:
            metrics: Engagement metrics
            store_id: Store ID
            
        Returns:
            List of engagement insights
        """
        try:
            # Check if we have the required metrics
            if not metrics.get("page_id"):
                logger.info(f"Missing page_id in engagement metrics for store {store_id}")
                return InsightsService._get_mock_engagement_insights("unknown")
            
            # Check if we already have insights for this page
            existing_insights_cursor = db_analysis["meta_insights"].find({
                "store_id": store_id,
                "insight_type": "engagement",
                "source_data_id": metrics["page_id"]
            })
            existing_insights = await existing_insights_cursor.to_list(length=None)
            
            if existing_insights:
                logger.info(f"Found {len(existing_insights)} existing engagement insights for store {store_id}")
                return existing_insights
            
            # Generate insights using AI
            insights = await InsightsService._generate_engagement_insights(metrics, store_id)
            
            # If AI generation fails, use mock data
            if not insights:
                logger.warning(f"Failed to generate engagement insights for store {store_id}, using mock data")
                return InsightsService._get_mock_engagement_insights(metrics["page_id"])
            
            # Store insights in database
            for insight in insights:
                await db_analysis["meta_insights"].insert_one(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating engagement insights: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_engagement_insights(
                metrics.get("page_id", "unknown")
            )
    
    @staticmethod
    async def generate_audience_insights(
        followers: Dict[str, Any], 
        demographics: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Generate insights from audience data
        
        Args:
            followers: Follower data
            demographics: Audience demographics
            store_id: Store ID
            
        Returns:
            List of audience insights
        """
        try:
            # Check if we have the required data
            if not followers.get("page_id") or not demographics.get("page_id"):
                logger.info(f"Missing page_id in audience data for store {store_id}")
                return InsightsService._get_mock_audience_insights("unknown")
            
            # Check if we already have insights for this page
            existing_insights_cursor = db_analysis["meta_insights"].find({
                "store_id": store_id,
                "insight_type": "audience",
                "source_data_id": followers["page_id"]
            })
            existing_insights = await existing_insights_cursor.to_list(length=None)
            
            if existing_insights:
                logger.info(f"Found {len(existing_insights)} existing audience insights for store {store_id}")
                return existing_insights
            
            # Generate insights using AI
            insights = await InsightsService._generate_audience_insights(followers, demographics, store_id)
            
            # If AI generation fails, use mock data
            if not insights:
                logger.warning(f"Failed to generate audience insights for store {store_id}, using mock data")
                return InsightsService._get_mock_audience_insights(followers["page_id"])
            
            # Store insights in database
            for insight in insights:
                await db_analysis["meta_insights"].insert_one(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating audience insights: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_audience_insights(
                followers.get("page_id", "unknown")
            )
    
    @staticmethod
    async def generate_ad_insights(
        performance: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Generate insights from ad performance data
        
        Args:
            performance: Ad performance data
            store_id: Store ID
            
        Returns:
            List of ad insights
        """
        try:
            # Check if we have enough data to analyze
            if len(performance) < 2:
                logger.info(f"Not enough ad performance data to analyze for store {store_id}")
                return InsightsService._get_mock_ad_insights(
                    performance[0]["campaign_id"] if performance else "unknown"
                )
            
            # Extract campaign IDs
            campaign_ids = [item["campaign_id"] for item in performance]
            
            # Check if we already have insights for these campaigns
            existing_insights_cursor = db_analysis["meta_insights"].find({
                "store_id": store_id,
                "insight_type": "ad_performance",
                "source_data_id": {"$in": campaign_ids}
            })
            existing_insights = await existing_insights_cursor.to_list(length=None)
            
            if existing_insights:
                logger.info(f"Found {len(existing_insights)} existing ad insights for store {store_id}")
                return existing_insights
            
            # Generate insights using AI
            insights = await InsightsService._generate_ad_insights(performance, store_id)
            
            # If AI generation fails, use mock data
            if not insights:
                logger.warning(f"Failed to generate ad insights for store {store_id}, using mock data")
                return InsightsService._get_mock_ad_insights(performance[0]["campaign_id"])
            
            # Store insights in database
            for insight in insights:
                await db_analysis["meta_insights"].insert_one(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating ad insights: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_ad_insights(
                performance[0]["campaign_id"] if performance else "unknown"
            )
    
    @staticmethod
    async def correlate_posts_with_sales(
        posts: List[Dict[str, Any]], 
        sales: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Correlate posts with sales data
        
        Args:
            posts: List of Meta posts
            sales: Sales data
            store_id: Store ID
            
        Returns:
            List of correlation insights
        """
        try:
            # Check if we have enough data to analyze
            if len(posts) < 3 or not sales:
                logger.info(f"Not enough data to correlate posts with sales for store {store_id}")
                return InsightsService._get_mock_correlation_insights("posts", "sales")
            
            # Generate insights using AI
            insights = await InsightsService._generate_correlation_insights(posts, sales, store_id)
            
            # If AI generation fails, use mock data
            if not insights:
                logger.warning(f"Failed to generate correlation insights for store {store_id}, using mock data")
                return InsightsService._get_mock_correlation_insights("posts", "sales")
            
            # Store insights in database
            for insight in insights:
                await db_analysis["meta_insights"].insert_one(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error correlating posts with sales: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_correlation_insights("posts", "sales")
    
    @staticmethod
    async def get_insights_for_page(
        page_id: str, 
        store_id: str, 
        insight_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get insights for a specific page
        
        Args:
            page_id: Page ID
            store_id: Store ID
            insight_type: Optional insight type filter
            
        Returns:
            List of insights
        """
        try:
            # Build query
            query = {
                "store_id": store_id,
                "source_data_id": page_id
            }
            
            if insight_type:
                query["insight_type"] = insight_type
            
            # Get insights from database
            insights_cursor = db_analysis["meta_insights"].find(query).sort("timestamp", -1)
            insights = await insights_cursor.to_list(length=None)
            
            if not insights:
                logger.info(f"No insights found for page {page_id} in store {store_id}")
                # Return mock data based on insight type
                if insight_type == "content_analysis":
                    return InsightsService._get_mock_content_insights(page_id)
                elif insight_type == "comment_analysis":
                    return InsightsService._get_mock_comment_insights(page_id)
                elif insight_type == "engagement":
                    return InsightsService._get_mock_engagement_insights(page_id)
                elif insight_type == "audience":
                    return InsightsService._get_mock_audience_insights(page_id)
                else:
                    # Return a mix of mock insights
                    return (
                        InsightsService._get_mock_content_insights(page_id) +
                        InsightsService._get_mock_engagement_insights(page_id)
                    )
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting insights for page {page_id}: {str(e)}")
            # Return mock data as fallback
            return InsightsService._get_mock_content_insights(page_id)
    
    @staticmethod
    async def perform_advanced_correlation(
        meta_data: Dict[str, Any],
        store_data: Dict[str, Any],
        store_id: str,
        time_lag: int = 0,
        segment: Optional[str] = None,
        dimensions: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform advanced correlation analysis between Meta and store data
        
        Args:
            meta_data: Meta data including posts, engagement, audience metrics
            store_data: Store data including sales, customers, products
            store_id: Store ID
            time_lag: Time lag in days for correlation analysis
            segment: Segment to analyze (e.g., product category, customer segment)
            dimensions: Dimensions to include in correlation analysis
            
        Returns:
            List of advanced correlation insights
        """
        try:
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Check if we have enough data
            if not meta_data or not store_data:
                logger.info(f"Not enough data for advanced correlation analysis for store {store_id}")
                return []
            
            # Extract Meta metrics
            meta_metrics = {}
            
            # Extract engagement metrics
            if "posts" in meta_data:
                posts = meta_data["posts"]
                post_dates = []
                post_engagement = []
                post_likes = []
                post_comments = []
                post_shares = []
                
                for post in posts:
                    try:
                        created_time = post.get("created_time")
                        if not created_time:
                            continue
                            
                        post_date = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                        likes = post.get("likes", 0)
                        comments = post.get("comments", 0)
                        shares = post.get("shares", 0)
                        engagement = likes + comments + shares
                        
                        post_dates.append(post_date)
                        post_engagement.append(engagement)
                        post_likes.append(likes)
                        post_comments.append(comments)
                        post_shares.append(shares)
                    except (ValueError, AttributeError):
                        continue
                
                meta_metrics["post_dates"] = post_dates
                meta_metrics["post_engagement"] = post_engagement
                meta_metrics["post_likes"] = post_likes
                meta_metrics["post_comments"] = post_comments
                meta_metrics["post_shares"] = post_shares
            
            # Extract audience metrics
            if "audience" in meta_data:
                audience = meta_data["audience"]
                meta_metrics["audience_growth"] = audience.get("growth", [])
                meta_metrics["audience_demographics"] = audience.get("demographics", {})
            
            # Extract store metrics
            store_metrics = {}
            
            # Extract sales data
            if "sales" in store_data:
                sales = store_data["sales"]
                sales_dates = []
                sales_values = []
                
                daily_sales = sales.get("daily_sales", {})
                if daily_sales:
                    for date_str, value in daily_sales.items():
                        try:
                            sales_date = datetime.fromisoformat(date_str)
                            sales_dates.append(sales_date)
                            sales_values.append(value)
                        except ValueError:
                            continue
                
                store_metrics["sales_dates"] = sales_dates
                store_metrics["sales_values"] = sales_values
            
            # Extract customer data
            if "customers" in store_data:
                customers = store_data["customers"]
                store_metrics["new_customers"] = customers.get("new_customers", [])
                store_metrics["customer_retention"] = customers.get("retention", [])
            
            # Extract product data
            if "products" in store_data:
                products = store_data["products"]
                store_metrics["product_views"] = products.get("views", [])
                store_metrics["product_conversions"] = products.get("conversions", [])
            
            # Check if we have enough data for correlation
            if not meta_metrics or not store_metrics:
                logger.info(f"Not enough metrics for advanced correlation analysis for store {store_id}")
                return []
            
            # Perform multi-dimensional correlation analysis
            correlation_results = {}
            
            # Define dimensions to analyze
            if not dimensions:
                dimensions = ["engagement", "likes", "comments", "shares"]
            
            # Create dataframes for analysis
            meta_df = None
            if "post_dates" in meta_metrics and len(meta_metrics["post_dates"]) > 0:
                meta_df = pd.DataFrame({
                    'date': meta_metrics["post_dates"]
                })
                
                for dim in dimensions:
                    if f"post_{dim}" in meta_metrics:
                        meta_df[dim] = meta_metrics[f"post_{dim}"]
                
                meta_df['date'] = pd.to_datetime(meta_df['date'])
                meta_df.set_index('date', inplace=True)
                meta_df = meta_df.resample('D').sum().reset_index()
            
            store_df = None
            if "sales_dates" in store_metrics and len(store_metrics["sales_dates"]) > 0:
                store_df = pd.DataFrame({
                    'date': store_metrics["sales_dates"],
                    'sales': store_metrics["sales_values"]
                })
                
                store_df['date'] = pd.to_datetime(store_df['date'])
                store_df.set_index('date', inplace=True)
                store_df = store_df.resample('D').sum().reset_index()
            
            # Check if we have valid dataframes
            if meta_df is None or store_df is None:
                logger.info(f"Could not create valid dataframes for correlation analysis for store {store_id}")
                return []
            
            # Merge dataframes
            merged_df = pd.merge(meta_df, store_df, on='date', how='outer').fillna(0)
            
            # Apply segmentation if specified
            if segment:
                # This would filter the data based on the segment
                # For example, if segment is "product_category", we would filter by that category
                # This is a placeholder for actual segmentation logic
                pass
            
            # Calculate lagged variables (0-7 days)
            max_lag = 7
            for dim in dimensions:
                if dim in merged_df.columns:
                    for lag in range(max_lag + 1):
                        merged_df[f'{dim}_lag_{lag}'] = merged_df[dim].shift(lag)
            
            # Drop NaN values
            merged_df.dropna(inplace=True)
            
            # Check if we have enough data after merging and dropping NaNs
            if len(merged_df) < 5:
                logger.info(f"Not enough overlapping data for advanced correlation analysis for store {store_id}")
                return []
            
            # Calculate correlations for each dimension and lag
            for dim in dimensions:
                if dim in merged_df.columns:
                    dim_correlations = {}
                    dim_p_values = {}
                    
                    for lag in range(max_lag + 1):
                        lag_col = f'{dim}_lag_{lag}'
                        if lag_col in merged_df.columns:
                            corr, p_value = stats.pearsonr(merged_df[lag_col], merged_df['sales'])
                            dim_correlations[lag] = corr
                            dim_p_values[lag] = p_value
                    
                    # Find best lag for this dimension
                    if dim_correlations:
                        best_lag = max(dim_correlations.items(), key=lambda x: abs(x[1]))[0]
                        best_corr = dim_correlations[best_lag]
                        best_p = dim_p_values[best_lag]
                        
                        correlation_results[dim] = {
                            "best_lag": best_lag,
                            "correlation": best_corr,
                            "p_value": best_p,
                            "all_correlations": dim_correlations,
                            "all_p_values": dim_p_values
                        }
            
            # Generate insights based on correlation results
            insights = []
            
            for dim, result in correlation_results.items():
                best_lag = result["best_lag"]
                best_corr = result["correlation"]
                best_p = result["p_value"]
                
                # Skip insignificant correlations
                if abs(best_corr) < 0.3 or best_p > 0.05:
                    continue
                
                direction = "positive" if best_corr > 0 else "negative"
                strength = "strong" if abs(best_corr) > 0.7 else "moderate"
                lag_text = f"{best_lag} day{'s' if best_lag > 1 else ''}"
                
                # Format dimension name for display
                dim_display = dim.replace('_', ' ').title()
                
                insight_text = f"There is a {strength} {direction} correlation (r={best_corr:.2f}) between {dim_display} and sales with a {lag_text} lag for {store_info.get('name', 'your business')}."
                
                if best_corr > 0:
                    recommendations = [
                        f"Focus on increasing {dim_display.lower()} as it appears to drive sales after {lag_text}",
                        f"Invest more in content that generates high {dim_display.lower()}",
                        f"Plan promotional posts {lag_text} before expected sales periods"
                    ]
                else:
                    recommendations = [
                        f"Investigate why higher {dim_display.lower()} might be negatively correlated with sales",
                        "Ensure social media content is aligned with actual product offerings",
                        f"Focus on conversion-oriented content rather than just {dim_display.lower()}"
                    ]
                
                confidence_score = 0.7 if best_p < 0.01 else 0.6
                
                # Format insight for database
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "advanced_correlation",
                    "insight_text": insight_text,
                    "recommendations": recommendations,
                    "confidence_score": confidence_score,
                    "correlation_metrics": {
                        dim: {
                            "best_lag": best_lag,
                            "correlation": best_corr,
                            "p_value": best_p
                        }
                    },
                    "time_lag": best_lag,
                    "segment": segment,
                    "dimensions": [dim],
                    "source_data_type": "advanced_correlation",
                    "source_data_id": f"{store_id}_meta_sales_advanced_correlation",
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                insights.append(insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error performing advanced correlation analysis: {str(e)}")
            return []
    
    @staticmethod
    async def forecast_metrics(
        metric_type: str,
        historical_data: List[Dict[str, Any]],
        forecast_periods: int = 30,
        store_id: Optional[str] = None,
        confidence_level: float = 0.95
    ) -> List[Dict[str, Any]]:
        """
        Forecast future values for specified metrics
        
        Args:
            metric_type: Type of metric to forecast (e.g., engagement, sales)
            historical_data: Historical data for the metric
            forecast_periods: Number of periods to forecast
            store_id: Store ID
            confidence_level: Confidence level for prediction intervals
            
        Returns:
            List of forecast insights
        """
        try:
            # Get store information for context
            store_info = {}
            if store_id:
                store_info = await InsightsService._get_store_info(store_id)
            
            # Check if we have enough data
            if not historical_data or len(historical_data) < 10:
                logger.info(f"Not enough historical data for forecasting {metric_type}")
                return []
            
            # Extract time series data
            dates = []
            values = []
            
            for data_point in historical_data:
                try:
                    date_str = data_point.get("date")
                    value = data_point.get("value")
                    
                    if not date_str or value is None:
                        continue
                    
                    date = datetime.fromisoformat(date_str.replace('Z', '+00:00') if 'Z' in date_str else date_str)
                    dates.append(date)
                    values.append(float(value))
                except (ValueError, AttributeError):
                    continue
            
            # Check if we have enough data after extraction
            if len(dates) < 10:
                logger.info(f"Not enough valid data points for forecasting {metric_type}")
                return []
            
            # Create dataframe
            df = pd.DataFrame({
                'date': dates,
                'value': values
            })
            
            # Sort by date
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Set date as index
            df.set_index('date', inplace=True)
            
            # Determine frequency
            # This is a simple approach - in a real implementation, we would use more sophisticated methods
            date_diffs = df.index.to_series().diff().dropna()
            if len(date_diffs) > 0:
                # Convert to numeric value in days - this avoids attribute access issues
                try:
                    # Try to convert to days directly
                    date_diff_days = date_diffs.dt.total_seconds() / (24 * 3600)
                    median_diff_days = date_diff_days.median()
                except:
                    # Fallback to a simple numeric approach
                    median_diff_days = 1  # Default to daily if conversion fails
                
                if median_diff_days <= 1:
                    freq = 'D'  # Daily
                elif 1 < median_diff_days <= 7:
                    freq = 'W'  # Weekly
                else:
                    freq = 'M'  # Monthly
            else:
                freq = 'D'  # Default to daily
            
            # Resample to regular frequency
            df = df.resample(freq).mean()
            
            # Forward fill missing values - use a different approach to avoid the fillna issue
            df = df.ffill()  # This is equivalent to df.fillna(method='ffill')
            
            # Check if we have enough data after resampling
            if len(df) < 10:
                logger.info(f"Not enough data after resampling for forecasting {metric_type}")
                return []
            
            # Train-test split
            train_size = int(len(df) * 0.8)
            train, test = df.iloc[:train_size], df.iloc[train_size:]
            
            # Check if statsmodels is available before attempting to use it
            if not STATSMODELS_AVAILABLE:
                logger.warning("statsmodels package is not installed. Forecasting functionality is limited.")
                return []
            
            # Only import statsmodels when we know it's available
            try:
                from statsmodels.tsa.arima.model import ARIMA
                from statsmodels.tsa.statespace.sarimax import SARIMAX
            except ImportError:
                logger.error("Failed to import statsmodels components despite availability check")
                return []
            
            # Try different models and select the best one
            models = []
            
            # ARIMA model
            try:
                arima_model = ARIMA(train['value'], order=(1, 1, 1))
                arima_fit = arima_model.fit()
                # Tell Pylance to ignore attribute access warnings
                arima_forecast = arima_fit.forecast(steps=len(test))  # type: ignore[attr-defined]
                arima_mse = np.mean((test['value'] - arima_forecast) ** 2)
                models.append(('ARIMA', arima_fit, arima_mse))
            except Exception as e:
                logger.warning(f"Error fitting ARIMA model: {str(e)}")
            
            # SARIMA model
            try:
                sarima_model = SARIMAX(train['value'], order=(1, 1, 1), seasonal_order=(1, 1, 1, 12))
                sarima_fit = sarima_model.fit(disp=False)
                # Tell Pylance to ignore attribute access warnings
                sarima_forecast = sarima_fit.forecast(steps=len(test))  # type: ignore[attr-defined]
                sarima_mse = np.mean((test['value'] - sarima_forecast) ** 2)
                models.append(('SARIMA', sarima_fit, sarima_mse))
            except Exception as e:
                logger.warning(f"Error fitting SARIMA model: {str(e)}")
            
            # Select best model
            if not models:
                logger.info(f"No models could be fitted for forecasting {metric_type}")
                return []
            
            best_model_name, best_model, best_mse = min(models, key=lambda x: x[2])
            
            # Retrain on full dataset
            if best_model_name == 'ARIMA':
                final_model = ARIMA(df['value'], order=(1, 1, 1))
                final_fit = final_model.fit()
            else:  # SARIMA
                final_model = SARIMAX(df['value'], order=(1, 1, 1), seasonal_order=(1, 1, 1, 12))
                final_fit = final_model.fit(disp=False)
            
            # Generate forecast
            # Tell Pylance to ignore attribute access warnings
            forecast = final_fit.forecast(steps=forecast_periods)  # type: ignore[attr-defined]
            
            # Generate prediction intervals
            from scipy import stats as scipy_stats
            
            alpha = 1 - confidence_level
            z = scipy_stats.norm.ppf(1 - alpha/2)
            
            # Get forecast standard errors
            # Tell Pylance to ignore attribute access warnings
            forecast_errors = np.sqrt(final_fit.forecast_variance(steps=forecast_periods))  # type: ignore[attr-defined]
            
            # Calculate prediction intervals
            # Ensure all values are scalar to avoid attribute access issues
            lower_bounds = np.array([float(f) - z * float(e) for f, e in zip(forecast, forecast_errors)])
            upper_bounds = np.array([float(f) + z * float(e) for f, e in zip(forecast, forecast_errors)])
            
            # Generate future dates
            last_date = df.index[-1]
            if freq == 'D':
                future_dates = [last_date + timedelta(days=i+1) for i in range(forecast_periods)]
            elif freq == 'W':
                future_dates = [last_date + timedelta(weeks=i+1) for i in range(forecast_periods)]
            else:  # 'M'
                future_dates = [last_date + pd.DateOffset(months=i+1) for i in range(forecast_periods)]
            
            # Format forecast values
            forecast_values = []
            confidence_intervals = []
            
            for i, date in enumerate(future_dates):
                forecast_values.append({
                    "date": date.isoformat(),
                    "value": float(forecast[i])
                })
                
                confidence_intervals.append({
                    "date": date.isoformat(),
                    "lower": float(lower_bounds[i]),
                    "upper": float(upper_bounds[i])
                })
            
            # Calculate forecast accuracy
            forecast_accuracy = 1.0 - np.sqrt(best_mse) / df['value'].mean()
            
            # Format insight text
            metric_display = metric_type.replace('_', ' ').title()
            forecast_period_text = f"{forecast_periods} {'days' if freq == 'D' else 'weeks' if freq == 'W' else 'months'}"
            
            # Determine trend
            last_value = df['value'].iloc[-1]
            forecast_end_value = forecast[-1]
            percent_change = ((forecast_end_value - last_value) / last_value) * 100
            
            if percent_change > 5:
                trend = "increasing"
                trend_strength = "significant" if percent_change > 20 else "moderate"
            elif percent_change < -5:
                trend = "decreasing"
                trend_strength = "significant" if percent_change < -20 else "moderate"
            else:
                trend = "stable"
                trend_strength = ""
            
            if trend == "stable":
                insight_text = f"The {metric_display.lower()} for {store_info.get('name', 'your business')} is forecasted to remain stable over the next {forecast_period_text}."
            else:
                insight_text = f"The {metric_display.lower()} for {store_info.get('name', 'your business')} is forecasted to show a {trend_strength} {trend} trend of {abs(percent_change):.1f}% over the next {forecast_period_text}."
            
            # Generate recommendations
            if trend == "increasing":
                recommendations = [
                    f"Prepare for increased {metric_display.lower()} by ensuring adequate resources",
                    "Capitalize on the positive trend with promotional campaigns",
                    "Analyze what's driving the increase to replicate success"
                ]
            elif trend == "decreasing":
                recommendations = [
                    f"Investigate potential causes for the declining {metric_display.lower()}",
                    "Implement engagement strategies to reverse the negative trend",
                    "Consider adjusting your content strategy to better resonate with your audience"
                ]
            else:  # stable
                recommendations = [
                    f"Maintain your current strategy as {metric_display.lower()} is stable",
                    "Experiment with new content types to potentially drive growth",
                    "Monitor for any early signs of change in the trend"
                ]
            
            # Format insight for database
            insight_id = str(uuid.uuid4())
            
            insight = {
                "id": insight_id,
                "store_id": store_id,
                "insight_type": "forecast",
                "insight_text": insight_text,
                "recommendations": recommendations,
                "confidence_score": min(0.9, max(0.5, forecast_accuracy)),
                "metric_name": metric_type,
                "forecast_values": forecast_values,
                "confidence_intervals": confidence_intervals,
                "forecast_accuracy": forecast_accuracy,
                "forecast_period": forecast_period_text,
                "source_data_type": "forecast",
                "source_data_id": f"{store_id}_{metric_type}_forecast",
                "timestamp": datetime.now(timezone.utc),
                "is_mock": False
            }
            
            return [insight]
            
        except Exception as e:
            logger.error(f"Error forecasting metrics: {str(e)}")
            return []
    
    @staticmethod
    async def get_direct_raw_insights(
        page_id: str,
        store_id: str
    ) -> List[Dict[str, Any]]:
        """
        Retrieves raw insights directly from MongoDB for the specified page and store.
        Performs minimal processing. Intended for direct API endpoint use.

        Args:
            page_id: The Meta page ID.
            store_id: The store ID.

        Returns:
            A list of raw insight dictionaries from MongoDB, or an empty list if none found or error.
        """
        logger.info(f"[Service Direct] Fetching raw insights for page {page_id}, store {store_id}")
        try:
            # Simple query to get all insights matching page_id and store_id
            # Note: Using page_id as the source_data_id based on previous findings
            query = {
                "store_id": store_id,
                "source_data_id": page_id
            }
            
            # Execute the query
            # Use motor's to_list() for async fetching
            cursor = db_analysis["meta_insights"].find(query).sort("timestamp", -1)
            insights_list = await cursor.to_list(length=100) # Limit length for safety

            if not insights_list:
                logger.warning(f"[Service Direct] No raw insights found for page {page_id}, store {store_id}")
                return [] # Return empty list if no documents found

            logger.info(f"[Service Direct] Found {len(insights_list)} raw insights for page {page_id}")
            return insights_list

        except Exception as e:
            logger.error(f"[Service Direct] Error fetching raw insights for page {page_id}, store {store_id}: {e}", exc_info=True)
            # In case of error, return an empty list to prevent breaking the endpoint
            # The route handler can decide how to signal this error if needed
            return []
    
    # Private methods for AI insight generation
    
    @staticmethod
    async def _generate_content_insights(
        posts: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate insights from post content using AI"""
        try:
            # Check budget before expensive AI operation
            estimated_tokens = len(str(posts)) // 4  # Rough token estimation
            estimated_cost = (estimated_tokens / 1000) * 0.03
            
            if not await check_insights_budget(store_id, estimated_cost):
                logger.warning(f"Content insights budget exceeded for store {store_id}")
                return InsightsService._get_mock_content_insights(posts[0]["id"] if posts else "unknown")
            
            # Track cost before operation
            await track_insights_cost(store_id, "content_insights", estimated_tokens)
            
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Prepare posts data for analysis
            posts_data = []
            for post in posts:
                post_data = {
                    "id": post.get("id", ""),
                    "message": post.get("message", ""),
                    "created_time": post.get("created_time", ""),
                    "type": post.get("type", ""),
                    "engagement": {
                        "likes": post.get("likes", 0),
                        "comments": post.get("comments", 0),
                        "shares": post.get("shares", 0)
                    }
                }
                posts_data.append(post_data)
            
            # Sort posts by engagement (total of likes, comments, shares)
            posts_data.sort(key=lambda x: sum(x["engagement"].values()), reverse=True)
            
            # Get top 10 posts for analysis
            top_posts = posts_data[:10]
            
            # Create prompt for OpenAI
            prompt = f"""
            You are an expert social media analyst. Analyze these Facebook/Instagram posts for {store_info.get('name', 'a business')} 
            which sells {store_info.get('category', 'products')}. 
            
            Posts data:
            {json.dumps(top_posts, indent=2)}
            
            Generate 3 specific, actionable insights about:
            1. Content patterns that drive engagement
            2. Optimal posting strategies
            3. Topics that resonate with the audience
            
            For each insight, provide:
            - A clear insight statement
            - 2-3 specific recommendations
            - A confidence score (0.0-1.0)
            - A list of relevant topics
            
            Format your response as a JSON object with an "insights" array containing objects with:
            - insight_text: The main insight
            - recommendations: Array of recommendation strings
            - confidence_score: Number between 0.0 and 1.0
            - topics: Array of relevant topics
            """
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert social media analyst providing insights in JSON format only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            # Parse response
            content = response.choices[0].message.content
            if not content:
                logger.error("Empty response from OpenAI API")
                return []
                
            insights_data = json.loads(content)
            
            # Format insights for database
            formatted_insights = []
            for i, insight_data in enumerate(insights_data.get("insights", [])):
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "content_analysis",
                    "insight_text": insight_data.get("insight_text", ""),
                    "recommendations": insight_data.get("recommendations", []),
                    "confidence_score": insight_data.get("confidence_score", 0.7),
                    "topics": insight_data.get("topics", []),
                    "post_ids": [p["id"] for p in top_posts],
                    "source_data_type": "meta_posts",
                    "source_data_id": posts[0]["id"] if posts else "",
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                formatted_insights.append(insight)
            
            return formatted_insights
            
        except Exception as e:
            logger.error(f"Error generating content insights: {str(e)}")
            return []
    
    @staticmethod
    async def _generate_comment_insights(
        comments: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate insights from comments using AI"""
        try:
            # Check budget before expensive AI operation
            estimated_tokens = len(str(comments)) // 4  # Rough token estimation
            estimated_cost = (estimated_tokens / 1000) * 0.03
            
            if not await check_insights_budget(store_id, estimated_cost):
                logger.warning(f"Comment insights budget exceeded for store {store_id}")
                return InsightsService._get_mock_comment_insights(comments[0]["post_id"] if comments else "unknown")
            
            # Track cost before operation
            await track_insights_cost(store_id, "comment_insights", estimated_tokens)
            
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Prepare comments data for analysis
            comments_data = []
            for comment in comments:
                comment_data = {
                    "id": comment.get("id", ""),
                    "message": comment.get("message", ""),
                    "created_time": comment.get("created_time", ""),
                }
                comments_data.append(comment_data)
            
            # Limit to 50 comments for analysis
            analysis_comments = comments_data[:50]
            
            # Create prompt for OpenAI
            prompt = f"""
            You are an expert in social media sentiment analysis. Analyze these Facebook/Instagram comments for {store_info.get('name', 'a business')} 
            which sells {store_info.get('category', 'products')}.
            
            Comments data:
            {json.dumps(analysis_comments, indent=2)}
            
            Generate 2 specific, actionable insights about:
            1. Overall sentiment and common themes
            2. Customer questions or concerns that need addressing
            
            For each insight, provide:
            - A clear insight statement
            - 2-3 specific recommendations
            - A confidence score (0.0-1.0)
            - A sentiment distribution (positive, neutral, negative percentages)
            
            Format your response as a JSON object with an "insights" array containing objects with:
            - insight_text: The main insight
            - recommendations: Array of recommendation strings
            - confidence_score: Number between 0.0 and 1.0
            - sentiment_distribution: Object with positive, neutral, negative percentages
            """
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert in social media sentiment analysis providing insights in JSON format only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            # Parse response
            content = response.choices[0].message.content
            if not content:
                logger.error("Empty response from OpenAI API")
                return []
                
            insights_data = json.loads(content)
            
            # Format insights for database
            formatted_insights = []
            for i, insight_data in enumerate(insights_data.get("insights", [])):
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "comment_analysis",
                    "insight_text": insight_data.get("insight_text", ""),
                    "recommendations": insight_data.get("recommendations", []),
                    "confidence_score": insight_data.get("confidence_score", 0.7),
                    "sentiment_distribution": insight_data.get("sentiment_distribution", {"positive": 0, "neutral": 0, "negative": 0}),
                    "comment_ids": [c["id"] for c in analysis_comments],
                    "source_data_type": "meta_comments",
                    "source_data_id": comments[0]["id"] if comments else "",
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                formatted_insights.append(insight)
            
            return formatted_insights
            
        except Exception as e:
            logger.error(f"Error generating comment insights: {str(e)}")
            return []
    
    @staticmethod
    async def _generate_engagement_insights(
        metrics: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate insights from engagement metrics using AI"""
        try:
            # Check budget before expensive AI operation
            estimated_tokens = len(str(metrics)) // 4  # Rough token estimation
            estimated_cost = (estimated_tokens / 1000) * 0.03
            
            if not await check_insights_budget(store_id, estimated_cost):
                logger.warning(f"Engagement insights budget exceeded for store {store_id}")
                return InsightsService._get_mock_engagement_insights(metrics.get("page_id", "unknown"))
            
            # Track cost before operation
            await track_insights_cost(store_id, "engagement_insights", estimated_tokens)
            
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Create prompt for OpenAI
            prompt = f"""
            You are an expert social media analyst. Analyze these engagement metrics for {store_info.get('name', 'a business')} 
            which sells {store_info.get('category', 'products')}.
            
            Engagement metrics:
            {json.dumps(metrics, indent=2)}
            
            Generate 2 specific, actionable insights about:
            1. Engagement patterns and trends
            2. Opportunities to improve engagement
            
            For each insight, provide:
            - A clear insight statement
            - 2-3 specific recommendations
            - A confidence score (0.0-1.0)
            
            Format your response as a JSON object with an "insights" array containing objects with:
            - insight_text: The main insight
            - recommendations: Array of recommendation strings
            - confidence_score: Number between 0.0 and 1.0
            """
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert social media analyst providing insights in JSON format only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            # Parse response
            content = response.choices[0].message.content
            if not content:
                logger.error("Empty response from OpenAI API")
                return []
                
            insights_data = json.loads(content)
            
            # Format insights for database
            formatted_insights = []
            for i, insight_data in enumerate(insights_data.get("insights", [])):
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "engagement",
                    "insight_text": insight_data.get("insight_text", ""),
                    "recommendations": insight_data.get("recommendations", []),
                    "confidence_score": insight_data.get("confidence_score", 0.7),
                    "engagement_metrics": metrics,
                    "source_data_type": "meta_engagement",
                    "source_data_id": metrics.get("page_id", ""),
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                formatted_insights.append(insight)
            
            return formatted_insights
            
        except Exception as e:
            logger.error(f"Error generating engagement insights: {str(e)}")
            return []
    
    @staticmethod
    async def _generate_audience_insights(
        followers: Dict[str, Any], 
        demographics: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate insights from audience data using AI"""
        try:
            # Check budget before expensive AI operation
            estimated_tokens = len(str(followers)) + len(str(demographics)) // 4  # Rough token estimation
            estimated_cost = (estimated_tokens / 1000) * 0.03
            
            if not await check_insights_budget(store_id, estimated_cost):
                logger.warning(f"Audience insights budget exceeded for store {store_id}")
                return InsightsService._get_mock_audience_insights(followers.get("page_id", "unknown"))
            
            # Track cost before operation
            await track_insights_cost(store_id, "audience_insights", estimated_tokens)
            
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Combine audience data
            audience_data = {
                "followers": followers,
                "demographics": demographics
            }
            
            # Create prompt for OpenAI
            prompt = f"""
            You are an expert in audience analysis. Analyze this audience data for {store_info.get('name', 'a business')} 
            which sells {store_info.get('category', 'products')}.
            
            Audience data:
            {json.dumps(audience_data, indent=2)}
            
            Generate 2 specific, actionable insights about:
            1. Key audience segments and characteristics
            2. Targeting opportunities based on demographics
            
            For each insight, provide:
            - A clear insight statement
            - 2-3 specific recommendations
            - A confidence score (0.0-1.0)
            
            Format your response as a JSON object with an "insights" array containing objects with:
            - insight_text: The main insight
            - recommendations: Array of recommendation strings
            - confidence_score: Number between 0.0 and 1.0
            """
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert in audience analysis providing insights in JSON format only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            # Parse response
            content = response.choices[0].message.content
            if not content:
                logger.error("Empty response from OpenAI API")
                return []
                
            insights_data = json.loads(content)
            
            # Format insights for database
            formatted_insights = []
            for i, insight_data in enumerate(insights_data.get("insights", [])):
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "audience",
                    "insight_text": insight_data.get("insight_text", ""),
                    "recommendations": insight_data.get("recommendations", []),
                    "confidence_score": insight_data.get("confidence_score", 0.7),
                    "audience_metrics": {
                        "followers": followers,
                        "demographics": demographics
                    },
                    "source_data_type": "meta_audience",
                    "source_data_id": followers.get("page_id", ""),
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                formatted_insights.append(insight)
            
            return formatted_insights
            
        except Exception as e:
            logger.error(f"Error generating audience insights: {str(e)}")
            return []
    
    @staticmethod
    async def _generate_ad_insights(
        performance: List[Dict[str, Any]], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate insights from ad performance using AI"""
        try:
            # Check budget before expensive AI operation
            estimated_tokens = len(str(performance)) // 4  # Rough token estimation
            estimated_cost = (estimated_tokens / 1000) * 0.03
            
            if not await check_insights_budget(store_id, estimated_cost):
                logger.warning(f"Ad insights budget exceeded for store {store_id}")
                return InsightsService._get_mock_ad_insights(performance[0]["id"] if performance else "unknown")
            
            # Track cost before operation
            await track_insights_cost(store_id, "ad_insights", estimated_tokens)
            
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Create prompt for OpenAI
            prompt = f"""
            You are an expert in digital advertising. Analyze this ad performance data for {store_info.get('name', 'a business')} 
            which sells {store_info.get('category', 'products')}.
            
            Ad performance data:
            {json.dumps(performance, indent=2)}
            
            Generate 2 specific, actionable insights about:
            1. Ad performance patterns and ROI
            2. Optimization opportunities for better results
            
            For each insight, provide:
            - A clear insight statement
            - 2-3 specific recommendations
            - A confidence score (0.0-1.0)
            
            Format your response as a JSON object with an "insights" array containing objects with:
            - insight_text: The main insight
            - recommendations: Array of recommendation strings
            - confidence_score: Number between 0.0 and 1.0
            """
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model=settings.OPENAI_DEFAULT_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert in digital advertising providing insights in JSON format only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            # Parse response
            content = response.choices[0].message.content
            if not content:
                logger.error("Empty response from OpenAI API")
                return []
                
            insights_data = json.loads(content)
            
            # Format insights for database
            formatted_insights = []
            for i, insight_data in enumerate(insights_data.get("insights", [])):
                insight_id = str(uuid.uuid4())
                
                insight = {
                    "id": insight_id,
                    "store_id": store_id,
                    "insight_type": "ad_performance",
                    "insight_text": insight_data.get("insight_text", ""),
                    "recommendations": insight_data.get("recommendations", []),
                    "confidence_score": insight_data.get("confidence_score", 0.7),
                    "ad_performance": performance,
                    "source_data_type": "meta_ads",
                    "source_data_id": performance[0]["id"] if performance else "",
                    "timestamp": datetime.now(timezone.utc),
                    "is_mock": False
                }
                
                formatted_insights.append(insight)
            
            return formatted_insights
            
        except Exception as e:
            logger.error(f"Error generating ad insights: {str(e)}")
            return []
    
    @staticmethod
    async def _generate_correlation_insights(
        posts: List[Dict[str, Any]], 
        sales: Dict[str, Any], 
        store_id: str
    ) -> List[Dict[str, Any]]:
        """Generate correlation insights between posts and sales using statistical analysis"""
        try:
            # Get store information for context
            store_info = await InsightsService._get_store_info(store_id)
            
            # Extract time series data
            post_dates = []
            post_engagement = []
            
            for post in posts:
                try:
                    created_time = post.get("created_time")
                    if not created_time:
                        continue
                        
                    post_date = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                    engagement = post.get("likes", 0) + post.get("comments", 0) + post.get("shares", 0)
                    
                    post_dates.append(post_date)
                    post_engagement.append(engagement)
                except (ValueError, AttributeError):
                    continue
            
            # Extract sales data
            sales_dates = []
            sales_values = []
            
            daily_sales = sales.get("daily_sales", {})
            if not daily_sales:
                logger.info(f"No daily sales data for store {store_id}")
                return []
                
            for date_str, value in daily_sales.items():
                try:
                    sales_date = datetime.fromisoformat(date_str)
                    sales_dates.append(sales_date)
                    sales_values.append(value)
                except ValueError:
                    continue
            
            # Check if we have enough data
            if len(post_dates) < 5 or len(sales_dates) < 5:
                logger.info(f"Not enough data for correlation analysis for store {store_id}")
                return []
            
            # Create dataframes
            posts_df = pd.DataFrame({
                'date': post_dates,
                'engagement': post_engagement
            })
            
            sales_df = pd.DataFrame({
                'date': sales_dates,
                'sales': sales_values
            })
            
            # Resample to daily data
            posts_df['date'] = pd.to_datetime(posts_df['date'])
            posts_df.set_index('date', inplace=True)
            daily_posts = posts_df.resample('D').sum().reset_index()
            
            sales_df['date'] = pd.to_datetime(sales_df['date'])
            sales_df.set_index('date', inplace=True)
            daily_sales = sales_df.resample('D').sum().reset_index()
            
            # Merge dataframes
            merged_df = pd.merge(daily_posts, daily_sales, on='date', how='outer').fillna(0)
            
            # Calculate lagged variables (1-3 days)
            for lag in range(1, 4):
                merged_df[f'engagement_lag_{lag}'] = merged_df['engagement'].shift(lag)
            
            # Drop NaN values
            merged_df.dropna(inplace=True)
            
            # Check if we have enough data after merging and dropping NaNs
            if len(merged_df) < 5:
                logger.info(f"Not enough overlapping data for correlation analysis for store {store_id}")
                return []
            
            # Calculate correlations
            correlations = {}
            p_values = {}
            
            for lag in range(1, 4):
                corr, p_value = stats.pearsonr(merged_df[f'engagement_lag_{lag}'], merged_df['sales'])
                correlations[lag] = corr
                p_values[lag] = p_value
            
            # Find best lag
            best_lag = max(correlations.items(), key=lambda x: abs(x[1]))[0]
            best_corr = correlations[best_lag]
            best_p = p_values[best_lag]
            
            # Generate insight based on correlation
            if abs(best_corr) < 0.3 or best_p > 0.05:
                insight_text = f"No significant correlation found between social media engagement and sales for {store_info.get('name', 'your business')}."
                recommendations = [
                    "Continue monitoring both metrics separately",
                    "Consider other factors that might influence sales",
                    "Experiment with different content types to see if any drive sales more directly"
                ]
                confidence_score = 0.6
            else:
                direction = "positive" if best_corr > 0 else "negative"
                strength = "strong" if abs(best_corr) > 0.7 else "moderate"
                lag_text = f"{best_lag} day{'s' if best_lag > 1 else ''}"
                
                insight_text = f"There is a {strength} {direction} correlation (r={best_corr:.2f}) between social media engagement and sales with a {lag_text} lag for {store_info.get('name', 'your business')}."
                
                if best_corr > 0:
                    recommendations = [
                        f"Focus on increasing engagement as it appears to drive sales after {lag_text}",
                        "Invest more in high-engagement content types",
                        "Plan promotional posts {lag_text} before expected sales periods"
                    ]
                else:
                    recommendations = [
                        "Investigate why higher engagement might be negatively correlated with sales",
                        "Ensure social media content is aligned with actual product offerings",
                        "Focus on conversion-oriented content rather than just engagement"
                    ]
                
                confidence_score = 0.7 if best_p < 0.01 else 0.6
            
            # Format insight for database
            insight_id = str(uuid.uuid4())
            
            insight = {
                "id": insight_id,
                "store_id": store_id,
                "insight_type": "correlation",
                "insight_text": insight_text,
                "recommendations": recommendations,
                "confidence_score": confidence_score,
                "correlation_data": {
                    "best_lag": best_lag,
                    "correlation": best_corr,
                    "p_value": best_p,
                    "all_correlations": correlations,
                    "all_p_values": p_values
                },
                "data_source_a": "meta_posts",
                "data_source_b": "sales",
                "source_data_type": "correlation",
                "source_data_id": f"{store_id}_meta_sales_correlation",
                "timestamp": datetime.now(timezone.utc),
                "is_mock": False
            }
            
            return [insight]
            
        except Exception as e:
            logger.error(f"Error generating correlation insights: {str(e)}")
            return []
    
    @staticmethod
    async def _get_store_info(store_id: str) -> Dict[str, Any]:
        """Get store information for context"""
        try:
            store = None # Initialize as None since source is removed
            if store:
                return {
                    "name": store.get("name", ""),
                    "category": store.get("category", ""),
                    "products_count": store.get("products_count", 0),
                    "customers_count": store.get("customers_count", 0)
                }
            return {}
        except Exception as e:
            logger.error(f"Error getting store info: {str(e)}")
            return {}
    
    # Mock data methods
    
    @staticmethod
    def _get_mock_content_insights(page_id: str) -> List[Dict[str, Any]]:
        """Get mock content insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "content_analysis",
                "insight_text": "Posts with video content receive 2.3x more engagement than image-only posts.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "posts",
                "source_data_id": page_id,
                "confidence_score": 0.87,
                "recommendations": [
                    "Increase video content production",
                    "Focus on short-form videos under 60 seconds",
                    "Add captions to videos for better accessibility"
                ],
                "post_ids": [page_id],
                "topics": ["video", "engagement"],
                "sentiment_score": 0.75,
                "is_mock": True,
                "store_id": "mock"
            },
            {
                "id": str(uuid.uuid4()),
                "insight_type": "content_analysis",
                "insight_text": "Posts that ask questions in the caption generate 50% more comments.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "posts",
                "source_data_id": page_id,
                "confidence_score": 0.82,
                "recommendations": [
                    "Include questions in post captions",
                    "Create polls and interactive content",
                    "Respond to comments to encourage conversation"
                ],
                "post_ids": [page_id],
                "topics": ["engagement", "comments"],
                "sentiment_score": 0.68,
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    def _get_mock_comment_insights(post_id: str) -> List[Dict[str, Any]]:
        """Get mock comment insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "comment_analysis",
                "insight_text": "Customer comments show high interest in product durability and quality.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "comments",
                "source_data_id": post_id,
                "confidence_score": 0.79,
                "recommendations": [
                    "Highlight product quality in marketing materials",
                    "Share customer testimonials about product durability",
                    "Create content showcasing product testing"
                ],
                "comment_ids": [post_id],
                "sentiment_distribution": {
                    "positive": 0.65,
                    "neutral": 0.25,
                    "negative": 0.10
                },
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    def _get_mock_engagement_insights(page_id: str) -> List[Dict[str, Any]]:
        """Get mock engagement insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "engagement",
                "insight_text": "Engagement peaks on Wednesdays between 6-8pm, suggesting optimal posting times.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "engagement_metrics",
                "source_data_id": page_id,
                "confidence_score": 0.85,
                "recommendations": [
                    "Schedule important posts for Wednesday evenings",
                    "Test posting during other high-engagement periods",
                    "Analyze competitor posting schedules"
                ],
                "engagement_metrics": {
                    "peak_day": "Wednesday",
                    "peak_time": "18:00-20:00",
                    "engagement_increase": 1.4
                },
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    def _get_mock_audience_insights(page_id: str) -> List[Dict[str, Any]]:
        """Get mock audience insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "audience",
                "insight_text": "Your audience is primarily 25-34 year olds interested in technology and fitness.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "audience_demographics",
                "source_data_id": page_id,
                "confidence_score": 0.92,
                "recommendations": [
                    "Create content targeting tech-savvy fitness enthusiasts",
                    "Use language and imagery that resonates with millennials",
                    "Explore partnerships with fitness tech brands"
                ],
                "audience_metrics": {
                    "primary_age_range": "25-34",
                    "top_interests": ["technology", "fitness", "travel"],
                    "gender_split": {"male": 0.55, "female": 0.45}
                },
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    def _get_mock_ad_insights(campaign_id: str) -> List[Dict[str, Any]]:
        """Get mock ad insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "ad_performance",
                "insight_text": "Carousel ads outperform single image ads with 1.7x higher CTR.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "ad_performance",
                "source_data_id": campaign_id,
                "confidence_score": 0.88,
                "recommendations": [
                    "Increase budget allocation for carousel ads",
                    "Test different image sequences in carousel ads",
                    "Include strong CTAs in each carousel card"
                ],
                "ad_metrics": {
                    "carousel_ctr": 0.034,
                    "single_image_ctr": 0.02,
                    "improvement_factor": 1.7
                },
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    def _get_mock_correlation_insights(source_a: str, source_b: str) -> List[Dict[str, Any]]:
        """Get mock correlation insights"""
        return [
            {
                "id": str(uuid.uuid4()),
                "insight_type": "correlation",
                "insight_text": "Product posts featuring customer testimonials correlate with 40% higher sales within 48 hours.",
                "timestamp": datetime.now(timezone.utc),
                "source_data_type": "correlation",
                "source_data_id": f"{source_a}_{source_b}",
                "confidence_score": 0.76,
                "recommendations": [
                    "Increase frequency of testimonial posts",
                    "Feature testimonials for best-selling products",
                    "Create a testimonial campaign for new products"
                ],
                "correlation_score": 0.76,
                "data_source_a": source_a,
                "data_source_b": source_b,
                "is_mock": True,
                "store_id": "mock"
            }
        ]
    
    @staticmethod
    async def generate_comprehensive_insights(page_id: str, store_id: str) -> List[Dict[str, Any]]:
        """
        Generate comprehensive AI insights for a Meta page by analyzing all available data
        """
        logger.info(f"Starting comprehensive insights generation for page {page_id}, store {store_id}")
        
        try:
            all_insights = []
            
            # 1. Get and analyze posts data
            posts_cursor = db_analysis["meta_posts"].find({"page_id": page_id, "store_id": store_id})
            posts = await posts_cursor.to_list(length=50)  # Limit to recent posts for performance
            
            if posts:
                logger.info(f"Found {len(posts)} posts for analysis")
                content_insights = await InsightsService._generate_content_insights(posts, store_id)
                all_insights.extend(content_insights)
                
                # Get engagement metrics from posts
                engagement_metrics = {
                    "page_id": page_id,
                    "total_posts": len(posts),
                    "total_engagement": sum(post.get("likes_count", 0) + post.get("comments_count", 0) + post.get("shares_count", 0) for post in posts),
                    "avg_engagement": sum(post.get("likes_count", 0) + post.get("comments_count", 0) + post.get("shares_count", 0) for post in posts) / len(posts) if posts else 0
                }
                engagement_insights = await InsightsService._generate_engagement_insights(engagement_metrics, store_id)
                all_insights.extend(engagement_insights)
            
            # 2. Get and analyze comments data
            comments_cursor = db_analysis["meta_comments"].find({"page_id": page_id, "store_id": store_id})
            comments = await comments_cursor.to_list(length=100)  # Limit recent comments
            
            if comments:
                logger.info(f"Found {len(comments)} comments for sentiment analysis")
                comment_insights = await InsightsService._generate_comment_insights(comments, store_id)
                all_insights.extend(comment_insights)
            
            # 3. Get audience demographics if available
            page_cursor = db_analysis["meta_pages"].find_one({"id": page_id, "store_id": store_id})
            page_data = await page_cursor
            
            if page_data and page_data.get("followers_count"):
                followers_data = {
                    "page_id": page_id,
                    "total_followers": page_data.get("followers_count", 0),
                    "platform": page_data.get("platform", "facebook")
                }
                
                # Mock demographics for now - in production you'd get real demo data
                demographics = {
                    "age_gender": [
                        {"age_range": "25-34", "gender": "female", "percentage": 35},
                        {"age_range": "35-44", "gender": "female", "percentage": 25},
                        {"age_range": "25-34", "gender": "male", "percentage": 20},
                        {"age_range": "35-44", "gender": "male", "percentage": 20}
                    ],
                    "top_countries": [
                        {"country": "ES", "percentage": 70},
                        {"country": "MX", "percentage": 15},
                        {"country": "AR", "percentage": 10},
                        {"country": "CO", "percentage": 5}
                    ]
                }
                
                audience_insights = await InsightsService._generate_audience_insights(followers_data, demographics, store_id)
                all_insights.extend(audience_insights)
            
            # 4. Try to correlate with sales data if available
            try:
                # Get recent sales data for correlation
                sales_cursor = db_analysis["store_sales_summary"].find({"store_id": store_id}).sort("date", -1).limit(30)
                sales_data = await sales_cursor.to_list(length=30)
                
                if sales_data and posts:
                    logger.info(f"Found {len(sales_data)} sales records for correlation analysis")
                    correlation_insights = await InsightsService._generate_correlation_insights(posts, sales_data, store_id)
                    all_insights.extend(correlation_insights)
            except Exception as correlation_error:
                logger.warning(f"Could not generate correlation insights: {correlation_error}")
            
            # 5. Store all generated insights in the database
            if all_insights:
                for insight in all_insights:
                    # Ensure required fields
                    insight.update({
                        "page_id": page_id,
                        "store_id": store_id,
                        "generated_at": datetime.now(timezone.utc),
                        "is_comprehensive": True  # Flag to indicate this was part of comprehensive generation
                    })
                
                # Insert insights into database
                await db_analysis["meta_insights"].insert_many(all_insights)
                logger.info(f"Stored {len(all_insights)} comprehensive insights for page {page_id}")
            
            return all_insights
            
        except Exception as e:
            logger.error(f"Error generating comprehensive insights for page {page_id}: {str(e)}")
            raise e 
