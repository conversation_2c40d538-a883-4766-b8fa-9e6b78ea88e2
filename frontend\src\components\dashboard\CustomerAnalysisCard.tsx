import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getLocalizedDbText } from '../../utils/localizationUtils';
import type { Analysis } from '../../services/storeService';

interface CustomerAnalysisCardProps {
  analysisData: Analysis | null | undefined;
}

// Simple inline markdown parser helper
const renderInlineFormatting = (text: string): React.ReactNode => {
  // Match **bold**, *italic*
  const parts = text.split(/(\*{1,2}[^*]+\*{1,2})/g);
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      return <strong key={index}>{part.slice(2, -2)}</strong>;
    }
    if (part.startsWith('*') && part.endsWith('*')) {
      return <em key={index}>{part.slice(1, -1)}</em>;
    }
    return part; // Return plain text part
  });
};

const CustomerAnalysisCard: React.FC<CustomerAnalysisCardProps> = ({ analysisData }) => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  const localizedAnalysisText = getLocalizedDbText(analysisData, 'customer_analysis', currentLang);

  // Updated helper function to format analysis text
  const formatAnalysisText = (text: string | null | undefined): React.ReactNode => {
    if (!text || text.trim() === '') {
      return (
        <Typography variant="body2" color="text.secondary" fontStyle="italic" sx={{ mb: 1 }}>
          {t('customerAnalysisCard.notAvailable', 'No customer analysis available.')}
        </Typography>
      );
    }

    // Split into paragraphs by double newline, then handle lines within paragraphs
    const paragraphs = text.split(/\n\s*\n/); // Split by one or more newlines

    return paragraphs.map((paragraph, pIndex) => {
      const lines = paragraph.split('\n').filter(line => line.trim() !== '');
      const isList = lines.every(line => line.trim().startsWith('- ') || line.trim().startsWith('* ') || line.trim().startsWith('• '));

      if (isList) {
        return (
          <Box component="ul" key={pIndex} sx={{ pl: 2, mt: 0, mb: 1 }}>
            {lines.map((line, lIndex) => {
              const cleanLine = line.trim().replace(/^[-*•]\s*/, '');
              return (
                <Box
                  component="li"
                  key={lIndex}
                  sx={theme => theme.palette.mode === 'dark' ? {
                    py: 0.25,
                    px: 1.5,
                    mb: 1,
                    backgroundColor: theme.palette.background.paper,
                    color: '#fff',
                    borderRadius: 2,
                    transition: 'background 0.2s',
                    '&:hover': {
                      backgroundColor: '#222',
                      color: '#fff',
                    },
                  } : {
                    py: 0.25,
                    px: 1.5,
                    mb: 1
                  }}
                >
                  <Typography variant="body2" component="span" sx={theme => theme.palette.mode === 'dark' ? { color: '#fff' } : {}}>
                    {renderInlineFormatting(cleanLine)}
                  </Typography>
                </Box>
              );
            })}
          </Box>
        );
      } else {
        // Treat as a regular paragraph block
        return (
          <Typography key={pIndex} variant="body2" color="text.secondary" sx={{ mb: 1.5 }}>
            {lines.map((line, lIndex) => (
              <React.Fragment key={lIndex}>
                {renderInlineFormatting(line.trim())}
                {lIndex < lines.length - 1 ? <br /> : null} {/* Add line breaks within paragraph */}
              </React.Fragment>
            ))}
          </Typography>
        );
      }
    });
  };

  return (
    <Card elevation={2} sx={{ display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h6" gutterBottom>
          {t('customerAnalysisCard.title', 'Customer Analysis')}
        </Typography>
        <Box sx={{
          flexGrow: 1,
          pr: 1
        }}>
          {formatAnalysisText(localizedAnalysisText)}
        </Box>
      </CardContent>
    </Card>
  );
};

export default CustomerAnalysisCard; 